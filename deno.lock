{"version": "4", "remote": {"https://deno.land/std@0.168.0/async/abortable.ts": "80b2ac399f142cc528f95a037a7d0e653296352d95c681e284533765961de409", "https://deno.land/std@0.168.0/async/deadline.ts": "2c2deb53c7c28ca1dda7a3ad81e70508b1ebc25db52559de6b8636c9278fd41f", "https://deno.land/std@0.168.0/async/debounce.ts": "60301ffb37e730cd2d6f9dadfd0ecb2a38857681bd7aaf6b0a106b06e5210a98", "https://deno.land/std@0.168.0/async/deferred.ts": "77d3f84255c3627f1cc88699d8472b664d7635990d5358c4351623e098e917d6", "https://deno.land/std@0.168.0/async/delay.ts": "5a9bfba8de38840308a7a33786a0155a7f6c1f7a859558ddcec5fe06e16daf57", "https://deno.land/std@0.168.0/async/mod.ts": "7809ad4bb223e40f5fdc043e5c7ca04e0e25eed35c32c3c32e28697c553fa6d9", "https://deno.land/std@0.168.0/async/mux_async_iterator.ts": "770a0ff26c59f8bbbda6b703a2235f04e379f73238e8d66a087edc68c2a2c35f", "https://deno.land/std@0.168.0/async/pool.ts": "6854d8cd675a74c73391c82005cbbe4cc58183bddcd1fbbd7c2bcda42b61cf69", "https://deno.land/std@0.168.0/async/retry.ts": "e8e5173623915bbc0ddc537698fa418cf875456c347eda1ed453528645b42e67", "https://deno.land/std@0.168.0/async/tee.ts": "3a47cc4e9a940904fd4341f0224907e199121c80b831faa5ec2b054c6d2eff5e", "https://deno.land/std@0.168.0/http/server.ts": "e99c1bee8a3f6571ee4cdeb2966efad465b8f6fe62bec1bdb59c1f007cc4d155", "https://deno.land/std@0.200.0/async/delay.ts": "a6142eb44cdd856b645086af2b811b1fcce08ec06bb7d50969e6a872ee9b8659", "https://deno.land/std@0.200.0/http/server.ts": "1b2403b3c544c0624ad23e8ca4e05877e65380d9e0d75d04957432d65c3d5f41"}, "workspace": {"packageJson": {"dependencies": ["npm:@emotion/react@^11.14.0", "npm:@emotion/styled@^11.14.0", "npm:@eslint/js@^9.9.0", "npm:@hookform/resolvers@^3.9.0", "npm:@mui/lab@^6.0.0-beta.24", "npm:@mui/material@^6.4.1", "npm:@mui/x-data-grid@^7.27.2", "npm:@mui/x-date-pickers@^7.24.1", "npm:@radix-ui/react-accordion@^1.2.0", "npm:@radix-ui/react-alert-dialog@^1.1.1", "npm:@radix-ui/react-aspect-ratio@^1.1.0", "npm:@radix-ui/react-avatar@^1.1.0", "npm:@radix-ui/react-checkbox@^1.1.1", "npm:@radix-ui/react-collapsible@^1.1.0", "npm:@radix-ui/react-context-menu@^2.2.1", "npm:@radix-ui/react-dialog@^1.1.1", "npm:@radix-ui/react-dropdown-menu@^2.1.1", "npm:@radix-ui/react-hover-card@^1.1.1", "npm:@radix-ui/react-label@^2.1.0", "npm:@radix-ui/react-menubar@^1.1.1", "npm:@radix-ui/react-navigation-menu@^1.2.0", "npm:@radix-ui/react-popover@^1.1.1", "npm:@radix-ui/react-progress@^1.1.0", "npm:@radix-ui/react-radio-group@^1.2.0", "npm:@radix-ui/react-scroll-area@^1.1.0", "npm:@radix-ui/react-select@^2.1.1", "npm:@radix-ui/react-separator@^1.1.0", "npm:@radix-ui/react-slider@^1.2.0", "npm:@radix-ui/react-slot@^1.1.0", "npm:@radix-ui/react-switch@^1.1.0", "npm:@radix-ui/react-tabs@^1.1.0", "npm:@radix-ui/react-toast@^1.2.1", "npm:@radix-ui/react-toggle-group@^1.1.0", "npm:@radix-ui/react-toggle@^1.1.0", "npm:@radix-ui/react-tooltip@^1.1.2", "npm:@reduxjs/toolkit@^2.5.0", "npm:@supabase/supabase-js@^2.48.1", "npm:@tailwindcss/typography@~0.5.15", "npm:@tanstack/react-query@^5.56.2", "npm:@testing-library/jest-dom@^6.6.3", "npm:@testing-library/react@^16.2.0", "npm:@toolpad/core@0.11", "npm:@types/bcryptjs@^2.4.6", "npm:@types/jest@^29.5.14", "npm:@types/leaflet@^1.9.16", "npm:@types/node@^22.5.5", "npm:@types/react-dom@^18.3.0", "npm:@types/react@^18.3.3", "npm:@vitejs/plugin-react-swc@^3.5.0", "npm:@vitejs/plugin-react@^4.3.4", "npm:@vitest/ui@^3.0.4", "npm:autoprefixer@^10.4.20", "npm:bcryptjs@^2.4.3", "npm:class-variance-authority@0.7", "npm:clsx@^2.1.1", "npm:cmdk@1", "npm:date-fns@^2.30.0", "npm:dayjs@^1.11.13", "npm:embla-carousel-react@^8.3.0", "npm:eslint-plugin-react-hooks@^5.1.0-rc.0", "npm:eslint-plugin-react-refresh@~0.4.9", "npm:eslint@^9.9.0", "npm:globals@^15.9.0", "npm:input-otp@^1.2.4", "npm:jsdom@26", "npm:leaflet@^1.9.4", "npm:lovable-tagger@^1.0.19", "npm:lucide-react@0.451", "npm:msw@^2.7.0", "npm:next-themes@0.3", "npm:notistack@^3.0.2", "npm:postcss@^8.4.47", "npm:react-big-calendar@^1.17.1", "npm:react-day-picker@^8.10.1", "npm:react-dom@^18.3.1", "npm:react-hook-form@^7.53.0", "npm:react-leaflet@^4.2.1", "npm:react-redux@^9.2.0", "npm:react-resizable-panels@^2.1.3", "npm:react-router-dom@^6.26.2", "npm:react@^18.3.1", "npm:recharts@^2.15.1", "npm:sonner@^1.5.0", "npm:tailwind-merge@^2.5.2", "npm:tailwindcss-animate@^1.0.7", "npm:tailwindcss@^3.4.11", "npm:typescript-eslint@^8.0.1", "npm:typescript@^5.5.3", "npm:vaul@~0.9.3", "npm:vite@^5.4.1", "npm:vitest@^3.0.4", "npm:zod@^3.23.8"]}}}