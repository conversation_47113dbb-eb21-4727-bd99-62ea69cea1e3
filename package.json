{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.10.0", "@mui/lab": "^6.0.0-beta.24", "@mui/material": "^6.4.1", "@mui/x-data-grid": "^7.27.2", "@mui/x-date-pickers": "^7.24.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@reduxjs/toolkit": "^2.5.0", "@tanstack/react-query": "^5.56.2", "@toolpad/core": "^0.11.0", "@types/styled-components": "^5.1.34", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.7.2", "input-otp": "^1.2.4", "leaflet": "^1.9.4", "lucide-react": "^0.451.0", "next-themes": "^0.3.0", "notistack": "^3.0.2", "react": "^18.3.1", "react-big-calendar": "^1.17.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-leaflet": "^4.2.1", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.15.1", "slugify": "^1.6.6", "sonner": "^1.5.0", "styled-components": "^6.1.17", "swiper": "^11.2.6", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "xlsx": "^0.18.5", "yup": "^1.6.1", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@supabase/supabase-js": "^2.48.1", "@tailwindcss/typography": "^0.5.15", "@types/bcryptjs": "^2.4.6", "@types/leaflet": "^1.9.16", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^26.0.0", "lovable-tagger": "^1.0.19", "msw": "^2.7.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}