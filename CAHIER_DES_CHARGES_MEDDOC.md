# CAHIER DES CHARGES - PLATEFORME MEDDoC

## 1. PRÉSENTATION DU PROJET

### 1.1 Contexte et Objectifs
**MEDDoC** est une plateforme de santé numérique innovante qui facilite la prise de rendez-vous en ligne entre patients et professionnels de santé. Le projet vise à digitaliser et moderniser l'accès aux soins en offrant une solution complète et sécurisée.

### 1.2 Vision du Projet
Créer une plateforme de santé numérique de référence qui :
- Simplifie la prise de rendez-vous médical
- Centralise les informations de santé des patients
- Optimise la gestion des cabinets médicaux
- Améliore la coordination entre professionnels de santé
- Offre une expérience utilisateur moderne et intuitive

### 1.3 Public Cible
- **Patients** : Particuliers cherchant à prendre rendez-vous avec des professionnels de santé
- **Professionnels de santé** : M<PERSON>de<PERSON><PERSON>, dentistes, spécialistes, paramédicaux
- **Administrateurs** : Gestionnaires de la plateforme

## 2. ARCHITECTURE TECHNIQUE

### 2.1 Stack Technologique

#### Frontend
- **Framework** : React 18.3.1 avec TypeScript 5.5.3
- **Build Tool** : Vite 5.4.1
- **Styling** : Tailwind CSS 3.4.11 + Material-UI 6.4.1
- **State Management** : Redux Toolkit 2.5.0
- **Routing** : React Router DOM 6.26.2
- **Animations** : Framer Motion 12.7.2
- **UI Components** : shadcn/ui + Radix UI
- **Charts** : Recharts 2.15.1
- **Maps** : Leaflet 1.9.4 + React Leaflet 4.2.1
- **Calendar** : React Big Calendar 1.17.1

#### Backend & Base de Données
- **Backend as a Service** : Supabase
- **Base de données** : PostgreSQL 15
- **Authentification** : Supabase Auth
- **Stockage** : Supabase Storage
- **API** : Supabase REST API + Edge Functions

#### Outils de Développement
- **Package Manager** : npm + Bun
- **Linting** : ESLint 9.9.0
- **Testing** : Vitest 3.0.4
- **Containerisation** : Docker + Docker Compose
- **CI/CD** : GitHub Actions

### 2.2 Architecture Logicielle

Le projet suit une architecture **Clean Architecture** avec séparation des couches :

```
src/
├── domain/           # Logique métier et modèles
├── application/      # Cas d'usage et services applicatifs (Redux slices & strategies)
├── infrastructure/   # Accès aux données et services externes
├── presentation/     # Interface utilisateur et composants
├── services/         # Services partagés et utilitaires
├── shared/          # Utilitaires et constantes partagées
├── store/           # Configuration Redux et store global
├── styles/          # Styles globaux et thèmes
├── assets/          # Ressources statiques (images, icons, etc.)
└── docs/            # Documentation technique du projet
```

---

### Explication détaillée de l'arborescence

src/
├── domain/
│   ├── DTOS/                    # Data Transfer Objects
│   │   └── service/             # DTOs spécifiques aux services
│   ├── interfaces/              # Interfaces et contrats
│   │   ├── common/              # Interfaces communes
│   │   ├── repositories/        # Contrats des repositories
│   │   ├── services/            # Contrats des services
│   │   ├── strategies/          # Contrats des stratégies
│   │   ├── supabase/            # Interfaces Supabase
│   │   └── usecases/            # Contrats des cas d'usage
│   ├── mappers/                 # Mappers pour transformation de données
│   ├── models/                  # Entités métier et modèles de domaine
│   │   └── enums/               # Énumérations du domaine
│   ├── services/                # Services de domaine (logique métier pure)
│   ├── types/                   # Types TypeScript du domaine
│   └── usecases/                # Cas d'usage organisés par domaine
│       ├── Region/              # Gestion des régions
│       ├── adhesionRequest/     # Demandes d'adhésion
│       ├── admins/              # Administration
│       ├── affectationMedicale/ # Affectations médicales
│       ├── appointment/         # Rendez-vous
│       ├── categories/          # Catégories
│       ├── commune/             # Communes
│       ├── contact/             # Contacts
│       ├── conversation/        # Conversations
│       ├── dash/                # Dashboard
│       ├── district/            # Districts
│       ├── employer/            # Employés
│       ├── entreesStocks/       # Entrées de stock
│       ├── evenements/          # Événements
│       ├── famille/             # Famille
│       ├── fournisseurs/        # Fournisseurs
│       ├── insurance/           # Assurances
│       ├── lots/                # Lots
│       ├── medicament/          # Médicaments
│       ├── message/             # Messages
│       ├── motCles/             # Mots-clés
│       ├── ordreAppartenance/   # Ordres d'appartenance
│       ├── patients/            # Patients
│       ├── photos/              # Photos
│       ├── prochePatient/       # Proches des patients
│       ├── professional/        # Professionnels
│       ├── professionalStock/   # Stock professionnel
│       ├── produits/            # Produits
│       ├── signeVitaux/         # Signes vitaux
│       ├── sorties/             # Sorties
│       ├── specialite/          # Spécialités
│       ├── user/                # Utilisateurs
│       └── vaccination/         # Vaccinations
│
├── application/
│   ├── slices/                  # Redux Toolkit slices (état global)
│   │   ├── admin/               # État admin
│   │   ├── auth/                # État authentification
│   │   ├── conversation/        # État conversations
│   │   ├── employer/            # État employés
│   │   ├── locationSelector/    # État sélecteur de localisation
│   │   ├── message/             # État messages
│   │   ├── notification/        # État notifications
│   │   ├── patient/             # État patient
│   │   ├── professionnal/       # État professionnel
│   │   └── statesInComponent/   # États locaux de composants
│   └── strategies/              # Stratégies applicatives
│
├── infrastructure/
│   ├── database/                # Configuration base de données
│   ├── factories/               # Factories pour création d'objets
│   ├── providers/               # Fournisseurs de services
│   └── repositories/            # Implémentations concrètes des repositories
│       ├── AdhesionRequest/     # Repository demandes d'adhésion
│       ├── EtablissementProfessionnel/ # Repository établissements
│       ├── ProfesionnalPatient/ # Repository relations professionnel-patient
│       ├── Region/              # Repository régions
│       ├── SpecificDataScheduleRepository/ # Repository planning spécifique
│       ├── TimelotRepository/   # Repository créneaux horaires
│       ├── WeeklyScheduleRepository/ # Repository planning hebdomadaire
│       ├── admins/              # Repository admins
│       ├── affectationMedicale/ # Repository affectations médicales
│       ├── allergie/            # Repository allergies
│       ├── antecedantChirurgicaux/ # Repository antécédents chirurgicaux
│       ├── antecedantFamiliaux/ # Repository antécédents familiaux
│       ├── antecedentGrossesse/ # Repository antécédents grossesse
│       ├── antecedentSociaux/   # Repository antécédents sociaux
│       ├── appointment/         # Repository rendez-vous
│       ├── availabilitySettings/ # Repository paramètres disponibilité
│       ├── carnetDeSante/       # Repository carnet de santé
│       ├── categories/          # Repository catégories
│       ├── commune/             # Repository communes
│       ├── conditionGynecologique/ # Repository conditions gynécologiques
│       ├── contact/             # Repository contacts
│       ├── conversation/        # Repository conversations
│       ├── dash/                # Repository dashboard
│       ├── diagnostics/         # Repository diagnostics
│       ├── dispositifMedicaux/  # Repository dispositifs médicaux
│       ├── district/            # Repository districts
│       ├── employer/            # Repository employés
│       ├── entreesStocks/       # Repository entrées de stock
│       ├── evenement/           # Repository événements
│       ├── facturation/         # Repository facturation
│       ├── famille/             # Repository famille
│       ├── fournisseurs/        # Repository fournisseurs
│       ├── historiqueCarnetSante/ # Repository historique carnet
│       ├── insurance/           # Repository assurances
│       ├── invitationDash/      # Repository invitations dashboard
│       ├── lots/                # Repository lots
│       ├── medicalConsultation/ # Repository consultations médicales
│       ├── medicament/          # Repository médicaments
│       ├── message/             # Repository messages
│       ├── motCles/             # Repository mots-clés
│       ├── ordreAppartenance/   # Repository ordres d'appartenance
│       ├── patients/            # Repository patients
│       ├── photos/              # Repository photos
│       ├── prochePatient/       # Repository proches patients
│       ├── professionalAvailability/ # Repository disponibilités pro
│       ├── professionalDiploma/ # Repository diplômes pro
│       ├── professionalStock/   # Repository stock professionnel
│       ├── produits/            # Repository produits
│       ├── signeVitaux/         # Repository signes vitaux
│       ├── sorties/             # Repository sorties
│       ├── specialite/          # Repository spécialités
│       ├── user/                # Repository utilisateurs
│       └── vaccination/         # Repository vaccinations
│
├── presentation/
│   ├── App.tsx                  # Point d'entrée principal de l'interface
│   ├── Test.tsx                 # Fichier de test ou de démonstration
│   ├── components/              # Composants UI réutilisables et spécifiques
│   │   ├── common/              # Composants génériques (boutons, modales, etc.)
│   │   ├── features/            # Composants liés aux fonctionnalités métier
│   │   ├── layouts/             # Layouts globaux (sidebar, header, etc.)
│   │   ├── locationSelector/    # Sélecteurs de localisation
│   │   ├── preview/             # Composants d'aperçu
│   │   └── professional/        # Composants espace professionnel
│   ├── config/                  # Configuration de présentation
│   ├── constants/               # Constantes de présentation
│   ├── contexts/                # Contexts React pour gestion d'état
│   │   ├── constants/           # Constantes des contextes
│   │   ├── provider/            # Fournisseurs de contextes
│   │   ├── reducers/            # Réducteurs pour gestion d'état
│   │   ├── types/               # Types liés aux contextes
│   │   └── useContext/          # Hooks personnalisés pour contextes
│   ├── hooks/                   # Hooks React personnalisés
│   │   ├── adhesionRequest/     # Hooks demandes d'adhésion
│   │   ├── agenda/              # Hooks gestion d'agenda
│   │   ├── appointment/         # Hooks rendez-vous
│   │   ├── authentification/    # Hooks authentification
│   │   ├── cabinetMedical/      # Hooks cabinet médical
│   │   ├── carnetDeSante/       # Hooks carnet de santé
│   │   ├── categories/          # Hooks catégories
│   │   ├── common/              # Hooks communs
│   │   ├── consultationMedicale/ # Hooks consultations médicales
│   │   ├── consultationStep/    # Hooks étapes consultation
│   │   ├── dash/                # Hooks dashboard
│   │   ├── dashInvitations/     # Hooks invitations dashboard
│   │   ├── dashboard/           # Hooks tableau de bord
│   │   ├── diagnostic/          # Hooks diagnostics
│   │   ├── employer/            # Hooks employés
│   │   ├── facturation/         # Hooks facturation
│   │   ├── messaging/           # Hooks messagerie
│   │   ├── patient/             # Hooks patient
│   │   ├── preview/             # Hooks aperçu
│   │   ├── professional/        # Hooks professionnel
│   │   ├── professionalStock/   # Hooks stock professionnel
│   │   ├── sections/            # Hooks sections
│   │   ├── signeVitaux/         # Hooks signes vitaux
│   │   └── user/                # Hooks utilisateur
│   ├── lib/                     # Fonctions utilitaires et thèmes
│   ├── pages/                   # Pages de l'application
│   │   ├── admin/               # Pages d'administration
│   │   ├── authentification/    # Pages d'authentification
│   │   ├── dash/                # Pages dashboard
│   │   ├── landingPage/         # Page d'accueil
│   │   ├── patient/             # Pages patient
│   │   ├── professional/        # Pages professionnel
│   │   ├── searchProfessional/  # Pages recherche professionnel
│   │   └── shared/              # Pages partagées
│   ├── routes/                  # Définition des routes
│   ├── types/                   # Types TypeScript de présentation
│   └── utils/                   # Utilitaires de présentation
│
├── services/                    # Services partagés et utilitaires
├── shared/                      # Utilitaires et constantes partagées
├── store/                       # Configuration Redux et store global
├── styles/                      # Styles globaux et thèmes
├── assets/                      # Ressources statiques
│   ├── cua/                     # Assets CUA
│   ├── gestionDeStock/          # Assets gestion de stock
│   └── [autres assets]          # Images, logos, icônes
└── docs/                        # Documentation technique
    ├── corrections-donnees-manquantes-evenements.md
    ├── corrections-email-disponibilites.md
    ├── corrections-email-utilisateurs-disponibilites.md
    └── [autres docs techniques]
```

#### Explication des principaux sous-dossiers
- **components/** : Composants UI réutilisables et spécifiques (common, features, layouts…)
- **pages/** : Pages de l'application, organisées par domaine (authentification, patient, professionnel…)
- **hooks/** : Hooks React personnalisés pour la logique de présentation et d'état
- **constants/** : Constantes utilisées dans la présentation
- **contexts/** : Contexts React pour la gestion d'état global/local
- **lib/** : Fonctions utilitaires et thèmes pour la présentation
- **styles/** : Fichiers de styles globaux ou spécifiques à la présentation
- **types/** : Types TypeScript spécifiques à la présentation
- **utils/** : Fonctions utilitaires spécifiques à la présentation
- **routes/** : Définition des routes/pages (si utilisé)

---

Ce découpage permet :
- Une séparation claire des responsabilités,
- Une meilleure maintenabilité,
- Une évolutivité facilitée,
- Des tests plus simples (chaque couche est isolée).

## 3. FONCTIONNALITÉS DÉTAILLÉES

### 3.1 Module d'Authentification et Gestion des Utilisateurs

#### 3.1.1 Inscription et Connexion
- **Inscription Patient** : Processus en 3 étapes avec validation
- **Inscription Professionnel** : Demande d'adhésion avec validation admin
- **Connexion** : Authentification sécurisée avec Supabase Auth
- **Confirmation Email** : Validation des comptes par email
- **Récupération de mot de passe** : Système de reset sécurisé

#### 3.1.2 Gestion des Rôles
- **Patient** : Accès aux fonctionnalités patient
- **Professionnel** : Accès aux fonctionnalités professionnel
- **Administrateur** : Gestion globale de la plateforme

### 3.2 Module de Recherche et Prise de Rendez-vous

#### 3.2.1 Recherche de Professionnels
- **Recherche par spécialité** : Médecins généralistes, spécialistes, paramédicaux
- **Recherche géolocalisée** : Trouver des professionnels proches
- **Filtres avancés** : Disponibilité, langues parlées, modes de paiement
- **Carte interactive** : Visualisation géographique des professionnels

#### 3.2.2 Gestion des Rendez-vous
- **Prise de RDV en ligne** : Sélection de créneaux disponibles
- **Gestion des disponibilités** : Horaires hebdomadaires et spécifiques
- **Types de consultation** : Première consultation, suivi, urgence
- **Motifs de consultation** : Description détaillée des raisons
- **Rappels automatiques** : SMS et emails de confirmation

#### 3.2.3 Gestion des Rendez-vous (Professionnel)
- **Agenda intelligent** : Visualisation et gestion des créneaux
- **Modification/Annulation** : Gestion flexible des RDV
- **Statuts des RDV** : À venir, terminé, annulé, manqué
- **Notifications** : Alertes pour les RDV à venir

### 3.3 Module Carnet de Santé Numérique

#### 3.3.1 Gestion des Données Médicales
- **Allergies** : Gestion complète des allergies avec réactions
- **Médicaments** : Traitements en cours avec posologie
- **Antécédents médicaux** : Historique des affections
- **Antécédents chirurgicaux** : Interventions passées
- **Antécédents familiaux** : Histoire familiale médicale
- **Vaccinations** : Calendrier vaccinal complet
- **Dispositifs médicaux** : Appareils et équipements
- **Conditions gynécologiques** : Suivi spécifique
- **Tests et diagnostics** : Résultats d'examens avec fichiers

#### 3.3.2 Fonctionnalités Avancées
- **Confidentialité** : Contrôle d'accès aux données sensibles
- **Historique des modifications** : Traçabilité complète
- **Partage sécurisé** : Partage avec professionnels autorisés
- **Export/Impression** : Génération de rapports PDF

### 3.4 Module de Communication

#### 3.4.1 Messagerie
- **Chat en temps réel** : Communication patient-professionnel
- **Notifications push** : Alertes instantanées
- **Historique des conversations** : Conservation des échanges
- **Pièces jointes** : Partage de documents médicaux

#### 3.4.2 Notifications
- **Rappels de RDV** : SMS et emails automatiques
- **Confirmations** : Validation des rendez-vous
- **Modifications** : Alertes de changements
- **Annulations** : Notifications d'annulation

### 3.5 Module de Gestion Administrative

#### 3.5.1 Gestion des Professionnels
- **Validation des adhésions** : Processus d'approbation admin
- **Gestion des profils** : Modération des informations
- **Statistiques** : Suivi de l'activité des professionnels
- **Support** : Assistance aux professionnels

#### 3.5.2 Gestion des Patients
- **Base de données patients** : Répertoire complet
- **Statistiques d'usage** : Métriques d'utilisation
- **Support utilisateur** : Assistance aux patients

### 3.6 Module de Facturation et Gestion Financière

#### 3.6.1 Facturation
- **Génération de factures** : Facturation automatique
- **Suivi des paiements** : Gestion des encaissements
- **Historique financier** : Traçabilité des transactions
- **Rapports financiers** : Analyses et statistiques

### 3.7 Module de Gestion des Stocks

#### 3.7.1 Gestion d'Inventaire
- **Suivi des stocks** : Inventaire en temps réel
- **Alertes de rupture** : Notifications automatiques
- **Historique des mouvements** : Traçabilité complète
- **Catégorisation** : Organisation des produits

### 3.8 Module de Gestion des Employés

#### 3.8.1 Gestion d'Équipe
- **Gestion des employés** : Profils et permissions
- **Planning** : Organisation des équipes
- **Suivi des activités** : Monitoring des performances

## 4. INTERFACES UTILISATEUR

### 4.1 Interface Patient

#### 4.1.1 Dashboard Patient
- **Rendez-vous à venir** : Vue d'ensemble des RDV
- **Historique médical** : Accès au carnet de santé
- **Recherche rapide** : Trouver un professionnel
- **Notifications** : Alertes et messages

#### 4.1.2 Pages Principales
- **Accueil** : Landing page avec recherche
- **Recherche de professionnels** : Interface de recherche avancée
- **Mes rendez-vous** : Gestion des RDV
- **Mon carnet de santé** : Accès aux données médicales
- **Ma famille** : Gestion des proches
- **Messages** : Communication avec professionnels

### 4.2 Interface Professionnel

#### 4.2.1 Dashboard Professionnel
- **Agenda du jour** : Vue des RDV du jour
- **Statistiques** : Métriques d'activité
- **Patients récents** : Derniers patients consultés
- **Notifications** : Alertes importantes

#### 4.2.2 Pages Principales
- **Agenda** : Gestion complète des créneaux
- **Mes rendez-vous** : Vue d'ensemble des RDV
- **Mes patients** : Gestion de la patientèle
- **Facturation** : Gestion financière
- **Stock** : Gestion des inventaires
- **Employés** : Gestion d'équipe

### 4.3 Interface Administrateur

#### 4.3.1 Dashboard Admin
- **Statistiques globales** : Métriques de la plateforme
- **Demandes en attente** : Validation des adhésions
- **Alertes système** : Notifications importantes

#### 4.3.2 Pages Principales
- **Gestion des professionnels** : Modération et validation
- **Gestion des patients** : Support utilisateur
- **Demandes d'adhésion** : Processus de validation
- **Support** : Assistance et maintenance

## 5. SÉCURITÉ ET CONFORMITÉ

### 5.1 Sécurité des Données
- **Chiffrement** : Données chiffrées en transit et au repos
- **Authentification** : Système d'auth sécurisé avec Supabase
- **Autorisation** : Contrôle d'accès granulaire
- **Audit** : Logs de sécurité complets

### 5.2 Conformité RGPD
- **Consentement** : Gestion des consentements utilisateur
- **Droit à l'oubli** : Suppression des données personnelles
- **Portabilité** : Export des données utilisateur
- **Transparence** : Politique de confidentialité claire

### 5.3 Sécurité Médicale
- **Confidentialité** : Protection des données médicales
- **Traçabilité** : Historique des accès et modifications
- **Sauvegarde** : Backup sécurisé des données
- **Récupération** : Plan de reprise d'activité

## 6. PERFORMANCES ET SCALABILITÉ

### 6.1 Performance
- **Temps de réponse** : < 2 secondes pour les requêtes
- **Disponibilité** : 99.9% de disponibilité
- **Optimisation** : Lazy loading et code splitting
- **Cache** : Mise en cache intelligente

### 6.2 Scalabilité
- **Architecture cloud** : Infrastructure évolutive
- **Base de données** : PostgreSQL optimisé
- **CDN** : Distribution de contenu global
- **Monitoring** : Surveillance des performances

## 7. INTÉGRATIONS ET API

### 7.1 API REST
- **Endpoints sécurisés** : Authentification JWT
- **Documentation** : API documentation complète
- **Rate limiting** : Protection contre les abus
- **Versioning** : Gestion des versions d'API

### 7.2 Intégrations Externes
- **SMS** : Service de notifications SMS
- **Email** : Service d'envoi d'emails
- **Paiements** : Intégration de solutions de paiement
- **Maps** : Intégration de services cartographiques

## 8. DÉPLOIEMENT ET MAINTENANCE

### 8.1 Environnements
- **Développement** : Environnement de développement local
- **Staging** : Environnement de test
- **Production** : Environnement de production

### 8.2 Déploiement
- **CI/CD** : Pipeline automatisé
- **Docker** : Containerisation complète
- **Monitoring** : Surveillance en temps réel
- **Backup** : Sauvegarde automatique

### 8.3 Maintenance
- **Mises à jour** : Processus de mise à jour sécurisé
- **Support** : Support technique 24/7
- **Documentation** : Documentation technique complète
- **Formation** : Formation des utilisateurs

## 9. ROADMAP ET ÉVOLUTIONS

### 9.1 Phase 1 (Actuelle)
- ✅ Authentification et gestion des utilisateurs
- ✅ Prise de rendez-vous basique
- ✅ Carnet de santé numérique
- ✅ Interface professionnel

### 9.2 Phase 2 (Prévue)
- 🔄 Téléconsultation intégrée
- 🔄 Application mobile native
- 🔄 Intelligence artificielle pour les recommandations
- 🔄 Intégration avec les systèmes hospitaliers

### 9.3 Phase 3 (Future)
- 📋 Blockchain pour la sécurité des données
- 📋 IoT pour le monitoring santé
- 📋 IA avancée pour le diagnostic préliminaire
- 📋 Intégration européenne

## 10. MÉTRIQUES ET KPIs

### 10.1 Métriques Utilisateurs
- **Nombre d'utilisateurs actifs** : Objectif 10,000+
- **Taux de conversion** : Objectif 15%
- **Temps de session** : Objectif 8 minutes
- **Taux de satisfaction** : Objectif 4.5/5

### 10.2 Métriques Techniques
- **Temps de réponse** : < 2 secondes
- **Disponibilité** : 99.9%
- **Taux d'erreur** : < 0.1%
- **Performance** : Score Lighthouse > 90

### 10.3 Métriques Business
- **Nombre de RDV** : Objectif 50,000/mois
- **Taux de remplissage** : Objectif 85%
- **Revenus** : Objectif 100K€/an
- **Croissance** : Objectif 20%/mois

## 11. RISQUES ET MITIGATION

### 11.1 Risques Techniques
- **Panne serveur** : Infrastructure redondante
- **Perte de données** : Backup multiple
- **Sécurité** : Audit de sécurité régulier
- **Performance** : Monitoring continu

### 11.2 Risques Business
- **Concurrence** : Innovation continue
- **Réglementation** : Veille réglementaire
- **Adoption** : Formation et support
- **Financement** : Diversification des sources

## 12. CONCLUSION

La plateforme MEDDoC représente une solution complète et innovante pour la digitalisation du secteur de la santé. Avec ses fonctionnalités avancées, son architecture robuste et sa vision claire, elle est positionnée pour devenir un acteur majeur dans l'écosystème de santé numérique.

Le projet combine technologies modernes, sécurité renforcée et expérience utilisateur optimale pour offrir une solution qui répond aux besoins actuels et futurs du secteur médical.

---

**Document généré le** : $(date)  
**Version** : 1.0  
**Auteur** : Analyse automatique du code source  
**Projet** : MEDDoC - Plateforme de Santé Numérique



