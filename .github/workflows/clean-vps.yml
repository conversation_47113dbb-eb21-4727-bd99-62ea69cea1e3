name: Nettoyage Docker VPS

on:
  schedule:
    - cron: '0 2 * * 0' # Tous les dimanches à 2h du matin
  workflow_dispatch:     # Permet de lancer manuellement depuis l'interface GitHub

jobs:
  clean:
    name: Nettoyage Docker VPS
    runs-on: ubuntu-latest

    steps:
      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_KEY }}" | base64 -d > ~/.ssh/id_rsa || echo "${{ secrets.VPS_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Nettoyage Docker sur VPS
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          echo "🧼 Début du nettoyage Docker sur VPS..."
          echo "📅 Date: $(date)"
          echo "🖥️ Serveur: $(hostname)"
          
          # Afficher l'espace disque avant nettoyage
          echo ""
          echo "📊 === ÉTAT AVANT NETTOYAGE ==="
          echo "💾 Espace disque:"
          df -h / | grep -E "(Filesystem|/dev/)"
          
          echo ""
          echo "🐳 Ressources Docker:"
          docker system df 2>/dev/null || echo "Docker system df non disponible"
          
          echo ""
          echo "📦 Images Docker:"
          docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | head -10
          
          echo ""
          echo "🗑️ === DÉBUT DU NETTOYAGE ==="
          
          # Arrêter les conteneurs non utilisés (optionnel - commenté pour sécurité)
          # echo "⏹️ Arrêt des conteneurs non utilisés..."
          # docker container prune -f
          
          # Supprimer les images non utilisées
          echo "🖼️ Suppression des images non utilisées..."
          docker image prune -a -f
          
          # Supprimer les volumes non utilisés
          echo "💽 Suppression des volumes non utilisés..."
          docker volume prune -f
          
          # Supprimer les réseaux non utilisés
          echo "🌐 Suppression des réseaux non utilisés..."
          docker network prune -f
          
          # Supprimer le cache de build Docker
          echo "🧹 Suppression du cache de build..."
          docker builder prune -a -f
          
          # Nettoyage global (tout ce qui n'est pas utilisé)
          echo "🧽 Nettoyage global des ressources inutilisées..."
          docker system prune -a --volumes -f
          
          # Tronquer les fichiers de log Docker (si le répertoire existe)
          echo "📝 Nettoyage des logs Docker..."
          if [ -d "/var/lib/docker/containers/" ]; then
            # Compter les fichiers de log avant
            LOG_COUNT=$(find /var/lib/docker/containers/ -name '*-json.log' 2>/dev/null | wc -l)
            echo "📄 Fichiers de log trouvés: $LOG_COUNT"
            
            # Tronquer les logs
            find /var/lib/docker/containers/ -name '*-json.log' -exec truncate -s 0 {} \; 2>/dev/null || true
            echo "✂️ Logs Docker tronqués"
          else
            echo "ℹ️ Répertoire des logs Docker non trouvé"
          fi
          
          # Nettoyer les logs système (optionnel)
          echo "🗂️ Nettoyage des logs système anciens..."
          sudo journalctl --vacuum-time=7d 2>/dev/null || echo "Nettoyage journalctl non disponible"
          
          echo ""
          echo "📊 === ÉTAT APRÈS NETTOYAGE ==="
          echo "💾 Espace disque:"
          df -h / | grep -E "(Filesystem|/dev/)"
          
          echo ""
          echo "🐳 Ressources Docker:"
          docker system df 2>/dev/null || echo "Docker system df non disponible"
          
          echo ""
          echo "📦 Images Docker restantes:"
          docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | head -10
          
          echo ""
          echo "✅ Nettoyage Docker terminé avec succès!"
          echo "🕐 Terminé à: $(date)"
          EOF

      - name: Vérification post-nettoyage
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          echo ""
          echo "🔍 === VÉRIFICATION FINALE ==="
          
          # Vérifier que les services essentiels fonctionnent toujours
          echo "🏥 Vérification des services Docker:"
          if docker ps > /dev/null 2>&1; then
            echo "✅ Docker daemon fonctionne"
            
            # Afficher les conteneurs en cours d'exécution
            RUNNING_CONTAINERS=$(docker ps --format "table {{.Names}}\t{{.Status}}" | tail -n +2)
            if [ -n "$RUNNING_CONTAINERS" ]; then
              echo "🏃 Conteneurs en cours d'exécution:"
              echo "$RUNNING_CONTAINERS"
            else
              echo "ℹ️ Aucun conteneur en cours d'exécution"
            fi
          else
            echo "❌ Problème avec Docker daemon"
          fi
          
          # Vérifier l'espace disque disponible
          DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
          echo "💾 Utilisation disque: ${DISK_USAGE}%"
          
          if [ "$DISK_USAGE" -lt 80 ]; then
            echo "✅ Espace disque OK (< 80%)"
          elif [ "$DISK_USAGE" -lt 90 ]; then
            echo "⚠️ Espace disque élevé (80-90%)"
          else
            echo "🚨 Espace disque critique (> 90%)"
          fi
          
          echo ""
          echo "🎉 Nettoyage VPS terminé!"
          EOF
