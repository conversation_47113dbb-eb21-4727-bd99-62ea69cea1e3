import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Obtenir le chemin du répertoire actuel en utilisant ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Lire le fichier JSON des médicaments
const medicationsFilePath = join(__dirname, 'src', 'assets', 'medications_fr.json');
const outputFilePath = join(__dirname, 'insert-medications.sql');

try {
  // Lire le contenu du fichier JSON
  const jsonData = fs.readFileSync(medicationsFilePath, 'utf8');
  const medications = JSON.parse(jsonData);

  console.log(`Nombre total de médicaments: ${medications.length}`);

  // Début de la requête SQL
  let sqlQuery = 'INSERT INTO liste_medicaments (nom, dosage, forme) VALUES\n';
  
  // Ajouter chaque médicament à la requête
  medications.forEach((med, index) => {
    // Échapper les apostrophes dans les chaînes de caractères
    const nom = med.nom ? med.nom.replace(/'/g, "''") : '';
    const dosage = med.dosage ? med.dosage.replace(/'/g, "''") : '';
    const forme = med.forme ? med.forme.replace(/'/g, "''") : '';
    
    // Ajouter les valeurs à la requête
    sqlQuery += `('${nom}', '${dosage}', '${forme}')`;
    
    // Ajouter une virgule si ce n'est pas le dernier élément
    if (index < medications.length - 1) {
      sqlQuery += ',\n';
    } else {
      sqlQuery += ';\n';
    }
  });

  // Écrire la requête SQL dans un fichier
  fs.writeFileSync(outputFilePath, sqlQuery);
  console.log(`Requête SQL générée avec succès dans le fichier: ${outputFilePath}`);
  console.log(`Taille du fichier SQL généré: ${(sqlQuery.length / (1024 * 1024)).toFixed(2)} MB`);

} catch (error) {
  console.error('Erreur lors de la génération de la requête SQL:', error);
}
