@startuml diagramme-use-cases-global
!theme plain
title Diagramme de Use Cases Global - Plateforme Meddoc

' Configuration de la mise en page
!define RECTANGLE
skinparam packageStyle rectangle
skinparam linetype ortho
skinparam nodesep 10
skinparam ranksep 20

skinparam usecase {
    BackgroundColor LightBlue
    BorderColor DarkBlue
    ArrowColor Black
    FontSize 10
}

skinparam actor {
    BackgroundColor LightYellow
    BorderColor DarkBlue
    FontSize 10
}

skinparam package {
    BackgroundColor LightGray
    BorderColor DarkGray
    FontSize 12
    FontStyle bold
}

' Positionnement des acteurs
left to right direction

' Acteurs à gauche
actor "Patient" as Patient
actor "Profession<PERSON> de Sant<PERSON>" as Professional  
actor "Administrateur" as Admin

' Acteurs systèmes à droite
actor "Système de Notification" as NotificationSystem
actor "Syst<PERSON> de Paiement" as PaymentSystem

' Package principal avec organisation en grille
package "Plateforme Meddoc" {
    
    together {
        ' Ligne 1 - Authentification et Rendez-vous
        package "Gestion Authentification" {
            usecase "S'inscrire" as UC_Register
            usecase "Se connecter" as UC_Login
            usecase "Gérer profil" as UC_ManageProfile
            usecase "Réinitialiser mot de passe" as UC_ResetPassword
            usecase "Valider compte" as UC_ValidateAccount
        }
        
        package "Gestion Rendez-vous" {
            usecase "Rechercher professionnel" as UC_SearchProfessional
            usecase "Prendre rendez-vous" as UC_BookAppointment
            usecase "Modifier rendez-vous" as UC_ModifyAppointment
            usecase "Annuler rendez-vous" as UC_CancelAppointment
            usecase "Gérer disponibilités" as UC_ManageAvailability
            usecase "Consulter agenda" as UC_ViewAgenda
        }
    }
    
    together {
        ' Ligne 2 - Carnet de Santé et Professionnels
        package "Gestion Carnet de Santé" {
            usecase "Consulter carnet santé" as UC_ViewHealthRecord
            usecase "Ajouter données médicales" as UC_AddMedicalData
            usecase "Gérer allergies" as UC_ManageAllergies
            usecase "Gérer médicaments" as UC_ManageMedications
            usecase "Gérer antécédents" as UC_ManageHistory
            usecase "Gérer vaccinations" as UC_ManageVaccinations
            usecase "Partager données" as UC_ShareData
        }
        
        package "Gestion Professionnels" {
            usecase "Demander adhésion" as UC_RequestMembership
            usecase "Valider adhésion" as UC_ValidateMembership
            usecase "Gérer établissement" as UC_ManageEstablishment
            usecase "Gérer spécialités" as UC_ManageSpecialties
            usecase "Gérer diplômes" as UC_ManageDiplomas
            usecase "Gérer employés" as UC_ManageEmployees
        }
    }
    
    together {
        ' Ligne 3 - Communication et Administration
        package "Gestion Communication" {
            usecase "Envoyer message" as UC_SendMessage
            usecase "Recevoir message" as UC_ReceiveMessage
            usecase "Gérer conversations" as UC_ManageConversations
            usecase "Envoyer notifications" as UC_SendNotifications
        }
        
        package "Gestion Administration" {
            usecase "Modérer utilisateurs" as UC_ModerateUsers
            usecase "Gérer demandes" as UC_ManageRequests
            usecase "Consulter statistiques" as UC_ViewStatistics
            usecase "Gérer support" as UC_ManageSupport
            usecase "Configurer système" as UC_ConfigureSystem
        }
    }
    
    together {
        ' Ligne 4 - Stocks et Facturation
        package "Gestion Stocks" {
            usecase "Gérer inventaire" as UC_ManageInventory
            usecase "Suivre stocks" as UC_TrackStock
            usecase "Gérer fournisseurs" as UC_ManageSuppliers
            usecase "Gérer entrées/sorties" as UC_ManageStockMovements
            usecase "Alertes rupture" as UC_StockAlerts
        }
        
        package "Gestion Facturation" {
            usecase "Générer factures" as UC_GenerateInvoices
            usecase "Traiter paiements" as UC_ProcessPayments
            usecase "Consulter historique" as UC_ViewPaymentHistory
            usecase "Gérer tarifs" as UC_ManageRates
        }
    }
}

' Relations Patient
Patient --> UC_Register
Patient --> UC_Login
Patient --> UC_ManageProfile
Patient --> UC_ResetPassword
Patient --> UC_SearchProfessional
Patient --> UC_BookAppointment
Patient --> UC_ModifyAppointment
Patient --> UC_CancelAppointment
Patient --> UC_ViewHealthRecord
Patient --> UC_AddMedicalData
Patient --> UC_ManageAllergies
Patient --> UC_ManageMedications
Patient --> UC_ManageHistory
Patient --> UC_ManageVaccinations
Patient --> UC_SendMessage
Patient --> UC_ReceiveMessage
Patient --> UC_ManageConversations

' Relations Professionnel
Professional --> UC_Register
Professional --> UC_Login
Professional --> UC_ManageProfile
Professional --> UC_RequestMembership
Professional --> UC_ManageAvailability
Professional --> UC_ViewAgenda
Professional --> UC_ModifyAppointment
Professional --> UC_CancelAppointment
Professional --> UC_ViewHealthRecord
Professional --> UC_AddMedicalData
Professional --> UC_ShareData
Professional --> UC_ManageEstablishment
Professional --> UC_ManageSpecialties
Professional --> UC_ManageDiplomas
Professional --> UC_ManageEmployees
Professional --> UC_SendMessage
Professional --> UC_ReceiveMessage
Professional --> UC_ManageConversations
Professional --> UC_ManageInventory
Professional --> UC_TrackStock
Professional --> UC_ManageSuppliers
Professional --> UC_ManageStockMovements
Professional --> UC_GenerateInvoices
Professional --> UC_ProcessPayments
Professional --> UC_ViewPaymentHistory
Professional --> UC_ManageRates

' Relations Administrateur
Admin --> UC_Login
Admin --> UC_ValidateMembership
Admin --> UC_ModerateUsers
Admin --> UC_ManageRequests
Admin --> UC_ViewStatistics
Admin --> UC_ManageSupport
Admin --> UC_ConfigureSystem
Admin --> UC_ValidateAccount

' Relations Systèmes externes
NotificationSystem --> UC_SendNotifications
PaymentSystem --> UC_ProcessPayments

' Relations Include et Extend
UC_BookAppointment .> UC_SearchProfessional : include
UC_ModifyAppointment .> UC_ViewAgenda : include
UC_CancelAppointment .> UC_SendNotifications : include
UC_AddMedicalData .> UC_ValidateAccount : include
UC_ProcessPayments .> UC_GenerateInvoices : include
UC_StockAlerts .> UC_SendNotifications : include

@enduml
