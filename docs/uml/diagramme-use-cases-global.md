# Diagramme de Use Cases Global - Plateforme Meddoc

```plantuml
@startuml
!theme plain
title Diagramme de Use Cases Global - Plateforme Meddoc

' Acteurs
actor "Patient" as Patient
actor "Professionnel de Santé" as Professional
actor "Administrateur" as Admin
actor "Système de Notification" as NotificationSystem
actor "Système de Paiement" as PaymentSystem

' Package principal
package "Plateforme Meddoc" {
    
    ' Use Cases Authentification
    package "Gestion Authentification" {
        usecase "S'inscrire" as UC_Register
        usecase "Se connecter" as UC_Login
        usecase "Gérer profil" as UC_ManageProfile
        usecase "Réinitialiser mot de passe" as UC_ResetPassword
        usecase "Valider compte" as UC_ValidateAccount
    }
    
    ' Use Cases Rendez-vous
    package "Gestion Rendez-vous" {
        usecase "Rechercher professionnel" as UC_SearchProfessional
        usecase "Prendre rendez-vous" as UC_BookAppointment
        usecase "Modifier rendez-vous" as UC_ModifyAppointment
        usecase "Annuler rendez-vous" as UC_CancelAppointment
        usecase "Gérer disponibilités" as UC_ManageAvailability
        usecase "Consulter agenda" as UC_ViewAgenda
    }
    
    ' Use Cases Carnet de Santé
    package "Gestion Carnet de Santé" {
        usecase "Consulter carnet santé" as UC_ViewHealthRecord
        usecase "Ajouter données médicales" as UC_AddMedicalData
        usecase "Gérer allergies" as UC_ManageAllergies
        usecase "Gérer médicaments" as UC_ManageMedications
        usecase "Gérer antécédents" as UC_ManageHistory
        usecase "Gérer vaccinations" as UC_ManageVaccinations
        usecase "Partager données" as UC_ShareData
    }
    
    ' Use Cases Professionnels
    package "Gestion Professionnels" {
        usecase "Demander adhésion" as UC_RequestMembership
        usecase "Valider adhésion" as UC_ValidateMembership
        usecase "Gérer établissement" as UC_ManageEstablishment
        usecase "Gérer spécialités" as UC_ManageSpecialties
        usecase "Gérer diplômes" as UC_ManageDiplomas
        usecase "Gérer employés" as UC_ManageEmployees
    }
    
    ' Use Cases Communication
    package "Gestion Communication" {
        usecase "Envoyer message" as UC_SendMessage
        usecase "Recevoir message" as UC_ReceiveMessage
        usecase "Gérer conversations" as UC_ManageConversations
        usecase "Envoyer notifications" as UC_SendNotifications
    }
    
    ' Use Cases Administration
    package "Gestion Administration" {
        usecase "Modérer utilisateurs" as UC_ModerateUsers
        usecase "Gérer demandes" as UC_ManageRequests
        usecase "Consulter statistiques" as UC_ViewStatistics
        usecase "Gérer support" as UC_ManageSupport
        usecase "Configurer système" as UC_ConfigureSystem
    }
    
    ' Use Cases Stocks
    package "Gestion Stocks" {
        usecase "Gérer inventaire" as UC_ManageInventory
        usecase "Suivre stocks" as UC_TrackStock
        usecase "Gérer fournisseurs" as UC_ManageSuppliers
        usecase "Gérer entrées/sorties" as UC_ManageStockMovements
        usecase "Alertes rupture" as UC_StockAlerts
    }
    
    ' Use Cases Facturation
    package "Gestion Facturation" {
        usecase "Générer factures" as UC_GenerateInvoices
        usecase "Traiter paiements" as UC_ProcessPayments
        usecase "Consulter historique" as UC_ViewPaymentHistory
        usecase "Gérer tarifs" as UC_ManageRates
    }
}

' Relations Patient
Patient --> UC_Register
Patient --> UC_Login
Patient --> UC_ManageProfile
Patient --> UC_ResetPassword
Patient --> UC_SearchProfessional
Patient --> UC_BookAppointment
Patient --> UC_ModifyAppointment
Patient --> UC_CancelAppointment
Patient --> UC_ViewHealthRecord
Patient --> UC_AddMedicalData
Patient --> UC_ManageAllergies
Patient --> UC_ManageMedications
Patient --> UC_ManageHistory
Patient --> UC_ManageVaccinations
Patient --> UC_SendMessage
Patient --> UC_ReceiveMessage
Patient --> UC_ManageConversations

' Relations Professionnel
Professional --> UC_Register
Professional --> UC_Login
Professional --> UC_ManageProfile
Professional --> UC_RequestMembership
Professional --> UC_ManageAvailability
Professional --> UC_ViewAgenda
Professional --> UC_ModifyAppointment
Professional --> UC_CancelAppointment
Professional --> UC_ViewHealthRecord
Professional --> UC_AddMedicalData
Professional --> UC_ShareData
Professional --> UC_ManageEstablishment
Professional --> UC_ManageSpecialties
Professional --> UC_ManageDiplomas
Professional --> UC_ManageEmployees
Professional --> UC_SendMessage
Professional --> UC_ReceiveMessage
Professional --> UC_ManageConversations
Professional --> UC_ManageInventory
Professional --> UC_TrackStock
Professional --> UC_ManageSuppliers
Professional --> UC_ManageStockMovements
Professional --> UC_GenerateInvoices
Professional --> UC_ProcessPayments
Professional --> UC_ViewPaymentHistory
Professional --> UC_ManageRates

' Relations Administrateur
Admin --> UC_Login
Admin --> UC_ValidateMembership
Admin --> UC_ModerateUsers
Admin --> UC_ManageRequests
Admin --> UC_ViewStatistics
Admin --> UC_ManageSupport
Admin --> UC_ConfigureSystem
Admin --> UC_ValidateAccount

' Relations Systèmes externes
NotificationSystem --> UC_SendNotifications
PaymentSystem --> UC_ProcessPayments

' Includes et Extends
UC_BookAppointment ..> UC_SearchProfessional : <<include>>
UC_ModifyAppointment ..> UC_ViewAgenda : <<include>>
UC_CancelAppointment ..> UC_SendNotifications : <<include>>
UC_AddMedicalData ..> UC_ValidateAccount : <<include>>
UC_ProcessPayments ..> UC_GenerateInvoices : <<include>>
UC_StockAlerts ..> UC_SendNotifications : <<include>>

@enduml
```

## Description des Acteurs

### Patient
- Utilisateur final cherchant des soins médicaux
- Peut prendre des rendez-vous et gérer son carnet de santé
- Communique avec les professionnels de santé

### Professionnel de Santé
- Médecins, dentistes, spécialistes, paramédicaux
- Gère son agenda, ses patients et son établissement
- Accède aux données médicales partagées

### Administrateur
- Gestionnaire de la plateforme
- Valide les adhésions et modère le contenu
- Accès aux statistiques et configuration système

### Systèmes Externes
- **Système de Notification** : Gestion des SMS, emails, push
- **Système de Paiement** : Traitement des transactions financières

## Use Cases Principaux par Acteur

### Patient (17 use cases)
- Authentification et gestion de profil
- Recherche et prise de rendez-vous
- Gestion complète du carnet de santé
- Communication avec professionnels

### Professionnel de Santé (25 use cases)
- Gestion complète de l'activité professionnelle
- Gestion des patients et rendez-vous
- Gestion administrative et financière
- Gestion des stocks et inventaire

### Administrateur (7 use cases)
- Supervision et modération
- Validation des demandes
- Configuration et support système
