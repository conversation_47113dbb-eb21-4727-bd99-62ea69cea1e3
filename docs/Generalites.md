# Documentation Technique: Repositories, Usecases et Circulation des Données de l'application MEDDoC

## Introduction

Cette documentation explique l'organisation du code et la circulation des données dans l'application. Elle est divisée en deux grandes parties : le **Backend** et le **Frontend**.

---

## 📦 Côté Backend

L'application utilise [Supabase](https://supabase.com/) comme backend principal. Supabase propose plusieurs fonctionnalités intégrées :

### 1. Rôles de Supabase

- **Stockage des données** : Supabase utilise PostgreSQL pour le stockage. Une console intuitive permet la gestion des données.
- **Authentification** : Fournit un système complet d'authentification prêt à l'emploi (email/mot de passe, OAuth, etc.).
- **Stockage de fichiers** : Permet de gérer les fichiers utilisateurs (ex. : photo de profil, documents PDF).

### 2. Circulation des Données Backend

Voici le cheminement typique des données côté backend :

```text
Supabase <=> Repositories => Usecases => Frontend
```

#### 📁 Repositories

Les repositories sont les seules entités en contact direct avec Supabase. Ils sont responsables des opérations CRUD.

- Localisation : `./src/infrastructure/repositories/<nom-table>/`
- Typage : les entités utilisées sont définies dans `./src/domain/models/`

#### 📁 Usecases

Les usecases utilisent les interfaces des repositories par injection de dépendance. Ils contiennent la **logique métier** et représentent la dernière couche côté backend.

> [!NOTE] Pour plus d'informations sur la création de repositories et de usecases, consultez [`repository_usecase_guide.md`](./repository_usecase_guide.md)

---

## 🧩 Côté Frontend

L'interface utilisateur est développée avec **React + TypeScript**. Le code source commence dans :

```bash
./src/presentation
```

### 📁 Structure du Frontend

```bash
./src/presentation
├── components       # Composants UI réutilisables par page
├── constants        # Constantes de l'application côté frontend
├── contexts         # Contextes React (ex : gestion de formulaires)
├── hooks            # Custom hooks pour traitement logique côté UI
├── lib              # Données statiques (ex : sidebar protégée)
├── pages            # Pages de l'application (routées via React Router)
├── routes           # Définition des routes (publiques, privées, secrètes)
├── styles           # Fichiers TailwindCSS / thèmes visuels
├── types            # Types globaux réutilisables
└── utils            # Fonctions utilitaires frontend
```

### 🔁 Circulation des Données Frontend

La majorité des données du frontend proviennent de **Redux Toolkit**, avec :

- Store global : `./src/store/index.ts`
- Slices Redux : `./src/application/slice`

**Exceptions** : certaines données statiques (ex. : options d’un `<select>` comme les mots-clés professionnels) ne passent pas par le store.

### 💡 Bonnes pratiques frontend

- La logique métier frontend est isolée dans des **custom hooks** :
  - Exemple : `./src/presentation/hooks/<page>/<useHook>.ts`
- Les composants sont responsables uniquement de **l'affichage** des données.
- L’usage du store permet d’éviter de recharger les mêmes données depuis la base de données plusieurs fois par session utilisateur.

---

## ✅ Résumé

| Élément            | Backend                                    | Frontend                                                 |
| ------------------ | ------------------------------------------ | -------------------------------------------------------- |
| Source des données | Supabase (PostgreSQL, Auth, Storage)       | Redux store (avec quelques données statiques hors-store) |
| Traitement         | Repositories → Usecases                    | Hooks → Composants                                       |
| Organisation       | Clean Architecture (interface + injection) | Structure modulaire par responsabilité                   |

---

## 🔚 Conclusion

Cette architecture modulaire et claire améliore la lisibilité, la testabilité et la maintenabilité du projet. En suivant ce flux de données cohérent, chaque nouvelle fonctionnalité pourra s'intégrer naturellement dans la structure existante.

