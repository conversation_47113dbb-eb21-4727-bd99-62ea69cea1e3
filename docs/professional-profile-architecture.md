# Architecture de la Page ProfessionalProfile

## Vue d'ensemble

La page `ProfessionalProfile.tsx` est le composant principal qui gère l'affichage et l'édition du profil d'un professionnel de santé. Elle utilise une architecture modulaire avec des sections éditables indépendantes, chacune gérant sa propre validation et ses propres données.

## Structure du Composant

### Imports et Dépendances

```typescript
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { RootState } from "@/store";
import { useSelector } from "react-redux";
import { motion } from "framer-motion";
```

### État Local

```typescript
const [isEditing, setIsEditing] = useState(false);
```

- `isEditing` : Contrôle l'état d'édition global de la page

### Hooks Personnalisés

```typescript
const { id } = useSelector((state: RootState) => state.authentification.user);
const { handleValidationError } = useErrorHandler();
const {
  profileData,
  isLoading,
  error: profileError,
  fetchProfileData,
  updateBaseInfo,
  updatePresentation,
  updateServices,
  updateProfessionalInfo,
  motClesActions,
  languagesActions,
} = useProfessionalProfile();

// Utilisation du hook principal pour tous les handlers
const handlers = useProfessionalProfileHandlers();
```

## Architecture des Handlers

### 1. Séparation des Responsabilités

Pour respecter les bonnes pratiques, les handlers ont été organisés en hooks spécialisés :

#### Hooks Spécialisés

- **`useContactHandlers`** : Gestion des informations de contact
- **`useEstablishmentHandlers`** : Gestion des informations d'établissement
- **`useMediaHandlers`** : Gestion des médias (photos, images)
- **`usePaymentInsuranceHandlers`** : Gestion des paiements et assurances

#### Hook Principal

- **`useProfessionalProfileHandlers`** : Orchestration de tous les handlers spécialisés

### 2. Avantages de cette Architecture

#### Séparation des Responsabilités

```typescript
// Avant : Tous les handlers dans le composant principal
const handleSaveContact = async (contactData) => {
  /* ... */
};
const handleSaveEstablishment = async (establishmentData) => {
  /* ... */
};
// ... 15+ handlers dans le composant

// Après : Handlers organisés par domaine
const { handleSaveContact } = useContactHandlers();
const { handleSaveEstablishment } = useEstablishmentHandlers();
const handlers = useProfessionalProfileHandlers(); // Interface unifiée
```

#### Réutilisabilité

- Chaque hook peut être utilisé indépendamment
- Logique métier réutilisable dans d'autres composants
- Tests unitaires facilités

#### Maintenabilité

- Code plus lisible et organisé
- Modifications isolées par domaine
- Documentation claire par domaine

### 3. Structure des Hooks

#### Hook Spécialisé (Exemple : Contact)

```typescript
export const useContactHandlers = () => {
  const handleSaveContact = useCallback(
    async (contactData: ContactFormData) => {
      try {
        // Logique métier spécifique au contact
        return true;
      } catch (error) {
        return false;
      }
    },
    []
  );

  return { handleSaveContact };
};
```

#### Hook Principal (Orchestration)

```typescript
export const useProfessionalProfileHandlers = () => {
  // Hooks spécialisés
  const contactHandlers = useContactHandlers();
  const establishmentHandlers = useEstablishmentHandlers();
  const mediaHandlers = useMediaHandlers();
  const paymentInsuranceHandlers = usePaymentInsuranceHandlers();

  // Handlers additionnels
  const handleAddDiploma = useCallback(async (diploma) => {
    /* ... */
  }, []);

  return {
    ...contactHandlers,
    ...establishmentHandlers,
    ...mediaHandlers,
    ...paymentInsuranceHandlers,
    handleAddDiploma,
    // ... autres handlers
  };
};
```

## Architecture des Sections

### 1. Système de Sections Éditables

Chaque section utilise le pattern CRUD (Create, Read, Update, Delete) avec validation intégrée :

#### Composants de Section Disponibles

- `ContactCRUDSection` : Informations de contact
- `PresentationEditSection` : Présentation générale
- `SpecialtiesCRUDSection` : Spécialités médicales
- `ProfessionalInformationEdit` : Informations professionnelles
- `ServicesEditSection` : Services proposés
- `EstablishmentCRUDSection` : Informations d'établissement
- `DiplomasCRUDSection` : Diplômes et formations
- `ExperiencesCRUDSection` : Expériences professionnelles
- `PublicationsCRUDSection` : Publications et recherches
- `LanguagesCRUDSection` : Langues parlées
- `PaymentAndInsuranceCRUDSection` : Modes de paiement et assurances
- `KeywordsCRUDSection` : Mots-clés et symptômes
- `CabinetImagesCRUDSection` : Galerie d'images du cabinet

### 2. Hook useSectionForm

Chaque section utilise le hook `useSectionForm` qui encapsule :

- **Validation Zod** : Schémas de validation stricts
- **React Hook Form** : Gestion des formulaires
- **Gestion d'erreurs** : Messages d'erreur centralisés
- **État de sauvegarde** : Indicateurs de chargement

#### Fonctionnement de la Validation

```typescript
const {
  register,
  formState: { errors },
  save,
  cancel,
  isSaving,
  watch,
} = useSectionForm({
  schema: contactSchema,
  defaultValues: {
    /* ... */
  },
  onSave: async (data) => await onSave(data),
  sectionName: "informations de contact",
});
```

### 3. Composant EditableSection

Wrapper générique qui gère :

- **Toggle d'édition** : Basculer entre mode lecture/édition
- **Animations** : Transitions fluides avec Framer Motion
- **Gestion d'erreurs** : Reste en mode édition si validation échoue
- **Boutons d'action** : Sauvegarder/Annuler

## Gestion des Données

### 1. Chargement Initial

```typescript
useEffect(() => {
  if (id) {
    fetchProfileData(id);
  }
}, [id, fetchProfileData]);
```

### 2. Handlers de Sauvegarde

Chaque section a son propre handler :

```typescript
const handleSaveContact = async (contactData: ContactFormData) => {
  try {
    // Logique de sauvegarde
    return true; // Succès
  } catch (error) {
    return false; // Échec
  }
};
```

### 3. Rechargement des Données

Après chaque modification réussie, les données sont rechargées :

```typescript
if (id) {
  await fetchProfileData(id);
}
```

## Validation et Gestion d'Erreurs

### 1. Validation Zod

Chaque section utilise un schéma Zod spécifique :

```typescript
// Exemple : contactSchema
export const contactSchema = z.object({
  email: z.string().email("Veuillez saisir une adresse email valide"),
  telephone: z.string().min(1, "Le numéro de téléphone est requis"),
  adresse: z.string().min(1, "L'adresse est requise"),
  fokontany: z.string().min(1, "Le fokontany est requis"),
  infoAcces: z.string().optional(),
});
```

### 2. Gestion d'Erreurs Centralisée

Utilise le hook `useErrorHandler` pour :

- **Erreurs de validation** : Messages utilisateur appropriés
- **Erreurs d'API** : Gestion des erreurs réseau
- **Erreurs de permissions** : Accès refusé
- **Messages de succès** : Confirmation des actions

### 3. Comportement de Validation

1. **Mode édition** : L'utilisateur modifie les données
2. **Clic sur "Sauvegarder"** : Déclenche la validation
3. **Si validation échoue** :
   - Formulaire reste en mode édition
   - Erreurs s'affichent
   - Aucune sauvegarde n'est effectuée
4. **Si validation réussit** :
   - Données sont sauvegardées
   - Formulaire passe en mode lecture
   - Message de succès affiché

## Interface Utilisateur

### 1. Layout Responsive

```typescript
<div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
  {/* Colonne gauche : Photo + Contact */}
  <motion.div className="lg:col-span-1">
    {/* ... */}
  </motion.div>

  {/* Colonne droite : Sections détaillées */}
  <motion.div className="lg:col-span-2">
    {/* ... */}
  </motion.div>
</div>
```

### 2. Animations

Utilise Framer Motion pour :

- **Animations d'entrée** : Apparition progressive des sections
- **Transitions fluides** : Changements d'état
- **Feedback visuel** : Indicateurs de chargement

### 3. SEO et Métadonnées

```typescript
<Helmet>
  <title>{pageTitle}</title>
  <meta name="description" content={pageDescription} />
  <meta name="keywords" content={keywords} />
  <meta property="og:title" content={pageTitle} />
  <meta property="og:description" content={pageDescription} />
  <meta property="og:image" content={profileImage} />
</Helmet>
```

## État de Chargement

### 1. Loading Spinner

```typescript
if (isLoading) {
  return (
    <div className="flex flex-col gap-4 justify-center items-center min-h-screen">
      <LoadingSpinner className="h-auto" />
      <p>Chargement des donnees</p>
    </div>
  );
}
```

### 2. Gestion d'Erreurs

```typescript
if (profileError) {
  return null; // Ou composant d'erreur
}

if (!profileData) {
  return (
    <div className="flex justify-center items-center min-h-screen">
      <div className="text-gray-600">Aucune donnée trouvée</div>
    </div>
  );
}
```

## Extensibilité

### 1. Ajout de Nouvelles Sections

Pour ajouter une nouvelle section :

1. **Créer le composant** : `NewSectionCRUDSection.tsx`
2. **Définir le schéma** : `newSectionSchema`
3. **Ajouter le handler** : `handleSaveNewSection`
4. **Intégrer dans le JSX** : Ajouter dans la grille

### 2. Ajout de Nouveaux Handlers

Pour ajouter un nouveau domaine de handlers :

1. **Créer le hook spécialisé** : `useNewDomainHandlers.ts`
2. **Implémenter les handlers** : Logique métier spécifique
3. **Intégrer dans le hook principal** : `useProfessionalProfileHandlers`
4. **Utiliser dans le composant** : Via l'objet `handlers`

### 3. Personnalisation des Validations

Chaque section peut avoir ses propres règles de validation en modifiant le schéma Zod correspondant.

### 4. Gestion d'État Avancée

Le système supporte :

- **États de chargement** : Indicateurs visuels
- **Gestion d'erreurs** : Messages appropriés
- **Optimistic updates** : Mise à jour immédiate de l'UI
- **Rollback** : Annulation des modifications

## Bonnes Pratiques

### 1. Séparation des Responsabilités

- **Composants de section** : Gèrent leur propre logique
- **Hook useSectionForm** : Logique commune de validation
- **Handlers spécialisés** : Logique métier par domaine
- **Hook principal** : Orchestration et interface unifiée
- **Page principale** : Layout et coordination

### 2. Performance

- **Lazy loading** : Chargement à la demande
- **Memoization** : Optimisation des re-renders
- **Debouncing** : Réduction des appels API

### 3. Accessibilité

- **ARIA labels** : Support des lecteurs d'écran
- **Navigation clavier** : Contrôles au clavier
- **Messages d'erreur** : Descriptions claires

## Tests

### 1. Tests de Validation

Chaque section peut être testée individuellement :

```typescript
// Test de validation ContactCRUDSection
<ContactCRUDSection
  email="<EMAIL>"
  telephone="+261 34 12 345 67"
  adresse="123 Rue de la Paix"
  fokontany="Antananarivo"
  onSave={handleSaveContact}
/>
```

### 2. Tests des Handlers

Chaque hook spécialisé peut être testé indépendamment :

```typescript
// Test du hook useContactHandlers
const { handleSaveContact } = useContactHandlers();
const result = await handleSaveContact(mockContactData);
expect(result).toBe(true);
```

### 3. Scénarios de Test

- **Validation réussie** : Sauvegarde et passage en mode lecture
- **Validation échouée** : Reste en mode édition avec erreurs
- **Erreur réseau** : Gestion des timeouts et retry
- **Permissions** : Gestion des accès refusés

## Conclusion

La page `ProfessionalProfile.tsx` utilise une architecture modulaire robuste qui :

- ✅ **Sépare les responsabilités** : Chaque section et handler sont indépendants
- ✅ **Gère la validation** : Système de validation strict et fiable
- ✅ **Offre une UX fluide** : Animations et feedback utilisateur
- ✅ **Est extensible** : Facile d'ajouter de nouvelles sections et handlers
- ✅ **Gère les erreurs** : Messages appropriés et récupération
- ✅ **Optimise les performances** : Chargement et mise à jour efficaces
- ✅ **Respecte les bonnes pratiques** : Architecture propre et maintenable

Cette architecture permet une maintenance facile et une évolution progressive de l'application.
