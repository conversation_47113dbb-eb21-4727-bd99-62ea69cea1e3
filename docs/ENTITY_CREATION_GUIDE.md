# 📋 Guide de Création d'Entités - Architecture Clean

Ce guide détaille la création complète d'une nouvelle entité dans l'architecture Clean de l'application, en utilisant l'entité **"Dash"** comme exemple pratique.

## 🏗️ Architecture Overview

L'application suit une **Architecture Clean** avec séparation claire des responsabilités :

```
src/
├── domain/           # Logique métier pure
├── infrastructure/   # Accès aux données externes
├── application/      # Orchestration et état global
└── presentation/     # Interface utilisateur
```

## 📝 Étapes de Création d'une Entité

### 1. 🎯 Domain Layer - Modèles et Interfaces

#### 1.1 Créer le Modèle de Domaine

**Fichier :** `src/domain/models/Dash.ts`

```typescript
export interface Dash {
  id: number;
  utilisateur_id: number;
  nom: string;
  description?: string;
  created_at?: Date;
  updated_at?: Date;
}
```

#### 1.2 Créer les DTOs (Data Transfer Objects)

**Fichier :** `src/domain/DTOS/DashDTO.ts`

```typescript
import { Dash } from "@/domain/models";

/**
 * DTO pour les données complètes d'un dash
 */
export type DashDTO = Dash & {
  user?: {
    nom: string;
    email: string;
  };
  statistics?: {
    totalItems: number;
    lastUpdated: Date;
  };
};

/**
 * DTO pour la création d'un dash
 */
export type CreateDashDTO = Omit<Dash, 'id' | 'created_at' | 'updated_at'>;

/**
 * DTO pour la mise à jour d'un dash
 */
export type UpdateDashDTO = Partial<Omit<Dash, 'id' | 'utilisateur_id'>>;
```

#### 1.3 Définir les Interfaces de Repository

**Fichier :** `src/domain/interfaces/repositories/dash/IDashRepository.ts`

```typescript
import { Dash, DashDTO } from "@/domain/models";

export interface IGetDashByIdRepository {
  execute(id: number): Promise<Dash | null>;
}

export interface IGetDashByUserIdRepository {
  execute(userId: number): Promise<Dash | null>;
}

export interface ICreateDashRepository {
  execute(data: Omit<Dash, 'id'>): Promise<Dash>;
}

export interface IUpdateDashRepository {
  execute(id: number, data: Partial<Dash>): Promise<Dash>;
}

export interface IDeleteDashRepository {
  execute(id: number): Promise<void>;
}

export interface IGetAllDashRepository {
  execute(userId: number): Promise<Dash[]>;
}
```

#### 1.4 Définir les Interfaces de Use Cases

**Fichier :** `src/domain/interfaces/usecases/dash/IDashUsecases.ts`

```typescript
import { Dash } from "@/domain/models";

export interface IGetDashByIdUsecase {
  execute(id: number): Promise<Dash | null>;
}

export interface IGetDashByUserIdUsecase {
  execute(userId: number): Promise<Dash | null>;
}

export interface ICreateDashUsecase {
  execute(data: Omit<Dash, 'id'>): Promise<Dash>;
}

export interface IUpdateDashUsecase {
  execute(id: number, data: Partial<Dash>): Promise<Dash>;
}

export interface IDeleteDashUsecase {
  execute(id: number): Promise<void>;
}
```

#### 1.5 Mettre à jour les exports

**Fichier :** `src/domain/models/index.ts`
```typescript
export * from "./Dash";
```

**Fichier :** `src/domain/DTOS/index.ts`
```typescript
export * from "./DashDTO";
```

### 2. 🔧 Infrastructure Layer - Repositories

#### 2.1 Constantes de Base de Données

**Fichier :** `src/infrastructure/repositories/dash/constants.ts`

```typescript
export const DASH_TABLE_NAME = "dash";
```

#### 2.2 Implémentation des Repositories

**Fichier :** `src/infrastructure/repositories/dash/GetDashByIdRepository.ts`

```typescript
import { supabase } from "@/infrastructure/supabase/supabase";
import { DASH_TABLE_NAME } from "./constants";
import { Dash } from "@/domain/models/Dash";
import { IGetDashByIdRepository } from "@/domain/interfaces/repositories/dash/IDashRepository";

export class GetDashByIdRepository implements IGetDashByIdRepository {
  async execute(id: number): Promise<Dash | null> {
    const { data, error } = await supabase
      .from(DASH_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .maybeSingle();

    if (error) throw error;
    return data as Dash;
  }
}
```

**Fichier :** `src/infrastructure/repositories/dash/CreateDashRepository.ts`

```typescript
import { supabase } from "@/infrastructure/supabase/supabase";
import { DASH_TABLE_NAME } from "./constants";
import { Dash } from "@/domain/models/Dash";
import { ICreateDashRepository } from "@/domain/interfaces/repositories/dash/IDashRepository";

export class CreateDashRepository implements ICreateDashRepository {
  async execute(data: Omit<Dash, 'id'>): Promise<Dash> {
    const { data: result, error } = await supabase
      .from(DASH_TABLE_NAME)
      .insert(data)
      .select()
      .single();

    if (error) throw error;
    return result as Dash;
  }
}
```

#### 2.3 Export des Repositories

**Fichier :** `src/infrastructure/repositories/dash/index.ts`

```typescript
export * from "./GetDashByIdRepository";
export * from "./GetDashByUserIdRepository";
export * from "./CreateDashRepository";
export * from "./UpdateDashRepository";
export * from "./DeleteDashRepository";
export * from "./GetAllDashRepository";
```

### 3. 🎯 Domain Layer - Use Cases

#### 3.1 Créer les Use Cases

**Fichier :** `src/domain/usecases/dash/GetDashByIdUsecase.ts`

```typescript
import { Dash } from "@/domain/models";
import { IGetDashByIdRepository } from "@/domain/interfaces/repositories/dash/IDashRepository";
import { IGetDashByIdUsecase } from "@/domain/interfaces/usecases/dash/IDashUsecases";

export class GetDashByIdUsecase implements IGetDashByIdUsecase {
  constructor(
    private readonly getDashByIdRepository: IGetDashByIdRepository
  ) {}

  async execute(id: number): Promise<Dash | null> {
    if (!id || id <= 0) {
      throw new Error("ID invalide");
    }

    return await this.getDashByIdRepository.execute(id);
  }
}
```

**Fichier :** `src/domain/usecases/dash/CreateDashUsecase.ts`

```typescript
import { Dash } from "@/domain/models";
import { ICreateDashRepository } from "@/domain/interfaces/repositories/dash/IDashRepository";
import { ICreateDashUsecase } from "@/domain/interfaces/usecases/dash/IDashUsecases";

export class CreateDashUsecase implements ICreateDashUsecase {
  constructor(
    private readonly createDashRepository: ICreateDashRepository
  ) {}

  async execute(data: Omit<Dash, 'id'>): Promise<Dash> {
    // Validation métier
    if (!data.nom || data.nom.trim().length === 0) {
      throw new Error("Le nom du dash est requis");
    }

    if (!data.utilisateur_id || data.utilisateur_id <= 0) {
      throw new Error("ID utilisateur invalide");
    }

    // Logique métier supplémentaire si nécessaire
    const dashData = {
      ...data,
      nom: data.nom.trim(),
      created_at: new Date(),
      updated_at: new Date(),
    };

    return await this.createDashRepository.execute(dashData);
  }
}
```

### 4. 🔄 Application Layer - Redux Slices

#### 4.1 Créer le Slice Redux

**Fichier :** `src/application/slices/dash/dashSlice.ts`

```typescript
import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Dash } from "@/domain/models";

// Async Thunks
export const getDashById = createAsyncThunk(
  "dash/getDashById",
  async (id: number, { rejectWithValue }) => {
    try {
      // Ici, vous utiliseriez vos use cases
      // const usecase = new GetDashByIdUsecase(new GetDashByIdRepository());
      // return await usecase.execute(id);
      
      // Pour l'exemple, simulation d'un appel API
      const response = await fetch(`/api/dash/${id}`);
      if (!response.ok) throw new Error("Dash non trouvé");
      return await response.json();
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const createDash = createAsyncThunk(
  "dash/createDash",
  async (data: Omit<Dash, 'id'>, { rejectWithValue }) => {
    try {
      const response = await fetch("/api/dash", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error("Erreur lors de la création");
      return await response.json();
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// State Interface
interface DashState {
  dashes: Dash[];
  selectedDash: Dash | null;
  loading: boolean;
  error: string | null;
}

const initialState: DashState = {
  dashes: [],
  selectedDash: null,
  loading: false,
  error: null,
};

// Slice
const dashSlice = createSlice({
  name: "dash",
  initialState,
  reducers: {
    setSelectedDash: (state, action: PayloadAction<Dash | null>) => {
      state.selectedDash = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetDashState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      // Get Dash By ID
      .addCase(getDashById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDashById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedDash = action.payload;
      })
      .addCase(getDashById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Create Dash
      .addCase(createDash.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createDash.fulfilled, (state, action) => {
        state.loading = false;
        state.dashes.push(action.payload);
      })
      .addCase(createDash.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedDash, clearError, resetDashState } = dashSlice.actions;
export default dashSlice.reducer;
```

#### 4.2 Ajouter au Store

**Fichier :** `src/store/index.ts`

```typescript
import dashReducer from "@/application/slices/dash/dashSlice";

export const store = configureStore({
  reducer: {
    // ... autres reducers
    dash: dashReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
```

### 5. 🎨 Presentation Layer - Hooks et Composants

#### 5.1 Créer le Hook Custom

**Fichier :** `src/presentation/hooks/dash/useDash.ts`

```typescript
import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "../redux";
import { 
  getDashById, 
  createDash, 
  setSelectedDash,
  clearError 
} from "@/application/slices/dash/dashSlice";
import { Dash } from "@/domain/models";

export const useDash = () => {
  const dispatch = useAppDispatch();
  const { dashes, selectedDash, loading, error } = useAppSelector(
    (state) => state.dash
  );

  const fetchDashById = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      return await dispatch(getDashById(id)).unwrap();
    },
    [dispatch]
  );

  const handleCreateDash = useCallback(
    async (data: Omit<Dash, 'id'>) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      return await dispatch(createDash(data)).unwrap();
    },
    [dispatch]
  );

  const selectDash = useCallback(
    (dash: Dash | null) => {
      dispatch(setSelectedDash(dash));
    },
    [dispatch]
  );

  const clearDashError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  return {
    // State
    dashes,
    selectedDash,
    loading,
    error,
    
    // Actions
    fetchDashById,
    handleCreateDash,
    selectDash,
    clearDashError,
  };
};
```

#### 5.2 Créer les Schémas de Validation

**Fichier :** `src/presentation/pages/dash/DashSchema.ts`

```typescript
import { z } from "zod";

export const dashSchema = z.object({
  nom: z.string().min(1, "Nom requis").max(100, "Nom trop long"),
  description: z.string().optional(),
  utilisateur_id: z.number().positive("ID utilisateur invalide"),
});

export type DashFormData = z.infer<typeof dashSchema>;

export const createDashSchema = dashSchema.omit({ utilisateur_id: true });
export type CreateDashFormData = z.infer<typeof createDashSchema>;
```

#### 5.3 Créer les Composants

**Fichier :** `src/presentation/components/features/dash/DashCard.tsx`

```typescript
import React from "react";
import { Card, CardContent, Typography, Box, Chip } from "@mui/material";
import { Dash } from "@/domain/models";
import { Dashboard, Edit, Delete } from "lucide-react";

interface DashCardProps {
  dash: Dash;
  onEdit?: (dash: Dash) => void;
  onDelete?: (id: number) => void;
  onClick?: (dash: Dash) => void;
}

export const DashCard: React.FC<DashCardProps> = ({
  dash,
  onEdit,
  onDelete,
  onClick,
}) => {
  return (
    <Card
      className="hover:shadow-lg transition-shadow cursor-pointer dark:bg-gray-800"
      onClick={() => onClick?.(dash)}
    >
      <CardContent>
        <Box className="flex items-start justify-between mb-2">
          <Box className="flex items-center gap-2">
            <Dashboard size={20} className="text-primary" />
            <Typography variant="h6" className="font-semibold dark:text-gray-100">
              {dash.nom}
            </Typography>
          </Box>

          <Box className="flex gap-1">
            {onEdit && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(dash);
                }}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
              >
                <Edit size={16} />
              </button>
            )}
            {onDelete && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(dash.id);
                }}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-red-500"
              >
                <Delete size={16} />
              </button>
            )}
          </Box>
        </Box>

        {dash.description && (
          <Typography
            variant="body2"
            className="text-gray-600 dark:text-gray-300 mb-2"
          >
            {dash.description}
          </Typography>
        )}

        <Box className="flex justify-between items-center">
          <Chip
            label="Actif"
            size="small"
            color="success"
            variant="outlined"
          />

          {dash.updated_at && (
            <Typography variant="caption" className="text-gray-500">
              Modifié le {new Date(dash.updated_at).toLocaleDateString()}
            </Typography>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};
```

**Fichier :** `src/presentation/pages/dash/MyDashes.tsx`

```typescript
import React, { useEffect, useState } from "react";
import { Box, Typography, Button, Grid } from "@mui/material";
import { Plus, Search } from "lucide-react";
import { useDash } from "@/presentation/hooks/dash/useDash";
import { DashCard } from "@/presentation/components/features/dash/DashCard";
import { useAppSelector } from "@/presentation/hooks/redux";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";

const MyDashes: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const { dashes, loading, fetchDashById, handleCreateDash } = useDash();
  const userId = useAppSelector((state) => state.authentification.userData?.id);

  useEffect(() => {
    if (userId) {
      // Charger les dashes de l'utilisateur
      // fetchDashesByUserId(userId);
    }
  }, [userId]);

  const filteredDashes = dashes.filter((dash) =>
    dash.nom.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreateNew = () => {
    // Ouvrir modal de création
    console.log("Créer nouveau dash");
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <Box className="w-full p-6">
      {/* Header */}
      <Box className="flex justify-between items-center mb-6">
        <Typography variant="h4" className="font-bold dark:text-gray-100">
          Mes Tableaux de Bord
        </Typography>

        <Button
          variant="contained"
          startIcon={<Plus />}
          onClick={handleCreateNew}
          className="bg-primary hover:bg-primary-dark"
        >
          Nouveau Dashboard
        </Button>
      </Box>

      {/* Barre de recherche */}
      <Box className="mb-6">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Rechercher un dashboard..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
          />
        </div>
      </Box>

      {/* Liste des dashes */}
      {filteredDashes.length === 0 ? (
        <Box className="text-center py-12">
          <Typography variant="h6" className="text-gray-500 mb-2">
            Aucun dashboard trouvé
          </Typography>
          <Typography variant="body2" className="text-gray-400">
            {searchQuery
              ? "Aucun dashboard ne correspond à votre recherche"
              : "Créez votre premier dashboard pour commencer"
            }
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {filteredDashes.map((dash) => (
            <Grid item xs={12} sm={6} md={4} key={dash.id}>
              <DashCard
                dash={dash}
                onClick={(dash) => console.log("Ouvrir dash", dash)}
                onEdit={(dash) => console.log("Éditer dash", dash)}
                onDelete={(id) => console.log("Supprimer dash", id)}
              />
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default MyDashes;
```

## 🎯 Bonnes Pratiques et Conventions

### 1. **Nommage des Fichiers**
- **Modèles** : `PascalCase.ts` (ex: `Dash.ts`)
- **DTOs** : `PascalCaseDTO.ts` (ex: `DashDTO.ts`)
- **Repositories** : `VerbEntityRepository.ts` (ex: `GetDashByIdRepository.ts`)
- **Use Cases** : `VerbEntityUsecase.ts` (ex: `CreateDashUsecase.ts`)
- **Hooks** : `useEntity.ts` (ex: `useDash.ts`)
- **Composants** : `PascalCase.tsx` (ex: `DashCard.tsx`)

### 2. **Structure des Dossiers**
```
src/
├── domain/
│   ├── models/
│   │   └── Dash.ts
│   ├── DTOS/
│   │   └── DashDTO.ts
│   ├── interfaces/
│   │   ├── repositories/dash/
│   │   └── usecases/dash/
│   └── usecases/dash/
├── infrastructure/
│   └── repositories/dash/
├── application/
│   └── slices/dash/
└── presentation/
    ├── hooks/dash/
    ├── components/features/dash/
    └── pages/dash/
```

### 3. **Gestion des Erreurs**
- **Use Cases** : Validation métier et erreurs explicites
- **Repositories** : Gestion des erreurs de base de données
- **Hooks** : Gestion des erreurs réseau et d'état
- **Composants** : Affichage des erreurs utilisateur

### 4. **Tests**
```typescript
// src/domain/usecases/dash/__tests__/CreateDashUsecase.test.ts
describe('CreateDashUsecase', () => {
  it('should create dash with valid data', async () => {
    // Test implementation
  });

  it('should throw error with invalid data', async () => {
    // Test implementation
  });
});
```

### 5. **Types et Interfaces**
- Utiliser des **interfaces** pour les contrats
- Utiliser des **types** pour les unions et transformations
- Préférer `Omit<>` et `Pick<>` pour les variations

### 6. **Performance**
- **useMemo** pour les calculs coûteux
- **useCallback** pour les fonctions dans les dépendances
- **React.memo** pour les composants purs
- **Lazy loading** pour les composants volumineux

## 🚀 Checklist de Création d'Entité

- [ ] **Domain Models** créés
- [ ] **DTOs** définis
- [ ] **Interfaces Repository** créées
- [ ] **Interfaces Use Cases** créées
- [ ] **Repositories** implémentés
- [ ] **Use Cases** implémentés
- [ ] **Redux Slice** créé
- [ ] **Hook custom** créé
- [ ] **Schémas de validation** créés
- [ ] **Composants UI** créés
- [ ] **Pages** créées
- [ ] **Tests** écrits
- [ ] **Documentation** mise à jour
- [ ] **Types exportés** dans les index

## 📚 Ressources Supplémentaires

- [Architecture Clean](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [Redux Toolkit](https://redux-toolkit.js.org/)
- [React Hook Form](https://react-hook-form.com/)
- [Zod Validation](https://zod.dev/)

---

**Note :** Cette documentation doit être mise à jour à chaque évolution de l'architecture ou des conventions du projet.
