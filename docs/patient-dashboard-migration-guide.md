# Patient Dashboard Migration Guide

## Overview

This guide documents the migration from the monolithic patient dashboard to the refactored, component-based architecture. It provides step-by-step instructions for adopting the new components and hooks.

## Migration Summary

### Before (Monolithic)
- Single 269-line Dashboard component
- Mixed business logic and presentation
- Hardcoded styling and configuration
- Limited reusability and testability

### After (Refactored)
- 4 focused sub-components + main container
- Separated business logic into specialized hooks
- Centralized configuration and constants
- Improved maintainability and testability

## Step-by-Step Migration

### 1. Update Imports

**Before:**
```tsx
import { FC, useState, useCallback, useEffect } from "react";
import { Calendar, Clock, CheckCircle, BarChart2, Heart, CalendarX } from "lucide-react";
import { XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar, BarChart } from "recharts";
import StatCard from "@/presentation/components/common/StatCard";
import NextAppointment from "@/presentation/components/features/patient/appointments/NextAppointment";
import { useDarkMode } from "@/presentation/hooks/use-dark-mode";
import { getMockHealthScore } from "@/presentation/utils/healthScoreCalculator";
import { useDashboardPatient } from "@/presentation/hooks/dashboard/patient/use-dashboard-patient";
```

**After:**
```tsx
import { FC, useState, useCallback } from "react";
import { useAppSelector } from "@/presentation/hooks/redux";
import NextAppointment from "@/presentation/components/features/patient/appointments/NextAppointment";
import {
  DashboardHeader,
  StatisticsGrid,
  AppointmentChart,
  PersonalStatsCard,
} from "@/presentation/components/features/patient/dashboard";
import { 
  useDashboardPatient,
  useHealthScore,
  useAppointmentActions,
  usePrescriptionActions,
} from "@/presentation/hooks/dashboard/patient";
import { LAYOUT_CONFIG } from "@/presentation/constants/patient-dashboard.constants";
```

### 2. Replace Manual Chart Configuration

**Before:**
```tsx
const tooltipContentStyle = isDarkMode
  ? {
      backgroundColor: "rgba(31, 41, 55, 0.9)",
      borderColor: "#374151",
      color: "#f9fafb",
    }
  : {
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      borderColor: "#e5e7eb",
      color: "#111827",
    };
```

**After:**
```tsx
// Chart configuration is now handled automatically by AppointmentChart component
// No manual configuration needed in the main Dashboard component
```

### 3. Replace Health Score Calculation

**Before:**
```tsx
const healthScore = getMockHealthScore(
  patient?.signe_vitaux,
  appointmentPatient,
);
```

**After:**
```tsx
const healthScore = useHealthScore(patient?.signe_vitaux, appointmentPatient);
```

### 4. Replace Action Handlers

**Before:**
```tsx
const handleCancelAppointment = useCallback((id: number) => {
  console.log("Annulation du rendez-vous:", id);
}, []);
```

**After:**
```tsx
const { cancelAppointment } = useAppointmentActions();

const handleCancelAppointment = useCallback(async (id: number) => {
  const result = await cancelAppointment(id);
  // Handle result with toast notifications
}, [cancelAppointment]);
```

### 5. Replace Component Structure

**Before:**
```tsx
return (
  <div className="p-6 max-w-7xl mx-auto">
    <header className="mb-8">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
        Bonjour, {patient?.nom + " " + patient?.prenom || "Patient"}
      </h1>
      <p className="text-gray-600 dark:text-gray-400 mt-1">
        Voici un aperçu de votre suivi médical
      </p>
    </header>

    <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <StatCard title="Rendez-vous Total" value={totalAppointments} />
      {/* More StatCards... */}
    </section>

    {/* Complex chart JSX... */}
    {/* Complex stats JSX... */}
  </div>
);
```

**After:**
```tsx
const patientName = patient ? `${patient.nom} ${patient.prenom}` : undefined;

return (
  <div className={LAYOUT_CONFIG.mainContainer}>
    <DashboardHeader 
      patientName={patientName}
      className={LAYOUT_CONFIG.fullWidthSection}
    />

    <StatisticsGrid
      totalAppointments={totalAppointments}
      upcomingAppointments={patientMetrics?.newPatients || 0}
      healthStatus={healthScore.status}
      trends={patientMetrics?.trends}
    />

    <div className={LAYOUT_CONFIG.twoColumnGrid}>
      <AppointmentChart
        data={appointmentData}
        isDarkMode={isDarkMode}
        className={LAYOUT_CONFIG.chartColumn}
      />

      <PersonalStatsCard
        appointmentStats={appointmentStats}
        currentWeight={patient?.signe_vitaux?.[0]?.poid}
        className={LAYOUT_CONFIG.statsColumn}
      />
    </div>

    {/* Next appointment section remains similar */}
  </div>
);
```

## New Hook Usage

### useHealthScore
```tsx
// Lightweight health score calculation
const healthScore = useHealthScore(patient?.signe_vitaux, appointmentPatient);
// Returns: { score: number, status: string, color: string }
```

### useAppointmentActions
```tsx
const { cancelAppointment, rescheduleAppointment, loading } = useAppointmentActions();

const handleCancel = async (id: number) => {
  const result = await cancelAppointment(id);
  if (result.success) {
    toast.success(result.message);
  } else {
    toast.error(result.error);
  }
};
```

### usePrescriptionActions
```tsx
const { requestRefill, markAsTaken, loading } = usePrescriptionActions();

const handleRefill = async (id: string) => {
  const result = await requestRefill(id);
  // Handle result
};
```

## Configuration Migration

### Constants Usage
Replace hardcoded values with centralized constants:

```tsx
// Before
<div className="p-6 max-w-7xl mx-auto">
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">

// After
<div className={LAYOUT_CONFIG.mainContainer}>
<StatisticsGrid className={LAYOUT_CONFIG.statisticsGrid} />
```

### Color and Styling
```tsx
// Before
fill="#4F46E5"
bgColor="bg-indigo-50 dark:bg-indigo-900/30"

// After - handled automatically by components using:
// DASHBOARD_CHART_COLORS.primary
// PATIENT_STAT_CARDS.totalAppointments.bgColor
```

## Testing Migration

### Component Tests
```tsx
// Test individual components
import { DashboardHeader } from '@/presentation/components/features/patient/dashboard';

test('renders patient name correctly', () => {
  render(<DashboardHeader patientName="Marie Dubois" />);
  expect(screen.getByText('Bonjour, Marie Dubois')).toBeInTheDocument();
});
```

### Hook Tests
```tsx
// Test specialized hooks
import { useHealthScore } from '@/presentation/hooks/dashboard/patient';

test('calculates health score correctly', () => {
  const { result } = renderHook(() => useHealthScore(mockVitals, mockAppointments));
  expect(result.current.status).toBe('Excellente');
});
```

## Benefits After Migration

### Performance
- ✅ Reduced re-renders through component memoization
- ✅ Optimized calculations with specialized hooks
- ✅ Efficient data flow with focused responsibilities

### Maintainability
- ✅ Clear separation of concerns
- ✅ Focused, testable components
- ✅ Centralized configuration management
- ✅ Comprehensive documentation

### Developer Experience
- ✅ Better code organization
- ✅ Easier debugging and testing
- ✅ Improved reusability
- ✅ Clear architectural patterns

## Rollback Plan

If issues arise, you can temporarily revert by:

1. Keeping the old Dashboard component as `Dashboard.legacy.tsx`
2. Switching imports back to the legacy version
3. Gradually migrating specific sections

## Next Steps

1. **Testing**: Run comprehensive tests on the refactored components
2. **Performance**: Monitor performance improvements
3. **Documentation**: Update any additional documentation
4. **Training**: Brief team on new architecture patterns
5. **Cleanup**: Remove legacy code after successful migration

## Support

For questions about the migration:
- Review the architecture documentation: `src/docs/patient-dashboard-architecture.md`
- Check component README: `src/presentation/components/features/patient/dashboard/README.md`
- Examine hook documentation in individual hook files
