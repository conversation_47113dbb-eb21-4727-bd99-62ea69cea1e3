# Edge Functions supabase

Pour executer les section de code dans cette repertoire, il faut que:
- [Supabase-cli](https://supabase.com/docs/guides/local-development/cli/getting-started?queryGroups=platform&platform=npx) soit installee sur le systeme ou en utilisant le gestionnaire de package `npm`
- [Deno](https://deno.com/) Supabase-cli utilise Deno non pas Node.js

## Ajouter une nouvelle edge function

Lancer la commande:
```bash
supabase functions new <nom-du-edge-function>
```
Cette commande va generer une nouvelle repertoire dans `./supabase/functions/<nom-du-edge-function>` avec les fichiers de base notament `index.ts`. C'est le point d'entree de la fonction

## Tester une fonction localement:

Lancer la commande:
```bash
supabase functions serve <nom-du-edge-function> --no-verify-jwt
```

Le drapeau `--no-verify-jwt` indique qu'il ne faut pas verifier le token supabase dans une execution en local. Il est recommandee de ne pas la mettre lors de la deploiement de la fonction pour raison de securite.

## Definir de cle (variable)

- **En local**:  
Ajouter le cle dans le fichier `.env`

- **En production (deployee)**:  
Lancer la commande:
```bash
supabase secrets set <nom>:<Contenu>
```

## Deployement du fonction

Lancer la commande:
```bash
supabase functions deploy <nom-du-edge-function>
```

## Plus d'info

Pour plus d'info, rendez-vous sur la [documentation officiel](https://supabase.com/docs/guides/functions)