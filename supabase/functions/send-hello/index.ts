Deno.serve(async (req: Request): Promise<Response> => {
  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Méthode non autorisée" }), {
      status: 405,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    const { to, name } = await req.json();
    if (!to) {
      return new Response(
        JSON.stringify({ error: "L'email du destinataire est requis" }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }

    const BREVO_API_KEY = Deno.env.get("BREVO_API_KEY");
    const BREVO_SENDER = { email: "<EMAIL>", name: "hubs" };

    if (!BREVO_API_KEY) {
      return new Response(
        JSON.stringify({ error: "Clé API Brevo manquante" }),
        { status: 500, headers: { "Content-Type": "application/json" } },
      );
    }

    const response = await fetch("https://api.brevo.com/v3/smtp/email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": BREVO_API_KEY,
      },
      body: JSON.stringify({
        sender: BREVO_SENDER,
        to: [{ email: to }],
        subject: `Hello ${name || "World"}!`,
        htmlContent: `<p>Hello from Supabase Functions!</p>`,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      return new Response(
        JSON.stringify({
          error: "Échec de l'envoi d'email",
          details: errorText,
        }),
        { status: 500, headers: { "Content-Type": "application/json" } },
      );
    }

    return new Response(
      JSON.stringify({ message: "Email envoyé avec succès" }),
      { status: 200, headers: { "Content-Type": "application/json" } },
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: "Erreur interne",
        details: error instanceof Error ? error.message : error,
      }),
      { status: 500, headers: { "Content-Type": "application/json" } },
    );
  }
});
