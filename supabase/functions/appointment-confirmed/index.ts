Deno.serve(async (req: Request) => {
  const origin = req.headers.get("origin") ?? "*";

  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 204,
      headers: {
        "Access-Control-Allow-Origin": origin,
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Max-Age": "86400",
      },
    });
  }

  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Méthode non autorisée" }),
      {
        status: 405,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": origin,
        },
      },
    );
  }

  try {
    const {
      to,
      FirstName,
      DoctorName,
      AppointmentDate,
      AppointmentTime,
      Location,
    } = await req.json();

    if (
      !to || !FirstName || !DoctorName || !AppointmentDate ||
      !AppointmentTime || !Location
    ) {
      return new Response(
        JSON.stringify({
          error:
            "Tous les champs sont requis (to, FirstName, DoctorName, AppointmentDate, AppointmentTime, Location)",
        }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": origin,
          },
        },
      );
    }

    const BREVO_API_KEY = Deno.env.get("BREVO_API_KEY");
    const TEMPLATE_ID = 10;

    if (!BREVO_API_KEY) {
      return new Response(
        JSON.stringify({ error: "Clé API Brevo manquante" }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": origin,
          },
        },
      );
    }

    const response = await fetch("https://api.brevo.com/v3/smtp/email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": BREVO_API_KEY,
      },
      body: JSON.stringify({
        to: [{ email: to }],
        templateId: TEMPLATE_ID,
        params: {
          FirstName,
          DoctorName,
          AppointmentDate,
          AppointmentTime,
          Location,
          Email: to, // Pour l'afficher en bas dans le footer
        },
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      return new Response(
        JSON.stringify({
          error: "Échec de l'envoi de l'e-mail",
          details: errorText,
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": origin,
          },
        },
      );
    }

    return new Response(
      JSON.stringify({
        message: "E-mail de confirmation envoyé avec succès",
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": origin,
        },
      },
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: "Erreur interne",
        details: error instanceof Error ? error.message : error,
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": origin,
        },
      },
    );
  }
});
