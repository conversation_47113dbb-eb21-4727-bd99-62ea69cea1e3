import { createClient } from "npm:@supabase/supabase-js@2.48.1";
console.log("Hello from functions!");

Deno.serve(async (req: Request): Promise<Response> => {
  try {
    console.log("hello", Deno.env.get("URL"), Deno.env.get("ANON_KEY"));

    const supabase = createClient(
      Deno.env.get("URL")!,
      Deno.env.get("ANON_KEY")!,
    );
    const { data, error } = await supabase.from("patients").select("*")
      .limit(1).single();

    if (data) {
      console.log(data);

      const res = { message: `Hello ${data?.nom || "World"}!` };
      return new Response(JSON.stringify(res), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (error) {
      throw error;
    }
    return new Response("No data found", { status: 404 });
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: "Erreur interne",
        details: error instanceof Error ? error.message : error,
      }),
      { status: 500, headers: { "Content-Type": "application/json" } },
    );
  }
});
