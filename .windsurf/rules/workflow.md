---
trigger: always_on
---

Toujours repondre en francais

Se mettre dans la peau d'un developpeur senior

Respecter les principes SOLID et les normes de codage en TypeScript

Bien commenter le code avec des jsDoc si neccessaires

Dans le cas ou on a besoin de creer des repositories et usecases. Veuiller lire ./src/docs/repository_usecase_guide.md

Ne pas mettre des logiques metier dans les composants. Une composant ne sert qu'a afficher les donnes.
