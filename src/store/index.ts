import { configureStore } from "@reduxjs/toolkit";
import listeSpecialitesReducer from "@/application/slices/professionnal/listeSpecialitesSlice";
import listeInsurancesReducer from "@/application/slices/professionnal/listeInsurancesSlice";
import authSliceReducer from "@/application/slices/auth/authSlice";
import availabilitySettignsStateSliceReducer from "@/application/slices/statesInComponent/availabilitySettignsStateSlice";
import locationSliceReducer from "@/application/slices/locationSelector/locationSlice";
import typeEtablissementSliceReducer from "@/application/slices/professionnal/typeEtablissementSlice";
import availabilitySettignsSliceReducer from "@/application/slices/professionnal/availabilitySettingsSlice";
import evenementSliceReducer from "@/application/slices/professionnal/evenementSlice";
import searchProfessionalsReducer from "@/application/slices/professionnal/searchProfessionalSlice";
import professionalEtablishmentSliceReducer from "@/application/slices/professionnal/professionalEtablishmentSlice";
import professionalReducer from "@/application/slices/professionnal/professionnalSlice";
import professionalProfileReducer from "@/application/slices/professionnal/professionalProfileSlice";
import consultationStateSliceReducer from "@/application/slices/patient/consultationStateSlice";
import { previewReducer } from "@/presentation/hooks/preview/use-cabinet-medical-preview";

import notificationSliceReducer from "@/application/slices/notification/notificationSlice";
import appointmentReducer from "@/application/slices/professionnal/appointmentSlice";
import addEventStateSliceReducer from "@/application/slices/statesInComponent/addEventStateSlice";
import adhesionRequestSliceReducer from "@/application/slices/admin/adhesionRequestSlice";
import prochePatientSliceReducer from "@/application/slices/patient/prochePatientSlice";
import medicalConsultationReducer from "@/application/slices/professionnal/medicalConsultationSlice";
import professionalPatientSliceReducer from "@/application/slices/professionnal/professionnelPatientSlice";

import affectationMedicaleSliceReducer from "@/application/slices/professionnal/affectationMedicaleSlice";
import antecedentSociauxSliceReducer from "@/application/slices/professionnal/antecedentSociauxSlice";
import dispositifMedicauxSliceReducer from "@/application/slices/professionnal/dispositifMedicauxSlice";
import antecedantChirurgicauxSliceReducer from "@/application/slices/professionnal/antecedantChirurgicauxSlice";
import antecedantFamiliauxSliceReducer from "@/application/slices/professionnal/antecedantFamiliauxSlice";
import vaccinationSliceReducer from "@/application/slices/professionnal/vaccinationSlice";
import medicamentSliceReducer from "@/application/slices/professionnal/medicamentSlice";
import allergieSliceReducer from "@/application/slices/professionnal/allergieSlice";
import carnetSanteSliceReducer from "@/application/slices/professionnal/carnetSanteSlice";
import historiqueCarnetSanteSliceReducer from "@/application/slices/professionnal/historiqueCarnetSanteSlice";
import signeVitauxReducer from "@/application/slices/professionnal/signeVitauxSlice";
import antecedentGrossesseSliceReducer from "@/application/slices/professionnal/antecedentGrossesseSlice";
import conditionGynecologiqueSliceReducer from "@/application/slices/professionnal/conditionGynecologiqueSlice";
import urgenceSliceReducer from "@/application/slices/patient/urgenceSlice";
import diagnosticSliceReducer from "@/application/slices/professionnal/diagnosticSlice";
import facturationSliceReducer from "@/application/slices/professionnal/facturationSlice";
import patientSliceReducer from "@/application/slices/patient/patientSlice";
import messageSliceReducer from "@/application/slices/message/messageSlice";
import conversationSliceReducer from "@/application/slices/conversation/conversationSlice";
import StocksSliceReducer from "@/application/slices/professionnal/professionalStockSlice";
import employerSliceReducer from "@/application/slices/employer/employerSlice";
import dashInvitationsSliceReducer from "@/application/slices/admin/dashInvitationsSlice";
import userSliceReducer from "@/application/slices/auth/userSlice";

export const store = configureStore({
  reducer: {
    listeSpecialites: listeSpecialitesReducer,
    listeInsurances: listeInsurancesReducer,
    authentification: authSliceReducer,
    availabilityState: availabilitySettignsStateSliceReducer,
    location: locationSliceReducer,
    typeEtablissement: typeEtablissementSliceReducer,
    availabilitySettings: availabilitySettignsSliceReducer,
    evenement: evenementSliceReducer,
    searchProfessionals: searchProfessionalsReducer,
    professionalEtablishment: professionalEtablishmentSliceReducer,
    professional: professionalReducer,
    professionalProfile: professionalProfileReducer,
    consultationState: consultationStateSliceReducer,
    notification: notificationSliceReducer,
    appointment: appointmentReducer,
    addEventState: addEventStateSliceReducer,
    adhesionRequest: adhesionRequestSliceReducer,
    prochePatient: prochePatientSliceReducer,
    medicalConsultation: medicalConsultationReducer,
    professionnelPatient: professionalPatientSliceReducer,
    affectationMedicale: affectationMedicaleSliceReducer,
    antecedentSociaux: antecedentSociauxSliceReducer,
    dispositifMedicaux: dispositifMedicauxSliceReducer,
    antecedantChirurgicaux: antecedantChirurgicauxSliceReducer,
    antecedantFamiliaux: antecedantFamiliauxSliceReducer,
    vaccination: vaccinationSliceReducer,
    medicament: medicamentSliceReducer,
    allergies: allergieSliceReducer,
    carnetSante: carnetSanteSliceReducer,
    historiqueCarnetSante: historiqueCarnetSanteSliceReducer,
    signeVitaux: signeVitauxReducer,
    antecedentGrossesse: antecedentGrossesseSliceReducer,
    conditionGynecologiqueSlice: conditionGynecologiqueSliceReducer,
    urgence: urgenceSliceReducer,
    diagnostic: diagnosticSliceReducer,
    facturation: facturationSliceReducer,
    preview: previewReducer,
    patient: patientSliceReducer,
    message: messageSliceReducer,
    conversation: conversationSliceReducer,
    professionalStock: StocksSliceReducer,
    employer: employerSliceReducer,
    dashInvitations: dashInvitationsSliceReducer,
    user: userSliceReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
