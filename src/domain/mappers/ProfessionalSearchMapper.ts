import {
  SearchProfessionalDTO,
  ProfessionalCardDTO,
  TimeSlotProffessionalCard,
} from "@/domain/DTOS/ProfessionalDTO";
import {
  Contact,
  horaire_hebdomadaire,
  horaire_date_specifique,
  creneau_horaire,
} from "@/domain/models";
import { PhotoTypeEnum } from "@/domain/models/enums";
import { IProfessionalAvailabilitiesFilter } from "@/domain/interfaces/services/IProfessionalAvaiabilitiesFilter";

/**
 * Mapper pour transformer les données de recherche de professionnels
 * du format repository vers le format attendu par le frontend
 */
export class ProfessionalSearchMapper {
  /**
   * Transforme un SearchProfessionalDTO en ProfessionalCardDTO
   * @param searchData Données brutes du repository
   * @param today Date actuelle pour filtrer les créneaux disponibles
   * @param availabilitiesFilter Service de filtrage des disponibilités
   * @returns Données formatées pour l'affichage en carte
   */
  static toProfessionalCardDTO(
    searchData: SearchProfessionalDTO,
    today?: string,
    availabilitiesFilter?: IProfessionalAvailabilitiesFilter
  ): ProfessionalCardDTO {
    const todayDate = today ? new Date(today) : new Date();

    // Extraction des créneaux de disponibilité futurs
    console.log(
      "🔍 Debug disponibilité - Paramètres reçus:",
      searchData.parametre_disponibilite
    );

    let disponibilite: TimeSlotProffessionalCard[] = [];

    // Utiliser le service éprouvé si disponible et si on a des paramètres de disponibilité
    if (
      availabilitiesFilter &&
      searchData.parametre_disponibilite &&
      searchData.parametre_disponibilite.length > 0
    ) {
      console.log("✅ Utilisation du service ProfessionalAvailabilitiesFilter");

      const firstAvailabilityParam = searchData.parametre_disponibilite[0];
      const rendezVous = searchData.rendez_vous || [];
      const evenements = searchData.utilisateurs?.evenement || [];
      const tempsConsultation =
        firstAvailabilityParam.temps_moyen_consulation || 30;

      disponibilite = availabilitiesFilter.filter(
        firstAvailabilityParam,
        rendezVous,
        evenements,
        tempsConsultation
      );

      console.log("📅 Créneaux extraits via service:", disponibilite.length);
    } else {
      console.log(
        "❌ Service non disponible, utilisation de la méthode de fallback"
      );
      disponibilite = this.extractAvailableTimeSlotsFromParams(
        searchData.parametre_disponibilite || [],
        searchData.id,
        todayDate
      );
      console.log("📅 Créneaux extraits via fallback:", disponibilite.length);
    }

    // Extraction des données depuis les utilisateurs
    const contacts = searchData.utilisateurs?.contact || [];
    const evenements = searchData.utilisateurs?.evenement || [];
    const photos = searchData.utilisateurs?.photos || [];
    const email = searchData.utilisateurs?.email || undefined;

    console.log("📧 Email extrait depuis utilisateurs:", email);

    return {
      // Informations de base du professionnel
      id: searchData.id,
      utilisateur_id: searchData.utilisateur_id,
      titre: searchData.titre,
      nom: searchData.nom,
      prenom: searchData.prenom,
      sexe: searchData.sexe,
      numero_ordre: searchData.numero_ordre,
      raison_sociale: searchData.raison_sociale,
      nif: searchData.nif,
      stat: searchData.stat,
      presentation_generale: searchData.presentation_generale,
      types_consultation: searchData.types_consultation,
      modes_paiement_acceptes: searchData.modes_paiement_acceptes,
      nouveau_patient_acceptes: searchData.nouveau_patient_acceptes,
      adresse: searchData.adresse,
      region: searchData.region,
      district: searchData.district,
      commune: searchData.commune,
      fokontany: searchData.fokontany,
      informations_acces: searchData.informations_acces,
      geolocalisation: searchData.geolocalisation,

      // Relations transformées
      specialite: searchData.specialites_professionnel || [],
      disponibilite,
      etablissements_professionnel:
        searchData.etablissements_professionnel || [],
      contacts: contacts,
      motCles: searchData.mot_cles_professionnel || [],
      email,

      // Champs maintenant disponibles dans SearchProfessionalDTO
      evenements: evenements,
      horaire_hebdomadaire: this.extractHoraireHebdomadaire(
        searchData.parametre_disponibilite || []
      ),
      publications: searchData.publication_professionnel || [],
      experiences: searchData.experience_professionnel || [],
      diplomes: searchData.diplome_professionnel || [],
      langues: searchData.langues_parlees_professionnel || [],
      photos: this.transformPhotos(photos, searchData.utilisateur_id),
    };
  }

  /**
   * Transforme une liste de SearchProfessionalDTO en ProfessionalCardDTO
   * @param searchDataList Liste des données brutes du repository
   * @param today Date actuelle pour filtrer les créneaux disponibles
   * @param availabilitiesFilter Service de filtrage des disponibilités
   * @returns Liste des données formatées pour l'affichage
   */
  static toProfessionalCardDTOList(
    searchDataList: SearchProfessionalDTO[],
    today?: string,
    availabilitiesFilter?: IProfessionalAvailabilitiesFilter
  ): ProfessionalCardDTO[] {
    return searchDataList.map((data) =>
      this.toProfessionalCardDTO(data, today, availabilitiesFilter)
    );
  }

  /**
   * Extrait les créneaux de disponibilité futurs à partir des paramètres de disponibilité
   * @param parametres Paramètres de disponibilité du professionnel
   * @param professionalId ID du professionnel
   * @param todayDate Date de référence
   * @returns Liste des créneaux disponibles
   */
  private static extractAvailableTimeSlotsFromParams(
    parametres: any[],
    professionalId: number,
    todayDate: Date
  ): TimeSlotProffessionalCard[] {
    console.log(
      "🔍 Debug extractAvailableTimeSlotsFromParams - Paramètres:",
      parametres
    );
    const timeSlots: TimeSlotProffessionalCard[] = [];

    if (!parametres || parametres.length === 0) {
      console.log("❌ Aucun paramètre de disponibilité trouvé");
      return timeSlots;
    }

    parametres.forEach((param) => {
      console.log("🔍 Traitement paramètre:", param);

      // Traitement des horaires hebdomadaires
      if (
        param.horaire_hebdomadaire &&
        Array.isArray(param.horaire_hebdomadaire)
      ) {
        console.log(
          "📅 Horaires hebdomadaires trouvés:",
          param.horaire_hebdomadaire.length
        );

        param.horaire_hebdomadaire.forEach((horaire: any) => {
          console.log("🔍 Traitement horaire:", horaire);

          if (
            horaire.creneau_horaire &&
            Array.isArray(horaire.creneau_horaire)
          ) {
            console.log("⏰ Créneaux trouvés:", horaire.creneau_horaire.length);

            horaire.creneau_horaire.forEach((creneau: any) => {
              console.log("🔍 Traitement créneau:", creneau);

              // Génère les créneaux pour les 30 prochains jours
              for (let i = 0; i < 30; i++) {
                const date = new Date(todayDate);
                date.setDate(date.getDate() + i);

                // Vérifie si le jour correspond au jour de la semaine de l'horaire
                if (this.isDayMatching(date, horaire.jour)) {
                  const slot = {
                    id_professionnel: professionalId,
                    date: date.toISOString().split("T")[0],
                    start: creneau.heure_debut,
                    end: creneau.heure_fin,
                  };
                  timeSlots.push(slot);
                  console.log("✅ Créneau ajouté:", slot);
                }
              }
            });
          } else {
            console.log("❌ Pas de créneaux horaires pour:", horaire.jour);
          }
        });
      } else {
        console.log("❌ Pas d'horaires hebdomadaires dans le paramètre");
      }

      // Traitement des horaires de dates spécifiques
      if (
        param.horaire_date_specifique &&
        Array.isArray(param.horaire_date_specifique)
      ) {
        param.horaire_date_specifique.forEach((horaire: any) => {
          const horairDate = new Date(horaire.date);
          if (horairDate >= todayDate && horaire.creneau_horaire) {
            horaire.creneau_horaire.forEach((creneau: any) => {
              timeSlots.push({
                id_professionnel: professionalId,
                date: horairDate.toISOString().split("T")[0],
                start: creneau.heure_debut,
                end: creneau.heure_fin,
              });
            });
          }
        });
      }
    });

    console.log("📊 Total créneaux générés:", timeSlots.length);

    // Tri par date et heure
    return timeSlots.sort((a, b) => {
      const dateCompare = a.date.localeCompare(b.date);
      if (dateCompare !== 0) return dateCompare;
      return a.start.localeCompare(b.start);
    });
  }

  /**
   * Extrait l'email depuis la liste des contacts
   * @param contacts Liste des contacts du professionnel
   * @returns Email du professionnel ou undefined
   */
  private static extractEmailFromContacts(
    contacts: Contact[]
  ): string | undefined {
    console.log(
      "🔍 Debug extractEmailFromContacts - Contacts reçus:",
      contacts
    );

    if (!contacts || contacts.length === 0) {
      console.log("❌ Aucun contact trouvé");
      return undefined;
    }

    // Note: La table contact dans Supabase n'a pas de champ contact_type
    // On suppose que l'email est identifiable par un format d'email
    const emailContact = contacts.find((contact) => {
      console.log(
        "🔍 Vérification contact:",
        contact,
        "contient @:",
        contact.numero?.includes("@")
      );
      return contact.numero && contact.numero.includes("@");
    });

    const email = emailContact?.numero || undefined;
    console.log("📧 Email extrait:", email);
    console.log(
      "ℹ️ Note: Si pas d'email, c'est normal - les contacts peuvent être des téléphones"
    );
    return email;
  }

  /**
   * Vérifie si une date correspond à un jour de la semaine donné
   * @param date Date à vérifier
   * @param jour Jour de la semaine (format français)
   * @returns true si la date correspond au jour
   */
  private static isDayMatching(date: Date, jour: string): boolean {
    const dayNames = [
      "dimanche",
      "lundi",
      "mardi",
      "mercredi",
      "jeudi",
      "vendredi",
      "samedi",
    ];
    const dayIndex = date.getDay();
    return dayNames[dayIndex].toLowerCase() === jour.toLowerCase();
  }

  /**
   * Extrait les horaires hebdomadaires depuis les paramètres de disponibilité
   * @param parametres Paramètres de disponibilité
   * @returns Liste des horaires hebdomadaires
   */
  private static extractHoraireHebdomadaire(
    parametres: Array<{
      horaire_hebdomadaire?: horaire_hebdomadaire[];
    }>
  ): horaire_hebdomadaire[] {
    const horaires: horaire_hebdomadaire[] = [];
    parametres.forEach((param) => {
      if (
        param.horaire_hebdomadaire &&
        Array.isArray(param.horaire_hebdomadaire)
      ) {
        horaires.push(...param.horaire_hebdomadaire);
      }
    });
    return horaires;
  }

  /**
   * Transforme les photos du format Supabase vers le format attendu
   * @param photos Photos depuis Supabase
   * @param utilisateur_id ID de l'utilisateur
   * @returns Photos formatées
   */
  private static transformPhotos(
    photos: Array<{
      id: number;
      path: string;
      type: string;
    }>,
    utilisateur_id: number
  ) {
    return photos.map((photo) => ({
      id: photo.id,
      path: photo.path,
      type: photo.type as PhotoTypeEnum,
      utilisateur_id,
    }));
  }
}
