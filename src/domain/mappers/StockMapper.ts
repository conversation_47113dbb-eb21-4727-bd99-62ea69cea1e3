import { StockDTO } from "../DTOS/StockDTO.ts";
import { Categories } from "../models/Categories.ts";
import { EntreesStocks } from "../models/EntreesStocks.ts";
import { Fournisseurs } from "../models/Fournisseurs.ts";
import { Lots } from "../models/Lots.ts";
import { SortiesStocks } from "../models/SortiesStocks.ts";

/**
 * Mapper pour transformer les données de stocks
 * du format Supabase brut vers StockDTO
 */
export class StockMapper {
  /**
   * Transforme une entrée brute Supabase en StockDTO
   * @param raw Données brutes du repository
   * @returns StockDTO formaté
   */
  static toStockDTO(raw: any): StockDTO {
    const fournisseurs: Fournisseurs[] =
      raw.entrees?.map((e: any) => e.fournisseur) || [];

    const uniqueFournisseurs = Array.from(
      new Set(fournisseurs.map((f: any) => f.id))
    ).map((id: any) => fournisseurs.find((f: any) => f.id === id));

    const categories: Categories[] = raw.categorie ? [raw.categorie] : [];

    const uniqueCategories = Array.from(
      new Set(categories.map((c: any) => c.id))
    ).map((id: any) => categories.find((c: any) => c.id === id));

    const entrees: EntreesStocks[] =
      raw.entrees.map((e: any) => ({
        id: e.id,
        stock_id: e.stock_id,
        fournisseur_id: e.fournisseur_id,
        quantite: e.quantite,
        prix_unitaire: e.prix_unitaire,
        date_entree: e.date_entree,
      })) || [];

    const uniqueEntrees = Array.from(
      new Set(entrees.map((e: any) => e.id))
    ).map((id: any) => entrees.find((e: any) => e.id === id));

    const lots: Lots[] =
      raw.entrees?.flatMap(
        (e: any) =>
          e.lots.map((l: any) => ({
            id: l.id,
            entree_id: e.id,
            numero_lot: l.numero_lot,
            quantite: l.quantite,
            date_expiration: l.date_expiration,
          })) || []
      ) || [];

    const uniqueLots = Array.from(new Set(lots.map((l: any) => l.id))).map(
      (id: any) => lots.find((l: any) => l.id === id)
    );

    const sorties: SortiesStocks[] =
      raw.entrees?.flatMap(
        (e: any) => e.lots?.flatMap((l: any) => l.sorties || []) || []
      ) || [];

    const uniqueSorties = Array.from(
      new Set(sorties.map((s: any) => s.id))
    ).map((id: any) => sorties.find((s: any) => s.id === id));

    const out: StockDTO = {
      id: raw.id,
      utilisateur_id: raw.utilisateur_id,
      nom: raw.nom,
      description: raw.description,
      categorie_id: raw.categorie_id,
      unite: raw.unite,
      seuil_alerte: raw.seuil_alerte,
      cree_le: raw.cree_le,
      fournisseurs: uniqueFournisseurs,
      categories: uniqueCategories,
      lots: uniqueLots,
      entrees: uniqueEntrees,
      sorties: uniqueSorties,
    };

    return out;
  }

  /**
   * Transforme une liste de résultats Supabase en liste de StockDTO
   * @param rawList Liste des données brutes Supabase
   * @returns Liste des StockDTO
   */
  static toStockDTOList(rawList: any[]): StockDTO[] {
    return rawList.map(this.toStockDTO);
  }
}
