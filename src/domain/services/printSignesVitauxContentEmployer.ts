import { ProfessionalCardDTO } from "../DTOS";
import { Dash, Employer, Patient, signe_vitaux } from "../models";
import { PRIMARY, SECONDARY, BLACK } from "@/shared/constants/Color";

export const printSignesVitauxContentEmployer = (
  signeVitaux: Omit<signe_vitaux, "id">,
  employer: Employer,
  dash: Dash
) => {
  // Convert hex colors to RGB for CSS variables
  const primaryColor = PRIMARY || "#5dd3fb";
  const secondaryColor = SECONDARY || "#9CE6FE";
  const blackColor = BLACK || "#082a4d";

  const currentDate = new Date().toLocaleDateString("fr-FR");
  const birthDate = new Date(employer?.date_de_naissance).toLocaleDateString(
    "fr-FR"
  );
  const age = Math.floor(
    (new Date().getTime() - new Date(employer?.date_de_naissance).getTime()) /
      (365.25 * 24 * 60 * 60 * 1000)
  );

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Signes Vitaux</title>
        <style>
          :root {
            --primary-color: ${primaryColor};
            --secondary-color: ${secondaryColor};
            --black-color: ${blackColor};
            --background-color: #ffffff;
            --text-color: #333333;
            --border-color: #e2e8f0;
            --item-bg-color: #f8f9fa;
            --shadow-color: rgba(0, 0, 0, 0.1);
          }

          @media (prefers-color-scheme: dark) {
            :root {
              --background-color: #1a202c;
              --text-color: #f7fafc;
              --border-color: #2d3748;
              --item-bg-color: #2d3748;
              --shadow-color: rgba(0, 0, 0, 0.3);
            }
          }

          * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-color);
            color: var(--text-color);
          }

          h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 10px;
          }

          .titre {
            text-align: center;
          }

          .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: var(--background-color);
            border-radius: 8px;
            box-shadow: 0 4px 6px var(--shadow-color);
            overflow: hidden;
          }

          header {
            text-align: center;
            padding-bottom: 20px;
          }

          .patient-info {
            background-color: #f8f9fa;
            border-radius: 5px;
            margin: 1rem 20px;
            margin-bottom: 20px;
          }
  
          .patient-info p {
            margin-bottom: 5px;
          }

          .content {
            padding: 0 20px 20px;
          }

          .vitals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
          }

          .vital-item {
            padding: 16px;
            background-color: var(--item-bg-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px var(--shadow-color);
            border-left: 4px solid var(--primary-color);
            transition: transform 0.2s ease;
          }

          .vital-item:hover {
            transform: translateY(-2px);
          }

          .vital-label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
            font-size: 14px;
            text-transform: uppercase;
          }

          .vital-value {
            font-size: 20px;
            font-weight: 500;
          }

          .vital-unit {
            font-size: 14px;
            color: #718096;
            margin-left: 4px;
          }

          @media print {
            body {
              margin: 0;
              padding: 0;
              background-color: white;
              color: black;
            }

            .container {
              box-shadow: none;
              max-width: 100%;
            }

            .vital-item {
              border: 1px solid var(--border-color);
              box-shadow: none;
              page-break-inside: avoid;
            }

            .vital-label {
              color: var(--black-color);
            }
          }

          @media (max-width: 600px) {
            .vitals-grid {
              grid-template-columns: 1fr;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <header>
            <h1>${dash?.nom}</h1>
          </header>
          
          <h1 class="titre">Signe Viaux</h1>
            
          <div>
            <div class="patient-info">
            <p><strong>Date :</strong> ${currentDate}</p>
            <p><strong>NOM et Prénoms :</strong> ${employer?.nom} ${employer?.prenom}</p>
            <p><strong>Date de Naissance :</strong> ${birthDate} (Age: ${age} ans)</p>
            <p><strong>Adresse :</strong> ${employer?.matricule || "Non renseignée"}</p>
          </div>

          <div class="content">
            <div class="vitals-grid">
              <div class="vital-item">
                <div class="vital-label">Taille</div>
                <div class="vital-value">${signeVitaux.taille || "-"}<span class="vital-unit">cm</span></div>
              </div>

              <div class="vital-item">
                <div class="vital-label">Poids</div>
                <div class="vital-value">${signeVitaux.poid || "-"}<span class="vital-unit">kg</span></div>
              </div>

              <div class="vital-item">
                <div class="vital-label">Température</div>
                <div class="vital-value">${signeVitaux.temperature || "-"}<span class="vital-unit">°C</span></div>
              </div>

              <div class="vital-item">
                <div class="vital-label">Indice de masse corporelle</div>
                <div class="vital-value">${signeVitaux.indice_masse_corporel || "-"}</div>
              </div>

              <div class="vital-item">
                <div class="vital-label">Circonférence de la tête</div>
                <div class="vital-value">${signeVitaux.circonference_tete || "-"}<span class="vital-unit">cm</span></div>
              </div>

              <div class="vital-item">
                <div class="vital-label">Fréquence cardiaque</div>
                <div class="vital-value">${signeVitaux.frequence_cardiaque || "-"}<span class="vital-unit">bpm</span></div>
              </div>

              <div class="vital-item">
                <div class="vital-label">Fréquence respiratoire</div>
                <div class="vital-value">${signeVitaux.frequence_respiratoire || "-"}<span class="vital-unit">rpm</span></div>
              </div>

              <div class="vital-item">
                <div class="vital-label">SAO2</div>
                <div class="vital-value">${signeVitaux.sa02 || "-"}<span class="vital-unit">%</span></div>
              </div>

              <div class="vital-item">
                <div class="vital-label">Niveau de glucose</div>
                <div class="vital-value">${signeVitaux.niveau_glucose || "-"}<span class="vital-unit">mg/dL</span></div>
              </div>
              
              <div class="vital-item">
                <div class="vital-label">Tension artérielle</div>
                <div class="vital-value">${signeVitaux.tension_arterielle || "-"}<span class="vital-unit">mmHg/dL</span></div>
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  `;
};
