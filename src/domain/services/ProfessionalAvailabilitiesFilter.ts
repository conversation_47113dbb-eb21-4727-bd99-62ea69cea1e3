import { AvailabilitySettingsDTO, TimeSlotProffessionalCard } from "../DTOS";
import { IDateSplitter } from "../interfaces/services/IDateSplitter";
import { RendezVous, Evenement } from "../models";
import { IProfessionalAvailabilitiesFilter } from "../interfaces/services/IProfessionalAvaiabilitiesFilter";
import { rendez_vous_statut_enum } from "../models/enums";
import LoadEventService from "./LoadEventService.ts";

class ProfessionalAvailabilitiesFilter
  implements IProfessionalAvailabilitiesFilter
{
  constructor(
    private readonly timeSplitter: IDateSplitter,
    private readonly loadEvent: LoadEventService
  ) {}

  isSameAvailability(
    a: TimeSlotProffessionalCard,
    b: TimeSlotProffessionalCard
  ) {
    return a.date === b.date && a.start === b.start && a.end === b.end;
  }

  filter(
    availabilitySettings: AvailabilitySettingsDTO,
    appointments: RendezVous[],
    events: Evenement[],
    timeConsultationAverage: number
  ) {
    const out: TimeSlotProffessionalCard[] = [];

    const allEvents = this.loadEvent.execute({
      disponibilites: availabilitySettings,
      evenement: events,
      appointments: appointments,
    });

    const settings: TimeSlotProffessionalCard[] = [];
    allEvents
      .filter(
        (setting) =>
          setting.type === "disponibilite" || setting.type === "exception"
      )
      .map((event) => {
        const out = this.timeSplitter.splitDate(
          new Date(event.start),
          new Date(event.end),
          timeConsultationAverage
        );
        settings.push(...out);
      });

    const unavailabilities: TimeSlotProffessionalCard[] = [];

    allEvents
      .filter(
        (setting) =>
          setting.type === "evenement" ||
          setting.type === rendez_vous_statut_enum.A_VENIR
      )
      .map((event) => {
        const out = this.timeSplitter.splitDate(
          new Date(event.start),
          new Date(event.end),
          timeConsultationAverage
        );
        unavailabilities.push(...out);
      });

    // TODO: Rendez-vous

    const filteredAvailabilities = settings.filter(
      (setting) =>
        !unavailabilities.some((unavailability) =>
          this.isSameAvailability(setting, unavailability)
        )
    );

    return filteredAvailabilities;
  }
}

export default ProfessionalAvailabilitiesFilter;
