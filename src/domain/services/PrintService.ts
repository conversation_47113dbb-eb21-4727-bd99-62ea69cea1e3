import { CarnetSanteDTO, ProfessionalCardDTO } from "../DTOS";
import { employer } from "../interfaces/repositories";
import {
  consultation_medical,
  Dash,
  Employer,
  Patient,
  Proche,
  signe_vitaux,
} from "../models";
import { printCarnetSanteContent } from "./printCarnetSanteContent";
import { printCarnetSanteContentEmployer } from "./printCarnetSanteContentEmployer";
import { printCarnetSanteContentProche } from "./printCarnetSanteContentProche";
import { printConsultationContent } from "./printConsultationContent";
import { printConsultationContentEmployer } from "./printConsultationContentEmployer";
import { printConsultationContentProche } from "./printConsultationContentProche";
import { printSignesVitauxContent } from "./printSignesVitauxContent";
import { printSignesVitauxContentEmployer } from "./printSignesVitauxContentEmployer";
import { printSignesVitauxContentProche } from "./printSignesVitauxContentProche";

export class PrintService {
  /**
   * Helper method to handle the printing process
   * @param contentGenerator Function that generates the HTML content to print
   * @returns Promise that resolves when printing is complete
   */
  private static printContent(contentGenerator: () => string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      try {
        // Create the HTML content first
        const content = contentGenerator();

        // Create a hidden iframe for printing
        const iframe = document.createElement("iframe");
        iframe.style.display = "none";
        document.body.appendChild(iframe);

        if (!iframe.contentWindow) {
          document.body.removeChild(iframe);
          reject(new Error("Failed to create iframe"));
          return;
        }

        // Set up the load handler before writing content
        iframe.onload = () => {
          try {
            // Small delay to ensure content is fully rendered
            setTimeout(() => {
              try {
                iframe.contentWindow?.print();
                // Remove the iframe after a delay to ensure print dialog is shown
                setTimeout(() => {
                  document.body.removeChild(iframe);
                  resolve();
                }, 1000);
              } catch (printError) {
                console.error("Print error:", printError);
                document.body.removeChild(iframe);
                reject(printError);
              }
            }, 100);
          } catch (loadError) {
            console.error("iframe load error:", loadError);
            document.body.removeChild(iframe);
            reject(loadError);
          }
        };

        // Write the content to the iframe
        try {
          iframe.contentWindow.document.open();
          iframe.contentWindow.document.write(content);
          iframe.contentWindow.document.close();
        } catch (writeError) {
          console.error("Content write error:", writeError);
          document.body.removeChild(iframe);
          reject(writeError);
        }
      } catch (error) {
        console.error("General print error:", error);
        reject(error);
      }
    });
  }
  static printCarnetSante(
    carnet: {
      title: string;
      content: {
        id: number;
        nom: string;
      }[];
    }[],
    patient: Patient,
    employer: Employer,
    proche: Proche,
    professionnel: ProfessionalCardDTO,
    dash: Dash
  ) {
    if (professionnel) {
      if (patient) {
        return this.printContent(() =>
          printCarnetSanteContent(carnet, patient, professionnel)
        );
      } else if (proche) {
        return this.printContent(() =>
          printCarnetSanteContentProche(carnet, proche, professionnel)
        );
      }
    } else if (dash) {
      if (employer) {
        return this.printContent(() =>
          printCarnetSanteContentEmployer(carnet, employer, dash)
        );
      } else if (proche) {
        return this.printContent(() =>
          printCarnetSanteContentProche(carnet, proche, dash)
        );
      }
    }
  }

  static printConsultation({
    carnet,
    signeVitaux,
    consultations,
    patient,
    employer,
    proche,
    professionnel,
    dash,
  }: {
    carnet: Partial<CarnetSanteDTO>;
    signeVitaux: signe_vitaux;
    consultations: consultation_medical[];
    patient: Patient;
    employer: Employer;
    proche: Proche;
    professionnel: ProfessionalCardDTO;
    dash: Dash;
  }) {
    if (professionnel) {
      if (patient) {
        return this.printContent(() =>
          printConsultationContent({
            carnet,
            signeVitaux,
            consultations,
            patient,
            professionnel,
          })
        );
      } else if (proche) {
        return this.printContent(() =>
          printConsultationContentProche({
            carnet,
            signeVitaux,
            consultations,
            proche,
            professionnel,
          })
        );
      }
    } else if (dash) {
      if (employer) {
        return this.printContent(() =>
          printConsultationContentEmployer({
            carnet,
            signeVitaux,
            consultations,
            employer,
            dash,
          })
        );
      } else if (proche) {
        return this.printContent(() =>
          printConsultationContentProche({
            carnet,
            signeVitaux,
            consultations,
            proche,
            dash,
          })
        );
      }
    }
  }

  static printSignesVitaux(
    signeVitaux: Omit<signe_vitaux, "id">,
    patient: Patient,
    professionnel: ProfessionalCardDTO,
    employer: Employer,
    proche: Proche,
    dash: Dash
  ) {
    if (professionnel) {
      if (patient) {
        return this.printContent(() =>
          printSignesVitauxContent(signeVitaux, patient, professionnel)
        );
      } else if (proche) {
        return this.printContent(() =>
          printSignesVitauxContentProche(signeVitaux, proche, professionnel)
        );
      }
    } else if (dash) {
      if (employer) {
        return this.printContent(() =>
          printSignesVitauxContentEmployer(signeVitaux, employer, dash)
        );
      } else if (proche) {
        return this.printContent(() =>
          printSignesVitauxContentProche(signeVitaux, proche, dash)
        );
      }
    }
  }

  static printMedicament(
    data: {
      title: string;
      content: {
        id: number;
        nom: string;
      }[];
    }[],
    patient: Patient,
    proche: Proche,
    employer: Employer,
    professionnel: ProfessionalCardDTO,
    dash: Dash
  ) {
    if (professionnel) {
      if (patient) {
        return this.printContent(() =>
          printCarnetSanteContent(data, patient, professionnel)
        );
      } else if (proche) {
        return this.printContent(() =>
          printCarnetSanteContentProche(data, proche, professionnel)
        );
      }
    } else if (dash) {
      if (employer) {
        return this.printContent(() =>
          printCarnetSanteContentEmployer(data, employer, dash)
        );
      } else if (proche) {
        return this.printContent(() =>
          printCarnetSanteContentProche(data, proche, dash)
        );
      }
    }
  }
}
