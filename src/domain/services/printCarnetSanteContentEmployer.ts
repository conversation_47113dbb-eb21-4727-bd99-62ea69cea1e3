import { PRIMARY, SECONDARY, BLACK } from "@/shared/constants/Color";
import { Dash, Employer } from "../models";

export const printCarnetSanteContentEmployer = (
  carnet: {
    title: string;
    content: {
      id: number;
      nom: string;
    }[];
  }[],
  employer: Employer,
  dash: Dash
) => {
  // Convert hex colors to RGB for CSS variables
  const primaryColor = PRIMARY || "#5dd3fb";
  const secondaryColor = SECONDARY || "#9CE6FE";
  const blackColor = BLACK || "#082a4d";

  const currentDate = new Date().toLocaleDateString("fr-FR");
  const birthDate = new Date(employer.date_de_naissance).toLocaleDateString(
    "fr-FR"
  );
  const age = Math.floor(
    (new Date().getTime() - new Date(employer.date_de_naissance).getTime()) /
      (365.25 * 24 * 60 * 60 * 1000)
  );

  return `
  <!DOCTYPE html>
  <html>
    <head>
      <title>Carnet de Santé</title>
      <style>
        :root {
          --primary-color: ${primaryColor};
          --secondary-color: ${secondaryColor};
          --black-color: ${blackColor};
          --background-color: #ffffff;
          --text-color: #333333;
          --border-color: #e2e8f0;
          --item-bg-color: #f8f9fa;
          --shadow-color: rgba(0, 0, 0, 0.1);
        }

        @media (prefers-color-scheme: dark) {
          :root {
            --background-color: #1a202c;
            --text-color: #f7fafc;
            --border-color: #2d3748;
            --item-bg-color: #2d3748;
            --shadow-color: rgba(0, 0, 0, 0.3);
          }
        }

        * {
          box-sizing: border-box;
          margin: 0;
          padding: 0;
        }

        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          margin: 0;
          padding: 20px;
          background-color: var(--background-color);
          color: var(--text-color);
        }

        header {
          text-align: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
        }

        .container {
          max-width: 800px;
          margin: 0 auto;
          background-color: var(--background-color);
          border-radius: 8px;
          box-shadow: 0 4px 6px var(--shadow-color);
          overflow: hidden;
        }

        .speciality {
          font-size: 18px;
          color: #34495e;
        }

        .subspeciality, .exam-type {
          font-size: 14px;
          color: #7f8c8d;
        }

        .contact, .order {
          font-size: 13px;
          color: #95a5a6;
        }

        .grid-cols-2 {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 2rem;
        }

        h1 {
          color: #2c3e50;
          font-size: 24px;
          margin-bottom: 8px;
        }

        .header p {
          font-size: 14px;
          opacity: 0.9;
        }

        .section {
          margin-bottom: 12px;
          page-break-inside: avoid;
        }

        .section:last-child {
          border-bottom: none;
        }

        .section-title {
          font-weight: 600;
          font-size: 18px;
          margin-bottom: 12px;
          color: #2c3e50;
          display: inline-block;
        }

        .list-items {
          display: flex;
          flex-direction: wrap;
          gap: 12px;
        }

        .content-item {
          padding: 6px 8px;
          background-color: var(--item-bg-color);
          border-radius: 8px;
          box-shadow: 0 2px 4px var(--shadow-color);
          border-left: 4px solid var(--primary-color);
          transition: transform 0.2s ease;
        }

        .content-item:hover {
          transform: translateY(-2px);
        }

        @media print {
          body {
            margin: 0;
            padding: 0;
            background-color: white;
            color: black;
          }

          .container {
            box-shadow: none;
            max-width: 100%;
          }

          .header {
            background-color: white;
            color: var(--black-color);
            border-bottom: 2px solid var(--primary-color);
            padding: 15px;
          }

          .content-item {
            border: 1px solid var(--border-color);
            box-shadow: none;
            page-break-inside: avoid;
          }

          .patient-info {
            background-color: #f8f9fa;
            border-radius: 5px;
          }
  
          .patient-info p {
            margin-bottom: 5px;
          }

          .section {
            page-break-inside: avoid;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <header>
          <h1>${dash?.nom}</h1>
        </header>

        <div class="grid-cols-2 gap-6">
          <div>
            <div class="patient-info">
            <p><strong>Date :</strong> ${currentDate}</p>
            <p><strong>NOM et Prénoms :</strong> ${employer.nom} ${employer.prenom}</p>
            <p><strong>Date de Naissance :</strong> ${birthDate} (Age: ${age} ans)</p>
            <p><strong>Matricule :</strong> ${employer.matricule || "Non renseignée"}</p>
          </div>
          </div>
          ${carnet
            .map((section) =>
              section.content.length > 0
                ? `
            <div class="section">
              <div class="section-title">${section.title}</div>
              <div class="list-items">
                ${section.content
                  .map(
                    (item) => `
                  <div class="content-item">
                    ${item.nom}
                  </div>
                `
                  )
                  .join("")}
              </div>
            </div>
          `
                : ""
            )
            .join("")}
        </div>
      </div>
    </body>
  </html>
`;
};
