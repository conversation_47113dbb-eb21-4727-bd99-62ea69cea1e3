import { ProfessionalProfileDTO } from "@/domain/DTOS/CabinetMedicalFormDTO";

/**
 * Interface pour le use case de récupération complète du profil professionnel
 * 
 * @description Cette interface définit le contrat pour orchestrer la récupération
 * de toutes les données d'un profil professionnel en une seule opération optimisée.
 * Le use case gère la logique métier, le traitement des données et la construction
 * du DTO final.
 * 
 * @architecture
 * Cette interface respecte les principes de Clean Architecture :
 * - Définit un contrat clair pour la couche domaine
 * - Permet l'inversion de dépendance
 * - Facilite les tests unitaires avec des mocks
 * - Sépare la logique métier de l'infrastructure
 * - Orchestre les opérations complexes
 */
export interface IGetProfessionalProfileCompleteUsecase {
  /**
   * Récupère et traite toutes les données du profil professionnel
   * 
   * @param professionalId - L'identifiant unique du professionnel
   * @returns Promise<ProfessionalProfileDTO | null> - Les données complètes du profil traitées ou null si non trouvé
   * 
   * @throws {Error} En cas d'erreur lors de la récupération ou du traitement des données
   * 
   * @description Cette méthode :
   * - Récupère toutes les données via le repository optimisé
   * - Traite les méthodes de paiement depuis le champ modes_paiement_acceptes
   * - Organise les photos par type (profil vs cabinet)
   * - Valide et structure les données selon le DTO
   * - Gère les erreurs de manière appropriée
   * 
   * @example
   * ```typescript
   * const usecase = new GetProfessionalProfileCompleteUsecase(repository);
   * const profileData = await usecase.execute(123);
   * 
   * if (profileData) {
   *   console.log(`Profil de ${profileData.baseInfo.nom} ${profileData.baseInfo.prenom}`);
   *   console.log(`Spécialités: ${profileData.specialities.length}`);
   *   console.log(`Méthodes de paiement: ${profileData.paymentMethods.length}`);
   * }
   * ```
   */
  execute(professionalId: number): Promise<ProfessionalProfileDTO | null>;
}
