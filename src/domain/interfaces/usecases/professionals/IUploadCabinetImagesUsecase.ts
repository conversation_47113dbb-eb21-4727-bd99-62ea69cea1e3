import { PhotoTypeEnum } from "@/domain/models/enums/photo_type_enum.ts";
import { Photo } from "@/domain/models/Photo.ts";

/**
 * Interface pour le usecase d'upload d'images du cabinet
 */
export interface IUploadCabinetImagesUsecase {
  execute(
    userId: number,
    files: File[],
    path: string,
    type: PhotoTypeEnum
  ): Promise<{
    success: boolean;
    photos?: Photo[];
    errors?: string[];
    partialSuccess?: boolean;
  }>;
}
