import { RegisterUserDTO } from "@/domain/DTOS/RegisterUserDTO";
import { Patient, Contact } from "@/domain/models";

export interface registerProps {
  email: string;
  password: string;
  additionnalInfo: Omit<Patient, "id" | "utilisateur_id" | "unique_id">;
  isLoginUser?: boolean;
  contact?: Omit<Contact, "id" | "utilisateur_id">[];
}

export interface IRegisterUserUsecase {
  execute(data: registerProps, redirectToURL: string): Promise<RegisterUserDTO>;
}
