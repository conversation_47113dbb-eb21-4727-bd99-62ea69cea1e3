import { Employer } from "@/domain/models/Employer";

/**
 * Interface pour le usecase de mise à jour d'un employé
 */
export interface IUpdateEmployerUsecase {
  /**
   * Exécute la mise à jour d'un employé
   * @param id - L'ID de l'employé à mettre à jour
   * @param employerData - Les données partielles de l'employé à mettre à jour
   * @param profilePhoto - La photo de profil de l'employé (optionnelle)
   * @param lastPath - Le chemin de la photo de profil de l'employé (optionnel)
   * @returns Promise<Employer> - L'employé mis à jour
   */
  execute(
    id: number,
    employerData: Partial<Employer>,
    profilePhoto: File | null,
    lastPath: string
  ): Promise<Employer>;
}
