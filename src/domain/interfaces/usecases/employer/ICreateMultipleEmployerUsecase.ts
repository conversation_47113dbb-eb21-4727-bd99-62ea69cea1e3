import { Employer } from "@/domain/models/Employer";

/**
 * Interface pour le usecase de création d'un employé
 */
export interface ICreateMultipleEmployerUsecase {
    /**
     * Exécute la création d'un employé
     * @param employers - Données de l'employé à créer (sans l'ID)
     * @returns Promise<Employer> - L'employé créé avec son ID
     */
    execute(employers: Omit<Employer[], "id">): Promise<Employer[]>;
}
