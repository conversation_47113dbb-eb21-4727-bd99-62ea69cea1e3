import { SearchProfessionalDTO } from "@/domain/DTOS/ProfessionalDTO";

/**
 * Paramètres pour la recherche de professionnels de santé
 */
export interface SearchProfessionalsRepositoryParams {
  /** Nom, prénom, raison sociale ou numéro d'ordre du professionnel */
  name?: string | null;
  /** Adresse, région, district, commune ou fokontany */
  localization?: string | null;
  /** Numéro de page (0-indexé) */
  page?: number;
  /** Nombre d'éléments par page */
  limit?: number;
}

/**
 * Interface du repository pour la recherche de professionnels de santé
 * Contient uniquement les requêtes SQL sans logique métier
 */
export interface ISearchProfessionalsRepository {
  /**
   * Exécute la recherche de professionnels selon les critères fournis
   * @param params Paramètres de recherche et pagination
   * @returns Liste des professionnels trouvés avec leurs relations
   */
  execute(
    params: SearchProfessionalsRepositoryParams
  ): Promise<SearchProfessionalDTO[]>;

  /**
   * Recherche les professionnels par proximité géographique
   * @param latitude Latitude du point de référence
   * @param longitude Longitude du point de référence
   * @param radiusKm Rayon de recherche en kilomètres
   * @param limit Nombre maximum de résultats
   * @returns Liste des professionnels dans le rayon spécifié
   */
  searchByProximity(
    latitude: number,
    longitude: number,
    radiusKm?: number,
    limit?: number
  ): Promise<SearchProfessionalDTO[]>;

  /**
   * Recherche intelligente combinant texte et géolocalisation
   * @param params Paramètres de recherche incluant la géolocalisation
   * @returns Liste des professionnels triés par pertinence et proximité
   */
  searchWithGeolocation(
    params: SearchProfessionalsRepositoryParams & {
      latitude?: number;
      longitude?: number;
      radiusKm?: number;
    }
  ): Promise<SearchProfessionalDTO[]>;
}

// Alias pour compatibilité avec l'implémentation existante
export interface IGetProfessionalInformationRepository
  extends ISearchProfessionalsRepository {}
