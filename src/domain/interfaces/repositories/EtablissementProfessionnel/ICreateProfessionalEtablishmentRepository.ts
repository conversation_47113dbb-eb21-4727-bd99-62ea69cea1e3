import { EtablissementProfessionnel } from "@/domain/models";

/**
 * Interface pour le repository de création d'établissement professionnel
 *
 * @description Définit le contrat pour la persistance d'un établissement professionnel
 */
export interface ICreateProfessionalEtablishmentRepository {
  /**
   * Crée un nouvel établissement professionnel dans la base de données
   *
   * @param etablissement - Les données de l'établissement à créer (sans l'ID)
   * @returns Promise<EtablissementProfessionnel> - L'établissement créé avec son ID
   * @throws Error si la création échoue
   */
  execute(
    etablissement: Omit<EtablissementProfessionnel, "id">,
  ): Promise<EtablissementProfessionnel>;
}
