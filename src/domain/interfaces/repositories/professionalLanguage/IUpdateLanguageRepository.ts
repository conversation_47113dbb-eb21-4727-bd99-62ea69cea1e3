import { LangueParleeProfessionnel } from "@/domain/models";

/**
 * Interface pour le repository de mise à jour des langues du professionnel
 */
export interface IUpdateLanguageRepository {
  /**
   * Met à jour une langue parlée par le professionnel
   * @param languageId - ID de la langue à mettre à jour
   * @param languageData - Nouvelles données de la langue
   * @returns Langue mise à jour ou null en cas d'erreur
   */
  execute(
    languageId: number,
    languageData: Partial<Omit<LangueParleeProfessionnel, "id" | "id_professionnel">>
  ): Promise<LangueParleeProfessionnel | null>;
}
