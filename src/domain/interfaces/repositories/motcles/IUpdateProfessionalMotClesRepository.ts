import { MotClesProfessionnel } from "@/domain/models";

/**
 * Interface pour le repository de mise à jour des mots-clés du professionnel
 */
export interface IUpdateProfessionalMotClesRepository {
  /**
   * Met à jour un mot-clé du professionnel
   * @param motCleId - ID du mot-clé à mettre à jour
   * @param motCleData - Nouvelles données du mot-clé
   * @returns Mot-clé mis à jour ou null en cas d'erreur
   */
  execute(
    motCleId: number,
    motCleData: Partial<Omit<MotClesProfessionnel, "id" | "id_professionnel">>
  ): Promise<MotClesProfessionnel | null>;
}
