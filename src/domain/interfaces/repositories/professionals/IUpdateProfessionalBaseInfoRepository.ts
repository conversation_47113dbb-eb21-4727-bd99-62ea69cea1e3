import { Professionnel } from "@/domain/models";

/**
 * Interface pour le repository de mise à jour des informations de base du professionnel
 * 
 * @description Cette interface définit le contrat pour mettre à jour les informations
 * personnelles de base d'un professionnel (titre, nom, prénom) dans la table professionnels.
 * 
 * @architecture
 * Cette interface respecte les principes de Clean Architecture :
 * - Définit un contrat clair pour la couche infrastructure
 * - Permet l'inversion de dépendance
 * - Facilite les tests unitaires avec des mocks
 * - Sépare les préoccupations entre domaine et infrastructure
 */
export interface IUpdateProfessionalBaseInfoRepository {
  /**
   * Met à jour les informations de base du professionnel
   * 
   * @param professionalId - L'identifiant unique du professionnel
   * @param baseInfo - Les informations de base à mettre à jour (titre, nom, prénom)
   * @returns Promise<Professionnel> - Les données mises à jour du professionnel
   * 
   * @throws {Error} En cas d'erreur lors de la mise à jour
   * 
   * @example
   * ```typescript
   * const repository = new UpdateProfessionalBaseInfoRepository();
   * const updatedProfessional = await repository.execute(123, {
   *   titre: professionnels_titre_enum.DR,
   *   nom: "Dupont",
   *   prenom: "Marie"
   * });
   * ```
   */
  execute(
    professionalId: number, 
    baseInfo: Pick<Professionnel, 'titre' | 'nom' | 'prenom'>
  ): Promise<Professionnel>;
}
