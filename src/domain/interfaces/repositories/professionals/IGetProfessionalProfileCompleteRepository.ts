/**
 * Interface pour le repository de récupération complète du profil professionnel
 *
 * @description Cette interface définit le contrat pour récupérer toutes les données
 * d'un profil professionnel en une seule requête optimisée avec toutes les jointures
 * nécessaires (spécialités, diplômes, expériences, publications, langues, mots-clés,
 * assurances, contacts, photos, établissements).
 *
 * @architecture
 * Cette interface respecte les principes de Clean Architecture :
 * - Définit un contrat clair pour la couche infrastructure
 * - Permet l'inversion de dépendance
 * - Facilite les tests unitaires avec des mocks
 * - Sépare les préoccupations entre domaine et infrastructure
 */
export interface IGetProfessionalProfileCompleteRepository {
  /**
   * Récupère toutes les données du profil professionnel en une seule requête optimisée
   *
   * @param professionalId - L'identifiant unique du professionnel
   * @returns Promise<ProfessionalProfileDTO | null> - Les données complètes du profil ou null si non trouvé
   *
   * @throws {Error} En cas d'erreur lors de la récupération des données
   *
   * @example
   * ```typescript
   * const repository = new GetProfessionalProfileCompleteRepository();
   * const profileData = await repository.execute(123);
   *
   * if (profileData) {
   *   console.log(`Profil de ${profileData.baseInfo.nom} ${profileData.baseInfo.prenom}`);
   *   console.log(`Spécialités: ${profileData.specialities.length}`);
   *   console.log(`Diplômes: ${profileData.diplomas.length}`);
   * }
   * ```
   */
  execute(professionalId: number): Promise<any | null>;
}
