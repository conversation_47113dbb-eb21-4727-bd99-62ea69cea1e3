import { Professionnel, Contact } from "@/domain/models";

/**
 * Interface pour le repository de mise à jour des informations de contact du professionnel
 */
export interface IUpdateProfessionalContactRepository {
  /**
   * Met à jour les informations de contact et d'adresse du professionnel
   * @param professionalId - ID du professionnel
   * @param contactData - Données de contact à mettre à jour
   * @returns Professionnel mis à jour ou null en cas d'erreur
   */
  execute(
    professionalId: number,
    contactData: {
      adresse?: string;
      fokontany?: string;
      informations_acces?: string;
    }
  ): Promise<Professionnel | null>;
}

/**
 * Interface pour le repository de mise à jour du numéro de téléphone
 */
export interface IUpdateProfessionalPhoneRepository {
  /**
   * Met à jour le numéro de téléphone du professionnel
   * @param userId - ID de l'utilisateur
   * @param phoneNumber - Nouveau numéro de téléphone
   * @returns Contact mis à jour ou null en cas d'erreur
   */
  execute(
    userId: number,
    phoneNumber: string
  ): Promise<Contact | null>;
}
