import { Professionnel } from "@/domain/models";

/**
 * Interface pour le repository de mise à jour des informations professionnelles
 */
export interface IUpdateProfessionalProfessionalInfoRepository {
  /**
   * Met à jour les informations professionnelles du professionnel
   * @param professionalId - ID du professionnel
   * @param professionalData - Données professionnelles à mettre à jour
   * @returns Professionnel mis à jour ou null en cas d'erreur
   */
  execute(
    professionalId: number,
    professionalData: {
      numero_ordre?: string;
      raison_sociale?: string;
      nif?: string;
      stat?: string;
    }
  ): Promise<Professionnel | null>;
}
