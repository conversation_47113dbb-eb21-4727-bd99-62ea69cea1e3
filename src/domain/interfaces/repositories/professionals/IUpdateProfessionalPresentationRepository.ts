import { Professionnel } from "@/domain/models";

/**
 * Interface pour le repository de mise à jour de la présentation du professionnel
 */
export interface IUpdateProfessionalPresentationRepository {
  /**
   * Met à jour la présentation générale du professionnel
   * @param professionalId - ID du professionnel
   * @param presentation - Nouvelle présentation générale
   * @returns Professionnel mis à jour ou null en cas d'erreur
   */
  execute(
    professionalId: number,
    presentation: string
  ): Promise<Professionnel | null>;
}
