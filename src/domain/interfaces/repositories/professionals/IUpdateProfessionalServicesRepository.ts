import { Professionnel } from "@/domain/models";
import { professionnels_types_consultation_enum } from "@/domain/models/enums";

/**
 * Interface pour le repository de mise à jour des services du professionnel
 */
export interface IUpdateProfessionalServicesRepository {
  /**
   * Met à jour les informations de services du professionnel
   * @param professionalId - ID du professionnel
   * @param servicesData - Données des services à mettre à jour
   * @returns Professionnel mis à jour ou null en cas d'erreur
   */
  execute(
    professionalId: number,
    servicesData: {
      types_consultation?: professionnels_types_consultation_enum;
      nouveau_patient_acceptes?: boolean;
    }
  ): Promise<Professionnel | null>;
}
