import { AvailabilitySettingsDTO } from "@/domain/DTOS";
import { Evenement, RendezVous } from "@/domain/models";
import { Event } from "@/shared/types/SettingsType";

export interface ILoadEventService {
  execute(
    data: {
      disponibilites: AvailabilitySettingsDTO;
      evenement: Evenement[];
      appointments: RendezVous[];
    },
    filter?: {
      isDisponibilites: boolean;
      isEvenement: boolean;
      isAppointments: boolean;
    }
  ): Event[];
}
