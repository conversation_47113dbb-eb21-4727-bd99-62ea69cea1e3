// Interfaces principales
export * from "./ParametreDisponibilite";
export * from "./Patient";
export * from "./Proches";
export * from "./Professionnel";
export * from "./RendezVous";
export * from "./Utilisateurs";
export * from "./Dash";
export * from "./InvitationDash";

// Interfaces liées aux professionnels
export * from "./ControleParametre";
export * from "./CreneauHoraire";
export * from "./DiplomeProfessionnel";
export * from "./EtablissementProfessionnel";
export * from "./Evenement";
export * from "./ExperienceProfessionnel";
export * from "./HoraireDateSpecifique";
export * from "./HoraireHebdomadaire";
export * from "./LangueParleeProfessionnel";
export * from "./ListeMotCle";
export * from "./ListeSpecialites";
export * from "./ListeTypeEtablissement";
export * from "./MotClesProfessionnel";
export * from "./OrdreAppartenance";
export * from "./Pause";
export * from "./PhotoProfessionnel";
export * from "./PublicationProfessionnel";
export * from "./ReseauxSociauxProfessionnel";
export * from "./SpecialiteProfessionnel";
export * from "./ProfessionnelPatient";
export * from "./Antecedant_sociaux";
export * from "./TestsMedicauxDiagnosticsEtDepistage";
export * from "./AssuranceProfessionnel";

// Interfaces administratives
export * from "./ActionHistorique";
export * from "./Administrateurs";
export * from "./DemandeAdhesion";
export * from "./JournauxActivites";
export * from "./Messages";
export * from "./Invitation";

// Interfaces médicales
export * from "./CarnetDeSanteNumerique";
export * from "./ConsultationMedical";
export * from "./HistoriqueMedicaux";
export * from "./LaboratoireDiagnostics";
export * from "./SigneVitaux";
export * from "./Urgence";
export * from "./CarnetDeSanteNumerique";
export * from "./ConsultationMedical";
export * from "./HistoriqueMedicaux";
export * from "./LaboratoireDiagnostics";
export * from "./SigneVitaux";
export * from "./Urgence";
export * from "./Facturation";

// Interfaces antécédents et conditions médicales
export * from "./Allergie";
export * from "./AntecedantFamilliaux";
export * from "./Vaccination";
export * from "./AffectationMedical";
export * from "./AntecedantChirurgicaux";
export * from "./AntecedantSociauxAlcolique";
export * from "./AntecedantSociauxFumeur";
export * from "./DispositifMedicaux";
export * from "./HistoriqueMentalEtPsyciatre";
export * from "./Medicament";
export * from "./HistoriqueCarnetSante";
export * from "./CarnetSante";
export * from "./ConditionGynecologique";
export * from "./AntecedentGrossesse";

// Types localisations
export * from "./Province";
export * from "./Region";
export * from "./District";
export * from "./Commune";

// Types utilitaires
export * from "./Point";
export * from "./Contact";
export * from "./Messages";
export * from "./Conversation";

// Gestion de stock
export * from "./Categories";
export * from "./SousCategories";
export * from "./Stocks";
export * from "./Employer";
export * from "./Fournisseurs";
export * from "./EntreesStocks";
export * from "./SortiesStocks";
export * from "./ListeMedicaments";
export * from "./Lots";
