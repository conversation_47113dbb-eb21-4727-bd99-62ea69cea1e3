import { patients_groupe_sanguin_enum, sexe_enum } from "./enums";

export interface Patient {
  id: number;
  utilisateur_id?: number;
  unique_id: string;
  nom: string;
  prenom?: string;
  sexe: sexe_enum;
  decede?: boolean;
  date_naissance: Date;
  adresse?: string;
  province: string;
  district: string;
  commune: string;
  region: string;
  donneur_sang?: boolean;
  email?: string;
  groupe_sanguin?: patients_groupe_sanguin_enum;
  nationalite?: string;
  pays?: string;
  situation_matrimonial: string;
  telephone: string;
  nb_enfant?: number;
  profession?: string;
}
