import { sexe_enum, status_administratif_enum } from "@/domain/models/enums";

export interface Employer {
  id: number;
  id_utilisateur: number;
  id_dash: number;
  nom: string;
  prenom: string;
  date_de_naissance: Date;
  matricule: string;
  fonction: string;
  date_entree_en_fonction: Date;
  status_administratif: status_administratif_enum;
  direction: string;
  sexe: sexe_enum;
  photo?: string;
  cree_a: string;
  mis_a_jour_a: string;
  est_supprimee?: boolean;
  decede?: boolean;
}
