import { Patient, Urgence, signe_vitaux } from "@/domain/models";
import { CarnetSanteDTO } from "./CarnetSanteDTO";

/**
 * DTO pour les données complètes d'un patient
 *
 * @interface PatientDTO
 * @extends Patient
 *
 * @property {Urgence[]} urgence - Liste des contacts d'urgence du patient
 * @property {signe_vitaux} signe_vitaux - Derniers signes vitaux enregistrés
 * @property {string} [avatar] - URL de l'avatar du patient (optionnel)
 * @property {CarnetSanteDTO | null} [carnetSante] - Carnet de santé du patient (null pour les nouveaux patients)
 */
export type PatientDTO = Patient & {
  urgence: Urgence[];
  avatar?: string;
  carnet_sante?: CarnetSanteDTO | null;
};
