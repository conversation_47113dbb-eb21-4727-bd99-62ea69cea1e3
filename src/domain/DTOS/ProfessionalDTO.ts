import {
  Contact,
  DiplomeProfessionnel,
  EtablissementProfessionnel,
  Evenement,
  ExperienceProfessionnel,
  horaire_hebdomadaire,
  LangueParleeProfessionnel,
  MotClesProfessionnel,
  Professionnel,
  PublicationProfessionnel,
  RendezVous,
  SpecialiteProfessionnel,
} from "../models";
import {
  professionnels_types_consultation_enum,
  sexe_enum,
} from "../models/enums";
import { Photo } from "../models/Photo";
import { AvailabilitySettingsDTO } from "./AvailabililtySettingsDTO";

export type SearchProfessionalDTO = Professionnel & {
  rendez_vous: RendezVous[];
  specialites_professionnel: SpecialiteProfessionnel[];
  etablissements_professionnel: EtablissementProfessionnel[];
  parametre_disponibilite: AvailabilitySettingsDTO[];
  mot_cles_professionnel: MotClesProfessionnel[];
  publication_professionnel: PublicationProfessionnel[];
  experience_professionnel: ExperienceProfessionnel[];
  diplome_professionnel: DiplomeProfessionnel[];
  langues_parlees_professionnel: LangueParleeProfessionnel[];
  utilisateurs: {
    id: number;
    email: string | null;
    evenement: Evenement[];
    contact: Contact[];
    photos: {
      id: number;
      path: string;
      type: string;
    }[];
  };
};

export type TimeSlotProffessionalCard = {
  id_professionnel: number;

  date: string;
  start: string;
  end: string;
};

export type ProfessionalCardDTO = Professionnel & {
  specialite: SpecialiteProfessionnel[];
  disponibilite: TimeSlotProffessionalCard[];
  etablissements_professionnel: EtablissementProfessionnel[];
  horaire_hebdomadaire?: horaire_hebdomadaire[];
  contacts: Contact[];
  evenements?: Evenement[];
  publications?: PublicationProfessionnel[];
  experiences?: ExperienceProfessionnel[];
  diplomes?: DiplomeProfessionnel[];
  langues?: LangueParleeProfessionnel[];
  types_consultation?: professionnels_types_consultation_enum;
  motCles?: MotClesProfessionnel[];
  email?: string;
  photos?: Photo[];
};

export type ProfessionalCompleteDTO = Professionnel & {
  specialites_professionnel: SpecialiteProfessionnel[];
};

export type ProfessionalProfileData = Professionnel & {
  specialites_professionnel: SpecialiteProfessionnel[];
  parametre_disponibilite: AvailabilitySettingsDTO;
  etablissements_professionnel: EtablissementProfessionnel[];
};
