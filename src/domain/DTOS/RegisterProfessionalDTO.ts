import {
  Contact,
  DiplomeProfessionnel,
  EtablissementProfessionnel,
  ExperienceProfessionnel,
  LangueParleeProfessionnel,
  ListeAssurances,
  ListeSpecialites,
  ordre_appartenance,
  Professionnel,
  PublicationProfessionnel,
  Utilisateur,
} from "@/domain/models";
import { mot_cles } from "../models/MotCles";

/**
 * DTO pour l'enregistrement d'un professionnel
 */
export interface RegisterProfessionalDTO {
  user: Omit<Utilisateur, "id">;
  userData: Omit<Professionnel, "id" | "utilisateur_id">;

  // Table a clee etrangers
  profile_image?: Blob;
  cabinetImages: Blob[];

  specialities: ListeSpecialites[];
  ordreAppartenances: ordre_appartenance[];
  insurances: ListeAssurances[];
  diplomas: Omit<DiplomeProfessionnel, "id" | "id_professionnel">[];
  experiencies: Omit<ExperienceProfessionnel, "id" | "id_professionnel">[];
  publications: Omit<PublicationProfessionnel, "id" | "id_professionnel">[];
  spokenLanguages: Omit<LangueParleeProfessionnel, "id" | "id_professionnel">[];
  searchKeywords: mot_cles[];
  etablissement?: Omit<EtablissementProfessionnel, "id" | "id_professionnel">;
  telephone?: Omit<Contact, "id" | "utilisateur_id">;
}
