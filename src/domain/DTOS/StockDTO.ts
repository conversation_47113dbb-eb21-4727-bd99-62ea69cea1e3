import { Categories } from "../models/Categories.ts";
import { EntreesStocks } from "../models/EntreesStocks.ts";
import { Fournisseurs } from "../models/Fournisseurs.ts";
import { Lots } from "../models/Lots.ts";
import { SortiesStocks } from "../models/SortiesStocks.ts";
import { Stocks } from "../models/Stocks.ts";

export interface CreateStockDTO {
  stock_id: number;
  fournisseur_id: number;
  quantite: number;
  prix_unitaire: number;
  date_entree: string;
  lot: Omit<Lots, "id" | "entree_id">;
}

/**
 * Interface de données pour les statistiques
 */
export interface StockStatistics {
  totalProduits: number;
  totalFournisseurs: number;
  alertesPeremption: number;
  alertesStock: LowStockCardDTO[];
  valeurTotaleStock: number;
  mouvementsRecents: {
    id: number;
    name: string;
    quantite: number;
    date: string;
    type: string;
  }[];
}

// DTO de l'ensemble de donnee a la recuperation global
export interface StockDTO extends Stocks {
  fournisseurs: Fournisseurs[];
  categories: Categories[];
  lots: Lots[];
  entrees: EntreesStocks[];
  sorties: SortiesStocks[];
}

export interface StockCardDTO {
  id: number;
  nom: string;
  unite: string;
  seuil_alerte: number;
  quantite_totale: number;
}

/* Ligne de donnee entree stock affichee cotee frontend */
export interface EntreeStockRowDTO extends EntreesStocks {
  produit: string;
  fournisseur: string;
  lot: string;
}

export interface SortieStockRowDTO extends SortiesStocks {
  produit: string;
  lot: string;
}

export interface SortieStockDTO {
  stock_id: number;
  quantite: number;
  type_sortie: string;
  destinataire?: string;
  date_sortie: string;
}

export interface LotWithStockData extends Lots {
  stocks: Stocks;
}

export interface StockNearPeremptionDateCardDTO {
  id: number;
  produit: string;
  quantite: number;
  date_expiration: string;
  numero_lot: string;
  jours_restants: number;
  priorite: string;
}

export interface LowStockCardDTO {
  id: number;
  produit: string;
  stock_actuel: number;
  seuil_alerte: number;
  unite: string;
  priorite: string;
}
