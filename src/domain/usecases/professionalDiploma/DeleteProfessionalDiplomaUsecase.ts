import { IDeleteProfessionalDiplomaRepository } from "@/domain/interfaces/repositories/professionalDiploma";
import { IDeleteProfessionalDiplomaUsecase } from "@/domain/interfaces/usecases/professionalDiploma";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel";

class DeleteProfessionalDiplomaUsecase
  implements IDeleteProfessionalDiplomaUsecase
{
  constructor(
    private readonly deleteProfessionalDiplomaRepository: IDeleteProfessionalDiplomaRepository
  ) {}

  async execute(id: number): Promise<DiplomeProfessionnel | null> {
    try {
      return await this.deleteProfessionalDiplomaRepository.execute(id);
    } catch (error) {
      console.error("Error deleting professional diploma:", error);
      return null;
    }
  }
}

export default DeleteProfessionalDiplomaUsecase;
