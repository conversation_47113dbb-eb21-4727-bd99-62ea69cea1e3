import { ICreateFamilleRepository } from "@/domain/interfaces/repositories/famille/ICreateFamilleRepository";
import { Familles } from "@/domain/models/Famille";

export class CreateFamilleUsecase {
  constructor(
    private readonly createFamilleRepository: ICreateFamilleRepository
  ) {}

  async execute(familles: Omit<Familles, "id">[]): Promise<Familles[]> {
    return this.createFamilleRepository.execute(familles);
  }
}
