import { IGetFamillesByEmployeeIdRepository } from "@/domain/interfaces/repositories/famille/IGetFamillesByEmployeeIdRepository";
import { Familles } from "@/domain/models/Famille";

export class GetFamillesByEmployeeIdUsecase {
  constructor(
    private readonly getFamillesByEmployeeIdRepository: IGetFamillesByEmployeeIdRepository
  ) {}

  async execute(id_employee: number): Promise<Familles[]> {
    return this.getFamillesByEmployeeIdRepository.execute(id_employee);
  }
}
