import { IUpdateFamilleRepository } from "@/domain/interfaces/repositories/famille/IUpdateFamilleRepository";
import { Familles } from "@/domain/models/Famille";

export class UpdateFamilleUsecase {
  constructor(
    private readonly updateFamilleRepository: IUpdateFamilleRepository
  ) {}

  async execute(famille: Familles): Promise<Familles> {
    return this.updateFamilleRepository.execute(famille);
  }
}
