import { IGetEventsByUserIdRepository } from "@/domain/interfaces/repositories/evenement/IGetEventsByUserIdRepository.ts";
import { IGetEventsByUserIdUsecase } from "@/domain/interfaces/usecases/evenements/IGetEventsByUserIdUsecase.ts";
import { Evenement } from "@/domain/models/Evenement.ts";

class GetEventsByUserIdUsecase implements IGetEventsByUserIdUsecase {
  constructor(
    private readonly getEventsByUserIdRepository: IGetEventsByUserIdRepository
  ) {}

  async execute(userId: number): Promise<Evenement[]> {
    try {
      const events = await this.getEventsByUserIdRepository.execute(userId);
      return events;
    } catch (error) {
      return [];
    }
  }
}

export default GetEventsByUserIdUsecase;
