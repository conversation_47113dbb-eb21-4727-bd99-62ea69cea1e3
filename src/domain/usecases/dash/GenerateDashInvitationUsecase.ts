import { IDASHInvitationEmailService } from "@/domain/interfaces/services/customEmailService/IDASHInvitationEmailService.ts";
import { IVerifyAndCheckEmail } from "@/domain/interfaces/services/IVerifyAndCheckEmail";
import { ICreateInvitationDashUsecase } from "@/domain/interfaces/usecases/invitationDash/ICreateInvitationDashUsecase";
import { IGetAuthenticatedUserUsecase } from "@/domain/interfaces/usecases/user/IGetAuthenticatedUserUsecase.ts";
import { IGetUserByEmailUsecase } from "@/domain/interfaces/usecases/user/IGetUserByEmailUsecase.ts";
import { utilisateurs_role_enum } from "@/domain/models/enums/utilisateurRole.ts";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation.ts";
import { v4 as uuidv4 } from "uuid";

class GenerateDashInvitationUsecase {
  constructor(
    private readonly emailValidator: IVerifyAndCheckEmail,
    private readonly getAuthenticatedUserUsecase: IGetAuthenticatedUserUsecase,
    private readonly getUserByEmailUsecase: IGetUserByEmailUsecase,
    private readonly createInvitationDashUsecase: ICreateInvitationDashUsecase,
    private readonly dashInvitationEmailService: IDASHInvitationEmailService
  ) {}

  async execute(email: string, organizationName: string) {
    /**
     * TODO:
     * - [ ] Verification de la validitee de l'email
     * - [ ] Verifier que c'est vraiment generee par un administrateur
     * - [ ] Generation d'un token unique
     * - [ ] Creation d'une invitation
     * - [ ] Envoi d'un email
     */

    // 1. Verification de la validitee de l'email
    const isValid = await this.emailValidator.isEmailValid(email);

    if (!isValid) {
      throw new Error("Email invalide.");
    }

    // 2. Verifier que c'est vraiment generee par un administrateur
    const authenticatedUser = await this.getAuthenticatedUserUsecase.execute();

    if (!authenticatedUser || !authenticatedUser.email) {
      throw new Error("Aucune utilisateur connectee.");
    }

    const userData = await this.getUserByEmailUsecase.execute(
      authenticatedUser.email
    );

    if (!userData || userData.role !== utilisateurs_role_enum.ADMIN) {
      throw new Error(
        "Vous n'avez pas les droits pour effectuer cette action."
      );
    }

    // 3. Generation d'un token unique
    const token = uuidv4();

    // 4. Creation d'une invitation
    const invitation = await this.createInvitationDashUsecase.execute({
      email: email,
      token: token,
      cree_par: userData.id,
      est_utilisee: false,
    });

    // Creation de l'url
    const appUrl = `${import.meta.env.VITE_APP_URL}/${PublicRoutesNavigation.DASH_INVITATION.split(":token")[0]}${token}`;

    // 5. Envoi d'un email
    await this.dashInvitationEmailService.execute({
      to: email,
      organizationName: organizationName,
      link: appUrl,
    });

    return invitation;
  }
}

export default GenerateDashInvitationUsecase;
