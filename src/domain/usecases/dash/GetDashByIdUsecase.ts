import { IGetDashByIdRepository } from "@/domain/interfaces/repositories/dash/IGetDashByIdRepository";
import { IGetDashByIdUsecase } from "@/domain/interfaces/usecases/dash/IGetDashByIdUsecase";

class GetDashByIdUsecase implements IGetDashByIdUsecase {
  constructor(private readonly getDashByIdRepository: IGetDashByIdRepository) {}

  async execute(id: number) {
    return this.getDashByIdRepository.execute(id);
  }
}

export default GetDashByIdUsecase;
