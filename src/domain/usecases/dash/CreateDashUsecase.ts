import { ICreateDashRepository } from "@/domain/interfaces/repositories/dash/ICreateDashRepository";
import { ICreateDashUsecase } from "@/domain/interfaces/usecases/dash/ICreateDashUsecase";
import { Dash } from "@/domain/models";

export class CreateDashUsecase implements ICreateDashUsecase {
  constructor(
    private readonly createDashRepository: ICreateDashRepository
  ) {}

  async execute(dash: Omit<Dash, "id">): Promise<Dash> {
    try {
      // Validation des données
      if (!dash.nom || dash.nom.trim() === "") {
        throw new Error("Le nom du dash est requis");
      }

      if (!dash.utilisateur_id || dash.utilisateur_id <= 0) {
        throw new Error("L'ID utilisateur est requis et doit être valide");
      }

      // Nettoyage des données
      const cleanedDash = {
        ...dash,
        nom: dash.nom.trim(),
      };

      // Création du dash
      return await this.createDashRepository.execute(cleanedDash);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
