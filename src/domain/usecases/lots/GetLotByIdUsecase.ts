import { IGetLotByIdRepository } from "@/domain/interfaces/repositories/lots/IGetLotByIdRepository.ts";
import { IGetLotByIdUsecase } from "@/domain/interfaces/usecases/lots/IGetLotByIdUsecase.ts";
import { Lots } from "@/domain/models/Lots.ts";

class GetLotByIdUsecase implements IGetLotByIdUsecase {
  constructor(private readonly getLotByIdRepository: IGetLotByIdRepository) {}

  async execute(lotId: number): Promise<Lots> {
    return await this.getLotByIdRepository.execute(lotId);
  }
}

export default GetLotByIdUsecase;
