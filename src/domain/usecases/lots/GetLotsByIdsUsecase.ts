import { IGetLotsByIdsRepository } from "@/domain/interfaces/repositories/lots/IGetLotsByIdsRepository.ts";
import { IGetLotsByIdsUsecase } from "@/domain/interfaces/usecases/lots/IGetLotsByIdsUsecase.ts";
import { Lots } from "@/domain/models/Lots.ts";

class GetLotsByIdsUsecase implements IGetLotsByIdsUsecase {
  constructor(
    private readonly getLotsByIdsRepository: IGetLotsByIdsRepository
  ) {}

  async execute(ids: number[]): Promise<Lots[]> {
    return await this.getLotsByIdsRepository.execute(ids);
  }
}

export default GetLotsByIdsUsecase;
