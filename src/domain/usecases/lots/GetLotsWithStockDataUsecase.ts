import { LotWithStockData } from "@/domain/DTOS/StockDTO.ts";
import { IGetLotsWithStockDataRepository } from "@/domain/interfaces/repositories/lots/IGetLotsWithStockDataRepository.ts";
import { IGetLotsWithStockDataUsecase } from "@/domain/interfaces/usecases/lots/IGetLotsWithStockDataUsecase.ts";

class GetLotsWithStockDataUsecase implements IGetLotsWithStockDataUsecase {
  constructor(
    private readonly getLotsWithStockDataRepository: IGetLotsWithStockDataRepository
  ) {}

  async execute(userId: number): Promise<LotWithStockData[]> {
    const rawData = await this.getLotsWithStockDataRepository.execute(userId);

    // Transformer les données brutes en un format plus utilisable
    const transformedData: LotWithStockData[] = rawData.map((item) => ({
      id: item.id,
      entree_id: item.entree_id,
      numero_lot: item.numero_lot,
      quantite: item.quantite,
      date_expiration: item.date_expiration,
      stocks: item.entrees_stock.stocks,
    }));

    return transformedData;
  }
}

export default GetLotsWithStockDataUsecase;
