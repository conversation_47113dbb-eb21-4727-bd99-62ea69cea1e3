import { ICreateLotRepository } from "@/domain/interfaces/repositories/lots/ICreateLotRepository.ts";
import { ICreateLotUsecase } from "@/domain/interfaces/usecases/lots/ICreateLotUsecase.ts";
import { Lots } from "@/domain/models/Lots.ts";

class CreateLotUsecase implements ICreateLotUsecase {
  constructor(private readonly createLotRepository: ICreateLotRepository) {}

  async execute(lots: Omit<Lots, "id">[]): Promise<Lots[]> {
    return await this.createLotRepository.execute(lots);
  }
}

export default CreateLotUsecase;
