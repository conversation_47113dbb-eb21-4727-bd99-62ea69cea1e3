import { IUpdateLotRepository } from "@/domain/interfaces/repositories/lots/IUpdateLotRepository.ts";
import { IUpdateLotUsecase } from "@/domain/interfaces/usecases/lots/IUpdateLotUsecase.ts";
import { Lots } from "@/domain/models/Lots.ts";

class UpdateLotUsecase implements IUpdateLotUsecase {
  constructor(private readonly updateLotRepository: IUpdateLotRepository) {}

  async execute(
    lotId: number,
    lotData: Partial<Omit<Lots, "id">>
  ): Promise<Lots | null> {
    if (lotData && lotData?.quantite < 0) {
      throw new Error("La quantité ne peut pas être négative");
    }
    return await this.updateLotRepository.execute(lotId, lotData);
  }
}

export default UpdateLotUsecase;
