import { IDeleteLotRepository } from "@/domain/interfaces/repositories/lots/IDeleteLotRepository.ts";
import { IDeleteLotUsecase } from "@/domain/interfaces/usecases/lots/IDeleteLotUsecase.ts";

class DeleteLotUsecase implements IDeleteLotUsecase {
  constructor(private readonly deleteLotRepository: IDeleteLotRepository) {}

  async execute(lotId: number): Promise<boolean> {
    return await this.deleteLotRepository.execute(lotId);
  }
}

export default DeleteLotUsecase;
