import { IGetLotsByEntreeIdRepository } from "@/domain/interfaces/repositories/lots/IGetLotsByEntreeIdRepository";
import { IGetLotsByEntreeIdUsecase } from "@/domain/interfaces/usecases/lots/IGetLotsByEntreeIdUsecase.ts";

class GetLotsByEntreeIdUsecase implements IGetLotsByEntreeIdUsecase {
  constructor(
    private readonly getLotsByEntreeIdRepository: IGetLotsByEntreeIdRepository
  ) {}

  async execute(entreeId: number) {
    return await this.getLotsByEntreeIdRepository.execute(entreeId);
  }
}

export default GetLotsByEntreeIdUsecase;
