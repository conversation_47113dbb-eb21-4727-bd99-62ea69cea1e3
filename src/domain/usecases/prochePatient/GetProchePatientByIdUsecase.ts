import { IGetProchePatientByIdUsecase } from "@/domain/interfaces/usecases/prochePatient";
import { IGetProchePatientByIdRepository } from "@/domain/interfaces/repositories/prochePatient";
import { ProchePatientDTO } from "@/domain/DTOS";
import {
  IGetPatientByIdRepository,
  IGetPatientByUserIdRepository,
} from "@/domain/interfaces/repositories/patients";
import { Patient, Proche } from "@/domain/models";

export class GetProchePatientByIdUsecase
  implements IGetProchePatientByIdUsecase
{
  constructor(
    private getProchePatientByIdRepository: IGetProchePatientByIdRepository,
    private getPatientByUserIdRepository: IGetPatientByUserIdRepository
  ) {}

  async execute(id: number): Promise<ProchePatientDTO | null> {
    const result = await this.getProchePatientByIdRepository.execute(id);
    let patient: Patient = null;
    if (result.proches[0]) {
      patient = await this.getPatientByUserIdRepository.execute(
        result.proches[0].id_patient
      );

      return { ...result.proches[0], patient };
    } else {
      return null;
    }
  }
}
