import { IGetProcheEmployerByIdUsecase } from "@/domain/interfaces/usecases/prochePatient";
import { IGetProcheByIdRepository } from "@/domain/interfaces/repositories/prochePatient";
import { ProcheEmployerDTO } from "@/domain/DTOS";
import { IGetEmployerByUserIdRepository } from "@/domain/interfaces/repositories/employer";
import { Employer } from "@/domain/models";

export class GetProcheEmployerByIdUsecase
  implements IGetProcheEmployerByIdUsecase
{
  constructor(
    private getProcheByIdRepository: IGetProcheByIdRepository,
    private getEmployerByUserIdRepository: IGetEmployerByUserIdRepository
  ) {}
  async execute(id: number): Promise<ProcheEmployerDTO | null> {
    const result = await this.getProcheByIdRepository.execute(id);
    let employees: Employer = null;
    if (result.proches[0].id_patient) {
      employees = await this.getEmployerByUserIdRepository.execute(
        result.proches[0].id_patient
      );
    }
    return { ...result.proches[0], employees };
  }
}
