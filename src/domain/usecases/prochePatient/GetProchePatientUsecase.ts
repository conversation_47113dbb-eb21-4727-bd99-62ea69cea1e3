import { IGetProchePatientUsecase } from "@/domain/interfaces/usecases/prochePatient";
import { IGetProchePatientRepository } from "@/domain/interfaces/repositories/prochePatient";
import { ProchePatientDTO } from "@/domain/DTOS";

export class GetProchePatientUsecase implements IGetProchePatientUsecase {
  constructor(
    private getProchePatientRepository: IGetProchePatientRepository
  ) {}

  async execute(patientId: number[]): Promise<ProchePatientDTO[]> {
    const data = await this.getProchePatientRepository.execute(patientId);
    const output: ProchePatientDTO[] = data?.flatMap(
      ({ patients, proches }) => {
        if (!proches) {
          return [];
        }
        const patientData = patients[0]; // car il n'y a qu'un seul patient par bloc
        return proches.map((p) => ({
          ...p,
          patient: patientData,
        }));
      }
    );

    return output;
  }
}
