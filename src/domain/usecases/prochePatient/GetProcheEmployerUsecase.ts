import { IGetProcheEmployerUsecase } from "@/domain/interfaces/usecases/prochePatient";
import { IGetProcheEmployerRepository } from "@/domain/interfaces/repositories/prochePatient";
import { ProcheEmployerDTO } from "@/domain/DTOS";

export class GetProcheEmployerUsecase implements IGetProcheEmployerUsecase {
  constructor(
    private getProcheEmployerRepository: IGetProcheEmployerRepository
  ) {}

  async execute(patientId: number[]): Promise<ProcheEmployerDTO[]> {
    const data = await this.getProcheEmployerRepository.execute(patientId);
    const output: ProcheEmployerDTO[] = data?.flatMap(
      ({ employees, proches }) => {
        if (!proches) {
          return [];
        }
        const employerData = employees[0];
        return proches.map((p) => ({
          ...p,
          employees: employerData,
        }));
      }
    );

    return output;
  }
}
