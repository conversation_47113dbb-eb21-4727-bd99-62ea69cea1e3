import { IGetProcheUsecase } from "@/domain/interfaces/usecases/prochePatient";
import { IGetProcheRepository } from "@/domain/interfaces/repositories/prochePatient";
import { Proche } from "@/domain/models";

export class GetProcheUsecase implements IGetProcheUsecase {
  constructor(private getProcheRepository: IGetProcheRepository) {}

  async execute(patientId: number): Promise<Proche[]> {
    return await this.getProcheRepository.execute(patientId);
  }
}
