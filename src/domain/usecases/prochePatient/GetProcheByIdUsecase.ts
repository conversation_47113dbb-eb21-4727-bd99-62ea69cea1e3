import { IGetProcheByIdUsecase } from "@/domain/interfaces/usecases/prochePatient";
import { IGetProcheByIdRepository } from "@/domain/interfaces/repositories/prochePatient";
import { Proche } from "@/domain/models";

export class GetProcheByIdUsecase implements IGetProcheByIdUsecase {
  constructor(private getProcheByIdRepository: IGetProcheByIdRepository) {}

  async execute(id: number): Promise<Proche> {
    const data = await this.getProcheByIdRepository.execute(id);
    return data.proches[0];
  }
}
