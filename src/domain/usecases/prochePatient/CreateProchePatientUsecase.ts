import { Proche, Utilisateur } from "@/domain/models";
import { ICreateProchePatientUsecase } from "@/domain/interfaces/usecases/prochePatient";
import { ICreateProchePatientRepository } from "@/domain/interfaces/repositories/prochePatient";
import { ICreateUserUsecase } from "@/domain/interfaces/usecases/user";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { IDeleteUserRepository } from "@/domain/interfaces/repositories/user";

export class CreateProchePatientUsecase implements ICreateProchePatientUsecase {
  constructor(
    private createProchePatientRepository: ICreateProchePatientRepository,
    private createUserUsecase: ICreateUserUsecase,
    private deleteUserRepository: IDeleteUserRepository
  ) {}

  async execute(
    procheData: Omit<Proche, "id" | "id_utilisateur">
  ): Promise<Proche> {
    const dataUser: Omit<Utilisateur, "id" | "cree_a" | "mis_a_jour_a"> = {
      role: utilisateurs_role_enum.PROCHE,
    };
    const user = await this.createUserUsecase.execute(dataUser);
    if (user && user.id) {
      return await this.createProchePatientRepository.execute({
        ...procheData,
        utilisateur_id: user.id,
      });
    } else {
      await this.deleteUserRepository.execute(user.id);
      throw new Error("erreur lors de la création de l'utilisateur");
    }
  }
}
