import { ICreateHistoriqueCarnetSanteRepository } from '@/domain/interfaces/repositories/historiqueCarnetSante'
import { HistoriqueCarnetSante } from '@/domain/models'

export class CreatehistoriqueCarnetSanteUseCase {
  constructor(
    private readonly createHistoriqueCarnetSanteRepository: ICreateHistoriqueCarnetSanteRepository
  ) {}

  async execute(data: Omit<HistoriqueCarnetSante, 'id'>[]): Promise<HistoriqueCarnetSante[]> {
    return await this.createHistoriqueCarnetSanteRepository.execute(data)
  }
}
