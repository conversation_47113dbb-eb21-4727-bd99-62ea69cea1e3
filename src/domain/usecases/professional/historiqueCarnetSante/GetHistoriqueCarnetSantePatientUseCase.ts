import { HistoriqueCarnetSanteDTO } from "@/domain/DTOS/HistoriqueCarnetSanteDTO";
import { IGetHistoriqueCarnetSanteRepository } from "@/domain/interfaces/repositories/historiqueCarnetSante";

export class GetHistoriqueCarnetSantePatientUseCase {
  constructor(
    private readonly getHistoriqueCarnetSantePatientRepository: IGetHistoriqueCarnetSanteRepository
  ) {}

  async execute(
    carnetId: number,
    tableConcernee: string
  ): Promise<HistoriqueCarnetSanteDTO[]> {
    // Recuperation des donnees brutes
    const data = await this.getHistoriqueCarnetSantePatientRepository.execute(
      carnetId,
      tableConcernee
    );

    // Transformation des donnees en HistoriqueCarnetSanteDTO
    const out: HistoriqueCarnetSanteDTO[] = data.map((historique) => {
      const row: HistoriqueCarnetSanteDTO = {
        id: historique.id,
        id_professionnel: historique.id_professionnel,
        table_concernee: historique.table_concernee,
        id_element_concerne: historique.id_element_concernee,
        date_action: historique.date_action,
        type_action: historique.type_action,
        details: historique.details,
        id_carnet: historique.id_carnet,
        professionnel: {
          id: historique.professionnel.professionnels[0].id as number,
          nom: historique.professionnel.professionnels[0].nom as string,
        },
      };
      return row;
    });

    return out;
  }
}
