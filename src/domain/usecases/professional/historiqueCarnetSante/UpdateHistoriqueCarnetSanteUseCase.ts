import { IUpdateHistoriqueCarnetSanteRepository } from '@/domain/interfaces/repositories/historiqueCarnetSante'
import { HistoriqueCarnetSante } from '@/domain/models'

export class UpdateHistoriqueCarnetSanteUseCase {
  constructor(
    private readonly updateHistoriqueCarnetSanteRepository: IUpdateHistoriqueCarnetSanteRepository
  ) {}

  async execute(id: number, data: Partial<HistoriqueCarnetSante>): Promise<HistoriqueCarnetSante> {
    return await this.updateHistoriqueCarnetSanteRepository.execute(id, data)
  }
}
