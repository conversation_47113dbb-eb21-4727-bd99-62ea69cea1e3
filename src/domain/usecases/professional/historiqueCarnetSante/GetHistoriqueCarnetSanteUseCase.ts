import { HistoriqueCarnetSanteDTO } from "@/domain/DTOS/HistoriqueCarnetSanteDTO";
import { IGetHistoriqueCarnetSanteRepository } from "@/domain/interfaces/repositories/historiqueCarnetSante";

export class GetHistoriqueCarnetSanteUseCase {
  constructor(
    private readonly getHistoriqueCarnetSanteEmployeeRepository: IGetHistoriqueCarnetSanteRepository
  ) {}

  async execute(
    carnetId: number,
    tableConcernee: string
  ): Promise<HistoriqueCarnetSanteDTO[]> {
    // Recuperation des donnees brutes

    const data = await this.getHistoriqueCarnetSanteEmployeeRepository.execute(
      carnetId,
      tableConcernee
    );

    // Transformation des donnees en HistoriqueCarnetSanteDTO
    const out: HistoriqueCarnetSanteDTO[] = data.map((historique) => {
      const row: HistoriqueCarnetSanteDTO = {
        id: historique.id,
        id_professionnel: historique.id_professionnel,
        table_concernee: historique.table_concernee,
        id_element_concerne: historique.id_element_concernee,
        date_action: historique.date_action,
        type_action: historique.type_action,
        details: historique.details,
        id_carnet: historique.id_carnet,
        professionnel: {
          id: historique.professionnel.dash[0].id as number,
          nom: historique.professionnel.dash[0].nom as string,
        },
      };
      return row;
    });

    return out;
  }
}
