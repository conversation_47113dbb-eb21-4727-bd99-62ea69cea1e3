import { Vaccination } from '@/domain/models'
import { ICreateVaccinationRepository } from '@/domain/interfaces/repositories/vaccination'

export class CreateVaccinationUseCase {
  constructor(
    private readonly createVaccinationRepository: ICreateVaccinationRepository
  ) {}

  async execute(data: Omit<Vaccination, "id">[]): Promise<Vaccination[]> {
    const vaccination = await this.createVaccinationRepository.create(data)
    const result = vaccination.map((r) => {
      return{
        ...r,
        date_administration: r.date_administration instanceof Date ? new Date(r.date_administration).toISOString() : r.date_administration,
        prochaine_date_echeance: r.prochaine_date_echeance instanceof Date ? new Date(r.prochaine_date_echeance).toISOString() : r.prochaine_date_echeance,
      }
    })
    return result
  }
}
