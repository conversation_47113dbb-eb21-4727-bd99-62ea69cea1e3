import { Vaccination } from '@/domain/models'
import { IUpdateVaccinationRepository } from '@/domain/interfaces/repositories/vaccination'

export class UpdateVaccinationUseCase {
  constructor(
    private readonly updateVaccinationRepository: IUpdateVaccinationRepository
  ) {}

  async execute(id: number, data: Partial<Vaccination>): Promise<Vaccination> {
    const vaccination = await this.updateVaccinationRepository.update(id, data)
    return{
      ...vaccination,
      date_administration: vaccination.date_administration instanceof Date ? new Date(vaccination.date_administration).toISOString() : vaccination.date_administration,
      prochaine_date_echeance: vaccination.prochaine_date_echeance instanceof Date ? new Date(vaccination.prochaine_date_echeance).toISOString() : vaccination.prochaine_date_echeance,
    }
  }
}
