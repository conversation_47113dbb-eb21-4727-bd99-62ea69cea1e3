import { Vaccination } from '@/domain/models'
import { IGetVaccinationRepository } from '@/domain/interfaces/repositories/vaccination'

export class GetVaccinationUseCase {
  constructor(
    private readonly getVaccinationRepository: IGetVaccinationRepository
  ) {}

  async getById(id: number): Promise<Vaccination | null> {
    return await this.getVaccinationRepository.getById(id)
  }

  async getAll(carnetId: number): Promise<Vaccination[]> {
    return await this.getVaccinationRepository.getAll(carnetId)
  }
}
