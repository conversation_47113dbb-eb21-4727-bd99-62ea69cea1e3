import { IUpdateSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";
import { signe_vitaux } from "@/domain/models/SigneVitaux";

export class UpdateSigneVitauxUseCase {
  constructor(private readonly updateSigneVitauxRepository: IUpdateSigneVitauxRepository) {}

  async execute(id: number, data: Partial<signe_vitaux>): Promise<signe_vitaux> {
    return this.updateSigneVitauxRepository.execute(id, data);
  }
}
