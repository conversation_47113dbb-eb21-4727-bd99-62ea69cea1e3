import { IGetAllSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";
import { signe_vitaux } from "@/domain/models/SigneVitaux";

export class GetAllSigneVitauxUseCase {
  constructor(private readonly getAllSigneVitauxRepository: IGetAllSigneVitauxRepository) {}

  async execute(carnetId: number): Promise<signe_vitaux[]> {
    return this.getAllSigneVitauxRepository.execute(carnetId);
  }
}
