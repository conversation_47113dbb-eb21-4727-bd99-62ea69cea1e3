import { IGetByIdSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";
import { signe_vitaux } from "@/domain/models/SigneVitaux";

export class GetByIdSigneVitauxUseCase {
  constructor(private readonly getByIdSigneVitauxRepository: IGetByIdSigneVitauxRepository) {}

  async execute(carnetId: number): Promise<signe_vitaux> {
    return this.getByIdSigneVitauxRepository.execute(carnetId);
  }
}
