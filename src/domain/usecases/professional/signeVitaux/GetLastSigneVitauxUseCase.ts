import { IGetLastSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";
import { signe_vitaux } from "@/domain/models/SigneVitaux";
import { SupabaseError } from "@/infrastructure/supabase/supabaseError";

export class GetLastSigneVitauxUseCase {
  constructor(
    private readonly getLastSigneVitauxRepository: IGetLastSigneVitauxRepository,
  ) {}

  async execute(carnetId: number): Promise<signe_vitaux | null> {
    try {
      const data = await this.getLastSigneVitauxRepository.execute(carnetId);
      console.log("data", data);
      if (!data) return null;

      return data;
    } catch (error) {
      const formatedError = error as SupabaseError;
      console.log(
        "Erreur lors de la recuperation des signes vitaux:",
        formatedError.code,
        formatedError.message,
      );
      return null;
    }
  }
}
