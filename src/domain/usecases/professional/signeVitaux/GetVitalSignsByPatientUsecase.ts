import { VitalSignsDetails } from "@/domain/DTOS/VitalSignsDetailsDTO";
import { IGetVitalSignsByPatientRepository } from "@/domain/interfaces/repositories/signeVitaux/IGetVitalSignsByPatientRepository";
import { IGetVitalSignsByPatientUsecase } from "@/domain/interfaces/usecases/signeVitaux/IGetVitalSignsByPatientUsecase";

class GetVitalSignsByPatientUsecase implements IGetVitalSignsByPatientUsecase {
    constructor(
        private readonly getVitalSignsByPatientRepository: IGetVitalSignsByPatientRepository
    ) { }

    async execute(patientId: number): Promise<VitalSignsDetails[]> {
        try {
            // Validate patient ID
            if (!patientId || patientId <= 0) {
                throw new Error("Invalid patient ID provided");
            }

            const vitalSigns = await this.getVitalSignsByPatientRepository.execute(patientId);

            // Data is already sorted by date in descending order from repository
            return vitalSigns;
        } catch (error) {
            throw new Error(`Failed to get vital signs for patient ${patientId}: ${error}`);
        }
    }
}

export default GetVitalSignsByPatientUsecase;