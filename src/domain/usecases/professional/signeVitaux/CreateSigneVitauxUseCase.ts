import { signe_vitaux } from "@/domain/models/SigneVitaux";
import { CreateSigneVitauxRepository } from "@/infrastructure/repositories/signesVitaux";

export class CreateSigneVitauxUseCase {
  constructor(
    private readonly createSigneVitauxRepository: CreateSigneVitauxRepository
  ) {}

  async execute(data: Omit<signe_vitaux, 'id'>): Promise<signe_vitaux> {
    return this.createSigneVitauxRepository.execute(data);
  }
}
