import { ISupabaseUploader } from "@/domain/interfaces/services/ISupabaseUploader";
import { Photo } from "@/domain/models/Photo.ts";
import { PRESENTATION_IMAGE_BUCKET } from "@/domain/usecases/user/Register/constants";

/**
 * Usecase pour la suppression d'une image du cabinet médical
 *
 * @description
 * Ce usecase gère la suppression complète d'une image du cabinet :
 * - Récupération des informations de la photo (pour obtenir le chemin du fichier)
 * - Suppression du fichier dans Supabase Storage (bucket 'cabinet')
 * - Suppression de l'enregistrement en base de données
 * - Gestion des erreurs et rollback si nécessaire
 */
export class DeleteCabinetImageUsecase implements IDeleteCabinetImageUsecase {
  constructor(
    private readonly supabaseUploader: ISupabaseUploader,
    private readonly deleteCabinetImageRepository: IDeleteCabinetImageRepository
  ) {}

  /**
   * Exécute la suppression d'une image du cabinet
   *
   * @param photoId - ID de la photo à supprimer
   * @returns Résultat de la suppression
   */
  async execute(photoId: number): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Validation des paramètres
      if (!photoId || photoId <= 0) {
        return {
          success: false,
          error: "ID de photo invalide",
        };
      }

      // Supprimer la photo (récupère le chemin pour supprimer le fichier)
      const deleteResult =
        await this.deleteCabinetImageRepository.execute(photoId);

      if (!deleteResult.success) {
        return {
          success: false,
          error: "Erreur lors de la suppression de la photo en base de données",
        };
      }

      // Supprimer le fichier du storage si on a le chemin
      if (deleteResult.photoPath) {
        try {
          const fileName = this.extractFileNameFromUrl(deleteResult.photoPath);
          if (fileName) {
            const { error: storageError } =
              await this.supabaseUploader.deleteFile?.(
                PRESENTATION_IMAGE_BUCKET,
                fileName
              );

            if (storageError) {
              console.warn(
                "Erreur lors de la suppression du fichier du storage:",
                storageError
              );
              // On ne fait pas échouer l'opération si la suppression du fichier échoue
              // car l'enregistrement en base a déjà été supprimé
            }
          }
        } catch (error) {
          console.warn("Erreur lors de la suppression du fichier:", error);
          // On ne fait pas échouer l'opération
        }
      }

      return {
        success: true,
      };
    } catch (error) {
      console.error(
        "Erreur lors de la suppression de l'image du cabinet:",
        error
      );
      return {
        success: false,
        error: error instanceof Error ? error.message : "Erreur inconnue",
      };
    }
  }

  /**
   * Extrait le nom du fichier depuis une URL Supabase
   */
  private extractFileNameFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split("/");
      return pathParts[pathParts.length - 1] || null;
    } catch (error) {
      console.error("Erreur lors de l'extraction du nom de fichier:", error);
      return null;
    }
  }
}
