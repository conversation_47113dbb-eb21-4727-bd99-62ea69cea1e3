import { ICreateProfessionalEtablishmentRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel";
import { ICreateProfessionalEtablishmentUsecase } from "@/domain/interfaces/usecases/etablissementProfessionnel";
import { EtablissementProfessionnel } from "@/domain/models";

/**
 * Use case pour créer un établissement professionnel
 *
 * @description Ce use case gère la création d'un nouvel établissement professionnel
 * en utilisant le repository approprié et en gérant les erreurs potentielles.
 */
class CreateProfessionalEtablishmentUsecase
  implements ICreateProfessionalEtablishmentUsecase
{
  constructor(
    private readonly createEtablissementProfessionnelRepository: ICreateProfessionalEtablishmentRepository,
  ) {}

  /**
   * Exécute la création d'un établissement professionnel
   *
   * @param etablissement - Les données de l'établissement à créer (sans l'ID)
   * @returns Promise<EtablissementProfessionnel> - L'établissement créé avec son ID
   * @throws Error si la création échoue
   */
  async execute(
    etablissement: Omit<EtablissementProfessionnel, "id">,
  ): Promise<EtablissementProfessionnel> {
    try {
      // Validation des données requises
      if (!etablissement.nom_etablissement?.trim()) {
        throw new Error("Le nom de l'établissement est requis");
      }

      if (!etablissement.nom_responsable?.trim()) {
        throw new Error("Le nom du responsable est requis");
      }

      if (!etablissement.prenom_responsable?.trim()) {
        throw new Error("Le prénom du responsable est requis");
      }

      if (!etablissement.id_professionnel) {
        throw new Error("L'ID du professionnel est requis");
      }

      // Création de l'établissement via le repository
      const nouvelEtablissement =
        await this.createEtablissementProfessionnelRepository.execute(
          etablissement,
        );

      return nouvelEtablissement;
    } catch (error) {
      console.error(
        "Erreur lors de la création de l'établissement professionnel:",
        error,
      );

      // Re-lancer l'erreur avec un message approprié
      if (error instanceof Error) {
        throw new Error(
          `Échec de la création de l'établissement: ${error.message}`,
        );
      }

      throw new Error(
        "Erreur inconnue lors de la création de l'établissement professionnel",
      );
    }
  }
}

export default CreateProfessionalEtablishmentUsecase;
