import { IGetProfessionalEtablishmentsByProfessionalIdRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel";
import { IGetProfessionalEtablishmentsUsecase } from "@/domain/interfaces/usecases/etablissementProfessionnel";

class GetProfessionalEtablishmentsUsecase
  implements IGetProfessionalEtablishmentsUsecase
{
  constructor(
    private readonly getProfessionalEtablishmentByProfessionalIdRepository: IGetProfessionalEtablishmentsByProfessionalIdRepository,
  ) {}

  async execute(id: number) {
    return this.getProfessionalEtablishmentByProfessionalIdRepository.execute(
      id,
    );
  }
}

export default GetProfessionalEtablishmentsUsecase;
