import { IUpdateProfessionalEtablishmentRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel";
import { EtablissementProfessionnel } from "@/domain/models";

class UpdateProfessionalEtablishmentUsecase
  implements UpdateProfessionalEtablishmentUsecase
{
  constructor(
    private readonly updateProfessionalEtablishmentRepository: IUpdateProfessionalEtablishmentRepository,
  ) {}

  async execute(
    id: number,
    updates: Partial<Omit<EtablissementProfessionnel, "id">>,
  ) {
    return this.updateProfessionalEtablishmentRepository.execute(id, updates);
  }
}

export default UpdateProfessionalEtablishmentUsecase;
