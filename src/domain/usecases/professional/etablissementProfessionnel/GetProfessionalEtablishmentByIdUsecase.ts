import { IGetProfessionalEtablishmentByIdRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel";
import { IGetProfessionalEtablishmentByIdUsecase } from "@/domain/interfaces/usecases/etablissementProfessionnel";

class GetProfessionalEtablishmentByIdUsecase
  implements IGetProfessionalEtablishmentByIdUsecase
{
  constructor(
    private readonly getProfessionalEtablishmentByIdRepository: IGetProfessionalEtablishmentByIdRepository,
  ) {}

  async execute(id: number) {
    return this.getProfessionalEtablishmentByIdRepository.execute(id);
  }
}

export default GetProfessionalEtablishmentByIdUsecase;
