import { IDeleteProfessionalEtablishmentRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel";
import { IDeleteProfessionalEtablishmentUsecase } from "@/domain/interfaces/usecases/etablissementProfessionnel";

class DeleteProfessionalEtablishmentUsecase
  implements IDeleteProfessionalEtablishmentUsecase
{
  constructor(
    private readonly deleteProfessionalEtablishmentRepository: IDeleteProfessionalEtablishmentRepository,
  ) {}

  async execute(id: number) {
    return this.deleteProfessionalEtablishmentRepository.execute(id);
  }
}

export default DeleteProfessionalEtablishmentUsecase;
