// SearchProfessionalsUseCase.ts
import {
  ProfessionalCardDTO,
  SearchProfessionalDTO,
} from "@/domain/DTOS/ProfessionalDTO";
import { ProfessionalSearchMapper } from "@/domain/mappers/ProfessionalSearchMapper";
import type { ISearchProfessionalsRepository } from "@/domain/interfaces/repositories/ISearchProfessionalsRepository";
import { IProfessionalAvailabilitiesFilter } from "@/domain/interfaces/services/IProfessionalAvaiabilitiesFilter";

type UseCaseParams = {
  name?: string | null;
  localization?: string | null;
  today?: string; // ISO date string (ex: new Date().toISOString())
  page?: number;
  limit?: number;
};

class SearchProfessionalsUsecase {
  constructor(
    private readonly repo: ISearchProfessionalsRepository,
    private readonly professionalAvailabilitiesFilter: IProfessionalAvailabilitiesFilter
  ) {}

  /**
   * Logique métier pour la recherche de professionnels de santé :
   * - Appelle le repository pour récupérer les données brutes
   * - Applique les filtres métier (rendez-vous futurs, etc.)
   * - Transforme les données via le mapper vers le format ProfessionalCardDTO
   * - Applique le tri par pertinence
   * @param params Paramètres de recherche
   * @returns Liste des professionnels formatés pour l'affichage
   */
  async execute(params: UseCaseParams): Promise<ProfessionalCardDTO[]> {
    const { name, localization, today, page, limit } = params;

    // Récupère les professionnels depuis le repository
    const searchResults = await this.repo.execute({
      name,
      localization,
      page,
      limit,
    });

    // Applique la logique métier de filtrage
    const filteredResults = this.applyBusinessLogicFilters(
      searchResults,
      today,
      name
    );

    // Transforme les données via le mapper
    const professionalCards =
      ProfessionalSearchMapper.toProfessionalCardDTOList(
        filteredResults,
        today,
        this.professionalAvailabilitiesFilter
      );

    // Applique le tri par pertinence
    return this.sortByRelevance(professionalCards, name);
  }

  /**
   * Applique les filtres de logique métier sur les résultats bruts
   * @param results Résultats bruts du repository
   * @param today Date de référence pour filtrer les rendez-vous
   * @param searchQuery Terme de recherche pour filtrer par relations
   * @returns Résultats filtrés
   */
  private applyBusinessLogicFilters(
    results: SearchProfessionalDTO[],
    today?: string,
    searchQuery?: string | null
  ): SearchProfessionalDTO[] {
    const todayDate = today ? new Date(today) : new Date();
    const normalizedQuery = searchQuery?.toLowerCase().trim();

    return results
      .map((pro) => {
        const proClone = { ...pro };

        // Filtre les rendez-vous pour ne garder que les futurs
        if (Array.isArray(proClone.rendez_vous)) {
          proClone.rendez_vous = proClone.rendez_vous.filter((rv) => {
            const appointmentDate = new Date(rv.date_rendez_vous);
            return (
              !isNaN(appointmentDate.getTime()) && appointmentDate >= todayDate
            );
          });
        }

        return proClone;
      })
      .filter((pro) => {
        // Si pas de terme de recherche, on garde tous les résultats
        if (!normalizedQuery) return true;

        // Recherche dans les spécialités
        const matchInSpecialities = pro.specialites_professionnel?.some(
          (specialite) =>
            specialite.nom_specialite?.toLowerCase().includes(normalizedQuery)
        );

        // Recherche dans les mots-clés
        const matchInMotCles = pro.mot_cles_professionnel?.some((motCle) =>
          motCle.symptome?.toLowerCase().includes(normalizedQuery)
        );

        // Recherche dans les établissements
        const matchInEtablissements = pro.etablissements_professionnel?.some(
          (etablissement) =>
            etablissement.nom_etablissement
              ?.toLowerCase()
              .includes(normalizedQuery) ||
            etablissement.nom_responsable
              ?.toLowerCase()
              .includes(normalizedQuery) ||
            etablissement.prenom_responsable
              ?.toLowerCase()
              .includes(normalizedQuery)
        );

        // Recherche dans les champs du professionnel (déjà filtrés par le repository)
        const matchInProfessional =
          pro.nom?.toLowerCase().includes(normalizedQuery) ||
          pro.prenom?.toLowerCase().includes(normalizedQuery) ||
          pro.raison_sociale?.toLowerCase().includes(normalizedQuery) ||
          pro.numero_ordre?.toLowerCase().includes(normalizedQuery) ||
          pro.presentation_generale?.toLowerCase().includes(normalizedQuery);

        return (
          matchInSpecialities ||
          matchInMotCles ||
          matchInEtablissements ||
          matchInProfessional
        );
      });
  }

  /**
   * Trie les professionnels par pertinence
   * @param professionals Liste des professionnels
   * @param searchQuery Terme de recherche pour calculer la pertinence
   * @returns Liste triée par pertinence
   */
  private sortByRelevance(
    professionals: ProfessionalCardDTO[],
    searchQuery?: string | null
  ): ProfessionalCardDTO[] {
    return professionals.sort((a, b) => {
      // Priorité 1: Professionnels acceptant de nouveaux patients
      if (a.nouveau_patient_acceptes !== b.nouveau_patient_acceptes) {
        return (
          (b.nouveau_patient_acceptes ? 1 : 0) -
          (a.nouveau_patient_acceptes ? 1 : 0)
        );
      }

      // Priorité 2: Nombre de créneaux disponibles
      const aSlots = a.disponibilite?.length || 0;
      const bSlots = b.disponibilite?.length || 0;
      if (aSlots !== bSlots) {
        return bSlots - aSlots;
      }

      // Priorité 3: Correspondance exacte avec le nom/prénom
      if (searchQuery) {
        const query = searchQuery.toLowerCase().trim();
        const aExactMatch = this.hasExactMatch(a, query);
        const bExactMatch = this.hasExactMatch(b, query);
        if (aExactMatch !== bExactMatch) {
          return bExactMatch ? 1 : -1;
        }
      }

      // Priorité 4: Ordre alphabétique par nom
      return a.nom.localeCompare(b.nom);
    });
  }

  /**
   * Vérifie si un professionnel correspond exactement au terme de recherche
   * @param professional Professionnel à vérifier
   * @param query Terme de recherche
   * @returns true si correspondance exacte
   */
  private hasExactMatch(
    professional: ProfessionalCardDTO,
    query: string
  ): boolean {
    const fullName =
      `${professional.nom} ${professional.prenom || ""}`.toLowerCase();
    return (
      fullName.includes(query) ||
      (professional.raison_sociale || "").toLowerCase().includes(query)
    );
  }
}

export default SearchProfessionalsUsecase;
