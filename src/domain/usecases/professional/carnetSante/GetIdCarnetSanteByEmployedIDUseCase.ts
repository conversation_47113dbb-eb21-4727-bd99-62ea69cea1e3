import { IGetIdCarnetSanteByEmployedIDRepository } from "@/domain/interfaces/repositories/carnetSante";

export class GetIdCarnetSanteByEmployedIDUseCase {
  constructor(
    private readonly getIdCarnetSanteByEmployedIDRepository: IGetIdCarnetSanteByEmployedIDRepository
  ) {}

  async execute(id: number): Promise<number> {
    return await this.getIdCarnetSanteByEmployedIDRepository.execute(id);
  }
}
