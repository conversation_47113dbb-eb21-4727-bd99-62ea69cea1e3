import { IGetDistinctEventsByProfessionalIdRepository } from "@/domain/interfaces/repositories/evenement";
import { Evenement } from "@/domain/models";

export class GetDistinctEventsByProfessionalIdUsecase {
  constructor(
    private readonly getDistinctEventsByProfessionalIdRepository: IGetDistinctEventsByProfessionalIdRepository
  ) {}

  async execute(professionalId: number): Promise<Evenement[] | null> {
    const events =
      await this.getDistinctEventsByProfessionalIdRepository.execute(
        professionalId
      );
    return events;
  }
}
