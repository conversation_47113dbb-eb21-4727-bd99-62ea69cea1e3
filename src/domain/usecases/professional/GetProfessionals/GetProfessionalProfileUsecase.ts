import {
  ProfessionalCardDTO,
  TimeSlotProffessionalCard,
} from "@/domain/DTOS/ProfessionalDTO.ts";
import { IGetProfessionalProfileRepository } from "@/domain/interfaces/repositories/professionals";
import { IProfessionalAvailabilitiesFilter } from "@/domain/interfaces/services/IProfessionalAvaiabilitiesFilter.ts";
import { professionnels_titre_enum } from "@/domain/models/enums/professionnelsTitreEnum.ts";
import { Evenement } from "@/domain/models/Evenement.ts";
import { RendezVous } from "@/domain/models/RendezVous.ts";

class GetProfessionalProfileUsecase {
  constructor(
    private readonly getProfessionalProfileRepository: IGetProfessionalProfileRepository,
    private readonly professionalAvailabilitiesFilter: IProfessionalAvailabilitiesFilter
  ) {}

  async execute(
    title: professionnels_titre_enum,
    firstName: string,
    lastName: string,
    address: string
  ) {
    const dataBrute = await this.getProfessionalProfileRepository.execute(
      title,
      firstName,
      lastName,
      address
    );

    const professionalDataBrute = dataBrute?.professionnels[0];

    const appointments =
      (professionalDataBrute?.rendez_vous as RendezVous[]) || [];
    const events = (professionalDataBrute?.evenement as Evenement[]) || [];
    const temps_moyen_consulation =
      (professionalDataBrute?.parametre_disponibilite[0]
        ?.temps_moyen_consulation as number) || 30;

    const formattedAvailalities: TimeSlotProffessionalCard[] =
      this.professionalAvailabilitiesFilter.filter(
        professionalDataBrute?.parametre_disponibilite[0],
        appointments,
        events,
        temps_moyen_consulation
      );

    const out: ProfessionalCardDTO = {
      specialite: professionalDataBrute?.specialites_professionnel || [],
      disponibilite: formattedAvailalities,
      etablissements_professionnel:
        professionalDataBrute?.etablissements_professionnel,
      contacts: dataBrute?.contact,
      publications: professionalDataBrute?.publication_professionnel,
      experiences: professionalDataBrute?.experience_professionnel,
      diplomes: professionalDataBrute?.diplome_professionnel,
      langues: professionalDataBrute?.langues_parlees_professionnel,
      types_consultation: professionalDataBrute?.types_consultation || "",
      motCles: professionalDataBrute?.mot_cles_professionnel,
      horaire_hebdomadaire:
        professionalDataBrute?.parametre_disponibilite[0].horaire_hebdomadaire,
      email: dataBrute?.email,
      photos: dataBrute?.photos,
      id: dataBrute?.id,
      utilisateur_id: professionalDataBrute?.utilisateur_id,
      titre: professionalDataBrute?.titre,
      nom: professionalDataBrute?.nom,
      prenom: professionalDataBrute?.prenom,
      sexe: professionalDataBrute?.sexe,
      numero_ordre: professionalDataBrute?.numero_ordre,
      raison_sociale: professionalDataBrute?.raison_sociale,
      nif: professionalDataBrute?.nif,
      stat: professionalDataBrute?.stat,
      presentation_generale: professionalDataBrute?.presentation_generale,
      modes_paiement_acceptes: professionalDataBrute?.modes_paiement_acceptes,
      nouveau_patient_acceptes: professionalDataBrute?.nouveau_patient_acceptes,
      adresse: professionalDataBrute?.adresse,
      region: professionalDataBrute?.region,
      district: professionalDataBrute?.district,
      commune: professionalDataBrute?.commune,
      fokontany: professionalDataBrute?.fokontany,
      informations_acces: professionalDataBrute?.informations_acces,
      geolocalisation: professionalDataBrute.geolocalisation,
    };

    console.log("fadlkjf", out.specialite);

    return out;
  }
}

export default GetProfessionalProfileUsecase;
