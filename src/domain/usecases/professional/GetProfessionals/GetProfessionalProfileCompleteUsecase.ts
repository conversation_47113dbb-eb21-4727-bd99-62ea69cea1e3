import { IGetProfessionalProfileCompleteUsecase } from "@/domain/interfaces/usecases/professionals/IGetProfessionalProfileCompleteUsecase";
import { IGetProfessionalProfileCompleteRepository } from "@/domain/interfaces/repositories/professionals/IGetProfessionalProfileCompleteRepository";
import { ProfessionalProfileDTO } from "@/domain/DTOS/CabinetMedicalFormDTO";
import { Utilisateur } from "@/domain/models/Utilisateurs.ts";
import { Professionnel } from "@/domain/models/Professionnel.ts";

/**
 * Use case optimisé pour récupérer toutes les données du profil professionnel
 *
 * @description Ce use case orchestre la récupération de toutes les informations
 * nécessaires pour construire un profil professionnel complet en une seule opération.
 * Il gère la logique métier, le traitement des données et la construction du DTO final.
 *
 * @architecture
 * Ce use case respecte les principes de Clean Architecture :
 * - Sépare la logique métier de l'infrastructure
 * - Utilise l'inversion de dépendance avec les interfaces
 * - Orchestre les opérations complexes
 * - Gère les erreurs de manière appropriée
 * - Traite et valide les données métier
 *
 * @performance
 * Cette approche optimise les performances en :
 * - Réduisant le nombre de requêtes de ~12 à 1 seule requête
 * - Minimisant la latence réseau
 * - Centralisant le traitement des données
 * - Évitant les multiples aller-retours avec la base de données
 */
export class GetProfessionalProfileCompleteUsecase
  implements IGetProfessionalProfileCompleteUsecase
{
  /**
   * Constructeur du use case
   *
   * @param repository - Repository pour récupérer les données complètes du profil
   */
  constructor(
    private readonly repository: IGetProfessionalProfileCompleteRepository
  ) {}

  private generateUserDataObject(profileData: any): Utilisateur {
    return {
      id: profileData.id,
      role: profileData.role,
      email: profileData?.email,
      mot_de_passe_hash: profileData?.mot_de_passe_hash,
      bani: profileData?.bani,
      cree_a: profileData?.cree_a,
      mis_a_jour_a: profileData?.mis_a_jour_a,
    };
  }

  private generateProfessionalDataObject(profileData: any): Professionnel {
    const professionnel = profileData.professionnel[0];
    return {
      id: professionnel.id,
      utilisateur_id: professionnel.utilisateur_id,
      titre: professionnel.titre,
      nom: professionnel.nom,
      prenom: professionnel?.prenom,
      sexe: professionnel?.sexe,
      numero_ordre: professionnel.numero_ordre,
      raison_sociale: professionnel?.raison_sociale,
      nif: professionnel?.nif,
      stat: professionnel?.stat,
      presentation_generale: professionnel?.presentation_generale,
      temps_moyen_consulation: professionnel?.temps_moyen_consulation,
      types_consultation: professionnel?.types_consultation,
      modes_paiement_acceptes: professionnel?.modes_paiement_acceptes,
      nouveau_patient_acceptes: professionnel.nouveau_patient_acceptes,
      adresse: professionnel.adresse,
      region: professionnel.region,
      district: professionnel.district,
      commune: professionnel.commune,
      fokontany: professionnel.fokontany,
      informations_acces: professionnel.informations_acces,
      geolocalisation: professionnel.geolocalisation,
    };
  }

  /**
   * Récupère et traite toutes les données du profil professionnel
   *
   * @param professionalId - L'identifiant unique du professionnel
   * @returns Promise<ProfessionalProfileDTO | null> - Les données complètes du profil traitées ou null si non trouvé
   *
   * @throws {Error} En cas d'erreur lors de la récupération ou du traitement des données
   */
  async execute(
    professionalId: number
  ): Promise<ProfessionalProfileDTO | null> {
    try {
      if (!professionalId || professionalId <= 0) {
        throw new Error(
          "L'identifiant du professionnel doit être un nombre positif valide"
        );
      }

      const profileData = await this.repository.execute(professionalId);

      if (!profileData) {
        return null;
      }

      // Traitement des donnees brutes
      const out: ProfessionalProfileDTO = {
        baseInfo: this.generateProfessionalDataObject(profileData),
        userData: this.generateUserDataObject(profileData),
        diplomas: profileData.professionnel[0].diplome_professionnel,
        publications: profileData.professionnel[0].publication_professionnel,
        etablishments:
          profileData.professionnel[0].etablissements_professionnel,
        specialities: profileData.professionnel[0].specialites_professionnel,
        keywords: profileData.professionnel[0].mot_cles_professionnel,
        languages: profileData.professionnel[0].langues_parlees_professionnel,
        experiences: profileData.professionnel[0].experience_professionnel,
        photos: profileData.photos,
        paymentMethods: profileData.professionnel[0].modes_paiement_acceptes,
        insurances: profileData.professionnel[0].assurances_professionnel.map(
          (data) => data.assurance
        ),
        contact: profileData.contact,
      };

      return out;
    } catch (error) {
      console.error(
        "Erreur dans GetProfessionalProfileCompleteUsecase:",
        error
      );
      throw error;
    }
  }
}
