import { ProfessionalProfileData } from "@/domain/DTOS";
import { IGetProfessionalByIdRepository } from "@/domain/interfaces/repositories/professionals/IGetProfessionalByIdRepository";
import { IGetProfessionalEtablishmentsUsecase } from "@/domain/interfaces/usecases/etablissementProfessionnel";
import { IGetProfessionalAvailabilityByProfessionalIdUsecase } from "@/domain/interfaces/usecases/professionalAvailability";
import { IGetProfessionalProfileDataUsecase } from "@/domain/interfaces/usecases/professionals/IGetProfessionalProfileDataUsecase";
import { IGetProfessionalSpecialitiesUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";

export class GetProfessionalByIdUsecase implements IGetProfessionalProfileDataUsecase {
  constructor(
    private readonly getProfessionalByIdRepository: IGetProfessionalByIdRepository,
    private readonly getProfessionalSpecialitiesByProfessionalIdUsecase: IGetProfessionalSpecialitiesUsecase,
    private readonly getProfessionalAvailabilityByProfessionalIdUsecase: IGetProfessionalAvailabilityByProfessionalIdUsecase,
    private readonly getProfessionalEtablishmentsUsecase: IGetProfessionalEtablishmentsUsecase,
  ) {}

  async execute(id: number): Promise<ProfessionalProfileData | null> {
    const professionalData =
      await this.getProfessionalByIdRepository.execute(id);

    if (!professionalData) {
      return null;
    }

    const professionalSpecialities =
      await this.getProfessionalSpecialitiesByProfessionalIdUsecase.execute(id);
    const professionalAvailabilitySettings =
      await this.getProfessionalAvailabilityByProfessionalIdUsecase.execute(id);
    const professionalEtablishments =
      await this.getProfessionalEtablishmentsUsecase.execute(id);

    const out: ProfessionalProfileData = {
      ...professionalData,
      specialites_professionnel: professionalSpecialities,
      parametre_disponibilite: professionalAvailabilitySettings,
      etablissements_professionnel: professionalEtablishments,
    };

    return out;
  }
}
