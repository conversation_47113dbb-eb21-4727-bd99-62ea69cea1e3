import {
  IDeleteAntecedentSociauxAlcooliqueRepository,
  IDeleteAntecedentSociauxFummeurRepository,
} from "@/domain/interfaces/repositories/antecedentSociaux";

export class DeleteAntecedentSociauxUseCase {
  constructor(
    private readonly deleteAntecedentSociauxFummeurRepository: IDeleteAntecedentSociauxFummeurRepository,
    private readonly deleteAntecedentSociauxAlcooliqueRepository: IDeleteAntecedentSociauxAlcooliqueRepository
  ) {}

  async execute(id: number, nom: string): Promise<void> {
    console.log(id, nom);
    if (nom === "Fummeur") {
      await this.deleteAntecedentSociauxFummeurRepository.execute(id);
    } else {
      await this.deleteAntecedentSociauxAlcooliqueRepository.execute(id);
    }
  }
}
