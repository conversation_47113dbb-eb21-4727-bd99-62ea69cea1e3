import {
  Antecedant_sociaux,
  antecedant_sociaux_alcoolique,
  antecedant_sociaux_fumeur,
} from "@/domain/models";
import {
  ICreateAntecedentSociauxFummeurRepository,
  ICreateAntecedentSociauxAlcooliqueRepository,
} from "@/domain/interfaces/repositories/antecedentSociaux";

export class CreateAntecedentSociauxUseCase {
  constructor(
    private readonly createAntecedentSociauxFummeurRepository: ICreateAntecedentSociauxFummeurRepository,
    private readonly createAntecedentSociauxAlcooliqueRepository: ICreateAntecedentSociauxAlcooliqueRepository
  ) {}

  async execute(
    data: Omit<Antecedant_sociaux, "id">[]
  ): Promise<Antecedant_sociaux[]> {
    const promises: Promise<Antecedant_sociaux>[] = data.map((d) => {
      if (d.nom === "Fummeur") {
        return this.createAntecedentSociauxFummeurRepository
          .execute({
            annee_a_fumer: d.frequence,
            confidentialite: d.confidentielite,
            decede: false,
            fumeur_actif: d.consommation,
            id_carnet: d.id_carnet,
            quantite_par_jour: d.quantite_consomer,
            remarques: d.remarques,
          })
          .then(
            (resFumeur: antecedant_sociaux_fumeur): Antecedant_sociaux => ({
              id: resFumeur.id,
              id_carnet: resFumeur.id_carnet,
              nom: "Fummeur",
              consommation: resFumeur.fumeur_actif,
              frequence: resFumeur.annee_a_fumer,
              quantite_consomer: resFumeur.quantite_par_jour,
              remarques: resFumeur.remarques,
              confidentielite: resFumeur.confidentialite,
            })
          );
      } else {
        return this.createAntecedentSociauxAlcooliqueRepository
          .execute({
            consommez_activement: d.consommation,
            confidentialite: d.confidentielite,
            frequence: d.frequence,
            id_carnet: d.id_carnet,
            nb_boisson_consomer: Number(d.quantite_consomer),
            remarques: d.remarques,
          })
          .then(
            (resAlcool: antecedant_sociaux_alcoolique): Antecedant_sociaux => ({
              id: resAlcool.id,
              id_carnet: resAlcool.id_carnet,
              nom: "Alcoolique",
              consommation: resAlcool.consommez_activement,
              frequence: resAlcool.frequence,
              quantite_consomer: resAlcool.nb_boisson_consomer.toString(),
              remarques: resAlcool.remarques,
              confidentielite: resAlcool.confidentialite,
            })
          );
      }
    });

    return Promise.all(promises);
  }
}
