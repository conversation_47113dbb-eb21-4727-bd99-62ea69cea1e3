import { Antecedant_sociaux } from "@/domain/models";
import {
  IGetAntecedentSociauxAlcooliqueRepository,
  IGetAntecedentSociauxFummeurRepository,
} from "@/domain/interfaces/repositories/antecedentSociaux";

export class GetAntecedentSociauxUseCase {
  constructor(
    private readonly getAntecedentSociauxFummeurRepository: IGetAntecedentSociauxFummeurRepository,
    private readonly getAntecedentSociauxAlcooliqueRepository: IGetAntecedentSociauxAlcooliqueRepository
  ) {}

  async execute(carnetId: number): Promise<Antecedant_sociaux[]> {
    const [resFumeur, resAlcoolique] = await Promise.all([
      this.getAntecedentSociauxFummeurRepository.execute(carnetId),
      this.getAntecedentSociauxAlcooliqueRepository.execute(carnetId),
    ]);

    const antecedents: Antecedant_sociaux[] = [];

    if (resFumeur) {
      antecedents.push({
        id: resFumeur.id,
        id_carnet: resFumeur.id_carnet,
        nom: "Fummeur",
        consommation: resFumeur.fumeur_actif,
        frequence: resFumeur.annee_a_fumer.toString(),
        quantite_consomer: resFumeur.quantite_par_jour.toString(),
        remarques: resFumeur.remarques,
        confidentielite: resFumeur.confidentialite,
      });
    }

    if (resAlcoolique) {
      antecedents.push({
        id: resAlcoolique.id,
        id_carnet: resAlcoolique.id_carnet,
        nom: "Alcoolique",
        consommation: resAlcoolique.consommez_activement,
        frequence: resAlcoolique.frequence,
        quantite_consomer: resAlcoolique.nb_boisson_consomer.toString(),
        remarques: resAlcoolique.remarques,
        confidentielite: resAlcoolique.confidentialite,
      });
    }

    return antecedents;
  }
}
