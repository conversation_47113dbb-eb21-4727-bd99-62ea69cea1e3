import {
  Antecedant_sociaux,
  antecedant_sociaux_alcoolique,
  antecedant_sociaux_fumeur,
} from "@/domain/models";
import {
  IUpdateAntecedentSociauxAlcooliqueRepository,
  IUpdateAntecedentSociauxFummeurRepository,
} from "@/domain/interfaces/repositories/antecedentSociaux";

export class UpdateAntecedentSociauxUseCase {
  constructor(
    private readonly updateAntecedentSociauxFummeurRepository: IUpdateAntecedentSociauxFummeurRepository,
    private readonly updateAntecedentSociauxAlcooliqueRepository: IUpdateAntecedentSociauxAlcooliqueRepository
  ) {}

  async execute(
    id: number,
    data: Partial<Antecedant_sociaux>
  ): Promise<Antecedant_sociaux> {
    console.log(data);

    if (data.nom === "Fummeur") {
      return await this.updateAntecedentSociauxFummeurRepository
        .execute(id, {
          annee_a_fumer: data.frequence,
          confidentialite: data.confidentielite,
          decede: false,
          fumeur_actif: data.consommation,
          id_carnet: data.id_carnet,
          quantite_par_jour: data.quantite_consomer,
          remarques: data.remarques,
        })
        .then(
          (resFumeur: antecedant_sociaux_fumeur): Antecedant_sociaux => ({
            id: resFumeur.id,
            id_carnet: resFumeur.id_carnet,
            nom: "Fummeur",
            consommation: resFumeur.fumeur_actif,
            frequence: resFumeur.annee_a_fumer,
            quantite_consomer: resFumeur.quantite_par_jour,
            remarques: resFumeur.remarques,
            confidentielite: resFumeur.confidentialite,
          })
        );
    } else {
      return await this.updateAntecedentSociauxAlcooliqueRepository
        .execute(id, {
          consommez_activement: data.consommation,
          confidentialite: data.confidentielite,
          frequence: data.frequence,
          id_carnet: data.id_carnet,
          nb_boisson_consomer: Number(data.quantite_consomer),
          remarques: data.remarques,
        })
        .then(
          (resAlcool: antecedant_sociaux_alcoolique): Antecedant_sociaux => ({
            id: resAlcool.id,
            id_carnet: resAlcool.id_carnet,
            nom: "Alcoolique",
            consommation: resAlcool.consommez_activement,
            frequence: resAlcool.frequence,
            quantite_consomer: resAlcool.nb_boisson_consomer.toString(),
            remarques: resAlcool.remarques,
            confidentielite: resAlcool.confidentialite,
          })
        );
    }
  }
}
