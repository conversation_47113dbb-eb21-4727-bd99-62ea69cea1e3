import { IRegisterCabinetMedical } from "@/domain/interfaces/usecases/cabinetMedical";
import { IRegisterProfessionalUsecase } from "@/domain/interfaces/usecases/IRegisterProfessionalUsecase";
import {
  professionnels_categories_enum,
  utilisateurs_role_enum,
} from "@/domain/models/enums";
import { Professionnel, Utilisateur } from "@/domain/models";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";
import { IMarkInvitationAsUsedUsecase } from "@/domain/interfaces/usecases/professionalInvitation";

class RegisterCabinetMedical implements IRegisterCabinetMedical {
  constructor(
    private readonly registerProfessionalUsecase: IRegisterProfessionalUsecase,
    private readonly markProfessionalInvitationAsUsedUsecase: IMarkInvitationAsUsedUsecase,
  ) {}

  async execute(
    formData: CabinetMedicalFormDTO,
    invitationId: number,
  ): Promise<{
    userData: Professionnel;
    success: boolean;
    message?: string;
  }> {
    try {
      // Étape 1: Enregistrer le professionnel
      const user: Omit<Utilisateur, "id"> = {
        email: formData.email,
        mot_de_passe_hash: formData.motDePasse,
        role: utilisateurs_role_enum.PROFESSIONNEL,
        bani: false,
      };

      const userData: Omit<Professionnel, "id" | "utilisateur_id"> = {
        titre: formData.titre,
        nom: formData.nom,
        prenom: formData.prenom,
        numero_ordre: formData.numero_ordre,
        types_consultation: formData.typeConsultation,
        informations_acces: formData.infoAcces,

        adresse: formData.adresse,
        geolocalisation: formData.geolocation,

        region: formData.region?.nom || "",
        district: formData.district?.libelle || "",
        commune: formData.commune?.nom || "",
        fokontany: formData.fokotany,

        nif: formData.nif,
        stat: formData.stat,

        presentation_generale: formData.presentation,
        raison_sociale: formData.raison_sociale,
        modes_paiement_acceptes: formData.paymentMethods
          .map((method) => method.name)
          .join(", "),
        nouveau_patient_acceptes: formData.nouveauPatientAcceptes,
        sexe: formData.sexe,
      };

      const professional = await this.registerProfessionalUsecase.execute(
        {
          user: user,
          userData: userData,

          // Relations cles etrangeres
          diplomas: formData?.diplomes,
          experiencies: formData?.experiences,
          insurances: formData?.insurances,
          specialities: formData?.specialities,
          ordreAppartenances: formData?.ordre_appartenance,
          publications: formData?.publications,
          searchKeywords: formData?.motCles,
          spokenLanguages: formData?.langues,

          cabinetImages: formData?.cabinetImages,
          profile_image: formData?.profileImageFile,

          // Données de l'établissement professionnel
          etablissement: {
            nom_etablissement: formData.nom_etablissement,
            nom_responsable: formData.nom_responsable,
            prenom_responsable: formData.prenom_responsable,
            equipe: formData.equipe,
          },
        },
        professionnels_categories_enum.CABINET_MEDICAL,
      );

      if (!professional.success || !professional.professionalData) {
        throw new Error(
          professional.error || "Échec de l'enregistrement du professionnel",
        );
      }

      await this.markProfessionalInvitationAsUsedUsecase.execute(invitationId);

      return {
        userData: professional.professionalData,
        success: true,
      };
    } catch (error) {
      console.error(
        "Erreur lors de l'enregistrement du cabinet médical:",
        error,
      );
      throw error;
    }
  }
}

export default RegisterCabinetMedical;
