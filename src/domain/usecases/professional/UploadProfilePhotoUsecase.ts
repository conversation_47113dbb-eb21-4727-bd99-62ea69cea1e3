import { IUploadProfilePhotoUsecase } from "@/domain/interfaces/usecases/professionals/IUploadProfilePhotoUsecase.ts";
import { ISupabaseUploader } from "@/domain/interfaces/services/ISupabaseUploader";
import { IAddPhotosUsecase } from "@/domain/interfaces/usecases/photos";
import { PhotoTypeEnum } from "@/domain/models/enums";
import { Photo } from "@/domain/models/Photo";

/**
 * Usecase pour l'upload de photo de profil professionnel
 *
 * @description
 * Ce usecase gère l'upload complet d'une photo de profil :
 * - Upload du fichier vers Supabase Storage (bucket 'profile')
 * - Enregistrement des métadonnées en base de données
 * - Gestion des erreurs et validation
 */
export class UploadProfilePhotoUsecase implements IUploadProfilePhotoUsecase {
  constructor(
    private readonly supabaseUploader: ISupabaseUploader,
    private readonly addPhotosUsecase: IAddPhotosUsecase
  ) {}

  /**
   * Exécute l'upload de la photo de profil
   *
   * @param userId - ID de l'utilisateur
   * @param file - Fichier image à uploader
   * @returns Résultat de l'upload avec la photo créée ou une erreur
   */
  async execute(
    userId: number,
    file: File
  ): Promise<{
    success: boolean;
    photo?: Photo;
    error?: string;
  }> {
    try {
      // Validation des paramètres
      if (!userId || userId <= 0) {
        return {
          success: false,
          error: "ID utilisateur invalide",
        };
      }

      if (!file) {
        return {
          success: false,
          error: "Aucun fichier fourni",
        };
      }

      // Validation du type de fichier
      if (!this.isValidImageFile(file)) {
        return {
          success: false,
          error: "Type de fichier non supporté. Utilisez JPG, PNG ou WebP.",
        };
      }

      // Validation de la taille du fichier (max 5MB)
      const maxSizeBytes = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSizeBytes) {
        return {
          success: false,
          error: `Fichier trop volumineux (${(file.size / 1024 / 1024).toFixed(2)}MB). Taille maximale : 5MB.`,
        };
      }

      // Définir le dossier pour les photos de profil
      const folderPath = `profile_${userId}`;

      // Upload du fichier vers Supabase Storage
      const { url: profileUrl, error: uploadError } =
        await this.supabaseUploader.uploadFile(
          PhotoTypeEnum.PROFILE,
          file,
          folderPath,
          {
            cacheControl: "3600",
            upsert: true,
          }
        );

      if (uploadError) {
        console.error("Erreur d'upload de la photo de profil:", uploadError);
        return {
          success: false,
          error: "Erreur lors de l'upload du fichier",
        };
      }

      if (!profileUrl) {
        return {
          success: false,
          error: "URL de la photo non générée",
        };
      }

      // Enregistrement des métadonnées en base de données
      const photoData = {
        path: profileUrl,
        type: PhotoTypeEnum.PROFILE,
        utilisateur_id: userId,
      };

      let photos: Photo[];
      try {
        photos = await this.addPhotosUsecase.execute(
          photoData,
          file,
          folderPath,
          PhotoTypeEnum.PROFILE
        );

        if (!photos || photos.length === 0) {
          throw new Error("Aucune photo retournée après l'enregistrement");
        }
      } catch (error) {
        console.error(
          "Erreur lors de l'enregistrement des métadonnées:",
          error
        );

        // Rollback: Supprimer le fichier uploadé si l'enregistrement échoue
        if (profileUrl) {
          try {
            const uploadedFileName = this.extractFileNameFromUrl(profileUrl);
            if (uploadedFileName) {
              await this.supabaseUploader.deleteFile?.(
                PhotoTypeEnum.PROFILE,
                `${folderPath}/${uploadedFileName}`
              );
            }
          } catch (rollbackError) {
            console.error(
              "Erreur lors du rollback du fichier uploadé:",
              rollbackError
            );
          }
        }

        return {
          success: false,
          error: "Erreur lors de l'enregistrement des métadonnées",
        };
      }

      return {
        success: true,
        photo: photos[0],
      };
    } catch (error) {
      console.error("Erreur lors de l'upload de la photo de profil:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Erreur inconnue",
      };
    }
  }

  /**
   * Valide si le fichier est une image supportée
   */
  private isValidImageFile(file: File): boolean {
    const supportedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/webp",
    ];
    return supportedTypes.includes(file.type);
  }

  /**
   * Extrait le nom de fichier depuis une URL Supabase Storage
   */
  private extractFileNameFromUrl(url: string): string | null {
    try {
      const urlParts = url.split("/");
      return urlParts[urlParts.length - 1] || null;
    } catch (error) {
      console.error("Erreur lors de l'extraction du nom de fichier:", error);
      return null;
    }
  }
}
