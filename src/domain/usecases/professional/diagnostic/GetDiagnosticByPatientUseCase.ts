
import { IGetDiagnosticByPatientUseCase } from "@/domain/interfaces/usecases/diagnostic/IGetDiagnosticByPatientUseCase";
import { IGetDiagnostiByPatientRepository } from "@/domain/interfaces/repositories/diagnostic/IGetDiagnostiByPatientRepository";
import { DiagnosticDTO } from "@/domain/DTOS/DiagnosticDTO";


class GetDiagnosticByPatientUseCase implements IGetDiagnosticByPatientUseCase {
    constructor(
        private readonly getDiagnosticByPatientRepository: IGetDiagnostiByPatientRepository
    ) { }

    async execute(patientId: number): Promise<DiagnosticDTO[]> {
        try {
            if (!patientId || patientId <= 0) {
                throw new Error("Invalid patient ID provided");
            }
            const diagnostics = await this.getDiagnosticByPatientRepository.execute(patientId);
            return diagnostics;
        } catch (error) {
            throw new Error(`Failed to get diagnostics for patient ${patientId}: ${error}`);
        }
    }
}

export default GetDiagnosticByPatientUseCase;


