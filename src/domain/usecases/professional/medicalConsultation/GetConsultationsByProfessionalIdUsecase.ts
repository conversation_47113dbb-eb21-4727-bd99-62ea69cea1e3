import { IGetMedicalConsultationsByProfessionalIdRepository } from "@/domain/interfaces/repositories/medicalConsultation";
import { IGetMedicalConsultationsByProfessionalIdUsecase } from "@/domain/interfaces/usecases/medicalConsultation";
import { consultation_medical } from "@/domain/models";

export class GetMedicalConsultationsByProfessionalIdUsecase
  implements IGetMedicalConsultationsByProfessionalIdUsecase
{
  constructor(
    private readonly getMedicalConsultationsByProfessionalIdRepository: IGetMedicalConsultationsByProfessionalIdRepository
  ) {}

  async execute(
    professional_id: number
  ): Promise<consultation_medical[] | null> {
    return await this.getMedicalConsultationsByProfessionalIdRepository.execute(
      professional_id
    );
  }
}
