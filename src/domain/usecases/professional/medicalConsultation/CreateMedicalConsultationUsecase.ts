import { ICreateMedicalConsultationRepository } from "@/domain/interfaces/repositories/medicalConsultation";
import { ICreateMedicalConsultationUsecase } from "@/domain/interfaces/usecases/medicalConsultation";
import { consultation_medical } from "@/domain/models";

export class CreateMedicalConsultationUsecase implements ICreateMedicalConsultationUsecase {
    constructor(
        private readonly createMedicalConsultationRepository: ICreateMedicalConsultationRepository,
    ) { }

    async execute(
        consultation: Omit<consultation_medical, 'id'>
    ): Promise<consultation_medical | null> {
        try {
            return await this.createMedicalConsultationRepository.execute(consultation);
        } catch (error) {
            return null;
        }
    }
}
