import { IGetMedicalConsultationsByPatientIdRepository } from "@/domain/interfaces/repositories/medicalConsultation";
import { IGetMedicalConsultationsByPatientIdUsecase } from "@/domain/interfaces/usecases/medicalConsultation";
import { consultation_medical } from "@/domain/models";

export class GetMedicalConsultationsByPatientIdUsecase implements IGetMedicalConsultationsByPatientIdUsecase {
    constructor(
        private readonly getMedicalConsultationsByPatientIdRepository: IGetMedicalConsultationsByPatientIdRepository,
    ) { }

    async execute(
        patient_id: number
    ): Promise<consultation_medical[] | null> {
        try {
            return await this.getMedicalConsultationsByPatientIdRepository.execute(patient_id);
        } catch (error) {
            return null;
        }
    }
}
