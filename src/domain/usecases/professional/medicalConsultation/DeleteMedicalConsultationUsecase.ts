import { IDeleteMedicalConsultationRepository } from "@/domain/interfaces/repositories/medicalConsultation";
import { IDeleteMedicalConsultationUsecase } from "@/domain/interfaces/usecases/medicalConsultation";
import { consultation_medical } from "@/domain/models";

export class DeleteMedicalConsultationUsecase implements IDeleteMedicalConsultationUsecase {
    constructor(
        private readonly deleteMedicalConsultationRepository: IDeleteMedicalConsultationRepository,
    ) { }

    async execute(
        id: number
    ): Promise<consultation_medical | null> {
        try {
            return await this.deleteMedicalConsultationRepository.execute(id);
        } catch (error) {
            return null;
        }
    }
}
