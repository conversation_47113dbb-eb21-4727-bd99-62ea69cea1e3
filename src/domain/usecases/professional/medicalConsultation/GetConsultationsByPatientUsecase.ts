import { ConsultationDetails } from "@/domain/DTOS/MedicalConsultationDetailsDTO";
import { IGetConsultationsByPatientRepository } from "@/domain/interfaces/repositories/medicalConsultation/IGetConsultationsByPatientRepository";
import { IGetConsultationsByPatientUsecase } from "@/domain/interfaces/usecases/medicalConsultation";


class GetConsultationsByPatientUsecase implements IGetConsultationsByPatientUsecase {
    constructor(
        private readonly getConsultationsByPatientRepository: IGetConsultationsByPatientRepository
    ) { }

    async execute(patientId: number): Promise<ConsultationDetails[]> {
        try {
            // Validate patient ID
            if (!patientId || patientId <= 0) {
                throw new Error("Invalid patient ID provided");
            }

            const consultations = await this.getConsultationsByPatientRepository.execute(patientId);

            // Data is already sorted by date in descending order from repository
            return consultations;
        } catch (error) {
            throw new Error(`Failed to get consultations for patient ${patientId}: ${error}`);
        }
    }
}

export default GetConsultationsByPatientUsecase;