import { IGetMedicalConsultationRepository } from "@/domain/interfaces/repositories/medicalConsultation";
import { consultation_medical } from "@/domain/models";
import { IGetMedicalConsultationUsecase } from "@/domain/interfaces/usecases/medicalConsultation";

export class GetMedicalConsultationUsecase implements IGetMedicalConsultationUsecase {
    constructor(
        private readonly getMedicalConsultationsRepository: IGetMedicalConsultationRepository,
    ) { }

    async execute(
        id: number
    ): Promise<consultation_medical | null> {
        try {
            return await this.getMedicalConsultationsRepository.execute(id);
        } catch (error) {
            return null;
        }
    }
}
