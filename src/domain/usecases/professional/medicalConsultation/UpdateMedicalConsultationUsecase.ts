import { IUpdateMedicalConsultationRepository } from "@/domain/interfaces/repositories/medicalConsultation";
import { IUpdateMedicalConsultationUsecase } from "@/domain/interfaces/usecases/medicalConsultation";
import { consultation_medical } from "@/domain/models";

export class UpdateMedicalConsultationUsecase implements IUpdateMedicalConsultationUsecase {
    constructor(
        private readonly updateMedicalConsultationRepository: IUpdateMedicalConsultationRepository,
    ) { }

    async execute(
        id: number,
        consultation: Partial<consultation_medical>
    ): Promise<consultation_medical | null> {
        try {
            return await this.updateMedicalConsultationRepository.execute(id, consultation);
        } catch (error) {
            return null;
        }
    }
}
