import { ICreateMedicamentRepository } from '@/domain/interfaces/repositories/medicament'
import { Medicament } from '@/domain/models'

export class CreateMedicamentUseCase {
  constructor(
    private readonly createMedicamentRepository: ICreateMedicamentRepository
  ) {}

  async execute(data: Omit<Medicament, "id">[]): Promise<Medicament[]> {
    return await this.createMedicamentRepository.execute(data)
  }
}
