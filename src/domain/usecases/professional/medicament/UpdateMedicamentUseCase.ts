import { Medicament } from '@/domain/models'
import { IUpdateMedicamentRepository } from '@/domain/interfaces/repositories/medicament'

export class UpdateMedicamentUseCase {
  constructor(
    private readonly updateMedicamentRepository: IUpdateMedicamentRepository
  ) {}

  async execute(id: number, data: Partial<Medicament>): Promise<Medicament> {
    return await this.updateMedicamentRepository.execute(id, data)
  }
}
