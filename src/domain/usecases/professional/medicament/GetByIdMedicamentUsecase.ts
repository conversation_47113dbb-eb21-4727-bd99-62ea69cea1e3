import { IGetMedicamentByIdRepository } from '@/domain/interfaces/repositories/medicament/IGetMedicamentRepository'
import { Medicament } from '@/domain/models'

export class GetByIdMedicamentUsecase {
  constructor(
    private readonly getMedicamentByIdRepository: IGetMedicamentByIdRepository
  ) {}

  async getById(id: number): Promise<Medicament | null> {
    return await this.getMedicamentByIdRepository.execute(id)
  }
}
