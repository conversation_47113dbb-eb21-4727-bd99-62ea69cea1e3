import { IGetAllMedicamentRepository } from '@/domain/interfaces/repositories/medicament'
import { Medicament } from '@/domain/models'

export class GetAllMedicamentUseCase {
  constructor(
    private readonly getAllMedicamentRepository: IGetAllMedicamentRepository
  ) {}
  
  async getAll(carnetId: number): Promise<Medicament[]> {
    return await this.getAllMedicamentRepository.execute(carnetId)
  }
}
