import { IGetFacturationsByProfessionalIdRepository } from "@/domain/interfaces/repositories/facturation";
import { FacturationDTO } from "@/domain/DTOS";

export class GetFacturationsByProfessionalIdUsecase {
  constructor(
    private readonly getFacturationsByProfessionalIdRepository: IGetFacturationsByProfessionalIdRepository
  ) {}

  async execute(professional_id: number): Promise<FacturationDTO[] | null> {
    // Recuperation des donnees brute dans le repository
    const data =
      await this.getFacturationsByProfessionalIdRepository.execute(
        professional_id
      );

    // Traitement des donnees
    if (!data) return [];
    const out: FacturationDTO[] = data.map((facturation) => {
      const patientData = facturation.patient.patients[0];
      const professionnelData = facturation.professionnel.professionnels[0];

      const row: FacturationDTO = {
        id: facturation.id,
        id_professionnel: facturation.id_professionnel,
        id_patient: facturation.id_patient,
        montant: facturation.montant,
        total_paye: facturation.total_paye,
        recu: facturation.recu,
        date_paiement: facturation.date_paiement,
        informations: facturation.informations,
        date_creation: facturation.date_creation,
        patient: {
          id: patientData.id,
          nom: patientData.nom,
          prenom: patientData.prenom,
        },
        professionnel: {
          id: professionnelData.id,
          nom: professionnelData.nom,
          prenom: professionnelData.prenom,
        },
      };
      return row;
    });

    return out;
  }
}
