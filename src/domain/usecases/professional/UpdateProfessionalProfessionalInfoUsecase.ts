import { IUpdateProfessionalProfessionalInfoUsecase } from "@/domain/interfaces/usecases/professionals/IUpdateProfessionalProfessionalInfoUsecase";
import { IUpdateProfessionalProfessionalInfoRepository } from "@/domain/interfaces/repositories/professionals";
import { Professionnel } from "@/domain/models";

/**
 * Use case pour la mise à jour des informations professionnelles
 * 
 * @description Ce use case orchestre la mise à jour des informations professionnelles
 * d'un professionnel (numéro d'ordre, raison sociale, NIF, STAT).
 * Il gère la logique métier, la validation des données et la coordination avec le repository.
 */
export class UpdateProfessionalProfessionalInfoUsecase implements IUpdateProfessionalProfessionalInfoUsecase {
  /**
   * Constructeur du use case
   * 
   * @param repository - Repository pour la mise à jour des informations professionnelles
   */
  constructor(
    private readonly repository: IUpdateProfessionalProfessionalInfoRepository
  ) {}

  /**
   * Met à jour les informations professionnelles du professionnel
   * 
   * @param professionalId - L'identifiant unique du professionnel
   * @param professionalData - Les données professionnelles à mettre à jour
   * @returns Promise<Professionnel | null> - Les données mises à jour ou null en cas d'erreur
   */
  async execute(
    professionalId: number,
    professionalData: {
      numero_ordre?: string;
      raison_sociale?: string;
      nif?: string;
      stat?: string;
    }
  ): Promise<Professionnel | null> {
    try {
      // Validation des données d'entrée
      if (!professionalId || professionalId <= 0) {
        throw new Error(
          "L'identifiant du professionnel doit être un nombre positif valide"
        );
      }

      // Vérifier qu'au moins un champ est fourni
      if (
        professionalData.numero_ordre === undefined &&
        professionalData.raison_sociale === undefined &&
        professionalData.nif === undefined &&
        professionalData.stat === undefined
      ) {
        throw new Error(
          "Au moins une donnée professionnelle doit être fournie pour la mise à jour"
        );
      }

      // Normalisation des données
      const normalizedProfessionalData = {
        ...(professionalData.numero_ordre !== undefined && { 
          numero_ordre: professionalData.numero_ordre.trim() 
        }),
        ...(professionalData.raison_sociale !== undefined && { 
          raison_sociale: professionalData.raison_sociale.trim() 
        }),
        ...(professionalData.nif !== undefined && { 
          nif: professionalData.nif.trim() 
        }),
        ...(professionalData.stat !== undefined && { 
          stat: professionalData.stat.trim() 
        }),
      };

      // Validation du numéro d'ordre si fourni
      if (normalizedProfessionalData.numero_ordre !== undefined) {
        if (normalizedProfessionalData.numero_ordre.length === 0) {
          throw new Error("Le numéro d'ordre ne peut pas être vide");
        }
        // Validation basique du format (peut être étendue selon les règles métier)
        if (normalizedProfessionalData.numero_ordre.length < 3) {
          throw new Error("Le numéro d'ordre doit contenir au moins 3 caractères");
        }
      }

      // Validation du NIF si fourni (exemple de règle métier)
      if (normalizedProfessionalData.nif !== undefined && normalizedProfessionalData.nif.length > 0) {
        const nifRegex = /^[0-9]{13}$/; // Exemple : 13 chiffres pour Madagascar
        if (!nifRegex.test(normalizedProfessionalData.nif)) {
          throw new Error("Le format du NIF est invalide (13 chiffres requis)");
        }
      }

      // Validation du STAT si fourni (exemple de règle métier)
      if (normalizedProfessionalData.stat !== undefined && normalizedProfessionalData.stat.length > 0) {
        const statRegex = /^[0-9]{11}$/; // Exemple : 11 chiffres pour Madagascar
        if (!statRegex.test(normalizedProfessionalData.stat)) {
          throw new Error("Le format du STAT est invalide (11 chiffres requis)");
        }
      }

      // Mise à jour via le repository
      const updatedProfessional = await this.repository.execute(
        professionalId,
        normalizedProfessionalData
      );

      return updatedProfessional;
    } catch (error) {
      console.error(
        "Erreur dans UpdateProfessionalProfessionalInfoUsecase:",
        error
      );
      return null;
    }
  }
}
