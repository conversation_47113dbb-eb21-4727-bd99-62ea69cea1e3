import { IUpdateProfessionalRepository } from "@/domain/interfaces/repositories/professionals";
import { IUpdateProfessionalUsecase } from "@/domain/interfaces/usecases/professionals";
import { Professionnel } from "@/domain/models";

class UpdateProfessionalUsecase implements IUpdateProfessionalUsecase {
  constructor(private professionalRepository: IUpdateProfessionalRepository) {}

  async execute(
    id: number,
    professional: Partial<Professionnel>,
  ): Promise<Professionnel> {
    return this.professionalRepository.execute(id, professional);
  }
}
export default UpdateProfessionalUsecase;
