import { ProfessionnelPatientDTO } from "@/domain/DTOS";
import { IGetProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";
import { IGetProfessionalPatientUsecase } from "@/domain/interfaces/usecases/professionnelPatient";

export class GetProfessionalPatientUsecase
  implements IGetProfessionalPatientUsecase
{
  constructor(
    private readonly getProfessionalPatientRepository: IGetProfessionalPatientRepository,
  ) {}

  async execute(
    professionalId: number,
  ): Promise<ProfessionnelPatientDTO[] | null> {
    try {
      return await this.getProfessionalPatientRepository.execute(
        professionalId,
      );
    } catch (error) {
      return null;
    }
  }
}
