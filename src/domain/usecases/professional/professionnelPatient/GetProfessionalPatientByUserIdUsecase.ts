import { ProfessionnelPatientDTO } from "@/domain/DTOS";
import { IGetProfessionalPatientByUserIdRepository } from "@/domain/interfaces/repositories/professionnelPatient";

class GetProfessionalPatientByUserIdUsecase {
  constructor(
    private readonly getProfessionalPatientByUserIdRepository: IGetProfessionalPatientByUserIdRepository
  ) {}

  async execute(userId: number) {
    try {
      const result =
        await this.getProfessionalPatientByUserIdRepository.execute(userId);

      if (
        !result ||
        !result.patients ||
        !result.patients[0].professionnel_patient
      ) {
        return null;
      }

      const { professionnel_patient, ...patient } = result.patients[0];

      const finalResult = {
        ...professionnel_patient,
        patient,
      };

      return finalResult as ProfessionnelPatientDTO;
    } catch (error) {
      console.log("error", error);
      throw Error("Une erreur s'est produite");
    }
  }
}

export default GetProfessionalPatientByUserIdUsecase;
