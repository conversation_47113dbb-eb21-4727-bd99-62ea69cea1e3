import { ProfessionnelPatientDTO } from "@/domain/DTOS";
import { IGetProfessionalPatientByIdRepository } from "@/domain/interfaces/repositories/professionnelPatient";
import { IGetUserByIdRepository } from "@/domain/interfaces/repositories/user";
import { IGetProfessionalPatientByIdUsecase } from "@/domain/interfaces/usecases/professionnelPatient";

export class GetProfessionalPatientByIdUsecase
  implements IGetProfessionalPatientByIdUsecase
{
  constructor(
    private readonly getProfessionnelPatientByIdRepository: IGetProfessionalPatientByIdRepository,
    private readonly getUserByIdRepository: IGetUserByIdRepository
  ) {}

  async execute(patientId: number): Promise<ProfessionnelPatientDTO | null> {
    try {
      const professionnelPatient =
        await this.getProfessionnelPatientByIdRepository.execute(patientId);
      const utilisateur = await this.getUserByIdRepository.execute(
        professionnelPatient.patient.utilisateur_id
      );
      return { ...professionnelPatient, user: utilisateur };
    } catch (error) {
      return null;
    }
  }
}
