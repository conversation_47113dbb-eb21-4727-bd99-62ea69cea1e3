import { ProfessionnelPatient } from "@/domain/models";
import { ICreateProfessionalPatientUsecase } from "@/domain/interfaces/usecases/professionnelPatient";
import { ICreateProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";

export class CreateProfessionalPatientUsecase
  implements ICreateProfessionalPatientUsecase {
  constructor(
    private readonly createProfessionnelPatientRepository:
      ICreateProfessionalPatientRepository,
  ) {}

  async execute(
    data: Omit<ProfessionnelPatient, "id">,
  ): Promise<ProfessionnelPatient> {
    try {
      return this.createProfessionnelPatientRepository.execute(data);
    } catch (error) {
      throw error instanceof Error ? error.message : error;
    }
  }
}
