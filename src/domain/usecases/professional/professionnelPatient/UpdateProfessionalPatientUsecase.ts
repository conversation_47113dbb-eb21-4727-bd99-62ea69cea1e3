import { IUpdateProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";
import { IUpdateProfessionalPatientUsecase } from "@/domain/interfaces/usecases/professionnelPatient";
import { ProfessionnelPatient } from "@/domain/models";

export class UpdateProfessionalPatientUsecase
  implements IUpdateProfessionalPatientUsecase
{
  constructor(
    private readonly updateProfessionelPatientRepository: IUpdateProfessionalPatientRepository,
  ) {}

  async execute(
    id: number,
    professionelPatient: Partial<ProfessionnelPatient>,
  ): Promise<ProfessionnelPatient | null> {
    try {
      return await this.updateProfessionelPatientRepository.execute(
        id,
        professionelPatient,
      );
    } catch (error) {
      return null;
    }
  }
}
