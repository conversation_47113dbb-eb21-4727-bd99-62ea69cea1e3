import { IDeleteProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";
import { IDeleteProfessionalPatientUsecase } from "@/domain/interfaces/usecases/professionnelPatient";
import { ProfessionnelPatient } from "@/domain/models";

export class DeleteProfessionalPatientUsecase
  implements IDeleteProfessionalPatientUsecase
{
  constructor(
    private readonly deleteProfessionelPatientRepository: IDeleteProfessionalPatientRepository,
  ) {}

  async execute(id: number): Promise<ProfessionnelPatient | null> {
    try {
      return await this.deleteProfessionelPatientRepository.execute(id);
    } catch (error) {
      return null;
    }
  }
}
