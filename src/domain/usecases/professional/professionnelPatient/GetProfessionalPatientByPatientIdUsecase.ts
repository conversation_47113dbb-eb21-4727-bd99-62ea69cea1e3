import { ProfessionnelPatientDTO } from "@/domain/DTOS";
import { IGetProfessionalPatientByPatientIdRepository } from "@/domain/interfaces/repositories/professionnelPatient";
import { IGetProfessionalPatientByPatientIdUsecase } from "@/domain/interfaces/usecases/professionnelPatient";

class GetProfessionalPatientByPatientIdUsecase
  implements IGetProfessionalPatientByPatientIdUsecase
{
  constructor(
    private readonly getProfessionalPatientByPatientIdRepository: IGetProfessionalPatientByPatientIdRepository
  ) {}

  async execute(patientId: number): Promise<ProfessionnelPatientDTO | null> {
    const data =
      await this.getProfessionalPatientByPatientIdRepository.execute(patientId);

    return data;
  }
}

export default GetProfessionalPatientByPatientIdUsecase;
