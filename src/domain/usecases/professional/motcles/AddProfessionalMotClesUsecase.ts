import { ICreateProfessionalMotClesRepository } from "@/domain/interfaces/repositories/motcles/ICreateProfessionalMotClesRepository.ts";
import { MotClesProfessionnel } from "@/domain/models/MotClesProfessionnel.ts";

class AddProfessionalMotClesUsecase {
  constructor(
    private readonly addProfessionalMotClesRepository: ICreateProfessionalMotClesRepository
  ) {}

  async execute(motcles: Omit<MotClesProfessionnel, "id">[]) {
    try {
      return await this.addProfessionalMotClesRepository.execute(motcles);
    } catch (error) {
      console.error(
        "Erreur lors de l'ajout des mots-clés professionnelles :",
        error
      );
      throw new Error("Erreur lors de l'ajout des mots-clés professionnelles");
    }
  }
}

export default AddProfessionalMotClesUsecase;
