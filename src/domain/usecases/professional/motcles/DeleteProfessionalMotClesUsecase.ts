import { IDeleteProfessionalMotClesRepository } from "@/domain/interfaces/repositories/motcles";

class DeleteProfessionalMotClesUsecase
  implements IDeleteProfessionalMotClesRepository
{
  constructor(
    private readonly deleteProfessionalMotClesRepository: IDeleteProfessionalMotClesRepository
  ) {}

  async execute(id: number) {
    return await this.deleteProfessionalMotClesRepository.execute(id);
  }
}

export default DeleteProfessionalMotClesUsecase;
