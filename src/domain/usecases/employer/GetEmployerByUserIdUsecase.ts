import { IGetEmployerByUserIdRepository } from "@/domain/interfaces/repositories/employer/IGetEmployerByUserIdRepository";
import { Employer } from "@/domain/models/Employer";
import { IGetEmployerByUserIdUsecase } from "@/domain/interfaces/usecases/employer";

export class GetEmployerByUserIdUsecase implements IGetEmployerByUserIdUsecase {
  constructor(
    private readonly getEmployerByUserIdRepository: IGetEmployerByUserIdRepository
  ) {}

  async execute(id_utilisateur: number): Promise<Employer> {
    return this.getEmployerByUserIdRepository.execute(id_utilisateur);
  }
}
