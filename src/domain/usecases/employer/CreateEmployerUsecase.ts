import { ICreateEmployerRepository } from "@/domain/interfaces/repositories/employer/ICreateEmployerRepository";
import { Employer } from "@/domain/models/Employer";
import { ICreateEmployerUsecase } from "@/domain/interfaces/usecases/employer/ICreateEmployerUsecase";
import { ICreateUserUsecase } from "@/domain/interfaces/usecases/user";
import { Utilisateur } from "@/domain/models";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { IUploadProfilePhotoUsecase } from "@/domain/interfaces/usecases/professionals";
import { Photo } from "@/domain/models/Photo";

export class CreateEmployerUsecase implements ICreateEmployerUsecase {
  constructor(
    private readonly createEmployerRepository: ICreateEmployerRepository,
    private readonly createUserUsecase: ICreateUserUsecase,
    private readonly uploadProfilePhotoUsecase: IUploadProfilePhotoUsecase
  ) {}

  async execute(
    employers: Omit<Employer, "id" | "id_utilisateur">,
    photo?: File
  ): Promise<Employer> {
    const dataUser: Omit<Utilisateur, "id" | "cree_a" | "mis_a_jour_a"> = {
      role: utilisateurs_role_enum.EMPLOYER,
    };
    const user = await this.createUserUsecase.execute(dataUser);
    if (user && user.id) {
      let result: {
        success: boolean;
        photo?: Photo;
        error?: string;
      } = null;
      if (photo) {
        result = await this.uploadProfilePhotoUsecase.execute(user.id, photo);
      }
      return await this.createEmployerRepository.execute({
        ...employers,
        id_utilisateur: user.id,
        photo: result?.photo?.path || null,
      });
    } else {
      throw new Error("erreur lors de la création de l'utilisateur");
    }
  }
}
