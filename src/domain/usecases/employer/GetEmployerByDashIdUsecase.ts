import { IGetEmployerByDashIdRepository } from "@/domain/interfaces/repositories/employer";
import { IGetEmployerByDashIdUsecase } from "@/domain/interfaces/usecases/employer";
import { Employer } from "@/domain/models";
import { PostgrestError } from "@supabase/supabase-js";

class GetEmployersByDashIdUsecase implements IGetEmployerByDashIdUsecase {
  constructor(
    private readonly getEmployerByDashIdRepository: IGetEmployerByDashIdRepository
  ) {}

  async execute(dashId: number): Promise<Employer[]> {
    try {
      const data = await this.getEmployerByDashIdRepository.execute(dashId);
      return data;
    } catch (error) {
      // Traitement des cas specifique ici
      throw error as PostgrestError;
    }
  }
}

export default GetEmployersByDashIdUsecase;
