
import { ICreateMultipleEmployerRepository } from "@/domain/interfaces/repositories/employer/ICreateMultipleEmployerRepository";
import { Employer } from "@/domain/models/Employer";
import { ICreateMultipleEmployerUsecase } from "@/domain/interfaces/usecases/employer/ICreateMultipleEmployerUsecase";
import { ICreateUserUsecase } from "@/domain/interfaces/usecases/user";
import { Utilisateur } from "@/domain/models";
import { utilisateurs_role_enum } from "@/domain/models/enums";

export class CreateMultipleEmployerUsecase implements ICreateMultipleEmployerUsecase {
    constructor(
        private readonly createMultipleEmployerRepository: ICreateMultipleEmployerRepository,
        private readonly createUserUsecase: ICreateUserUsecase
    ) { }

    async execute(
        employers: Omit<Employer, "id" | "id_utilisateur">[]
    ): Promise<Employer[]> {
        // Option 1: Créer un utilisateur pour chaque employeur
        const employersWithUserId: (Omit<Employer, "id">)[] = [];

        for (const employer of employers) {
            const dataUser: Omit<Utilisateur, "id" | "cree_a" | "mis_a_jour_a"> = {
                role: utilisateurs_role_enum.EMPLOYER,
            };

            const user = await this.createUserUsecase.execute(dataUser);

            if (!user?.id) {
                throw new Error(`Erreur lors de la création de l'utilisateur pour l'employeur`);
            }

            employersWithUserId.push({
                ...employer,
                id_utilisateur: user.id,
            });
        }

        return await this.createMultipleEmployerRepository.execute(employersWithUserId);
    }
}
