import { IUpdateEmployerRepository } from "@/domain/interfaces/repositories/employer/IUpdateEmployerRepository";
import { Employer } from "@/domain/models/Employer";
import { IUpdateEmployerUsecase } from "@/domain/interfaces/usecases/employer/IUpdateEmployerUsecase";
import {
  IDeletePhotosUsecase,
  IGetPhotoByPathUsecase,
} from "@/domain/interfaces/usecases/photos";
import { IUploadProfilePhotoUsecase } from "@/domain/interfaces/usecases/professionals";

export class UpdateEmployerUsecase implements IUpdateEmployerUsecase {
  constructor(
    private readonly updateEmployerRepository: IUpdateEmployerRepository,
    private readonly deletePhotoUsecase: IDeletePhotosUsecase,
    private readonly uploadProfilePhotoUsecase: IUploadProfilePhotoUsecase,
    private readonly getPhotoByPathUsecase: IGetPhotoByPathUsecase
  ) {}

  async execute(
    id: number,
    employerData: Partial<Employer>,
    profilePhoto: File | null,
    lastPath: string
  ): Promise<Employer> {
    if (profilePhoto) {
      if (lastPath && lastPath !== "") {
        const photo = await this.getPhotoByPathUsecase.execute(lastPath);
        if (photo.id) {
          await this.deletePhotoUsecase.execute(photo.id);
        }
      }
      console.log(employerData.id_utilisateur, profilePhoto);

      const uploadedPhoto = await this.uploadProfilePhotoUsecase.execute(
        employerData.id_utilisateur,
        profilePhoto
      );
      console.log(uploadedPhoto);

      employerData.photo = uploadedPhoto.photo.path;
    }
    return this.updateEmployerRepository.execute(id, employerData);
  }
}
