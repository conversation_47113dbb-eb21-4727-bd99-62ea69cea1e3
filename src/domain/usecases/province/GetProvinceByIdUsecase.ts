import { IGetProvinceByIdUsecase } from "@/domain/interfaces/usecases/province";
import { IGetProvinceByIdRepository } from "@/domain/interfaces/repositories/province";
import { Province } from "@/domain/models";

class GetProvinceByIdUsecase implements IGetProvinceByIdUsecase {
  constructor(
    private readonly provinceRepository: IGetProvinceByIdRepository
  ) {}
  execute(id: number): Promise<Province> {
    return this.provinceRepository.execute(id);
  }
}

export default GetProvinceByIdUsecase;
