import { IGetProvincesUsecase } from "@/domain/interfaces/usecases/province";
import { IGetProvincesRepository } from "@/domain/interfaces/repositories/province";
import { Province } from "@/domain/models";

class GetProvincesUsecase implements IGetProvincesUsecase {
  constructor(private readonly provinceRepository: IGetProvincesRepository) {}
  execute(): Promise<Province[]> {
    return this.provinceRepository.execute();
  }
}

export default GetProvincesUsecase;
