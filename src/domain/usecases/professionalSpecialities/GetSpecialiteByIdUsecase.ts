import { IGetSpecialiteByIdRepository } from "@/domain/interfaces/repositories/professionalSpecialities";
import { IGetSpecialiteByIdUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";

class GetSpecialiteByIdUsecase implements IGetSpecialiteByIdUsecase {
  constructor(
    private readonly getSpecialiteByIdRepository: IGetSpecialiteByIdRepository
  ) {}

  async execute(id: number) {
    return await this.getSpecialiteByIdRepository.execute(id);
  }
}

export default GetSpecialiteByIdUsecase;
