import { IGetSpecialitiesRepository } from "@/domain/interfaces/repositories/professionalSpecialities";
import { IGetSpecialitiesUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";

class GetSpecialitiesUsecase implements IGetSpecialitiesUsecase {
  constructor(
    private readonly getSpecialitiesRepository: IGetSpecialitiesRepository
  ) {}

  async execute() {
    return await this.getSpecialitiesRepository.execute();
  }
}

export default GetSpecialitiesUsecase;
