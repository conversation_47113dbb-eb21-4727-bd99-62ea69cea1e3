import { ICreateSpecialiteRepository } from "@/domain/interfaces/repositories/professionalSpecialities";
import { ICreateSpecialiteUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";
import { SpecialiteProfessionnel } from "@/domain/models";

class CreateSpecialiteUsecase implements ICreateSpecialiteUsecase {
  constructor(
    private readonly createSpecialiteRepository: ICreateSpecialiteRepository
  ) { }

  async execute(specialiteInformations: Omit<SpecialiteProfessionnel, "id">[]) {
    return await this.createSpecialiteRepository.execute(specialiteInformations);
  }
}

export default CreateSpecialiteUsecase;
