import { IGetProfessionalSpecialitiesRepository } from "@/domain/interfaces/repositories/professionalSpecialities";
import { IGetProfessionalSpecialitiesUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";

class GetProfessionalSpecialitiesUsecase
  implements IGetProfessionalSpecialitiesUsecase
{
  constructor(
    private readonly getProfessionalSpecialitiesRepository: IGetProfessionalSpecialitiesRepository
  ) {}
  async execute(professionalId: number) {
    return await this.getProfessionalSpecialitiesRepository.execute(
      professionalId
    );
  }
}

export default GetProfessionalSpecialitiesUsecase;
