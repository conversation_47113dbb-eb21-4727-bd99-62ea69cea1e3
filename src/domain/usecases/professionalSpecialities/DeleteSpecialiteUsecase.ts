import { IDeleteSpecialiteRepository } from "@/domain/interfaces/repositories/professionalSpecialities";
import { IDeleteSpecialiteUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";

class DeleteSpecialiteUsecase implements IDeleteSpecialiteUsecase {
  constructor(
    private readonly deleteSpecialiteRepository: IDeleteSpecialiteRepository
  ) {}

  async execute(id: number) {
    return await this.deleteSpecialiteRepository.execute(id);
  }
}

export default DeleteSpecialiteUsecase;
