import { IGetSpecialitesByCategorieRepository } from "@/domain/interfaces/repositories/professionalSpecialities";
import { IGetSpecialitesByCategorieUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";

class GetSpecialitesByCategorieUsecase implements IGetSpecialitesByCategorieUsecase {
  constructor(
    private readonly getSpecialitesByCategorieRepository: IGetSpecialitesByCategorieRepository
  ) {}

  async execute(categorieId: number) {
    return await this.getSpecialitesByCategorieRepository.execute(categorieId);
  }
}

export default GetSpecialitesByCategorieUsecase;
