import { IUpdateSpecialiteRepository } from "@/domain/interfaces/repositories/professionalSpecialities";
import { IUpdateSpecialiteUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";
import { SpecialiteProfessionnel } from "@/domain/models";

class UpdateSpecialiteUsecase implements IUpdateSpecialiteUsecase {
  constructor(
    private readonly updateSpecialiteRepository: IUpdateSpecialiteRepository
  ) {}

  async execute(id: number, specialiteData: Partial<SpecialiteProfessionnel>) {
    return await this.updateSpecialiteRepository.execute(id, specialiteData);
  }
}

export default UpdateSpecialiteUsecase;
