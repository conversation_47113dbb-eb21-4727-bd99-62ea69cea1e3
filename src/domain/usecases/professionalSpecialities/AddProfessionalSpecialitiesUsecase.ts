import { IAddProfessionalSpecilitiesRepository } from "@/domain/interfaces/repositories/professionalSpecialities";
import { IAddProfessionalSpecilitiesUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";
import { SpecialiteProfessionnel } from "@/domain/models";

class AddProfessionalSpecilitiesUsecase
  implements IAddProfessionalSpecilitiesUsecase {
  constructor(
    private readonly addProfessionalSpecilitiesRepository: IAddProfessionalSpecilitiesRepository
  ) { }

  async execute(
    newSpecialities:
      | Omit<SpecialiteProfessionnel, "id">[]
  ) {
    try {
      const result =
        await this.addProfessionalSpecilitiesRepository.execute(
          newSpecialities
        );
      return result;
    } catch (error) {
      console.error("Erreur lors de l'ajout des spécialités professionnelles :", error);
      throw new Error("Erreur lors de l'ajout des spécialités professionnelles");
    }
  }
}

export default AddProfessionalSpecilitiesUsecase;
