import { IGetStocksDataRepository } from "@/domain/interfaces/repositories/stocks/IGetStocksDataRepository.ts";
import { IGetStocksDataUsecase } from "@/domain/interfaces/usecases/stocks/IGetStocksDataUsecase.ts";
import { StockMapper } from "@/domain/mappers/StockMapper.ts";

class GetStocksDataUsecase implements IGetStocksDataUsecase {
  constructor(
    private readonly getStocksDataRepository: IGetStocksDataRepository
  ) {}

  async execute(utilisateurId: number) {
    const data = await this.getStocksDataRepository.execute(utilisateurId);

    return StockMapper.toStockDTOList(data);
  }
}

export default GetStocksDataUsecase;
