import { Stocks } from "@/domain/models/Stocks";
import { IUpdateStocksRepository } from "@/domain/interfaces/repositories/stocks/IUpdateStocksRepository";
import { IGetStockByIdRepository } from "@/domain/interfaces/repositories/stocks/IGetStockByIdRepository";
import { IUpdateStocksUsecase } from "@/domain/interfaces/usecases/stocks/IUpdateStocksUsecase";

class UpdateStocksUsecase implements IUpdateStocksUsecase {
  constructor(
    private readonly updateStocksRepository: IUpdateStocksRepository,
    private readonly getStockByIdRepository: IGetStockByIdRepository
  ) {}

  async execute(stockId: number, stockData: Partial<Omit<Stocks, "id">>): Promise<Stocks | null> {
    // Validation des paramètres d'entrée
    if (!stockId || stockId <= 0) {
      throw new Error("ID stock invalide : doit être un nombre positif");
    }

    if (!stockData || Object.keys(stockData).length === 0) {
      throw new Error("Aucune donnée à mettre à jour fournie");
    }

    // Validation des données métier
    this.validateStockData(stockData);

    try {
      // Vérifier que le stock existe
      const existingStock = await this.getStockByIdRepository.execute(stockId);
      if (!existingStock) {
        throw new Error(`Stock avec l'ID ${stockId} non trouvé`);
      }

      // Appliquer la logique métier avant la mise à jour
      const processedStockData = this.processStockData(stockData, existingStock);

      // Effectuer la mise à jour
      const updatedStock = await this.updateStocksRepository.execute(stockId, processedStockData);

      if (!updatedStock) {
        throw new Error("Échec de la mise à jour du stock");
      }

      // Vérifier les règles métier après mise à jour
      this.validatePostUpdateRules(updatedStock);

      return updatedStock;
    } catch (error) {
      throw new Error(`Erreur lors de la mise à jour du stock : ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  /**
   * Valide les données de stock selon les règles métier
   */
  private validateStockData(stockData: Partial<Omit<Stocks, "id">>): void {
    // Validation de la quantité de stock
    if (stockData.stock_actuel !== undefined) {
      if (stockData.stock_actuel < 0) {
        throw new Error("La quantité de stock ne peut pas être négative");
      }
    }

    // Validation du seuil d'alerte
    if (stockData.seuil_alerte !== undefined) {
      if (stockData.seuil_alerte < 0) {
        throw new Error("Le seuil d'alerte ne peut pas être négatif");
      }
    }

    // Validation du nom
    if (stockData.nom !== undefined) {
      if (!stockData.nom || stockData.nom.trim().length === 0) {
        throw new Error("Le nom du stock ne peut pas être vide");
      }
      if (stockData.nom.length > 255) {
        throw new Error("Le nom du stock ne peut pas dépasser 255 caractères");
      }
    }

    // Validation de la date d'expiration
    if (stockData.date_expiration !== undefined && stockData.date_expiration) {
      const expirationDate = new Date(stockData.date_expiration);
      const today = new Date();
      
      if (isNaN(expirationDate.getTime())) {
        throw new Error("Format de date d'expiration invalide");
      }
      
      // Avertissement si la date d'expiration est dans le passé
      if (expirationDate < today) {
        console.warn("Attention : La date d'expiration est dans le passé");
      }
    }

    // Validation de la catégorie
    if (stockData.categorie_id !== undefined) {
      if (stockData.categorie_id <= 0) {
        throw new Error("L'ID de catégorie doit être un nombre positif");
      }
    }
  }

  /**
   * Traite les données de stock avant la mise à jour
   */
  private processStockData(
    stockData: Partial<Omit<Stocks, "id">>, 
    existingStock: Stocks
  ): Partial<Omit<Stocks, "id">> {
    const processedData = { ...stockData };

    // Normaliser le nom (trim et capitalisation)
    if (processedData.nom) {
      processedData.nom = processedData.nom.trim();
      processedData.nom = processedData.nom.charAt(0).toUpperCase() + processedData.nom.slice(1).toLowerCase();
    }

    // Ajuster automatiquement le seuil d'alerte si nécessaire
    if (processedData.stock_actuel !== undefined && !processedData.seuil_alerte) {
      // Si le stock actuel est mis à jour mais pas le seuil, suggérer un seuil
      const suggestedThreshold = Math.max(1, Math.floor(processedData.stock_actuel * 0.2));
      if (existingStock.seuil_alerte === undefined || existingStock.seuil_alerte === 0) {
        processedData.seuil_alerte = suggestedThreshold;
      }
    }

    // Valider la cohérence entre stock actuel et seuil d'alerte
    const finalStockActuel = processedData.stock_actuel ?? existingStock.stock_actuel ?? 0;
    const finalSeuilAlerte = processedData.seuil_alerte ?? existingStock.seuil_alerte ?? 0;

    if (finalSeuilAlerte > finalStockActuel && finalStockActuel > 0) {
      console.warn("Le seuil d'alerte est supérieur au stock actuel");
    }

    return processedData;
  }

  /**
   * Valide les règles métier après la mise à jour
   */
  private validatePostUpdateRules(updatedStock: Stocks): void {
    const stockActuel = updatedStock.stock_actuel || 0;
    const seuilAlerte = updatedStock.seuil_alerte || 0;

    // Vérifier les alertes de stock
    if (stockActuel === 0) {
      console.warn(`ALERTE : Stock en rupture pour ${updatedStock.nom}`);
    } else if (stockActuel <= seuilAlerte) {
      console.warn(`ALERTE : Stock faible pour ${updatedStock.nom} (${stockActuel} unités restantes)`);
    }

    // Vérifier la date d'expiration
    if (updatedStock.date_expiration) {
      const expirationDate = new Date(updatedStock.date_expiration);
      const today = new Date();
      const daysUntilExpiration = Math.ceil((expirationDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

      if (daysUntilExpiration <= 7 && daysUntilExpiration > 0) {
        console.warn(`ALERTE : ${updatedStock.nom} expire dans ${daysUntilExpiration} jour(s)`);
      } else if (daysUntilExpiration <= 0) {
        console.warn(`ALERTE : ${updatedStock.nom} a expiré`);
      }
    }
  }
}

export default UpdateStocksUsecase;
