import { Stocks } from "@/domain/models/Stocks";
import { IGetStocksRepository } from "@/domain/interfaces/repositories/stocks/IGetStocksRepository";
import { IGetStocksUsecase } from "@/domain/interfaces/usecases/stocks/IGetStocksUsecase";

class GetStocksUsecase implements IGetStocksUsecase {
  constructor(private readonly getStocksRepository: IGetStocksRepository) {}

  async execute(userId: number): Promise<Stocks[]> {
    // Validation des paramètres d'entrée
    if (!userId || userId <= 0) {
      throw new Error("ID utilisateur invalide : doit être un nombre positif");
    }

    try {
      // Récupérer tous les stocks de l'utilisateur
      const stocks = await this.getStocksRepository.execute(userId);

      return stocks;
    } catch (error) {
      throw new Error(
        `Erreur lors de la récupération des stocks : ${error instanceof Error ? error.message : "Erreur inconnue"}`
      );
    }
  }
}

export default GetStocksUsecase;
