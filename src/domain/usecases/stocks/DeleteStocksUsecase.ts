import { IDeleteStocksRepository } from "@/domain/interfaces/repositories/stocks/IDeleteStocksRepository";
import { IGetStockByIdRepository } from "@/domain/interfaces/repositories/stocks/IGetStockByIdRepository";
import { IDeleteStocksUsecase } from "@/domain/interfaces/usecases/stocks/IDeleteStocksUsecase";

class DeleteStocksUsecase implements IDeleteStocksUsecase {
  constructor(
    private readonly deleteStocksRepository: IDeleteStocksRepository,
    private readonly getStockByIdRepository: IGetStockByIdRepository
  ) {}

  async execute(stockId: number): Promise<boolean> {
    // Validation des paramètres d'entrée
    if (!stockId || stockId <= 0) {
      throw new Error("ID stock invalide : doit être un nombre positif");
    }

    try {
      // Vérifier que le stock existe avant suppression
      const existingStock = await this.getStockByIdRepository.execute(stockId);
      if (!existingStock) {
        throw new Error(`Stock avec l'ID ${stockId} non trouvé`);
      }

      // Appliquer les règles métier avant suppression
      this.validateDeletionRules(existingStock);

      // Effectuer la suppression
      const isDeleted = await this.deleteStocksRepository.execute(stockId);

      if (!isDeleted) {
        throw new Error("Échec de la suppression du stock");
      }

      // Log de l'action pour audit
      console.info(`Stock supprimé avec succès : ${existingStock.nom} (ID: ${stockId})`);

      return true;
    } catch (error) {
      throw new Error(`Erreur lors de la suppression du stock : ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  /**
   * Valide les règles métier avant la suppression
   */
  private validateDeletionRules(stock: any): void {
    const stockActuel = stock.stock_actuel || 0;

    // Règle métier : Avertir si on supprime un stock avec des quantités restantes
    if (stockActuel > 0) {
      console.warn(
        `Attention : Suppression d'un stock avec ${stockActuel} unités restantes pour ${stock.nom}. ` +
        `Assurez-vous que cette action est intentionnelle.`
      );
    }

    // Règle métier : Vérifier si le stock n'est pas critique pour l'activité
    const seuilAlerte = stock.seuil_alerte || 0;
    if (stockActuel > 0 && stockActuel <= seuilAlerte) {
      console.warn(
        `Attention : Suppression d'un stock en alerte (${stock.nom}). ` +
        `Cela pourrait affecter la continuité de l'activité.`
      );
    }

    // Règle métier : Vérifier la date d'expiration
    if (stock.date_expiration) {
      const expirationDate = new Date(stock.date_expiration);
      const today = new Date();
      const daysUntilExpiration = Math.ceil((expirationDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

      if (daysUntilExpiration > 30 && stockActuel > 0) {
        console.warn(
          `Attention : Suppression d'un stock non expiré (${stock.nom}) avec ${daysUntilExpiration} jours restants. ` +
          `Considérez une redistribution plutôt qu'une suppression.`
        );
      }
    }

    // Règle métier : Vérifier les dépendances potentielles
    // Note: Dans un système plus complexe, on vérifierait ici les références
    // dans d'autres tables (commandes en cours, prescriptions, etc.)
    this.checkDependencies(stock);
  }

  /**
   * Vérifie les dépendances potentielles avant suppression
   */
  private checkDependencies(stock: any): void {
    // Dans un système complet, cette méthode vérifierait :
    // - Les entrées de stock liées
    // - Les sorties de stock liées
    // - Les commandes en cours
    // - Les prescriptions actives
    // - Les réservations

    // Pour l'instant, on fait juste un log informatif
    console.info(
      `Vérification des dépendances pour le stock ${stock.nom} (ID: ${stock.id}). ` +
      `Note: Dans un système complet, cette vérification inclurait les entrées/sorties liées.`
    );

    // Exemple de règle métier : Empêcher la suppression si des mouvements récents
    const creationDate = new Date(stock.cree_le);
    const today = new Date();
    const daysSinceCreation = Math.ceil((today.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24));

    if (daysSinceCreation <= 1) {
      console.warn(
        `Attention : Suppression d'un stock créé récemment (${stock.nom}). ` +
        `Vérifiez qu'il n'y a pas d'erreur de saisie.`
      );
    }
  }
}

export default DeleteStocksUsecase;
