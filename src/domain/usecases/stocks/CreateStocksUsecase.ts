import { ICreateStocksRepository } from "@/domain/interfaces/repositories/stocks/ICreateStocksRepository.ts";
import { Stocks } from "@/domain/models/Stocks.ts";
import { PROFESSIONAL_STOCKS_TABLE_NAME } from "@/infrastructure/repositories/stocks/constants.ts";
import { supabase } from "@/infrastructure/supabase/supabase.ts";

class CreateStocksUsecase {
  constructor(
    private readonly createStocksRepository: ICreateStocksRepository
  ) {}

  async execute(stocks: Omit<Stocks, "id">[]) {
    return await this.createStocksRepository.execute(stocks);
  }
}

export default CreateStocksUsecase;
