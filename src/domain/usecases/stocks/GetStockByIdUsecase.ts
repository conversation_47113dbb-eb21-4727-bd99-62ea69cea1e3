import { Stocks } from "@/domain/models/Stocks";
import { IGetStockByIdRepository } from "@/domain/interfaces/repositories/stocks/IGetStockByIdRepository";
import { IGetStockByIdUsecase } from "@/domain/interfaces/usecases/stocks/IGetStockByIdUsecase";

class GetStockByIdUsecase implements IGetStockByIdUsecase {
  constructor(
    private readonly getStockByIdRepository: IGetStockByIdRepository
  ) {}

  async execute(stockId: number): Promise<Stocks | null> {
    // Validation des paramètres d'entrée
    if (!stockId || stockId <= 0) {
      throw new Error("ID stock invalide : doit être un nombre positif");
    }

    try {
      // Récupérer le stock par son ID
      const stock = await this.getStockByIdRepository.execute(stockId);

      if (!stock) {
        return null;
      }

      // Appliquer la logique métier pour enrichir les données
      const enrichedStock = {
        ...stock,
        // Calculer le statut du stock basé sur le seuil d'alerte
        statut_stock: this.calculateStockStatus(stock),
        // Calculer les jours jusqu'à expiration
        jours_avant_expiration: this.calculateDaysUntilExpiration(stock.date_expiration),
        // Calculer la valeur totale du stock
        valeur_totale: this.calculateTotalValue(stock),
        // Déterminer si le stock est critique
        est_critique: this.isStockCritical(stock)
      };

      return enrichedStock;
    } catch (error) {
      throw new Error(`Erreur lors de la récupération du stock : ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  /**
   * Calcule le statut du stock basé sur la quantité actuelle et le seuil d'alerte
   */
  private calculateStockStatus(stock: Stocks): string {
    const stockActuel = stock.stock_actuel || 0;
    const seuilAlerte = stock.seuil_alerte || 0;

    if (stockActuel === 0) {
      return "RUPTURE";
    } else if (stockActuel <= seuilAlerte) {
      return "ALERTE";
    } else if (stockActuel <= seuilAlerte * 2) {
      return "FAIBLE";
    } else {
      return "NORMAL";
    }
  }

  /**
   * Calcule le nombre de jours jusqu'à la date d'expiration
   */
  private calculateDaysUntilExpiration(dateExpiration?: string): number | null {
    if (!dateExpiration) {
      return null;
    }

    const today = new Date();
    const expirationDate = new Date(dateExpiration);
    const diffTime = expirationDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  }

  /**
   * Calcule la valeur totale du stock (quantité × prix unitaire estimé)
   * Note: Le prix unitaire n'est pas dans le modèle Stocks, donc on utilise une estimation
   */
  private calculateTotalValue(stock: Stocks): number {
    const stockActuel = stock.stock_actuel || 0;
    // Estimation du prix unitaire basée sur la catégorie ou valeur par défaut
    const prixUnitaireEstime = this.estimateUnitPrice(stock.categorie_id);
    
    return stockActuel * prixUnitaireEstime;
  }

  /**
   * Estime le prix unitaire basé sur la catégorie
   */
  private estimateUnitPrice(categorieId: number): number {
    // Logique d'estimation basée sur la catégorie
    const prixParCategorie: { [key: number]: number } = {
      1: 15.0,  // Médicaments
      2: 25.0,  // Matériel médical
      3: 10.0,  // Consommables
      4: 50.0,  // Équipements
    };

    return prixParCategorie[categorieId] || 20.0; // Prix par défaut
  }

  /**
   * Détermine si le stock est dans un état critique
   */
  private isStockCritical(stock: Stocks): boolean {
    const stockActuel = stock.stock_actuel || 0;
    const seuilAlerte = stock.seuil_alerte || 0;
    const joursAvantExpiration = this.calculateDaysUntilExpiration(stock.date_expiration);

    // Stock critique si :
    // - Quantité en rupture ou en alerte
    // - Expire dans moins de 7 jours
    return (
      stockActuel <= seuilAlerte ||
      (joursAvantExpiration !== null && joursAvantExpiration <= 7)
    );
  }
}

export default GetStockByIdUsecase;
