import { IGetAdminByUserIdRepository } from "@/domain/interfaces/repositories/admins";
import { IGetDashByIdRepository } from "@/domain/interfaces/repositories/dash/IGetDashByIdRepository";
import { IProfessionalRepository } from "@/domain/interfaces/repositories/IProfessionalRepository";
import { IGetPatientByIdUsecase } from "@/domain/interfaces/usecases/patients/IGetPatientByIdUsecase";
import { administrateurs, Dash, Patient, Professionnel } from "@/domain/models";
import { utilisateurs_role_enum } from "@/domain/models/enums";

export class GetUserInformationUsecase {
  constructor(
    private readonly adminRepository: IGetAdminByUserIdRepository,
    private readonly patientUsecase: IGetPatientByIdUsecase,
    private readonly professionalRepository: IProfessionalRepository,
    private readonly getDashByIdRepository: IGetDashByIdRepository
  ) {}

  private roleStrategies: Record<
    string,
    (
      id: number
    ) => Promise<Patient | administrateurs | Professionnel | Dash | null>
  > = {
    [utilisateurs_role_enum.ADMIN]: (id) => this.adminRepository.execute(id),
    [utilisateurs_role_enum.PATIENT]: (id) => this.patientUsecase.execute(id),
    [utilisateurs_role_enum.PROFESSIONNEL]: (id) =>
      this.professionalRepository.getProfessionalById(id),
    [utilisateurs_role_enum.DASH]: (id) =>
      this.getDashByIdRepository.execute(id),
  };

  async execute(
    role: string,
    id: number
  ): Promise<Patient | administrateurs | Professionnel | Dash | null> {
    const getUserInfo = this.roleStrategies[role];
    return getUserInfo ? getUserInfo(id) : null;
  }
}
