import { IProfessionalRepository } from "@/domain/interfaces/repositories";
import { RegisterProfessionalDTO } from "@/domain/DTOS/";
import { IEmailValidator } from "@/domain/interfaces/services/IEmailValidator";
import {
  AssuranceProfessionnel,
  Contact,
  DiplomeProfessionnel,
  ExperienceProfessionnel,
  LangueParleeProfessionnel,
  MotClesProfessionnel,
  ordre_appartenance_professionnel,
  Professionnel,
  PublicationProfessionnel,
  SpecialiteProfessionnel,
  Utilisateur,
} from "@/domain/models";
import { IRegisterProfessionalUsecase } from "@/domain/interfaces/usecases/IRegisterProfessionalUsecase";
import { ISupabaseUploader } from "@/domain/interfaces/services/ISupabaseUploader";
import { IAddPhotosUsecase } from "@/domain/interfaces/usecases/photos";
import {
  PhotoTypeEnum,
  professionnels_categories_enum,
  sexe_enum,
} from "@/domain/models/enums";
import { PRESENTATION_IMAGE_BUCKET, PROFILE_IMAGE_BUCKET } from "./constants";
import { IAddProfessionalSpecilitiesUsecase } from "@/domain/interfaces/usecases/professionalSpecialities";
import { ICreateOrdreAppartenanceUsecase } from "@/domain/interfaces/usecases/ordreAppartenance";
import { ICreateContactUsecase } from "@/domain/interfaces/usecases/contact";
import { IAddLanguageUsecase } from "@/domain/interfaces/usecases/professionalLanguage";
import { ICreateProfessionalExperienceUsecase } from "@/domain/interfaces/usecases/professionalExperience";
import { ICreateProfessionalPublicationUsecase } from "@/domain/interfaces/usecases/professionalPublication";
import { ICreateProfessionalMotClesUsecase } from "@/domain/interfaces/usecases/motCles";
import {
  ICreateAuthentificationUserUsecase,
  ICreateUserUsecase,
  IDeleteUserUsecase,
  IGetUserByEmailUsecase,
} from "@/domain/interfaces/usecases/user";
import { ICreateProfessionalInsuranceUsecase } from "@/domain/interfaces/usecases/insurance";
import { ICreateProfessionalDiplomaUsecase } from "@/domain/interfaces/usecases/professionalDiploma";
import { ICreateProfessionalEtablishmentUsecase } from "@/domain/interfaces/usecases/etablissementProfessionnel/ICreateProfessionalEtablishmentUsecase.ts";

export interface RegisterProfessionalResponse {
  professionalData: Professionnel | null;
  success: boolean;
  error?: string;
}

export class RegisterProfessionalUsecase
  implements IRegisterProfessionalUsecase
{
  constructor(
    private readonly createUserUsecase: ICreateUserUsecase,
    private readonly professionalRepository: IProfessionalRepository,
    private readonly getUserByEmailUsecase: IGetUserByEmailUsecase,
    private readonly createAuthenticationUserUsecase: ICreateAuthentificationUserUsecase,
    private readonly emailValidator: IEmailValidator,
    private readonly supabaseUploader: ISupabaseUploader,
    private readonly addPhotosUsecase: IAddPhotosUsecase,
    // Relation etrangeres
    private readonly addProfessionalSpecialityUsecase: IAddProfessionalSpecilitiesUsecase,
    private readonly createProfessionalInsurancesUsecase: ICreateProfessionalInsuranceUsecase,
    private readonly createOrdreAppartenanceUsecase: ICreateOrdreAppartenanceUsecase,
    private readonly createContactUsecase: ICreateContactUsecase,
    private readonly addLanguageUsecase: IAddLanguageUsecase,
    private readonly createProfessionalExperienciesUsecase: ICreateProfessionalExperienceUsecase,
    private readonly createProfessionalPublicationUsecase: ICreateProfessionalPublicationUsecase,
    private readonly createProfessionalMotClesUsecase: ICreateProfessionalMotClesUsecase,
    private readonly createProfessionalDiplomasUsecase: ICreateProfessionalDiplomaUsecase,
    // Rollback (en cas d'erreur)
    private readonly deleteUserUsecase: IDeleteUserUsecase,
    // Établissement professionnel
    private readonly createEtablissementProfessionnelUsecase: ICreateProfessionalEtablishmentUsecase
  ) {}

  async execute(
    data: RegisterProfessionalDTO,
    professionalCategory: professionnels_categories_enum
  ): Promise<RegisterProfessionalResponse> {
    let professionalInformation: null | Professionnel = null;
    let userInformation: null | Utilisateur = null;
    try {
      /*
       * ###########################################
       * ############# DONNEES DE BASE #############
       * ###########################################
       */

      // 1. Validation des donnees
      const isEmailValid = this.emailValidator.isValid(data.user.email);
      if (!isEmailValid) {
        return {
          professionalData: null,
          success: false,
          error: "Email invalide",
        };
      }

      // 2. Vérifier si l'email existe déjà
      const existingProfessional = await this.getUserByEmailUsecase.execute(
        data.user.email
      );

      if (existingProfessional) {
        return {
          professionalData: null,
          success: false,
          error: "Un professionnel avec cet email existe déjà",
        };
      }

      // 3. Creation de l'authentification supabase
      await this.createAuthenticationUserUsecase.execute(
        { email: data.user.email, password: data.user.mot_de_passe_hash },
        data.userData.nom
      );

      // 4. Creation de l'utilisateur (table utilisateurs)
      userInformation = await this.createUserUsecase.execute(data.user);

      // 5. Creation de l'utilisateur (table professionnels)
      const professionalUserData: Omit<Professionnel, "id"> = {
        nom: data.userData.nom,
        adresse: data.userData.adresse,
        geolocalisation: data.userData.geolocalisation,
        informations_acces: data.userData.informations_acces,
        numero_ordre: data.userData.numero_ordre,
        titre: data.userData.titre,
        types_consultation: data.userData.types_consultation,
        utilisateur_id: userInformation.id,
        commune: data.userData.commune || "",
        district: data.userData.district || "",
        fokontany: data.userData.fokontany || "",
        modes_paiement_acceptes: data.userData.modes_paiement_acceptes || "",
        nif: data.userData.nif || "",
        stat: data.userData.stat || "",
        nouveau_patient_acceptes:
          data.userData.nouveau_patient_acceptes || true,
        prenom: data.userData.prenom || "",
        presentation_generale: data.userData.presentation_generale || "",
        raison_sociale: data.userData.raison_sociale || "",
        region: data.userData.region || "",
        sexe: data.userData.sexe || sexe_enum.homme,
        temps_moyen_consulation: data.userData.temps_moyen_consulation,
      };

      professionalInformation =
        await this.professionalRepository.createProfessional(
          professionalUserData
        );

      // Gestion de l'établissement professionnel
      if (data?.etablissement && professionalInformation) {
        const etablissementData = {
          ...data.etablissement,
          id_professionnel: professionalInformation.id,
        };
        await this.createEtablissementProfessionnelUsecase.execute(
          etablissementData
        );
      }

      /*
       * ###########################################
       * ########### DONNEES OPTIONNELS ############
       * ###########################################
       */
      // Une erreur d'insertion ici ne devrait pas annuler la creation du professionnel

      // Gestion des diplomes
      if (data?.diplomas) {
        const formatedDiplomas: Omit<DiplomeProfessionnel, "id">[] =
          data.diplomas.map((diploma) => ({
            ...diploma,
            id_professionnel: professionalInformation.id,
          }));
        await this.createProfessionalDiplomasUsecase.execute(formatedDiplomas);

        // implement
        // await this.createProfessionalDiplomaUsecase.execute(data.diplomas)
      }

      // Gestion des experiences
      if (data?.experiencies) {
        const formatedProfessionalExperiences: Omit<
          ExperienceProfessionnel,
          "id"
        >[] = data.experiencies.map((exp) => ({
          ...exp,
          id_professionnel: professionalInformation.id,
        }));
        await this.createProfessionalExperienciesUsecase.execute(
          formatedProfessionalExperiences
        );
      }

      // Gestion des assurances
      if (data?.insurances && data.insurances.length > 0) {
        // transformation du type listeAssurance en assuranceProfessionnel
        const formatedInsurances: Omit<AssuranceProfessionnel, "id">[] =
          data.insurances.map((ins) => ({
            id_assurance: ins.id,
            id_professionnel: professionalInformation.id,
          }));
        await this.createProfessionalInsurancesUsecase.execute(
          formatedInsurances
        );
      }

      // Gestion des specialites
      if (data?.specialities && data.specialities.length > 0) {
        // transformation du type listeSpecialites en specialitesProfessionnel
        const formatedSpecialities: Omit<SpecialiteProfessionnel, "id">[] =
          data.specialities.map((spec) => ({
            nom_specialite: spec.nom_specialite,
            type_etablissement: professionalCategory,
            id_professionnel: professionalInformation.id,
          }));
        await this.addProfessionalSpecialityUsecase.execute(
          formatedSpecialities
        );
      }

      // Gestion des ordres d'appartenance
      if (data?.ordreAppartenances && data.ordreAppartenances.length > 0) {
        // transformation du type listeSpecialites en specialitesProfessionnel
        const formatedOrdreAppartenance: Omit<
          ordre_appartenance_professionnel,
          "id"
        >[] = data.ordreAppartenances.map((ord) => ({
          id_ordre_appartenance: ord.id,
          id_professionnel: professionalInformation.id,
        }));
        await this.createOrdreAppartenanceUsecase.execute(
          formatedOrdreAppartenance
        );
      }

      // Gestion des publications
      if (data?.publications) {
        const formatedPublications: Omit<PublicationProfessionnel, "id">[] =
          data.publications.map((pub) => ({
            ...pub,
            id_professionnel: professionalInformation.id,
          }));
        await this.createProfessionalPublicationUsecase.execute(
          formatedPublications
        );
      }

      // Gestion des mot cles
      if (data?.searchKeywords) {
        const formatedSearchKeywords: Omit<MotClesProfessionnel, "id">[] =
          data.searchKeywords.map((mot) => ({
            ...mot,
            id_professionnel: professionalInformation.id,
          }));
        await this.createProfessionalMotClesUsecase.execute(
          formatedSearchKeywords
        );
      }

      // Gestion des contacts
      if (data?.telephone) {
        const formatedContact: Omit<Contact, "id"> = {
          numero: data.telephone.numero,
          utilisateur_id: userInformation.id,
        };

        await this.createContactUsecase.execute([formatedContact]);
      }

      // Gestion des langues parlees
      if (data?.spokenLanguages) {
        const formatedLanguages: Omit<LangueParleeProfessionnel, "id">[] =
          data.spokenLanguages.map((lang) => ({
            nom_langue: lang.nom_langue,
            id_professionnel: professionalInformation.id,
          }));

        await this.addLanguageUsecase.execute(formatedLanguages);
      }

      // 6. Upload des images
      // Upload du profil
      if (data?.profile_image) {
        const { url: profileUrl, error: uploadError } =
          await this.supabaseUploader.uploadFile(
            PROFILE_IMAGE_BUCKET,
            data.profile_image,
            ""
          );

        if (uploadError) {
          console.log("Erreur d'upload du photo de profile", uploadError);
        }

        if (profileUrl) {
          await this.addPhotosUsecase.execute({
            path: profileUrl,
            type: PhotoTypeEnum.PROFILE,
            utilisateur_id: userInformation.id,
          });
        }
      }

      // Upload des photos de présentation du cabinet
      if (data?.cabinetImages && data.cabinetImages.length > 0) {
        const cabinetPhotosPromises = data.cabinetImages.map(async (image) => {
          const { url: cabinetImageUrl, error: uploadError } =
            await this.supabaseUploader.uploadFile(
              PRESENTATION_IMAGE_BUCKET,
              image,
              ""
            );

          if (uploadError) {
            console.log("Erreur d'upload d'une photo du cabinet", uploadError);
            return null;
          }

          if (cabinetImageUrl) {
            return this.addPhotosUsecase.execute({
              path: cabinetImageUrl,
              type: PhotoTypeEnum.PRESENTATION,
              utilisateur_id: userInformation.id,
            });
          }

          return null;
        });

        await Promise.all(cabinetPhotosPromises);
      }

      // Resultats
      return {
        professionalData: professionalInformation,
        success: true,
      };
    } catch (error) {
      console.log(error);

      if (userInformation && userInformation.id) {
        await this.deleteUserUsecase.execute(userInformation.id);
      }

      return {
        professionalData: null,
        success: false,
        error: error || "Erreur lors de l'inscription de l'utilisateur",
      };
    }
  }
}
