import { CreateProfessionalPatientDTO } from "@/domain/DTOS";
import { ICreatePatientRepository } from "@/domain/interfaces/repositories/patients";
import { ICreateProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";
import { IMatriculeGenerator } from "@/domain/interfaces/services/IMatriculeGenerator";
import { IRegisterProfessionalPatientUsecase } from "@/domain/interfaces/usecases/Register/IRegisterProfessinalPatientUsecase";
import { ProfessionnelPatient } from "@/domain/models";

class RegisterProfessionalPatientUsecase
  implements IRegisterProfessionalPatientUsecase {
  constructor(
    private readonly createPatientRepository: ICreatePatientRepository,
    private readonly createProfessionnelPatientRepository:
      ICreateProfessionalPatientRepository,
    private readonly matriculeGenerator: IMatriculeGenerator,
  ) {}

  async execute(
    data: Omit<CreateProfessionalPatientDTO, "id">,
  ): Promise<ProfessionnelPatient> {
    try {
      const date = new Date().getTime();
      const generatedMatricule = this.matriculeGenerator.generateMatricule(
        date,
        data.sexe,
      );

      const insertPatientData = await this.createPatientRepository.execute({
        nom: data.nom,
        prenom: data.prenom,
        sexe: data.sexe,
        date_naissance: data.date_naissance,
        district: data.district,
        commune: data.commune,
        region: data.region,
        decede: data.decede,
        adresse: data.adresse,
        nationalite: data.nationalite,
        situation_matrimonial: data.situation_matrimonial,
        nb_enfant: data.nb_enfant,
        profession: data.profession,
        unique_id: generatedMatricule,
      });

      const insertProfessionalPatientData = await this
        .createProfessionnelPatientRepository.execute({
          id_professionnel: data.id_professionnel,
          id_patient: insertPatientData.id,
        });

      return insertProfessionalPatientData;
    } catch (error) {
      console.log("error", error);

      throw new Error(error instanceof Error ? error.message : error);
    }
  }
}

export default RegisterProfessionalPatientUsecase;
