import { IVerifyAndCheckEmail } from "@/domain/interfaces/services/IVerifyAndCheckEmail";
import { ICreateDashUsecase } from "@/domain/interfaces/usecases/dash/ICreateDashUsecase";
import { IMarkInvitationDashAsUsedUsecase } from "@/domain/interfaces/usecases/invitationDash/IMarkInvitationDashAsUsedUsecase.ts";
import { ICreateAuthentificationUserUsecase } from "@/domain/interfaces/usecases/user/ICreateAuthentificationUserUsecase.ts";
import { ICreateUserUsecase } from "@/domain/interfaces/usecases/user/ICreateUserUsecase";
import { utilisateurs_role_enum } from "@/domain/models/enums/utilisateurRole";
import { DashInvitationFormData } from "@/shared/schemas/DashInvitationSchema";

class RegisterDashUsecase {
  constructor(
    private readonly emailValidator: IVerifyAndCheckEmail,
    private readonly createUserUsecase: ICreateUserUsecase,
    private readonly createDashUsecase: ICreateDashUsecase,
    private readonly createAuthentificationUserUsecase: ICreateAuthentificationUserUsecase,
    private readonly markInvitationDashAsUsedUsecase: IMarkInvitationDashAsUsedUsecase
  ) {}

  async execute(data: DashInvitationFormData, invitationId: number) {
    // 1. Verification de la validitee de l'email
    const isValid = await this.emailValidator.isEmailValid(data.email);

    if (!isValid) {
      throw new Error("Email invalide.");
    }

    // 2. Creation de l'authentification supabase
    await this.createAuthentificationUserUsecase.execute(
      {
        email: data.email,
        password: data.password,
      },
      data.organizationName
    );

    // 3. Creation de l'utilisateur (table utilisateurs)
    const user = await this.createUserUsecase.execute({
      email: data.email,
      mot_de_passe_hash: data.password,
      role: utilisateurs_role_enum.DASH,
      bani: false,
    });

    // 4. Creation de l'utilisateur (table dash)
    const dash = await this.createDashUsecase.execute({
      utilisateur_id: user.id,
      nom: data.organizationName,
    });

    // 5. Marquer l'invitation comme utilisée si tout s'est bien passé
    await this.markInvitationDashAsUsedUsecase.execute(invitationId);

    return {
      user: user,
      dash: dash,
      success: true,
    };
  }
}

export default RegisterDashUsecase;
