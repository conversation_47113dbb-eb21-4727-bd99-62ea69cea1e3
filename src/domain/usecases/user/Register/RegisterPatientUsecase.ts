import { Patient, Utilisateur } from "@/domain/models";
import { RegisterUserDTO } from "@/domain/DTOS";

// Interfaces
import { IUserRegistrationCleanupService } from "@/domain/interfaces/services/IUserRegistrationCleanupService";
import {
  IRegisterUserUsecase,
  registerProps,
} from "@/domain/interfaces/usecases/Register/IRegisterUserUsecase";

import { User } from "@supabase/supabase-js";
import { IVerifyAndCheckEmail } from "@/domain/interfaces/services/IVerifyAndCheckEmail";
import { ICreateContactUsecase } from "@/domain/interfaces/usecases/contact";
import {
  ICreateAuthentificationUserUsecase,
  ICreateUserUsecase,
} from "@/domain/interfaces/usecases/user";
import { ICreatePatientUsecase } from "@/domain/interfaces/usecases/patients";
import { IMatriculeGenerator } from "@/domain/interfaces/services/IMatriculeGenerator.ts";
import { utilisateurs_role_enum } from "@/domain/models/enums/utilisateurRole.ts";

export class RegisterPatientUsecase implements IRegisterUserUsecase {
  constructor(
    private readonly verifyAndCheckEmail: IVerifyAndCheckEmail,
    private readonly rollbackService: IUserRegistrationCleanupService,
    private readonly createAuthentificationUserUsecase: ICreateAuthentificationUserUsecase,
    private readonly createUserUsecase: ICreateUserUsecase,
    private readonly createPatientUsecase: ICreatePatientUsecase,
    private readonly createContactUsecase: ICreateContactUsecase,
    private readonly matriculeGenerator: IMatriculeGenerator
  ) {}

  async execute(
    data: registerProps,
    redirectToURL?: string
  ): Promise<RegisterUserDTO> {
    let newUser: User | null = null;
    let newUserData: Utilisateur | null = null;
    let newPatientData: Patient | null = null;

    if (!navigator.onLine) {
      throw new Error("Aucune connexion internet");
    }

    try {
      // Validation des donnees
      let isEmailValid = false;
      const isLoginUser = data.isLoginUser;

      if (isLoginUser && (!data.email || !data.password)) {
        throw new Error("Email et mot de passe sont requis");
      }

      if (isLoginUser) {
        isEmailValid = await this.verifyAndCheckEmail.isEmailValid(data.email);
        if (!isEmailValid) {
          throw new Error("Email invalide ou deja utilisée");
        }

        // Creer un utilisateur
        newUser = await this.createAuthentificationUserUsecase.execute(
          {
            email: data.email,
            password: data.password,
          },
          data.additionnalInfo.nom,
          redirectToURL
        );

        if (!newUser) {
          return {
            success: false,
            error: new Error("Erreur lors de la creation de l'utilisateur"),
            user: null,
            userData: null,
          };
        }
      }

      newUserData = await this.createUserUsecase.execute({
        role: utilisateurs_role_enum.PATIENT,
        email: data.email ? data.email : null,
        mot_de_passe_hash: data.password ? data.password : null,
      });

      if (!newUserData) {
        return {
          success: false,
          error: new Error("Erreur lors de la creation de l'utilisateur"),
          user: null,
          userData: null,
        };
      }

      const unique_id = this.matriculeGenerator.generateMatricule(
        data.additionnalInfo.sexe
      );

      newPatientData = await this.createPatientUsecase.execute({
        nom: data.additionnalInfo.nom,
        prenom: data.additionnalInfo?.prenom,
        sexe: data.additionnalInfo.sexe,
        date_naissance: data.additionnalInfo.date_naissance,
        adresse: data.additionnalInfo.adresse,
        region: data.additionnalInfo.region,
        commune: data.additionnalInfo.commune,
        district: data.additionnalInfo.district,
        province: data.additionnalInfo.province,
        situation_matrimonial: data.additionnalInfo.situation_matrimonial,
        telephone: data.additionnalInfo.telephone,
        nationalite: data.additionnalInfo.nationalite,
        unique_id: unique_id,
        utilisateur_id: newUserData.id,
      });

      if (!newPatientData) {
        return {
          success: false,
          error: new Error("Erreur lors de la creation du patient"),
          user: null,
          userData: null,
        };
      }

      // Inserer les contacts
      if (data.contact) {
        await this.createContactUsecase.execute(
          data.contact.map((contact) => ({
            numero: contact.numero,
            utilisateur_id: newUserData.id,
          }))
        );
      }

      // TODO: utiliser le contact generee pour envoyer une SMS des que le service des SMS est pret

      // Retourner les données complètes avec l'utilisateur
      return {
        success: true,
        userData: newPatientData,
        user: newUserData,
      };
    } catch (error) {
      await this.rollbackService.rollback(newUserData);

      if (error instanceof Error) {
        throw error;
      }
      throw new Error(String(error));
    }
  }
}
