import { IGetPhotoByUserIdRepository } from "@/domain/interfaces/repositories/photos";
import { IGetPhotosByUserIdUsecase } from "@/domain/interfaces/usecases/photos";
import { Photo } from "@/domain/models/Photo";

class GetPhotosByUserIdUsecase implements IGetPhotosByUserIdUsecase {
  constructor(
    private readonly getPhotoByUserIdRepository: IGetPhotoByUserIdRepository
  ) {}

  async execute(userId: number): Promise<Photo[]> {
    try {
      return this.getPhotoByUserIdRepository.execute(userId);
    } catch (error) {
      console.log(
        "Erreur lors de la recuperation des photos du professionnel",
        error
      );
      return error;
    }
  }
}

export default GetPhotosByUserIdUsecase;
