import { IGetPhotoByPathUsecase } from "@/domain/interfaces/usecases/photos";
import { Photo } from "@/domain/models/Photo";
import { IGetPhotoByPathRepository } from "@/infrastructure/repositories/photos";

class GetPhotosByPathUsecase implements IGetPhotoByPathUsecase {
  constructor(
    private readonly getPhotoByPathRepository: IGetPhotoByPathRepository
  ) {}

  async execute(path: string): Promise<Photo> {
    try {
      return this.getPhotoByPathRepository.execute(path);
    } catch (error) {
      console.log(
        "Erreur lors de la recuperation des photos du professionnel",
        error
      );
      return error;
    }
  }
}

export default GetPhotosByPathUsecase;
