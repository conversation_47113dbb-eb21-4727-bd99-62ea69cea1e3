import { IDeletePhotoRepository } from "@/domain/interfaces/repositories/photos/IDeletePhotoRepository.ts";
import { ISupabaseUploader } from "@/domain/interfaces/services/ISupabaseUploader";
import { IDeletePhotosUsecase } from "@/domain/interfaces/usecases/photos";

class DeletePhotoUsecase implements IDeletePhotosUsecase {
  constructor(
    private readonly deletePhotoRepository: IDeletePhotoRepository,
    private readonly supabaseUploader: ISupabaseUploader
  ) {}

  async execute(photoId: number) {
    try {
      const deletedPhoto = await this.deletePhotoRepository.execute(photoId);
      const matchingBucket = deletedPhoto.type;

      await this.supabaseUploader.deleteFile(matchingBucket, deletedPhoto.path);

      return deletedPhoto;
    } catch (error) {
      console.error("Erreur lors de la suppression du photo");
      throw error;
    }
  }
}

export default DeletePhotoUsecase;
