import { IGetPhotoByIdRepository } from "@/domain/interfaces/repositories/photos";
import { IGetPhotoByIdUsecase } from "@/domain/interfaces/usecases/photos";

class GetPhotosByIdUsecase implements IGetPhotoByIdUsecase {
  constructor(private getPhotosByIdRepository: IGetPhotoByIdRepository) {}

  async execute(id: number) {
    return await this.getPhotosByIdRepository.execute(id);
  }
}

export default GetPhotosByIdUsecase;
