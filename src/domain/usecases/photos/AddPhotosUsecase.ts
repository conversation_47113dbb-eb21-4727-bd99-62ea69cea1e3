import { IAddPhotosRepository } from "@/domain/interfaces/repositories/photos";
import { ISupabaseUploader } from "@/domain/interfaces/services/ISupabaseUploader";
import { IAddPhotosUsecase } from "@/domain/interfaces/usecases/photos";
import { PhotoTypeEnum } from "@/domain/models/enums";
import { Photo } from "@/domain/models/Photo";

class AddPhotosUsecase implements IAddPhotosUsecase {
  constructor(
    private readonly addPhotoRepository: IAddPhotosRepository,
    private readonly supabaseUploader: ISupabaseUploader,
  ) {}

  async execute(
    profilePhoto: Omit<Photo, "id">,
    file: File,
    path: string,
    type: PhotoTypeEnum,
  ): Promise<Photo[]> {
    const newPhoto = await this.addPhotoRepository.execute(profilePhoto);

    await this.supabaseUploader.uploadFile(type, file, path);

    return newPhoto;
  }
}

export default AddPhotosUsecase;
