import { IGetMedicamentByEmployerRepository, MedicamentEmployerStats } from "@/domain/interfaces/repositories/medicament/IGetMedicamentByEmployerRepository";
import { IGetMedicamentByEmployerUsecase } from "@/domain/interfaces/usecases/medicament/IGetMedicamentByEmployerUsecase";

class GetMedicamentByEmployerUsecase implements IGetMedicamentByEmployerUsecase {
    constructor(
        private readonly getMedicamentByEmployerRepository: IGetMedicamentByEmployerRepository
    ) { }

    async execute(): Promise<MedicamentEmployerStats[]> {
        try {
            const stats = await this.getMedicamentByEmployerRepository.execute();

            // Sort by repetitions in descending order to show most common medications first
            return stats.sort((a, b) => b.repetitions - a.repetitions);
        } catch (error) {
            throw new Error(`Failed to get medication statistics for employers: ${error}`);
        }
    }
}

export default GetMedicamentByEmployerUsecase;