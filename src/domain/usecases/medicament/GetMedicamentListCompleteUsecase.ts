import { IGetMedicamentListCompleteRepository } from "@/domain/interfaces/repositories/medicament/IGetMedicamentListCompleteRepository";
import { IGetMedicamentListCompleteUsecase } from "@/domain/interfaces/usecases/medicament/IGetMedicamentListCompleteUsecase";
import { ListeMedicaments } from "@/domain/models";

class GetMedicamentListCompleteUsecase implements IGetMedicamentListCompleteUsecase {
    constructor(
        private readonly getMedicamentListCompleteRepository: IGetMedicamentListCompleteRepository
    ) {}

    async execute(): Promise<ListeMedicaments[]> {
        return this.getMedicamentListCompleteRepository.execute();
    }
}

export default GetMedicamentListCompleteUsecase