import { IGetMedicamentByProfessionalRepository, MedicamentProfessionalStats } from "@/domain/interfaces/repositories/medicament/IGetMedicamentByProfessionalRepository";
import { IGetMedicamentByProfessionalUsecase } from "@/domain/interfaces/usecases/medicament/IGetMedicamentByProfessionalUsecase";

class GetMedicamentByProfessionalUsecase implements IGetMedicamentByProfessionalUsecase {
    constructor(
        private readonly getMedicamentByProfessionalRepository: IGetMedicamentByProfessionalRepository
    ) { }

    async execute(professionalId: number): Promise<MedicamentProfessionalStats[]> {
        try {
            // Validate professional ID
            if (!professionalId || professionalId <= 0) {
                throw new Error("Professional ID must be a positive number");
            }

            const stats = await this.getMedicamentByProfessionalRepository.execute(professionalId);

            // Repository already handles sorting, but we can add additional business logic here if needed
            return stats;
        } catch (error) {
            throw new Error(`Failed to get medication statistics for professional ${professionalId}: ${error}`);
        }
    }
}

export default GetMedicamentByProfessionalUsecase;