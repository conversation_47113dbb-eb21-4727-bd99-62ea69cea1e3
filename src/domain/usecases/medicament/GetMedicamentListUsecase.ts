import { IGetMedicamentListRepository } from "@/domain/interfaces/repositories/medicament/IGetMedicamentListRepository.ts";
import { IGetMedicamentListUsecase } from "@/domain/interfaces/usecases/medicament/IGetMedicamentListUsecase.ts";
import { Medicament } from "@/domain/models/Medicament.ts";

class GetMedicamentListUsecase implements IGetMedicamentListUsecase {
  constructor(
    private readonly getMedicamentListRepository: IGetMedicamentListRepository
  ) {}

  async execute(name: string): Promise<Medicament[]> {
    return this.getMedicamentListRepository.execute(name);
  }
}

export default GetMedicamentListUsecase;
