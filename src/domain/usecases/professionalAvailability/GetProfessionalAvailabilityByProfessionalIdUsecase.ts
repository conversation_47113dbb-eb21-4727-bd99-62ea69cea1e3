import { IGetProfessionalAvailabilityByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalAvailability";
import { IGetProfessionalAvailabilityByProfessionalIdUsecase } from "@/domain/interfaces/usecases/professionalAvailability";

class GetProfessionalAvailabilityByProfessionalIdUsecase
  implements IGetProfessionalAvailabilityByProfessionalIdUsecase
{
  constructor(
    private readonly getProfessionalAvailabilityByProfessionalIdRepository: IGetProfessionalAvailabilityByProfessionalIdRepository,
  ) {}

  async execute(professionalId: number) {
    return await this.getProfessionalAvailabilityByProfessionalIdRepository.execute(
      professionalId,
    );
  }
}

export default GetProfessionalAvailabilityByProfessionalIdUsecase;
