import { IGetInsuranceByIdRepository } from "@/domain/interfaces/repositories/insurance/IGetInsuranceByIdRepository.ts";
import { IGetInsuranceByIdUsecase } from "@/domain/interfaces/usecases/insurance/IGetInsuranceByIdUsecase.ts";

class GetInsuranceByIdUsecase implements IGetInsuranceByIdUsecase {
  constructor(
    private readonly getInsuranceByIdRepository: IGetInsuranceByIdRepository
  ) {}

  async execute(id: number) {
    return await this.getInsuranceByIdRepository.execute(id);
  }
}

export default GetInsuranceByIdUsecase;
