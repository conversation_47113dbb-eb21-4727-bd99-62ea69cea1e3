import { EntreesStocks } from "@/domain/models/EntreesStocks";
import { ICreateEntreesStocksRepository } from "@/domain/interfaces/repositories/entreesStocks/ICreateEntreesStocksRepository";
import { ICreateEntreesStocksUsecase } from "@/domain/interfaces/usecases/entreesStocks/ICreateEntreesStocksUsecase";
import { ICreateLotUsecase } from "@/domain/interfaces/usecases/lots/ICreateLotUsecase.ts";
import { Lots } from "@/domain/models/Lots.ts";
import { CreateStockDTO } from "@/domain/DTOS/StockDTO.ts";

class CreateEntreesStocksUsecase implements ICreateEntreesStocksUsecase {
  constructor(
    private readonly createEntreesStocksRepository: ICreateEntreesStocksRepository,
    private readonly createLotUsecase: ICreateLotUsecase
  ) {}

  async execute(
    stockData: CreateStockDTO[]
  ): Promise<{ entrees: EntreesStocks[]; lots: Lots[] }> {
    // Valider les données d'entrée
    if (!stockData) {
      throw new Error("Aucune entrée de stock fournie");
    }

    // Vérifier que tous les champs obligatoires sont présents
    for (const entree of stockData) {
      if (
        !entree ||
        !entree.stock_id ||
        !entree.quantite ||
        entree.quantite <= 0
      ) {
        throw new Error(
          "Données d'entrée invalides : stock_id et quantité positive requis"
        );
      }
    }

    try {
      // 1. Créer les entrées de stock
      const entreesStocks: Omit<EntreesStocks, "id">[] = stockData.map(
        (entree) => ({
          quantite: entree.quantite,
          stock_id: entree.stock_id,
          fournisseur_id: entree.fournisseur_id,
          prix_unitaire: entree.prix_unitaire,
          date_entree: entree.date_entree,
        })
      );
      const createdEntrees =
        await this.createEntreesStocksRepository.execute(entreesStocks);

      // 2. Créer les lots
      const lots = stockData.map((entree, index) => {
        const out: Omit<Lots, "id"> = {
          entree_id: createdEntrees[index].id,
          numero_lot: entree.lot.numero_lot,
          quantite: entree.lot.quantite,
          date_expiration: entree?.lot?.date_expiration,
        };

        return out;
      });
      const createdLots = await this.createLotUsecase.execute(lots);

      return { entrees: createdEntrees, lots: createdLots };
    } catch (error) {
      throw new Error(
        error instanceof Error ? error.message : "Erreur inconnue"
      );
    }
  }
}

export default CreateEntreesStocksUsecase;
