import { IGetEntreeStocksByStockIdRepository } from "@/domain/interfaces/repositories/entreesStocks/IGetEntreeStocksByStockIdRepository.ts";
import { IGetEntreesStocksByStockIdUsecase } from "@/domain/interfaces/usecases/entreesStocks/IGetEntreeStocksByStockIdUsecase.ts";

class GetEntreeStocksByStockIdUsecase
  implements IGetEntreesStocksByStockIdUsecase
{
  constructor(
    private readonly getEntreeStocksByStockIdRepository: IGetEntreeStocksByStockIdRepository
  ) {}

  async execute(stockId: number) {
    return await this.getEntreeStocksByStockIdRepository.execute(stockId);
  }
}

export default GetEntreeStocksByStockIdUsecase;
