import { EntreesStocks } from "@/domain/models/EntreesStocks";
import { IGetEntreesStocksRepository } from "@/domain/interfaces/repositories/entreesStocks/IGetEntreesStocksRepository";
import { IGetEntreesStocksUsecase } from "@/domain/interfaces/usecases/entreesStocks/IGetEntreesStocksUsecase";
import { Lots } from "@/domain/models/Lots.ts";
import { IGetLotsByEntreeIdUsecase } from "@/domain/interfaces/usecases/lots/IGetLotsByEntreeIdUsecase.ts";

class GetEntreesStocksUsecase implements IGetEntreesStocksUsecase {
  constructor(
    private readonly getEntreesStocksRepository: IGetEntreesStocksRepository,
    private readonly getLotsByEntreeIdUsecase: IGetLotsByEntreeIdUsecase
  ) {}

  async execute(
    professionalId: number
  ): Promise<{ entree: EntreesStocks; lots: Lots }[]> {
    if (!professionalId || professionalId <= 0) {
      throw new Error("ID professionnel invalide");
    }

    try {
      const entreesData =
        await this.getEntreesStocksRepository.execute(professionalId);

      // Recuperation des lots correspondants a chaque entree
      const entreesWithLots: { entrees: EntreesStocks; lots: Lots }[] =
        await Promise.all(
          entreesData.map(async (entree) => {
            const lots = await this.getLotsByEntreeIdUsecase.execute(entree.id);
            return { entrees: entree, lots: lots[0] };
          })
        );

      const out = entreesWithLots.map((entree) => ({
        entree: entree.entrees,
        lots: entree.lots,
      }));

      return out;
    } catch (error) {
      throw new Error(
        `Erreur lors de la récupération des entrées de stock : ${error instanceof Error ? error.message : "Erreur inconnue"}`
      );
    }
  }
}

export default GetEntreesStocksUsecase;
