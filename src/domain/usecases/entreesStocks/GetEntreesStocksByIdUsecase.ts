import { EntreesStocks } from "@/domain/models/EntreesStocks";
import { IGetEntreesStocksByIdRepository } from "@/domain/interfaces/repositories/entreesStocks/IGetEntreesStocksByIdRepository";
import { IGetEntreesStocksByIdUsecase } from "@/domain/interfaces/usecases/entreesStocks/IGetEntreesStocksByIdUsecase";

class GetEntreesStocksByIdUsecase implements IGetEntreesStocksByIdUsecase {
  constructor(
    private readonly getEntreesStocksByIdRepository: IGetEntreesStocksByIdRepository
  ) {}

  async execute(id: number): Promise<EntreesStocks | null> {
    if (!id || id <= 0) {
      throw new Error("ID invalide");
    }

    try {
      return await this.getEntreesStocksByIdRepository.execute(id);
    } catch (error) {
      throw new Error(`Erreur lors de la récupération de l'entrée de stock : ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }
}

export default GetEntreesStocksByIdUsecase;
