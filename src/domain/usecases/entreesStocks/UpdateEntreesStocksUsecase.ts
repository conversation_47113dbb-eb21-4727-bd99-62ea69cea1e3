import { EntreesStocks } from "@/domain/models/EntreesStocks";
import { IUpdateEntreesStocksRepository } from "@/domain/interfaces/repositories/entreesStocks/IUpdateEntreesStocksRepository";
import { IUpdateEntreesStocksUsecase } from "@/domain/interfaces/usecases/entreesStocks/IUpdateEntreesStocksUsecase";

class UpdateEntreesStocksUsecase implements IUpdateEntreesStocksUsecase {
  constructor(
    private readonly updateEntreesStocksRepository: IUpdateEntreesStocksRepository
  ) {}

  async execute(id: number, entreesStocks: Partial<Omit<EntreesStocks, "id">>): Promise<EntreesStocks | null> {
    if (!id || id <= 0) {
      throw new Error("ID invalide");
    }

    if (!entreesStocks || Object.keys(entreesStocks).length === 0) {
      throw new Error("Aucune donnée à mettre à jour");
    }

    // Valider les données si la quantité est fournie
    if (entreesStocks.quantite !== undefined && entreesStocks.quantite <= 0) {
      throw new Error("La quantité doit être positive");
    }

    try {
      return await this.updateEntreesStocksRepository.execute(id, entreesStocks);
    } catch (error) {
      throw new Error(`Erreur lors de la mise à jour de l'entrée de stock : ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }
}

export default UpdateEntreesStocksUsecase;
