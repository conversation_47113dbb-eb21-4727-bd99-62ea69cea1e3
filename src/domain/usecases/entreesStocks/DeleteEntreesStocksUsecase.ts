import { IDeleteEntreesStocksRepository } from "@/domain/interfaces/repositories/entreesStocks/IDeleteEntreesStocksRepository";
import { IDeleteEntreesStocksUsecase } from "@/domain/interfaces/usecases/entreesStocks/IDeleteEntreesStocksUsecase";

class DeleteEntreesStocksUsecase implements IDeleteEntreesStocksUsecase {
  constructor(
    private readonly deleteEntreesStocksRepository: IDeleteEntreesStocksRepository
  ) {}

  async execute(id: number): Promise<boolean> {
    if (!id || id <= 0) {
      throw new Error("ID invalide");
    }

    try {
      return await this.deleteEntreesStocksRepository.execute(id);
    } catch (error) {
      throw new Error(`Erreur lors de la suppression de l'entrée de stock : ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }
}

export default DeleteEntreesStocksUsecase;
