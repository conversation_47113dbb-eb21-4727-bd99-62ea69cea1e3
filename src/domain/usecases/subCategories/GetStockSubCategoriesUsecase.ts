import { IGetStockSubCategoriesRepository } from "@/domain/interfaces/repositories/subCategories/IGetStockSubCategoriesRepository";
import { IGetStockSubCategoriesUsecase } from "@/domain/interfaces/usecases/subCategories/IGetStockSubCategoriesUsecase.ts";

class GetStockSubCategoriesUsecase implements IGetStockSubCategoriesUsecase {
  constructor(
    private readonly getStockSubCategoriesRepository: IGetStockSubCategoriesRepository
  ) {}

  async execute() {
    return this.getStockSubCategoriesRepository.execute();
  }
}

export default GetStockSubCategoriesUsecase;
