import { IGetStockSubCategoriesByCategoryIdRepository } from "@/domain/interfaces/repositories/subCategories/IGetStockSubCategoriesByCategoryIdRepository";
import { IGetStockSubCategoriesByCategoryIdUsecase } from "@/domain/interfaces/usecases/subCategories/IGetStockSubCategoriesByCategoryIdUsecase.ts";
import { SousCategories } from "@/domain/models/SousCategories";

class GetStockSubCategoriesByCategoryIdUsecase
  implements IGetStockSubCategoriesByCategoryIdUsecase
{
  constructor(
    private readonly repository: IGetStockSubCategoriesByCategoryIdRepository
  ) {}

  async execute(categoryId: number): Promise<SousCategories[]> {
    return this.repository.execute(categoryId);
  }
}

export default GetStockSubCategoriesByCategoryIdUsecase;
