import { IGetStockSubCategoryByIdRepository } from "@/domain/interfaces/repositories/subCategories/IGetStockSubCategoryByIdRepository";
import { IGetStockSubCategoryByIdUsecase } from "@/domain/interfaces/usecases/subCategories/IGetStockSubCategoryByIdUsecase.ts";

class GetStockSubCategoryByIdUsecase
  implements IGetStockSubCategoryByIdUsecase
{
  constructor(
    private readonly getStockSubCategoryByIdRepository: IGetStockSubCategoryByIdRepository
  ) {}

  async execute(subCategoryId: number) {
    return this.getStockSubCategoryByIdRepository.execute(subCategoryId);
  }
}

export default GetStockSubCategoryByIdUsecase;
