import { IGetMessageByConversationIdRepository } from "@/domain/interfaces/repositories/message";
import { IGetUserByIdRepository } from "@/domain/interfaces/repositories/user";
import { type_message_enum } from "@/domain/models/enums";
import { MessageDTO } from "@/presentation/types/message.types";

export class GetMessageByConversationIdUsecase {
  constructor(
    private readonly getMessageByConversationIdRepository: IGetMessageByConversationIdRepository,
    private readonly getUserByIdRepository: IGetUserByIdRepository
  ) {}

  async execute(
    conversationId: number,
    userId: number
  ): Promise<MessageDTO[] | null> {
    if (!conversationId) {
      return [];
    }
    const messages =
      await this.getMessageByConversationIdRepository.execute(conversationId);
    const result = await Promise.all(
      messages.map(async (message) => {
        const user = await this.getUserByIdRepository.execute(
          message.id_expediteur
        );
        return {
          id: message.id,
          conversationId: message.id_conversation,
          senderId: message.id_expediteur,
          senderName: user.email.split("@")[0],
          senderRole: user.role,
          content: message.contenu,
          type: type_message_enum.text,
          status: message.status,
          sentAt: new Date(message.envoye_a),
          readAt: new Date(message.envoye_a),
          isOwn: message.id_expediteur === userId,
        };
      })
    );
    return result;
  }
}
