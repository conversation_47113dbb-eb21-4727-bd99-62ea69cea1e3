import { IGetUsersCanMessagingRepository } from "@/domain/interfaces/repositories/user";
import { IGetContactMessageUsecase } from "@/domain/interfaces/usecases/message";
import { Utilisateur } from "@/domain/models";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { Contact } from "@/presentation/types/message.types";

export class GetContactMessageUsecase implements IGetContactMessageUsecase {
  constructor(
    private readonly getUsersCanMessagingRepository: IGetUsersCanMessagingRepository,
    private readonly getUsersCanMessagingByPatientRepository: IGetUsersCanMessagingRepository
  ) {}

  async execute(role: utilisateurs_role_enum): Promise<Contact[]> {
    let users: Utilisateur[] = [];
    if (role !== utilisateurs_role_enum.PATIENT) {
      users = await this.getUsersCanMessagingRepository.execute();
    } else {
      users = await this.getUsersCanMessagingByPatientRepository.execute();
    }
    const contact: Contact[] = users.map((user) => {
      return {
        id: user.id,
        name: user.email.split("@")[0],
        role: user.role,
        avatarUrl: "",
        email: user.email,
        isOnline: true,
      };
    });
    return contact;
  }
}
