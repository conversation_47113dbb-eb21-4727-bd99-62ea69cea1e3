import { IGetStockCategoriesRepository } from "@/domain/interfaces/repositories/categories/IGetStockCategoriesRepository";
import { IGetStocksCategoriesUsecase } from "@/domain/interfaces/usecases/categories/IGetStocksCategoriesUsecase";

class GetStockCategoriesUsecase implements IGetStocksCategoriesUsecase {
  constructor(
    private readonly getStockCategoriesRepository: IGetStockCategoriesRepository
  ) {}

  async execute() {
    return this.getStockCategoriesRepository.execute();
  }
}

export default GetStockCategoriesUsecase;
