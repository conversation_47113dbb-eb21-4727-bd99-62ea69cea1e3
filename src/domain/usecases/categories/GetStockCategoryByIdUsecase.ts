import { IGetStockCategoryByIdRepository } from "@/domain/interfaces/repositories/categories/IGetStockCategoryByIdRepository";
import { IGetStockCategoryIdUsecase } from "@/domain/interfaces/usecases/categories/IGetStockCategoryByIdUsecase.ts";

class GetStockCategoryByIdUsecase implements IGetStockCategoryIdUsecase {
  constructor(
    private readonly getStockCategoriesByIdRepository: IGetStockCategoryByIdRepository
  ) {}

  async execute(categoryId: number) {
    return this.getStockCategoriesByIdRepository.execute(categoryId);
  }
}

export default GetStockCategoryByIdUsecase;
