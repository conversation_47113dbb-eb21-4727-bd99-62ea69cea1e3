import { IGetMaladieByProfessionalRepository, MaladieProfessionalStats } from "@/domain/interfaces/repositories/affectationMedicale";
import { IGetMaladieByProfessionalUsecase } from "@/domain/interfaces/usecases/affectationMedicale/IGetMaladieByProfessionalUsecase";

class GetMaladieByProfessionalUsecase implements IGetMaladieByProfessionalUsecase {
    constructor(
        private readonly getMaladieByProfessionalRepository: IGetMaladieByProfessionalRepository
    ) { }

    async execute(professionalId: number): Promise<MaladieProfessionalStats[]> {
        try {
            // Validate professional ID
            if (!professionalId || professionalId <= 0) {
                throw new Error("Professional ID must be a positive number");
            }

            const stats = await this.getMaladieByProfessionalRepository.execute(professionalId);

            // Repository already handles sorting, but we can add additional business logic here if needed
            return stats;
        } catch (error) {
            throw new Error(`Failed to get disease statistics for professional ${professionalId}: ${error}`);
        }
    }
}

export default GetMaladieByProfessionalUsecase;