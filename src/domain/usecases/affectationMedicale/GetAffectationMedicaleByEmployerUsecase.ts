import { IGetAffectationMedicaleByEmployerRepository, AffectationMedicaleEmployerStats } from "@/domain/interfaces/repositories/affectationMedicale/IGetAffectationMedicaleByEmployerRepository";
import { IGetAffectationMedicaleByEmployerUsecase } from "@/domain/interfaces/usecases/affectationMedicale/IGetAffectationMedicaleByEmployerUsecase";

class GetAffectationMedicaleByEmployerUsecase implements IGetAffectationMedicaleByEmployerUsecase {
    constructor(
        private readonly getAffectationMedicaleByEmployerRepository: IGetAffectationMedicaleByEmployerRepository
    ) { }

    async execute(): Promise<AffectationMedicaleEmployerStats[]> {
        try {
            const stats = await this.getAffectationMedicaleByEmployerRepository.execute();

            // Sort by repetitions in descending order to show most common diseases first
            return stats.sort((a, b) => b.repetitions - a.repetitions);
        } catch (error) {
            throw new Error(`Failed to get medical affectation statistics for employers: ${error}`);
        }
    }
}

export default GetAffectationMedicaleByEmployerUsecase;
