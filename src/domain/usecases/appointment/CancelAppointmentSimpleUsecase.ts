import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { ICancelAppointmentRepository } from "@/domain/interfaces/repositories/appointment";
import { ICancelAppointmentEmailService } from "@/domain/interfaces/services/customEmailService/ICancelAppointmentEmailService";
import { IGetProfessionalByIdUsecase } from "@/domain/interfaces/usecases/professionals/IGetProfessionalByIdUsecase";
import { IGetUserByIdUsecase } from "@/domain/interfaces/usecases/user";
import { RendezVous } from "@/domain/models";
import { GetPatientBasicInfoUsecase } from "../patients/GetPatientBasicInfoUsecase";

/**
 * Version simplifiée du CancelAppointmentUsecase qui évite les problèmes
 * de relations cassées avec le carnet de santé
 */
class CancelAppointmentSimpleUsecase {
  constructor(
    private readonly cancelAppointmentRepository: ICancelAppointmentRepository,
    private readonly getUserByIdUsecase: IGetUserByIdUsecase,
    private readonly getPatientBasicInfoUsecase: GetPatientBasicInfoUsecase,
    private readonly getProfessionalByIdUsecase: IGetProfessionalByIdUsecase,
    private readonly cancelAppointmentEmailService: ICancelAppointmentEmailService
  ) {}

  async execute(appointment: AppointmentProfessionalDTO): Promise<RendezVous> {
    try {
      // Récupération des données nécessaires
      const appointmentDate = String(appointment.date_rendez_vous).split("T")[0];
      const appointmentTime = String(appointment.date_rendez_vous).split("T")[1];

      const matchingProfessional = await this.getProfessionalByIdUsecase.execute(
        appointment.id_professionnel
      );

      // Utiliser la version simplifiée pour récupérer le patient
      const matchingPatient = await this.getPatientBasicInfoUsecase.execute(
        appointment.patient.id
      );

      const appUrl = import.meta.env.VITE_APP_URL;

      let matchingUser = null;
      if (matchingPatient?.utilisateur_id) {
        matchingUser = await this.getUserByIdUsecase.execute(
          matchingPatient.utilisateur_id
        );
      }

      // Annuler le rendez-vous
      const result = await this.cancelAppointmentRepository.execute(appointment.id);

      // Envoyer l'email si l'utilisateur existe
      if (matchingUser && matchingPatient) {
        await this.cancelAppointmentEmailService.execute({
          to: matchingUser.email,
          AppointmentDate: appointmentDate,
          AppointmentTime: appointmentTime,
          DoctorName: `${matchingProfessional.nom} ${matchingProfessional.prenom}`,
          FirstName: matchingPatient.nom,
          RescheduleURL: appUrl,
        });
      }

      return result;
    } catch (error) {
      console.log("Erreur lors de l'annulation du rendez-vous:", error);
      throw error;
    }
  }
}

export default CancelAppointmentSimpleUsecase;
