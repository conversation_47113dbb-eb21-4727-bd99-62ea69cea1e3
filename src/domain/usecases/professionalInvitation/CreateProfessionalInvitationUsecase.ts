import { ICreateProfessionalInvitationRepository } from "@/domain/interfaces/repositories/professionalInvitation";
import { ICreateProfessionalInvitationUsecase } from "@/domain/interfaces/usecases/professionalInvitation";
import { Invitation } from "@/domain/models";

export class CreateProfessionalInvitationUsecase
  implements ICreateProfessionalInvitationUsecase
{
  constructor(
    private readonly createProfessionalInvitationRepository: ICreateProfessionalInvitationRepository
  ) {}

  async execute(invitation: Omit<Invitation, "id">): Promise<Invitation> {
    try {
      // Ici, vous pourrez ajouter des contraintes ou logiques métier supplémentaires
      // Par exemple, vérifier si l'email n'est pas déjà utilisé, générer un token unique, etc.
      return await this.createProfessionalInvitationRepository.execute(
        invitation
      );
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
