import { IGetInvitationByTokenRepository } from "@/domain/interfaces/repositories/professionalInvitation";
import { IGetInvitationByTokenUsecase } from "@/domain/interfaces/usecases/professionalInvitation";

class GetInvitationByTokenUsecase implements IGetInvitationByTokenUsecase {
  constructor(
    private readonly getInvitationByTokenRepository: IGetInvitationByTokenRepository,
  ) {}

  async execute(token: string) {
    return this.getInvitationByTokenRepository.execute(token);
  }
}

export default GetInvitationByTokenUsecase;
