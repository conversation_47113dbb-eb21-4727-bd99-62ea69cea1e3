import { IMarkInvitationAsUsedRepository } from "@/domain/interfaces/repositories/professionalInvitation";
import { IMarkInvitationAsUsedUsecase } from "@/domain/interfaces/usecases/professionalInvitation";
import { Invitation } from "@/domain/models";

export class MarkInvitationAsUsedUsecase
  implements IMarkInvitationAsUsedUsecase
{
  constructor(
    private readonly markInvitationAsUsedRepository: IMarkInvitationAsUsedRepository,
  ) {}

  async execute(invitation_id: number): Promise<Invitation> {
    try {
      return await this.markInvitationAsUsedRepository.execute(invitation_id);
    } catch (error) {
      console.log(
        "Une erreur s'est produite lors de la mise a jour d'etat de l'invitation",
        error,
      );
      return null;
    }
  }
}
