import { IGetEtablishmentTypesRepository } from "@/domain/interfaces/repositories/typeEtablissement/IGetEtablishmentTypesRepository";
import { IGetEtablishmentTypesUsecase } from "@/domain/interfaces/usecases/typeEtablissement/IGetEtablishmentTypesUsecase";

class GetEtablishmentTypesUsecase implements IGetEtablishmentTypesUsecase {
  constructor(
    private readonly getEtablishmentTypesRepository: IGetEtablishmentTypesRepository
  ) {}

  async execute() {
    return this.getEtablishmentTypesRepository.execute();
  }
}

export default GetEtablishmentTypesUsecase;
