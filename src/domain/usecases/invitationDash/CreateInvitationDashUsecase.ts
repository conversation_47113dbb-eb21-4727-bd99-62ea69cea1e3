import { ICreateInvitationDashRepository } from "@/domain/interfaces/repositories/invitationDash";
import { ICreateInvitationDashUsecase } from "@/domain/interfaces/usecases/invitationDash";
import { InvitationDash } from "@/domain/models";

export class CreateInvitationDashUsecase
  implements ICreateInvitationDashUsecase
{
  constructor(
    private readonly createInvitationDashRepository: ICreateInvitationDashRepository
  ) {}

  async execute(
    invitation: Omit<InvitationDash, "id">
  ): Promise<InvitationDash> {
    try {
      return await this.createInvitationDashRepository.execute(invitation);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
