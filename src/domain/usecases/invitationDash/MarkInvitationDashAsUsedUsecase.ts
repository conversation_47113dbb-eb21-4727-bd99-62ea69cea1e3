import { IMarkInvitationDashAsUsedRepository } from "@/domain/interfaces/repositories/invitationDash";
import { IMarkInvitationDashAsUsedUsecase } from "@/domain/interfaces/usecases/invitationDash";
import { InvitationDash } from "@/domain/models";

export class MarkInvitationDashAsUsedUsecase
  implements IMarkInvitationDashAsUsedUsecase
{
  constructor(
    private readonly markInvitationDashAsUsedRepository: IMarkInvitationDashAsUsedRepository
  ) {}

  async execute(invitation_id: number): Promise<InvitationDash> {
    try {
      // Ici, vous pourrez ajouter des validations supplémentaires
      // Par exemple, vérifier si l'invitation existe et n'est pas déjà utilisée
      return await this.markInvitationDashAsUsedRepository.execute(
        invitation_id
      );
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
