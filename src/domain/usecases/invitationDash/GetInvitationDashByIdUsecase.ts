import { IGetInvitationDashByIdRepository } from "@/domain/interfaces/repositories/invitationDash";
import { IGetInvitationDashByIdUsecase } from "@/domain/interfaces/usecases/invitationDash";
import { InvitationDash } from "@/domain/models";

export class GetInvitationDashByIdUsecase
  implements IGetInvitationDashByIdUsecase
{
  constructor(
    private readonly getInvitationDashByIdRepository: IGetInvitationDashByIdRepository
  ) {}

  async execute(invitation_id: number): Promise<InvitationDash | null> {
    try {
      return await this.getInvitationDashByIdRepository.execute(invitation_id);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
