import { IGetInvitationDashByTokenRepository } from "@/domain/interfaces/repositories/invitationDash/IGetInvitationDashByTokenRepository";
import { IGetInvitationDashByTokenUsecase } from "@/domain/interfaces/usecases/invitationDash/IGetInvitationDashByTokenUsecase";
import { InvitationDash } from "@/domain/models";

export class GetInvitationDashByTokenUsecase implements IGetInvitationDashByTokenUsecase {
  constructor(
    private readonly getInvitationDashByTokenRepository: IGetInvitationDashByTokenRepository
  ) {}

  async execute(token: string): Promise<InvitationDash | null> {
    try {
      // Validation du token
      if (!token || token.trim() === "") {
        throw new Error("Le token est requis");
      }

      // Récupération de l'invitation par token
      const invitation = await this.getInvitationDashByTokenRepository.execute(token);
      
      // Vérification si l'invitation existe
      if (!invitation) {
        return null;
      }

      // Vérification si l'invitation n'a pas déjà été utilisée
      if (invitation.est_utilisee) {
        throw new Error("Cette invitation a déjà été utilisée");
      }

      return invitation;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
