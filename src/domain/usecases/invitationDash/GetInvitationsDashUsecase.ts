import { IGetInvitationsDashRepository } from "@/domain/interfaces/repositories/invitationDash";
import { IGetInvitationsDashUsecase } from "@/domain/interfaces/usecases/invitationDash";
import { InvitationDash } from "@/domain/models";

export class GetInvitationsDashUsecase implements IGetInvitationsDashUsecase {
  constructor(
    private readonly getInvitationsDashRepository: IGetInvitationsDashRepository
  ) {}

  async execute(): Promise<InvitationDash[]> {
    try {
      return await this.getInvitationsDashRepository.execute();
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
