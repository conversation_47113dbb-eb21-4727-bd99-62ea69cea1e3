import { PatientDTO } from "@/domain/DTOS";
import { IGetPatientByIdRepository } from "@/domain/interfaces/repositories/patients";
import { IGetPatientByIdUsecase } from "@/domain/interfaces/usecases/patients";

/**
 * Use case pour récupérer un patient par son ID avec toutes ses données associées
 *
 * @class GetPatientByIdUsecase
 * @implements {IGetPatientByIdUsecase}
 *
 * @description
 * Ce use case récupère les informations complètes d'un patient incluant :
 * - Les données personnelles du patient
 * - Les contacts d'urgence
 * - Les derniers signes vitaux
 * - Le carnet de santé (si existant, null pour les nouveaux patients)
 */
export class GetPatientByIdUsecase implements IGetPatientByIdUsecase {
  constructor(
    private readonly getPatientByIdRepository: IGetPatientByIdRepository
  ) {}

  async execute(id: number): Promise<PatientDTO | null> {
    const patientData = await this.getPatientByIdRepository.execute(id);

    if (!patientData) {
      return null;
    }
    const out: PatientDTO = {
      ...patientData,
      carnet_sante: patientData.carnet_sante[0],
    };

    return out;
  }
}
