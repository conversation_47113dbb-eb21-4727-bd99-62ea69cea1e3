import { Patient } from "@/domain/models";
import { GetPatientBasicInfoRepository } from "@/infrastructure/repositories/patients/GetPatientBasicInfoRepository";

/**
 * Use case pour récupérer les informations de base d'un patient
 * sans le carnet de santé (utilisé pour l'annulation de rendez-vous)
 */
export class GetPatientBasicInfoUsecase {
  constructor(
    private readonly getPatientBasicInfoRepository: GetPatientBasicInfoRepository
  ) {}

  async execute(id: number): Promise<Patient | null> {
    return await this.getPatientBasicInfoRepository.execute(id);
  }
}
