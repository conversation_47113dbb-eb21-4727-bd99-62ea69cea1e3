import { IGetPatientByPatientIdRepository } from "@/domain/interfaces/repositories/patients";
import { IGetPatientByPatientIdUsecase } from "@/domain/interfaces/usecases/patients";
import { Patient } from "@/domain/models";

class GetPatientByPatientIdUsecase implements IGetPatientByPatientIdUsecase {
  constructor(
    private readonly getPatientByPatientIdRepository: IGetPatientByPatientIdRepository
  ) {}

  async execute(id: number): Promise<Patient> {
    return await this.getPatientByPatientIdRepository.execute(id);
  }
}

export default GetPatientByPatientIdUsecase;
