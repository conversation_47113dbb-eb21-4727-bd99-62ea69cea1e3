import { IDeleteUrgenceRepository } from "@/domain/interfaces/repositories/urgence";
import { Urgence } from "@/domain/models";

export class DeleteUrgencesUsecase {
    constructor(
        private readonly deleteUrgenceRepository: IDeleteUrgenceRepository,
    ) { }

    async execute(
        id: number
    ): Promise<Urgence[] | null> {
        try {
            return await this.deleteUrgenceRepository.execute(id);
        } catch (error) {
            return null;
        }
    }
}
