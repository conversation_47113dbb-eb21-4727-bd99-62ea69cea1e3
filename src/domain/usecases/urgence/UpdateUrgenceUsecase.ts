import { IUpdateUrgenceRepository } from "@/domain/interfaces/repositories/urgence";
import { Urgence } from "@/domain/models";

export class UpdateUrgencesUsecase {
    constructor(
        private readonly updateUrgenceRepository: IUpdateUrgenceRepository,
    ) { }

    async execute(
        id: number,
        contact: Partial<Urgence>
    ): Promise<Urgence | null> {
        try {
            return await this.updateUrgenceRepository.execute(id, contact);
        } catch (error) {
            return null;
        }
    }
}
