import { ICreateUrgenceRepository, IDeleteUrgenceRepository } from "@/domain/interfaces/repositories/urgence";
import { Urgence } from "@/domain/models";

export class CreateUrgencesUsecase {
    constructor(
        private readonly createUrgenceRepository: ICreateUrgenceRepository,
        private readonly deleteUrgenceRepository: IDeleteUrgenceRepository,
    ) { }

    async execute(
        urgence: Omit<Urgence, 'id'>[]
    ): Promise<Urgence[] | null> {
        try {
            await this.deleteUrgenceRepository.execute(urgence[0].id_patient);
            return await this.createUrgenceRepository.execute(urgence);
        } catch (error) {
            console.error('Error in CreateUrgencesUsecase:', error);
            return null;
        }
    }
}
