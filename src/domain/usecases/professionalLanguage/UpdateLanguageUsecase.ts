import { IUpdateLanguageRepository } from "@/domain/interfaces/repositories/professionalLanguage/IUpdateLanguageRepository.ts";
import { LangueParleeProfessionnel } from "@/domain/models/LangueParleeProfessionnel.ts";
import { IUpdateLanguageUsecase } from "@/domain/interfaces/usecases/professionalLanguage/IUpdateLanguageUsecase.ts";

class UpdateLanguageUsecase implements IUpdateLanguageUsecase {
  constructor(
    private readonly updateLanguageRepository: IUpdateLanguageRepository
  ) {}

  async execute(language: Partial<LangueParleeProfessionnel>, id: number) {
    try {
      return await this.updateLanguageRepository.execute(id, language);
    } catch (error) {
      console.error("Error updating language:", error);
      throw error;
    }
  }
}

export default UpdateLanguageUsecase;
