import { IDeleteExpertiseProfessionnelRepository } from "@/domain/interfaces/repositories/expertiseProfessionnel/IDeleteExpertiseProfessionnelRepository";
import { IDeleteExpertiseProfessionnelUsecase } from "@/domain/interfaces/usecases/expertiseProfessionnel/IDeleteExpertiseProfessionnelUsecase";

export class DeleteExpertiseProfessionnelUsecase implements IDeleteExpertiseProfessionnelUsecase {
  constructor(
    private readonly deleteExpertiseProfessionnelRepository: IDeleteExpertiseProfessionnelRepository
  ) {}

  async execute(id: number): Promise<void> {
    return this.deleteExpertiseProfessionnelRepository.execute(id);
  }
}
