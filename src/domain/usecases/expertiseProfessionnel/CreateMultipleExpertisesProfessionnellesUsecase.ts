import { ICreateMultipleExpertisesProfessionnellesRepository } from "@/domain/interfaces/repositories/expertiseProfessionnel/ICreateMultipleExpertisesProfessionnellesRepository";
import { ExpertiseProfessionnel } from "@/domain/models/ExpertiseProfessionnel";
import { ICreateMultipleExpertisesProfessionnellesUsecase } from "@/domain/interfaces/usecases/expertiseProfessionnel/ICreateMultipleExpertisesProfessionnellesUsecase";

export class CreateMultipleExpertisesProfessionnellesUsecase implements ICreateMultipleExpertisesProfessionnellesUsecase {
  constructor(
    private readonly createMultipleExpertisesProfessionnellesRepository: ICreateMultipleExpertisesProfessionnellesRepository
  ) {}

  async execute(
    expertisesProfessionnelles: Omit<ExpertiseProfessionnel, "id" | "date_creation">[]
  ): Promise<ExpertiseProfessionnel[]> {
    // Ajouter la date de création automatiquement pour chaque expertise
    const expertisesProfessionnellesWithDate = expertisesProfessionnelles.map(expertise => ({
      ...expertise,
      date_creation: new Date()
    }));

    return this.createMultipleExpertisesProfessionnellesRepository.execute(expertisesProfessionnellesWithDate);
  }
}
