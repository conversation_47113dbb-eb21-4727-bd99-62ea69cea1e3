import { ICreateExpertiseProfessionnelRepository } from "@/domain/interfaces/repositories/expertiseProfessionnel/ICreateExpertiseProfessionnelRepository";
import { ExpertiseProfessionnel } from "@/domain/models/ExpertiseProfessionnel";
import { ICreateExpertiseProfessionnelUsecase } from "@/domain/interfaces/usecases/expertiseProfessionnel/ICreateExpertiseProfessionnelUsecase";

export class CreateExpertiseProfessionnelUsecase implements ICreateExpertiseProfessionnelUsecase {
  constructor(
    private readonly createExpertiseProfessionnelRepository: ICreateExpertiseProfessionnelRepository
  ) {}

  async execute(
    expertiseProfessionnel: Omit<ExpertiseProfessionnel, "id" | "date_creation">
  ): Promise<ExpertiseProfessionnel> {
    // Ajouter la date de création automatiquement
    const expertiseProfessionnelWithDate = {
      ...expertiseProfessionnel,
      date_creation: new Date()
    };

    return this.createExpertiseProfessionnelRepository.execute(expertiseProfessionnelWithDate);
  }
}
