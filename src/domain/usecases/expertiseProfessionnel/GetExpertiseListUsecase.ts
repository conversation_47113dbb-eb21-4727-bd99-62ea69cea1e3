import { IGetExpertiseListRepository } from "@/domain/interfaces/repositories/expertiseProfessionnel";
import { IGetExpertiseListUsecase } from "@/domain/interfaces/usecases/expertiseProfessionnel";

class GetExpertiseListUsecase implements IGetExpertiseListUsecase {
  constructor(
    private readonly getExpertiseListRepository: IGetExpertiseListRepository
  ) { }

  async execute() {
    return this.getExpertiseListRepository.execute()
  }
}


export default GetExpertiseListUsecase
