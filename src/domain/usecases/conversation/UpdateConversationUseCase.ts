import { conversation } from "@/domain/models";
import { IUpdateConversationRepository } from "@/domain/interfaces/repositories/conversation";

export class UpdateConversationUseCase {
  constructor(
    private readonly updateConversationRepository: IUpdateConversationRepository
  ) {}

  async execute(
    id: number,
    data: Partial<conversation>
  ): Promise<conversation> {
    return await this.updateConversationRepository.execute(id, data);
  }
}
