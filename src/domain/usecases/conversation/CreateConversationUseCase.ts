import { ICreateConversationRepository } from "@/domain/interfaces/repositories/conversation";
import { conversation } from "@/domain/models";

export class CreateConversationUseCase {
  constructor(
    private readonly createConversationRepository: ICreateConversationRepository
  ) {}

  async execute(data: Omit<conversation, "id">): Promise<conversation> {
    return await this.createConversationRepository.execute(data);
  }
}
