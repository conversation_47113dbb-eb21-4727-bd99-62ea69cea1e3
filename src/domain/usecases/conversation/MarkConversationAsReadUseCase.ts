import { conversation } from "@/domain/models";
import { IMarkConversationAsReadRepository } from "@/domain/interfaces/repositories/conversation";

export class MarkConversationAsReadUseCase {
  constructor(
    private readonly markConversationAsReadRepository: IMarkConversationAsReadRepository
  ) {}

  async execute(id: number): Promise<conversation> {
    return await this.markConversationAsReadRepository.execute(id);
  }
}
