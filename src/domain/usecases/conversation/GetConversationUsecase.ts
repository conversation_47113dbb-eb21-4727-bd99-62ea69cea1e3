import { IGetConversationRepository } from "@/domain/interfaces/repositories/conversation";
import { IGetUserByIdRepository } from "@/domain/interfaces/repositories/user";
import { Utilisateur } from "@/domain/models";
import { ConversationDTO } from "@/presentation/types/message.types";

export class GetConversationUsecase {
  constructor(
    private readonly getConversationRepository: IGetConversationRepository,
    private readonly getUserByIdRepository: IGetUserByIdRepository
  ) {}

  async execute(userId: number): Promise<ConversationDTO[] | null> {
    if (!userId) {
      return [];
    }
    const conversations = await this.getConversationRepository.execute(userId);
    console.log(conversations);

    const result = await Promise.all(
      conversations.map(async (conversation) => {
        const user = await this.getUserByIdRepository.execute(
          userId === conversation.id_recepteur
            ? conversation.id_expediteur
            : conversation.id_recepteur
        );

        return {
          createdAt: conversation.cree_a,
          id: conversation.id,
          idParticipant: conversation.id_recepteur,
          idExpediteur: conversation.id_expediteur,
          participant: {
            id: user.id,
            name: user.email.split("@")[0],
            role: user.role,
            avatarUrl: "",
            isOnline: true,
            lastSeen: null,
          },
          unreadCount: conversation.nombre_non_lu,
          updatedAt: conversation.mis_a_jour_a,
          isArchived: conversation.est_archive,
          isMuted: conversation.en_sourdine,
          lastMessage: conversation.dernier_message,
          lastMessageTime: conversation.heure_dernier_message,
        };
      })
    );

    return result;
  }
}
