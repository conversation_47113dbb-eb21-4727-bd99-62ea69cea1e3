import { IGetProfessionalByIdUsecase } from "../interfaces/usecases/professionals/IGetProfessionalByIdUsecase";
import { IGetUserByIdUsecase } from "../interfaces/usecases/user";
import { RendezVous } from "../models";

class GetFullAppointmentDetailUsecase {
  constructor(
    private readonly getUserById: IGetUserByIdUsecase,
    private readonly getProfessionalById: IGetProfessionalByIdUsecase,
  ) {}

  async execute(appointmentData: RendezVous) {
    const patient = await this.getUserById.execute(appointmentData.patient_id);
    const professional = await this.getProfessionalById.execute(
      appointmentData.id_professionnel,
    );

    return {
      patient,
      professional,
      appointment: appointmentData,
    };
  }
}

export default GetFullAppointmentDetailUsecase;
