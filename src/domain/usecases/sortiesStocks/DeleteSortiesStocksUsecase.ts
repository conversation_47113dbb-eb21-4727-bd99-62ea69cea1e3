import { IDeleteSortiesStocksRepository } from "@/domain/interfaces/repositories/sortiesStocks/IDeleteSortiesStocksRepository";
import { IDeleteSortiesStocksUsecase } from "@/domain/interfaces/usecases/sortiesStocks/IDeleteSortiesStocksUsecase";

class DeleteSortiesStocksUsecase implements IDeleteSortiesStocksUsecase {
  constructor(
    private readonly deleteSortiesStocksRepository: IDeleteSortiesStocksRepository
  ) {}

  async execute(id: number): Promise<boolean> {
    if (!id || id <= 0) {
      throw new Error("ID invalide");
    }

    // Note: La suppression d'une sortie de stock pourrait nécessiter une logique complexe
    // pour restaurer les quantités dans les stocks. Pour l'instant, on fait une suppression simple.
    
    try {
      return await this.deleteSortiesStocksRepository.execute(id);
    } catch (error) {
      throw new Error(`Erreur lors de la suppression de la sortie de stock : ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }
}

export default DeleteSortiesStocksUsecase;
