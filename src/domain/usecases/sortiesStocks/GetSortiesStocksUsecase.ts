import { SortiesStocks } from "@/domain/models/SortiesStocks";
import { IGetSortiesStocksRepository } from "@/domain/interfaces/repositories/sortiesStocks/IGetSortiesStocksRepository";
import { IGetSortiesStocksUsecase } from "@/domain/interfaces/usecases/sortiesStocks/IGetSortiesStocksUsecase";

class GetSortiesStocksUsecase implements IGetSortiesStocksUsecase {
  constructor(
    private readonly getSortiesStocksRepository: IGetSortiesStocksRepository
  ) {}

  async execute(professionalId: number): Promise<SortiesStocks[]> {
    if (!professionalId || professionalId <= 0) {
      throw new Error("ID professionnel invalide");
    }

    try {
      const rawData =
        await this.getSortiesStocksRepository.execute(professionalId);
      // Transformer les données brutes en un format plus utilisable
      const transformedData = rawData.map((item) => ({
        id: item.id,
        lot_id: item.lots.id,
        quantite: item.quantite,
        type_sortie: item.type_sortie,
        destinataire: item.destinataire,
        date_sortie: item.date_sortie,
      }));

      return transformedData;
    } catch (error) {
      throw new Error(
        `Erreur lors de la récupération des sorties de stock : ${error instanceof Error ? error.message : "Erreur inconnue"}`
      );
    }
  }
}

export default GetSortiesStocksUsecase;
