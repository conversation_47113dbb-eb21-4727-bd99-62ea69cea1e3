import { SortiesStocks } from "@/domain/models/SortiesStocks";
import { IUpdateSortiesStocksRepository } from "@/domain/interfaces/repositories/sortiesStocks/IUpdateSortiesStocksRepository";
import { IUpdateSortiesStocksUsecase } from "@/domain/interfaces/usecases/sortiesStocks/IUpdateSortiesStocksUsecase";

class UpdateSortiesStocksUsecase implements IUpdateSortiesStocksUsecase {
  constructor(
    private readonly updateSortiesStocksRepository: IUpdateSortiesStocksRepository
  ) {}

  async execute(id: number, sortiesStocks: Partial<Omit<SortiesStocks, "id">>): Promise<SortiesStocks | null> {
    if (!id || id <= 0) {
      throw new Error("ID invalide");
    }

    if (!sortiesStocks || Object.keys(sortiesStocks).length === 0) {
      throw new Error("Aucune donnée à mettre à jour");
    }

    // Valider les données si la quantité est fournie
    if (sortiesStocks.quantite !== undefined && sortiesStocks.quantite <= 0) {
      throw new Error("La quantité doit être positive");
    }

    // Note: La mise à jour d'une sortie de stock pourrait nécessiter une logique complexe
    // pour recalculer les stocks si la quantité change. Pour l'instant, on fait une mise à jour simple.
    
    try {
      return await this.updateSortiesStocksRepository.execute(id, sortiesStocks);
    } catch (error) {
      throw new Error(`Erreur lors de la mise à jour de la sortie de stock : ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }
}

export default UpdateSortiesStocksUsecase;
