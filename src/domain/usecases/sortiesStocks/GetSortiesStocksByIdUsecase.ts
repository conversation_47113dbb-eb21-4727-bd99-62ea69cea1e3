import { SortiesStocks } from "@/domain/models/SortiesStocks";
import { IGetSortiesStocksByIdRepository } from "@/domain/interfaces/repositories/sortiesStocks/IGetSortiesStocksByIdRepository";
import { IGetSortiesStocksByIdUsecase } from "@/domain/interfaces/usecases/sortiesStocks/IGetSortiesStocksByIdUsecase";

class GetSortiesStocksByIdUsecase implements IGetSortiesStocksByIdUsecase {
  constructor(
    private readonly getSortiesStocksByIdRepository: IGetSortiesStocksByIdRepository
  ) {}

  async execute(id: number): Promise<SortiesStocks | null> {
    if (!id || id <= 0) {
      throw new Error("ID invalide");
    }

    try {
      return await this.getSortiesStocksByIdRepository.execute(id);
    } catch (error) {
      throw new Error(`Erreur lors de la récupération de la sortie de stock : ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }
}

export default GetSortiesStocksByIdUsecase;
