import { ICreateSortiesStocksRepository } from "@/domain/interfaces/repositories/sortiesStocks/ICreateSortiesStocksRepository.ts";
import { ICreateSortiesStocksUsecase } from "@/domain/interfaces/usecases/sortiesStocks/ICreateSortiesStocksUsecase.ts";
import { IGetEntreesStocksByStockIdUsecase } from "@/domain/interfaces/usecases/entreesStocks/IGetEntreeStocksByStockIdUsecase.ts";
import { IGetLotsByEntreeIdUsecase } from "@/domain/interfaces/usecases/lots/IGetLotsByEntreeIdUsecase.ts";
import { IUpdateLotUsecase } from "@/domain/interfaces/usecases/lots/IUpdateLotUsecase.ts";
import { Lots } from "@/domain/models/Lots.ts";
import { SortiesStocks } from "@/domain/models/SortiesStocks.ts";
import dayjs from "dayjs";

class CreateSortiesStocksUsecase implements ICreateSortiesStocksUsecase {
  constructor(
    private readonly createSortiesStocksRepository: ICreateSortiesStocksRepository,
    private readonly getEntreeStocksByStockIdUsecase: IGetEntreesStocksByStockIdUsecase,
    private readonly getLotsByEntreeIdUsecase: IGetLotsByEntreeIdUsecase,
    private readonly updateLotUsecase: IUpdateLotUsecase
  ) {}

  async execute(
    stock_id: number,
    quantite: number,
    type_sortie: string,
    destinataire?: string
  ): Promise<SortiesStocks[]> {
    // Récupérer les lots de l'entrée de stock
    const entrees =
      await this.getEntreeStocksByStockIdUsecase.execute(stock_id);

    const lots: Lots[] = [];

    for (const entree of entrees) {
      const lotsEntree = await this.getLotsByEntreeIdUsecase.execute(entree.id);
      lots.push(...lotsEntree);
    }

    // Retirer les lots périmés
    const today = dayjs();
    const validLots = lots.filter((lot) => {
      const expirationDate = dayjs(lot.date_expiration);
      return expirationDate.isAfter(today);
    });

    // Compter quantité disponible (sans les lots périmés)
    const totalQuantity = validLots.reduce((acc, lot) => acc + lot.quantite, 0);

    if (totalQuantity < quantite) {
      throw new Error("Quantité insuffisante");
    }

    /**
     * Parcourir les lots triés par date d'expiration (sans les lots périmés).
     * Soustraire de chaque lot autant que possible jusqu'à satisfaire la quantité demandée.
     * Si un lot ne suffit pas → passer au lot suivant
     */
    validLots.sort((a, b) => {
      const dateA = new Date(a.date_expiration);
      const dateB = new Date(b.date_expiration);
      return dateA.getTime() - dateB.getTime();
    });

    const lotsUsed: {
      lot: Lots;
      quantiteUtilisee: number;
      quantiteRestante: number;
    }[] = [];

    /**
     * Exemple:
     * [50, 150, 200] lots disponibles
     * 90 quantité demandée
     *
     * 1er lot: 50
     * 2e lot: 40
     *
     * lotsUsed = [
     *  { lot: 1, quantiteUtilisee: 50, quantiteRestante: 0 },
     *  { lot: 2, quantiteUtilisee: 40, quantiteRestante: 110 },
     */

    for (const lot of validLots) {
      if (quantite <= 0) break;
      if (lot.quantite <= 0) continue;

      if (lot.quantite < quantite) {
        lotsUsed.push({
          lot,
          quantiteUtilisee: 0,
          quantiteRestante: lot.quantite,
        });
        quantite -= lot.quantite;
      } else {
        lotsUsed.push({
          lot,
          quantiteUtilisee: lot.quantite - quantite,
          quantiteRestante: quantite,
        });
        lot.quantite -= quantite;
        quantite = 0;
      }
    }

    // Mise à jour des lots utilisés
    for (const lot of lotsUsed) {
      await this.updateLotUsecase.execute(lot.lot.id, {
        quantite: lot.quantiteUtilisee,
      });
    }

    // Enregistrer la sortie
    const sorties = lotsUsed.map(({ lot, quantiteRestante }) => ({
      lot_id: lot.id,
      quantite: quantiteRestante,
      type_sortie,
      destinataire,
    }));

    const out = await this.createSortiesStocksRepository.execute(sorties);

    return out;
  }
}

export default CreateSortiesStocksUsecase;
