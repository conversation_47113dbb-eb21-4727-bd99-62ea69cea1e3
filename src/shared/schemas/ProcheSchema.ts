import { sexe_enum } from "@/domain/models/enums";
import { z } from "zod";

export const ProcheSchema = z.object({
  nom: z.string().min(1, "Le nom du proche est requis."),
  prenom: z.string().optional(),
  sexe: z.nativeEnum(sexe_enum, {
    required_error: "Le sexe est requis",
    invalid_type_error: "Veuillez sélectionner un sexe valide",
  }),
  date_naissance: z.date({
    required_error: "La date de naissance est requise.",
    invalid_type_error:
      "La date du rendez-vous doit être une chaîne de caractères.",
  }),
  lien_parente: z.string().min(1, "Le lien avec le proche est requis."),
});

export type ProcheFormData = z.infer<typeof ProcheSchema>;
