import { z } from "zod";

/**
 * Schémas de validation pour les formulaires de gestion de stock
 */
export const fournisseurSchema = z.object({
  nom: z.string().min(1, "Le nom du fournisseur est requis"),
  adresse: z.string().optional(),
  telephone: z.string().optional(),
  courriel: z.string().email("Email invalide").optional().or(z.literal("")),
});

export type FournisseurFormData = z.infer<typeof fournisseurSchema>;
