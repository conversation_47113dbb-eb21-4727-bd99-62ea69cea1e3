import { z } from "zod";
import {
  professionnels_titre_enum,
  professionnels_types_consultation_enum,
} from "@/domain/models/enums";

/**
 * Schéma de validation pour les informations de base
 */
export const baseInfoSchema = z.object({
  titre: z.nativeEnum(professionnels_titre_enum, {
    required_error: "Le titre est requis",
    invalid_type_error: "Veuillez sélectionner un titre valide",
  }),
  nom: z
    .string()
    .min(1, "Le nom est requis")
    .max(255, "Le nom ne peut pas dépasser 255 caractères"),
  prenom: z
    .string()
    .max(255, "Le prénom ne peut pas dépasser 255 caractères")
    .optional()
    .or(z.literal("")),
});

/**
 * Schéma de validation pour la section Présentation
 */
export const presentationSchema = z.object({
  presentation: z
    .string()
    .max(5000, "La présentation ne peut pas dépasser 5000 caractères")
    .optional()
    .or(z.literal("")),
});

/**
 * Schéma de validation pour la section Services
 */
export const servicesSchema = z.object({
  types_consultation: z.nativeEnum(professionnels_types_consultation_enum, {
    required_error: "Le type de consultation est requis",
    invalid_type_error: "Veuillez sélectionner un type de consultation valide",
  }),
  nouveau_patient_acceptes: z.boolean({
    required_error: "Veuillez indiquer si vous acceptez de nouveaux patients",
  }),
});

/**
 * Schéma de validation pour les informations professionnelles
 */
export const professionalInfoSchema = z.object({
  numero_ordre: z
    .string()
    .max(50, "Le numéro d'ordre ne peut pas dépasser 50 caractères")
    .optional()
    .or(z.literal("")),
  raison_sociale: z
    .string()
    .max(255, "La raison sociale ne peut pas dépasser 255 caractères")
    .optional()
    .or(z.literal("")),
  nif: z
    .string()
    .max(20, "Le NIF ne peut pas dépasser 20 caractères")
    .regex(/^[0-9]*$/, "Le NIF ne peut contenir que des chiffres")
    .optional()
    .or(z.literal("")),
  stat: z
    .string()
    .max(20, "Le STAT ne peut pas dépasser 20 caractères")
    .regex(/^[0-9]*$/, "Le STAT ne peut contenir que des chiffres")
    .optional()
    .or(z.literal("")),
});

/**
 * Schéma de validation pour l'ajout d'un mot-clé
 */
export const keywordSchema = z.object({
  symptome: z
    .string()
    .min(1, "Le mot-clé ne peut pas être vide")
    .max(255, "Le mot-clé ne peut pas dépasser 255 caractères")
    .trim(),
});

/**
 * Schéma de validation pour l'ajout d'une langue
 */
export const languageSchema = z.object({
  nom_langue: z
    .string()
    .min(1, "Le nom de la langue ne peut pas être vide")
    .max(100, "Le nom de la langue ne peut pas dépasser 100 caractères")
    .trim(),
});

/**
 * Schéma de validation pour l'ajout d'une spécialité
 */
export const specialtySchema = z.object({
  nom: z
    .string()
    .min(1, "Le nom de la spécialité ne peut pas être vide")
    .max(255, "Le nom de la spécialité ne peut pas dépasser 255 caractères")
    .trim(),
});

/**
 * Schéma de validation pour les images du cabinet
 */
export const cabinetImagesSchema = z.object({
  images: z
    .array(z.instanceof(File))
    .max(5, "Maximum 5 images autorisées")
    .optional(),
});

/**
 * Schéma de validation pour l'ajout d'un diplôme
 */
export const diplomaSchema = z.object({
  titre: z
    .string()
    .min(1, "Le titre du diplôme est requis")
    .max(255, "Le titre ne peut pas dépasser 255 caractères")
    .trim(),
  etablissement: z
    .string()
    .min(1, "L'établissement est requis")
    .max(255, "L'établissement ne peut pas dépasser 255 caractères")
    .trim(),
  annee: z
    .string()
    .min(4, "L'année doit contenir 4 chiffres")
    .max(4, "L'année doit contenir 4 chiffres")
    .regex(/^\d{4}$/, "L'année doit être au format YYYY"),
  description: z
    .string()
    .max(1000, "La description ne peut pas dépasser 1000 caractères")
    .optional()
    .or(z.literal("")),
});

/**
 * Schéma de validation pour l'ajout d'une expérience
 */
export const experienceSchema = z.object({
  poste: z
    .string()
    .min(1, "Le poste est requis")
    .max(255, "Le poste ne peut pas dépasser 255 caractères")
    .trim(),
  etablissement: z
    .string()
    .min(1, "L'établissement est requis")
    .max(255, "L'établissement ne peut pas dépasser 255 caractères")
    .trim(),
  date_debut: z.union([z.string(), z.date()]).refine((val) => {
    if (typeof val === "string") {
      return val.length >= 4;
    }
    return val instanceof Date;
  }, "La date de début est requise"),
  date_fin: z
    .union([z.string(), z.date(), z.null()])
    .optional()
    .or(z.literal("")),
  description: z
    .string()
    .max(2000, "La description ne peut pas dépasser 2000 caractères")
    .optional()
    .or(z.literal("")),
});

/**
 * Schéma de validation pour l'ajout d'une publication
 */
export const publicationSchema = z.object({
  titre: z
    .string()
    .min(1, "Le titre est requis")
    .max(500, "Le titre ne peut pas dépasser 500 caractères")
    .trim(),
  lien: z
    .string()
    .url("Le lien doit être une URL valide")
    .optional()
    .or(z.literal("")),
  annee: z
    .string()
    .min(4, "L'année doit contenir 4 chiffres")
    .max(4, "L'année doit contenir 4 chiffres")
    .regex(/^\d{4}$/, "L'année doit être au format YYYY"),
  description: z
    .string()
    .max(1000, "La description ne peut pas dépasser 1000 caractères")
    .optional()
    .or(z.literal("")),
});

/**
 * Schéma de validation pour les informations de contact
 */
export const contactSchema = z.object({
  email: z
    .string()
    .email("Veuillez saisir une adresse email valide")
    .max(255, "L'email ne peut pas dépasser 255 caractères"),
  telephone: z
    .string()
    .min(1, "Le numéro de téléphone est requis")
    .max(20, "Le numéro de téléphone ne peut pas dépasser 20 caractères")
    .regex(/^(\+261|0)[0-9\s\-.]{8,}$/, "Format de téléphone invalide"),
  adresse: z
    .string()
    .min(1, "L'adresse est requise")
    .max(255, "L'adresse ne peut pas dépasser 255 caractères"),
  fokontany: z
    .string()
    .min(1, "Le fokontany est requis")
    .max(100, "Le fokontany ne peut pas dépasser 100 caractères"),
  infoAcces: z
    .string()
    .max(500, "Les informations d'accès ne peuvent pas dépasser 500 caractères")
    .optional()
    .or(z.literal("")),
});

/**
 * Schéma de validation pour les modes de paiement et assurances
 */
export const paymentAndInsuranceSchema = z.object({
  modes_paiement_acceptes: z
    .string()
    .max(500, "Les modes de paiement ne peuvent pas dépasser 500 caractères")
    .optional()
    .or(z.literal("")),
  // Note: Les assurances sont gérées séparément via CRUD
});

/**
 * Schéma de validation pour les informations d'établissement
 */
export const establishmentSchema = z.object({
  nom_etablissement: z
    .string()
    .min(1, "Le nom de l'établissement est requis")
    .max(255, "Le nom de l'établissement ne peut pas dépasser 255 caractères"),
  nom_responsable: z
    .string()
    .min(1, "Le nom du responsable est requis")
    .max(255, "Le nom du responsable ne peut pas dépasser 255 caractères"),
  prenom_responsable: z
    .string()
    .min(1, "Le prénom du responsable est requis")
    .max(255, "Le prénom du responsable ne peut pas dépasser 255 caractères"),
  equipe: z
    .string()
    .max(
      1000,
      "La description de l'équipe ne peut pas dépasser 1000 caractères"
    )
    .optional()
    .or(z.literal("")),
});

// Types TypeScript dérivés des schémas
export type PresentationFormData = z.infer<typeof presentationSchema>;
export type ServicesFormData = z.infer<typeof servicesSchema>;
export type ProfessionalInfoFormData = z.infer<typeof professionalInfoSchema>;
export type KeywordFormData = z.infer<typeof keywordSchema>;
export type LanguageFormData = z.infer<typeof languageSchema>;
export type SpecialtyFormData = z.infer<typeof specialtySchema>;
export type ContactFormData = z.infer<typeof contactSchema>;
export type PaymentAndInsuranceFormData = z.infer<
  typeof paymentAndInsuranceSchema
>;
export type EstablishmentFormData = z.infer<typeof establishmentSchema>;
export type CabinetImagesFormData = z.infer<typeof cabinetImagesSchema>;
export type DiplomaFormData = z.infer<typeof diplomaSchema>;
export type ExperienceFormData = z.infer<typeof experienceSchema>;
export type PublicationFormData = z.infer<typeof publicationSchema>;

export type BaseInfoFormData = z.infer<typeof baseInfoSchema>;
