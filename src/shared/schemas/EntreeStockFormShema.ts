import { z } from "zod";

export const entreeStockSchema = z.object({
  stock_id: z.number().min(1, "Sélectionnez un produit"),
  fournisseur_id: z.number().min(1, "Sélectionnez un fournisseur"),
  quantite: z.coerce.number().min(1, "La quantité doit être positive"),
  prix_unitaire: z.coerce.number().min(0, "Le prix unitaire doit être positif"),
  numero_lot: z.string().optional(),
  date_expiration: z.date().nullable().optional(),
});

export type EntreeStockFormData = z.infer<typeof entreeStockSchema>;
