import { patients_groupe_sanguin_enum, sexe_enum } from "@/domain/models/enums";
import { z } from "zod";

// Regex patterns for validation
const phoneRegex = /^(0(32|33|34|39)\d{7}|\+261(32|33|34|39)\d{7})$/;
const nameRegex = /^[a-zA-ZÀ-ÿ\s'-]+$/;
const passwordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;

export const patientSchema = z
  .object({
    nom: z
      .string()
      .min(1, "Le nom est requis")
      .min(2, "Le nom doit contenir au moins 2 caractères")
      .max(50, "Le nom ne peut pas dépasser 50 caractères")
      .regex(
        nameRegex,
        "Le nom ne peut contenir que des lettres, espaces, apostrophes et tirets"
      ),
    prenom: z
      .string()
      .min(1, "Le prénom est requis")
      .min(2, "Le prénom doit contenir au moins 2 caractères")
      .max(50, "Le prénom ne peut pas dépasser 50 caractères")
      .regex(
        nameRegex,
        "Le prénom ne peut contenir que des lettres, espaces, apostrophes et tirets"
      ),
    sexe: z.nativeEnum(sexe_enum, {
      required_error: "Le sexe est requis",
      invalid_type_error: "Veuillez sélectionner un sexe valide",
    }),
    date_naissance: z.coerce
      .date({
        required_error: "La date de naissance est requise",
        invalid_type_error: "Veuillez entrer une date valide",
      })
      .refine((date) => {
        const today = new Date();
        const age = today.getFullYear() - date.getFullYear();
        return age >= 0 && age <= 120;
      }, "L'âge doit être entre 0 et 120 ans")
      .refine((date) => {
        const today = new Date();
        return date <= today;
      }, "La date de naissance ne peut pas être dans le futur"),
    adresse: z
      .string()
      .min(1, "L'adresse est requise")
      .min(5, "L'adresse doit contenir au moins 5 caractères")
      .max(200, "L'adresse ne peut pas dépasser 200 caractères"),
    province: z.string().min(1, "Le province est requis"),
    district: z.string().min(1, "Le district est requis"),
    commune: z.string().min(1, "La commune est requise"),
    region: z.string().min(1, "Le region est requis"),
    email: z
      .string()
      .min(1, "L'email est requis")
      .email("Format d'email invalide")
      .max(100, "L'email ne peut pas dépasser 100 caractères")
      .toLowerCase(),
    groupe_sanguin: z.nativeEnum(patients_groupe_sanguin_enum).optional(),
    nationalite: z.string().optional(),
    pays: z.string().optional(),
    situation_matrimonial: z.string().optional(),
    telephone: z
      .string()
      .min(1, "Le numéro de téléphone est requis")
      .regex(
        phoneRegex,
        "Format de téléphone invalide. Utilisez le format: 032XXXXXXX ou +261XXXXXXXXX"
      ),
    nb_enfant: z.number().optional(),
    profession: z.string().optional(),
    mot_de_passe: z
      .string()
      .min(1, "Le mot de passe est requis")
      .min(8, "Le mot de passe doit contenir au moins 8 caractères")
      .max(100, "Le mot de passe ne peut pas dépasser 100 caractères")
      .regex(
        passwordRegex,
        "Le mot de passe doit contenir au moins: 1 minuscule, 1 majuscule, 1 chiffre et 1 caractère spécial"
      ),
    confirmation_mot_de_passe: z
      .string()
      .min(1, "La confirmation du mot de passe est requise"),
  })
  .refine((data) => data.mot_de_passe === data.confirmation_mot_de_passe, {
    path: ["confirmation_mot_de_passe"],
    message: "Les mots de passe ne correspondent pas",
  });

export type PatientFormData = z.infer<typeof patientSchema>;
