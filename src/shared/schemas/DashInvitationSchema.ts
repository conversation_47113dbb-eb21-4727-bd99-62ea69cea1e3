import { z } from "zod";

/**
 * Schéma de validation pour la génération d'invitations dash
 */
export const dashInvitationSchema = z
  .object({
    email: z
      .string()
      .min(1, "L'email est requis")
      .email("Format d'email invalide")
      .max(100, "L'email ne peut pas dépasser 100 caractères"),
    organizationName: z
      .string()
      .min(1, "Le nom de l'organisme est requis")
      .min(2, "Le nom de l'organisme doit contenir au moins 2 caractères")
      .max(100, "Le nom de l'organisme ne peut pas dépasser 100 caractères")
      .regex(
        /^[a-zA-ZÀ-ÿ0-9\s\-_.&()]+$/,
        "Le nom de l'organisme ne peut contenir que des lettres, chiffres, espaces et caractères spéciaux autorisés (- _ . & ( ))"
      ),
    password: z
      .string()
      .min(1, "Le mot de passe est requis")
      .min(8, "Le mot de passe doit contenir au moins 8 caractères")
      .max(100, "Le mot de passe ne peut pas dépasser 100 caractères"),
    confirmPassword: z
      .string()
      .min(1, "La confirmation du mot de passe est requise"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    path: ["confirmPassword"],
    message: "Les mots de passe ne correspondent pas",
  });

export const createDashInvitationShema = z.object({
  email: z
    .string()
    .min(1, "L'email est requis")
    .email("Format d'email invalide")
    .max(100, "L'email ne peut pas dépasser 100 caractères"),
  organizationName: z
    .string()
    .min(1, "Le nom de l'organisme est requis")
    .min(2, "Le nom de l'organisme doit contenir au moins 2 caractères")
    .max(100, "Le nom de l'organisme ne peut pas dépasser 100 caractères")
    .regex(
      /^[a-zA-ZÀ-ÿ0-9\s\-_.&()]+$/,
      "Le nom de l'organisme ne peut contenir que des lettres, chiffres, espaces et caractères spéciaux autorisés (- _ . & ( ))"
    ),
});

/**
 * Type TypeScript dérivé du schéma de validation
 */
export type DashInvitationFormData = z.infer<typeof dashInvitationSchema>;
export type CreateDashInvitationFormData = z.infer<
  typeof createDashInvitationShema
>;
