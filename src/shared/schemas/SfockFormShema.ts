import { z } from "zod";

export const stockSchema = z.object({
  nom: z.string().min(1, "Le nom du produit est requis"),
  description: z.string().optional(),
  categorie_id: z.string().min(1, "La catégorie est requise"),
  unite: z.string().min(1, "L'unité est requise"),
  seuil_alerte: z.coerce.number().min(0, "Le seuil d'alerte doit être positif"),
});

export type StockFormData = z.infer<typeof stockSchema>;
