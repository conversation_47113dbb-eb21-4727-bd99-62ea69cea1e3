import { categorie_enum } from "@/domain/models/enums";
import { z } from "zod";
import { ProcheSchema } from "./ProcheSchema";

export const consultationStepShema = z.object({
  categorie: z.nativeEnum(categorie_enum, {
    message: "La catégorie est invalide.",
  }),
  speciality: z.string().min(1, "La spécialité est requise."),
  consultationMotif: z
    .string()
    .min(1, "Le motif de la consultation est requis."),
  consultationReason: z.string().optional(),
  forWhom: z
    .string()
    .min(1, "Ce champ est requis (pour qui est le rendez-vous)."),
  procheInfo: ProcheSchema.optional(),
});

export type ConsultationStepFormData = z.infer<typeof consultationStepShema>;
