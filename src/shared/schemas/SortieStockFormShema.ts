import { z } from "zod";

export const sortieStockSchema = z.object({
  stock_id: z.number().min(1, "Sélectionnez un produit"),
  quantite: z.coerce.number().min(1, "La quantité doit être supérieure à 0"),
  type_sortie: z.enum(["vente", "utilisation", "perte"], {
    required_error: "Sélectionnez un type de sortie",
  }),
  destinataire: z.string().optional(),
});

export type SortieStockFormData = z.infer<typeof sortieStockSchema>;
