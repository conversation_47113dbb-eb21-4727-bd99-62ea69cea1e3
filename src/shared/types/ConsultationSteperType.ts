import dayjs, { Dayjs } from "dayjs";

export type Info = {
  sex: string;
  firstName: string;
  lastName: string;
  birthDate: string | null;
};

export interface ConsultationStep1Props {
  speciality: string;
  consultationType: string;
  consultationReason: string;
  onSpecialityChange: (value: string) => void;
  onConsultationTypeChange: (value: string) => void;
  onConsultationReasonChange: (value: string) => void;
}

export interface ConsultationStep2Props {
  phone: string;
  email: string;
  confirmEmail: string;
  password: string;
  acceptConditions: boolean;
  onPhoneChange: (value: string) => void;
  onEmailChange: (value: string) => void;
  onConfirmEmailChange: (value: string) => void;
  onPasswordChange: (value: string) => void;
  onAcceptConditionsChange: (value: boolean) => void;
}

export interface ConsultationStep4Props {
  forWhom: string;
  patientInfo: Info;
  personalInfo: Info;
  additionalInfo: string;
  onForWhomChange: (value: string) => void;
  onAdditionalInfoChange: (value: string) => void;
}

export interface ConsultationStep5Props {
  time?: string;
  appointmentDate?: string;
  appointmentType?: string;
  doctor?: {
    name: string;
    title: string;
    avatar: string;
  };
  location?: {
    name: string;
    address: string;
    city: string;
    phone: string;
  };
}

