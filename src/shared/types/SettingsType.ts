import {
  controle_parametre,
  horaire_date_specifique,
  horaire_hebdomadaire,
  parametre_disponibilite
} from '@/domain/models'
import { rendez_vous_statut_enum } from '@/domain/models/enums'

export interface Event {
  id: number
  title: string
  type: "evenement" | "disponibilite" | "exception" | rendez_vous_statut_enum
  start: Date
  end: Date | null
  allDay: boolean
  backgroundColor: string
  color?: string
}

export interface SettingsProps {
  onClose: () => void
  onSave: (settings: parametre_disponibilite) => void
  initialSettings?: parametre_disponibilite
  initialSettingsLocal?: controle_parametre
  professionalId: number
}

export interface AvailableexceptionDateProps {
  dateSchedules: horaire_date_specifique[]
  onAddDate: (date: Date) => void
  onAddTimeSlot: (dateIndex: number) => void
  onDeleteTimeSlot: (dateIndex: number, timeSlotIndex: number) => void
  onStartTimeChange: (dateIndex: number, timeSlotIndex: number, newStartTime: string) => void
  onEndTimeChange: (dateIndex: number, timeSlotIndex: number, newEndTime: string) => void
}

export interface RenderSpecificScheduleProps {
  dateSchedules: horaire_date_specifique[]
  onAddDate: (date: Date) => void
  onAddTimeSlot: (dateIndex: number) => void
  onDeleteTimeSlot: (dateIndex: number, timeSlotIndex: number) => void
  onStartTimeChange: (dateIndex: number, timeSlotIndex: number, newStartTime: string) => void
  onEndTimeChange: (dateIndex: number, timeSlotIndex: number, newEndTime: string) => void
}

export interface RenderWeeklySelectorProps {
  schedule: horaire_hebdomadaire[]
  handleAddTimeSlot: (dayIndex: number) => void
  handleDeleteTimeSlot: (dayIndex: number, timeSlotIndex: number) => void
  handleCopyTimeSlot: (dayIndex: number) => void
  handleStartTimeChange: (dayIndex: number, timeSlotIndex: number, newStartTime: string) => void
  handleEndTimeChange: (dayIndex: number, timeSlotIndex: number, newEndTime: string) => void
}

export interface SettingsModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (settings: parametre_disponibilite) => void
  initialSettings?: parametre_disponibilite
  initialSettingsLocal?: controle_parametre
}

export interface PlanningProps {
  isMaxDays: boolean
  isMinHours: boolean
  planningType: string
  maxDays: string
  minHours: string
  errors: { [key: string]: string }
  startDate: Date | null
  endDate: Date | null
  setPlanningType: React.Dispatch<React.SetStateAction<string>>
  handleMaxDaysChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleMinHoursChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleIsMaxDays: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleIsMinHours: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleStartDateChange: (date: Date) => void
  handleEndDateChange: (date: Date) => void
}

export interface SettingRDVProps {
  isMaxReservationsPerDay: boolean
  isBreak: boolean
  maxReservationsPerDay: string
  dureePause: number
  tempPause: string
  canInviteOthers: boolean
  errors: { [key: string]: string }
  handleDureePauseChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleBreakDurationChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleMaxReservationsChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleCanInviteOthersChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleMaxReservationsPerDay: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleBreak: (e: React.ChangeEvent<HTMLInputElement>) => void
}

export interface AvailabilityProps {
  schedule: horaire_hebdomadaire[]
  availability: string
  dateSchedule: horaire_date_specifique[]
  handleAvailabilityChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleAddTimeSlot: (dayIndex: number) => void
  handleDeleteTimeSlot: (dayIndex: number, timeSlotIndex: number) => void
  handleCopyTimeSlot: (dayIndex: number) => void
  handleStartTimeChange: (dayIndex: number, timeSlotIndex: number, newStartTime: string) => void
  handleEndTimeChange: (dayIndex: number, timeSlotIndex: number, newEndTime: string) => void
  handleAddDate: (date: Date) => void
  handleAddDateTimeSlot: (dateIndex: number) => void
  handleDeleteDateTimeSlot: (dateIndex: number, timeSlotIndex: number) => void
  handleDateStartTimeChange: (
    dateIndex: number,
    timeSlotIndex: number,
    newStartTime: string
  ) => void
  handleDateEndTimeChange: (dateIndex: number, timeSlotIndex: number, newEndTime: string) => void
}
