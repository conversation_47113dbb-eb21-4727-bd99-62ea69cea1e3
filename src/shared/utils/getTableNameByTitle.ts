import { AFFECTATION_MEDICAL_TABLE_NAME } from "@/infrastructure/repositories/affectationMedicale/Constant";
import { ALLERGIE_TABLE_NAME } from "@/infrastructure/repositories/allergie/Constant";
import { ANTECEDANT_CHIRURGICAUX_TABLE_NAME } from "@/infrastructure/repositories/antecedantChirurgicaux/Constant";
import { ANTECEDANT_FAMILIAUX_TABLE_NAME } from "@/infrastructure/repositories/antecedantFamiliaux/Constant";
import { ANTECEDENT_GROSSESSE_TABLE_NAME } from "@/infrastructure/repositories/antecedentGrossesse/Constant";
import { ANTECEDENT_SOCIAUX_TABLE_NAME } from "@/infrastructure/repositories/antecedentSociaux/Constant";
import { CONDITION_GYNECOLOGIQUE_TABLE_NAME } from "@/infrastructure/repositories/conditionGynecologique/Constant";
import { DIAGNOSTIC_TABLE_NAME } from "@/infrastructure/repositories/diagnostics/Constant";
import { DISPOSITIF_MEDICAUX_TABLE_NAME } from "@/infrastructure/repositories/dispositifMedicaux/Constant";
import { MEDICAMENT_TABLE_NAME } from "@/infrastructure/repositories/medicament/Constant";
import { VACCINATION_TABLE_NAME } from "@/infrastructure/repositories/vaccination/Constant";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";

export const getTableNameByTitle = (title: string) => {
  switch (title) {
    case TITRES_CARNET_DE_SANTE.allergies:
      return ALLERGIE_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.medicaments:
      return MEDICAMENT_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.affectationMedicales:
      return AFFECTATION_MEDICAL_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.dispositifMedicaux:
      return DISPOSITIF_MEDICAUX_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux:
      return ANTECEDANT_CHIRURGICAUX_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.antecedantFamiliaux:
      return ANTECEDANT_FAMILIAUX_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.antecedentsSociaux:
      return ANTECEDENT_SOCIAUX_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.vaccination:
      return VACCINATION_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.conditionGynecologique:
      return CONDITION_GYNECOLOGIQUE_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.antecedentGrossesse:
      return ANTECEDENT_GROSSESSE_TABLE_NAME;
    case TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage:
      return DIAGNOSTIC_TABLE_NAME;
    default:
      return "";
  }
};
