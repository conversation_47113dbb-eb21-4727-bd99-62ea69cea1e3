import { Employer } from "@/domain/models";

// Fonction pour la repation de genre
export const generateGenderDistributionData = (employers: Employer[]) => {
  if (!employers) return [];

  // Créer un objet pour compter les employés par genre
  const genderCounts: Record<string, { name: string; value: number }> = {};

  // Compter les employés par genre
  employers.forEach((employee) => {
    if (employee.sexe && typeof employee.sexe === "string") {
      if (!genderCounts[employee.sexe]) {
        genderCounts[employee.sexe] = {
          name: employee.sexe,
          value: 0,
        };
      }
      genderCounts[employee.sexe].value++;
    }
  });

  // Convertir l'objet en tableau pour le graphique
  return Object.values(genderCounts).filter((item) => item.value > 0);
};
