import { View } from 'react-big-calendar'
import { format, startOfWeek, endOfWeek, addDays } from "date-fns";
import { fr } from "date-fns/locale";

export const getFormattedLabel = (date: Date, view: View) => {
    switch (view) {
      case "month":
        return format(addDays(date, -1), "MMMM yyyy", { locale: fr });
      case "week": {
        const start = startOfWeek(date, { locale: fr });
        const end = endOfWeek(date, { locale: fr });
        return `${format(addDays(start, -1), "d MMMM", { locale: fr })} - ${format(
          addDays(end, -1),
          "d MMMM yyyy",
          {
            locale: fr,
          }
        )}`;
      }
      case "day":
        return format(date, "EEEE d MMMM yyyy", { locale: fr });
      default:
        return format(date, "MMMM yyyy", { locale: fr });
    }
};