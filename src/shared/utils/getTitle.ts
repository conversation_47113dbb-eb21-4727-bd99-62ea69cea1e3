import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { action_carnet_de_sante_enum } from "@/domain/models/enums";

export const getTitle = (type: string, action: action_carnet_de_sante_enum) => {
  switch (type) {
    case TITRES_CARNET_DE_SANTE.allergies: {
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer l'allergie";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter de nouvelles allergies" : "Modifier les allergies";
    }
    case TITRES_CARNET_DE_SANTE.medicaments:
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer le médicament";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter de nouvelles médicaments" : "Modifier les médicaments";
    case TITRES_CARNET_DE_SANTE.affectationMedicales:
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer l'affectation médicale";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter de nouvelles Pathologies" : "Modifier les Pathologies";
    case TITRES_CARNET_DE_SANTE.dispositifMedicaux:
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer le dispositif médical";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter de nouveaux dispositifs medicaux" : "Modifier les dispositifs medicaux";
    case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux:
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer l'antécédent chirurgical";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter de nouveaux antécédents chirurgicaux" : "Modifier les antécédents chirurgicaux";
    case TITRES_CARNET_DE_SANTE.antecedantFamiliaux:
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer l'antécédent familial";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter de nouveaux antécédents familiaux" : "Modifier les antécédents familiaux";
    case TITRES_CARNET_DE_SANTE.antecedentsSociaux:
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer l'antécédent social";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter de nouveaux antécédents sociaux" : "Modifier les antécédents sociaux";
    case TITRES_CARNET_DE_SANTE.vaccination:
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer la vaccination";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter de nouvelles vaccinations" : "Modifier les vaccinations";
    case TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage:
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer le diagnostic";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter de nouveaux diagnostics" : "Modifier les diagnostics";
    default:
      if (action === action_carnet_de_sante_enum.suppression) {
        return "Supprimer";
      }
      return action === action_carnet_de_sante_enum.ajout ? "Ajouter" : "Modifier";
  }
}
