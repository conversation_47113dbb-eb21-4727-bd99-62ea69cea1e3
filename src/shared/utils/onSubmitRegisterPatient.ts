import { SignUpProps } from "@/domain/interfaces/repositories";
import { Patient } from "@/domain/models";
import { sexe_enum, utilisateurs_role_enum } from "@/domain/models/enums";
import { RegisterPatientState } from "@/presentation/contexts/types";

export const onSubmitRegisterPatient = async (
  values: RegisterPatientState,
  registerUser: (credentials: SignUpProps) => Promise<void>,
) => {
  try {
    const user = {
      email: values.email,
      mot_de_passe_hash: values.password,
      role: utilisateurs_role_enum.PATIENT,
      bani: false,
    };

    const patient: Omit<Patient, "id" | "utilisateur_id" | "unique_id"> = {
      nom: values.nom,
      prenom: values.prenom || "",
      sexe: values.sexe as sexe_enum,
      date_naissance: values.date_naissance,
      decede: false,
      adresse: values.adresse || "",
      district: values.district,
      commune: values.commune,
      region: values.fokontany,
      donneur_sang: false,
      nationalite: "Malagasy",
      telephone: values.telephone,
      situation_matrimonial: values.situation_matrimonial,
      nb_enfant: values.nb_enfant,
      profession: values.profession,
    };

    const contacts = values.contacts;

    await registerUser({
      role: utilisateurs_role_enum.PATIENT,
      user: user,
      userData: patient,
      contact: contacts,
    });
  } catch (error) {
    console.error(error);
    throw error;
  }
};
