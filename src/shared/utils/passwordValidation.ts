/**
 * Password validation utilities for patient registration
 */

export interface PasswordStrength {
  score: number; // 0-4 (weak to strong)
  feedback: string[];
  isValid: boolean;
}

export const validatePasswordStrength = (password: string): PasswordStrength => {
  const feedback: string[] = [];
  let score = 0;

  // Check minimum length
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push("Au moins 8 caractères");
  }

  // Check for lowercase
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push("Au moins une lettre minuscule");
  }

  // Check for uppercase
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push("Au moins une lettre majuscule");
  }

  // Check for numbers
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push("Au moins un chiffre");
  }

  // Check for special characters
  if (/[@$!%*?&]/.test(password)) {
    score += 1;
  } else {
    feedback.push("Au moins un caractère spécial (@$!%*?&)");
  }

  // Additional checks for very strong passwords
  if (password.length >= 12) {
    score += 0.5;
  }

  if (/[^a-zA-Z0-9@$!%*?&]/.test(password)) {
    score += 0.5;
  }

  const isValid = score >= 4;

  return {
    score: Math.min(score, 4),
    feedback,
    isValid,
  };
};

export const getPasswordStrengthText = (score: number): string => {
  if (score < 2) return "Très faible";
  if (score < 3) return "Faible";
  if (score < 4) return "Moyen";
  return "Fort";
};

export const getPasswordStrengthColor = (score: number): string => {
  if (score < 2) return "text-red-500";
  if (score < 3) return "text-orange-500";
  if (score < 4) return "text-yellow-500";
  return "text-green-500";
};

/**
 * Validate phone number format for Madagascar
 */
export const validateMalagasyPhone = (phone: string): boolean => {
  const localRegex = /^0(32|33|34|39)\d{7}$/;
  const intlRegex = /^\+261(32|33|34|39)\d{7}$/;
  return localRegex.test(phone) || intlRegex.test(phone);
};

/**
 * Format phone number for display
 */
export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters except +
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  if (cleaned.startsWith('+261')) {
    // International format
    const number = cleaned.slice(4);
    if (number.length === 9) {
      return `+261 ${number.slice(0, 2)} ${number.slice(2, 5)} ${number.slice(5, 7)} ${number.slice(7)}`;
    }
  } else if (cleaned.startsWith('0')) {
    // Local format
    if (cleaned.length === 10) {
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`;
    }
  }
  
  return phone; // Return original if can't format
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

/**
 * Validate name format (letters, spaces, apostrophes, hyphens only)
 */
export const validateName = (name: string): boolean => {
  const nameRegex = /^[a-zA-ZÀ-ÿ\s'-]+$/;
  return nameRegex.test(name);
};

/**
 * Validate age based on birth date
 */
export const validateAge = (birthDate: Date): { isValid: boolean; age: number } => {
  const today = new Date();
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  let actualAge = age;
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    actualAge--;
  }
  
  return {
    isValid: actualAge >= 0 && actualAge <= 120 && birthDate <= today,
    age: actualAge,
  };
};
