/**
 * Fonctions utilitaires pour gérer les paramètres de route en toute sécurité
 */

/**
 * Nettoie un paramètre de route en supprimant les espaces et en gérant les valeurs undefined/null
 * @param param - Le paramètre de route à nettoyer
 * @param defaultValue - Valeur par défaut à retourner si le paramètre est invalide (par défaut : chaîne vide)
 * @returns Paramètre de chaîne nettoyé
 */
export const sanitizeRouteParam = (param: string | undefined | null, defaultValue: string = ""): string => {
  if (param === null || param === undefined) {
    return defaultValue;
  }

  const trimmed = param.trim();
  return trimmed === "undefined" || trimmed === "null" ? defaultValue : trimmed;
};

/**
 * Valide si un paramètre de route est valide (non vide, non undefined, non null, ou représentations de chaîne de ceux-ci)
 * @param param - Le paramètre de route à valider
 * @returns True si le paramètre est valide
 */
export const isValidRouteParam = (param: string | undefined | null): boolean => {
  if (param === null || param === undefined) {
    return false;
  }

  const trimmed = param.trim();
  return trimmed.length > 0 && trimmed !== "undefined" && trimmed !== "null";
};

/**
 * Construit un chemin d'URL de recherche avec une validation appropriée des paramètres
 * @param basePath - Le chemin de base pour la route
 * @param localization - Le paramètre de localisation
 * @param speciality - Le paramètre de spécialité (optionnel)
 * @returns Chemin d'URL correctement formaté
 */
export const buildSearchPath = (
  basePath: string,
  localization: string | undefined | null,
  speciality?: string | undefined | null
): string => {
  const sanitizedLocation = sanitizeRouteParam(localization);
  const sanitizedSpeciality = sanitizeRouteParam(speciality);

  if (!isValidRouteParam(sanitizedLocation)) {
    throw new Error("Le paramètre de localisation est requis pour la recherche");
  }

  if (isValidRouteParam(sanitizedSpeciality)) {
    // Les deux paramètres sont disponibles : /medecins/:localization/:speciality
    return `${basePath}/${encodeURIComponent(sanitizedLocation)}/${encodeURIComponent(sanitizedSpeciality)}`;
  } else {
    // Seule la localisation est disponible : /medecins/:localization
    return `${basePath}/${encodeURIComponent(sanitizedLocation)}`;
  }
};

/**
 * Extrait et nettoie les paramètres de recherche à partir des paramètres de route
 * @param params - Objet des paramètres de route
 * @returns Objet avec les paramètres de localisation et de spécialité nettoyés
 */
export const extractSearchParams = (params: { localization?: string; speciality?: string }) => {
  return {
    localization: sanitizeRouteParam(params.localization),
    speciality: sanitizeRouteParam(params.speciality),
  };
};
