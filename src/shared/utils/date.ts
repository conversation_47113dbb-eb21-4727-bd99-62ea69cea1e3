import { format, parse, startOfDay } from "date-fns";
import { fr } from "date-fns/locale";

/**
 * Formatte une date pour l'affichage UI (FR): dd/MM/yyyy
 */
export function formatDisplay(date: Date | null | undefined): string {
  if (!date) return "";
  return format(date, "dd/MM/yyyy", { locale: fr });
}

/**
 * Convertit une Date en string API (yyyy-MM-dd) en normalisant à 00:00
 */
export function toApiDate(date: Date | null | undefined): string | null {
  if (!date) return null;
  return format(startOfDay(date), "yyyy-MM-dd");
}

/**
 * Parse un string API (yyyy-MM-dd) en Date locale
 */
export function fromApiDate(s: string | null | undefined): Date | null {
  if (!s) return null;
  // parse renvoie toujours une Date; si invalide, getTime() sera NaN
  const d = parse(s, "yyyy-MM-dd", new Date());
  return isNaN(d.getTime()) ? null : d;
}
