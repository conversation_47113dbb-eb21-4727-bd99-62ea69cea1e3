export const validateCurrentStep = ({
  errors,
  getValues,
  activeStep,
}: {
  errors: any;
  getValues: () => any;
  activeStep: number;
}): boolean => {
  let isValid = false;
  const values = getValues();

  switch (activeStep) {
    case 0:
      isValid = !!(
        values.email &&
        values.password &&
        values.confirmEmail &&
        values.nom &&
        values.prenom &&
        values.sexe &&
        values.date_naissance &&
        !errors.email &&
        !errors.password &&
        !errors.confirmEmail &&
        !errors.nom &&
        !errors.prenom &&
        !errors.sexe &&
        !errors.date_naissance
      );
      break;
    case 1:
      isValid = !!(
        values.district &&
        values.commune &&
        values.fokontany &&
        values.adresse &&
        !errors.district &&
        !errors.commune &&
        !errors.fokontany &&
        !errors.adresse
      );
      break;
    case 2:
      isValid = !!(
        values.telephones &&
        values.telephones.length > 0 &&
        values.telephones.every(
          (tel: any) => tel.numero && !errors?.telephones?.[0]?.numero
        )
      );
      break;
    default:
      isValid = false;
      break;
  }

  return isValid;
};
