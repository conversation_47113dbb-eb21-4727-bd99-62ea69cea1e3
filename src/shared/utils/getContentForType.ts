import { CarnetSanteDTO } from "@/domain/DTOS";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";

export const getContentForType = (
  type: string,
  data?: Partial<CarnetSanteDTO>
) => {
  switch (type) {
    case TITRES_CARNET_DE_SANTE.allergies:
      return data?.allergie?.map((a) => ({ id: a.id, nom: a.nom })) || [];
    case TITRES_CARNET_DE_SANTE.medicaments:
      return data?.medicament?.map((m) => ({ id: m.id, nom: m.nom })) || [];
    case TITRES_CARNET_DE_SANTE.affectationMedicales:
      return (
        data?.affectation_medical?.map((am) => ({
          id: am.id,
          nom: am.maladie,
        })) || []
      );
    case TITRES_CARNET_DE_SANTE.dispositifMedicaux:
      return (
        data?.dispositif_medicaux?.map((dm) => ({
          id: dm.id,
          nom: dm.nom,
        })) || []
      );
    case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux:
      return (
        data?.antecedant_chirurgicaux?.map((ac) => ({
          id: ac.id,
          nom: ac.nom,
        })) || []
      );
    case TITRES_CARNET_DE_SANTE.antecedantFamiliaux:
      return (
        data?.antecedant_familliaux?.map((af) => ({
          id: af.id,
          nom: af.nom_lien,
        })) || []
      );
    case TITRES_CARNET_DE_SANTE.antecedentsSociaux:
      return (
        data?.antecedant_sociaux?.map((as) => ({
          id: as.id,
          nom: as.nom,
        })) || []
      );
    case TITRES_CARNET_DE_SANTE.vaccination:
      return (
        data?.vaccination?.map((v) => ({ id: v.id, nom: v.nom_vaccin })) || []
      );
    case TITRES_CARNET_DE_SANTE.conditionGynecologique:
      return (
        data?.condition_gynecologique?.map((v) => ({
          id: v.id,
          nom: v.maladie,
        })) || []
      );
    case TITRES_CARNET_DE_SANTE.antecedentGrossesse:
      return (
        data?.antecedent_grossesse?.map((v) => ({ id: v.id, nom: v.parite })) ||
        []
      );
    case TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage:
      return data?.diagnostic?.map((d) => ({ id: d.id, nom: d.titre })) || [];
    default:
      return [];
  }
};
