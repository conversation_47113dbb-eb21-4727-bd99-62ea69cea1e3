// Validation
export const validateRegisterField = (
  name: string,
  value: string | Date | null | number,
  errors: { [key: string]: string },
  onValidation: (errors: { [key: string]: string }) => void
) => {
  const newErrors = { ...errors };
  switch (name) {
    case "email":
      {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        const isValidEmail = emailRegex.test(value as string);
        if (!value) {
          newErrors[name] = "L'email est requise'";
        } else if (!isValidEmail) {
          newErrors[name] = "email invalide";
        } else {
          delete newErrors[name];
        }
      }
      break;
    case "telephone":
      {
        const localRegex = /^0(32|33|34|39)\d{7}$/;
        const intlRegex = /^\+261(32|33|34|39)\d{7}$/;
        const isValidPhoneNumber =
          localRegex.test(value as string) || intlRegex.test(value as string);
        if (!value) {
          newErrors[name] = "telephone est requise'";
        } else if (!isValidPhoneNumber) {
          newErrors[name] = "telephone invalide";
        } else {
          delete newErrors[name];
        }
      }
      break;
  }

  onValidation(newErrors);
};
