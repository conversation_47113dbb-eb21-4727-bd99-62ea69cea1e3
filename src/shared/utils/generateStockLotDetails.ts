import { LotWithStockData } from "@/domain/DTOS/StockDTO.ts";

const generateStockLotDetails = (lots: LotWithStockData[]) => {
  if (!lots || lots.length === 0) return [];

  /**
   * Il y a 3 types de cas a verifier:
   * - Proche de la rupture
   * - Proche de la peremption
   * - Deja perimee
   *
   * On aurra besoin de retourner une format de donnee suivant:
   * - Nom du produit
   * - Numero du lot (si existe)
   * - Quantite restante
   * - Seuil d'alerte si existe
   * - Date d'expiration
   * - Jour restant pour l'expiration ou si deja perimmee
   */

  const lotDetails = lots.map((lot) => {
    const expirationDate = new Date(lot.date_expiration);
    const today = new Date();
    const daysUntilExpiration = Math.ceil(
      (expirationDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
    );

    return {
      ...lot,
      daysUntilExpiration,
    };
  });

  return lotDetails;
};

export default generateStockLotDetails;
