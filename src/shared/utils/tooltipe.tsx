import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

const Tooltip = ({
    children,
    content,
    position = 'top',
    delay = 200,
    className = '',
    arrow = true,
    trigger = 'hover', // 'hover', 'click', 'focus'
    size = 'medium', // 'small', 'medium', 'large', 'xl', 'auto'
    maxWidth = 'max-w-sm', // 'max-w-xs', 'max-w-sm', 'max-w-md', 'max-w-lg', 'max-w-xl', 'max-w-none'
    minWidth = '', // 'min-w-32', 'min-w-48', etc.
    autoSize = false // Si true, s'adapte automatiquement au contenu
}) => {
    const [isVisible, setIsVisible] = useState(false);
    const [actualPosition, setActualPosition] = useState(position);
    const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
    const tooltipRef = useRef(null);
    const triggerRef = useRef(null);
    const timeoutRef = useRef(null);

    useEffect(() => {
        if (isVisible && triggerRef.current) {
            const trigger = triggerRef.current;
            const rect = trigger.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

            let newPosition = position;
            let top = 0;
            let left = 0;

            // Estimation de la taille du tooltip (on utilisera des valeurs par défaut)
            const tooltipHeight = 40; // Estimation
            const tooltipWidth = 200; // Estimation
            const margin = 15;

            // Calcul de la position absolue dans le document
            const absoluteTop = rect.top + scrollTop;
            const absoluteLeft = rect.left + scrollLeft;

            // Vérification et ajustement de la position
            if (position === 'top' && rect.top - tooltipHeight < margin) {
                newPosition = 'bottom';
            } else if (position === 'bottom' && rect.bottom + tooltipHeight > viewportHeight - margin) {
                newPosition = 'top';
            } else if (position === 'left' && rect.left - tooltipWidth < margin) {
                newPosition = 'right';
            } else if (position === 'right' && rect.right + tooltipWidth > viewportWidth - margin) {
                newPosition = 'left';
            }

            // Calcul des coordonnées finales avec un espacement plus proche
            switch (newPosition) {
                case 'top':
                    top = absoluteTop - 8; // Position juste au-dessus de l'élément
                    left = absoluteLeft + rect.width / 2;
                    break;
                case 'bottom':
                    top = absoluteTop + rect.height + 8;
                    left = absoluteLeft + rect.width / 2;
                    break;
                case 'left':
                    top = absoluteTop + rect.height / 2;
                    left = absoluteLeft - 8;
                    break;
                case 'right':
                    top = absoluteTop + rect.height / 2;
                    left = absoluteLeft + rect.width + 8;
                    break;
                default:
                    top = absoluteTop - 8; // Position juste au-dessus de l'élément
                    left = absoluteLeft + rect.width / 2;
            }

            setActualPosition(newPosition);
            setTooltipPosition({ top, left });
        }
    }, [isVisible, position]);

    const showTooltip = () => {
        if (delay > 0) {
            timeoutRef.current = setTimeout(() => setIsVisible(true), delay);
        } else {
            setIsVisible(true);
        }
    };

    const hideTooltip = () => {
        clearTimeout(timeoutRef.current);
        setIsVisible(false);
    };

    const toggleTooltip = () => {
        setIsVisible(prev => !prev);
    };

    const getSizeClasses = () => {
        if (autoSize || size === 'auto') {
            // Taille automatique basée sur le contenu
            return 'px-3 py-2 text-sm whitespace-nowrap';
        }

        switch (size) {
            case 'small':
                return 'px-2 py-1 text-sm';
            case 'large':
                return 'px-5 py-3 text-sm';
            case 'xl':
                return 'px-6 py-4 text-sm';
            case 'medium':
            default:
                return 'px-3 py-2 text-sm';
        }
    };

    const getWidthClasses = () => {
        if (autoSize || size === 'auto') {
            return minWidth ? `${minWidth} w-auto` : 'w-auto';
        }

        const widthClass = maxWidth === 'max-w-none' ? '' : maxWidth;
        const minWidthClass = minWidth || '';

        return `${widthClass} ${minWidthClass}`.trim();
    };

    const getTooltipClasses = () => {
        const sizeClasses = getSizeClasses();
        const widthClasses = getWidthClasses();
        return `fixed z-[9999] ${sizeClasses} ${widthClasses} text-white 
                bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900 
                dark:from-gray-900 dark:via-gray-800 dark:to-gray-950 
                shadow-2xl shadow-slate-900/25 
                dark:shadow-black/50 dark:shadow-2xl
                rounded-xl border border-slate-600/20 
                dark:border-gray-600/30
                backdrop-blur-sm 
                pointer-events-none 
                transition-all duration-300 ease-out 
                animate-in fade-in-0 zoom-in-95 slide-in-from-top-2`;
    };

    const getTransformClasses = () => {
        switch (actualPosition) {
            case 'top':
                return 'transform -translate-x-1/2 -translate-y-full';
            case 'bottom':
                return 'transform -translate-x-1/2';
            case 'left':
                return 'transform -translate-x-full -translate-y-1/2';
            case 'right':
                return 'transform -translate-y-1/2';
            default:
                return 'transform -translate-x-1/2 -translate-y-full';
        }
    };

    const getArrowClasses = () => {
        if (!arrow) return '';

        // Flèche moderne qui s'adapte au nouveau gradient
        const arrowBase = 'absolute w-2.5 h-2.5 transform rotate-45 border border-slate-600/20 dark:border-gray-600/30';
        const gradientBg = 'bg-gradient-to-br from-slate-800 to-slate-900 dark:from-gray-900 dark:to-gray-950';

        switch (actualPosition) {
            case 'top':
                return `${arrowBase} ${gradientBg} top-full left-1/2 -translate-x-1/2 -mt-1`;
            case 'bottom':
                return `${arrowBase} ${gradientBg} bottom-full left-1/2 -translate-x-1/2 -mb-1`;
            case 'left':
                return `${arrowBase} ${gradientBg} left-full top-1/2 -translate-y-1/2 -ml-1`;
            case 'right':
                return `${arrowBase} ${gradientBg} right-full top-1/2 -translate-y-1/2 -mr-1`;
            default:
                return `${arrowBase} ${gradientBg} top-full left-1/2 -translate-x-1/2 -mt-1`;
        }
    };

    const getTriggerProps = () => {
        const baseProps = {
            ref: triggerRef,
            className: 'relative inline-block'
        };

        if (trigger === 'hover') {
            return {
                ...baseProps,
                onMouseEnter: showTooltip,
                onMouseLeave: hideTooltip,
                onFocus: showTooltip,
                onBlur: hideTooltip
            };
        } else if (trigger === 'click') {
            return {
                ...baseProps,
                onClick: toggleTooltip
            };
        } else if (trigger === 'focus') {
            return {
                ...baseProps,
                onFocus: showTooltip,
                onBlur: hideTooltip
            };
        }

        return baseProps;
    };

    const renderTooltip = () => {
        if (!isVisible) return null;

        const tooltipElement = (
            <div
                ref={tooltipRef}
                className={`${getTooltipClasses()} ${getTransformClasses()} ${className}`}
                style={{
                    top: tooltipPosition.top,
                    left: tooltipPosition.left,
                }}
                role="tooltip"
            >
                <div className={autoSize || size === 'auto' ? '' : 'break-words'}>
                    {content}
                </div>
                {arrow && <div className={getArrowClasses()} />}
            </div>
        );

        // Utilisation d'un portail pour rendre le tooltip dans le body
        return createPortal(tooltipElement, document.body);
    };

    return (
        <>
            <div {...getTriggerProps()}>
                {children}
            </div>
            {renderTooltip()}
        </>
    );
};

export default Tooltip;