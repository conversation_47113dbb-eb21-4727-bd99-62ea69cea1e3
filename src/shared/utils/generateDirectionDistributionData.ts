import { Employer } from "@/domain/models";

// Fonction pour générer les données de répartition par direction
export const generateDirectionDistributionData = (
  employers: Employer[],
  availableDirections: string[]
) => {
  if (!employers || !availableDirections) return [];

  // Créer un objet pour compter les employés par direction
  const directionCounts: Record<string, { name: string; value: number }> = {};

  // Compter les employés par direction
  employers.forEach((employee) => {
    if (employee.direction && typeof employee.direction === "string") {
      if (!directionCounts[employee.direction]) {
        directionCounts[employee.direction] = {
          name: employee.direction,
          value: 0,
        };
      }
      directionCounts[employee.direction].value++;
    }
  });

  // Convertir l'objet en tableau pour le graphique
  return Object.values(directionCounts).filter((item) => item.value > 0);
};
