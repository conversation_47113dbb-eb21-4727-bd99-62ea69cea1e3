import { creneau_horaire } from "@/domain/models";

export const calculateNextTimeSlot = (existingTimeSlots: creneau_horaire[]): creneau_horaire => {
  if (existingTimeSlots.length === 0) {
    return { heure_debut: '09:00', heure_fin: '17:00' };
  }

  // Trier les créneaux existants par heure de début
  const sortedSlots = [...existingTimeSlots].sort((a, b) =>
    a.heure_debut.localeCompare(b.heure_debut)
  );

  const lastSlot = sortedSlots[sortedSlots.length - 1];
  const [hours, minutes] = lastSlot.heure_fin.split(':').map(Number);

  // Ajouter une heure à l'heure de fin du dernier créneau
  let newStartHours = hours + 1;
  let newEndHours = newStartHours + 1;

  // Gérer le dépassement de 24h
  if (newStartHours >= 24) {
    newStartHours = 9; // Revenir à 9h le lendemain
    newEndHours = 17;
  } else if (newEndHours >= 24) {
    newEndHours = 23;
  }

  const heureDebut = `${String(newStartHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
  const heureFin = `${String(newEndHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

  return { heure_debut: heureDebut, heure_fin: heureFin };
};