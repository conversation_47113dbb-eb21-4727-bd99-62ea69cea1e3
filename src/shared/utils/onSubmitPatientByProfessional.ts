import { SignUpProps } from "@/domain/interfaces/repositories";
import { Patient } from "@/domain/models";
import {
    sexe_enum,
    utilisateurs_role_enum,
} from "@/domain/models/enums";
import { RegisterPatientState } from "@/presentation/contexts/types";

export const onSubmitPatientByProfessional = async (
    values: RegisterPatientState,
    registerUser: (credentials: SignUpProps) => Promise<void>,
    professionalId?: number
) => {
    try {
        const patient: Omit<Patient, "id" | "utilisateur_id" | "unique_id"> = {
            nom: values.nom,
            prenom: values.prenom || "",
            sexe: values.sexe as sexe_enum,
            date_naissance: values.date_naissance,
            decede: false,
            adresse: values.adresse || "",
            district: values.district,
            commune: values.commune,
            region: values.fokontany,
            donneur_sang: false,
            nationalite: "Malagasy",
            telephone: values.telephone,
            situation_matrimonial: values.situation_matrimonial,
            nb_enfant: values.nb_enfant,
            profession: values.profession,
        };
        const contacts = values.contacts
        console.log('patient', patient);

        await registerUser({
            role: utilisateurs_role_enum.PATIENT,
            userData: patient,
            contact: contacts,
            professionnelId: professionalId ? professionalId : null
        });
    } catch (error) {
        console.error(error);
        throw error;
    }
};