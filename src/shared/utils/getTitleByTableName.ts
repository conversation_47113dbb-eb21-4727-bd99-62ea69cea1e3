import { AFFECTATION_MEDICAL_TABLE_NAME } from "@/infrastructure/repositories/affectationMedicale/Constant";
import { ALLERGIE_TABLE_NAME } from "@/infrastructure/repositories/allergie/Constant";
import { ANTECEDANT_CHIRURGICAUX_TABLE_NAME } from "@/infrastructure/repositories/antecedantChirurgicaux/Constant";
import { ANTECEDANT_FAMILIAUX_TABLE_NAME } from "@/infrastructure/repositories/antecedantFamiliaux/Constant";
import { ANTECEDENT_GROSSESSE_TABLE_NAME } from "@/infrastructure/repositories/antecedentGrossesse/Constant";
import { ANTECEDENT_SOCIAUX_TABLE_NAME } from "@/infrastructure/repositories/antecedentSociaux/Constant";
import { CONDITION_GYNECOLOGIQUE_TABLE_NAME } from "@/infrastructure/repositories/conditionGynecologique/Constant";
import { DIAGNOSTIC_TABLE_NAME } from "@/infrastructure/repositories/diagnostics/Constant";
import { DISPOSITIF_MEDICAUX_TABLE_NAME } from "@/infrastructure/repositories/dispositifMedicaux/Constant";
import { MEDICAMENT_TABLE_NAME } from "@/infrastructure/repositories/medicament/Constant";
import { VACCINATION_TABLE_NAME } from "@/infrastructure/repositories/vaccination/Constant";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";

export const getTitleByTableName = (tableName: string) => {
  switch (tableName) {
    case ALLERGIE_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.allergies;
    case MEDICAMENT_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.medicaments;
    case AFFECTATION_MEDICAL_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.affectationMedicales;
    case DISPOSITIF_MEDICAUX_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.dispositifMedicaux;
    case ANTECEDANT_CHIRURGICAUX_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.antecedantChirurgicaux;
    case ANTECEDANT_FAMILIAUX_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.antecedantFamiliaux;
    case ANTECEDENT_SOCIAUX_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.antecedentsSociaux;
    case VACCINATION_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.vaccination;
    case CONDITION_GYNECOLOGIQUE_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.conditionGynecologique;
    case ANTECEDENT_GROSSESSE_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.antecedentGrossesse;
    case DIAGNOSTIC_TABLE_NAME:
      return TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage;
    default:
      return "";
  }
};
