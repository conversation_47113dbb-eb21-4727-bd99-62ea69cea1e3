import { Evenement } from '@/domain/models'
import {
    addDays,
    eachDayOfInterval,
    eachWeekOfInterval,
    eachMonthOfInterval,
    eachYearOfInterval,
    isWithinInterval,
} from 'date-fns';

export const generateRecurringEvents = (event: Evenement, viewStart: Date, viewEnd: Date) => {
    const startDate = new Date(event.date_debut);
    const endDate = new Date(event.date_fin);
    const events = [];

    // Calculer la durée de l'événement en millisecondes
    const eventDuration = endDate.getTime() - startDate.getTime();

    switch (event.repetition) {
        case 'once':
            if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                events.push({
                    id: event.id,
                    title: event.titre || 'Rendez-vous',
                    type: 'evenement',
                    start: startDate,
                    end: endDate,
                    allDay: false,
                    backgroundColor: '#FFB200', // Different color for events
                    color: '#000000'
                })
            }
            break;

        case 'allDays':
            eachDayOfInterval({ start: viewStart, end: viewEnd }).forEach(date => {
                const recurringStart = new Date(date);
                recurringStart.setHours(startDate.getHours(), startDate.getMinutes());
                const recurringEnd = new Date(recurringStart.getTime() + eventDuration);

                events.push({
                    id: event.id,
                    title: event.titre || 'Rendez-vous',
                    type: 'evenement',
                    start: recurringStart,
                    end: recurringEnd,
                    allDay: event.est_toute_la_journee,
                    backgroundColor: '#FFB200',
                    color: '#000000'
                });
            });
            break;

        case 'allWeeks':
            eachWeekOfInterval({ start: viewStart, end: viewEnd }).forEach(date => {
                const recurringStart = new Date(date);
                recurringStart.setHours(startDate.getHours(), startDate.getMinutes());
                const recurringEnd = new Date(recurringStart.getTime() + eventDuration);

                events.push({
                    id: event.id,
                    title: event.titre || 'Rendez-vous',
                    type: 'evenement',
                    start: recurringStart,
                    end: recurringEnd,
                    allDay: event.est_toute_la_journee,
                    backgroundColor: '#FFB200',
                    color: '#000000'
                });
            });
            break;

        case 'weekly':
            eachWeekOfInterval({ start: viewStart, end: viewEnd }).forEach(weekStart => {
                // Du lundi au vendredi
                for (let i = 1; i <= 5; i++) { // 1 = lundi, 5 = vendredi
                    const date = addDays(weekStart, i - 1);
                    const recurringStart = new Date(date);
                    recurringStart.setHours(startDate.getHours(), startDate.getMinutes());
                    const recurringEnd = new Date(recurringStart.getTime() + eventDuration);

                    events.push({
                        id: event.id,
                        title: event.titre || 'Rendez-vous',
                        type: 'evenement',
                        start: recurringStart,
                        end: recurringEnd,
                        allDay: event.est_toute_la_journee,
                        backgroundColor: '#FFB200',
                        color: '#000000'
                    });
                }
            });
            break;

        case 'allMonths':
            eachMonthOfInterval({ start: viewStart, end: viewEnd }).forEach(date => {
                const recurringStart = new Date(date);
                recurringStart.setHours(startDate.getHours(), startDate.getMinutes());
                const recurringEnd = new Date(recurringStart.getTime() + eventDuration);

                events.push({
                    id: event.id,
                    title: event.titre || 'Rendez-vous',
                    type: 'evenement',
                    start: recurringStart,
                    end: recurringEnd,
                    allDay: event.est_toute_la_journee,
                    backgroundColor: '#FFB200',
                    color: '#000000'
                });
            });
            break;

        case 'allYears':
            eachYearOfInterval({ start: viewStart, end: viewEnd }).forEach(date => {
                const recurringStart = new Date(date);
                recurringStart.setHours(startDate.getHours(), startDate.getMinutes());
                const recurringEnd = new Date(recurringStart.getTime() + eventDuration);

                events.push({
                    id: event.id,
                    title: event.titre || 'Rendez-vous',
                    type: 'evenement',
                    start: recurringStart,
                    end: recurringEnd,
                    allDay: event.est_toute_la_journee,
                    backgroundColor: '#FFB200',
                    color: '#000000'
                });
            });
            break;
    }

    return events;
};