import DateClass from "@/shared/utils/DateClass";
import { TimeSlotProffessionalCard } from "@/domain/DTOS";

export interface DateRange {
  min: Date;
  max: Date;
}

export class DateUtils {
  static getMinMaxDates(events: TimeSlotProffessionalCard[]): DateRange | null {
    if (events.length === 0) return null;

    let min = new Date(events[0].date);
    let max = new Date(events[0].date);

    for (let i = 1; i < events.length; i++) {
      const currentDate = new Date(events[i].date);
      if (currentDate < min) min = currentDate;
      if (currentDate > max) max = currentDate;
    }

    return { min, max };
  }

  static getNextAvailableDate(
    events: TimeSlotProffessionalCard[],
    today: Date,
  ): Date | null {
    const extremum = this.getMinMaxDates(events);

    if (!extremum) return null;
    console.clear();
    console.log("today", today);
    console.log("min", extremum.min);
    console.log("today > min", today.getTime() > extremum.min.getTime());

    return today.getTime() < extremum.min.getTime() ? extremum.min : null;
  }

  static getLastAvailableDate(
    events: TimeSlotProffessionalCard[],
  ): Date | null {
    const extremum = this.getMinMaxDates(events);

    if (!extremum) return null;
    return extremum.max;
  }

  static formatDateToISO(date: DateClass): string {
    return `${date.getAnnee()}-${String(date.getMois()).padStart(2, "0")}-${String(date.getJour()).padStart(2, "0")}`;
  }
}
