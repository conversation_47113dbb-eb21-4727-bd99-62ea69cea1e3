import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

export const calendarFormats = {
  timeGutterFormat: "HH:mm",
  eventTimeRangeFormat: ({
    start,
    end,
  }: {
    start: Date;
    end: Date;
  }) => {
    return `${format(start, "HH:mm", { locale: fr })} - ${format(end, "HH:mm", { locale: fr })}`;
  },
  dayRangeHeaderFormat: ({
    start,
    end,
  }: {
    start: Date;
    end: Date;
  }) => {
    return `${format(start, "d MMMM", { locale: fr })} - ${format(end, "d MMMM yyyy", { locale: fr })}`;
  },
  dayFormat: (date: Date) => {
    const day = format(date, "dd", { locale: fr });
    const weekday = format(date, "EEE", { locale: fr });
    return `${day} ${weekday}`;
  },
  weekdayFormat: (date: Date) => {
    return format(date, "EEE", { locale: fr });
  },
  dayHeaderFormat: (date: Date) => {
    return format(date, "dd EEE", { locale: fr });
  },
};
