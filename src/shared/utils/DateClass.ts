type DateProps = {
  jour?: number;
  mois?: number;
  annee?: number;
};

class DateClass {
  private jour: number;
  private mois: number;
  private annee: number;
  private static readonly joursSemaine = [
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
  ];

  constructor(annee: number = 2024, mois: number = 1, jour: number = 1) {
    this.annee = annee;
    this.mois = mois;
    this.jour = jour;

    // Verification
    if (mois > 12 || jour > this.jourMax(this.mois, this.annee)) {
      console.log("Date invalide", this.toString());
      this.jour = 1;
      this.mois = 1;
      this.annee = 1;
    }
  }

  public toLocaleDateString(
    locale: string,
    options?: Intl.DateTimeFormatOptions,
  ): string {
    const date = new Date(this.annee, this.mois - 1, this.jour); // Create a native Date object
    return date.toLocaleDateString(locale, options); // Use the native method
  }

  public getJour(): number {
    return this.jour;
  }

  public setJour(jour: number): void {
    if (this.mois === 2) {
      if (this.annee % 4 === 0) {
        if (jour > 29) return;
      } else {
        if (jour > 28) return;
      }
    }
    if (jour > 31) return;
    this.jour = jour;
  }

  public getMois(): number {
    return this.mois;
  }

  public setMois(mois: number): void {
    if (mois > 12) return;
    this.mois = mois;
  }

  public getAnnee(): number {
    return this.annee;
  }

  public setAnnee(annee: number): void {
    this.annee = annee;
  }

  private jourMax(mois: number, annee: number): number {
    if ((mois < 8 && mois % 2 !== 0) || (mois >= 8 && mois % 2 === 0)) {
      return 31;
    } else if (mois !== 2) {
      return 30;
    } else {
      return annee % 4 === 0 ? 29 : 28;
    }
  }

  public lendemain(): DateClass {
    const temp = new DateClass(this.annee, this.mois, this.jour);
    temp.jour++;
    if (temp.jour > this.jourMax(this.mois, this.annee)) {
      temp.jour = 1;
      temp.mois++;
      if (temp.mois > 12) {
        temp.mois = 1;
        temp.annee++;
      }
    }
    return temp;
  }

  public veille(): DateClass {
    const out = new DateClass(this.annee, this.mois, this.jour);

    if (out.getJour() === 1) {
      if (out.getMois() === 1) {
        out.setAnnee(out.getAnnee() - 1);
        out.setMois(12);
        out.setJour(this.jourMax(out.getMois(), out.getAnnee()));
      } else {
        out.setMois(out.getMois() - 1);
        out.setJour(this.jourMax(out.getMois(), out.getAnnee()));
      }
    } else {
      out.setJour(out.getJour() - 1);
    }

    return out;
  }

  private dateEnJour(): number {
    let totalJours = 0;
    for (let i = 1; i < this.annee; i++) {
      totalJours += i % 4 === 0 ? 366 : 365;
    }
    for (let i = 1; i < this.mois; i++) {
      totalJours += this.jourMax(i, this.annee);
    }
    totalJours += this.jour;
    return totalJours;
  }

  public difference(autre: DateClass): number {
    return this.dateEnJour() - autre.dateEnJour();
  }

  private nombreFormat(n: number): string {
    return n < 10 ? `0${n}` : `${n}`;
  }

  public toString(): string {
    return `${this.annee}-${this.nombreFormat(this.mois)}-${
      this.nombreFormat(this.jour)
    }`;
  }

  public toClassicDate(): Date {
    const out = new Date(this.annee, this.mois - 1, this.jour);
    return out;
  }

  get dayName(): string {
    const date = new Date(this.annee, this.mois - 1, this.jour);
    return DateClass.joursSemaine[date.getDay()];
  }

  get day(): string {
    const monthName = this.toLocaleDateString("fr-FR", { month: "short" });
    return `${this.jour} ${
      monthName.charAt(0).toUpperCase() + monthName.slice(1).toLowerCase()
    }`;
  }
}

export default DateClass;
