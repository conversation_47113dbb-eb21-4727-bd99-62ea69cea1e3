export const responsiveTimeSchedule = (setDateCount:(value: React.SetStateAction<number>) => void) => {
    if (window.innerWidth >= 1150) {
        setDateCount(4);
    } else if (window.innerWidth < 1150 && window.innerWidth >= 1000) {
        setDateCount(3);
    } else if (window.innerWidth < 1000 && window.innerWidth >= 900) {
        setDateCount(2);
    } else if (window.innerWidth < 900 && window.innerWidth >= 767) {
        setDateCount(1);
    } else if (window.innerWidth < 767 && window.innerWidth >= 650) {
        setDateCount(4);
    } else if (window.innerWidth < 650 && window.innerWidth >= 550) {
        setDateCount(3);
    } else if (window.innerWidth < 550 && window.innerWidth >= 450) {
        setDateCount(2);
    } else if (window.innerWidth < 450) {
        setDateCount(1);
    }
}

export const switchDateCount = (dateCount:number, setGrid: (value: React.SetStateAction<string>) => void)=>{
    switch (dateCount) {
        case 1:
          setGrid("grid-cols-2");
          break;
        case 2:
          setGrid("grid-cols-3");
          break;
        case 3:
          setGrid("grid-cols-4");
          break;
        case 4:
          setGrid("grid-cols-5");
          break;
      }
}