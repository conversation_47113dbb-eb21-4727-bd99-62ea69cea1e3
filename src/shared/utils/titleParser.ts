/**
 * Interface pour représenter un titre parsé avec son contenu de tooltip
 */
export interface ParsedTitle {
  /** Le titre principal sans les parenthèses */
  mainTitle: string;
  /** Le contenu entre parenthèses pour le tooltip */
  tooltipContent: string | null;
}

/**
 * Parse un titre de carnet de santé pour séparer le titre principal du contenu entre parenthèses
 * 
 * @param fullTitle - Le titre complet avec potentiellement du contenu entre parenthèses
 * @returns Un objet contenant le titre principal et le contenu du tooltip
 * 
 * @example
 * ```typescript
 * const result = parseTitleWithTooltip("Allergies (Réactions allergiques connues et contre-indication)");
 * // result.mainTitle = "Allergies"
 * // result.tooltipContent = "Réactions allergiques connues et contre-indication"
 * ```
 */
export const parseTitleWithTooltip = (fullTitle: string): ParsedTitle => {
  // Expression régulière pour capturer le titre principal et le contenu entre parenthèses
  const regex = /^([^(]+?)\s*\(([^)]+)\)\s*$/;
  const match = fullTitle.match(regex);

  if (match) {
    return {
      mainTitle: match[1].trim(),
      tooltipContent: match[2].trim()
    };
  }

  // Si aucune parenthèse n'est trouvée, retourner le titre complet
  return {
    mainTitle: fullTitle.trim(),
    tooltipContent: null
  };
};

/**
 * Vérifie si un titre contient du contenu entre parenthèses
 * 
 * @param title - Le titre à vérifier
 * @returns true si le titre contient des parenthèses avec du contenu
 */
export const hasTooltipContent = (title: string): boolean => {
  const regex = /\([^)]+\)/;
  return regex.test(title);
};
