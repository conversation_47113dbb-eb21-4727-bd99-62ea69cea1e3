import React from 'react';

/**
 * Met en surbrillance les occurrences d'un terme de recherche dans un texte
 * @param text - Le texte dans lequel chercher
 * @param searchTerm - Le terme à mettre en surbrillance
 * @returns Le texte avec les occurrences du terme en surbrillance
 */
export const highlightText = (text: string, searchTerm: string) => {
  if (!searchTerm) return text;
  
  const parts = text.split(new RegExp(`(${searchTerm})`, 'gi'));
  return parts.map((part, index) => 
    part.toLowerCase() === searchTerm.toLowerCase() ? 
      <span key={index} style={{ color: '#00A4A6' }}>{part}</span> : 
      part
  );
};
