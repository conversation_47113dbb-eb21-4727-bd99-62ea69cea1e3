import DateClass from "@/shared/utils/DateClass";

/**
 * Utilitaires pour la gestion des créneaux horaires
 */

/**
 * Vérifie si un créneau horaire est dans le passé
 * @param timeSlot - Le créneau horaire au format "HH:MM - HH:MM"
 * @param dayDate - L'objet DateClass représentant la date du jour
 * @returns true si le créneau est passé, false sinon
 */
export const isTimeSlotPast = (timeSlot: string, dayDate: DateClass): boolean => {
  // Les créneaux vides ne sont jamais considérés comme passés
  if (timeSlot === "-") return false;
  
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const slotDate = new Date(dayDate.getAnnee(), dayDate.getMois() - 1, dayDate.getJour());
  
  // Si la date du créneau est dans le futur, le créneau n'est pas passé
  if (slotDate > today) return false;
  
  // Si la date du créneau est dans le passé, le créneau est passé
  if (slotDate < today) return true;
  
  // Si c'est aujourd'hui, vérifier l'heure
  const [startTime] = timeSlot.split(" - ");
  const [hours, minutes] = startTime.split(":").map(Number);
  const slotDateTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hours, minutes);
  
  return slotDateTime < now;
};

/**
 * Filtre les créneaux horaires pour ne garder que ceux qui ne sont pas passés
 * @param timeSlots - Tableau des créneaux horaires
 * @param dayDate - L'objet DateClass représentant la date du jour
 * @returns Tableau des créneaux horaires non passés
 */
export const filterFutureTimeSlots = (timeSlots: string[], dayDate: DateClass): string[] => {
  return timeSlots.filter((timeSlot) => !isTimeSlotPast(timeSlot, dayDate));
};

/**
 * Vérifie si tous les créneaux d'un jour sont passés
 * @param timeSlots - Tableau des créneaux horaires
 * @param dayDate - L'objet DateClass représentant la date du jour
 * @returns true si tous les créneaux sont passés, false sinon
 */
export const areAllTimeSlotsEmpty = (timeSlots: string[], dayDate: DateClass): boolean => {
  const futureSlots = filterFutureTimeSlots(timeSlots, dayDate);
  return futureSlots.length === 0;
};
