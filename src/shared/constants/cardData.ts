import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { getContentForType } from "@/shared/utils/getContentForType";
import { CarnetSanteDTO } from "@/domain/DTOS";
import { active_tab_enum, sexe_enum } from "@/domain/models/enums";
import { getTitleByTableName } from "../utils/getTitleByTableName";

export const getDataCard = (data: Partial<CarnetSanteDTO>, type: string, tableConcernes?: string[]) => {
  switch (type) {
    case active_tab_enum.consultationMedicale:
      return getDataConsultationMedicale(data, tableConcernes)
    case sexe_enum.femme:
      return getDataByTitles(data, titlesFemme)
    case sexe_enum.homme:
      return getDataByTitles(data, titlesHomme)
    case TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage:
      return getDataByTitles(data, [TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage])
    default:
      return[];
  }
};

const getDataConsultationMedicale = (data: Partial<CarnetSanteDTO>, tableConcernes: string[]) => {
  return tableConcernes.map((tableConcerne) => {
    const title = getTitleByTableName(tableConcerne);
    return{
      title: title,
      content: getContentForType(title, data),
    }
  })
}

const getDataByTitles = (data: Partial<CarnetSanteDTO>, titles: string[]) => {
  return titles.map((title) => {
    return{
      title: title,
      content: getContentForType(title, data),
    }
  })
}

const titlesHomme = [
  TITRES_CARNET_DE_SANTE.allergies,
  TITRES_CARNET_DE_SANTE.medicaments,
  TITRES_CARNET_DE_SANTE.affectationMedicales,
  TITRES_CARNET_DE_SANTE.dispositifMedicaux,
  TITRES_CARNET_DE_SANTE.antecedantChirurgicaux,
  TITRES_CARNET_DE_SANTE.antecedantFamiliaux,
  TITRES_CARNET_DE_SANTE.antecedentsSociaux,
  TITRES_CARNET_DE_SANTE.vaccination,
];

const titlesFemme = [
  TITRES_CARNET_DE_SANTE.allergies,
  TITRES_CARNET_DE_SANTE.medicaments,
  TITRES_CARNET_DE_SANTE.affectationMedicales,
  TITRES_CARNET_DE_SANTE.dispositifMedicaux,
  TITRES_CARNET_DE_SANTE.antecedantChirurgicaux,
  TITRES_CARNET_DE_SANTE.antecedantFamiliaux,
  TITRES_CARNET_DE_SANTE.antecedentsSociaux,
  TITRES_CARNET_DE_SANTE.vaccination,
  TITRES_CARNET_DE_SANTE.antecedentGrossesse,
  TITRES_CARNET_DE_SANTE.conditionGynecologique,
];
