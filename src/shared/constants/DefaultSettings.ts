import { AvailabilitySettingsDTO } from "@/domain/DTOS";
import { DEFAULT_HORAIRE_HEBDOMADAIRE } from "@/shared/constants/DefaultHoraireHebdomadaire";

export const DEFAULT_SETTINGS: AvailabilitySettingsDTO = {
    id_professionnel: 0,
    horaire_hebdomadaire: DEFAULT_HORAIRE_HEBDOMADAIRE,
    type: "hebdomadaire",
    horaire_date_specifique: [],
    duree_pause: 0,
    max_rdv_par_jours: 0,
    peut_inviter_autre: false,
    date_debut: null,
    date_fin: null,
    temps_moyen_consulation: 30
};