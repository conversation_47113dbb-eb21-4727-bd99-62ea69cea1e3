export const AVAILABILITY_CONSTANTS = {
  GRID_BREAKPOINTS: {
    LARGE: 340,
    MEDIUM: 290,
    SMALL: 230,
  },
  DATE_COUNT: {
    LARGE: 5,
    MEDIUM: 4,
    SMALL: 3,
    XSMALL: 2,
  },
  VISIBLE_SLOTS: 3,
  AVAILABILITY_ROW_COUNT: 4
} as const;

export const AVAILABILITY_CLASSNAMES = {
  CONTAINER: 'overflow-hidden bg-white border rounded-lg relative min-h-[200px]',
  NAVIGATION: 'flex justify-between items-center absolute -left-2 -right-2 top-11 z-10',
  BUTTON: {
    NAV: 'flex items-center justify-center w-8 h-8 bg-gray-400 text-white',
    NAV_PREV: 'hover:bg-meddoc-primary disabled:cursor-not-allowed',
    NAV_NEXT: 'hover:bg-gray-100',
    TIMESLOT: 'block bg-gray-100 text-gray-700 py-2 text-sm hover:bg-gray-200 transition-colors',
    NEXT_AVAILABILITY: 'flex gap-2 bg-white text-gray-600 px-5 py-3 shadow-md hover:bg-meddoc-primary hover:text-white',
  },
  EMPTY_SLOT: 'block bg-gray-50 text-gray-400 py-2 text-sm cursor-not-allowed',
} as const;
