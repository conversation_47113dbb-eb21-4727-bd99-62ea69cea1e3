import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Pi<PERSON>, 
  <PERSON>, 
  <PERSON>ethos<PERSON>, 
  Scissors, 
  Users, 
  Home, 
  Shield, 
  Search, 
  Baby,
  User
} from "lucide-react";
import { TITRES_CARNET_DE_SANTE } from "./TitreCarnetDeSante";

/**
 * Configuration visuelle pour chaque type de carnet de santé
 * Utilise des icônes Lucide React et des classes Tailwind CSS cohérentes
 */
export interface CarnetVisualConfig {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  colorClass: string;
  backgroundClass: string;
  borderClass: string;
  avatarClass: string;
  hoverClass: string;
}

/**
 * Mapping des icônes et couleurs par type de carnet
 * Système cohérent avec des couleurs harmonieuses et des icônes appropriées
 */
const CARNET_VISUAL_CONFIGS: Record<string, CarnetVisualConfig> = {
  // Allergies - Orange (alerte/attention)
  [TITRES_CARNET_DE_SANTE.allergies]: {
    icon: Alert<PERSON>riangle,
    colorClass: "text-orange-600 dark:text-orange-400",
    backgroundClass: "bg-orange-50 dark:bg-orange-900/20",
    borderClass: "border-orange-200 dark:border-orange-800",
    avatarClass: "bg-gradient-to-br from-orange-500 to-orange-600",
    hoverClass: "hover:bg-orange-100 dark:hover:bg-orange-900/30"
  },

  // Médicaments - Bleu (médical/professionnel)
  [TITRES_CARNET_DE_SANTE.medicaments]: {
    icon: Pill,
    colorClass: "text-blue-600 dark:text-blue-400",
    backgroundClass: "bg-blue-50 dark:bg-blue-900/20",
    borderClass: "border-blue-200 dark:border-blue-800",
    avatarClass: "bg-gradient-to-br from-blue-500 to-blue-600",
    hoverClass: "hover:bg-blue-100 dark:hover:bg-blue-900/30"
  },

  // Pathologies - Rouge (sérieux/critique)
  [TITRES_CARNET_DE_SANTE.affectationMedicales]: {
    icon: Heart,
    colorClass: "text-red-600 dark:text-red-400",
    backgroundClass: "bg-red-50 dark:bg-red-900/20",
    borderClass: "border-red-200 dark:border-red-800",
    avatarClass: "bg-gradient-to-br from-red-500 to-red-600",
    hoverClass: "hover:bg-red-100 dark:hover:bg-red-900/30"
  },

  // Dispositifs médicaux - Violet (technologie médicale)
  [TITRES_CARNET_DE_SANTE.dispositifMedicaux]: {
    icon: Stethoscope,
    colorClass: "text-violet-600 dark:text-violet-400",
    backgroundClass: "bg-violet-50 dark:bg-violet-900/20",
    borderClass: "border-violet-200 dark:border-violet-800",
    avatarClass: "bg-gradient-to-br from-violet-500 to-violet-600",
    hoverClass: "hover:bg-violet-100 dark:hover:bg-violet-900/30"
  },

  // Antécédents chirurgicaux - Cyan (intervention médicale)
  [TITRES_CARNET_DE_SANTE.antecedantChirurgicaux]: {
    icon: Scissors,
    colorClass: "text-cyan-600 dark:text-cyan-400",
    backgroundClass: "bg-cyan-50 dark:bg-cyan-900/20",
    borderClass: "border-cyan-200 dark:border-cyan-800",
    avatarClass: "bg-gradient-to-br from-cyan-500 to-cyan-600",
    hoverClass: "hover:bg-cyan-100 dark:hover:bg-cyan-900/30"
  },

  // Antécédents familiaux - Emerald (hérédité/famille)
  [TITRES_CARNET_DE_SANTE.antecedantFamiliaux]: {
    icon: Users,
    colorClass: "text-emerald-600 dark:text-emerald-400",
    backgroundClass: "bg-emerald-50 dark:bg-emerald-900/20",
    borderClass: "border-emerald-200 dark:border-emerald-800",
    avatarClass: "bg-gradient-to-br from-emerald-500 to-emerald-600",
    hoverClass: "hover:bg-emerald-100 dark:hover:bg-emerald-900/30"
  },

  // Antécédents sociaux - Vert (environnement/vie)
  [TITRES_CARNET_DE_SANTE.antecedentsSociaux]: {
    icon: Home,
    colorClass: "text-green-600 dark:text-green-400",
    backgroundClass: "bg-green-50 dark:bg-green-900/20",
    borderClass: "border-green-200 dark:border-green-800",
    avatarClass: "bg-gradient-to-br from-green-500 to-green-600",
    hoverClass: "hover:bg-green-100 dark:hover:bg-green-900/30"
  },

  // Vaccination - Indigo (protection/prévention)
  [TITRES_CARNET_DE_SANTE.vaccination]: {
    icon: Shield,
    colorClass: "text-indigo-600 dark:text-indigo-400",
    backgroundClass: "bg-indigo-50 dark:bg-indigo-900/20",
    borderClass: "border-indigo-200 dark:border-indigo-800",
    avatarClass: "bg-gradient-to-br from-indigo-500 to-indigo-600",
    hoverClass: "hover:bg-indigo-100 dark:hover:bg-indigo-900/30"
  },

  // Tests médicaux et dépistage - Teal (diagnostic/analyse)
  [TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage]: {
    icon: Search,
    colorClass: "text-teal-600 dark:text-teal-400",
    backgroundClass: "bg-teal-50 dark:bg-teal-900/20",
    borderClass: "border-teal-200 dark:border-teal-800",
    avatarClass: "bg-gradient-to-br from-teal-500 to-teal-600",
    hoverClass: "hover:bg-teal-100 dark:hover:bg-teal-900/30"
  },

  // Antécédents grossesse - Purple (maternité)
  [TITRES_CARNET_DE_SANTE.antecedentGrossesse]: {
    icon: Baby,
    colorClass: "text-purple-600 dark:text-purple-400",
    backgroundClass: "bg-purple-50 dark:bg-purple-900/20",
    borderClass: "border-purple-200 dark:border-purple-800",
    avatarClass: "bg-gradient-to-br from-purple-500 to-purple-600",
    hoverClass: "hover:bg-purple-100 dark:hover:bg-purple-900/30"
  },

  // Condition gynécologique - Rose (santé féminine)
  [TITRES_CARNET_DE_SANTE.conditionGynecologique]: {
    icon: User,
    colorClass: "text-pink-600 dark:text-pink-400",
    backgroundClass: "bg-pink-50 dark:bg-pink-900/20",
    borderClass: "border-pink-200 dark:border-pink-800",
    avatarClass: "bg-gradient-to-br from-pink-500 to-pink-600",
    hoverClass: "hover:bg-pink-100 dark:hover:bg-pink-900/30"
  }
};

/**
 * Configuration par défaut pour les carnets non mappés
 */
const DEFAULT_CONFIG: CarnetVisualConfig = {
  icon: Heart,
  colorClass: "text-gray-600 dark:text-gray-400",
  backgroundClass: "bg-gray-50 dark:bg-gray-900/20",
  borderClass: "border-gray-200 dark:border-gray-800",
  avatarClass: "bg-gradient-to-br from-gray-500 to-gray-600",
  hoverClass: "hover:bg-gray-100 dark:hover:bg-gray-900/30"
};

/**
 * Récupère la configuration visuelle pour un type de carnet donné
 * @param carnetTitle - Le titre du carnet de santé
 * @returns Configuration visuelle (icône, couleurs, classes CSS)
 */
export const getCarnetVisualConfig = (carnetTitle: string): CarnetVisualConfig => {
  return CARNET_VISUAL_CONFIGS[carnetTitle] || DEFAULT_CONFIG;
};

/**
 * Récupère toutes les configurations disponibles
 * @returns Objet contenant toutes les configurations visuelles
 */
export const getAllCarnetConfigs = (): Record<string, CarnetVisualConfig> => {
  return CARNET_VISUAL_CONFIGS;
};

/**
 * Vérifie si un carnet a une configuration personnalisée
 * @param carnetTitle - Le titre du carnet de santé
 * @returns true si le carnet a une configuration personnalisée
 */
export const hasCustomConfig = (carnetTitle: string): boolean => {
  return carnetTitle in CARNET_VISUAL_CONFIGS;
};
