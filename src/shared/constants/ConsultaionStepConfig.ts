/**
 * Configuration des champs par étape pour la validation
 */
export const CONULTATION_STEP_FIELDS = {
  0: ["categorie", "consultationMotif", "speciality"] as const,
  1: [] as const,
  2: [] as const,
  3: ["forWhom"] as const,
  4: [] as const,
} as const;

/**
 * Messages d'aide pour chaque étape
 */
export const STEP_HELP_MESSAGES = {
  0: "Renseignez vos informations personnelles et votre adresse complète.",
  1: "Créez vos identifiants de connexion et renseignez vos coordonnées.",
} as const;

/**
 * Titres des étapes
 */
export const STEP_TITLES = {
  0: "Informations personnelles",
  1: "Identifiants et contact",
} as const;

/**
 * Type pour les clés des étapes
 */
export type StepKey = keyof typeof CONULTATION_STEP_FIELDS;

/**
 * Type pour les champs d'une étape spécifique
 */
export type StepFields<T extends StepKey> =
  (typeof CONULTATION_STEP_FIELDS)[T][number];

/**
 * Type union de tous les champs de toutes les étapes
 */
export type AllStepFields = StepFields<0> | StepFields<3>;
