# Exemple d'utilisation du système de recherche de professionnels

## Configuration et injection de dépendance

```typescript
import SearchProfessionalsRepository from "@/infrastructure/repositories/SearchProfessionalsRepository";
import SearchProfessionalsUsecase from "@/domain/usecases/professional/GetProfessionnalInformations/SearchProfessionalsUsecase";

// Création du repository
const repository = new SearchProfessionalsRepository();

// Injection de dépendance dans le usecase
const searchUsecase = new SearchProfessionalsUsecase(repository);
```

## Exemples de recherche

### 1. Recherche par spécialité

```typescript
const cardiologues = await searchUsecase.execute({
  name: "cardiologue",
  localization: null,
  today: new Date().toISOString(),
  page: 0,
  limit: 20
});

console.log(`Trouvé ${cardiologues.length} cardiologues`);
cardiologues.forEach(pro => {
  console.log(`${pro.nom} ${pro.prenom} - ${pro.specialite.map(s => s.nom_specialite).join(', ')}`);
});
```

### 2. Recherche par nom de professionnel

```typescript
const docteurRakoto = await searchUsecase.execute({
  name: "Rakoto",
  localization: null,
  today: new Date().toISOString(),
  page: 0,
  limit: 10
});
```

### 3. Recherche par localisation

```typescript
const professionnelsAntananarivo = await searchUsecase.execute({
  name: null,
  localization: "Antananarivo",
  today: new Date().toISOString(),
  page: 0,
  limit: 50
});
```

### 4. Recherche combinée

```typescript
const pediatresAntananarivo = await searchUsecase.execute({
  name: "pédiatre",
  localization: "Antananarivo",
  today: new Date().toISOString(),
  page: 0,
  limit: 15
});
```

### 5. Recherche par proximité géographique (si PostGIS configuré)

```typescript
// Recherche dans un rayon de 10km autour d'Antananarivo
const professionnelsProches = await repository.searchByProximity(
  -18.8792, // latitude
  47.5079,  // longitude
  10,       // rayon en km
  20        // limite de résultats
);
```

### 6. Recherche intelligente avec géolocalisation

```typescript
const resultatsIntelligents = await repository.searchWithGeolocation({
  name: "dentiste",
  localization: "Antananarivo",
  latitude: -18.8792,
  longitude: 47.5079,
  radiusKm: 15,
  page: 0,
  limit: 25
});
```

## Utilisation dans un composant React

```typescript
import React, { useState, useEffect } from 'react';
import { ProfessionalCardDTO } from '@/domain/DTOS/ProfessionalDTO';
import SearchProfessionalsRepository from "@/infrastructure/repositories/SearchProfessionalsRepository";
import SearchProfessionalsUsecase from "@/domain/usecases/professional/GetProfessionnalInformations/SearchProfessionalsUsecase";

const ProfessionalSearchComponent: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [location, setLocation] = useState('');
  const [professionals, setProfessionals] = useState<ProfessionalCardDTO[]>([]);
  const [loading, setLoading] = useState(false);

  // Configuration du usecase
  const repository = new SearchProfessionalsRepository();
  const searchUsecase = new SearchProfessionalsUsecase(repository);

  const handleSearch = async () => {
    setLoading(true);
    try {
      const results = await searchUsecase.execute({
        name: searchTerm || null,
        localization: location || null,
        today: new Date().toISOString(),
        page: 0,
        limit: 20
      });
      setProfessionals(results);
    } catch (error) {
      console.error('Erreur lors de la recherche:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="professional-search">
      <div className="search-form">
        <input
          type="text"
          placeholder="Nom, spécialité ou mot-clé..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <input
          type="text"
          placeholder="Localisation..."
          value={location}
          onChange={(e) => setLocation(e.target.value)}
        />
        <button onClick={handleSearch} disabled={loading}>
          {loading ? 'Recherche...' : 'Rechercher'}
        </button>
      </div>

      <div className="results">
        {professionals.map((pro) => (
          <div key={pro.id} className="professional-card">
            <h3>{pro.nom} {pro.prenom}</h3>
            <p>{pro.specialite.map(s => s.nom_specialite).join(', ')}</p>
            <p>{pro.adresse}</p>
            <p>Créneaux disponibles: {pro.disponibilite?.length || 0}</p>
            {pro.nouveau_patient_acceptes && (
              <span className="badge">Accepte nouveaux patients</span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProfessionalSearchComponent;
```

## Hook personnalisé pour la recherche

```typescript
import { useState, useCallback } from 'react';
import { ProfessionalCardDTO } from '@/domain/DTOS/ProfessionalDTO';
import SearchProfessionalsRepository from "@/infrastructure/repositories/SearchProfessionalsRepository";
import SearchProfessionalsUsecase from "@/domain/usecases/professional/GetProfessionnalInformations/SearchProfessionalsUsecase";

interface SearchParams {
  name?: string;
  localization?: string;
  page?: number;
  limit?: number;
}

export const useProfessionalSearch = () => {
  const [professionals, setProfessionals] = useState<ProfessionalCardDTO[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Configuration du usecase (pourrait être injecté via un contexte)
  const repository = new SearchProfessionalsRepository();
  const searchUsecase = new SearchProfessionalsUsecase(repository);

  const search = useCallback(async (params: SearchParams) => {
    setLoading(true);
    setError(null);
    
    try {
      const results = await searchUsecase.execute({
        name: params.name || null,
        localization: params.localization || null,
        today: new Date().toISOString(),
        page: params.page || 0,
        limit: params.limit || 20
      });
      
      setProfessionals(results);
      return results;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la recherche';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [searchUsecase]);

  const searchByProximity = useCallback(async (
    latitude: number,
    longitude: number,
    radiusKm: number = 10,
    limit: number = 20
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const results = await repository.searchByProximity(latitude, longitude, radiusKm, limit);
      setProfessionals(results);
      return results;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la recherche par proximité';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [repository]);

  return {
    professionals,
    loading,
    error,
    search,
    searchByProximity,
    clearResults: () => setProfessionals([]),
    clearError: () => setError(null)
  };
};
```

## Gestion des erreurs

```typescript
try {
  const results = await searchUsecase.execute({
    name: "cardiologue",
    localization: "Antananarivo",
    today: new Date().toISOString(),
    page: 0,
    limit: 20
  });
  
  if (results.length === 0) {
    console.log("Aucun professionnel trouvé pour ces critères");
  }
} catch (error) {
  if (error.message.includes('network')) {
    console.error("Erreur de connexion à la base de données");
  } else {
    console.error("Erreur lors de la recherche:", error.message);
  }
}
```

## Optimisations recommandées

1. **Debouncing** pour les recherches en temps réel
2. **Cache** pour les résultats fréquents
3. **Pagination infinie** pour de grandes listes
4. **Géolocalisation automatique** de l'utilisateur
5. **Filtres avancés** (spécialités, disponibilités, etc.)
