# Système de Recherche de Professionnels de Santé avec Géolocalisation

## Vue d'ensemble

Ce document décrit l'implémentation du système de recherche performant pour les professionnels de santé, incluant la recherche par géolocalisation et les critères textuels.

## Architecture

### Composants principaux

1. **SearchProfessionalsRepository** : Repository contenant uniquement les requêtes SQL
2. **SearchProfessionalsUsecase** : UseCase avec la logique métier et transformation des données
3. **ProfessionalSearchMapper** : Mapper pour transformer les données vers le format frontend
4. **ProfessionalCardDTO** : Type de données retourné pour l'affichage

### Séparation des responsabilités

- **Repository** : Requêtes SQL pures, sans logique métier
- **UseCase** : Logique métier, filtrage, tri par pertinence
- **Mapper** : Transformation des données entre formats
- **DTO** : Contrats de données typés

## Fonctionnalités de recherche

### Recherche textuelle

La recherche textuelle supporte :
- **Nom/Prénom** du professionnel
- **Raison sociale**
- **Numéro d'ordre**
- **Spécialités** (jointure avec table specialites_professionnel)
- **Mots-clés** (jointure avec table mot_cles_professionnel)
- **Établissements** (nom, responsables)

### Recherche géographique

#### Recherche textuelle par localisation
- Adresse du professionnel
- Région, district, commune, fokontany
- Adresses des établissements

#### Recherche par proximité (PostGIS)
- Recherche dans un rayon défini (km)
- Basée sur les coordonnées GPS (latitude/longitude)
- Tri par distance

## Configuration de la base de données

### Extension PostGIS (optionnelle)

Pour activer la recherche par proximité géographique, vous devez :

1. **Activer l'extension PostGIS** :
```sql
CREATE EXTENSION IF NOT EXISTS postgis;
```

2. **Créer la fonction de recherche par proximité** :
```sql
CREATE OR REPLACE FUNCTION search_professionals_by_proximity(
  search_lat FLOAT,
  search_lng FLOAT,
  radius_km FLOAT DEFAULT 10,
  result_limit INT DEFAULT 50
)
RETURNS TABLE (
  id INT,
  utilisateur_id INT,
  titre professionnels_titre_enum,
  nom VARCHAR(255),
  prenom VARCHAR(255),
  sexe sexe_enum,
  numero_ordre VARCHAR(50),
  raison_sociale VARCHAR(255),
  nif VARCHAR(255),
  stat VARCHAR(255),
  presentation_generale TEXT,
  temps_moyen_consulation INT,
  types_consultation professionnels_types_consultation_enum,
  modes_paiement_acceptes TEXT,
  nouveau_patient_acceptes BOOLEAN,
  adresse TEXT,
  region VARCHAR(255),
  district VARCHAR(255),
  commune VARCHAR(255),
  fokontany VARCHAR(255),
  informations_acces TEXT,
  geolocalisation POINT,
  distance_km FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.*,
    ST_Distance(
      ST_GeogFromText('POINT(' || search_lng || ' ' || search_lat || ')'),
      ST_GeogFromText('POINT(' || ST_X(p.geolocalisation) || ' ' || ST_Y(p.geolocalisation) || ')')
    ) / 1000 AS distance_km
  FROM professionnels p
  WHERE p.geolocalisation IS NOT NULL
    AND ST_DWithin(
      ST_GeogFromText('POINT(' || search_lng || ' ' || search_lat || ')'),
      ST_GeogFromText('POINT(' || ST_X(p.geolocalisation) || ' ' || ST_Y(p.geolocalisation) || ')'),
      radius_km * 1000
    )
  ORDER BY distance_km ASC, p.nouveau_patient_acceptes DESC
  LIMIT result_limit;
END;
$$ LANGUAGE plpgsql;
```

### Index pour les performances

```sql
-- Index spatial pour la géolocalisation
CREATE INDEX IF NOT EXISTS idx_professionnels_geolocalisation 
ON professionnels USING GIST (geolocalisation);

-- Index pour la recherche textuelle
CREATE INDEX IF NOT EXISTS idx_professionnels_search_text 
ON professionnels USING GIN (
  to_tsvector('french', 
    COALESCE(nom, '') || ' ' || 
    COALESCE(prenom, '') || ' ' || 
    COALESCE(raison_sociale, '') || ' ' ||
    COALESCE(numero_ordre, '')
  )
);

-- Index pour les spécialités
CREATE INDEX IF NOT EXISTS idx_specialites_professionnel_nom 
ON specialites_professionnel USING GIN (to_tsvector('french', nom_specialite));

-- Index pour les mots-clés
CREATE INDEX IF NOT EXISTS idx_mot_cles_professionnel_nom 
ON mot_cles_professionnel USING GIN (to_tsvector('french', nom));
```

## Utilisation

### Exemple d'utilisation du UseCase

```typescript
import SearchProfessionalsRepository from "@/infrastructure/repositories/SearchProfessionalsRepository";
import SearchProfessionalsUsecase from "@/domain/usecases/professional/GetProfessionnalInformations/SearchProfessionalsUsecase";

// Injection de dépendance
const repository = new SearchProfessionalsRepository();
const usecase = new SearchProfessionalsUsecase(repository);

// Recherche textuelle
const results = await usecase.execute({
  name: "cardiologue",
  localization: "Antananarivo",
  today: new Date().toISOString(),
  page: 0,
  limit: 20
});

// Recherche par proximité (si PostGIS configuré)
const proximityResults = await repository.searchByProximity(
  -18.8792, // latitude Antananarivo
  47.5079,  // longitude Antananarivo
  10,       // rayon 10km
  20        // limite 20 résultats
);
```

### Tri par pertinence

Le système applique un tri intelligent basé sur :
1. **Professionnels acceptant de nouveaux patients** (priorité haute)
2. **Nombre de créneaux disponibles** (plus = mieux)
3. **Correspondance exacte** avec le terme de recherche
4. **Ordre alphabétique** par nom

## Performance

### Optimisations implémentées

- **Requêtes SELECT optimisées** : Sélection uniquement des champs nécessaires
- **Pagination** : Limitation du nombre de résultats
- **Index de base de données** : Pour la recherche textuelle et géographique
- **Échappement SQL** : Protection contre l'injection SQL
- **Tri côté base de données** : Réduction du traitement côté application

### Recommandations

- Utiliser la pagination pour les grandes listes
- Configurer les index recommandés
- Monitorer les performances des requêtes
- Considérer un cache pour les recherches fréquentes

## Évolutions futures

- **Recherche par spécialité avancée** avec filtres multiples
- **Recherche par disponibilité** en temps réel
- **Recherche par assurances acceptées**
- **Système de notation/avis** pour améliorer le tri
- **Cache Redis** pour les recherches populaires
- **Recherche full-text** avec Elasticsearch pour de très gros volumes
