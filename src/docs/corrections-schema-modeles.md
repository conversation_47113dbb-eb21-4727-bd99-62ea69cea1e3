# Corrections des incohérences entre schéma de base de données et modèles TypeScript

## Problèmes identifiés et corrigés

### 1. ❌ Erreur PGRST200 - Relation contacts
**Problème** : Relation directe entre `professionnels` et `contact` inexistante
**Solution** : Suppression temporaire des contacts de la requête

### 2. ❌ Erreur 42703 - Colonne `etablissements_professionnel.adresse` 
**Problème** : Le modèle `EtablissementProfessionnel` ne contenait pas le champ `adresse`
**Solution** : ✅ Ajout du champ `adresse: string` au modèle

### 3. ❌ Erreur sur `mot_cles_professionnel.symptome`
**Problème** : Le modèle utilisait `symptome` mais le schéma DB utilise `nom`
**Solution** : ✅ Changement de `symptome` vers `nom` dans le modèle et les requêtes

## Modèles corrigés

### EtablissementProfessionnel.ts
```typescript
export interface EtablissementProfessionnel {
  id: number;
  id_professionnel: number;
  nom_etablissement: string;
  nom_responsable: string;
  prenom_responsable: string;
  adresse: string;        // ✅ AJOUTÉ
  equipe?: string;        // ✅ RENDU OPTIONNEL
}
```

### MotClesProfessionnel.ts
```typescript
export interface MotClesProfessionnel {
  id: number;
  nom: string;           // ✅ CHANGÉ de 'symptome' vers 'nom'
  id_professionnel: number;
}
```

### Contact.ts
```typescript
export interface Contact {
  id: number;
  contact_numero: string;  // ✅ CORRIGÉ de 'numero' vers 'contact_numero'
  contact_type?: string;   // ✅ AJOUTÉ
  utilisateur_id: number;
}
```

## Incohérences restantes à surveiller

### Table `professionnels`
- **database.sql** : `id_ordre_appartenance INT NOT NULL`
- **insertData.sql** : `ordre_appartenance VARCHAR(255) NOT NULL`
- **Modèle** : Aucun des deux champs présent

### Recommandations
1. **Vérifier le schéma réel** en production/développement
2. **Standardiser** les fichiers de schéma
3. **Ajouter des tests** pour valider la cohérence schéma/modèles
4. **Documenter** les champs optionnels vs obligatoires

## Test recommandé

```typescript
// Test simple pour vérifier les corrections
const result = await usecase.execute({
  name: "test",
  localization: "Antananarivo",
  today: new Date().toISOString(),
  page: 0,
  limit: 5
});

console.log("Résultats:", result);
console.log("Établissements:", result[0]?.etablissements_professionnel);
console.log("Mots-clés:", result[0]?.mot_cles);
```

## Statut actuel

✅ **Erreurs SQL corrigées**
✅ **Modèles alignés avec le schéma**
✅ **Requêtes Supabase fonctionnelles**
❌ **Contacts temporairement désactivés**

Le système de recherche devrait maintenant fonctionner sans erreurs de colonnes manquantes.
