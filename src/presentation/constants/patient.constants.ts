export const APPOINTMENT_STATUS_STYLES = {
  upcoming: {
    container: 'bg-blue-50 dark:bg-blue-950 border-blue-100 dark:border-blue-900',
    text: 'text-blue-800 dark:text-blue-200',
    badge: 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
  },
  completed: {
    container: 'bg-green-50 dark:bg-green-950 border-green-100 dark:border-green-900',
    text: 'text-green-800 dark:text-green-200',
    badge: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
  },
  cancelled: {
    container: 'bg-red-50 dark:bg-red-950 border-red-100 dark:border-red-900',
    text: 'text-red-800 dark:text-red-200',
    badge: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
  },
} as const;

export const APPOINTMENT_STATUS_LABELS: Record<string, string> = {
  upcoming: 'À venir',
  completed: 'Terminé',
  cancelled: 'Annulé',
};

export const DOCUMENT_TYPE_ICONS = {
  prescription: '📄',
  report: '📋',
  analysis: '📊',
  other: '📎',
} as const;

export const DATE_FORMAT_OPTIONS: Intl.DateTimeFormatOptions = {
  weekday: 'long',
  day: 'numeric',
  month: 'long',
  year: 'numeric',
};
