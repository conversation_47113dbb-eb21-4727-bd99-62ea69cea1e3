/**
 * Dashboard Configuration Constants
 * 
 * Centralized configuration for dashboard charts, colors, and layout settings.
 * Used across patient and professional dashboard components.
 */

/**
 * Chart color palette for dashboard visualizations
 */
export const DASHBOARD_CHART_COLORS = {
  primary: '#4F46E5',      // Indigo - Primary actions, main data
  success: '#10B981',      // Emerald - Positive metrics, completed items
  warning: '#F59E0B',      // Amber - Warnings, pending items
  danger: '#EF4444',       // Red - Errors, cancelled items
  info: '#3B82F6',         // Blue - Information, secondary data
  purple: '#8B5CF6',       // Purple - Special categories
  teal: '#14B8A6',         // Teal - Health metrics
  orange: '#F97316',       // Orange - Alerts, notifications
} as const;

/**
 * Chart configuration settings
 */
export const DASHBOARD_CHART_CONFIG = {
  margins: {
    default: { top: 10, right: 30, left: 0, bottom: 0 },
    compact: { top: 5, right: 15, left: 0, bottom: 0 },
    extended: { top: 20, right: 40, left: 10, bottom: 10 },
  },
  grid: {
    strokeDasharray: '3 3',
    strokeLight: '#f3f4f6',
    strokeDark: '#374151',
  },
  axis: {
    strokeLight: '#9ca3af',
    strokeDark: '#6b7280',
  },
  animation: {
    duration: 300,
    easing: 'ease-out',
  },
} as const;

/**
 * Health score thresholds and status mapping
 */
export const HEALTH_SCORE_THRESHOLDS = {
  excellent: { min: 90, max: 100, status: 'Excellente' as const },
  good: { min: 75, max: 89, status: 'Bonne' as const },
  average: { min: 60, max: 74, status: 'Moyenne' as const },
  concerning: { min: 40, max: 59, status: 'À surveiller' as const },
  poor: { min: 0, max: 39, status: 'Préoccupante' as const },
} as const;

/**
 * Health score color mapping
 */
export const HEALTH_SCORE_COLORS = {
  'Excellente': DASHBOARD_CHART_COLORS.success,
  'Bonne': DASHBOARD_CHART_COLORS.info,
  'Moyenne': DASHBOARD_CHART_COLORS.warning,
  'À surveiller': DASHBOARD_CHART_COLORS.orange,
  'Préoccupante': DASHBOARD_CHART_COLORS.danger,
} as const;

/**
 * Prescription status configuration
 */
export const PRESCRIPTION_STATUS_CONFIG = [
  { 
    name: 'Actives', 
    value: 0, 
    color: DASHBOARD_CHART_COLORS.primary,
    bgColor: 'bg-indigo-50 dark:bg-indigo-900/30',
  },
  { 
    name: 'Terminées', 
    value: 0, 
    color: DASHBOARD_CHART_COLORS.success,
    bgColor: 'bg-emerald-50 dark:bg-emerald-900/30',
  },
  { 
    name: 'Expirées', 
    value: 0, 
    color: DASHBOARD_CHART_COLORS.warning,
    bgColor: 'bg-amber-50 dark:bg-amber-900/30',
  },
] as const;

/**
 * Dashboard grid breakpoints and responsive settings
 */
export const DASHBOARD_GRID_CONFIG = {
  breakpoints: {
    mobile: 'grid-cols-1',
    tablet: 'md:grid-cols-2',
    desktop: 'lg:grid-cols-3',
    wide: 'xl:grid-cols-4',
  },
  gaps: {
    small: 'gap-4',
    medium: 'gap-6',
    large: 'gap-8',
  },
  spacing: {
    section: 'mb-8',
    card: 'mb-6',
    element: 'mb-4',
  },
} as const;

/**
 * Chart height configurations
 */
export const CHART_HEIGHTS = {
  compact: 'h-48',      // 192px
  medium: 'h-64',       // 256px
  large: 'h-80',        // 320px
  xlarge: 'h-96',       // 384px
} as const;

/**
 * Tooltip configuration for charts
 */
export const TOOLTIP_CONFIG = {
  light: {
    contentStyle: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      color: '#111827',
      borderRadius: '8px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    },
    itemStyle: { color: '#111827' },
    labelStyle: { color: '#111827', fontWeight: '500' },
  },
  dark: {
    contentStyle: {
      backgroundColor: 'rgba(31, 41, 55, 0.95)',
      borderColor: '#374151',
      color: '#f9fafb',
      borderRadius: '8px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',
    },
    itemStyle: { color: '#f3f4f6' },
    labelStyle: { color: '#f3f4f6', fontWeight: '500' },
  },
} as const;

/**
 * Animation and transition constants
 */
export const DASHBOARD_ANIMATIONS = {
  fadeIn: 'animate-fade-in',
  slideIn: 'animate-slide-in',
  pulse: 'animate-pulse-slow',
  gradient: 'animate-gradient-x',
  transition: 'transition-all duration-300 ease-out',
} as const;

/**
 * Icon size configurations
 */
export const ICON_SIZES = {
  small: 'h-4 w-4',
  medium: 'h-5 w-5',
  large: 'h-6 w-6',
  xlarge: 'h-8 w-8',
} as const;
