import { demande_adhesion_statut_enum } from "@/domain/models/enums";
import {
  DESTRUCTIVE,
  EVENT_COLOR,
  GRE<PERSON>,
  PRIMARY,
} from "@/shared/constants/Color";

export const ADHESION_REQUEST_STATUS_COLORS = {
  [demande_adhesion_statut_enum.EN_ATTENTE]: EVENT_COLOR, // orange
  [demande_adhesion_statut_enum.LU]: PRIMARY, // bleu
  [demande_adhesion_statut_enum.APPROUVEE]: GREEN, // vert
  [demande_adhesion_statut_enum.REJETEE]: DESTRUCTIVE, // rouge
} as const;

export const ADHESION_REQUEST_STATUS_LABELS = {
  [demande_adhesion_statut_enum.EN_ATTENTE]: "En attente",
  [demande_adhesion_statut_enum.LU]: "Lu",
  [demande_adhesion_statut_enum.APPROUVEE]: "Approuvée",
  [demande_adhesion_statut_enum.REJETEE]: "Rejetée",
} as const;

export const ADHESION_REQUEST_STATUS_MUI_COLORS = {
  [demande_adhesion_statut_enum.EN_ATTENTE]: "warning",
  [demande_adhesion_statut_enum.LU]: "primary",
  [demande_adhesion_statut_enum.APPROUVEE]: "success",
  [demande_adhesion_statut_enum.REJETEE]: "error",
} as const;
