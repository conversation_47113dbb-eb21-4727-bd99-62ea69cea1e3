/**
 * Patient Dashboard Specific Constants
 * 
 * Constants and configuration specific to the patient dashboard interface.
 * Includes styling, layout, and patient-specific data configurations.
 */

import { DASHBOARD_CHART_COLORS, DASHBOARD_GRID_CONFIG } from './dashboard.constants';

/**
 * Patient dashboard card styling configurations
 */
export const PATIENT_DASHBOARD_STYLES = {
  cards: {
    container: 'rounded-xl shadow-sm p-6',
    header: 'rounded-xl shadow-sm overflow-hidden',
    content: 'bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6',
    grid: `${DASHBOARD_GRID_CONFIG.breakpoints.mobile} ${DASHBOARD_GRID_CONFIG.breakpoints.tablet} ${DASHBOARD_GRID_CONFIG.breakpoints.desktop} ${DASHBOARD_GRID_CONFIG.gaps.medium}`,
  },
  charts: {
    container: 'bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6',
    height: 'h-80',
    fullHeight: 'h-full',
  },
  sections: {
    header: 'mb-8',
    main: 'mb-8',
    footer: 'mb-6',
  },
} as const;

/**
 * Patient statistics card configurations
 */
export const PATIENT_STAT_CARDS = {
  totalAppointments: {
    title: 'Rendez-vous Total',
    icon: 'Calendar',
    color: DASHBOARD_CHART_COLORS.primary,
    bgColor: 'bg-indigo-50 dark:bg-indigo-900/30',
    iconColor: 'text-indigo-600 dark:text-indigo-400',
  },
  upcomingAppointments: {
    title: 'Prochains RDV',
    icon: 'Clock',
    color: DASHBOARD_CHART_COLORS.success,
    bgColor: 'bg-emerald-50 dark:bg-emerald-900/30',
    iconColor: 'text-emerald-600 dark:text-emerald-400',
  },
  healthStatus: {
    title: 'Santé Générale',
    icon: 'Heart',
    color: DASHBOARD_CHART_COLORS.warning,
    bgColor: 'bg-amber-50 dark:bg-amber-900/30',
    iconColor: 'text-amber-600 dark:text-amber-400',
  },
} as const;

/**
 * Personal statistics mini-cards configuration
 */
export const PERSONAL_STATS_CONFIG = {
  completed: {
    title: 'RDV Complétés',
    icon: 'CheckCircle',
    bgColor: 'bg-green-50 dark:bg-green-900/30',
    iconColor: 'text-green-500 dark:text-green-400',
  },
  upcoming: {
    title: 'À venir',
    icon: 'Clock',
    bgColor: 'bg-blue-50 dark:bg-blue-900/30',
    iconColor: 'text-blue-500 dark:text-blue-400',
  },
  cancelled: {
    title: 'RDV Annulés',
    icon: 'CalendarX',
    bgColor: 'bg-amber-50 dark:bg-amber-900/30',
    iconColor: 'text-amber-500 dark:text-amber-400',
  },
  currentWeight: {
    title: 'Poids actuel',
    icon: 'Heart',
    bgColor: 'bg-indigo-50 dark:bg-indigo-900/30',
    iconColor: 'text-indigo-500 dark:text-indigo-400',
    unit: 'kg',
  },
} as const;

/**
 * Dashboard header configuration
 */
export const DASHBOARD_HEADER_CONFIG = {
  greeting: {
    title: 'Bonjour',
    fallbackName: 'Patient',
    titleClasses: 'text-3xl font-bold text-gray-900 dark:text-white',
    subtitleClasses: 'text-gray-600 dark:text-gray-400 mt-1',
  },
  subtitle: 'Voici un aperçu de votre suivi médical',
} as const;

/**
 * Chart section configurations
 */
export const CHART_SECTIONS = {
  appointmentHistory: {
    title: 'Historique des Rendez-vous',
    subtitle: '6 derniers mois',
    icon: 'BarChart2',
    dataKey: 'appointments',
    color: DASHBOARD_CHART_COLORS.primary,
    darkColor: 'dark:fill-indigo-900/80',
  },
  personalStats: {
    title: 'Statistiques Personnelles',
    gridCols: 'grid-cols-2',
    gap: 'gap-4',
  },
} as const;

/**
 * Layout configurations for different sections
 */
export const LAYOUT_CONFIG = {
  mainContainer: 'p-6 max-w-7xl mx-auto',
  twoColumnGrid: 'grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8',
  chartColumn: 'lg:col-span-2',
  statsColumn: 'lg:col-span-1',
  fullWidthSection: 'grid grid-cols-1 gap-6 mb-8',
} as const;

/**
 * Patient dashboard specific text content
 */
export const DASHBOARD_TEXT = {
  noAppointmentToday: 'Aucun rendez-vous aujourd\'hui',
  loadingMessage: 'Chargement des données...',
  errorMessage: 'Erreur lors du chargement des données',
  emptyStateMessage: 'Aucune donnée disponible',
} as const;

/**
 * Responsive breakpoints for patient dashboard
 */
export const RESPONSIVE_CONFIG = {
  mobile: {
    statsGrid: 'grid-cols-1',
    chartHeight: 'h-64',
    padding: 'p-4',
  },
  tablet: {
    statsGrid: 'md:grid-cols-2',
    chartHeight: 'h-72',
    padding: 'p-6',
  },
  desktop: {
    statsGrid: 'lg:grid-cols-3',
    chartHeight: 'h-80',
    padding: 'p-6',
  },
} as const;

/**
 * Animation delays for staggered loading
 */
export const ANIMATION_DELAYS = {
  header: 'delay-0',
  statsCards: 'delay-100',
  charts: 'delay-200',
  personalStats: 'delay-300',
} as const;

/**
 * Patient dashboard data refresh intervals (in milliseconds)
 */
export const REFRESH_INTERVALS = {
  healthScore: 30000,      // 30 seconds
  appointments: 60000,     // 1 minute
  statistics: 300000,      // 5 minutes
  charts: 600000,          // 10 minutes
} as const;
