import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { sexeEnumValues } from "@/domain/models/enums";
import { z } from "zod";

export const PatientFormByProfessionalSchema = z.object({
  nom: z.string().min(1, "Le nom est requis"),
  prenom: z.string().optional(),
  sexe: z.enum(sexeEnumValues as [string, ...string[]], {
      required_error: "Le sexe est requis",
  }),
  date_naissance: z.date({
      required_error: "La date de naissance est requise",
  }),
  telephones: z.array(
      z.object({
          numero: z
              .string()
              .min(8, "Au moins 8 chiffres")
              .regex(/^[0-9+\s-]+$/, "Numéro de téléphone invalide"),
      })
  ).min(1, "Au moins un numéro de téléphone est requis"),
  adresse: z.string().optional(),
  district: z.string().optional(),
  commune: z.string().optional(),
  fokontany: z.string().min(1, "Le fokontany est requis"),
  donneur_sang: z.boolean().optional(),
  groupe_sanguin: z.string().optional(),
  nationalite: z.string().optional(),
  pays: z.string().optional(),
  situation_matrimonial: z
      .string()
      .min(1, "La situation matrimoniale est requise"),
  nb_enfant: z
      .number()
      .optional(),
  profession: z.string().optional(),
})

export const usePatientFormByProfessional = () => {
  const {
    watch,
    getValues,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(PatientFormByProfessionalSchema),
    mode: "onBlur",
    defaultValues: {
      nom: "",
      prenom: "",
      sexe: "",
      date_naissance: null,
      telephone: "",
      adresse: "",
      district: "",
      commune: "",
      fokontany: "",
      donneur_sang: false,
      groupe_sanguin: "",
      nationalite: "Malagasy",
      pays: "",
      profession: "",
      situation_matrimonial: "celibataire",
      autre_profession: "",
      nb_enfant: null,
    },
  });

  return {
    watch,
    getValues,
    handleSubmit,
    control,
    errors,
  };
};
