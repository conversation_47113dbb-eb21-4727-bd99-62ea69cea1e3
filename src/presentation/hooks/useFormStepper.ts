import { useCallback, useState } from "react";

interface UseFormStepperReturn {
  activeStep: number;
  handleNext: (count: number) => void;
  handleBack: (count: number) => void;
  resetStepper: () => void;
}

export const useFormStepper = (totalSteps: number): UseFormStepperReturn => {
  const [activeStep, setActiveStep] = useState(0);

  const handleNext = useCallback(
    (count: number) => {
      setActiveStep((prevStep) =>
        Math.min(prevStep + count, totalSteps - count)
      );
    },
    [totalSteps]
  );

  const handleBack = useCallback((count: number) => {
    setActiveStep((prevStep) => Math.max(prevStep - count, 0));
  }, []);

  const resetStepper = useCallback(() => {
    setActiveStep(0);
  }, []);

  return {
    activeStep,
    handleNext,
    handleBack,
    resetStepper,
  };
};
