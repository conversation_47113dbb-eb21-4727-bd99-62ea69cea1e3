import { useCallback, useRef } from 'react';

/**
 * Hook personnalisé pour implémenter le throttling
 * Limite l'exécution d'une fonction à un intervalle spécifique
 * 
 * @param callback - La fonction à throttler
 * @param delay - Le délai en millisecondes entre les exécutions
 * @returns La fonction throttlée
 */
export const useThrottle = <T extends (...args: unknown[]) => void>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const throttledCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      
      // Si assez de temps s'est écoulé depuis la dernière exécution
      if (now - lastRun.current >= delay) {
        lastRun.current = now;
        callback(...args);
      } else {
        // Sino<PERSON>, programmer l'exécution pour plus tard
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          lastRun.current = Date.now();
          callback(...args);
        }, delay - (now - lastRun.current));
      }
    },
    [callback, delay]
  ) as T;

  return throttledCallback;
};

export default useThrottle;
