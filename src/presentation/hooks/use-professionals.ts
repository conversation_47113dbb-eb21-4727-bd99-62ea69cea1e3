import { getProfessionalById, getProfessionals, setSelectedProfessional } from "@/application/slices/professionnal/professionnalSlice"
import { SupabaseError } from "@/infrastructure/supabase/supabaseError"
import { AppDispatch, RootState } from "@/store"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { toast } from "sonner"

const useProfessionals = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { professionals, loading, selectedProfessionnal, error } = useSelector((state: RootState) => state.professional)

  useEffect(() => {
    if (error) {
      toast.error(error)
    }
  }, [error])

  const getProfessionalsList = async () => {
    try {
      if (professionals.length == 0) await dispatch(getProfessionals())
    } catch (error) {
      toast.error(
        error instanceof SupabaseError
          ? error.message
          : 'Erreur lors de la recuperation des professionnels'
      )
    }
  }
  
  const getProfessionalsById = async (id: number) => {
    try {
      await dispatch(getProfessionalById(id))
    } catch (error) {
      toast.error(
        error instanceof SupabaseError
          ? error.message
          : 'Erreur lors de la recuperation des professionnels'
      )
    }
  }

  return {
    professionals,
    loading,
    selectedProfessionnal,
    getProfessionalsById,
    getProfessionalsList
  }

}

export default useProfessionals
