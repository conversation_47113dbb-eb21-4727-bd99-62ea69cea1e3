import { useEffect } from "react";
import { useParams } from "react-router-dom";
import { useProfessionnelPatient } from "./use-professionnelPatient";
import { useAppSelector } from "./redux";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import useAuth from "./use-auth";
import { useProche } from "./use-proches";

export const useProfilePatientData = (fetch?: boolean) => {
  const { id } = useParams();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const userId = useAppSelector((state) => state.authentification.user?.id);

  const { selectedDataProfessionalPatient, getProfessionalPatientByUserId } =
    useProfessionnelPatient();
  const {
    loading: loadingProche,
    selectedProcheEmployer,
    selectedProchePatient,
    handleGetProcheEmployerById,
    handleGetProchePatientById,
  } = useProche();
  const { roleUserSelected, loading: loadingRole } = useAuth();

  useEffect(() => {
    if (fetch) {
      if (role === utilisateurs_role_enum.PATIENT) {
        getProfessionalPatientByUserId(userId);
      } else if (roleUserSelected === utilisateurs_role_enum.PATIENT) {
        getProfessionalPatientByUserId(Number(id));
      } else if (roleUserSelected === utilisateurs_role_enum.PROCHE) {
        // handleGetProcheById(Number(id));
        if (role === utilisateurs_role_enum.PROFESSIONNEL) {
          handleGetProchePatientById(Number(id));
        } else {
          handleGetProcheEmployerById(Number(id));
        }
      }
    }
  }, [role, userId, id, roleUserSelected, fetch]);

  return {
    loading: loadingProche || loadingRole,
    patientData: {
      ...selectedDataProfessionalPatient?.patient,
      user: selectedDataProfessionalPatient?.user,
    },
    professionalPatientId: selectedDataProfessionalPatient?.id,
    selectedProchePatient,
    selectedProcheEmployer,
  };
};
