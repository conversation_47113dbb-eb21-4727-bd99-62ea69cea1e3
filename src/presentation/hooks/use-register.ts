import {
  resendConfirmationEmail,
  setError,
  setIsOpen,
  setVerificationCode,
} from "@/application/slices/auth/authSlice";
import { SignUpProps } from "@/domain/interfaces/repositories";
import { AppDispatch, RootState } from "@/store";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useToast } from "../components/common/toast/Toast";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useNavigate } from "react-router-dom";
import { SuccessMessages } from "@/shared/constants/SuccessMessages";
import { RegisterUserDTO } from "@/domain/DTOS";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";

const useRegister = () => {
  const toast = useToast();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { verificationCode, isOpen, loading, error } = useSelector(
    (state: RootState) => state.authentification
  );

  const handleToggleModal = (value: boolean) => {
    dispatch(setIsOpen(value));
  };

  const handleVerificationCode = (code: number[]) => {
    dispatch(setVerificationCode(code));
  };

  const handleVerificationCodeError = (err: string | null) => {
    dispatch(setError(err));
  };

  const resendValidationEmail = async (email: string) => {
    try {
      await dispatch(resendConfirmationEmail(email));
      toast.success("Email de validation envoyé avec succès");
    } catch (error) {
      toast.error(error);
    }
  };

  const isStep1Valid = (data: SignUpProps): boolean => {
    console.log(data);

    let out = false;
    if (
      data.userData.nom &&
      data.userData.prenom &&
      data.user.mot_de_passe_hash &&
      data.userData.sexe &&
      data.user.email
    ) {
      out = true;
    }

    return out;
  };

  const isStep2Valid = (data: SignUpProps): boolean => {
    return false;
  };
  const isStep3Valid = (data: SignUpProps): boolean => {
    return false;
  };

  const validateStep = (activeStep: number, data: SignUpProps) => {
    switch (activeStep) {
      case 0:
        return isStep1Valid(data);

      case 1:
        return isStep2Valid(data);

      case 2:
        return isStep3Valid(data);
      default:
        throw new Error(
          `La validation de l'etape ${
            activeStep + 1
          } n'a pas encore ete implementee`
        );
    }
  };

  return {
    error,
    loading,
    isOpen,
    verificationCode,
    handleToggleModal,
    resendValidationEmail,
    handleVerificationCode,
    handleVerificationCodeError,
    validateStep,
  };
};

export default useRegister;
