import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import {
  createAdhesionRequest,
  deleteAdhesionRequest,
  getAdhesionRequest,
  markAsRead,
  approveAdhesionRequest,
  rejectAdhesionRequest,
  setSelectedRequest,
  clearSelectedRequest,
  openDetailModal,
  closeDetailModal,
  setSearchQuery,
  setStatusFilter,
} from "@/application/slices/admin/adhesionRequestSlice";
import { demande_adhesion } from "@/domain/models";
import { useNavigate } from "react-router-dom";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useToast } from "../use-toast";

/**
 * Hook pour gérer les opérations CRUD des demandes d'adhésion
 * Gère les interactions avec le store Redux et les appels API
 */
const useAdhesionRequest = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    requestAdhesionList,
    selectedRequestAdhesion,
    loading,
    error,
    detailModalOpen,
    searchQuery,
    statusFilter,
  } = useSelector((state: RootState) => state.adhesionRequest);

  const navigate = useNavigate();
  const toast = useToast();

  /**
   * Récupère la liste des demandes d'adhésion
   * Ne fait l'appel API que si la liste est vide
   */
  const handleGetAdhesionRequestList = async () => {
    if (requestAdhesionList.length === 0) await dispatch(getAdhesionRequest());
  };

  /**
   * Crée une nouvelle demande d'adhésion
   * Redirige vers la page de succès en cas de réussite
   */
  const handleCreateAdhesionRequest = async (
    data: Omit<demande_adhesion, "id" | "status" | "cree_a">
  ) => {
    try {
      await dispatch(createAdhesionRequest(data)).unwrap();
      navigate(`/${PublicRoutesNavigation.ADHESION_SUCCESS}`);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      toast.error(errorMessage);
    }
  };

  /**
   * Supprime une demande d'adhésion
   */
  const handleDeleteAdhesionRequest = async (id: number) => {
    await dispatch(deleteAdhesionRequest(id));
  };

  /**
   * Marque une demande d'adhésion comme lue
   */
  const handleMarkAsRead = async (id: number) => {
    await dispatch(markAsRead(id));
  };

  /**
   * Approuve une demande d'adhésion
   */
  const handleApproveAdhesionRequest = async (id: number) => {
    await dispatch(approveAdhesionRequest(id));
  };

  /**
   * Rejette une demande d'adhésion
   */
  const handleRejectAdhesionRequest = async (id: number) => {
    await dispatch(rejectAdhesionRequest(id));
  };

  /**
   * Sélectionne une demande d'adhésion et ouvre le modal de détails
   */
  const handleViewDetails = (request: demande_adhesion) => {
    dispatch(setSelectedRequest(request));
    dispatch(openDetailModal());
  };

  /**
   * Ferme le modal de détails
   */
  const handleCloseDetailModal = () => {
    dispatch(closeDetailModal());
  };

  /**
   * Met à jour la requête de recherche
   */
  const handleSetSearchQuery = (query: string) => {
    dispatch(setSearchQuery(query));
  };

  /**
   * Met à jour le filtre de statut
   */
  const handleSetStatusFilter = (status: string) => {
    dispatch(setStatusFilter(status));
  };

  /**
   * Filtre les demandes d'adhésion en fonction de la requête de recherche et du statut
   */
  const getFilteredRequests = () => {
    return requestAdhesionList.filter((request) => {
      // Filtre par statut
      if (statusFilter !== "all" && request.status !== statusFilter) {
        return false;
      }

      // Si pas de recherche, on retourne tous les résultats qui correspondent au filtre de statut
      if (!searchQuery.trim()) return true;

      // Filtre par recherche
      const searchLower = searchQuery.toLowerCase();
      const nomComplet = `${request.prenom} ${request.nom}`.toLowerCase();

      return (
        nomComplet.includes(searchLower) ||
        request.email.toLowerCase().includes(searchLower) ||
        request.telephone.toLowerCase().includes(searchLower) ||
        request.adresse.toLowerCase().includes(searchLower) ||
        (request.type_etablissement &&
          request.type_etablissement.toLowerCase().includes(searchLower))
      );
    });
  };

  return {
    // États
    requestAdhesionList,
    selectedRequestAdhesion,
    loading,
    error,
    detailModalOpen,
    searchQuery,
    statusFilter,
    filteredRequests: getFilteredRequests(),

    // Actions
    handleGetAdhesionRequestList,
    handleCreateAdhesionRequest,
    handleDeleteAdhesionRequest,
    handleMarkAsRead,
    handleApproveAdhesionRequest,
    handleRejectAdhesionRequest,
    handleViewDetails,
    handleCloseDetailModal,
    handleSetSearchQuery,
    handleSetStatusFilter,
  };
};

export default useAdhesionRequest;
