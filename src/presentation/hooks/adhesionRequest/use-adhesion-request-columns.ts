import { GridColDef } from "@mui/x-data-grid";
import { AdhesionRequestColumns } from "@/presentation/components/features/admin/adhesionRequests/AdhesionRequestColumns";

/**
 * Custom hook pour obtenir les colonnes du DataGrid des demandes d'adhésion
 * Permet de réutiliser la configuration des colonnes dans différents composants
 */
export const useAdhesionRequestColumns = (): GridColDef[] => {
  return AdhesionRequestColumns();
};
