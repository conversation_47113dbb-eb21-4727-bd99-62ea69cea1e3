import { useState } from "react";
import { demande_adhesion } from "@/domain/models";
import { useToast } from "../use-toast";
import useAdhesionRequest from "./use-adhesion-request";

/**
 * Custom hook pour gérer l'interface utilisateur des demandes d'adhésion
 * Utilise le hook useAdhesionRequest qui gère maintenant l'état dans Redux
 */
export const useAdhesionRequestUI = () => {
  // État local uniquement pour le menu contextuel
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  // Utilisation du hook principal qui gère tout l'état dans Redux
  const {
    searchQuery,
    statusFilter,
    detailModalOpen,
    selectedRequestAdhesion,
    filteredRequests,
    loading,
    error,
    handleSetSearchQuery,
    handleSetStatusFilter,
    handleGetAdhesionRequestList,
    handleViewDetails,
    handleMarkAsRead,
    handleApproveAdhesionRequest,
    handleRejectAdhesionRequest,
    handleDeleteAdhesionRequest,
    handleCloseDetailModal,
  } = useAdhesionRequest();

  const toast = useToast();

  // Gestionnaires d'événements pour les actions UI
  const handleMenuOpen = (
    event: React.MouseEvent<HTMLElement>,
    request: demande_adhesion
  ) => {
    setAnchorEl(event.currentTarget);
    // Sélectionner la demande pour les actions du menu
    handleViewDetails(request);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDelete = async (id: number) => {
    try {
      await handleDeleteAdhesionRequest(id);
      toast.success("Demande d'adhésion supprimée avec succès");
    } catch (error) {
      toast.error("Erreur lors de la suppression de la demande d'adhésion");
    }
    handleMenuClose();
  };

  const handleMarkAsReadUI = async (id: number) => {
    try {
      await handleMarkAsRead(id);
      toast.success("Demande marquée comme lue avec succès");
    } catch (error) {
      toast.error("Erreur lors du marquage de la demande comme lue");
    }
    handleMenuClose();
  };

  const handleApprove = async (id: number) => {
    try {
      await handleApproveAdhesionRequest(id);
      toast.success("Demande approuvée avec succès");
    } catch (error) {
      toast.error("Erreur lors de l'approbation de la demande");
    }
    handleMenuClose();
  };

  const handleReject = async (id: number) => {
    try {
      await handleRejectAdhesionRequest(id);
      toast.success("Demande rejetée avec succès");
    } catch (error) {
      toast.error("Erreur lors du rejet de la demande");
    }
    handleMenuClose();
  };

  return {
    // États
    searchQuery,
    statusFilter,
    detailModalOpen,
    selectedRequest: selectedRequestAdhesion,
    anchorEl,
    filteredRequests,
    loading,
    error,

    // Actions
    setSearchQuery: handleSetSearchQuery,
    setStatusFilter: handleSetStatusFilter,
    handleGetAdhesionRequestList,
    handleMenuOpen,
    handleMenuClose,
    handleViewDetails,
    handleDelete,
    handleMarkAsRead: handleMarkAsReadUI,
    handleApprove,
    handleReject,
    closeDetailModal: handleCloseDetailModal,
  };
};
