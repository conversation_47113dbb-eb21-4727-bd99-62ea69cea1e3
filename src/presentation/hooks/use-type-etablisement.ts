import { SupabaseError } from '@/infrastructure/supabase/supabaseError'
import {
  getTypeEtablissements,
  setIsPopOverOpen as setIsPopOverOpenAction
} from '@/application/slices/professionnal/typeEtablissementSlice'

import { AppDispatch, RootState } from '@/store'
import { useDispatch, useSelector } from 'react-redux'
import { useToast } from '../components/common/toast/Toast'

const useTypeEtablissement = () => {
  const toast = useToast();
  const { error, listes, isPopoverOpen, loading } = useSelector(
    (state: RootState) => state.typeEtablissement
  )

  const dispatch = useDispatch<AppDispatch>()

  const setIsPopoverOpen = (isOpen: boolean) => {
    dispatch(setIsPopOverOpenAction(isOpen))
  }

  const openNextPopOver = () => {
    setIsPopoverOpen(false)
    dispatch(setIsPopOverOpenAction(true))
  }

  const getTypeEtablissementList = async () => {
    try {
      if (listes.length == 0) await dispatch(getTypeEtablissements())
    } catch (error) {
      toast.error(
        error instanceof SupabaseError
          ? error.message
          : 'Erreur lors de la recuperation des lites de specialitees'
      )
    }
  }

  return {
    setIsPopoverOpen,
    error,
    listes,
    isPopoverOpen,
    loading,
    getTypeEtablissementList,
    openNextPopOver
  }
}

export default useTypeEtablissement
