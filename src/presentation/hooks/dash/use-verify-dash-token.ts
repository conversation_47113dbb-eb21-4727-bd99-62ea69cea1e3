import { InvitationDash } from "@/domain/models/InvitationDash.ts";
import { GetInvitationDashByTokenUsecase } from "@/domain/usecases/invitationDash/GetInvitationDashByTokenUsecase.ts";
import GetInvitationDashByTokenRepository from "@/infrastructure/repositories/invitationDash/GetInvitationDashByTokenRepository.ts";
import { useEffect, useState } from "react";

const getInvitationDashByTokenRepository =
  new GetInvitationDashByTokenRepository();
const getInvitationDashByTokenUsecase = new GetInvitationDashByTokenUsecase(
  getInvitationDashByTokenRepository
);

const useVerifyDashToken = (token?: string) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isTokenValid, setIsTokenValid] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [invitationData, setInvitationData] = useState<InvitationDash | null>(
    null
  );

  useEffect(() => {
    const doTokenVerification = async () => {
      try {
        setIsLoading(true);
        setError("");
        setIsTokenValid(false);

        if (!token) {
          throw new Error("Token manquant");
        }

        const invitation = await getInvitationDashByTokenUsecase.execute(token);

        if (!invitation) {
          setIsTokenValid(false);
        } else {
          setInvitationData(invitation);
          setIsTokenValid(true);
        }
      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("Une erreur inconnue est survenue.");
        }
      } finally {
        setIsLoading(false);
      }
    };

    doTokenVerification();
  }, [token]);

  return { isLoading, isTokenValid, error, setError, invitationData };
};

export default useVerifyDashToken;
