import { PasswordService } from "@/domain/services/PasswordService";
import { VerifyAndCheckEmail } from "@/domain/services/VerifyAndCheckEmail";
import { CreateDashUsecase } from "@/domain/usecases/dash/CreateDashUsecase";
import {
  CreateAuthentificationUserUsecase,
  CreateUserUsecase,
  GetUserByEmailUsecase,
} from "@/domain/usecases/user/index";
import RegisterDashUsecase from "@/domain/usecases/user/Register/RegisterDashUsecase";
import CreateDashRepository from "@/infrastructure/repositories/dash/CreateDashRepository";
import CreateUserRepository from "@/infrastructure/repositories/user/CreateUserRepository";
import GetUserByEmailRepository from "@/infrastructure/repositories/user/GetUserByEmailRepository";
import {
  DashInvitationFormData,
  dashInvitationSchema,
} from "@/shared/schemas/DashInvitationSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "../use-toast";
import { PostgrestError } from "@supabase/supabase-js";
import { useNavigate } from "react-router-dom";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useState } from "react";
import CreateAuthentificationUserRepository from "@/infrastructure/repositories/user/CreateAuthentificationUserRepository";
import MarkInvitationDashAsUsedRepository from "@/infrastructure/repositories/invitationDash/MarkInvitationDashAsUsedRepository.ts";
import { MarkInvitationDashAsUsedUsecase } from "@/domain/usecases/invitationDash/MarkInvitationDashAsUsedUsecase.ts";

const createUserRepository = new CreateUserRepository();
const createDashRepository = new CreateDashRepository();
const passwordService = new PasswordService();
const createUserUsecase = new CreateUserUsecase(
  createUserRepository,
  passwordService
);
const createDashUsecase = new CreateDashUsecase(createDashRepository);
const getUserByEmailRepository = new GetUserByEmailRepository();
const getUserByEmailUsecase = new GetUserByEmailUsecase(
  getUserByEmailRepository
);
const createAuthentificationUserRepository =
  new CreateAuthentificationUserRepository();
const createAuthentificationUserUsecase = new CreateAuthentificationUserUsecase(
  createAuthentificationUserRepository
);

const markInvitationDashAsUsedRepository =
  new MarkInvitationDashAsUsedRepository();
const markInvitationDashAsUsedUsecase = new MarkInvitationDashAsUsedUsecase(
  markInvitationDashAsUsedRepository
);

const verifyAndCheckEmail = new VerifyAndCheckEmail(getUserByEmailUsecase);

const registerDashUsecase = new RegisterDashUsecase(
  verifyAndCheckEmail,
  createUserUsecase,
  createDashUsecase,
  createAuthentificationUserUsecase,
  markInvitationDashAsUsedUsecase
);

const useDashForm = (
  invitationId: number,
  defaultValue?: DashInvitationFormData
) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<DashInvitationFormData>({
    resolver: zodResolver(dashInvitationSchema),
    mode: "onBlur",
    defaultValues: defaultValue || { email: "", organizationName: "" },
  });
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const toast = useToast();

  const onSubmit = async (data: DashInvitationFormData) => {
    try {
      setIsLoading(true);
      const result = await registerDashUsecase.execute(data, invitationId);

      if (result.success) {
        navigate(`/${PublicRoutesNavigation.LOGIN_PAGE}`);
      } else {
        toast.error("Une erreur inconnue est survenue.");
      }
    } catch (error) {
      if (error instanceof Error || error instanceof PostgrestError) {
        toast.error(error.message);
      } else {
        toast.error("Une erreur inconnue est survenue.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return {
    register,
    handleSubmit: handleSubmit(onSubmit),
    errors,
    getValues,
    isLoading,
  };
};

export default useDashForm;
