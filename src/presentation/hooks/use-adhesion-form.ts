import { z } from "zod";
import { useState } from "react";
import { Commune, District } from "@/domain/models";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

// Zod validation schema
export const formSchema = z.object({
  firstName: z.string().min(2, "Le prénom doit contenir au moins 2 caractères"),
  lastName: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
  phone: z
    .string()
    .min(8, "Le numéro de téléphone doit contenir au moins 8 chiffres")
    .regex(/^[0-9+\s-]+$/, "Numéro de téléphone invalide"),
  email: z.string().email("Adresse e-mail invalide"),
  district: z.string().min(1, "Le district est requis"),
  commune: z.string().min(1, "La commune est requise"),
  typeEtablissement: z.string().min(1, "Le type d'établissement est requis"),
  adresse: z.string().min(1, "L'adresse est requise"),
});

export type FormData = z.infer<typeof formSchema>;

export const useAdhesionForm = () => {
  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    phone: "",
    email: "",
    district: "",
    commune: "",
    typeEtablissement: "",
    adresse: "",
  });
  const { control, register, setValue, getValues } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleChange =
    (field: string) => (event: React.ChangeEvent<{ value: unknown }>) => {
      const value = event.target.value as string;
      setFormData((prev) => ({ ...prev, [field]: value }));
      if (errors[field]) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[field];
          return newErrors;
        });
      }
    };

  const handleTypeEtablissementChange = (_: unknown, value: string | null) => {
    setFormData((prev) => ({ ...prev, typeEtablissement: value || "" }));
    if (errors.typeEtablissement) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.typeEtablissement;
        return newErrors;
      });
    }
  };

  const handleCommuneChange = (value: string | null) => {
    setFormData((prev) => ({ ...prev, commune: value || "" }));
    if (errors.commune) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.commune;
        return newErrors;
      });
    }
  };

  const handleDistrictChange = (value: string | null) => {
    setFormData((prev) => ({ ...prev, district: value || "" }));
    if (errors.district) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.district;
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const updatedFormData = {
      ...formData,
    };

    try {
      const validatedData = formSchema.parse(updatedFormData);
      return { isValid: true, data: validatedData, errors: null };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: { [key: string]: string } = {};
        error.errors.forEach((err) => {
          const path = err.path.join(".");
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
        return { isValid: false, data: null, errors: newErrors };
      }
      return {
        isValid: false,
        data: null,
        errors: { form: "Erreur de validation" },
      };
    }
  };

  return {
    formData,
    errors,
    handleChange,
    handleTypeEtablissementChange,
    handleCommuneChange,
    handleDistrictChange,
    validateForm,
    register,
  };
};
