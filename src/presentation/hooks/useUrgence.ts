import { Urgence } from "@/domain/models";
import { AppDispatch, RootState } from "@/store";
import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  createUrgenceSlice,
  getAllUrgenceSlices,
  updateUrgenceSlice,
  deleteUrgenceSlice,
  setSelectedUrgenceSlice,
  clearSelectedUrgenceSlice,
} from "@/application/slices/patient/urgenceSlice";

export const useUrgence = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    urgence,
    loading,
    error
  } = useSelector(
    (state: RootState) => state.urgence
  );

  const create = useCallback(
    async (data: Omit<Urgence, "id">[]) => {
      await dispatch(createUrgenceSlice(data));
    },
    [dispatch]
  );

  const getAll = useCallback(
    async (carnetId: number) => {
      await dispatch(getAllUrgenceSlices(carnetId));
    },
    [dispatch]
  );

  const update = useCallback(
    async (id: number, data: Partial<Urgence>) => {
      await dispatch(updateUrgenceSlice({ id, data }));
    },
    [dispatch]
  );

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteUrgenceSlice(id));
    },
    [dispatch]
  );

  const select = useCallback(
    (allergie: Urgence | null) => {
      dispatch(setSelectedUrgenceSlice(allergie));
    },
    [dispatch]
  );

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedUrgenceSlice());
  }, [dispatch]);

  return {
    loading,
    error,
    urgence,
    create,
    select,
    getAll,
    update,
    remove,
    clearSelected,
  };
};
