import { useCallback, useRef, useState } from "react";
import { useToast } from "@/presentation/hooks/use-toast";
import {
  MessageCompositionHook,
  MessageCompositionState,
} from "@/presentation/types/message.types";
import {
  ALLOWED_FILE_TYPES,
  MAX_FILE_SIZE,
  MESSAGE_ERROR_MESSAGES,
  MESSAGE_LIMITS,
} from "@/presentation/constants/message.constants";

/**
 * Custom hook for managing message composition state and operations
 * Handles text input, file attachments, validation, and sending
 *
 * @param onSendMessage - Callback function to send the message
 * @returns {MessageCompositionHook} Composition state and operations
 */
export const useMessageComposition = (
  onSendMessage: (content: string, attachments?: File[]) => Promise<void>
): MessageCompositionHook => {
  // State management
  const [state, setState] = useState<MessageCompositionState>({
    content: "",
    isSending: false,
    attachments: [],
    error: undefined,
  });

  const toast = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * Update message content
   * @param content - New message content
   */
  const setContent = useCallback((content: string) => {
    // Clear error when user starts typing
    setState((prev) => ({
      ...prev,
      content,
      error:
        content.length > MESSAGE_LIMITS.maxLength
          ? MESSAGE_ERROR_MESSAGES.messageTooLong
          : undefined,
    }));
  }, []);

  /**
   * Validate file before adding as attachment
   * @param file - File to validate
   * @returns {boolean} Whether file is valid
   */
  const validateFile = useCallback(
    (file: File): boolean => {
      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        toast.error(MESSAGE_ERROR_MESSAGES.fileTooLarge);
        return false;
      }

      // Check file type
      if (!ALLOWED_FILE_TYPES.includes(file.type as any)) {
        toast.error(MESSAGE_ERROR_MESSAGES.fileTypeNotAllowed);
        return false;
      }

      return true;
    },
    [toast]
  );

  /**
   * Add attachment to the message
   * @param file - File to attach
   */
  const addAttachment = useCallback(
    (file: File) => {
      setState((prev) => {
        // Check if we've reached the maximum number of attachments
        if (prev.attachments.length >= MESSAGE_LIMITS.maxAttachments) {
          toast.error(MESSAGE_ERROR_MESSAGES.tooManyAttachments);
          return prev;
        }

        // Validate the file
        if (!validateFile(file)) {
          return prev;
        }

        // Check if file is already attached
        const isDuplicate = prev.attachments.some(
          (attachment) =>
            attachment.name === file.name && attachment.size === file.size
        );

        if (isDuplicate) {
          toast.error("Ce fichier est déjà attaché.");
          return prev;
        }

        return {
          ...prev,
          attachments: [...prev.attachments, file],
          error: undefined,
        };
      });
    },
    [validateFile, toast]
  );

  /**
   * Remove attachment from the message
   * @param index - Index of attachment to remove
   */
  const removeAttachment = useCallback((index: number) => {
    setState((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index),
      error: undefined,
    }));
  }, []);

  /**
   * Handle file input change
   * @param event - File input change event
   */
  const handleFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (!files) return;

      Array.from(files).forEach((file) => {
        addAttachment(file);
      });

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [addAttachment]
  );

  /**
   * Trigger file selection dialog
   */
  const triggerFileSelection = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  /**
   * Validate message before sending
   * @returns {boolean} Whether message is valid
   */
  const validateMessage = useCallback((): boolean => {
    const trimmedContent = state.content.trim();

    // Check if message is empty and no attachments
    if (!trimmedContent && state.attachments.length === 0) {
      setState((prev) => ({
        ...prev,
        error: MESSAGE_ERROR_MESSAGES.messageEmpty,
      }));
      return false;
    }

    // Check message length
    if (trimmedContent.length > MESSAGE_LIMITS.maxLength) {
      setState((prev) => ({
        ...prev,
        error: MESSAGE_ERROR_MESSAGES.messageTooLong,
      }));
      return false;
    }

    // Check attachments count
    if (state.attachments.length > MESSAGE_LIMITS.maxAttachments) {
      setState((prev) => ({
        ...prev,
        error: MESSAGE_ERROR_MESSAGES.tooManyAttachments,
      }));
      return false;
    }

    return true;
  }, [state.content, state.attachments]);

  /**
   * Send the message
   */
  const sendMessage = useCallback(async () => {
    // Validate message
    if (!validateMessage()) {
      return;
    }

    setState((prev) => ({ ...prev, isSending: true, error: undefined }));

    try {
      await onSendMessage(state.content.trim(), state.attachments);

      // Reset state after successful send
      setState({
        content: "",
        isSending: false,
        attachments: [],
        error: undefined,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : MESSAGE_ERROR_MESSAGES.sendFailed;

      setState((prev) => ({
        ...prev,
        isSending: false,
        error: errorMessage,
      }));

      toast.error(errorMessage);
    }
  }, [validateMessage, onSendMessage, state.content, state.attachments, toast]);

  /**
   * Handle keyboard shortcuts
   * @param event - Keyboard event
   */
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      // Send message on Enter (without Shift)
      if (event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
      }
    },
    [sendMessage]
  );

  /**
   * Reset composition state
   */
  const reset = useCallback(() => {
    setState({
      content: "",
      isSending: false,
      attachments: [],
      error: undefined,
    });
  }, []);

  /**
   * Format file size for display
   * @param bytes - File size in bytes
   * @returns {string} Formatted file size
   */
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  }, []);

  /**
   * Get file icon based on file type
   * @param fileType - MIME type of the file
   * @returns {string} Icon name
   */
  const getFileIcon = useCallback((fileType: string): string => {
    if (fileType.startsWith("image/")) return "image";
    if (fileType === "application/pdf") return "file-text";
    if (fileType.includes("word") || fileType.includes("document")) {
      return "file-text";
    }
    if (fileType === "text/plain") return "file-text";
    return "file";
  }, []);

  /**
   * Check if message can be sent
   * @returns {boolean} Whether message can be sent
   */
  const canSendMessage = useCallback((): boolean => {
    const hasContent = state.content.trim().length > 0;
    const hasAttachments = state.attachments.length > 0;
    const isNotSending = !state.isSending;
    const hasNoError = !state.error;

    return (hasContent || hasAttachments) && isNotSending && hasNoError;
  }, [state.content, state.attachments, state.isSending, state.error]);

  return {
    state,
    setContent,
    addAttachment,
    removeAttachment,
    sendMessage,
    reset,
    // Additional utility functions
    handleKeyDown,
    handleFileInputChange,
    triggerFileSelection,
    formatFileSize,
    getFileIcon,
    canSendMessage,
    fileInputRef,
  } as MessageCompositionHook;
};
