import { useState, useEffect, useCallback, useRef } from "react";
import { RealtimeMessagingService } from "@/infrastructure/services/realtime/RealtimeMessagingService";

interface TypingUser {
  userId: number;
  userName?: string;
  timestamp: number;
}

interface UseTypingIndicatorProps {
  conversationId: number | null;
  currentUserId: number;
  realtimeService: RealtimeMessagingService | null;
}

interface UseTypingIndicatorReturn {
  typingUsers: TypingUser[];
  sendTyping: () => void;
  isAnyoneTyping: boolean;
  typingMessage: string;
}

const TYPING_TIMEOUT = 3000; // 3 seconds
const TYPING_DEBOUNCE = 1000; // 1 second between typing events

export const useTypingIndicator = ({
  conversationId,
  currentUserId,
  realtimeService,
}: UseTypingIndicatorProps): UseTypingIndicatorReturn => {
  const [typingUsers, setTypingUsers] = useState<Map<number, TypingUser>>(
    new Map()
  );
  const lastTypingSentRef = useRef<number>(0);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up typing users after timeout
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setTypingUsers((prev) => {
        const updated = new Map(prev);
        let hasChanges = false;

        updated.forEach((user, userId) => {
          if (now - user.timestamp > TYPING_TIMEOUT) {
            updated.delete(userId);
            hasChanges = true;
          }
        });

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Handle incoming typing events
  useEffect(() => {
    if (!realtimeService || !conversationId) return;

    const handleTypingEvent = (data: {
      userId: number;
      conversationId: number;
      isTyping: boolean;
      userName?: string;
    }) => {
      // Ignore typing events from current user
      if (data.userId === currentUserId) return;

      // Only handle events for current conversation
      if (data.conversationId !== conversationId) return;

      setTypingUsers((prev) => {
        const updated = new Map(prev);

        if (data.isTyping) {
          updated.set(data.userId, {
            userId: data.userId,
            userName: data.userName,
            timestamp: Date.now(),
          });
        } else {
          updated.delete(data.userId);
        }

        return updated;
      });
    };

    // Subscribe to typing events through the realtime service
    realtimeService.subscribeToConversation(conversationId, {
      onTyping: handleTypingEvent,
    });

    return () => {
      // Cleanup will be handled by the parent component
    };
  }, [realtimeService, conversationId, currentUserId]);

  // Send typing indicator
  const sendTyping = useCallback(() => {
    if (!realtimeService || !conversationId) return;

    const now = Date.now();

    // Debounce typing events
    if (now - lastTypingSentRef.current < TYPING_DEBOUNCE) {
      return;
    }

    lastTypingSentRef.current = now;
    realtimeService.sendTypingIndicator(conversationId, currentUserId, true);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set timeout to send stop typing
    typingTimeoutRef.current = setTimeout(() => {
      if (realtimeService && conversationId) {
        realtimeService.sendTypingIndicator(
          conversationId,
          currentUserId,
          false
        );
      }
    }, TYPING_TIMEOUT);
  }, [realtimeService, conversationId, currentUserId]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      // Send stop typing when component unmounts
      if (realtimeService && conversationId) {
        realtimeService.sendTypingIndicator(
          conversationId,
          currentUserId,
          false
        );
      }
    };
  }, [realtimeService, conversationId, currentUserId]);

  // Calculate typing message
  const typingUsersArray = Array.from(typingUsers.values());
  const isAnyoneTyping = typingUsersArray.length > 0;

  let typingMessage = "";
  if (isAnyoneTyping) {
    if (typingUsersArray.length === 1) {
      const userName = typingUsersArray[0].userName || "Quelqu'un";
      typingMessage = `${userName} est en train d'écrire...`;
    } else if (typingUsersArray.length === 2) {
      const names = typingUsersArray
        .map((u) => u.userName || "Quelqu'un")
        .join(" et ");
      typingMessage = `${names} sont en train d'écrire...`;
    } else {
      typingMessage = `${typingUsersArray.length} personnes sont en train d'écrire...`;
    }
  }

  return {
    typingUsers: typingUsersArray,
    sendTyping,
    isAnyoneTyping,
    typingMessage,
  };
};
