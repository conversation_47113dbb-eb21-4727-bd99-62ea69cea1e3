import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useAppDispatch, useAppSelector } from "@/presentation/hooks/redux";
import { useToast } from "@/presentation/hooks/use-toast";
import { RealtimeMessagingService } from "@/infrastructure/services/realtime/RealtimeMessagingService";
import { useTypingIndicator } from "@/presentation/hooks/messaging/useTypingIndicator";
import {
  ConversationDTO,
  MessageDTO,
  Contact,
  MessagingDataHook,
  MessageSearchFilters,
} from "@/presentation/types/message.types";
import {
  getContactMessageSlice,
  getMessageByConversationIdSlices,
  createMessageSlice,
} from "@/application/slices/message/messageSlice";
import {
  createConversationSlice,
  getConversationSlice,
  updateConversationSlice,
  deleteConversationSlice,
  markConversationAsReadSlice,
} from "@/application/slices/conversation/conversationSlice";
import {
  MESSAGE_ERROR_MESSAGES,
  MESSAGE_SUCCESS_MESSAGES,
} from "@/presentation/constants/message.constants";
import {
  message_status_enum,
  type_message_enum,
  utilisateurs_role_enum,
} from "@/domain/models/enums";
import sound from "@/assets/audio/notification.mp3";
import { conversation } from "@/domain/models";
export const useMessagingData = (): MessagingDataHook => {
  const dispatch = useAppDispatch();
  const {
    error: messageError,
    contacts: contactMessage,
    messagesDTO,
  } = useAppSelector((state) => state.message);
  const { error: conversationError, conversationsDTO } = useAppSelector(
    (state) => state.conversation
  );
  // State management
  const [conversations, setConversations] = useState<ConversationDTO[]>([]);
  const [activeConversation, setActiveConversationState] =
    useState<ConversationDTO | null>(null);
  const [messages, setMessages] = useState<MessageDTO[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchFilters, setSearchFilters] = useState<MessageSearchFilters>({});

  // Get current user data from Redux store
  const currentUser = useAppSelector((state) => state.authentification.user);

  // Realtime service instance
  const realtimeServiceRef = useRef<RealtimeMessagingService | null>(null);

  // Typing indicator hook
  const { typingUsers, sendTyping, isAnyoneTyping, typingMessage } =
    useTypingIndicator({
      conversationId: activeConversation?.id || null,
      currentUserId: currentUser?.id || 0,
      realtimeService: realtimeServiceRef.current,
    });
  const authData = useAppSelector((state) => state.authentification.authData);
  const toast = useToast();

  /**
   * Load conversations from API
   */
  const loadConversations = useCallback(async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    setError(null);

    try {
      const conversation = await dispatch(
        getConversationSlice(currentUser.id)
      ).unwrap();
      setConversations(conversation);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : MESSAGE_ERROR_MESSAGES.loadFailed;
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [currentUser?.id, conversationsDTO, toast]);

  /**
   * Load messages for a specific conversation
   */
  const loadMessages = useCallback(
    async (conversationId: number) => {
      setLoading(true);
      setError(null);

      try {
        const response = await dispatch(
          getMessageByConversationIdSlices({
            conversationId: conversationId,
            userId: currentUser?.id,
          })
        ).unwrap();
        setMessages(response);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : MESSAGE_ERROR_MESSAGES.loadFailed;
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    [messagesDTO, toast]
  );

  /**
   * Load available contacts
   */
  const loadContacts = useCallback(
    async (role: utilisateurs_role_enum) => {
      if (!currentUser?.id) return;

      try {
        const response = await dispatch(getContactMessageSlice(role)).unwrap();
        setContacts(response);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : MESSAGE_ERROR_MESSAGES.loadFailed;
        setError(errorMessage);
        toast.error(errorMessage);
      }
    },
    [currentUser?.id, contactMessage, toast]
  );

  /**
   * Set active conversation and load its messages
   */
  const setActiveConversation = useCallback(
    async (conversationId: number) => {
      const conversation = conversations.find(
        (conv) => conv.id === conversationId
      );
      if (!conversation) {
        toast.error(MESSAGE_ERROR_MESSAGES.conversationNotFound);
        return;
      }

      setActiveConversationState(conversation);
      await loadMessages(conversationId);
    },
    [conversations, loadMessages, toast]
  );

  /**
   * Send a message
   */
  const sendMessage = useCallback(
    async (conversationId: number, content: string, attachments?: File[]) => {
      if (!content.trim()) {
        toast.error(MESSAGE_ERROR_MESSAGES.messageEmpty);
        return;
      }

      if (!currentUser?.id) {
        toast.error(MESSAGE_ERROR_MESSAGES.permissionDenied);
        return;
      }

      try {
        const message = await dispatch(
          createMessageSlice({
            id_conversation: conversationId,
            contenu: content,
            id_expediteur: currentUser.id,
            envoye_a: new Date(),
            status: message_status_enum.sent,
            est_proprietaire: true,
          })
        ).unwrap();

        if (message.id) {
          const data: Partial<conversation> = {
            dernier_message: message.contenu,
            mis_a_jour_a: message.envoye_a,
          };
          await dispatch(
            updateConversationSlice({
              id: conversationId,
              data: data,
            })
          );
          // Create mock message
          const newMessage: MessageDTO = {
            id: message.id,
            conversationId: message.id_conversation,
            senderId: currentUser.id,
            senderName: `${currentUser.email}`.trim(),
            senderRole: currentUser.role, // This should be determined from user data
            content: message.contenu,
            type: type_message_enum.text,
            status: message_status_enum.sent,
            sentAt: new Date(),
            isOwn: true,
          };

          setMessages((prev) => [...prev, newMessage]);
          setConversations((prev) => {
            const newConversation = prev.map((conversation) =>
              conversation.id === conversationId
                ? {
                    ...conversation,
                    lastMessage: newMessage.content,
                    updatedAt: new Date(),
                  }
                : conversation
            );
            return newConversation;
          });
        } else {
          console.log(messageError);
          return;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : MESSAGE_ERROR_MESSAGES.sendFailed;
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    [currentUser, toast]
  );

  /**
   * Mark conversation as read
   */
  const markAsRead = useCallback(
    async (conversationId: number) => {
      try {
        const response = await dispatch(
          markConversationAsReadSlice(conversationId)
        ).unwrap();

        // Update local state
        if (response.id) {
          setConversations((prev) =>
            prev.map((conv) =>
              conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv
            )
          );
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : MESSAGE_ERROR_MESSAGES.loadFailed;
        toast.error(errorMessage);
      }
    },
    [toast]
  );

  /**
   * Search conversations
   */
  const searchConversations = useCallback((query: string) => {
    setSearchFilters((prev) => ({ ...prev, query }));
  }, []);

  const getConversationBetween = (
    conversations: ConversationDTO[],
    userId1: number,
    userId2: number
  ): ConversationDTO | undefined => {
    return conversations.find(
      (c) =>
        (c.idExpediteur === userId1 && c.idParticipant === userId2) ||
        (c.idExpediteur === userId2 && c.idParticipant === userId1)
    );
  };

  /**
   * Create new conversation
   */
  const createNewConversation = useCallback(
    async (contactId: number) => {
      const contact = contacts.find((c) => c.id === contactId);
      if (!contact) {
        toast.error(MESSAGE_ERROR_MESSAGES.contactNotFound);
        return;
      }

      try {
        const existingConversation = getConversationBetween(
          conversations,
          currentUser.id,
          contactId
        );

        if (existingConversation) {
          setActiveConversation(existingConversation.id);
        } else {
          const response = await dispatch(
            createConversationSlice({
              cree_a: new Date(),
              id_expediteur: currentUser.id,
              id_recepteur: contactId,
              mis_a_jour_a: new Date(),
              nombre_non_lu: 0,
            })
          ).unwrap();

          const newConversation: ConversationDTO = {
            id: response.id,
            idExpediteur: response.id_expediteur,
            idParticipant: response.id_recepteur,
            participant: {
              id: contact.id,
              name: contact.name,
              role: contact.role,
              isOnline: contact.isOnline,
            },
            lastMessage: "",
            lastMessageTime: "",
            unreadCount: 0,
            createdAt: response.cree_a,
            updatedAt: response.mis_a_jour_a,
          };

          setActiveConversationState(newConversation);
          setMessages([]);
          toast.success(MESSAGE_SUCCESS_MESSAGES.conversationCreated);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : MESSAGE_ERROR_MESSAGES.loadFailed;
        toast.error(errorMessage);
      }
    },
    [contacts, currentUser, toast]
  );

  /**
   * Archive conversation
   */
  const archiveConversation = useCallback(
    async (conversationId: number) => {
      try {
        // TODO: Replace with actual API call
        // await conversationAPI.archive(conversationId);

        setConversations((prev) =>
          prev.map((conv) =>
            conv.id === conversationId ? { ...conv, isArchived: true } : conv
          )
        );
        toast.success(MESSAGE_SUCCESS_MESSAGES.conversationArchived);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : MESSAGE_ERROR_MESSAGES.loadFailed;
        toast.error(errorMessage);
      }
    },
    [toast]
  );

  /**
   * Delete conversation
   */
  const deleteConversation = useCallback(
    async (conversationId: number) => {
      try {
        const id = await dispatch(
          deleteConversationSlice(conversationId)
        ).unwrap();
        if (id) {
          setConversations((prev) =>
            prev.filter((conv) => conv.id !== conversationId)
          );
          if (activeConversation?.id === conversationId) {
            setActiveConversationState(null);
            setMessages([]);
          }
          toast.success(MESSAGE_SUCCESS_MESSAGES.conversationDeleted);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : MESSAGE_ERROR_MESSAGES.loadFailed;
        toast.error(errorMessage);
      }
    },
    [activeConversation?.id, toast]
  );

  /**
   * Refresh conversations
   */
  const refreshConversations = useCallback(() => {
    loadConversations();
  }, [loadConversations]);

  // Filter conversations based on search
  const filteredConversations = useMemo(() => {
    if (!searchFilters.query) return conversations;

    const query = searchFilters.query.toLowerCase();
    return conversations.filter(
      (conv) =>
        conv.participant.name.toLowerCase().includes(query) ||
        conv.lastMessage?.toLowerCase().includes(query)
    );
  }, [conversations, searchFilters.query]);

  // Initialize realtime service
  useEffect(() => {
    if (currentUser?.id) {
      // Create realtime service instance
      realtimeServiceRef.current = new RealtimeMessagingService(currentUser.id);

      // Subscribe to user conversations for realtime updates
      realtimeServiceRef.current.subscribeToUserConversations(currentUser.id, {
        onNewConversation: async (newConv) => {
          const otherUserId =
            newConv.id_expediteur === currentUser.id
              ? newConv.id_recepteur
              : newConv.id_expediteur;

          const response = await dispatch(
            getContactMessageSlice(currentUser.role)
          ).unwrap();

          const participant = response?.find((c) => c.id === otherUserId) || {
            id: otherUserId,
            name: "Inconnu",
            role: utilisateurs_role_enum.PATIENT,
            isOnline: false,
          };

          const newConversation: ConversationDTO = {
            id: newConv.id,
            idExpediteur: newConv.id_expediteur,
            idParticipant: newConv.id_recepteur,
            participant,
            lastMessage: newConv.dernier_message || "",
            lastMessageTime: "",
            unreadCount: newConv.nombre_non_lu || 0,
            createdAt: newConv.cree_a,
            updatedAt: newConv.mis_a_jour_a,
          };

          setConversations((prev) => [newConversation, ...prev]);
        },
        onUpdateConversation: (updatedConversation) => {
          // Update conversation in local state
          setConversations((prev) => {
            const existingIndex = prev.findIndex(
              (c) => c.id === updatedConversation.id
            );
            if (existingIndex !== -1) {
              const updated = [...prev];
              // Update the conversation while preserving the DTO structure
              const existingConv = updated[existingIndex];
              updated[existingIndex] = {
                ...existingConv,
                lastMessage:
                  updatedConversation.dernier_message ||
                  existingConv.lastMessage,
                lastMessageTime: updatedConversation.mis_a_jour_a
                  ? ""
                  : existingConv.lastMessageTime,
                unreadCount:
                  updatedConversation.nombre_non_lu ?? existingConv.unreadCount,
                updatedAt:
                  updatedConversation.mis_a_jour_a || existingConv.updatedAt,
              };
              // Sort conversations by last message time
              return updated.sort(
                (a, b) =>
                  new Date(b.lastMessageTime).getTime() -
                  new Date(a.lastMessageTime).getTime()
              );
            }
            return prev;
          });
        },
        onPresence: (data) => {
          // Update contact online status
          setContacts((prev) =>
            prev.map((contact) =>
              contact.id === data.userId
                ? { ...contact, isOnline: data.status === "online" }
                : contact
            )
          );
          // Update conversation participant online status
          setConversations((prev) =>
            prev.map((conv) => {
              if (conv.participant.id === data.userId) {
                return {
                  ...conv,
                  participant: {
                    ...conv.participant,
                    isOnline: data.status === "online",
                  },
                };
              }
              return conv;
            })
          );
        },
      });

      // Set online status
      realtimeServiceRef.current.updatePresenceStatus("online");

      // Handle visibility change for presence
      const handleVisibilityChange = () => {
        if (document.visibilityState === "visible") {
          realtimeServiceRef.current?.updatePresenceStatus("online");
        } else {
          realtimeServiceRef.current?.updatePresenceStatus("offline");
        }
      };

      document.addEventListener("visibilitychange", handleVisibilityChange);

      // Cleanup function
      return () => {
        document.removeEventListener(
          "visibilitychange",
          handleVisibilityChange
        );
        if (realtimeServiceRef.current) {
          realtimeServiceRef.current.cleanup();
          realtimeServiceRef.current = null;
        }
      };
    }
  }, [currentUser?.id]);

  // Subscribe to active conversation for realtime messages
  useEffect(() => {
    if (
      activeConversation?.id &&
      currentUser?.id &&
      realtimeServiceRef.current
    ) {
      // Subscribe to conversation for realtime updates
      realtimeServiceRef.current.subscribeToConversation(
        activeConversation.id,
        {
          onNewMessage: (newMessage) => {
            // Add new message to the messages list
            const messageDTO: MessageDTO = {
              id: newMessage.id,
              conversationId: newMessage.id_conversation,
              senderId: newMessage.id_expediteur,
              senderName: activeConversation.participant.name,
              senderRole: activeConversation.participant.role,
              content: newMessage.contenu,
              type: type_message_enum.text,
              status: message_status_enum.sent,
              sentAt: new Date(newMessage.envoye_a),
              readAt: undefined,
              isOwn: false,
            };

            setMessages((prev) => [...prev, messageDTO]);

            // Update conversation with new message
            setConversations((prev) =>
              prev.map((conv) =>
                conv.id === activeConversation.id
                  ? {
                      ...conv,
                      lastMessage: newMessage.contenu,
                      lastMessageTime: new Date(
                        newMessage.envoye_a
                      ).toISOString(),
                      unreadCount: conv.unreadCount + 1,
                    }
                  : conv
              )
            );

            try {
              const audio = new Audio(sound);
              audio.play();
            } catch (error) {
              toast.error(error);
            }
          },
          onUpdateMessage: (updatedMessage) => {
            // Update existing message
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === updatedMessage.id
                  ? {
                      ...msg,
                      status: message_status_enum.sent,
                      readAt: undefined,
                    }
                  : msg
              )
            );
          },
          onDeleteMessage: (messageId) => {
            // Remove deleted message
            setMessages((prev) => prev.filter((msg) => msg.id !== messageId));
          },
        }
      );

      return () => {
        // Unsubscribe when conversation changes
        if (realtimeServiceRef.current) {
          realtimeServiceRef.current.unsubscribeFromConversation(
            activeConversation.id
          );
        }
      };
    }
  }, [activeConversation?.id, currentUser?.id]);

  // Load initial data
  useEffect(() => {
    if (currentUser?.id) {
      loadConversations();
      loadContacts(currentUser.role);
    }
  }, [currentUser?.id]);

  return {
    conversations: filteredConversations,
    activeConversation,
    messages,
    contacts,
    loading,
    error,
    setActiveConversation,
    typingUsers,
    sendTyping,
    isAnyoneTyping,
    typingMessage,
    sendMessage,
    markAsRead,
    searchConversations,
    createNewConversation,
    archiveConversation,
    deleteConversation,
    refreshConversations,
  };
};
