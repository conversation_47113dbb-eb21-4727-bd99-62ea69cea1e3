import { patients_groupe_sanguin_enum, sexe_enum } from "@/domain/models/enums";
import { useRegisterPatientStateContext } from "../contexts/useContext/useRegisterPatientStateContext";
import { Patient, Urgence } from "@/domain/models";
import { validateRegisterField } from "@/shared/utils/validateRegisterField";

export const useRegisterPatientState = () => {
  const { state, dispatch } = useRegisterPatientStateContext();

  const updateField = (
    field: string,
    value:
      | string
      | number
      | boolean
      | null
      | sexe_enum
      | Date
      | {
          [key: string]: string;
        }
      | { numero: string }[]
      | {
          contact_urgence_nom: string;
          contact_urgence_prenom?: string;
          contact_urgence_telephone: string;
          relation?: string;
          adresse?: string;
        }[]
  ) => {
    dispatch({
      type: "UPDATE_FIELD",
      payload: { field, value },
    });
  };

  const handleEmailChange = (value: string) => {
    updateField("email", value);
    validateRegisterField("email", value, state.errors, (newErrors) => {
      updateField("errors", newErrors);
    });
  };
  const handlePasswordChange = (value: string) =>
    updateField("password", value);
  const handleConfirmEmailChange = (value: string) =>
    updateField("confirmEmail", value);
  const handleNomChange = (value: string) => updateField("nom", value);
  const handlePrenomChange = (value: string) => updateField("prenom", value);
  const handleSexeChange = (value: sexe_enum) => updateField("sexe", value);
  const handleDateNaissanceChange = (value: Date) =>
    updateField("date_naissance", value);
  const handleAdresseChange = (value: string) => updateField("adresse", value);
  const handleProfessionChange = (value: string) =>
    updateField("profession", value);
  const handleNbEnfantChange = (value: number) =>
    updateField("nb_enfant", value);
  const handleRegionChange = (value: string) => updateField("region", value);
  const handleDistrictChange = (value: string) =>
    updateField("district", value);
  const handleCommuneChange = (value: string) => updateField("commune", value);
  const handleFokontanyChange = (value: string) =>
    updateField("fokontany", value);
  const handleDonneurSangChange = (value: string) =>
    updateField("donneur_sang", value === "oui");
  const handleGroupeSanguinChange = (value: patients_groupe_sanguin_enum) =>
    updateField("groupe_sanguin", value);
  const handleNationaliteChange = (value: string) =>
    updateField("nationalite", value);
  const handlePaysChange = (value: string) => updateField("pays", value);
  const handleSituationMatrimonialeChange = (value: string) =>
    updateField("situation_matrimonial", value);
  const handleAutreProfessionChange = (value: string) =>
    updateField("autre_profession", value);
  const handleNumeroTelephoneChange = (value: string) =>
    updateField("telephone", value);
  const handleContactChange = (index: number, value: string) =>
    updateField(
      "contacts",
      state.contacts.map((contact, idx) =>
        idx === index ? { ...contact, numero: value } : contact
      )
    );
  const handleRemoveContact = (index: number) =>
    updateField(
      "contacts",
      state.contacts.filter((_, idx) => idx !== index)
    );
  const handleAddContact = () =>
    updateField("contacts", [...state.contacts, { numero: "" }]);
  const handleAddContactUrgence = () =>
    updateField("urgences", [
      ...state.urgences,
      {
        contact_urgence_nom: "",
        contact_urgence_prenom: "",
        contact_urgence_telephone: "",
      },
    ]);

  const handleRemoveContactUrgence = (index: number) =>
    updateField(
      "urgences",
      state.urgences.filter((_, idx) => idx !== index)
    );

  const handleContactUrgenceChange = (
    index: number,
    field: keyof (typeof state.urgences)[0],
    value: string
  ) =>
    updateField(
      "urgences",
      state.urgences.map((contact, idx) =>
        idx === index ? { ...contact, [field]: value } : contact
      )
    );

  const handleInitialiseContactUrgence = (urgence: Urgence[]) =>
    updateField("urgences", urgence);

  const handleAcceptConditionsChange = (value: boolean) =>
    updateField("acceptConditions", value);

  const initialiseState = (
    patient: Patient & { avatar?: string; urgence?: Urgence[] }
  ) => {
    // handleEmailChange(patient.nom);
    // handlePasswordChange(patient.nom);
    handleNomChange(patient.nom);
    handlePrenomChange(patient.prenom);
    handleSexeChange(patient.sexe);
    handleDateNaissanceChange(new Date(patient.date_naissance));
    handleAdresseChange(patient.adresse);
    handleProfessionChange(patient.profession);
    handleNbEnfantChange(patient.nb_enfant);
    // handleRegionChange(patient.region);
    handleDistrictChange(patient.district);
    handleCommuneChange(patient.commune);
    handleFokontanyChange(patient.region);
    handleDonneurSangChange(patient.donneur_sang === true ? "oui" : "non");
    handleGroupeSanguinChange(patient.groupe_sanguin);
    handleNationaliteChange(patient.nationalite);
    handlePaysChange(patient.pays);
    handleSituationMatrimonialeChange(patient.situation_matrimonial);
    handleEmailChange(patient.email);
    handleNumeroTelephoneChange(patient.telephone);
    if (patient.urgence) {
      handleInitialiseContactUrgence(patient.urgence);
    }
    // handleAutreProfessionChange(patient.profession);
  };

  const getPatientData = () => {
    return {
      nom: state.nom,
      prenom: state.prenom,
      sexe: state.sexe,
      date_naissance: state.date_naissance,
      adresse: state.adresse,
      profession: state.profession,
      nb_enfant: state.nb_enfant,
      district: state.district,
      commune: state.commune,
      fokontany: state.fokontany,
      donneur_sang: state.donneur_sang,
      groupe_sanguin: state.groupe_sanguin,
      nationalite: state.nationalite,
      pays: state.pays,
      situation_matrimonial: state.situation_matrimonial,
      email: state.email,
      telephone: state.telephone,
    };
  };

  const getContactUrgence = (patientId: number): Omit<Urgence, "id">[] => {
    return state.urgences.map((urgence) => ({
      id_patient: patientId,
      contact_urgence_nom: urgence.contact_urgence_nom,
      contact_urgence_prenom: urgence.contact_urgence_prenom,
      contact_urgence_telephone: urgence.contact_urgence_telephone,
      relation: urgence.relation,
      adresse: urgence.adresse,
    }));
  };

  const resetState = () => {
    dispatch({
      type: "RESET_STATE",
    });
  };

  const setErrors = (value: { [key: string]: string }) => {
    dispatch({
      type: "SET_ERRORS",
      payload: { errors: value },
    });
  };

  return {
    ...state,
    state,
    getPatientData,
    getContactUrgence,
    resetState,
    setErrors,
    handleEmailChange,
    handlePasswordChange,
    handleConfirmEmailChange,
    handleNomChange,
    handlePrenomChange,
    handleSexeChange,
    handleDateNaissanceChange,
    handleAdresseChange,
    handleProfessionChange,
    handleNbEnfantChange,
    handleRegionChange,
    handleDistrictChange,
    handleCommuneChange,
    handleFokontanyChange,
    handleDonneurSangChange,
    handleGroupeSanguinChange,
    handleNationaliteChange,
    handlePaysChange,
    handleSituationMatrimonialeChange,
    handleAutreProfessionChange,
    handleContactChange,
    handleAddContact,
    handleRemoveContact,
    handleAcceptConditionsChange,
    handleNumeroTelephoneChange,
    handleAddContactUrgence,
    handleRemoveContactUrgence,
    handleContactUrgenceChange,
    initialiseState,
  };
};
