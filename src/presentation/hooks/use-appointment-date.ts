export const useAppointmentDate = () => {
  const compareAppointmentDate = (d1: Date, d2: Date) => {
    const d1Number = d1.getTime();
    const d2Number = new Date(d2).getTime();

    const diff = Math.round((d1Number - d2Number) / 1000 / 60 / 60 / 24);

    return diff;
  };

  const isAppointmentDoneButtonDisabled = (appointmentDate: Date) => {
    const now = new Date();
    return compareAppointmentDate(now, appointmentDate) != 0;
  };

  return {
    compareAppointmentDate,
    isAppointmentDoneButtonDisabled,
  };
};

export default useAppointmentDate;
