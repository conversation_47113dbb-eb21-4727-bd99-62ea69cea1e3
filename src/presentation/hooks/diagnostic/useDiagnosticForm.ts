import { laboratoire_diagnostics } from "@/domain/models";
import { useAppDispatch, useAppSelector } from "../redux";
import {
  setTitle,
  setTypeFichier,
  setImpressionResultat,
  setSelectedFile,
  setPath,
  setRemarks,
  resetDiagnosticState,
} from "@/application/slices/professionnal/diagnosticSlice";
import { GetLocaleDate } from "@/shared/utils/getLocaleDate";

export const useDiagnosticForm = (idCarnetSante?: number) => {
  const dispatch = useAppDispatch();

  const { loading, diagnosticState } = useAppSelector(
    (state) => state.diagnostic
  );
  const isFormValid = () => {
    return (
      !!diagnosticState.remarks ||
      !!diagnosticState.type_fichier ||
      !!diagnosticState.titre
    );
  };

  const initialiseState = (diagnostic: laboratoire_diagnostics) => {
    dispatch(setTitle(diagnostic.titre));
    dispatch(setTypeFichier(diagnostic.type_fichier));
    dispatch(setImpressionResultat(diagnostic.resultat));
    dispatch(setPath(diagnostic.path));
    dispatch(setRemarks(diagnostic.remarque));
  };

  const resetForm = () => {
    dispatch(resetDiagnosticState());
  };

  const getDiagnosticData = (): Omit<laboratoire_diagnostics, "id"> => {
    return {
      id_carnet: idCarnetSante,
      titre: diagnosticState.titre,
      type_fichier: diagnosticState.type_fichier,
      resultat: diagnosticState.impression_resultat,
      path: diagnosticState.path,
      date: GetLocaleDate().toISOString(),
      remarque: diagnosticState.remarks,
    };
  };
  return {
    loading,
    isFormValid: isFormValid(),
    resetForm,
    initialiseState,
    getDiagnosticData,
  };
};
