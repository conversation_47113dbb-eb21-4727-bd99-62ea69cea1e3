import { useEffect } from "react";
import { useSectionBase } from "./use-section-base";
import useSpecialitiesLists from "@/presentation/hooks/use-specialities-lists";
import { ListeSpecialites } from "@/domain/models";
import { useCabinetMedicalStateContext } from "@/presentation/contexts/useContext";
import DeleteSpecialiteRepository from "@/infrastructure/repositories/professionalSpecialities/DeleteSpecialiteRepository.ts";
import DeleteSpecialiteUsecase from "@/domain/usecases/professionalSpecialities/DeleteSpecialiteUsecase.ts";

const deleteSpecialityRepository = new DeleteSpecialiteRepository();
const deleteSpecialityUsecase = new DeleteSpecialiteUsecase(
  deleteSpecialityRepository
);

/**
 * Hook personnalisé pour la section des spécialités
 */
export const useSpecialitiesSection = () => {
  const { state } = useCabinetMedicalStateContext();
  const sectionBase = useSectionBase("panel6");

  // Utiliser le hook pour récupérer les spécialités
  const { listes: rawSpecialities, getSpecialitiesList } =
    useSpecialitiesLists();

  /**
   * Supprimer une specialité
   */
  const handleDeleteSpecialty = async (specialtyId: number) => {
    try {
      await deleteSpecialityUsecase.execute(specialtyId);
      return true;
    } catch (error) {
      console.error("Erreur lors de la suppression de la spécialité:", error);
      return false;
    }
  };

  // Filtrer les spécialités pour éviter les doublons
  const specialities = rawSpecialities.reduce(
    (acc: ListeSpecialites[], speciality) => {
      if (!acc.some((s) => s.id === speciality.id)) {
        acc.push(speciality);
      }
      return acc;
    },
    []
  );

  // Charger les spécialités au chargement du composant
  useEffect(() => {
    getSpecialitiesList();
  }, [getSpecialitiesList]);

  return {
    ...sectionBase,
    specialities,
    state,
    handleDeleteSpecialty,
  };
};
