import { useEffect, useState } from "react";
import { useSectionBase } from "./use-section-base";
import { mot_cles } from "@/domain/models/MotCles";
import { useCabinetMedicalStateContext } from "@/presentation/contexts/useContext";
import { DeleteProfessionalMotClesRepository } from "@/infrastructure/repositories/motCles/DeleteProfessionalMotClesRepository";
import GetMotCles from "@/infrastructure/repositories/motCles/GetMotClesRepository";
import GetMotClesUsecase from "@/domain/usecases/motCles/GetMotClesUsecase";

// Initialisation des repositories et usecases
const deleteKeywordRepository = new DeleteProfessionalMotClesRepository();
const getKeywordsRepository = new GetMotCles();
const getKeywordsUsecase = new GetMotClesUsecase(getKeywordsRepository);

/**
 * Hook personnalisé pour la section des mots-clés
 * 
 * @description
 * Ce hook gère la logique métier pour la section des mots-clés professionnels :
 * - Récupération de la liste maître des mots-clés disponibles
 * - Suppression de mots-clés professionnels
 * - Gestion des états de chargement et d'erreur
 */
export const useKeywordsSection = () => {
  const { state } = useCabinetMedicalStateContext();
  const sectionBase = useSectionBase("panel7"); // Utiliser un ID unique pour les mots-clés

  // État local pour les mots-clés disponibles
  const [keywords, setKeywords] = useState<mot_cles[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Récupère la liste des mots-clés disponibles
   */
  const getKeywordsList = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const keywordsList = await getKeywordsUsecase.execute();
      setKeywords(keywordsList);
    } catch (error) {
      console.error("Erreur lors de la récupération des mots-clés:", error);
      setError("Erreur lors du chargement des mots-clés");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Supprime un mot-clé professionnel
   * 
   * @param keywordId - ID du mot-clé à supprimer
   * @returns true si la suppression a réussi, false sinon
   */
  const handleDeleteKeyword = async (keywordId: number): Promise<boolean> => {
    try {
      const success = await deleteKeywordRepository.execute(keywordId);
      return success;
    } catch (error) {
      console.error("Erreur lors de la suppression du mot-clé:", error);
      return false;
    }
  };

  // Filtrer les mots-clés pour éviter les doublons
  const uniqueKeywords = keywords.reduce(
    (acc: mot_cles[], keyword) => {
      if (!acc.some((k) => k.id === keyword.id)) {
        acc.push(keyword);
      }
      return acc;
    },
    []
  );

  // Charger les mots-clés au chargement du composant
  useEffect(() => {
    getKeywordsList();
  }, []);

  return {
    ...sectionBase,
    keywords: uniqueKeywords,
    isLoading,
    error,
    state,
    handleDeleteKeyword,
    getKeywordsList,
  };
};
