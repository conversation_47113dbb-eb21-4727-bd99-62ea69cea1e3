import { useCallback } from "react";
import { useCabinetMedicalStateContext } from "@/presentation/contexts/useContext";

/**
 * Hook de base pour les sections du formulaire d'inscription du cabinet médical
 * @param panelId Identifiant du panel (ex: "panel1")
 */
export const useSectionBase = (panelId: string) => {
  const { dispatch, state } = useCabinetMedicalStateContext();
  const isExpanded = state.panelExpanded === panelId;

  const handleAccordionChange = useCallback(
    (_event: React.SyntheticEvent, expanded: boolean) => {
      // Si l'accordéon est en train d'être ouvert, définir panelExpanded sur ce panelId
      if (expanded) {
        dispatch({
          type: "UPDATE_FIELD",
          payload: { field: "panelExpanded", value: panelId },
        });
      }
      // Si l'accordéon est en train d'être fermé, définir panelExpanded sur une chaîne vide
      // Cela permet de fermer l'accordéon actuel
      else if (state.panelExpanded === panelId) {
        dispatch({
          type: "UPDATE_FIELD",
          payload: { field: "panelExpanded", value: "" },
        });
      }
    },
    [dispatch, panelId, state.panelExpanded]
  );

  return {
    isExpanded,
    handleAccordionChange,
  };
};
