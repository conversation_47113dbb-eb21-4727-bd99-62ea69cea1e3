import { useEffect, useState } from "react";
import { useSectionBase } from "./use-section-base";
import useInsuranceProfessional from "@/presentation/hooks/use-insurance";
import { AssuranceProfessionnel, ListeAssurances } from "@/domain/models";

// Interface pour les modes de paiement
export interface PaymentMethod {
  id: string;
  name: string;
}

/**
 * Hook personnalisé pour la section des services
 */
export const useServicesSection = () => {
  const sectionBase = useSectionBase("panel7");

  // Utiliser le hook pour récupérer les assurances
  const { getInsurances } = useInsuranceProfessional();
  const [insurances, setInsurances] = useState<ListeAssurances[]>([]);

  // Charger les assurances au chargement du composant
  useEffect(() => {
    const loadAssurances = async () => {
      try {
        const assurancesData = await getInsurances();
        if (assurancesData) {
          setInsurances(assurancesData);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des assurances:", error);
      }
    };

    loadAssurances();
  }, []);

  return {
    ...sectionBase,
    insurances,
  };
};
