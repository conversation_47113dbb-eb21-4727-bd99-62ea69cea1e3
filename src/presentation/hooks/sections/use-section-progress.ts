import { useEffect, useMemo, useRef } from "react";
import { useCabinetMedicalStateContext } from "@/presentation/contexts/useContext";

/**
 * Hook personnalisé pour gérer la progression des sections du formulaire
 * @param panelId Identifiant du panel (ex: "panel1")
 * @param isComplete Booléen indiquant si la section est complète
 */
export const useSectionProgress = (panelId: string, isComplete: boolean) => {
  const { state, dispatch } = useCabinetMedicalStateContext();

  // Récupérer l'état de progression actuel pour cette section
  const currentProgress =
    state.sectionProgress[panelId as keyof typeof state.sectionProgress];

  // Référence pour éviter des calculs inutiles
  const prevIsCompleteRef = useRef(currentProgress);

  // Mettre à jour l'état de progression dans le contexte et calculer la progression globale
  useEffect(() => {
    // Ne mettre à jour que si l'état a changé
    if (isComplete !== currentProgress) {
      // Copier l'état actuel pour éviter des références mutables
      const currentSectionProgress = { ...state.sectionProgress };

      // Mettre à jour la progression de la section
      dispatch({
        type: "UPDATE_FIELD",
        payload: {
          field: "sectionProgress",
          value: { [panelId]: isComplete },
        },
      });

      // Calculer la nouvelle progression globale
      const totalSections = Object.keys(currentSectionProgress).length;

      // Créer une copie mise à jour de l'état de progression
      const updatedProgress = {
        ...currentSectionProgress,
        [panelId]: isComplete,
      };

      // Compter les sections complétées
      const completedSections =
        Object.values(updatedProgress).filter(Boolean).length;

      const newOverallProgress = Math.round(
        (completedSections / totalSections) * 100
      );

      // Ne mettre à jour que si la progression globale a changé
      if (newOverallProgress !== state.overallProgress) {
        dispatch({
          type: "UPDATE_FIELD",
          payload: {
            field: "overallProgress",
            value: newOverallProgress,
          },
        });
      }

      // Mettre à jour la référence
      prevIsCompleteRef.current = isComplete;
    }
  }, [isComplete, currentProgress, panelId, dispatch]);

  // Mémoriser le résultat pour éviter des re-rendus inutiles
  return useMemo(
    () => ({
      isComplete: currentProgress,
      overallProgress: state.overallProgress,
    }),
    [currentProgress, state.overallProgress]
  );
};
