import { useEffect, useState } from "react";
import { useSectionBase } from "./use-section-base";
import { useOrdreAppartenance } from "@/presentation/hooks/use-ordre-appartenance";
import { ordre_appartenance } from "@/domain/models";
import { useFormContext } from "react-hook-form";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

/**
 * Hook personnalisé pour la section d'informations professionnelles
 */
export const useProfessionalInfoSection = () => {
  const sectionBase = useSectionBase("panel1");

  // Utiliser le hook pour récupérer les ordres d'appartenance
  const { getOrdreAppartenanceList, ordreAppartenanceList, loading } =
    useOrdreAppartenance();

  const { setValue } = useFormContext<CabinetMedicalFormDTO>();

  const [ordresList, setOrdresList] = useState<ordre_appartenance[]>([]);
  const [selectedOrdres, setSelectedOrdres] = useState<ordre_appartenance[]>(
    [],
  );

  const handleOrdreAppartenanceChange = (
    selectedOrdres: ordre_appartenance[],
  ) => {
    setSelectedOrdres(selectedOrdres);
    setValue("ordre_appartenance", selectedOrdres);
  };

  // Charger les ordres d'appartenance au chargement du composant
  useEffect(() => {
    const loadOrdres = async () => {
      try {
        const ordresData = await getOrdreAppartenanceList();
        if (ordresData) {
          // Trier les ordres d'appartenance par ordre alphabétique
          const sortedOrdres = [...ordresData].sort((a, b) =>
            a.nom.localeCompare(b.nom)
          );
          setOrdresList(sortedOrdres);
        }
      } catch (error) {
        console.error(
          "Erreur lors du chargement des ordres d'appartenance:",
          error,
        );
      }
    };

    loadOrdres();
  }, []);

  return {
    ...sectionBase,
    selectedOrdres,
    ordresList,
    handleOrdreAppartenanceChange,
    ordreAppartenanceList,
    loading,
  };
};
