import { useEffect } from "react";
import { useSectionBase } from "./use-section-base";
import { LangueParleeProfessionnel } from "@/domain/models";
import { useCabinetMedicalStateContext } from "@/presentation/contexts/useContext";
import { DeleteLanguageRepository } from "@/infrastructure/repositories/professionalLanguage/DeleteLanguageRepository";
import { DeleteLanguageUsecase } from "@/domain/usecases/professionalLanguage/DeleteLanguageUsecase";
import { AddLanguageRepository } from "@/infrastructure/repositories/professionalLanguage/AddLanguageRepository";
import { AddLanguageUsecase } from "@/domain/usecases/professionalLanguage/AddLanguageUsecase";

// Initialisation des repositories et usecases
const deleteLanguageRepository = new DeleteLanguageRepository();
const deleteLanguageUsecase = new DeleteLanguageUsecase(deleteLanguageRepository);

const addLanguageRepository = new AddLanguageRepository();
const addLanguageUsecase = new AddLanguageUsecase(addLanguageRepository);

/**
 * Hook personnalisé pour la section des langues professionnelles
 * 
 * @description Ce hook encapsule la logique métier pour la gestion des langues
 * parlées par un professionnel de santé. Il suit la même architecture que
 * use-specialities-section pour maintenir la cohérence du projet.
 */
export const useLanguagesSection = () => {
  const { state } = useCabinetMedicalStateContext();
  const sectionBase = useSectionBase("panel6"); // Utilise le même panel que les spécialités

  /**
   * Ajouter une ou plusieurs langues
   * @param languageNames - Tableau des noms de langues à ajouter
   * @returns Promise<boolean> - true si succès, false sinon
   */
  const handleAddLanguages = async (languageNames: string[]): Promise<boolean> => {
    try {
      if (!state.professionalData?.id) {
        console.error("ID du professionnel non disponible");
        return false;
      }

      // Préparer les données pour l'ajout
      const languagesToAdd: Omit<LangueParleeProfessionnel, "id">[] = languageNames.map(name => ({
        nom_langue: name.trim(),
        id_professionnel: state.professionalData!.id,
      }));

      await addLanguageUsecase.execute(languagesToAdd);
      return true;
    } catch (error) {
      console.error("Erreur lors de l'ajout des langues:", error);
      return false;
    }
  };

  /**
   * Supprimer une langue
   * @param languageId - ID de la langue à supprimer
   * @returns Promise<boolean> - true si succès, false sinon
   */
  const handleDeleteLanguage = async (languageId: number): Promise<boolean> => {
    try {
      await deleteLanguageUsecase.execute(languageId);
      return true;
    } catch (error) {
      console.error("Erreur lors de la suppression de la langue:", error);
      return false;
    }
  };

  return {
    ...sectionBase,
    state,
    handleAddLanguages,
    handleDeleteLanguage,
  };
};
