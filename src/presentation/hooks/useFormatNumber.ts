import { useMemo } from 'react';
import { CURRENCY_FORMAT_OPTIONS } from '@/presentation/constants/professional.constants';

type FormatOptions = typeof CURRENCY_FORMAT_OPTIONS;

export function useFormatNumber(value: number, options: Partial<FormatOptions> = {}) {
  return useMemo(() => {
    return new Intl.NumberFormat('fr-FR', {
      ...CURRENCY_FORMAT_OPTIONS,
      ...options,
    }).format(value);
  }, [value, options]);
}
