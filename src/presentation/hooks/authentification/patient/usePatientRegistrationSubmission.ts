import { useCallback } from "react";
import { FieldErrors, UseFormGetValues, UseFormTrigger } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { PatientFormData } from "@/shared/schemas/PatientShema";
import { registerProps } from "@/domain/interfaces/usecases/Register/IRegisterUserUsecase.ts";
import { useToast } from "@/presentation/hooks/use-toast";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { SuccessMessages } from "@/shared/constants/SuccessMessages";
import { usePatientRegistrationValidation } from "./usePatientRegistrationValidation";
import { Patient } from "@/domain/models";
import { RegisterUserDTO } from "@/domain/DTOS";

interface UsePatientRegistrationSubmissionProps {
  registerUser: (
    data: registerProps,
    redirectToURL?: string
  ) => Promise<RegisterUserDTO>;
  trigger: UseFormTrigger<PatientFormData>;
  getValues: UseFormGetValues<PatientFormData>;
  errors: FieldErrors<PatientFormData>;
}

interface UsePatientRegistrationSubmissionReturn {
  onSubmit: (
    isLoginUser?: boolean,
    redirectToURL?: string
  ) => Promise<RegisterUserDTO>;
  formatFormDataForSubmission: (
    formData: PatientFormData,
    isLoginUser?: boolean
  ) => registerProps;
}

/**
 * Hook personnalisé pour gérer la soumission du formulaire d'inscription patient
 *
 * Responsabilités :
 * - Validation finale avant soumission
 * - Formatage des données pour l'API
 * - Gestion de la soumission
 * - Navigation après succès
 * - Gestion des erreurs de soumission
 */
export const usePatientRegistrationSubmission = ({
  registerUser,
  trigger,
  getValues,
  errors,
}: UsePatientRegistrationSubmissionProps): UsePatientRegistrationSubmissionReturn => {
  const toast = useToast();
  const navigate = useNavigate();

  const { validateAllSteps, validateNotAuth } =
    usePatientRegistrationValidation({
      trigger,
      errors,
    });

  /**
   * Formate les données du formulaire pour la soumission
   */
  const formatFormDataForSubmission = useCallback(
    (formData: PatientFormData, isLoginUser: boolean): registerProps => {
      return {
        email: formData.email,
        password: formData.mot_de_passe,
        additionnalInfo: {
          nom: formData.nom,
          prenom: formData.prenom,
          sexe: formData.sexe,
          date_naissance: formData.date_naissance,
          adresse: formData.adresse,
          province: formData.province,
          district: formData.district,
          commune: formData.commune,
          region: formData.region,
          telephone: formData.telephone,
          groupe_sanguin: formData.groupe_sanguin,
          nationalite: formData.nationalite || "",
          pays: formData.pays || "",
          situation_matrimonial: formData.situation_matrimonial,
          nb_enfant: formData.nb_enfant ? formData.nb_enfant : undefined,
          profession: formData.profession || "",
        },
        contact: formData.telephone.split(",").map((numero) => ({
          numero: numero.trim(),
        })),
        isLoginUser: isLoginUser,
      };
    },
    []
  );

  /**
   * Fonction principale de soumission du formulaire
   */
  const onSubmit = useCallback(
    async (
      isLoginUser?: boolean,
      redirectToURL?: string
    ): Promise<RegisterUserDTO> => {
      try {
        // Validation finale de tous les champs
        let isFormValid: boolean = false;
        if (isLoginUser) {
          isFormValid = await validateAllSteps();
        } else {
          isFormValid = await validateNotAuth();
        }
        if (!isFormValid) {
          toast.error("Veuillez corriger les erreurs dans le formulaire");
          return;
        }

        // Récupération et formatage des données
        const formData = getValues();
        const registerData = formatFormDataForSubmission(formData, isLoginUser);

        // Soumission
        const result = await registerUser(registerData, redirectToURL);
        if (result?.success) {
          if (
            isLoginUser &&
            location.pathname !== `/${PublicRoutesNavigation.CONSULTATION_PAGE}`
          ) {
            toast.success(SuccessMessages.REGISTER_SUCCESS);
            navigate(`/${PublicRoutesNavigation.LOGIN_PAGE}`);
          } else {
            return result;
          }
        }
      } catch (error) {
        console.error("Erreur lors de l'enregistrement:", error);
        toast.error(
          error?.message || "Une erreur est survenue lors de l'enregistrement"
        );
      }
    },
    [
      validateAllSteps,
      validateNotAuth,
      getValues,
      formatFormDataForSubmission,
      registerUser,
      toast,
      navigate,
    ]
  );

  return {
    onSubmit,
    formatFormDataForSubmission,
  };
};
