import { FieldErrors } from "react-hook-form";
import { PatientFormData } from "@/shared/schemas/PatientShema";
import {
  CONULTATION_STEP_FIELDS,
  StepKey,
} from "@/shared/constants/ConsultaionStepConfig";

/**
 * Utilitaires de validation spécifiques aux patients
 *
 * Ces fonctions sont utilisées exclusivement par les hooks d'authentification patient
 * et ne devraient pas être utilisées directement dans les composants.
 */

/**
 * Obtient le nombre d'erreurs pour une étape spécifique
 */
export const getStepErrorCount = (
  step: number,
  errors: FieldErrors<PatientFormData>
): number => {
  const stepFields = CONULTATION_STEP_FIELDS[step as StepKey];
  if (!stepFields) return 0;

  return stepFields.filter((field) => errors[field]).length;
};

/**
 * Vérifie si une étape spécifique est valide
 */
export const isStepValid = (
  step: number,
  errors: FieldErrors<PatientFormData>
): boolean => {
  const stepFields = CONULTATION_STEP_FIELDS[step as StepKey];
  if (!stepFields) return true;

  return !stepFields.some((field) => errors[field]);
};

/**
 * Obtient les erreurs pour une étape spécifique
 */
export const getStepErrors = (
  step: number,
  errors: FieldErrors<PatientFormData>
): string[] => {
  const stepFields = CONULTATION_STEP_FIELDS[step as StepKey];
  if (!stepFields) return [];

  return stepFields
    .filter((field) => errors[field])
    .map((field) => errors[field]?.message || `Erreur dans le champ ${field}`);
};

/**
 * Fait défiler vers le premier champ en erreur de l'étape actuelle
 */
export const scrollToFirstError = (
  step: number,
  errors: FieldErrors<PatientFormData>
): void => {
  const stepFields = CONULTATION_STEP_FIELDS[step as StepKey];
  if (!stepFields) return;

  const firstErrorField = stepFields.find((field) => errors[field]);
  if (firstErrorField) {
    const element = document.getElementById(firstErrorField);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
      // Focus sur le champ pour améliorer l'accessibilité
      element.focus();
    }
  }
};

/**
 * Vérifie si toutes les étapes précédentes sont valides
 */
export const arePreviousStepsValid = (
  currentStep: number,
  errors: FieldErrors<PatientFormData>
): boolean => {
  for (let step = 0; step < currentStep; step++) {
    if (!isStepValid(step, errors)) {
      return false;
    }
  }
  return true;
};

/**
 * Obtient un résumé de validation pour toutes les étapes
 */
export const getValidationSummary = (
  errors: FieldErrors<PatientFormData>
): { step: number; isValid: boolean; errorCount: number }[] => {
  return Object.keys(CONULTATION_STEP_FIELDS).map((stepKey) => {
    const step = parseInt(stepKey);
    return {
      step,
      isValid: isStepValid(step, errors),
      errorCount: getStepErrorCount(step, errors),
    };
  });
};
