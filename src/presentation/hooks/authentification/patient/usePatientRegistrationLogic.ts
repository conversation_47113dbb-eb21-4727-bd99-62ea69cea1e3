import { PATIENT_STEPS } from "@/shared/constants/PatientSteps";
import { useFormStepper } from "@/presentation/hooks/useFormStepper";
import useRegisterPatient from "@/presentation/hooks/authentification/use-register-patient";
import { usePatientRegistrationNavigation } from "./usePatientRegistrationNavigation";
import { usePatientRegistrationSubmission } from "./usePatientRegistrationSubmission";
import { usePatientRegistrationValidation } from "./usePatientRegistrationValidation";

/**
 * Hook principal qui orchestre toute la logique d'inscription patient
 * 
 * Ce hook combine tous les hooks spécialisés et fournit une interface
 * unifiée pour le composant RegisterPatient.
 * 
 * Responsabilités :
 * - Orchestration des différents aspects de l'inscription
 * - Interface unifiée pour le composant
 * - Gestion de l'état global du formulaire
 */
export const usePatientRegistrationLogic = () => {
  // Gestion du stepper
  const { activeStep, handleNext, handleBack } = useFormStepper(
    PATIENT_STEPS.length,
  );

  // Gestion du formulaire et de l'API
  const {
    registerUser,
    isLoading,
    control,
    formState,
    getValues,
    handleSubmit,
    register,
    setValue,
    trigger,
  } = useRegisterPatient();

  const errors = formState.errors;

  // Hooks spécialisés pour la logique métier
  const validation = usePatientRegistrationValidation({
    trigger,
    errors,
  });

  const navigation = usePatientRegistrationNavigation({
    activeStep,
    handleNext,
    handleBack,
    trigger,
    errors,
  });

  const submission = usePatientRegistrationSubmission({
    registerUser,
    trigger,
    getValues,
    errors,
  });

  // Interface unifiée pour le composant
  return {
    // État du stepper
    activeStep,
    
    // Contrôles du formulaire
    control,
    errors,
    register,
    setValue,
    handleSubmit,
    isLoading,
    
    // Actions de navigation
    handleNextStep: navigation.handleNextStep,
    handlePreviousStep: navigation.handlePreviousStep,
    canNavigateToStep: navigation.canNavigateToStep,
    
    // Actions de validation
    validateCurrentStep: validation.validateCurrentStep,
    validateAllSteps: validation.validateAllSteps,
    
    // Action de soumission
    onSubmit: submission.onSubmit,
    
    // Utilitaires
    formatFormDataForSubmission: submission.formatFormDataForSubmission,
  };
};
