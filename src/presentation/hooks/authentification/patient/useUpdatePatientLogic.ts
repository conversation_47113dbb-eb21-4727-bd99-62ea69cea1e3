import useRegisterPatient from "@/presentation/hooks/authentification/use-register-patient";
import { usePatientRegistrationValidation } from "./usePatientRegistrationValidation";
import { useUpdatePatient } from "./useUpdatePatient";
import { Patient, Utilisateur } from "@/domain/models";

/**
 * Hook principal qui orchestre toute la logique d'inscription patient
 *
 * Ce hook combine tous les hooks spécialisés et fournit une interface
 * unifiée pour le composant RegisterPatient.
 *
 * Responsabilités :
 * - Orchestration des différents aspects de l'inscription
 * - Interface unifiée pour le composant
 * - Gestion de l'état global du formulaire
 */
export const useUpdatePatientLogic = (patient: Patient) => {
  // Gestion du formulaire et de l'API
  const {
    registerUser,
    isLoading,
    control,
    formState,
    getValues,
    handleSubmit,
    register,
    setValue,
    trigger,
  } = useRegisterPatient(patient);

  const errors = formState.errors;

  // Hooks spécialisés pour la logique métier
  const validation = usePatientRegistrationValidation({
    trigger,
    errors,
  });

  const submission = useUpdatePatient({
    registerUser,
    trigger,
    getValues,
    errors,
  });

  // Interface unifiée pour le composant
  return {
    // Contrôles du formulaire
    control,
    errors,
    register,
    setValue,
    handleSubmit,
    isLoading,

    // Actions de validation
    validateCurrentStep: validation.validateCurrentStep,
    validateAllSteps: validation.validateAllSteps,

    // Action de soumission
    onSubmit: submission.onSubmit,

    // Utilitaires
    formatFormDataForSubmission: submission.formatFormDataForSubmission,
  };
};
