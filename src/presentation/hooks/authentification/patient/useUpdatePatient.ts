import { useCallback } from "react";
import { FieldErrors, UseFormGetValues, UseFormTrigger } from "react-hook-form";
import { PatientFormData } from "@/shared/schemas/PatientShema";
import { registerProps } from "@/domain/interfaces/usecases/Register/IRegisterUserUsecase.ts";
import { useToast } from "@/presentation/hooks/use-toast";
import { usePatientRegistrationValidation } from "./usePatientRegistrationValidation";
import { RegisterUserDTO } from "@/domain/DTOS";
import { useCarnetDeSante } from "../../carnetDeSante";
import { useIsCanLogin } from "./utils/getIsCanLogin";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { useAppSelector } from "../../redux";
import { useProfilePatientData } from "../../useProfilePatientData";

interface UseUpdatePatientProps {
  registerUser: (data: registerProps) => Promise<RegisterUserDTO>;
  trigger: UseFormTrigger<PatientFormData>;
  getValues: UseFormGetValues<PatientFormData>;
  errors: FieldErrors<PatientFormData>;
}

interface UseUpdatePatientReturn {
  onSubmit: (id: number, isLoginUser?: boolean) => Promise<RegisterUserDTO>;
  formatFormDataForSubmission: (formData: PatientFormData) => registerProps;
}

/**
 * Hook personnalisé pour gérer la soumission du formulaire d'inscription patient
 *
 * Responsabilités :
 * - Validation finale avant soumission
 * - Formatage des données pour l'API
 * - Gestion de la soumission
 * - Navigation après succès
 * - Gestion des erreurs de soumission
 */
export const useUpdatePatient = ({
  registerUser,
  trigger,
  getValues,
  errors,
}: UseUpdatePatientProps): UseUpdatePatientReturn => {
  const toast = useToast();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { validateAllSteps, validateNotAuth } =
    usePatientRegistrationValidation({
      trigger,
      errors,
    });
  const { handleUpdatePatient } = useCarnetDeSante();
  const isLoginUser = useIsCanLogin();

  /**
   * Formate les données du formulaire pour la soumission
   */
  const formatFormDataForSubmission = useCallback(
    (formData: PatientFormData): registerProps => {
      return {
        email: formData.email,
        password: formData.mot_de_passe,
        additionnalInfo: {
          nom: formData.nom,
          prenom: formData.prenom,
          sexe: formData.sexe,
          date_naissance: formData.date_naissance,
          adresse: formData.adresse,
          province: formData.province,
          district: formData.district,
          commune: formData.commune,
          region: formData.region,
          telephone: formData.telephone,
          groupe_sanguin: formData.groupe_sanguin,
          nationalite: formData.nationalite || "",
          pays: formData.pays || "",
          situation_matrimonial: formData.situation_matrimonial,
          nb_enfant: formData.nb_enfant ? formData.nb_enfant : undefined,
          profession: formData.profession || "",
        },
        contact: formData.telephone.split(",").map((numero) => ({
          numero: numero.trim(),
        })),
      };
    },
    []
  );

  /**
   * Fonction principale de soumission du formulaire
   */
  const onSubmit = useCallback(
    async (id: number): Promise<RegisterUserDTO> => {
      try {
        // Récupération et formatage des données
        const formData = getValues();
        if (isLoginUser && role === utilisateurs_role_enum.PROFESSIONNEL) {
          toast.error("Vous ne pouvez pas modiffier ses information");
          return;
        }

        // Validation finale de tous les champs
        let isFormValid: boolean = false;
        if (isLoginUser) {
          isFormValid = await validateAllSteps();
        } else {
          isFormValid = await validateNotAuth();
        }
        // if (!isFormValid) {
        //   toast.error("Veuillez corriger les erreurs dans le formulaire");
        //   return;
        // }

        const registerData = formatFormDataForSubmission(formData);

        // Soumission
        await handleUpdatePatient(id, registerData.additionnalInfo);
      } catch (error) {
        console.error("Erreur lors de l'enregistrement:", error);
        toast.error(
          error?.message || "Une erreur est survenue lors de l'enregistrement"
        );
      }
    },
    [
      validateAllSteps,
      validateNotAuth,
      getValues,
      formatFormDataForSubmission,
      handleUpdatePatient,
      toast,
      role,
    ]
  );

  return {
    onSubmit,
    formatFormDataForSubmission,
  };
};
