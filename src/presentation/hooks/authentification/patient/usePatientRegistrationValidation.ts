import { useCallback } from "react";
import { FieldErrors, UseFormTrigger } from "react-hook-form";
import { PatientFormData } from "@/shared/schemas/PatientShema";
import { PATIENT_STEP_FIELDS } from "@/shared/constants/PatientStepConfig";
import { getStepErrorCount, scrollToFirstError } from "./utils";
import { useToast } from "@/presentation/hooks/use-toast";

interface UsePatientRegistrationValidationProps {
  trigger: UseFormTrigger<PatientFormData>;
  errors: FieldErrors<PatientFormData>;
}

interface UsePatientRegistrationValidationReturn {
  validateCurrentStep: (step: number) => Promise<boolean>;
  validateAllSteps: () => Promise<boolean>;
  validateNotAuth: () => Promise<boolean>;
  showValidationError: (step: number) => void;
}

/**
 * Hook personnalisé pour gérer la validation du formulaire d'inscription patient
 *
 * Responsabilités :
 * - Validation des étapes individuelles
 * - Validation complète du formulaire
 * - Gestion des messages d'erreur de validation
 * - Défilement vers les erreurs
 */
export const usePatientRegistrationValidation = ({
  trigger,
  errors,
}: UsePatientRegistrationValidationProps): UsePatientRegistrationValidationReturn => {
  const toast = useToast();

  /**
   * Valide une étape spécifique du formulaire
   */
  const validateCurrentStep = useCallback(
    async (step: number): Promise<boolean> => {
      const fieldsToValidate =
        PATIENT_STEP_FIELDS[step as keyof typeof PATIENT_STEP_FIELDS];
      if (!fieldsToValidate) return true;

      // Déclencher la validation pour les champs de l'étape actuelle
      const isValid = await trigger(fieldsToValidate);
      return isValid;
    },
    [trigger]
  );

  /**
   * Valide tous les champs du formulaire
   */
  const validateAllSteps = useCallback(async (): Promise<boolean> => {
    const isFormValid = await trigger();
    return isFormValid;
  }, [trigger]);
  /**
   * Validation sans autentification
   */
  const validateNotAuth = useCallback(async (): Promise<boolean> => {
    const isFormValid = await trigger([
      "nom",
      "prenom",
      "sexe",
      "date_naissance",
      "adresse",
      "region",
      "district",
      "commune",
      "situation_matrimonial",
      "telephone",
    ]);
    return isFormValid;
  }, [trigger]);

  /**
   * Affiche un message d'erreur de validation et fait défiler vers la première erreur
   */
  const showValidationError = useCallback(
    (step: number): void => {
      toast.error(
        'Certaines informations sont manquantes ou incorrectes. Merci de les vérifier.'
      );
      // Faire défiler vers le premier champ en erreur
      scrollToFirstError(step, errors);
    },
    [errors, toast]
  );

  return {
    validateCurrentStep,
    validateAllSteps,
    validateNotAuth,
    showValidationError,
  };
};
