import { useCallback } from "react";
import { FieldErrors, UseFormTrigger } from "react-hook-form";
import { PatientFormData } from "@/shared/schemas/PatientShema";
import { usePatientRegistrationValidation } from "./usePatientRegistrationValidation";

interface UsePatientRegistrationNavigationProps {
  activeStep: number;
  handleNext: (count: number) => void;
  handleBack: (count: number) => void;
  trigger: UseFormTrigger<PatientFormData>;
  errors: FieldErrors<PatientFormData>;
}

interface UsePatientRegistrationNavigationReturn {
  handleNextStep: (count: number) => Promise<void>;
  handlePreviousStep: (count: number) => void;
  canNavigateToStep: (targetStep: number) => Promise<boolean>;
}

/**
 * Hook personnalisé pour gérer la navigation du formulaire d'inscription patient
 *
 * Responsabilités :
 * - Navigation vers l'étape suivante avec validation
 * - Navigation vers l'étape précédente
 * - Vérification des permissions de navigation
 * - Intégration avec la validation
 */
export const usePatientRegistrationNavigation = ({
  activeStep,
  handleNext,
  handleBack,
  trigger,
  errors,
}: UsePatientRegistrationNavigationProps): UsePatientRegistrationNavigationReturn => {
  const { validateCurrentStep, showValidationError } =
    usePatientRegistrationValidation({
      trigger,
      errors,
    });

  /**
   * Gère la navigation vers l'étape suivante avec validation
   */
  const handleNextStep = useCallback(
    async (count: number): Promise<void> => {
      const isCurrentStepValid = await validateCurrentStep(activeStep);

      if (isCurrentStepValid) {
        handleNext(count);
      } else {
        showValidationError(activeStep);
      }
    },
    [activeStep, validateCurrentStep, handleNext, showValidationError]
  );

  /**
   * Gère la navigation vers l'étape précédente
   */
  const handlePreviousStep = useCallback(
    (count: number): void => {
      handleBack(count);
    },
    [handleBack]
  );

  /**
   * Vérifie si on peut naviguer vers une étape spécifique
   * Utile pour la navigation directe via le stepper
   */
  const canNavigateToStep = useCallback(
    async (targetStep: number): Promise<boolean> => {
      // On peut toujours revenir en arrière
      if (targetStep <= activeStep) {
        return true;
      }

      // Pour aller en avant, il faut valider toutes les étapes intermédiaires
      for (let step = activeStep; step < targetStep; step++) {
        const isStepValid = await validateCurrentStep(step);
        if (!isStepValid) {
          showValidationError(step);
          return false;
        }
      }

      return true;
    },
    [activeStep, validateCurrentStep, showValidationError]
  );

  return {
    handleNextStep,
    handlePreviousStep,
    canNavigateToStep,
  };
};
