// Types et interfaces
import { registerProps } from "@/domain/interfaces/usecases/Register/IRegisterUserUsecase.ts";
import { RegisterUserDTO } from "@/domain/DTOS/RegisterUserDTO.ts";

// Services du domaine
import { MatriculeGenerator } from "@/domain/services/MatriculeGenerator.ts";
import { PasswordService } from "@/domain/services/PasswordService.ts";
import { UserRegistrationCleanupService } from "@/domain/services/UserRegistrationCleanupService.ts";
import { VerifyAndCheckEmail } from "@/domain/services/VerifyAndCheckEmail.ts";

// Use cases du domaine
import { CreateContactUsecase } from "@/domain/usecases/contact/CreateContactUsecase.ts";
import CreatePatientUsecase from "@/domain/usecases/patients/CreatePatientUsecase.ts";
import {
  CreateAuthentificationUserUsecase,
  CreateUserUsecase,
  DeleteUserUsecase,
  GetUserByEmailUsecase,
} from "@/domain/usecases/user/index.ts";
import { RegisterPatientUsecase } from "@/domain/usecases/user/Register/RegisterPatientUsecase.ts";

// Repositories d'infrastructure
import { CreateContactRepository } from "@/infrastructure/repositories/contact/CreateContactRepository.ts";
import CreatePatientRepository from "@/infrastructure/repositories/patients/CreatePatientRepository.ts";
import CreateAuthentificationUserRepository from "@/infrastructure/repositories/user/CreateAuthentificationUserRepository.ts";
import CreateUserRepository from "@/infrastructure/repositories/user/CreateUserRepository.ts";
import DeleteUserRepository from "@/infrastructure/repositories/user/DeleteUserRepository.ts";
import GetUserByEmailRepository from "@/infrastructure/repositories/user/GetUserByEmailRepository.ts";
import { useEffect, useState } from "react";
import { useToast } from "../use-toast.ts";
import {
  Control,
  FieldErrors,
  useForm,
  UseFormGetValues,
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormSetValue,
  UseFormTrigger,
} from "react-hook-form";
import {
  PatientFormData,
  patientSchema,
} from "@/shared/schemas/PatientShema.ts";
import { zodResolver } from "@hookform/resolvers/zod";
import { SupabaseError } from "@/infrastructure/supabase/supabaseError.ts";
import { Patient, Utilisateur } from "@/domain/models";
import { useLocationSelector } from "../use-location-selector.ts";

/**
 * Type de retour du hook useRegisterPatient
 */
interface UseRegisterPatientReturn {
  registerUser: (
    data: registerProps,
    redirectToURL?: string
  ) => Promise<RegisterUserDTO | undefined>;
  isLoading: boolean;
  control: Control<PatientFormData>;
  register: UseFormRegister<PatientFormData>;
  handleSubmit: UseFormHandleSubmit<PatientFormData>;
  formState: { errors: FieldErrors<PatientFormData> };
  setValue: UseFormSetValue<PatientFormData>;
  trigger: UseFormTrigger<PatientFormData>;
  getValues: UseFormGetValues<PatientFormData>;
}

/**
 * Hook personnalisé pour l'enregistrement d'un patient
 *
 * Ce hook encapsule toute la logique nécessaire pour enregistrer un nouveau patient
 * dans le système, incluant la validation, la création des entités liées et la gestion
 * des erreurs avec rollback automatique.
 *
 * @returns {UseRegisterPatientReturn} Objet contenant la fonction registerUser et l'état isLoading
 */
const useRegisterPatient = (patient?: Patient): UseRegisterPatientReturn => {
  // State pour la gestion de l'état de chargement
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const toast = useToast();
  // const [currentPatient, setCurrentPatient] = useState<Patient | null>(null)

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    trigger,
    getValues,
  } = useForm<PatientFormData>({
    resolver: zodResolver(patientSchema),
    mode: "onChange", // Validation en temps réel
    defaultValues: { ...patient },
  });

  /**
   * Fonction pour enregistrer un nouveau patient
   *
   * Cette fonction orchestre le processus complet d'enregistrement d'un patient :
   * - Vérification de l'unicité de l'email
   * - Création de l'utilisateur d'authentification
   * - Création de l'utilisateur métier
   * - Création du profil patient
   * - Création des informations de contact
   * - Génération du matricule patient
   * - Gestion des rollbacks en cas d'erreur
   *
   * @param {registerProps} data - Données d'enregistrement du patient
   * @returns {Promise} Promesse de l'exécution de l'enregistrement
   */

  // Repository pour la vérification d'email
  const getUserByEmailRepository = new GetUserByEmailRepository();

  // Repository pour la suppression d'utilisateur (rollback)
  const deleteUserRepository = new DeleteUserRepository();

  // Repository pour l'authentification
  const createAuthentificationUserRepository =
    new CreateAuthentificationUserRepository();

  const passwordService = new PasswordService();

  // Repository pour la création d'utilisateur
  const createUserRepository = new CreateUserRepository();

  // Repository pour la création de patient
  const createPatientRepository = new CreatePatientRepository();

  // Repository pour la création de contact
  const createContactRepository = new CreateContactRepository();

  // Use case pour vérifier l'existence d'un utilisateur par email
  const getUserByEmailUsecase = new GetUserByEmailUsecase(
    getUserByEmailRepository
  );

  // Use case pour supprimer un utilisateur (utilisé pour le rollback)
  const deleteUserUsecase = new DeleteUserUsecase(deleteUserRepository);

  // Use case pour créer un utilisateur d'authentification
  const createAuthentificationUserUsecase =
    new CreateAuthentificationUserUsecase(createAuthentificationUserRepository);

  // Use case pour créer un utilisateur métier
  const createUserUsecase = new CreateUserUsecase(
    createUserRepository,
    passwordService
  );

  // Use case pour créer un patient
  const createPatientUsecase = new CreatePatientUsecase(
    createPatientRepository
  );

  // Use case pour créer un contact
  const createContactUsecase = new CreateContactUsecase(
    createContactRepository
  );

  // Service de vérification et validation d'email
  const verifyAndCheckEmail = new VerifyAndCheckEmail(getUserByEmailUsecase);

  // Service de nettoyage en cas d'échec d'enregistrement
  const rollbackService = new UserRegistrationCleanupService(deleteUserUsecase);

  // Service de génération de matricule patient
  const matriculeGenerator = new MatriculeGenerator();

  // Use case principal qui orchestre tout le processus d'enregistrement
  const registerUserUsecase = new RegisterPatientUsecase(
    verifyAndCheckEmail,
    rollbackService,
    createAuthentificationUserUsecase,
    createUserUsecase,
    createPatientUsecase,
    createContactUsecase,
    matriculeGenerator
  );

  const registerUser = async (data: registerProps, redirectToURL?: string) => {
    try {
      // Exécution du processus d'enregistrement
      const result = await registerUserUsecase.execute(data, redirectToURL);
      return result;
    } catch (error) {
      const formatedError = error as SupabaseError;

      /* Traitement des erereurs specifique depuis supabase.
       * Dans le cas ou on a besoin de traiter manuellement une erreur,
       * il faut la mettre ici */
      switch (formatedError.code) {
        case "over_email_send_rate_limit":
          toast.error("Trop de tentatives : veuillez réessayer plus tard.");
          break;
        default:
          break;
      }

      toast.error(error.message || "Erreur lors de l'enregistrement");
      console.log("error", error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    registerUser,
    isLoading,

    control,
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    trigger,
    getValues,
  };
};

export default useRegisterPatient;
