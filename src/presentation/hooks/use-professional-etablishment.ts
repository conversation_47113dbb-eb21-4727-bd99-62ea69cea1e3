import { useSelector } from "react-redux";
import { useAppDispatch, useAppSelector } from "./redux";
import { RootState } from "@/store";
import { useEffect } from "react";
import { toast } from "sonner";
import { SupabaseError } from "@/infrastructure/supabase/supabaseError";
import { getProfessionalEtablishments } from "@/application/slices/professionnal/professionalEtablishmentSlice";

const useProfessionalEtablisement = () => {
  const dispatch = useAppDispatch();
  const { listes, loading, error } = useSelector(
    (state: RootState) => state.professionalEtablishment,
  );

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  const getProfessionalEtablisementList = async () => {
    try {
      if (listes.length == 0) await dispatch(getProfessionalEtablishments());
    } catch (error) {
      toast.error(
        error instanceof SupabaseError
          ? error.message
          : "Erreur lors de la recuperation des lites de specialitees",
      );
    }
  };

  return { listes, loading, getProfessionalEtablisementList };
};

export default useProfessionalEtablisement;
