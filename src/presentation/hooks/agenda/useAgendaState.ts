import { useState, useEffect } from "react";
import { View } from "react-big-calendar";
import { Event } from "@/shared/types/SettingsType";
import { controle_parametre, Evenement, RendezVous } from "@/domain/models";
import { DEFAULT_SETTINGS_LOCAL } from "@/shared/constants/DefaultSettingsLocal";
import LoadEvents from "@/domain/services/LoadEventService";
import { AvailabilitySettingsDTO } from "@/domain/DTOS";
import { useAgendaStateContext } from "@/presentation/contexts/useContext/useAgendaStateContext";
import { useEventState } from "@/presentation/hooks/agenda/events";
import { useAvailabilitySettings } from "./settings";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale/use-consultation-state";

const loadEvents = new LoadEvents();

export const useAgendaState = (
  settings?: AvailabilitySettingsDTO,
  evenements?: Evenement[],
  appointments?: RendezVous[] | AppointmentProfessionalDTO[]
) => {
  const { state, dispatch } = useAgendaStateContext();
  const { saveSettings } = useAvailabilitySettings();
  const { handleCreateAppointmentByProfessional } = useConsultationState();
  const { resetState } = useEventState();

  const [settingsLocal, setSettingsLocal] = useState<controle_parametre>(() => {
    const savedSettings = localStorage.getItem("settingLocal");
    if (savedSettings) {
      return JSON.parse(savedSettings);
    }
    return DEFAULT_SETTINGS_LOCAL;
  });

  const updateField = (
    field: string,
    value: number | string | boolean | null | Event | Event[] | HTMLElement
  ) => {
    dispatch({
      type: "UPDATE_FIELD",
      payload: { field, value },
    });
  };

  useEffect(() => {
    if (settings || evenements || appointments) {
      const isDisponibilites = state.isDisponibilites;
      const isEvenement = state.isEvenement;
      const isAppointments = state.isAppointments;
      const newEvents = loadEvents.execute(
        {
          disponibilites: settings,
          evenement: evenements,
          appointments: appointments,
        },
        { isDisponibilites, isEvenement, isAppointments }
      );
      updateField("events", newEvents);
    }
  }, [
    settings,
    evenements,
    appointments,
    state.isDisponibilites,
    state.isEvenement,
    state.isAppointments,
  ]);

  const handleIsAddEventModalOpen = (value: boolean) => {
    updateField("isAddEventModalOpen", value);
  };

  const handleIsSettingsModalOpen = (value: boolean) => {
    updateField("isSettingsModalOpen", value);
  };

  const handleIsAppointmentModalOpen = (value: boolean) => {
    updateField("isAppointmentModalOpen", value);
  };

  const handleIsAppointmentsChange = (value: boolean) => {
    updateField("isAppointments", value);
  };

  const handleIsDisponibilitesChange = (value: boolean) => {
    updateField("isDisponibilites", value);
  };

  const handleIsEvenementChange = (value: boolean) => {
    updateField("isEvenement", value);
  };

  const handleSelectSlot = (event: Event) => {
    updateField("isAddEventModalOpen", true);
    updateField("timeEvent", event);
  };

  const handleSelectEvent = async (
    event: Event,
    e: React.MouseEvent<HTMLElement>
  ) => {
    updateField("selectedEvent", event);
    updateField("anchorEl", e.currentTarget);
  };

  const handleSaveEvent = async () => {
    updateField("isAddEventModalOpen", false);
  };

  const handleCloseAddEventModal = () => {
    updateField("isAddEventModalOpen", false);
    updateField("idCurrentEvent", 0);
    updateField("timeEvent", null);
    resetState();
  };

  const handleCloseAppointment = (value: boolean) => {
    updateField("isAppointmentModalOpen", value);
    updateField("idCurrentEvent", 0);
    updateField("timeEvent", null);
    resetState();
  };

  const handleCloseDeleteEvenementModal = () => {
    updateField("isDeleteEventModalOpen", false);
    updateField("idCurrentEvent", 0);
    resetState();
  };

  const handleCloseDeleteSettingsModal = () => {
    updateField("isDeleteSettingsModalOpen", false);
    updateField("idCurrentEvent", 0);
  };

  const handleSaveSettings = async (newSettings: AvailabilitySettingsDTO) => {
    await saveSettings(newSettings);
    setSettingsLocal(() => {
      const savedSettings = localStorage.getItem("settingLocal");
      if (savedSettings) {
        return JSON.parse(savedSettings);
      }
      return DEFAULT_SETTINGS_LOCAL;
    });
    updateField("isSettingsModalOpen", false);
    updateField("isSaveSettings", !state.isSaveSettings);
  };

  const handleSaveAppointment = async (data: Omit<RendezVous, "id">) => {
    await handleCreateAppointmentByProfessional(data);
    updateField("isAppointmentModalOpen", false);
    updateField("isSaveSettings", !state.isSaveSettings);
  };

  const handleViewChange = (newView: View) => {
    updateField("currentView", newView);
  };

  const handleCloseSettings = (value: boolean) => {
    updateField("isSettingsModalOpen", value);
  };

  const handleClosePopover = () => {
    updateField("anchorEl", null);
    updateField("selectedEvent", null);
  };

  const handleEditEvent = () => {
    if (state.selectedEvent?.type === "evenement") {
      updateField("isAddEventModalOpen", true);
    } else if (
      state.selectedEvent?.type === "disponibilite" ||
      state.selectedEvent?.type === "exception"
    ) {
      updateField("isSettingsModalOpen", true);
    }
    updateField("timeEvent", state.selectedEvent);
    updateField("idCurrentEvent", state.selectedEvent?.id || 0);
    handleClosePopover();
  };

  const handleDeleteEvent = () => {
    if (state.selectedEvent?.type === "evenement") {
      updateField("isDeleteEventModalOpen", true);
    } else if (
      state.selectedEvent?.type === "disponibilite" ||
      state.selectedEvent?.type === "exception"
    ) {
      updateField("isDeleteSettingsModalOpen", true);
    }
    updateField("timeEvent", state.selectedEvent);
    updateField("idCurrentEvent", state.selectedEvent?.id || 0);
    handleClosePopover();
  };

  return {
    ...state,
    settingsLocal,
    handleIsEvenementChange,
    handleIsDisponibilitesChange,
    handleIsAppointmentsChange,
    setSettingsLocal,
    handleDeleteEvent,
    handleEditEvent,
    handleCloseSettings,
    handleViewChange,
    handleSaveSettings,
    handleCloseDeleteSettingsModal,
    handleCloseDeleteEvenementModal,
    handleCloseAddEventModal,
    handleCloseAppointment,
    handleSaveEvent,
    handleSelectSlot,
    handleSelectEvent,
    handleIsSettingsModalOpen,
    handleIsAddEventModalOpen,
    handleIsAppointmentModalOpen,
    handleClosePopover,
    handleSaveAppointment,
  };
};
