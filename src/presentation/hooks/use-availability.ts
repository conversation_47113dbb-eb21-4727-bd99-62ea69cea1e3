import DateClass from "@/shared/utils/DateClass";
import { useCallback, useEffect, useState } from "react";
import { ProfessionalCardDTO, TimeSlotProffessionalCard } from "@/domain/DTOS";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  resetTimeSlot,
  setProfessionnal,
  setTimeSlot,
} from "@/application/slices/professionnal/appointmentSlice";
import { useNavigate } from "react-router-dom";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { AVAILABILITY_CONSTANTS } from "@/shared/constants/availabilityConstants";
import { DateUtils } from "@/shared/utils/date.utils";
import { ScheduleDay } from "@/domain/types/schedule";
import { DataStepConsultation } from "@/presentation/hooks/consultationStep/DataStepConsultation";
import { saveToLocalStorage } from "@/presentation/hooks/consultationStep/saveToLocalStorage";
import { CONSULTATION_STEP_DATA } from "./consultationStep/constant";

/**
 * Type pour les classes de grille possibles
 */
type GridType = "grid-cols-2" | "grid-cols-3" | "grid-cols-4";

interface UseAvailabilityReturn {
  schedule: ScheduleDay[];
  grid: GridType;
  voirPlus: boolean;
  handlePrevious: () => void;
  handleNext: () => void;
  isDisabled: () => boolean;
  toggleVoirPlus: (horairesRef: React.MutableRefObject<HTMLDivElement>) => void;
  updateDateCount: (width: number) => void;
  events: TimeSlotProffessionalCard[];
  handleSelectTimeSlot: (
    professionalID: number,
    professional: ProfessionalCardDTO,
    timeSlot: TimeSlotProffessionalCard
  ) => void;
  startDate: DateClass;
  setStartDate: (date: DateClass) => void;
  extremum: {
    min: Date | null;
    max: Date | null;
  };
  getProfessionalAvailabilitiesById: (id: number) => ProfessionalCardDTO | null;
  handleResetTimeSlot: () => void;
}

export const useAvailability = (
  events: TimeSlotProffessionalCard[] = []
): UseAvailabilityReturn => {
  const today = new Date();
  const [startDate, setStartDate] = useState(
    new DateClass(today.getFullYear(), today.getMonth() + 1, today.getDate())
  );

  const [schedule, setSchedule] = useState<ScheduleDay[]>([]);

  const [grid, setGrid] = useState<GridType>("grid-cols-4");

  const [voirPlus, setVoirPlus] = useState(false);
  const [dateCount, setDateCount] = useState(5);

  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();

  const { professionals } = useSelector(
    (state: RootState) => state.searchProfessionals
  );

  // Nombre de date affichee
  const updateDateCount = useCallback((width: number) => {
    const { GRID_BREAKPOINTS, DATE_COUNT } = AVAILABILITY_CONSTANTS;

    if (width > GRID_BREAKPOINTS.LARGE) {
      setDateCount(DATE_COUNT.LARGE);
      setGrid("grid-cols-5" as GridType);
    } else if (width > GRID_BREAKPOINTS.MEDIUM) {
      setDateCount(DATE_COUNT.MEDIUM);
      setGrid("grid-cols-4" as GridType);
    } else if (width > GRID_BREAKPOINTS.SMALL) {
      setDateCount(DATE_COUNT.SMALL);
      setGrid("grid-cols-3" as GridType);
    } else {
      setDateCount(DATE_COUNT.XSMALL);
      setGrid("grid-cols-2" as GridType);
    }
  }, []);

  useEffect(() => {
    const generateSchedule = (
      date: DateClass,
      count: number
    ): ScheduleDay[] => {
      const schedules: ScheduleDay[] = [];
      let currentDate = date;

      for (let i = 0; i < count; i++) {
        const currentDateStr = `${currentDate.getAnnee()}-${String(
          currentDate.getMois()
        ).padStart(2, "0")}-${String(currentDate.getJour()).padStart(2, "0")}`;

        // Recuperation et arrangement des crenau horaires correspondant a la date d'aujourd'hui
        const dayEvents = events.filter(
          (event) => event.date === currentDateStr
        );
        dayEvents.sort((a, b) => a.start.localeCompare(b.start));

        schedules.push({
          date: currentDate,
          horaires:
            dayEvents.length > 0
              ? dayEvents.map((event) => `${event.start} - ${event.end}`)
              : [],
        });

        currentDate = currentDate.lendemain();
      }

      return schedules;
    };

    const newSchedule = generateSchedule(startDate, dateCount);

    setSchedule(newSchedule);
  }, [startDate, dateCount]);

  const handlePrevious = () => {
    setStartDate((prev) => {
      for (let i = 0; i < dateCount; i++) {
        prev = prev.veille();
      }
      return prev;
    });
  };

  const handleNext = () => {
    setStartDate((prev) => {
      for (let i = 0; i < dateCount; i++) {
        prev = prev.lendemain();
      }
      return prev;
    });
  };

  const isDisabled = () => {
    return (
      startDate.difference(
        new DateClass(
          today.getFullYear(),
          today.getMonth() + 1,
          today.getDate()
        )
      ) <= 0
    );
  };

  // const toggleVoirPlus = () => setVoirPlus((prev) => !prev);

  const toggleVoirPlus = (
    horairesRef: React.MutableRefObject<HTMLDivElement>
  ) => {
    setVoirPlus((prev) => !prev);
    if (voirPlus && horairesRef.current) {
      setTimeout(() => {
        horairesRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "end",
        });
      }, 100); // Donne le temps au DOM de "déplier"
    }
  };

  const handleSelectTimeSlot = (
    professionalID: number,
    selectedProfessional: ProfessionalCardDTO,
    selectedTimeSlot: TimeSlotProffessionalCard
  ) => {
    dispatch(setTimeSlot(selectedTimeSlot));
    const data: DataStepConsultation = {
      ...selectedTimeSlot,
      id_professionnel: professionalID,
    };
    saveToLocalStorage(CONSULTATION_STEP_DATA, data);
    if (
      !(
        location.pathname.includes("agenda") ||
        location.pathname.includes("mes-patients")
      )
    ) {
      dispatch(setProfessionnal(selectedProfessional));
      navigate(`/${PublicRoutesNavigation.CONSULTATION_PAGE}`);
    }
  };

  const handleResetTimeSlot = () => {
    dispatch(resetTimeSlot());
  };

  const [extremum, setExtremum] = useState<{
    min: Date | null;
    max: Date | null;
  }>();

  useEffect(() => {
    const extremumDates = DateUtils.getMinMaxDates(events);
    setExtremum(extremumDates);
  }, [events]);

  const getProfessionalAvailabilitiesById = (id: number) => {
    const out = professionals.find((professional) => professional.id === id);
    return out ? out : null;
  };

  return {
    schedule,
    grid,
    voirPlus,
    handlePrevious,
    handleNext,
    isDisabled,
    toggleVoirPlus,
    updateDateCount,
    handleSelectTimeSlot,
    startDate,
    setStartDate,
    extremum,
    getProfessionalAvailabilitiesById,
    handleResetTimeSlot,
    events,
  };
};
