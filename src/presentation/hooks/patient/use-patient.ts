import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "../redux";
import {
  getPatientById as fetchPatientById,
  updatePatient,
  deletePatient,
} from "@/application/slices/patient/patientSlice";
import { Patient } from "@/domain/models";

export const usePatient = () => {
  const dispatch = useAppDispatch();
  const { patient, loading, error } = useAppSelector((state) => state.patient);

  const getPatientById = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.",
        );
      }

      return await dispatch(fetchPatientById({ id: id })).unwrap();
    },
    [dispatch],
  );

  const handleUpdatePatient = useCallback(
    async (id: number, data: Partial<Patient>) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.",
        );
      }
      await dispatch(updatePatient({ id, data })).unwrap();
    },
    [dispatch],
  );

  const handleDeletePatient = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.",
        );
      }
      await dispatch(deletePatient(id)).unwrap();
    },
    [dispatch],
  );

  return {
    loading,
    error,
    patient,
    getPatientById,
    handleUpdatePatient,
    handleDeletePatient,
  };
};
