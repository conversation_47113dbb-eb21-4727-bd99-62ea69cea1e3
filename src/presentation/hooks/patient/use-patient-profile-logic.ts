import { useEffect, useState, useCallback } from "react";
import { usePatient } from "@/presentation/hooks/patient/use-patient";
import { useAppSelector, useAppDispatch } from "@/presentation/hooks/redux";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import { useRestoreAuth } from "@/presentation/hooks/use-restore-auth";
import { Patient } from "@/domain/models";
import { setUserData } from "@/application/slices/auth/authSlice";

/**
 * Hook personnalisé pour gérer toute la logique du profil patient
 * Centralise la gestion des états, des données et des interactions
 */
export const usePatientProfileLogic = () => {
    // Hooks d'authentification et de restauration
    const { isRestoreDone } = useRestoreAuth();
    const dispatch = useAppDispatch();

    // États Redux pour les données utilisateur
    const patientData = useAppSelector((state) => state.authentification.userData);
    const patientId = patientData?.id;
    const authLoading = useAppSelector((state) => state.authentification.loading);

    // Hook pour les données patient
    const { loading, patient, getPatientById } = usePatient();

    // Hook pour la gestion de la localisation
    const {
        getCommuneById,
        getRegionById,
        getDistrictById,
        getProvinceById,
        selectedProvince,
        selectedDistrict,
        selectedCommune,
        selectedRegion
    } = useLocationSelector();

    // États locaux pour les noms de localisation
    const [communeName, setCommuneName] = useState<string>("");
    const [regionName, setRegionName] = useState<string>("");
    const [districtName, setDistrictName] = useState<string>("");
    const [provinceName, setProvinceName] = useState<string>("");

    // États pour éviter les appels répétés
    const [loadedIds, setLoadedIds] = useState<{
        commune?: string;
        district?: string;
        region?: string;
        province?: string;
    }>({});

    // État pour le modal de modification
    const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);

    // Patient actuel (combinaison des données du hook et du store)
    const currentPatient = patient || (patientData && 'unique_id' in patientData ? patientData as Patient : null);

    // Fonction pour calculer l'âge
    const calculateAge = useCallback((birthDate: Date) => {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        return age;
    }, []);

    // Fonctions pour gérer le modal
    const handleOpenEditModal = useCallback(() => {
        setIsEditModalOpen(true);
    }, []);

    const handleCloseEditModal = useCallback(() => {
        setIsEditModalOpen(false);
    }, []);

    // Fonction pour gérer le succès de la modification du profil
    const handleProfileUpdateSuccess = useCallback(async () => {

        try {
            if (patientId) {

                const updatedPatient = await getPatientById(patientId);

                // Mettre à jour les données dans le store d'authentification
                if (updatedPatient) {

                    dispatch(setUserData(updatedPatient));
                }


            } else {

            }
        } catch (error) {
            console.error("Erreur lors du rechargement:", error);
        }
    }, [patientId, getPatientById, dispatch]);

    // Effet pour charger les données du patient
    useEffect(() => {
        if (patientId) {
            getPatientById(patientId);
        }
    }, [patientId, getPatientById]);

    // Effets pour récupérer les données de localisation
    useEffect(() => {
        if (currentPatient?.commune &&
            !selectedCommune &&
            loadedIds.commune !== currentPatient.commune) {
            const fetchCommuneName = async () => {
                try {
                    await getCommuneById(parseInt(currentPatient.commune));
                    setLoadedIds(prev => ({ ...prev, commune: currentPatient.commune }));
                } catch (error) {

                    setCommuneName("Commune non disponible");
                }
            };
            fetchCommuneName();
        }
    }, [currentPatient?.commune, selectedCommune, loadedIds.commune]);

    useEffect(() => {
        if (currentPatient?.district &&
            !selectedDistrict &&
            loadedIds.district !== currentPatient.district) {
            const fetchDistrictName = async () => {
                try {
                    await getDistrictById(parseInt(currentPatient.district));
                    setLoadedIds(prev => ({ ...prev, district: currentPatient.district }));
                } catch (error) {

                    setDistrictName("District non disponible");
                }
            };
            fetchDistrictName();
        }
    }, [currentPatient?.district, selectedDistrict, loadedIds.district]);

    useEffect(() => {
        if (currentPatient?.region &&
            !selectedRegion &&
            loadedIds.region !== currentPatient.region) {
            const fetchRegionName = async () => {
                try {
                    await getRegionById(parseInt(currentPatient.region));
                    setLoadedIds(prev => ({ ...prev, region: currentPatient.region }));
                } catch (error) {

                    setRegionName("Région non disponible");
                }
            };
            fetchRegionName();
        }
    }, [currentPatient?.region, selectedRegion, loadedIds.region]);

    useEffect(() => {
        if (currentPatient?.province &&
            !selectedProvince &&
            loadedIds.province !== currentPatient.province) {
            const fetchProvinceName = async () => {
                try {
                    await getProvinceById(parseInt(currentPatient.province));
                    setLoadedIds(prev => ({ ...prev, province: currentPatient.province }));
                } catch (error) {

                    setProvinceName("Province non disponible");
                }
            };
            fetchProvinceName();
        }
    }, [currentPatient?.province, selectedProvince, loadedIds.province]);

    // Effets pour mettre à jour les noms quand les données sont récupérées
    useEffect(() => {
        if (selectedCommune) {
            setCommuneName(selectedCommune.nom);
        }
    }, [selectedCommune]);

    useEffect(() => {
        if (selectedDistrict) {
            setDistrictName(selectedDistrict.libelle);
        }
    }, [selectedDistrict]);

    useEffect(() => {
        if (selectedRegion) {
            setRegionName(selectedRegion.nom);
        }
    }, [selectedRegion]);
    useEffect(() => {
        if (selectedProvince) {
            setProvinceName(selectedProvince.nom);
        }
    }, [selectedProvince]);

    // Fonctions pour vérifier les états de chargement et d'erreur
    const isLoading = !isRestoreDone || authLoading || loading;
    const isUnauthenticated = isRestoreDone && !patientId;
    const isPatientNotFound = !currentPatient && !loading;

    return {
        // États
        currentPatient,
        patientId,
        isRestoreDone,
        authLoading,
        loading,
        communeName,
        regionName,
        districtName,
        provinceName,
        isEditModalOpen,

        // États de vérification
        isLoading,
        isUnauthenticated,
        isPatientNotFound,

        // Fonctions
        calculateAge,
        handleOpenEditModal,
        handleCloseEditModal,
        handleProfileUpdateSuccess,
    };
};
