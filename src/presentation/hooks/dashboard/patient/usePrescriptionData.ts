import { useMemo } from 'react';

/**
 * Interface moderne pour les données de prescription
 */
export interface PrescriptionData {
  /** Identifiant unique de la prescription */
  id: string;
  /** Nom du médicament */
  medication: string;
  /** Dosage prescrit */
  dosage: string;
  /** Fréquence de prise */
  frequency: string;
  /** Date de début du traitement */
  startDate: Date;
  /** Date de fin du traitement */
  endDate: Date;
  /** Nom du médecin prescripteur */
  doctorName: string;
  /** Statut actif de la prescription */
  isActive: boolean;
  /** Nombre de renouvellements restants */
  refillsLeft: number;
  /** Instructions spéciales */
  instructions?: string;
  /** Effets secondaires connus */
  sideEffects?: string[];
}

/**
 * Statistiques des prescriptions
 */
export interface PrescriptionStats {
  /** Nombre total de prescriptions */
  total: number;
  /** Prescriptions actives */
  active: number;
  /** Prescriptions terminées */
  completed: number;
  /** Prescriptions expirées */
  expired: number;
  /** Prescriptions nécessitant un renouvellement */
  needingRefill: number;
}

/**
 * Interface de retour du hook usePrescriptionData
 */
export interface PrescriptionDataResult {
  /** Liste des prescriptions */
  prescriptions: PrescriptionData[];
  /** Statistiques des prescriptions */
  stats: PrescriptionStats;
  /** Prescriptions actives uniquement */
  activePrescriptions: PrescriptionData[];
  /** Prescriptions nécessitant un renouvellement */
  prescriptionsNeedingRefill: PrescriptionData[];
  /** État de chargement */
  loading: boolean;
  /** Erreur éventuelle */
  error: string | null;
}

/**
 * Hook moderne pour la gestion des données de prescriptions
 * 
 * Fournit une interface propre et typée pour accéder aux données de prescription
 * du patient avec des calculs automatiques des statistiques et des filtres.
 * 
 * @hook
 * @example
 * ```tsx
 * const { 
 *   prescriptions, 
 *   stats, 
 *   activePrescriptions,
 *   prescriptionsNeedingRefill,
 *   loading 
 * } = usePrescriptionData(patientId);
 * 
 * return (
 *   <div>
 *     <h3>Prescriptions actives: {stats.active}</h3>
 *     {activePrescriptions.map(prescription => (
 *       <PrescriptionCard key={prescription.id} prescription={prescription} />
 *     ))}
 *   </div>
 * );
 * ```
 * 
 * @param patientId - ID du patient pour lequel récupérer les prescriptions
 * @returns Objet contenant les prescriptions et statistiques associées
 * 
 * @architecture
 * - Interface TypeScript stricte pour la sécurité des types
 * - Calculs automatiques des statistiques et filtres
 * - Gestion d'état moderne avec loading et error states
 * - Mémorisation pour optimiser les performances
 * 
 * @performance
 * - Calculs mémorisés pour éviter les recalculs inutiles
 * - Filtres optimisés avec useMemo
 * - Interface légère sans logique métier complexe
 */
export const usePrescriptionData = (patientId?: number): PrescriptionDataResult => {
  // TODO: Remplacer par un appel API réel
  // Pour l'instant, retourner des données vides en attendant l'implémentation de l'API
  const prescriptions: PrescriptionData[] = useMemo(() => {
    if (!patientId) return [];
    
    // Données temporaires pour le développement
    // À remplacer par un appel API réel
    return [];
  }, [patientId]);

  // Calcul des statistiques mémorisées
  const stats: PrescriptionStats = useMemo(() => {
    const now = new Date();
    
    const active = prescriptions.filter(p => p.isActive && new Date(p.endDate) > now).length;
    const completed = prescriptions.filter(p => !p.isActive).length;
    const expired = prescriptions.filter(p => p.isActive && new Date(p.endDate) <= now).length;
    const needingRefill = prescriptions.filter(p => p.isActive && p.refillsLeft <= 1).length;

    return {
      total: prescriptions.length,
      active,
      completed,
      expired,
      needingRefill,
    };
  }, [prescriptions]);

  // Filtres mémorisés
  const activePrescriptions = useMemo(() => {
    const now = new Date();
    return prescriptions.filter(p => p.isActive && new Date(p.endDate) > now);
  }, [prescriptions]);

  const prescriptionsNeedingRefill = useMemo(() => {
    return prescriptions.filter(p => p.isActive && p.refillsLeft <= 1);
  }, [prescriptions]);

  return {
    prescriptions,
    stats,
    activePrescriptions,
    prescriptionsNeedingRefill,
    loading: false, // TODO: Implémenter le vrai état de chargement
    error: null,    // TODO: Implémenter la gestion d'erreurs
  };
};

/**
 * Hook pour obtenir une prescription spécifique par ID
 * 
 * @hook
 * @param prescriptionId - ID de la prescription à récupérer
 * @returns Prescription trouvée ou null
 */
export const usePrescription = (prescriptionId: string): PrescriptionData | null => {
  // TODO: Implémenter la récupération d'une prescription spécifique
  return null;
};

/**
 * Hook pour les prescriptions récentes
 * 
 * @hook
 * @param patientId - ID du patient
 * @param days - Nombre de jours pour définir "récent" (défaut: 30)
 * @returns Prescriptions récentes
 */
export const useRecentPrescriptions = (
  patientId?: number, 
  days: number = 30
): PrescriptionData[] => {
  const { prescriptions } = usePrescriptionData(patientId);
  
  return useMemo(() => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return prescriptions.filter(p => new Date(p.startDate) >= cutoffDate);
  }, [prescriptions, days]);
};
