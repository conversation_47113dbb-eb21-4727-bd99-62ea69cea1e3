/**
 * Patient Dashboard Hooks - Architecture Moderne
 *
 * Exports centralisés pour tous les hooks liés au dashboard patient.
 * <PERSON><PERSON> hooks fournissent des fonctionnalités spécialisées pour différents
 * aspects de l'expérience du tableau de bord patient.
 *
 * @architecture_moderne
 * - Hooks spécialisés avec responsabilités claires
 * - Gestion d'erreurs intégrée et moderne
 * - Types TypeScript stricts pour la sécurité
 * - Performance optimisée avec mémorisation
 */

// Hooks de gestion des données
export { useDashboardPatient } from './use-dashboard-patient';
export {
  usePrescriptionData,
  usePrescription,
  useRecentPrescriptions
} from './usePrescriptionData';

// Hooks de visualisation et graphiques
export {
  useChartConfiguration,
  useSpecificChartConfig,
  useResponsiveChartConfig
} from './useChartConfiguration';

// Hooks de métriques et santé
export {
  useHealthScore,
  useHealthScoreTrend,
  useHealthScoreFromPatient
} from './useHealthScore';

// Hooks d'actions utilisateur
export {
  useAppointmentActions,
  useAppointmentValidation
} from './useAppointmentActions';

export {
  usePrescriptionActions,
  usePrescriptionValidation
} from './usePrescriptionActions';

// Hooks de gestion d'erreurs modernes
export {
  useErrorHandling,
  useAsyncOperation,
  useRetryableOperation
} from './useErrorHandling';

// Types modernes pour TypeScript
export type {
  PrescriptionData,
  PrescriptionStats,
  PrescriptionDataResult
} from './usePrescriptionData';
