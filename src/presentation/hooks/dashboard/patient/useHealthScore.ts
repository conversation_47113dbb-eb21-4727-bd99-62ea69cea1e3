import { useMemo } from "react";
import { signe_vitaux } from "@/domain/models";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { PatientDTO } from "@/domain/DTOS";
import {
  HEALTH_SCORE_THRESHOLDS,
  HEALTH_SCORE_COLORS,
} from "@/presentation/constants/dashboard.constants";
import { calculateHealthScoreFromPatient } from "@/presentation/utils/healthScoreCalculator";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

/**
 * Interface du résultat du score de santé
 */
interface HealthScoreResult {
  /** Score numérique de 0 à 100 */
  score: number;
  /** Statut textuel basé sur les seuils de score */
  status: "Excellente" | "Bonne" | "Moyenne" | "À surveiller" | "Préoccupante";
  /** Couleur associée au statut de santé */
  color: string;
  /** Informations de tendance pour comparaison */
  trend?: {
    value: number;
    isPositive: boolean;
    label: string;
  };
}

/**
 * Hook personnalisé pour calculer le score de santé du patient
 *
 * Fournit un calcul léger du score de santé basé sur les signes vitaux
 * et l'historique des rendez-vous. Optimisé pour les performances avec des
 * calculs mémorisés et une surcharge minimale de validation en temps réel.
 *
 * @hook
 * @example
 * ```tsx
 * const healthScore = useHealthScore(patient?.signe_vitaux, appointments);
 *
 * return (
 *   <div>
 *     <span>Score: {healthScore.score}</span>
 *     <span style={{ color: healthScore.color }}>
 *       {healthScore.status}
 *     </span>
 *   </div>
 * );
 * ```
 *
 * @param vitalSigns - Tableau des données de signes vitaux du patient
 * @param appointments - Tableau des rendez-vous du patient
 * @returns Objet score de santé avec score, statut et couleur
 *
 * @performance
 * - Calculs mémorisés pour éviter les recalculs inutiles
 * - Validation légère sans traitement lourd en temps réel
 * - Calcul de score efficace avec un traitement minimal des données
 *
 * @architecture
 * - Sépare la logique de calcul de santé de la présentation
 * - Utilise des seuils centralisés et des configurations de couleurs
 * - Fournit un scoring cohérent dans toute l'application
 */
export const useHealthScore = (
  vitalSigns?: signe_vitaux[],
  appointments?: AppointmentPatientDTO[],
): HealthScoreResult => {
  return useMemo(() => {
    let score = 70; // Score neutre par défaut

    // Calcul de la contribution des signes vitaux (40% du score total)
    if (vitalSigns && vitalSigns.length > 0) {
      const latestVitals = vitalSigns[0]; // Signes vitaux les plus récents
      let vitalScore = 100;

      // Évaluation basique des signes vitaux (validation légère)
      if (latestVitals.tension_arterielle) {
        const [systolic, diastolic] = latestVitals.tension_arterielle
          .split("/")
          .map(Number);
        if (
          systolic > 140 ||
          systolic < 90 ||
          diastolic > 90 ||
          diastolic < 60
        ) {
          vitalScore -= 20;
        }
      }

      if (latestVitals.poid && latestVitals.taille) {
        const bmi = latestVitals.poid / Math.pow(latestVitals.taille / 100, 2);
        if (bmi > 30 || bmi < 18.5) {
          vitalScore -= 15;
        }
      }

      if (latestVitals.temperature) {
        if (latestVitals.temperature > 38 || latestVitals.temperature < 36) {
          vitalScore -= 10;
        }
      }

      score = score * 0.6 + vitalScore * 0.4;
    }

    // Calcul de la contribution de l'adhésion aux rendez-vous (30% du score total)
    if (appointments && appointments.length > 0) {
      const recentAppointments = appointments.slice(0, 10); // 10 derniers rendez-vous
      const completedCount = recentAppointments.filter(
        (apt) => apt.statut === rendez_vous_statut_enum.TERMINER,
      ).length;

      const adherenceRate = completedCount / recentAppointments.length;
      const adherenceScore = adherenceRate * 100;

      score = score * 0.7 + adherenceScore * 0.3;
    }

    // S'assurer que le score est dans les limites
    const finalScore = Math.max(0, Math.min(100, Math.round(score)));

    // Déterminer le statut basé sur les seuils
    let status: HealthScoreResult["status"] = "Moyenne";

    if (finalScore >= HEALTH_SCORE_THRESHOLDS.excellent.min) {
      status = HEALTH_SCORE_THRESHOLDS.excellent.status;
    } else if (finalScore >= HEALTH_SCORE_THRESHOLDS.good.min) {
      status = HEALTH_SCORE_THRESHOLDS.good.status;
    } else if (finalScore >= HEALTH_SCORE_THRESHOLDS.average.min) {
      status = HEALTH_SCORE_THRESHOLDS.average.status;
    } else if (finalScore >= HEALTH_SCORE_THRESHOLDS.concerning.min) {
      status = HEALTH_SCORE_THRESHOLDS.concerning.status;
    } else {
      status = HEALTH_SCORE_THRESHOLDS.poor.status;
    }

    // Obtenir la couleur pour le statut
    const color = HEALTH_SCORE_COLORS[status];

    return {
      score: finalScore,
      status,
      color,
    };
  }, [vitalSigns, appointments]);
};

/**
 * Hook pour obtenir la tendance du score de santé dans le temps
 *
 * Compare le score de santé actuel avec la période précédente pour montrer
 * les tendances d'amélioration ou de déclin.
 *
 * @hook
 * @param currentVitals - Signes vitaux actuels
 * @param previousVitals - Signes vitaux de la période précédente
 * @param currentAppointments - Rendez-vous actuels
 * @param previousAppointments - Rendez-vous précédents
 * @returns Score de santé avec informations de tendance
 */
export const useHealthScoreTrend = (
  currentVitals?: signe_vitaux[],
  previousVitals?: signe_vitaux[],
  currentAppointments?: AppointmentPatientDTO[],
  previousAppointments?: AppointmentPatientDTO[],
): HealthScoreResult => {
  const currentScore = useHealthScore(currentVitals, currentAppointments);
  const previousScore = useHealthScore(previousVitals, previousAppointments);

  return useMemo(() => {
    const trendValue = currentScore.score - previousScore.score;
    const trendPercentage =
      previousScore.score > 0
        ? Math.round((trendValue / previousScore.score) * 100)
        : 0;

    return {
      ...currentScore,
      trend: {
        value: Math.abs(trendPercentage),
        isPositive: trendValue >= 0,
        label: "vs mois dernier",
      },
    };
  }, [currentScore, previousScore]);
};

/**
 * Hook pour calculer le score de santé à partir d'un PatientDTO avec carnet de santé intégré
 *
 * Utilise les nouvelles données du carnet de santé intégrées dans le PatientDTO
 * pour calculer un score de santé plus précis et complet.
 *
 * @hook
 * @example
 * ```tsx
 * const healthScore = useHealthScoreFromPatient(patient, appointments);
 *
 * return (
 *   <div>
 *     <span>Score: {healthScore.score}</span>
 *     <span style={{ color: healthScore.color }}>
 *       {healthScore.status}
 *     </span>
 *     {patient.carnetSante && (
 *       <p>Allergies: {patient.carnetSante.allergie.length}</p>
 *     )}
 *   </div>
 * );
 * ```
 *
 * @param patient - PatientDTO avec carnet de santé intégré
 * @param appointments - Historique des rendez-vous
 * @returns Score de santé calculé avec les données du carnet de santé
 *
 * @architecture
 * - Utilise les données du carnet de santé pour un calcul plus précis
 * - Gère les cas où le carnet de santé est null (nouveaux patients)
 * - Intègre allergies, médicaments, et conditions médicales dans le calcul
 */
export const useHealthScoreFromPatient = (
  patient?: PatientDTO,
  appointments?: AppointmentPatientDTO[],
): HealthScoreResult => {
  return useMemo(() => {
    if (!patient) {
      return {
        score: 0,
        status: "Préoccupante",
        color: HEALTH_SCORE_COLORS["Préoccupante"],
      };
    }

    return calculateHealthScoreFromPatient(patient, appointments);
  }, [patient, appointments]);
};
