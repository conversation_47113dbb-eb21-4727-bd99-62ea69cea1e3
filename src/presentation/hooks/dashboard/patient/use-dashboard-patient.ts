import { useEffect, useMemo, useState } from "react";
import { useConsultationState, useMedicalConsultation } from "@/presentation/hooks/consultationMedicale";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { usePatient } from "../../patient/use-patient";
import { useCarnetDeSante } from "../../carnetDeSante";
import { PatientMetricsData } from "@/presentation/types/patient.types";
import { useSigneVitaux } from "../../signeVitaux";
import { PRESCRIPTION_STATUS_CONFIG } from "@/presentation/constants/dashboard.constants";
import { useAppDispatch, useAppSelector } from "../../redux";
import { useDiagnostic } from "../../carnetDeSante/sousCarnet/useDiagnostic";

/**
 * Hook personnalisé pour la gestion des données du tableau de bord patient
 *
 * Fournit une récupération et un traitement centralisés des données pour le tableau de bord patient.
 * Se concentre sur la gestion des données et délègue la logique métier aux hooks spécialisés.
 *
 * @hook
 * @param patientId - ID du patient pour lequel récupérer les données
 * @returns Objet contenant les données patient, métriques et états de chargement
 *
 * @architecture
 * - Concentré sur la récupération et le traitement basique des données
 * - Délègue les calculs complexes aux hooks spécialisés
 * - Fournit une interface de données propre pour les composants du tableau de bord
 *
 * @performance
 * - Calculs mémorisés pour les données de graphiques
 * - Filtrage et traitement efficaces des données
 * - Re-rendus minimaux grâce à une gestion appropriée des dépendances
 */
export const useDashboardPatient = (patientId: number) => {
  const id = useAppSelector(
    (state) => state.authentification.userData?.utilisateur_id,
  );
  const {
    appointmentPatient,
    fetchAppointmentListByPatientId,
    loading: isFetchingAppointments,
  } = useConsultationState();
  const {
    patient,
    getPatientById,
    loading: isFetchingPatientData,
  } = usePatient();
  const {
    carnetSantes,
    get: getCarneSante,
    loading: isFetchingCarnetSante,
  } = useCarnetDeSante();
  const {
    signeVitaux,
    getAll: getSigneVitaux,
    loading: isFetchingSigneVitaux,
  } = useSigneVitaux();


  const { getMedicalConsultationsByPatient } =
    useMedicalConsultation();
  const { getSigneVitauxByPatient } =
    useSigneVitaux();
  const { getByPatient } =
    useDiagnostic();

  // Données de métriques patients
  const [patientMetrics, setPatientMetrics] =
    useState<PatientMetricsData>(null);

  // Données pour le graphique de rendez-vous
  const [appointmentData, setAppointmentData] = useState<
    {
      name: string;
      appointments: number;
    }[]
  >([]);

  const [healthMetrics, setHealthMetrics] = useState<
    {
      name: string;
      poids: number;
      tension: number;
    }[]
  >([]);

  // Configuration des ordonnances (déplacée vers les constantes)
  const ordonnancesState = PRESCRIPTION_STATUS_CONFIG;

  // Statistiques des rendez-vous
  const [appointmentStats, setAppointmentStats] = useState({
    completed: 0,
    upcoming: 0,
    cancelled: 0,
    completedThisMonth: 0,
  });

  const [consultationsPatient, setConsultationsPatient] = useState([]);
  const [signeVitauxPatient, setSigneVitauxPatient] = useState([]);
  const [diagnosticPatient, setDiagnosticPatient] = useState([]);

  // Charger les données initiales
  useEffect(() => {
    if (patientId) {
      // Charger d'abord le carnet de santé pour obtenir l'ID
      getPatientById(patientId);
      fetchAppointmentListByPatientId(patientId);
      consultationsByPatient();
      signeVitauxByPatient();
      diagnosticByPatient();
    }
  }, [patientId]);

  // Mise à jour des statistiques de rendez-vous
  useEffect(() => {
    if (appointmentPatient) {
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Filtrer les rendez-vous du mois en cours
      const thisMonthAppointments = appointmentPatient.filter((appointment) => {
        try {
          const appointmentDate = new Date(appointment.date_rendez_vous);
          return appointmentDate >= firstDayOfMonth && appointmentDate <= now;
        } catch (error) {
          return false;
        }
      });

      // Compter les rendez-vous par statut (tous)
      const completed = appointmentPatient.filter(
        (appointment) =>
          appointment.statut === rendez_vous_statut_enum.TERMINER,
      ).length;

      const upcoming = appointmentPatient.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.A_VENIR,
      ).length;

      const cancelled = appointmentPatient.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.ANNULER,
      ).length;

      // Compter les rendez-vous terminés du mois en cours
      const completedThisMonth = thisMonthAppointments.filter(
        (appointment) =>
          appointment.statut === rendez_vous_statut_enum.TERMINER,
      ).length;

      setAppointmentStats({
        completed,
        upcoming,
        cancelled,
        completedThisMonth: completedThisMonth,
      });
    }
  }, [appointmentPatient]);


  // filter consultations
  const consultationsByPatient = async () => {
    try {
      const result = await getMedicalConsultationsByPatient(id);
      // Vérifier que result est un tableau avant de l'assigner
      if (Array.isArray(result)) {
        setConsultationsPatient(result);
        return result;
      } else {
        console.warn("getMedicalConsultationsByPatient did not return an array:", result);
        setConsultationsPatient([]);
        return [];
      }
    } catch (error) {
      console.error("Error fetching consultations:", error);
      setConsultationsPatient([]); // Définir un tableau vide en cas d'erreur
      return [];
    }
  };

  const signeVitauxByPatient = async () => {
    try {
      const result = await getSigneVitauxByPatient(id);
      // Vérifier que result est un tableau avant de l'assigner
      if (Array.isArray(result)) {
        setSigneVitauxPatient(result);
        return result;
      } else {
        console.warn("getSigneVitauxByPatient did not return an array:", result);
        setSigneVitauxPatient([]);
        return [];
      }
    } catch (error) {
      console.error("Error fetching signe vitaux:", error);
      setSigneVitauxPatient([]); // Définir un tableau vide en cas d'erreur
      return [];
    }
  }

  const diagnosticByPatient = async () => {
    try {
      const result = await getByPatient(id);
      console.log("getByPatient result:", result);

      if (Array.isArray(result)) {
        setDiagnosticPatient(result);
        return result;
      } else {
        console.warn("getByPatient did not return an array:", result);
        setDiagnosticPatient([]);
        return [];
      }

    } catch (error) {
      console.error("Error fetching diagnostics:", error);
      setDiagnosticPatient([]); // Définir un tableau vide en cas d'erreur
      return [];
    }
  }

  // Filtrer les rendez-vous du jour
  const todayAppointment = useMemo(() => {
    // Si pas de rendez-vous, retourner null
    if (!appointmentPatient || appointmentPatient.length === 0) {
      return null;
    }

    // Rendez-vous du jour
    const today = new Date();
    const todayStr = today.toISOString().split("T")[0];

    return appointmentPatient.find((appointment) => {
      try {
        // Vérifier si date_rendez_vous existe
        if (!appointment.date_rendez_vous) {
          return false;
        }

        // Essayer de créer un objet Date
        const appointmentDate = new Date(appointment.date_rendez_vous);

        // Vérifier si la date est valide
        if (isNaN(appointmentDate.getTime())) {
          return false;
        }

        const appointmentDateStr = appointmentDate.toISOString().split("T")[0];
        return appointmentDateStr === todayStr;
      } catch (error) {
        return false;
      }
    });
  }, [appointmentPatient]);

  // Mise à jour des métriques patients
  useEffect(() => {
    if (appointmentPatient) {
      const totalPatients = appointmentPatient.length;

      // Calculer les nouveaux patients de ce mois-ci
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const newPatients = appointmentPatient.filter((patient) => {
        try {
          const createdDate = new Date(patient.date_rendez_vous);
          // Vérifier si la date de rendez-vous est dans le mois en cours
          return createdDate >= firstDayOfMonth && createdDate <= now;
        } catch (error) {
          return false;
        }
      }).length;

      // Mise à jour des métriques
      setPatientMetrics({
        totalPatients,
        newPatients,
        trends: {
          newPatients: Math.round((newPatients / totalPatients) * 100) || 0,
        },
      });
    }
  }, [appointmentPatient]);

  // Mise à jour des données de rendez-vous
  useEffect(() => {
    if (appointmentPatient) {
      // Calculer les données pour les graphiques
      const now = new Date();

      // Générer les données pour le graphique de rendez-vous (6 derniers mois)
      const last6Months = [];
      const monthNames = [
        "Jan",
        "Fév",
        "Mar",
        "Avr",
        "Mai",
        "Juin",
        "Juil",
        "Août",
        "Sep",
        "Oct",
        "Nov",
        "Déc",
      ];

      for (let i = 5; i >= 0; i--) {
        const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = monthNames[monthDate.getMonth()];
        const monthFirstDay = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth(),
          1,
        );
        const monthLastDay = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth() + 1,
          0,
        );

        const monthAppointments = appointmentPatient.filter((appointment) => {
          try {
            const appointmentDate = new Date(appointment.date_rendez_vous);
            return (
              appointmentDate >= monthFirstDay &&
              appointmentDate <= monthLastDay
            );
          } catch (error) {
            return false;
          }
        });

        last6Months.push({
          name: monthName,
          appointments: monthAppointments.length,
        });
      }

      setAppointmentData(last6Months);
    }
  }, [appointmentPatient]);

  // Évolution des métriques de santé
  useEffect(() => {
    if (signeVitaux) {
      // Calculer les données pour les métriques de santé
      const now = new Date();

      // Générer les données pour le graphique des métriques de santé (6 derniers mois)
      const last6Months: {
        name: string;
        poids: number;
        tension: number;
      }[] = [];

      const monthNames = [
        "Jan",
        "Fév",
        "Mar",
        "Avr",
        "Mai",
        "Juin",
        "Juil",
        "Août",
        "Sep",
        "Oct",
        "Nov",
        "Déc",
      ];

      for (let i = 5; i >= 0; i--) {
        const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = monthNames[monthDate.getMonth()];
        const monthFirstDay = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth(),
          1,
        );
        const monthLastDay = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth() + 1,
          0,
        );

        const monthSV = signeVitaux.filter((sv) => {
          try {
            const svDate = new Date(sv.date_visite);
            return svDate >= monthFirstDay && svDate <= monthLastDay;
          } catch (error) {
            return false;
          }
        });

        last6Months.push({
          name: monthName,
          poids: monthSV[monthSV.length - 1]?.poid,
          tension: Number(
            monthSV[monthSV.length - 1]?.tension_arterielle?.split("/")[0],
          ),
        });
      }

      setHealthMetrics(last6Months);
    }
  }, [signeVitaux]);

  return {
    signeVitauxPatient,
    consultationsPatient,
    signeVitauxByPatient,
    consultationsByPatient,
    diagnosticPatient,
    patientMetrics,
    appointmentData,
    ordonnancesState,
    appointmentStats,
    todayAppointment,
    totalAppointments: appointmentPatient?.length || 0,
    appointmentPatient,
    patient,
    healthMetrics,
    loading: {
      appointments: appointmentPatient === null,
      facturations: appointmentPatient === null,
      carnetSante: carnetSantes === null,
      patient: patient === null,
    },
  };
};
