import { useState, useCallback, useRef } from 'react';
import { DashboardError, NotificationType } from '@/presentation/types/dashboard.types';

/**
 * Interface pour la gestion d'erreurs
 */
interface ErrorHandlingState {
  /** Erreur courante */
  error: DashboardError | null;
  /** Historique des erreurs */
  errorHistory: DashboardError[];
  /** Indicateur si une erreur est en cours de traitement */
  isHandlingError: boolean;
}

/**
 * Interface de retour du hook useErrorHandling
 */
interface ErrorHandlingResult {
  /** État actuel des erreurs */
  errorState: ErrorHandlingState;
  /** Fonction pour signaler une erreur */
  reportError: (error: Error | string, context?: Record<string, unknown>) => void;
  /** Fonction pour effacer l'erreur courante */
  clearError: () => void;
  /** Fonction pour effacer l'historique */
  clearHistory: () => void;
  /** Fonction pour réessayer une opération */
  retry: (operation: () => Promise<void>) => Promise<void>;
  /** Fonction pour créer une notification d'erreur */
  createErrorNotification: (error: DashboardError) => {
    type: NotificationType;
    title: string;
    message: string;
  };
}

/**
 * Hook moderne pour la gestion d'erreurs dans le dashboard
 * 
 * Fournit un système complet de gestion d'erreurs avec historique,
 * notifications automatiques et mécanismes de récupération.
 * 
 * @hook
 * @example
 * ```tsx
 * const { errorState, reportError, clearError, retry } = useErrorHandling();
 * 
 * const handleDataFetch = async () => {
 *   try {
 *     const data = await fetchPatientData();
 *     setData(data);
 *   } catch (error) {
 *     reportError(error, { context: 'data-fetch', patientId });
 *   }
 * };
 * 
 * const handleRetry = () => {
 *   retry(handleDataFetch);
 * };
 * 
 * if (errorState.error) {
 *   return <ErrorDisplay error={errorState.error} onRetry={handleRetry} />;
 * }
 * ```
 * 
 * @returns Objet contenant l'état des erreurs et les fonctions de gestion
 * 
 * @architecture
 * - Gestion centralisée des erreurs avec contexte
 * - Historique des erreurs pour le debugging
 * - Mécanismes de récupération automatique
 * - Intégration avec le système de notifications
 * 
 * @performance
 * - État optimisé avec useRef pour éviter les re-renders
 * - Callbacks mémorisés pour la performance
 * - Gestion efficace de l'historique avec limite
 */
export const useErrorHandling = (): ErrorHandlingResult => {
  const [errorState, setErrorState] = useState<ErrorHandlingState>({
    error: null,
    errorHistory: [],
    isHandlingError: false,
  });

  // Référence pour éviter les re-renders inutiles
  const errorCountRef = useRef(0);
  const maxHistorySize = 10;

  /**
   * Crée un objet DashboardError standardisé
   */
  const createDashboardError = useCallback((
    error: Error | string,
    context?: Record<string, unknown>
  ): DashboardError => {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorCode = error instanceof Error ? error.name : 'UNKNOWN_ERROR';

    return {
      code: `DASHBOARD_${errorCode}_${++errorCountRef.current}`,
      message: errorMessage,
      details: {
        ...context,
        stack: error instanceof Error ? error.stack : undefined,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date(),
    };
  }, []);

  /**
   * Signale une nouvelle erreur
   */
  const reportError = useCallback((
    error: Error | string,
    context?: Record<string, unknown>
  ) => {
    const dashboardError = createDashboardError(error, context);

    setErrorState(prevState => {
      const newHistory = [dashboardError, ...prevState.errorHistory]
        .slice(0, maxHistorySize);

      return {
        error: dashboardError,
        errorHistory: newHistory,
        isHandlingError: false,
      };
    });

    // Log l'erreur pour le debugging en développement
    if (process.env.NODE_ENV === 'development') {
      console.error('Dashboard Error:', dashboardError);
    }

    // TODO: Envoyer l'erreur à un service de monitoring en production
    // sendErrorToMonitoring(dashboardError);
  }, [createDashboardError]);

  /**
   * Efface l'erreur courante
   */
  const clearError = useCallback(() => {
    setErrorState(prevState => ({
      ...prevState,
      error: null,
      isHandlingError: false,
    }));
  }, []);

  /**
   * Efface l'historique des erreurs
   */
  const clearHistory = useCallback(() => {
    setErrorState(prevState => ({
      ...prevState,
      errorHistory: [],
    }));
  }, []);

  /**
   * Réessaie une opération avec gestion d'erreur
   */
  const retry = useCallback(async (operation: () => Promise<void>) => {
    setErrorState(prevState => ({
      ...prevState,
      isHandlingError: true,
    }));

    try {
      await operation();
      clearError();
    } catch (error) {
      reportError(error as Error, { context: 'retry-operation' });
    }
  }, [clearError, reportError]);

  /**
   * Crée une notification d'erreur formatée
   */
  const createErrorNotification = useCallback((error: DashboardError) => {
    // Détermine le type de notification basé sur le code d'erreur
    let type: NotificationType = 'error';
    let title = 'Erreur';

    if (error.code.includes('NETWORK')) {
      type = 'warning';
      title = 'Problème de connexion';
    } else if (error.code.includes('VALIDATION')) {
      type = 'warning';
      title = 'Données invalides';
    } else if (error.code.includes('PERMISSION')) {
      type = 'error';
      title = 'Accès refusé';
    }

    return {
      type,
      title,
      message: error.message,
    };
  }, []);

  return {
    errorState,
    reportError,
    clearError,
    clearHistory,
    retry,
    createErrorNotification,
  };
};

/**
 * Hook pour la gestion d'erreurs spécifique aux opérations asynchrones
 * 
 * @hook
 * @param operation - Fonction asynchrone à exécuter
 * @returns État et contrôles pour l'opération
 */
export const useAsyncOperation = <T>(
  operation: () => Promise<T>
) => {
  const [state, setState] = useState<{
    data: T | null;
    loading: boolean;
    error: DashboardError | null;
  }>({
    data: null,
    loading: false,
    error: null,
  });

  const { reportError } = useErrorHandling();

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await operation();
      setState({ data: result, loading: false, error: null });
      return result;
    } catch (error) {
      const dashboardError = {
        code: 'ASYNC_OPERATION_ERROR',
        message: error instanceof Error ? error.message : 'Opération échouée',
        details: { operation: operation.name },
        timestamp: new Date(),
      };

      setState(prev => ({ ...prev, loading: false, error: dashboardError }));
      reportError(error as Error, { context: 'async-operation' });
      throw error;
    }
  }, [operation, reportError]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
};

/**
 * Hook pour la gestion d'erreurs avec retry automatique
 * 
 * @hook
 * @param operation - Opération à exécuter
 * @param maxRetries - Nombre maximum de tentatives
 * @param retryDelay - Délai entre les tentatives (ms)
 */
export const useRetryableOperation = <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  retryDelay: number = 1000
) => {
  const [retryCount, setRetryCount] = useState(0);
  const { errorState, reportError, clearError } = useErrorHandling();

  const executeWithRetry = useCallback(async (): Promise<T> => {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await operation();
        setRetryCount(0);
        clearError();
        return result;
      } catch (error) {
        lastError = error as Error;
        setRetryCount(attempt + 1);

        if (attempt < maxRetries) {
          // Attendre avant la prochaine tentative
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
        }
      }
    }

    // Toutes les tentatives ont échoué
    reportError(lastError!, { 
      context: 'retryable-operation',
      maxRetries,
      finalAttempt: retryCount 
    });
    throw lastError!;
  }, [operation, maxRetries, retryDelay, retryCount, reportError, clearError]);

  return {
    executeWithRetry,
    retryCount,
    error: errorState.error,
    isRetrying: retryCount > 0,
  };
};
