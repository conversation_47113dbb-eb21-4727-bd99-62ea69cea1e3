import { useMemo } from 'react';
import { 
  DASHBOARD_CHART_CONFIG, 
  TOOLTIP_CONFIG, 
  DASHBOARD_CHART_COLORS 
} from '@/presentation/constants/dashboard.constants';

/**
 * Chart configuration interface for type safety
 */
interface ChartConfiguration {
  tooltipContentStyle: React.CSSProperties;
  tooltipItemStyle: React.CSSProperties;
  tooltipLabelStyle: React.CSSProperties;
  gridConfig: {
    strokeDasharray: string;
    stroke: string;
  };
  axisConfig: {
    stroke: string;
  };
  colors: typeof DASHBOARD_CHART_COLORS;
  margins: typeof DASHBOARD_CHART_CONFIG.margins.default;
}

/**
 * Custom hook for managing patient dashboard chart configuration
 * 
 * Provides chart styling, tooltip configuration, and theme-aware
 * settings for dashboard charts. Optimized for performance with
 * memoized configurations.
 * 
 * @hook
 * @example
 * ```tsx
 * const { 
 *   tooltipContentStyle, 
 *   tooltipItemStyle, 
 *   tooltipLabelStyle,
 *   gridConfig,
 *   axisConfig,
 *   colors 
 * } = useChartConfiguration(isDarkMode);
 * 
 * <BarChart data={data} margin={margins}>
 *   <CartesianGrid {...gridConfig} />
 *   <XAxis {...axisConfig} />
 *   <Tooltip 
 *     contentStyle={tooltipContentStyle}
 *     itemStyle={tooltipItemStyle}
 *     labelStyle={tooltipLabelStyle}
 *   />
 *   <Bar dataKey="value" fill={colors.primary} />
 * </BarChart>
 * ```
 * 
 * @param isDarkMode - Current dark mode state from theme context
 * @returns Object containing chart and tooltip configurations
 * 
 * @performance
 * - Configurations are memoized based on theme changes
 * - Lightweight theme-based style calculations
 * - Prevents unnecessary re-renders of chart components
 * 
 * @architecture
 * - Centralizes all chart styling logic
 * - Provides consistent theming across dashboard charts
 * - Separates presentation configuration from business logic
 */
export const useChartConfiguration = (isDarkMode: boolean): ChartConfiguration => {
  return useMemo(() => {
    // Select theme-appropriate tooltip configuration
    const tooltipConfig = isDarkMode ? TOOLTIP_CONFIG.dark : TOOLTIP_CONFIG.light;
    
    // Configure grid styling based on theme
    const gridConfig = {
      strokeDasharray: DASHBOARD_CHART_CONFIG.grid.strokeDasharray,
      stroke: isDarkMode 
        ? DASHBOARD_CHART_CONFIG.grid.strokeDark 
        : DASHBOARD_CHART_CONFIG.grid.strokeLight,
    };

    // Configure axis styling based on theme
    const axisConfig = {
      stroke: isDarkMode 
        ? DASHBOARD_CHART_CONFIG.axis.strokeDark 
        : DASHBOARD_CHART_CONFIG.axis.strokeLight,
    };

    return {
      // Tooltip configurations
      tooltipContentStyle: tooltipConfig.contentStyle,
      tooltipItemStyle: tooltipConfig.itemStyle,
      tooltipLabelStyle: tooltipConfig.labelStyle,
      
      // Chart element configurations
      gridConfig,
      axisConfig,
      
      // Color palette
      colors: DASHBOARD_CHART_COLORS,
      
      // Layout configurations
      margins: DASHBOARD_CHART_CONFIG.margins.default,
    };
  }, [isDarkMode]);
};

/**
 * Hook for getting chart-specific configurations
 * 
 * Provides specialized configurations for different chart types
 * used in the patient dashboard.
 * 
 * @hook
 * @example
 * ```tsx
 * const appointmentChartConfig = useSpecificChartConfig('appointment', isDarkMode);
 * const healthChartConfig = useSpecificChartConfig('health', isDarkMode);
 * ```
 * 
 * @param chartType - Type of chart ('appointment' | 'health' | 'stats')
 * @param isDarkMode - Current dark mode state
 * @returns Chart-specific configuration object
 */
export const useSpecificChartConfig = (
  chartType: 'appointment' | 'health' | 'stats',
  isDarkMode: boolean
) => {
  const baseConfig = useChartConfiguration(isDarkMode);

  return useMemo(() => {
    switch (chartType) {
      case 'appointment':
        return {
          ...baseConfig,
          primaryColor: baseConfig.colors.primary,
          secondaryColor: baseConfig.colors.info,
          margins: DASHBOARD_CHART_CONFIG.margins.default,
          barRadius: 4,
        };
      
      case 'health':
        return {
          ...baseConfig,
          primaryColor: baseConfig.colors.success,
          warningColor: baseConfig.colors.warning,
          dangerColor: baseConfig.colors.danger,
          margins: DASHBOARD_CHART_CONFIG.margins.compact,
          lineStrokeWidth: 2,
        };
      
      case 'stats':
        return {
          ...baseConfig,
          colors: {
            completed: baseConfig.colors.success,
            upcoming: baseConfig.colors.info,
            cancelled: baseConfig.colors.warning,
            weight: baseConfig.colors.purple,
          },
          margins: DASHBOARD_CHART_CONFIG.margins.compact,
        };
      
      default:
        return baseConfig;
    }
  }, [chartType, baseConfig]);
};

/**
 * Hook for responsive chart configurations
 * 
 * Provides responsive chart settings based on screen size
 * and device capabilities.
 * 
 * @hook
 * @param screenSize - Current screen size ('mobile' | 'tablet' | 'desktop')
 * @param isDarkMode - Current dark mode state
 * @returns Responsive chart configuration
 */
export const useResponsiveChartConfig = (
  screenSize: 'mobile' | 'tablet' | 'desktop',
  isDarkMode: boolean
) => {
  const baseConfig = useChartConfiguration(isDarkMode);

  return useMemo(() => {
    const responsiveMargins = {
      mobile: DASHBOARD_CHART_CONFIG.margins.compact,
      tablet: DASHBOARD_CHART_CONFIG.margins.default,
      desktop: DASHBOARD_CHART_CONFIG.margins.extended,
    };

    const responsiveFontSizes = {
      mobile: { fontSize: 12 },
      tablet: { fontSize: 14 },
      desktop: { fontSize: 16 },
    };

    return {
      ...baseConfig,
      margins: responsiveMargins[screenSize],
      fontSize: responsiveFontSizes[screenSize],
      // Adjust tooltip positioning for mobile
      tooltipOffset: screenSize === 'mobile' ? 10 : 20,
    };
  }, [screenSize, baseConfig]);
};
