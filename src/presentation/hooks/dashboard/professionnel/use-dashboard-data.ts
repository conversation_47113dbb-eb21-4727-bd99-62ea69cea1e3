import { useEffect, useMemo, useState } from "react";
import {
  Revenue,
  PatientMetricsData,
} from "@/presentation/types/professional.types";
import {
  useConsultationState,
  useMedicalConsultation,
} from "@/presentation/hooks/consultationMedicale";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { useFacturation } from "@/presentation/hooks/facturation";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { useMedicament } from "../../carnetDeSante/sousCarnet/useMedicament";
import { Medicament } from "@/domain/models";
import { useAffectationMedicale } from "../../carnetDeSante/sousCarnet/useAffectationMedicale";

export const useDashboardData = (professionalId: number) => {
  const { appointmentProfessional, fetchAppointmentListByProfessionalId } =
    useConsultationState();
  const { dataProfessionalPatients, getProfessionnelPatient } =
    useProfessionnelPatient();
  const { listeFacturationProfessional, getFacturationsByProfessionalId } =
    useFacturation();
  const { consultations, getMedicalConsultationsByProfessionalId } =
    useMedicalConsultation();
  const { getMedicamentByProfessional } =
    useMedicament();
  const { getByProfessional } =
    useAffectationMedicale();

  const [isStatistiqueModalOpen, setIsStatistiqueModalOpen] = useState<boolean>(false);


  // Données de métriques patients
  const [patientMetrics, setPatientMetrics] =
    useState<PatientMetricsData>(null);

  // Données de revenus
  const [revenue, setRevenue] = useState<Revenue>({
    today: 0,
    monthly: 0,
    yearly: 0,
    trend: { percentage: 0, isPositive: true },
  });

  // Données pour le graphique de revenus
  const [revenueData, setRevenueData] = useState([]);

  // Données pour le graphique de distribution des patients
  const [patientDistribution, setPatientDistribution] = useState([
    { name: "Nouveaux", value: 0, color: "#4F46E5" },
    { name: "Réguliers", value: 0, color: "#10B981" },
    { name: "Occasionnels", value: 0, color: "#F59E0B" },
  ]);

  // Statistiques des rendez-vous
  const [appointmentStats, setAppointmentStats] = useState({
    completed: 0,
    upcoming: 0,
    cancelled: 0,
    totalThisMonth: 0,
    completedThisMonth: 0,
  });

  // Données de medicament
  const [top5Medicaments, setTop5Medicaments] = useState([]);
  const [isLoadingMedicament, setIsLoadingMedicament] = useState(false);

  // Données de maladie
  const [top5Maladies, setTop5Maladies] = useState([]);
  const [isLoadingMaladie, setIsLoadingMaladie] = useState(false);

  // Charger les données initiales
  useEffect(() => {
    if (professionalId) {
      getProfessionnelPatient(professionalId);
      getFacturationsByProfessionalId(professionalId);
      fetchAppointmentListByProfessionalId(professionalId);
      getMedicalConsultationsByProfessionalId(professionalId);
    }
  }, [professionalId]);

  // Fetch and filter Stats medicament
  const fetchAndFilterStatsMedicament = async () => {
    setIsLoadingMedicament(true);
    try {
      const stats = await getMedicamentByProfessional(professionalId);

      if (Array.isArray(stats) && stats.length > 0) {
        const top5MedicamentsResult = stats
          .filter((item) => item.nom && item.repetitions)
          .sort((a, b) => b.repetitions - a.repetitions)
          .slice(0, 5);

        setTop5Medicaments(top5MedicamentsResult);

        return top5MedicamentsResult;
      }
      setTop5Medicaments([]);
      return [];
    } catch (error) {
      console.error("Erreur lors du filtrage des stats:", error);
      setTop5Medicaments([]);
      return [];
    } finally {
      setIsLoadingMedicament(false);
    }
  };

  const fetchAndFilterStatsMaladie = async () => {
    setIsLoadingMaladie(true);
    try {
      const stats = await getByProfessional(professionalId);

      if (Array.isArray(stats) && stats.length > 0) {
        const top5MaladiesResult = stats
          .filter((item) => item.maladie && item.repetitions)
          .sort((a, b) => b.repetitions - a.repetitions)
          .slice(0, 5);

        setTop5Maladies(top5MaladiesResult);

        return top5MaladiesResult;
      }
      setTop5Maladies([]);
      return [];
    } catch (error) {
      console.error("Erreur lors du filtrage des stats:", error);
      setTop5Maladies([]);
      return [];
    } finally {
      setIsLoadingMaladie(false);
    }
  };

  // Charger automatiquement les données au montage du composant
  useEffect(() => {
    fetchAndFilterStatsMedicament();
    fetchAndFilterStatsMaladie();
  }, [professionalId]);

  // Mise à jour des métriques patients
  useEffect(() => {
    if (dataProfessionalPatients) {
      const totalPatients = dataProfessionalPatients.length;

      // Calculer les nouveaux patients de ce mois-ci
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const newPatients = dataProfessionalPatients.filter((patient) => {
        try {
          const createdDate = new Date(patient.created_date);
          // Vérifier si la date de création est dans le mois en cours
          return createdDate >= firstDayOfMonth && createdDate <= now;
        } catch (error) {
          return false;
        }
      }).length;

      const todayPatients = dataProfessionalPatients.filter((patient) => {
        try {
          const createdDate = new Date(patient.created_date);
          // Vérifier si la date de création est aujourd'hui
          return createdDate.toDateString() === new Date().toDateString();
        } catch (error) {
          return false;
        }
      }).length;

      // Calculer les patients réguliers (plus d'une visite)
      const patientVisitCounts: Record<string, number> = {};
      appointmentProfessional?.forEach((appointment) => {
        const patientId = appointment.patient?.id;
        if (patientId) {
          patientVisitCounts[patientId] =
            (patientVisitCounts[patientId] || 0) + 1;
        }
      });

      const returningPatients = Object.values(patientVisitCounts).filter(
        (count) => count > 1
      ).length;

      // Mise à jour des métriques
      setPatientMetrics({
        totalPatients,
        newPatients,
        todayPatients,
        returningPatients,
        averageVisitTime: "45 min", // À calculer si les données sont disponibles
        trends: {
          newPatients: Math.round((newPatients / totalPatients) * 100) || 0,
          returningPatients:
            Math.round((returningPatients / totalPatients) * 100) || 0,
        },
      });

      // Mise à jour de la distribution des patients
      setPatientDistribution([
        { name: "Nouveaux", value: newPatients, color: "#4F46E5" },
        { name: "Réguliers", value: returningPatients, color: "#10B981" },
        {
          name: "Occasionnels",
          value: totalPatients - newPatients - returningPatients,
          color: "#F59E0B",
        },
      ]);
    }
  }, [dataProfessionalPatients, appointmentProfessional]);

  // Mise à jour des statistiques de rendez-vous
  useEffect(() => {
    if (appointmentProfessional) {
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Filtrer les rendez-vous du mois en cours
      const thisMonthAppointments = appointmentProfessional.filter(
        (appointment) => {
          try {
            const appointmentDate = new Date(appointment.date_rendez_vous);
            return appointmentDate >= firstDayOfMonth && appointmentDate <= now;
          } catch (error) {
            return false;
          }
        }
      );

      // Compter les rendez-vous par statut (tous)
      const completed = appointmentProfessional.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.TERMINER
      ).length;

      const upcoming = appointmentProfessional.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.A_VENIR
      ).length;

      const cancelled = appointmentProfessional.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.ANNULER
      ).length;

      // Compter les rendez-vous terminés du mois en cours
      const completedThisMonth = thisMonthAppointments.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.TERMINER
      ).length;

      setAppointmentStats({
        completed,
        upcoming,
        cancelled,
        totalThisMonth: thisMonthAppointments.length,
        completedThisMonth: completedThisMonth,
      });
    }
  }, [appointmentProfessional]);

  // Mise à jour des données de revenus
  useEffect(() => {
    if (listeFacturationProfessional) {
      // Calculer le revenu total du mois en cours
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const todayFacturations = listeFacturationProfessional.filter(
        (facture) => {
          try {
            const factureDate = new Date(facture.date_creation);
            return factureDate.toDateString() === new Date().toDateString();
          } catch (error) {
            return false;
          }
        }
      );

      const thisMonthFacturations = listeFacturationProfessional.filter(
        (facture) => {
          try {
            const factureDate = new Date(facture.date_creation);
            return factureDate >= firstDayOfMonth && factureDate <= now;
          } catch (error) {
            return false;
          }
        }
      );

      const thisYearFacturations = listeFacturationProfessional.filter(
        (facture) => {
          try {
            const factureDate = new Date(facture.date_creation);
            return (
              factureDate >= new Date(now.getFullYear(), 0, 1) &&
              factureDate <= new Date(now.getFullYear(), 11, 31)
            );
          } catch (error) {
            return false;
          }
        }
      );

      const lastMonthFirstDay = new Date(
        now.getFullYear(),
        now.getMonth() - 1,
        1
      );
      const lastMonthLastDay = new Date(now.getFullYear(), now.getMonth(), 0);

      const lastMonthFacturations = listeFacturationProfessional.filter(
        (facture) => {
          try {
            const factureDate = new Date(facture.date_creation);
            return (
              factureDate >= lastMonthFirstDay &&
              factureDate <= lastMonthLastDay
            );
          } catch (error) {
            return false;
          }
        }
      );

      // Calculer le montant total des facturations aujourd'hui
      const todayTotal = todayFacturations.reduce(
        (total, facture) => total + (facture.montant || 0),
        0
      );

      // Calculer le montant total des facturations du mois en cours
      const thisMonthTotal = thisMonthFacturations.reduce(
        (total, facture) => total + (facture.montant || 0),
        0
      );

      // Calculer le montant total des facturations cette année
      const thisYearTotal = thisYearFacturations.reduce(
        (total, facture) => total + (facture.montant || 0),
        0
      );

      // Calculer le montant total des facturations du mois précédent
      const lastMonthTotal = lastMonthFacturations.reduce(
        (total, facture) => total + (facture.montant || 0),
        0
      );

      // Calculer la tendance
      let trendPercentage = 0;
      let isPositive = true;

      if (lastMonthTotal > 0) {
        trendPercentage = Math.round(
          ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100
        );
        isPositive = trendPercentage >= 0;
        trendPercentage = Math.abs(trendPercentage);
      }

      setRevenue({
        today: todayTotal,
        monthly: thisMonthTotal,
        yearly: thisYearTotal,
        trend: { percentage: trendPercentage, isPositive },
      });

      // Générer les données pour le graphique de revenus (6 derniers mois)
      const last6Months = [];
      const monthNames = [
        "Jan",
        "Fév",
        "Mar",
        "Avr",
        "Mai",
        "Juin",
        "Juil",
        "Août",
        "Sep",
        "Oct",
        "Nov",
        "Déc",
      ];

      for (let i = 5; i >= 0; i--) {
        const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = monthNames[monthDate.getMonth()];
        const monthFirstDay = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth(),
          1
        );
        const monthLastDay = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth() + 1,
          0
        );

        const monthFacturations = listeFacturationProfessional.filter(
          (facture) => {
            try {
              const factureDate = new Date(facture.date_creation);
              return (
                factureDate >= monthFirstDay && factureDate <= monthLastDay
              );
            } catch (error) {
              return false;
            }
          }
        );

        const monthTotal = monthFacturations.reduce(
          (total, facture) => total + (facture.montant || 0),
          0
        );

        last6Months.push({
          name: monthName,
          revenue: monthTotal,
        });
      }

      setRevenueData(last6Months);
    }
  }, [listeFacturationProfessional]);

  // Filtrer les rendez-vous du jour
  const todayAppointments = useMemo(() => {
    // Si pas de rendez-vous, retourner un tableau vide
    if (!appointmentProfessional || appointmentProfessional.length === 0) {
      return [];
    }

    // Rendez-vous du jour
    const today = new Date();
    const todayStr = today.toISOString().split("T")[0];

    return appointmentProfessional.filter((appointment) => {
      try {
        // Vérifier si date_rendez_vous existe
        if (!appointment.date_rendez_vous) {
          return false;
        }

        // Essayer de créer un objet Date
        const appointmentDate = new Date(appointment.date_rendez_vous);

        // Vérifier si la date est valide
        if (isNaN(appointmentDate.getTime())) {
          return false;
        }

        const appointmentDateStr = appointmentDate.toISOString().split("T")[0];
        return appointmentDateStr === todayStr;
      } catch (error) {
        return false;
      }
    });
  }, [appointmentProfessional]);

  // Filtrer les consultations du jour
  const todayConsultations = useMemo(() => {
    // Si pas de consultations, retourner un tableau vide
    // console.log(consultations);
    if (!consultations || consultations.length === 0) {
      return [];
    }

    // Consultations du jour
    const today = new Date();
    const todayStr = today.toISOString().split("T")[0];

    return consultations.filter((consultation) => {
      try {
        // Vérifier si la date de la consultation existe
        if (!consultation.date_visite) {
          return false;
        }

        // Essayer de créer un objet Date
        const consultationDate = new Date(consultation.date_visite);

        // Vérifier si la date est valide
        if (isNaN(consultationDate.getTime())) {
          return false;
        }

        const consultationDateStr = consultationDate
          .toISOString()
          .split("T")[0];
        return consultationDateStr === todayStr;
      } catch (error) {
        return false;
      }
    });
  }, [consultations]);


  // Filtrer les patients créés récemment ( 7 derniers jours) et les trier par date de création décroissante
  const recentPatient = useMemo(() => {
    // console.log(dataProfessionalPatients);

    if (!dataProfessionalPatients || dataProfessionalPatients.length === 0) {
      return [];
    }

    const now = new Date();
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(now.getDate() - 7);

    return dataProfessionalPatients
      .filter((patient) => {
        try {
          if (!patient.created_date) return false;
          const createdDate = new Date(patient.created_date);
          //filtre si pour les 7 dernier jour
          // return createdDate >= sevenDaysAgo && createdDate <= now;
          return createdDate;
        } catch (error) {
          return false;
        }
      })
      // Trier par date de création décroissante (les plus récents en premier)
      .sort((a, b) => {
        const dateA = a.created_date ? new Date(a.created_date).getTime() : 0;
        const dateB = b.created_date ? new Date(b.created_date).getTime() : 0;
        return dateB - dateA;
      });
  }, [dataProfessionalPatients]);

  // Filtrer les patients mise à jour récemment ( 7 derniers jours) et les trier par date de mise à jour décroissante
  const recentUpdatedPatient = useMemo(() => {
    if (!dataProfessionalPatients || dataProfessionalPatients.length === 0) {
      return [];
    }

    const now = new Date();
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(now.getDate() - 7);

    return dataProfessionalPatients
      .filter((patient) => {
        try {
          if (!patient.updated_date) return false;
          const updatedDate = new Date(patient.updated_date);
          //filtre si pour les 7 dernier jour
          // return updatedDate >= sevenDaysAgo && updatedDate <= now;
          return updatedDate;
        } catch (error) {
          return false;
        }
      })
      // Trier par date de mise à jour décroissante (les plus récents en premier)
      .sort((a, b) => {
        const dateA = a.updated_date ? new Date(a.updated_date).getTime() : 0;
        const dateB = b.updated_date ? new Date(b.updated_date).getTime() : 0;
        return dateB - dateA;
      });
  }, [dataProfessionalPatients]);


  // Calculer le taux de complétion des rendez-vous (uniquement pour le mois en cours)
  const completionRate =
    appointmentStats.totalThisMonth > 0
      ? Math.round(
        (appointmentStats.completedThisMonth /
          appointmentStats.totalThisMonth) *
        100
      )
      : 0;

  return {
    isStatistiqueModalOpen,
    setIsStatistiqueModalOpen,
    top5Medicaments,
    top5Maladies,
    patientMetrics,
    recentPatient,
    recentUpdatedPatient,
    revenue,
    revenueData,
    patientDistribution,
    appointmentStats,
    todayAppointments,
    completionRate,
    totalAppointments: appointmentProfessional?.length || 0,
    totalConsultations: consultations?.length || 0,
    todayConsultations: todayConsultations,
    loading: {
      appointments: appointmentProfessional === null,
      patients: dataProfessionalPatients === null,
      facturations: listeFacturationProfessional === null,
    },
  };
};
