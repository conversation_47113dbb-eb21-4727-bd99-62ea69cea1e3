import { useEffect, useMemo, useState } from "react";
import useAvailableDirections from "@/presentation/pages/dash/employer/useAvailableDirections";
import { useEmployer } from "../../employer/useEmployer";
import { useAppSelector } from "../../redux";
import { Building2, FileText, UserPlus } from "lucide-react";
import { Employer } from "@/domain/models";
import { useAffectationMedicale } from "../../carnetDeSante/sousCarnet/useAffectationMedicale";
import { useMedicament } from "../../carnetDeSante/sousCarnet/useMedicament";
import { useEvenement } from "../../agenda/events";
import { useMedicalConsultation } from "../../consultationMedicale";

const useDashboardDass = () => {
  const id = useAppSelector((state) => state.authentification.userData?.id);
  const userId = useAppSelector((state) => state.authentification.user?.id);
  const availableDirections = useAvailableDirections();
  const { evenements, fetchDistinctEvenements } = useEvenement();
  const [isAddEmployeeFormOpen, setIsAddEmployeeFormOpen] =
    useState<boolean>(false);
  const { employers, getAll } = useEmployer();
  const { getByEmployer } = useAffectationMedicale();
  const { getMedicamentByEmployer } = useMedicament();
  const { consultations, getMedicalConsultationsByProfessionalId } =
    useMedicalConsultation();

  const [totalConsultationsThisMonth, setTotalConsultationsThisMonth] =
    useState(0);

  // États pour stocker les 5 maladies les plus répétitives
  const [top5Maladies, setTop5Maladies] = useState([]);
  const [top5Medicaments, setTop5Medicaments] = useState([]);
  const [isLoadingMaladies, setIsLoadingMaladies] = useState(false);
  const [isLoadingMedicament, setIsLoadingMedicament] = useState(false);
  const [isStatistiqueDassModal, setIsStatistiqueDassModal] = useState(false);

  const fetchAndFilterStats = async () => {
    setIsLoadingMaladies(true);
    try {
      const stats = await getByEmployer();

      if (Array.isArray(stats) && stats.length > 0) {
        const top5MaladiesResult = stats
          .filter((item) => item.maladie && item.repetitions)
          .sort((a, b) => b.repetitions - a.repetitions)
          .slice(0, 5);

        setTop5Maladies(top5MaladiesResult);

        return top5MaladiesResult;
      }

      setTop5Maladies([]);
      return [];
    } catch (error) {
      console.error("Erreur lors du filtrage des stats:", error);
      setTop5Maladies([]);
      return [];
    } finally {
      setIsLoadingMaladies(false);
    }
  };

  // Fetch and filter Stats medicament
  const fetchAndFilterStatsMedicament = async () => {
    setIsLoadingMedicament(true);
    try {
      const stats = await getMedicamentByEmployer();

      if (Array.isArray(stats) && stats.length > 0) {
        const top5MedicamentsResult = stats
          .filter((item) => item.nom && item.repetitions)
          .sort((a, b) => b.repetitions - a.repetitions)
          .slice(0, 5);

        setTop5Medicaments(top5MedicamentsResult);

        return top5MedicamentsResult;
      }
      setTop5Medicaments([]);
      return [];
    } catch (error) {
      console.error("Erreur lors du filtrage des stats:", error);
      setTop5Medicaments([]);
      return [];
    } finally {
      setIsLoadingMedicament(false);
    }
  };

  // Charger automatiquement les données au montage du composant
  useEffect(() => {
    fetchAndFilterStats();
    fetchAndFilterStatsMedicament();
  }, [getByEmployer, getMedicamentByEmployer]);

  const [stats, setStats] = useState<{
    totalEmployees: number;
    totalDirections: number;
    newEmployees: number;
    pendingRequests: number;
    trends: {
      totalEmployees: number;
      newEmployees: number;
    };
  }>({
    totalEmployees: 0,
    totalDirections: 0,
    newEmployees: 0,
    pendingRequests: 0,
    trends: {
      totalEmployees: 5.2, // Default value that will be replaced
      newEmployees: 12.5, // Default value that will be replaced
    },
  });

  // Track previous month's data to calculate trends
  const [prevMonthStats, setPrevMonthStats] = useState({
    totalEmployees: 0,
    newEmployees: 0,
  });

  useEffect(() => {
    getAll(id);
    // Simulate fetching previous month's data
    // In a real app, this would come from an API call or database
    setPrevMonthStats({
      totalEmployees: 95, // For demonstration - previous month had 95 employees
      newEmployees: 8, // For demonstration - previous month had 8 new employees
    });
  }, [getAll, id]);

  useEffect(() => {
    if (userId) {
      fetchDistinctEvenements(userId);
      getMedicalConsultationsByProfessionalId(userId);
    }
  }, [userId]);

  // Récupérer les activités récentes
  const [recentActivities, setRecentActivities] = useState<
    {
      id: number;
      type: string;
      message: string;
      time: string;
      icon: any;
    }[]
  >([]);

  useEffect(() => {
    if (employers) {
      const currentTotalEmployees = employers.length;

      // Calculer les nouvelles embauches ce mois
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Filtre pour les employés créés ce mois-ci
      const newEmployeesThisMonth = employers.filter((e) => {
        try {
          const creationDate = new Date(e.cree_a);
          return creationDate >= firstDayOfMonth && creationDate <= now;
        } catch (error) {
          return false;
        }
      });

      // Calcul des tendances en pourcentage
      const totalEmployeesTrend =
        prevMonthStats.totalEmployees > 0
          ? ((currentTotalEmployees - prevMonthStats.totalEmployees) /
              prevMonthStats.totalEmployees) *
            100
          : 5.2; // Valeur par défaut si les données précédentes ne sont pas disponibles

      const newEmployeesTrend =
        prevMonthStats.newEmployees > 0
          ? ((newEmployeesThisMonth.length - prevMonthStats.newEmployees) /
              prevMonthStats.newEmployees) *
            100
          : 12.5; // Valeur par défaut si les données précédentes ne sont pas disponibles

      setStats({
        totalEmployees: currentTotalEmployees,
        totalDirections: availableDirections.length,
        newEmployees: newEmployeesThisMonth.length,
        pendingRequests: 8,
        trends: {
          totalEmployees: Number(totalEmployeesTrend.toFixed(1)),
          newEmployees: Number(newEmployeesTrend.toFixed(1)),
        },
      });

      // Générer des activités récentes basées sur les données réelles
      generateRecentActivities(employers, availableDirections);
    }
  }, [employers, availableDirections, prevMonthStats]);

  // Fonction pour générer des activités récentes basées sur les données réelles
  const generateRecentActivities = (
    employers: Employer[],
    directions: any[]
  ) => {
    const activities = [];

    // Trouver les 3 employés les plus récents
    const recentEmployees = [...employers]
      .sort(
        (a, b) => new Date(b.cree_a).getTime() - new Date(a.cree_a).getTime()
      )
      .slice(0, 2);

    // Ajouter des activités pour chaque employé récent
    recentEmployees.forEach((employee, index) => {
      const creationDate = new Date(employee.cree_a);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - creationDate.getTime());
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60));

      let timeString;
      if (diffDays > 0) {
        timeString = `Il y a ${diffDays}j`;
      } else {
        timeString = `Il y a ${diffHours}h`;
      }

      activities.push({
        id: index + 1,
        type: "new_employee",
        message: `Nouvel employé ajouté : ${employee.nom} ${employee.prenom}`,
        time: timeString,
        icon: UserPlus,
      });
    });

    // Ajouter une activité pour la direction (simulée)
    if (directions && directions.length > 0) {
      const randomIndex = Math.floor(Math.random() * directions.length);
      activities.push({
        id: activities.length + 1,
        type: "direction_update",
        message: `Direction mise à jour : ${directions[randomIndex].nom}`,
        time: "Il y a 4h",
        icon: Building2,
      });
    }

    // Ajouter une activité de rapport (simulée)
    activities.push({
      id: activities.length + 1,
      type: "report",
      message: "Rapport mensuel généré",
      time: "Il y a 1j",
      icon: FileText,
    });

    setRecentActivities(activities);
  };

  // filtrer employe creer recentement
  const recentEmploye = useMemo(() => {
    // console.log(dataProfessionalPatients);

    if (!employers || employers.length === 0) {
      return [];
    }

    const now = new Date();
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(now.getDate() - 7);

    return (
      employers
        .filter((emp) => {
          try {
            if (!emp.cree_a) return false;
            const createdDate = new Date(emp.cree_a);
            return createdDate >= sevenDaysAgo && createdDate <= now;
          } catch (error) {
            return false;
          }
        })
        // Trier par date de création décroissante (les plus récents en premier)
        .sort((a, b) => {
          const dateA = a.cree_a ? new Date(a.cree_a).getTime() : 0;
          const dateB = b.cree_a ? new Date(b.cree_a).getTime() : 0;
          return dateB - dateA;
        })
    );
  }, [employers]);

  // filtrer employe mise a jour recement
  const recentEmployeUpdate = useMemo(() => {
    if (!employers || employers.length === 0) {
      return [];
    }

    const now = new Date();
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(now.getDate() - 7);

    return (
      employers
        .filter((emp) => {
          try {
            if (!emp.mis_a_jour_a) return false;
            const updatedDate = new Date(emp.mis_a_jour_a);
            return updatedDate >= sevenDaysAgo && updatedDate <= now;
          } catch (error) {
            return false;
          }
        })
        // Trier par date de mise à jour décroissante (les plus récents en premier)
        .sort((a, b) => {
          const dateA = a.mis_a_jour_a ? new Date(a.mis_a_jour_a).getTime() : 0;
          const dateB = b.mis_a_jour_a ? new Date(b.mis_a_jour_a).getTime() : 0;
          return dateB - dateA;
        })
    );
  }, [employers]);

  // Consultations ce mois
  useEffect(() => {
    if (consultations) {
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Filtre pour les consultations ce mois-ci
      const thisMonthConsultations = consultations.filter((consultation) => {
        try {
          const consultationDate = new Date(consultation.date_visite);
          return consultationDate >= firstDayOfMonth && consultationDate <= now;
        } catch (error) {
          return false;
        }
      });

      setTotalConsultationsThisMonth(thisMonthConsultations.length);
    }
  }, [consultations]);

  return {
    isAddEmployeeFormOpen,
    isStatistiqueDassModal,
    fetchAndFilterStats,
    setIsAddEmployeeFormOpen,
    upcomingEvents: evenements,
    stats,
    recentActivities,
    employers, // Employer[] type from the useEmployer hook
    availableDirections, // Direction[] type from the useAvailableDirections hook
    top5Maladies,
    top5Medicaments,
    isLoadingMaladies,
    isLoadingMedicament,
    recentEmploye,
    recentEmployeUpdate,
    setIsStatistiqueDassModal,
    totalConsultationsThisMonth,
  };
};

export default useDashboardDass;
