import { Commune, District } from "@/domain/models";
import SearchCommunesByNameUsecase from "@/domain/usecases/commune/SearchCommunesByNameUsecase";
import SearchDistrictsByNameUsecase from "@/domain/usecases/district/SearchDistrictsByNameUsecase";
import SearchCommunesByNameRepository from "@/infrastructure/repositories/commune/SearchCommunesByNameRepository";
import SearchDistrictsByNameRepository from "@/infrastructure/repositories/district/SearchDistrictsByNameRepository";
import { AppDispatch } from "@/store";
import { useState } from "react";
import { useDispatch } from "react-redux";

const useLocationFilter = () => {
  const [filteredDistricts, setFilteredDistricts] = useState<District[]>();
  const [filteredCommunes, setFilteredCommunes] = useState<Commune[]>();

  const searchDistrictsByNameRepository = new SearchDistrictsByNameRepository();
  const searchCommunesByNameRepository = new SearchCommunesByNameRepository();

  const searchDistrictsByNameUsecase = new SearchDistrictsByNameUsecase(
    searchDistrictsByNameRepository
  );
  const searchCommunesByNameUsecase = new SearchCommunesByNameUsecase(
    searchCommunesByNameRepository
  );

  const search = async (searchTerm: string) => {
    setFilteredDistricts(
      await searchDistrictsByNameUsecase.execute(searchTerm)
    );
    setFilteredCommunes(await searchCommunesByNameUsecase.execute(searchTerm));
  };

  return {
    filteredDistricts,
    filteredCommunes,
    search,
  };
};

export default useLocationFilter;
