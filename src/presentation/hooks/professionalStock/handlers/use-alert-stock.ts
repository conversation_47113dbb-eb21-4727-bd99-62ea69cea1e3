import { useEffect, useMemo, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../redux.ts";
import {
  LotWithStockData,
  LowStockCardDTO,
  StockNearPeremptionDateCardDTO,
} from "@/domain/DTOS/StockDTO.ts";
import dayjs from "dayjs";

const useAlertStock = () => {
  const {
    lots,
    stocksNearPeremption,
    lowStockAlert,
    expiredStocks: expiredLotsFromState,
  } = useAppSelector((state) => state.professionalStock);
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const REMAINING_DAYS = 30;

  const generateStockAlert = useMemo(() => {
    // Défauts si pas de lots
    if (!Array.isArray(lots) || lots.length === 0) {
      return {
        stockAlertsFiltered: [] as StockNearPeremptionDateCardDTO[],
        lowStockAlertsFiltered: [] as LowStockCardDTO[],
        expiredLots: [] as LotWithStockData[],
      };
    }

    const today = dayjs().format("YYYY-MM-DD");

    // 1) Lots déjà périmés (expiration < today)
    const expiredLots = lots.filter((lot) => {
      if (!lot?.date_expiration) return false;
      const exp = dayjs(lot.date_expiration).format("YYYY-MM-DD");
      return exp < today;
    });

    // 2) Lots valides (on retire seulement les lots strictement périmés)
    const validLots = lots.filter((lot) => {
      if (!lot?.date_expiration) return true; // pas de date -> considérer valide
      const exp = dayjs(lot.date_expiration).format("YYYY-MM-DD");
      return exp >= today;
    });

    // 3) Alertes de péremption (sur validLots)
    const stockAlerts: StockNearPeremptionDateCardDTO[] = validLots.map(
      (lot) => {
        const expirationDate = lot.date_expiration
          ? dayjs(lot.date_expiration).format("YYYY-MM-DD")
          : null;
        const daysUntilExpiration =
          expirationDate !== null
            ? Math.ceil(
                (new Date(expirationDate).getTime() -
                  new Date(today).getTime()) /
                  (1000 * 60 * 60 * 24)
              )
            : Infinity; // si pas de date, ne pas alerter par péremption

        return {
          id: lot?.id,
          produit: lot?.stocks?.nom,
          quantite: lot?.quantite,
          date_expiration: dayjs(lot?.date_expiration).format("YYYY-MM-DD"),
          numero_lot: lot?.numero_lot,
          jours_restants: daysUntilExpiration,
          priorite: "haute",
        } as StockNearPeremptionDateCardDTO;
      }
    );

    const stockAlertsFiltered = stockAlerts.filter(
      (s) => s.jours_restants <= REMAINING_DAYS && s.jours_restants > 0
    );

    // 4) Combiner les lots par produit (sur validLots)
    const combinedMap = new Map<string | number, LotWithStockData>();
    for (const lot of validLots as LotWithStockData[]) {
      const stockId = lot?.stocks?.id ?? `lot-${lot.id}`; // fallback si pas de stocks.id
      const existing = combinedMap.get(stockId);
      if (existing) {
        existing.quantite = (existing.quantite ?? 0) + (lot.quantite ?? 0);
        // on peut aussi mettre à jour la date_expiration si besoin (ex: la plus proche)
        // existing.date_expiration = chooseOne(existing.date_expiration, lot.date_expiration)
      } else {
        // clonage superficiel pour éviter de muter l'original
        combinedMap.set(stockId, { ...lot });
      }
    }
    const combinedStockAlerts = Array.from(combinedMap.values());

    // 5) Alertes de stock faible (sur combinedStockAlerts)
    const lowStockAlerts: LowStockCardDTO[] = combinedStockAlerts.map((lot) => {
      return {
        id: lot.id,
        produit: lot?.stocks?.nom,
        stock_actuel: lot?.quantite ?? 0,
        seuil_alerte: lot?.stocks?.seuil_alerte ?? 0,
        unite: lot?.stocks?.unite,
        priorite: "haute",
      } as LowStockCardDTO;
    });

    const lowStockAlertsFiltered = lowStockAlerts.filter(
      (l) => l.stock_actuel <= l.seuil_alerte
    );

    return {
      stockAlertsFiltered,
      lowStockAlertsFiltered,
      expiredLots,
    };
  }, [lots]); // dépend uniquement des lots

  useEffect(() => {
    const loadAlerts = async () => {
      setIsLoading(true);
      try {
        const alerts = generateStockAlert ?? {
          stockAlertsFiltered: [],
          lowStockAlertsFiltered: [],
          expiredLots: [],
        };

        dispatch({
          type: "professionalStock/setStocksNearPeremption",
          payload: alerts.stockAlertsFiltered,
        });
        dispatch({
          type: "professionalStock/setLowStockAlert",
          payload: alerts.lowStockAlertsFiltered,
        });
        dispatch({
          type: "professionalStock/setExpiredLots",
          payload: alerts.expiredLots,
        });
      } catch (error) {
        console.error("Erreur lors de la génération des alertes:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAlerts();
  }, [generateStockAlert, dispatch]);

  return {
    generateStockAlert,
    isLoadingAlertes: isLoading,
    stockAlerts: stocksNearPeremption,
    lowStockAlerts: lowStockAlert,
    expiredLots: expiredLotsFromState,
  };
};

export default useAlertStock;
