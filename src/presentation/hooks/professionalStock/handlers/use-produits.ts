import { useCallback, useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../redux.ts";
import { Stocks } from "@/domain/models/Stocks.ts";
import {
  createProduit,
  deleteProduit,
  getCategories as getCategoriesAction,
  getProduits,
  updateProduit,
} from "@/application/slices/professionnal/professionalStockSlice.ts";
import { useToast } from "../../use-toast.ts";
import dayjs from "dayjs";

export interface ProduitWithQuantite extends Stocks {
  quantite: number;
  derniereEntree: string;
}

const useProduits = (utilisateurId?: number) => {
  const dispatch = useAppDispatch();
  const toast = useToast();
  const {
    produits: produitList,
    lots,
    entrees,
    categories,
  } = useAppSelector((state) => state.professionalStock);
  const [isLoadingProduit, setIsLoadingProduit] = useState(false);
  const [detailedProduit, setDetailedProduit] = useState<ProduitWithQuantite[]>(
    []
  );

  useEffect(() => {
    if (!produitList || produitList.length === 0 || !entrees || !lots) return;
    const today = dayjs();

    const data: ProduitWithQuantite[] = produitList.map((produit) => {
      const matchingEntree = entrees.filter(
        (entree) => entree.stock_id === produit.id
      );

      const quantite = lots
        .filter((lot) =>
          matchingEntree.find(
            (e) =>
              e.id === lot.entree_id &&
              lot.quantite > 0 &&
              dayjs(lot.date_expiration).isAfter(today)
          )
        )
        .reduce((acc, lot) => acc + lot.quantite, 0);
      const derniereEntree = matchingEntree.sort((a, b) =>
        dayjs(b.date_entree).diff(a.date_entree)
      )[0]?.date_entree;

      const out: ProduitWithQuantite = {
        ...produit,
        quantite,
        derniereEntree,
      };

      return out;
    });
    setDetailedProduit(data);
  }, [produitList, entrees, lots]);

  const handleAddProduit = useCallback(
    async (data: Omit<Stocks, "id">) => {
      if (!utilisateurId) return false;
      try {
        setIsLoadingProduit(true);
        const createdProduit = await dispatch(
          createProduit({ ...data, utilisateur_id: utilisateurId })
        ).unwrap();

        toast.success("Produit ajouté avec succès");
        return createdProduit !== null;
      } catch (error) {
        console.error("Erreur lors de l'ajout du produit:", error);
        return false;
      } finally {
        setIsLoadingProduit(false);
      }
    },
    [dispatch, utilisateurId, toast]
  );

  const handleUpdateProduit = useCallback(
    async (id: number, data: Omit<Stocks, "id">) => {
      if (!utilisateurId) return false;
      try {
        setIsLoadingProduit(true);
        const updatedProduit = await dispatch(
          updateProduit({
            id,
            produit: { ...data, utilisateur_id: utilisateurId },
          })
        ).unwrap();

        toast.success("Produit mis à jour avec succès");
        return updatedProduit !== null;
      } catch (error) {
        console.error("Erreur lors de la mise à jour du produit:", error);
        return false;
      } finally {
        setIsLoadingProduit(false);
      }
    },
    [dispatch, utilisateurId, toast]
  );

  const handleDeleteProduit = useCallback(
    async (id: number) => {
      try {
        setIsLoadingProduit(true);
        await dispatch(deleteProduit(id)).unwrap();
        toast.success("Produit supprimé avec succès");
        return true;
      } catch (error) {
        console.error("Erreur lors de la suppression du produit:", error);
        return false;
      } finally {
        setIsLoadingProduit(false);
      }
    },
    [dispatch, toast]
  );

  const getProfessionalProduits = useCallback(async () => {
    if (!utilisateurId) return;
    try {
      setIsLoadingProduit(true);
      await dispatch(getProduits(utilisateurId)).unwrap();
    } catch (error) {
      console.error("Erreur lors de la récupération des produits:", error);
    } finally {
      setIsLoadingProduit(false);
    }
  }, [dispatch, utilisateurId]);

  const getCategories = useCallback(async () => {
    try {
      await dispatch(getCategoriesAction()).unwrap();
    } catch (error) {
      console.error("Erreur lors de la récupération des categories:", error);
    }
  }, [dispatch]);

  return {
    handleAddProduit,
    handleUpdateProduit,
    handleDeleteProduit,
    getProfessionalProduits,
    getCategories,
    produitList: detailedProduit,
    isLoadingProduit,
    categories,
  };
};

export default useProduits;
