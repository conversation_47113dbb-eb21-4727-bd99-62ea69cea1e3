import {
  createEntreeStock,
  getEntrees,
} from "@/application/slices/professionnal/professionalStockSlice.ts";
import { EntreeStockFormData } from "@/shared/schemas/EntreeStockFormShema.ts";
import { useCallback, useState } from "react";
import { useToast } from "../../use-toast.ts";
import { useAppDispatch, useAppSelector } from "../../redux.ts";
import { CreateStockDTO } from "@/domain/DTOS/StockDTO.ts";
import dayjs from "dayjs";

const useEntreeStock = (utilisateurId?: number) => {
  const dispatch = useAppDispatch();
  const toast = useToast();
  const { entrees } = useAppSelector((state) => state.professionalStock);
  const [isLoadingEntree, setIsLoadingEntree] = useState(false);

  const handleAddEntree = useCallback(
    async (data: EntreeStockFormData) => {
      if (!utilisateurId) return false;
      try {
        setIsLoadingEntree(true);

        // Transformation des données
        const formatedData: CreateStockDTO = {
          stock_id: data.stock_id,
          fournisseur_id: data.fournisseur_id,
          quantite: data.quantite,
          prix_unitaire: data.prix_unitaire,
          date_entree: dayjs().format("YYYY-MM-DD"),
          lot: {
            numero_lot: data.numero_lot,
            quantite: data.quantite,
            date_expiration: data.date_expiration,
          },
        };

        const createdEntree = await dispatch(
          createEntreeStock(formatedData)
        ).unwrap();

        toast.success("Entrée de stock ajoutée avec succès");
        return createdEntree !== null;
      } catch (error) {
        console.error("Erreur lors de l'ajout de l'entrée de stock:", error);
        return false;
      } finally {
        setIsLoadingEntree(false);
      }
    },
    [dispatch]
  );

  const getProfessionalEntrees = useCallback(async () => {
    if (!utilisateurId) return;
    try {
      setIsLoadingEntree(true);
      await dispatch(getEntrees(utilisateurId)).unwrap();
    } catch (error) {
      console.error(
        "Erreur lors de la récupération des entrées de stock:",
        error
      );
    } finally {
      setIsLoadingEntree(false);
    }
  }, [dispatch]);

  return {
    handleAddEntree,
    getProfessionalEntrees,
    entreesStocks: entrees,
    isLoadingEntree,
  };
};

export default useEntreeStock;
