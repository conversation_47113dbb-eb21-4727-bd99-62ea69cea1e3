import { useCallback, useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../redux.ts";
import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import {
  createFournisseur,
  deleteFournisseur,
  getFournisseurs,
  updateFournisseur,
} from "@/application/slices/professionnal/professionalStockSlice.ts";
import { useToast } from "../../use-toast.ts";

const useFournisseurs = (utilisateurId?: number) => {
  const dispatch = useAppDispatch();
  const toast = useToast();
  const { fournisseurs: fournisseurList } = useAppSelector(
    (state) => state.professionalStock
  );
  const [isLoadingFournisseur, setIsLoadingFournisseur] = useState(false);

  const handleAddFournisseur = useCallback(
    async (data: Omit<Fournisseurs, "id">) => {
      if (!utilisateurId) return false;
      try {
        setIsLoadingFournisseur(true);
        const createdFournisseur = await dispatch(
          createFournisseur({ ...data, utilisateur_id: utilisateurId })
        ).unwrap();

        toast.success("Fournisseur ajouté avec succès");
        return createdFournisseur !== null;
      } catch (error) {
        console.error("Erreur lors de l'ajout du fournisseur:", error);
        return false;
      } finally {
        setIsLoadingFournisseur(false);
      }
    },
    [dispatch]
  );

  const handleUpdateFournisseur = useCallback(
    async (id: number, data: Omit<Fournisseurs, "id">) => {
      if (!utilisateurId) return false;
      try {
        setIsLoadingFournisseur(true);
        const updatedFournisseur = await dispatch(
          updateFournisseur({ id, fournisseur: data })
        ).unwrap();

        toast.success("Fournisseur mis à jour avec succès");
        return updatedFournisseur !== null;
      } catch (error) {
        console.error("Erreur lors de la mise à jour du fournisseur:", error);
        return false;
      } finally {
        setIsLoadingFournisseur(false);
      }
    },
    [dispatch]
  );

  const handleDeleteFournisseur = useCallback(async (id: number) => {
    try {
      setIsLoadingFournisseur(true);
      await dispatch(deleteFournisseur(id)).unwrap();
      toast.success("Fournisseur supprimé avec succès");
      return true;
    } catch (error) {
      console.error("Erreur lors de la suppression du fournisseur:", error);
      return false;
    } finally {
      setIsLoadingFournisseur(false);
    }
  }, []);

  const getProfessionalFournisseurs = useCallback(async () => {
    if (!utilisateurId) return;
    try {
      setIsLoadingFournisseur(true);
      await dispatch(getFournisseurs(utilisateurId)).unwrap();
    } catch (error) {
      console.error("Erreur lors de la récupération des fournisseurs:", error);
    } finally {
      setIsLoadingFournisseur(false);
    }
  }, [dispatch]);

  return {
    handleAddFournisseur,
    handleUpdateFournisseur,
    handleDeleteFournisseur,
    getProfessionalFournisseurs,
    fournisseurList,
    isLoadingFournisseur,
  };
};

export default useFournisseurs;
