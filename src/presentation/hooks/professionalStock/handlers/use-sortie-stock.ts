import { useCallback, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../redux.ts";
import { useToast } from "../../use-toast.ts";
import { SortieStockFormData } from "@/shared/schemas/SortieStockFormShema.ts";
import { SortieStockDTO } from "@/domain/DTOS/StockDTO.ts";
import {
  createSortieStock,
  getSorties,
} from "@/application/slices/professionnal/professionalStockSlice.ts";

const useSortieStock = (utilisateurId?: number) => {
  const dispatch = useAppDispatch();
  const { sorties } = useAppSelector((state) => state.professionalStock);
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();

  const handleAddSortie = useCallback(async (data: SortieStockFormData) => {
    if (!utilisateurId) return false;
    try {
      setIsLoading(true);

      // Transformation des données
      const formatedData: SortieStockDTO = {
        stock_id: data.stock_id,
        quantite: data.quantite,
        type_sortie: data.type_sortie,
        destinataire: data.destinataire,
        date_sortie: new Date().toISOString(),
      };

      const createdSortie = await dispatch(
        createSortieStock(formatedData)
      ).unwrap();

      toast.success("Sortie de stock ajoutée avec succès");
      return createdSortie?.sorties !== null;
    } catch (error) {
      console.error("Erreur lors de l'ajout de la sortie de stock:", error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getProfessionalSorties = useCallback(async () => {
    if (!utilisateurId) return;
    try {
      setIsLoading(true);
      await dispatch(getSorties(utilisateurId)).unwrap();
    } catch (error) {
      console.error(
        "Erreur lors de la récupération des sorties de stock:",
        error
      );
    } finally {
      setIsLoading(false);
    }
  }, [dispatch]);

  return {
    handleAddSortie,
    getProfessionalSorties,
    isLoadingSortie: isLoading,
    sortieList: sorties,
  };
};

export default useSortieStock;
