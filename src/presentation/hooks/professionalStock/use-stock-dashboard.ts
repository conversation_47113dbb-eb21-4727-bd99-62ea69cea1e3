import { useAppDispatch, useAppSelector } from "../redux.ts";
import { useCallback, useState } from "react";
import {
  EntreeStockRowDTO,
  SortieStockRowDTO,
  StockStatistics,
} from "@/domain/DTOS/StockDTO.ts";
import dayjs from "dayjs";

const useStockDashboard = (utilisateurId?: number) => {
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const {
    dashboardData,
    lowStockAlert,
    expiredStocks,
    produits,
    entrees,
    sorties,
    fournisseurs,
  } = useAppSelector((state) => state?.professionalStock);

  const generateDashboardData = useCallback(() => {
    if (!utilisateurId) return;

    // Vérifier que toutes les données nécessaires sont chargées
    if (!entrees || !sorties || !produits || !fournisseurs) {
      return;
    }

    const totalCost = entrees.reduce((total, entree) => {
      return total + entree.prix_unitaire * entree.quantite;
    }, 0);

    // Recuperer les mouvements recents: les entrees du jours
    const recentEntrees = entrees
      .filter((entree) => {
        const entreeDate = dayjs(entree.date_entree);
        const today = dayjs();
        return (
          entreeDate.date() === today.date() &&
          entreeDate.month() === today.month() &&
          entreeDate.year() === today.year()
        );
      })
      .slice(0, 5);

    const recentSorties = sorties
      .filter((sortie) => {
        const sortieDate = new Date(sortie.date_sortie);
        const today = new Date();
        return (
          sortieDate.getDate() === today.getDate() &&
          sortieDate.getMonth() === today.getMonth() &&
          sortieDate.getFullYear() === today.getFullYear()
        );
      })
      .slice(0, 5);

    const recentMouvements = [...recentEntrees, ...recentSorties];

    const formatedMouvements = recentMouvements.map(
      (mouvement: EntreeStockRowDTO | SortieStockRowDTO, index: number) => {
        if (typeof (mouvement as EntreeStockRowDTO).date_entree === "string") {
          return {
            id: index,
            name: mouvement.produit,
            quantite: mouvement.quantite,
            date: (mouvement as EntreeStockRowDTO).date_entree,
            type: "Entree",
          };
        } else {
          return {
            id: index,
            name: mouvement.produit,
            quantite: mouvement.quantite,
            date: (mouvement as SortieStockRowDTO).date_sortie,
            type: "Sortie",
          };
        }
      }
    );

    const out: StockStatistics = {
      totalProduits: produits.length,
      totalFournisseurs: fournisseurs.length,
      alertesPeremption: expiredStocks?.length || 0,
      alertesStock: lowStockAlert || [],
      valeurTotaleStock: totalCost,
      mouvementsRecents: formatedMouvements,
    };

    dispatch({ type: "professionalStock/setDashboardData", payload: out });

    return out;
  }, [
    dispatch,
    utilisateurId,
    entrees,
    sorties,
    produits,
    fournisseurs,
    expiredStocks,
    lowStockAlert,
  ]);

  return {
    isLoadingStockData: isLoading,
    generateDashboardData,
    dashboardData,
  };
};

export default useStockDashboard;
