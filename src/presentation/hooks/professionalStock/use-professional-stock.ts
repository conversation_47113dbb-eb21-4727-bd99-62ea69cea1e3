import { useCallback, useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../redux.ts";
import useProfessionalStockHandlers from "./use-professional-stock-handlers.ts";
import { getLots } from "@/application/slices/professionnal/professionalStockSlice.ts";

const useProfessionalStock = () => {
  const authentification = useAppSelector((state) => state?.authentification);

  const dispatch = useAppDispatch();
  const { lots, entrees, sorties, fournisseurs } = useAppSelector(
    (state) => state?.professionalStock
  );
  const handlers = useProfessionalStockHandlers(authentification?.user?.id);

  const loadDatas = useCallback(async () => {
    if (!handlers.isLoadingStockData) {
      await handlers.getProfessionalProduits();
      await handlers.getProfessionalFournisseurs();
      await handlers.getProfessionalEntrees();
      await handlers.getProfessionalSorties();
      await handlers.getCategories();
    }
  }, [authentification, dispatch]);

  useEffect(() => {
    // Ne générer les données du dashboard que si toutes les données sont chargées
    if (entrees && sorties && fournisseurs && handlers.produitList) {
      handlers.generateDashboardData();
    }
  }, [entrees, sorties, fournisseurs, handlers.produitList, handlers.generateDashboardData]);

  const getLotsData = useCallback(async () => {
    const userId = authentification?.user?.id;

    if (userId) {
      await dispatch(getLots(userId));
    }
  }, [authentification, dispatch]);

  /**
   * Refaire une recuperation des lots a chaque fois
   * qu'il y a une nouvelle entree ou sortie pour que
   * les quantites soient a jour
   */

  useEffect(() => {
    getLotsData();
  }, [entrees, sorties, getLotsData]);

  // Recuperation des donnees global
  useEffect(() => {
    loadDatas();
  }, [dispatch, loadDatas]);

  return {
    handlers,
    lots,
  };
};

export default useProfessionalStock;
