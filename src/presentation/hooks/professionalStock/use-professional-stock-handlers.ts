import { useEffect } from "react";
import useEntreeStock from "./handlers/use-entree-stock.ts";
import useFournisseurs from "./handlers/use-fournisseurs.ts";
import useProduits from "./handlers/use-produits.ts";
import useSortieStock from "./handlers/use-sortie-stock.ts";
import useStockDashboard from "./use-stock-dashboard.ts";
import { useToast } from "../use-toast.ts";
import { useAppDispatch, useAppSelector } from "../redux.ts";
import { setError } from "@/application/slices/professionnal/professionalStockSlice.ts";
import useAlertStock from "./handlers/use-alert-stock.ts";

const useProfessionalStockHandlers = (utilisateurId?: number) => {
  const { error } = useAppSelector((state) => state.professionalStock);
  const dispatch = useAppDispatch();

  const stockDashboardHandlers = useStockDashboard(utilisateurId);
  const fournisseursHandlers = useFournisseurs(utilisateurId);
  const produitsHandlers = useProduits(utilisateurId);
  const entreeStockHandlers = useEntreeStock(utilisateurId);
  const sortieStockHandlers = useSortieStock(utilisateurId);
  const alertStockHandlers = useAlertStock();
  const toast = useToast();

  useEffect(() => {
    if (error) {
      toast.error(error);
      dispatch(setError(null));
    }
  }, [error]);

  return {
    ...stockDashboardHandlers,
    ...fournisseursHandlers,
    ...produitsHandlers,
    ...entreeStockHandlers,
    ...sortieStockHandlers,
    ...alertStockHandlers,
  };
};

export default useProfessionalStockHandlers;
