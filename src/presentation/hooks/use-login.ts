import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import {
  loginUser,
  logoutUser,
  setError,
} from "@/application/slices/auth/authSlice";
import { useCallback, useEffect, useState } from "react";
import { SuccessMessages } from "@/shared/constants/SuccessMessages";
import { useNavigate } from "react-router-dom";
import {
  AdminRoutesNavigations,
  PublicRoutesNavigation,
  ProfessionalRoutesNavigations,
  PatientRoutesNavigation,
  DashRoutesNavigation,
} from "@/shared/constants/AppRoutesNavigation";
import { useToast } from "./use-toast";
import { Utilisateur } from "@/domain/models";
import { LoginUserDTO } from "@/domain/DTOS/LoginUserDTO";
import { utilisateurs_role_enum } from "@/domain/models/enums/utilisateurRole.ts";

const useLogin = () => {
  const toast = useToast();
  const { error, loading } = useSelector(
    (state: RootState) => state.authentification
  );
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [email, setEmail] = useState<string>("");

  useEffect(() => {
    if (error) {
      if (error.includes("Email not confirmed")) {
        navigate(
          `/${PublicRoutesNavigation.EMAIL_CONFIRMATION_PAGE.split("/:email")[0]}/${email}`
        );
      }
      if (error.includes("Aucune utilisateur connectee")) return;
      toast.error(error);
      dispatch(setError(null));
    }
  }, [error, dispatch, navigate, toast]);

  const redirectAuthenticatedUser = (userToRedirect: Utilisateur) => {
    if (!userToRedirect) {
      console.error("Aucun utilisateur a rediriger");
      return;
    }

    if (!userToRedirect.role) {
      toast.error("Erreur: rôle utilisateur non défini");
      return;
    }

    switch (userToRedirect.role) {
      case "patient":
        if (
          location.pathname !== `/${PublicRoutesNavigation.CONSULTATION_PAGE}`
        ) {
          navigate(`/${PatientRoutesNavigation.DASHBOARD}`);
        }
        break;
      case "professionnel":
        navigate(`/${ProfessionalRoutesNavigations.DASHBOARD}`);
        break;
      case "admin":
        navigate(`/${AdminRoutesNavigations.DASHBOARD}`);
        break;
      case "dash":
        navigate(`/${DashRoutesNavigation.DASHBOARD}`);
        break;
      default:
        console.error("Role de l'utilisateur inconnue: ", userToRedirect.role);
        toast.error("Erreur: rôle utilisateur non reconnu");
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const data = {
        email: email.trim(),
        password: password.trim(),
      };

      setEmail(data.email);

      const resultAction = await dispatch(
        loginUser({ email: data.email, password: data.password })
      );
      const loginData = resultAction.payload as LoginUserDTO;

      if (!loginData || !loginData.success) {
        return;
      }

      if (!loginData.user) {
        console.error("Aucune donnee utilisateur trouvee dans la reponse");
        toast.error(
          "Erreur lors de la connexion: données utilisateur manquantes"
        );
        return;
      }

      const loggedInUser = loginData.user;

      if (loginData.user.role === utilisateurs_role_enum.DASH) {
        toast.success(SuccessMessages.LOGIN_SUCCESS);
        setTimeout(() => {
          toast.success(SuccessMessages.DASS_LOGIN_SUCCESS);
        }, 4000);
      } else {
        toast.success(SuccessMessages.LOGIN_SUCCESS);
      }
      redirectAuthenticatedUser(loggedInUser);
    } catch (error) {
      console.log(error);
    }
  };

  const logout = async () => {
    try {
      await dispatch(logoutUser());

      // Réinitialiser le mode sombre en mode clair lors de la déconnexion
      document.documentElement.classList.remove("dark");

      // Optionnel : sauvegarder la préférence en localStorage
      localStorage.setItem("theme", "light");

      navigate(PublicRoutesNavigation.MAIN_PAGE);
    } catch (error) {
      console.log(error);
    }
  };

  return {
    login,
    loading,
    error,
    logout,
  };
};

export default useLogin;
