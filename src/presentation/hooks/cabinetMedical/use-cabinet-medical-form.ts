import {
  professionnels_types_consultation_enum,
  sexe_enum,
} from "@/domain/models/enums";
import { useCabinetMedicalStateContext } from "@/presentation/contexts/useContext";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { useToast } from "../use-toast";
import { cabinetMedicalState } from "@/presentation/contexts/types";
import {
  FORM_CONFIG,
  PERSISTENCE_CONFIG,
  UPLOAD_CONFIG,
} from "@/shared/constants/cabinetMedicalConfig";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";
import { RegisterProfessionalUsecase } from "@/domain/usecases/user/Register/RegisterProfessionalUseCase";
import CreateUserRepository from "@/infrastructure/repositories/user/CreateUserRepository";
import { PasswordService } from "@/domain/services/PasswordService";
import {
  CreateAuthentificationUserUsecase,
  CreateUserUsecase,
  DeleteUserUsecase,
  GetUserByEmailUsecase,
} from "@/domain/usecases/user";
import GetUserByEmailRepository from "@/infrastructure/repositories/user/GetUserByEmailRepository";
import { ProfessionalRepository } from "@/infrastructure/repositories";
import RegisterCabinetMedical from "@/domain/usecases/professional/RegisterCabinetMedical/RegisterCabinetMedical";
import CreateAuthentificationUserRepository from "@/infrastructure/repositories/user/CreateAuthentificationUserRepository";
import { SupabaseUploader } from "@/domain/services/SupabaseUploader";
import { FilenameGenerator } from "@/domain/services/FilenameGenerator";
import AddPhotosUsecase from "@/domain/usecases/photos/AddPhotosUsecase";
import AddPhotoRepository from "@/infrastructure/repositories/photos/AddPhotosRepository";
import AddProfessionalSpecilitiesRepository from "@/infrastructure/repositories/professionalSpecialities/AddProfessionalSpecilitiesRepository";
import AddProfessionalSpecilitiesUsecase from "@/domain/usecases/professionalSpecialities/AddProfessionalSpecialitiesUsecase";
import { CreateOrdreAppartenanceUsecase } from "@/domain/usecases/ordreAppartenance";
import CreateOrdreAppartenanceRepository from "@/infrastructure/repositories/ordreAppartenance/CreateOrdreAppartenanceRepository";
import { CreateContactUsecase } from "@/domain/usecases/contact";
import { CreateContactRepository } from "@/infrastructure/repositories/contact";
import { AddLanguageUsecase } from "@/domain/usecases/professionalLanguage";
import { AddLanguageRepository } from "@/infrastructure/repositories/professionalLanguage";
import { CreateProfessionalExperienceUsecase } from "@/domain/usecases/professionalExperience";
import { CreateProfessionalExperienceRepository } from "@/infrastructure/repositories/professionalExperience";
import { CreateProfessionalPublicationUsecase } from "@/domain/usecases/professionalPublication";
import { CreateProfessionalPublicationRepository } from "@/infrastructure/repositories/professionalPublication";
import CreateProfessionalMotClesUsecase from "@/domain/usecases/motCles/CreateProfessionalMotClesUsecase";
import CreateProfessionalMotClesRepository from "@/infrastructure/repositories/motCles/CreateProfessionalMotClesRepository";
import { EmailValidator } from "@/domain/services/EmailValidator";
import CreateProfessionalInsuranceUsecase from "@/domain/usecases/insurance/CreateProfessionalInsuranceUsecase";
import { CreateProfessionalInsuranceRepository } from "@/infrastructure/repositories/insurance";
import DeleteUserRepository from "@/infrastructure/repositories/user/DeleteUserRepository";
import { CreateProfessionalDiplomaRepository } from "@/infrastructure/repositories/professionalDiploma";
import { CreateProfessionalDiplomaUsecase } from "@/domain/usecases/professionalDiploma";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import CreateProfessionalEtablishmentRepository from "@/infrastructure/repositories/EtablissementProfessionnel/CreateProfessionalEtablishmentRepository";
import MarkInvitationAsUsedRepository from "@/infrastructure/repositories/professionalInvitation/MarkInvitationAsUsedRepository";
import { MarkInvitationAsUsedUsecase } from "@/domain/usecases/professionalInvitation";
import CreateProfessionalEtablishmentUsecase from "@/domain/usecases/professional/etablissementProfessionnel/CreateProfessionalEtablishmentUsecase";

export interface PaymentMethod {
  id: string;
  name: string;
}

export interface Diplome {
  id: string | number;
  titre: string;
  etablissement: string;
  annee: string;
  description?: string;
}

export interface Experience {
  id: string | number;
  poste: string;
  etablissement: string;
  date_debut: string;
  date_fin?: string;
  description?: string;
  est_actuel?: boolean;
}

export interface Publication {
  id: string | number;
  titre: string;
  annee: string;
  description?: string;
  lien?: string;
}

export interface Langue {
  id: string | number;
  nom_langue: string;
}

const useCabinetMedicalForm = (professionalInvitationId: number) => {
  const toast = useToast();
  const navigate = useNavigate();

  // Accéder au contexte pour l'état de l'UI
  const { state, dispatch } = useCabinetMedicalStateContext();

  // Dependances du usecase d'inscription de professionnel
  // Service
  const passwordService = new PasswordService();
  const filenameGenerator = new FilenameGenerator();
  const supabaseUploader = new SupabaseUploader(filenameGenerator);
  const emailValidator = new EmailValidator();

  // Reposotory
  const createUserRepository = new CreateUserRepository();
  const getUserByEmailRepository = new GetUserByEmailRepository();
  const professionalRepository = new ProfessionalRepository();
  const createAuthenticationUserRepository =
    new CreateAuthentificationUserRepository();
  const addPhotosRepository = new AddPhotoRepository();
  const addProfessionnalSpecialitiesRepository =
    new AddProfessionalSpecilitiesRepository();
  const createOrdreAppartenanceRepository =
    new CreateOrdreAppartenanceRepository();
  const createContactRepository = new CreateContactRepository();
  const addLanguageRepository = new AddLanguageRepository();
  const createProfessionnalExperienciesRepository =
    new CreateProfessionalExperienceRepository();
  const createProfessionalPublicationRepository =
    new CreateProfessionalPublicationRepository();
  const createProfessionalMotClesRepository =
    new CreateProfessionalMotClesRepository();
  const createProfessionalInsurancesRepository =
    new CreateProfessionalInsuranceRepository();
  const deleteUserRepository = new DeleteUserRepository();
  const createProfessionalDiplomaRepository =
    new CreateProfessionalDiplomaRepository();
  const createEtablissementProfessionnelRepository =
    new CreateProfessionalEtablishmentRepository();
  const markProfessionalInvitationAsUsedRepository =
    new MarkInvitationAsUsedRepository();

  // Usecase
  const createUserUsecase = new CreateUserUsecase(
    createUserRepository,
    passwordService,
  );
  const getUserByEmailUsecase = new GetUserByEmailUsecase(
    getUserByEmailRepository,
  );
  const createAuthenticationUserUsecase = new CreateAuthentificationUserUsecase(
    createAuthenticationUserRepository,
  );
  const addPhotosUsecase = new AddPhotosUsecase(
    addPhotosRepository,
    supabaseUploader,
  );
  const addProfessionnalSpecialitiesUsecase =
    new AddProfessionalSpecilitiesUsecase(
      addProfessionnalSpecialitiesRepository,
    );
  const createOrdreAppartenanceUsecase = new CreateOrdreAppartenanceUsecase(
    createOrdreAppartenanceRepository,
  );
  const createContactUsecase = new CreateContactUsecase(
    createContactRepository,
  );
  const addLanguageUsecase = new AddLanguageUsecase(addLanguageRepository);
  const createProfessionnalExperienciesUsecase =
    new CreateProfessionalExperienceUsecase(
      createProfessionnalExperienciesRepository,
    );
  const createProfessionalPublicationUsecase =
    new CreateProfessionalPublicationUsecase(
      createProfessionalPublicationRepository,
    );
  const createProfessionalMotClesUsecase = new CreateProfessionalMotClesUsecase(
    createProfessionalMotClesRepository,
  );
  const createProfessionalInsurancesUsecase =
    new CreateProfessionalInsuranceUsecase(
      createProfessionalInsurancesRepository,
    );
  const deleteUserUsecase = new DeleteUserUsecase(deleteUserRepository);
  const createProfessionalDiplomasUsecase =
    new CreateProfessionalDiplomaUsecase(createProfessionalDiplomaRepository);
  const createEtablissementProfessionnelUsecase =
    new CreateProfessionalEtablishmentUsecase(
      createEtablissementProfessionnelRepository,
    );

  const registerProfessionalUsecase = new RegisterProfessionalUsecase(
    createUserUsecase,
    professionalRepository,
    getUserByEmailUsecase,
    createAuthenticationUserUsecase,
    emailValidator,
    supabaseUploader,
    addPhotosUsecase,
    addProfessionnalSpecialitiesUsecase,
    createProfessionalInsurancesUsecase,
    createOrdreAppartenanceUsecase,
    createContactUsecase,
    addLanguageUsecase,
    createProfessionnalExperienciesUsecase,
    createProfessionalPublicationUsecase,
    createProfessionalMotClesUsecase,
    createProfessionalDiplomasUsecase,
    deleteUserUsecase,
    createEtablissementProfessionnelUsecase,
  );

  const markProfessionalInvitationAsUsedUsecase =
    new MarkInvitationAsUsedUsecase(markProfessionalInvitationAsUsedRepository);

  const registerCabinetMedicalUsecase = new RegisterCabinetMedical(
    registerProfessionalUsecase,
    markProfessionalInvitationAsUsedUsecase,
  );

  // Récupérer les données sauvegardées du localStorage - défini avant son utilisation
  const getSavedFormData =
    useCallback((): Partial<CabinetMedicalFormDTO> | null => {
      try {
        const savedData = localStorage.getItem(
          PERSISTENCE_CONFIG.LOCAL_STORAGE_KEY,
        );
        if (savedData) {
          return JSON.parse(savedData);
        }
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des données sauvegardées:",
          error,
        );
      }
      return null;
    }, []);

  // Récupérer les données sauvegardées immédiatement pour l'initialisation
  const initialSavedData = useMemo(
    () => getSavedFormData(),
    [getSavedFormData],
  );

  // État pour suivre les données sauvegardées
  const [savedData, setSavedData] =
    useState<Partial<CabinetMedicalFormDTO> | null>(initialSavedData);

  // Initialiser le formulaire avec les données sauvegardées ou des valeurs par défaut
  const methods = useForm<CabinetMedicalFormDTO>({
    defaultValues: {
      ...FORM_CONFIG.DEFAULT_VALUES,
      typeConsultation: professionnels_types_consultation_enum.EN_CABINET,
      ...(initialSavedData
        ? {
            ...initialSavedData,
            sexe: initialSavedData.sexe as sexe_enum,
          }
        : {}),
    },
    mode: FORM_CONFIG.VALIDATION_MODE,
    reValidateMode: FORM_CONFIG.REVALIDATION_MODE,
  });

  const { watch, reset } = methods;

  // Fonction pour mettre à jour l'état de l'UI
  const updateUIState = useCallback(
    (
      field: keyof cabinetMedicalState,
      value: boolean | string | number | null,
    ) => {
      dispatch({
        type: "UPDATE_FIELD",
        payload: { field, value },
      });
    },
    [dispatch],
  );

  // Fonctions pour mettre à jour l'état de l'UI
  const setIsLoading = useCallback(
    (value: boolean) => {
      updateUIState("isLoading", value);
    },
    [updateUIState],
  );

  const setSubmissionSuccess = useCallback(
    (value: boolean) => {
      updateUIState("isSubmissionSuccess", value);
    },
    [updateUIState],
  );

  /**
   * Gère les erreurs de différentes sources et les affiche à l'utilisateur
   * @param error - L'erreur à traiter (string, Error ou objet avec message)
   */
  const handleError = useCallback(
    (error: string | Error | { message?: string } | unknown) => {
      let errorMessage = "Une erreur inattendue s'est produite";

      if (typeof error === "string") {
        errorMessage = error;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      } else if (
        error &&
        typeof error === "object" &&
        "message" in error &&
        typeof error.message === "string"
      ) {
        errorMessage = error.message;
      }

      // Mettre à jour l'état d'erreur
      updateUIState("error", errorMessage);

      // Afficher l'erreur dans la console pour le débogage
      console.error("Erreur dans le formulaire:", error);

      // Désactiver l'état de chargement si actif
      if (state.isLoading) {
        updateUIState("isLoading", false);
      }
    },
    [updateUIState, state.isLoading],
  );

  // Fonction simplifiée pour la compatibilité avec le code existant
  const setError = useCallback(
    (value: string) => {
      handleError(value);
    },
    [handleError],
  );

  // Fonctions pour gérer la persistance des données
  const saveFormData = useCallback(() => {
    const data = methods.getValues();
    // Ne pas sauvegarder les mots de passe, les images, les URLs de prévisualisation ou les erreurs
    const {
      profileImageFile,
      cabinetImages,
      cabinetImagesPreviewUrls,
      cabinetImagesErrors,
      motDePasse,
      confirmMotDePasse,
      region,
      district,
      commune,
      ...dataToSave
    } = data;
    localStorage.setItem(
      PERSISTENCE_CONFIG.LOCAL_STORAGE_KEY,
      JSON.stringify(dataToSave),
    );
    updateUIState("hasSavedData", true);
  }, [methods, updateUIState]);

  const clearSavedData = useCallback(() => {
    // Nettoyer les URLs de prévisualisation pour libérer la mémoire
    const cabinetImagesPreviewUrls =
      methods.getValues("cabinetImagesPreviewUrls") || [];
    cabinetImagesPreviewUrls.forEach((url) => {
      if (url) URL.revokeObjectURL(url);
    });

    // Nettoyer l'URL de prévisualisation de l'image de profil
    const profileImagePreviewUrl = methods.getValues("profileImagePreviewUrl");
    if (profileImagePreviewUrl) {
      URL.revokeObjectURL(profileImagePreviewUrl);
    }

    localStorage.removeItem(PERSISTENCE_CONFIG.LOCAL_STORAGE_KEY);
    updateUIState("hasSavedData", false);
    updateUIState("overallProgress", 0);
    location.reload();
  }, [updateUIState, methods]);

  // Mémoriser les champs requis et les champs de tableau pour éviter de les recréer à chaque appel
  const requiredFields = useMemo(
    () => new Set(FORM_CONFIG.REQUIRED_FIELDS),
    [],
  );
  const arrayFields = useMemo(
    () => new Set(FORM_CONFIG.REQUIRED_ARRAY_FIELDS),
    [],
  );
  const totalRequiredFields = useMemo(
    () => requiredFields.size + arrayFields.size,
    [requiredFields, arrayFields],
  );

  // Mémoriser le dernier état de progression pour éviter des calculs inutiles
  const [lastProgress, setLastProgress] = useState<{
    value: number;
    formState: Record<string, boolean>;
  }>({
    value: 0,
    formState: {},
  });

  // Calculer la progression du formulaire de manière optimisée
  const calculateProgress = useCallback(() => {
    const data = methods.getValues();

    // Vérifier si les données ont changé depuis le dernier calcul
    const currentFormState: Record<string, boolean> = {};
    let shouldRecalculate = false;

    // Créer un état actuel du formulaire pour comparaison
    requiredFields.forEach((field: string) => {
      const fieldName = field as keyof CabinetMedicalFormDTO;
      const isFieldFilled = Boolean(data[fieldName]);
      currentFormState[field] = isFieldFilled;

      // Si l'état d'un champ a changé, recalculer
      if (lastProgress.formState[field] !== isFieldFilled) {
        shouldRecalculate = true;
      }
    });

    arrayFields.forEach((field: string) => {
      const fieldName = field as keyof CabinetMedicalFormDTO;
      const array = data[fieldName] as unknown[];
      const isArrayFilled = Boolean(
        array && Array.isArray(array) && array.length > 0,
      );
      currentFormState[field] = isArrayFilled;

      // Si l'état d'un tableau a changé, recalculer
      if (lastProgress.formState[field] !== isArrayFilled) {
        shouldRecalculate = true;
      }
    });

    // Si rien n'a changé, retourner la valeur précédente
    if (!shouldRecalculate && lastProgress.value > 0) {
      return lastProgress.value;
    }

    // Compter les champs remplis en une seule passe
    let filledFields = 0;

    // Compter les champs requis remplis
    for (const field of requiredFields as Set<string>) {
      if (currentFormState[field]) {
        filledFields++;
      }
    }

    // Compter les tableaux qui ont au moins un élément
    for (const field of arrayFields as Set<string>) {
      if (currentFormState[field]) {
        filledFields++;
      }
    }

    // Calculer le pourcentage
    const progress = Math.round((filledFields / totalRequiredFields) * 100);

    // Mettre à jour l'état mémorisé
    setLastProgress({
      value: progress,
      formState: currentFormState,
    });

    // Mettre à jour l'état de progression uniquement si la valeur a changé
    if (progress !== lastProgress.value) {
      updateUIState("overallProgress", progress);
    }

    return progress;
  }, [
    methods,
    updateUIState,
    requiredFields,
    arrayFields,
    totalRequiredFields,
    lastProgress,
  ]);

  // Fonction pour restaurer les données sauvegardées
  const restoreSavedData = useCallback(() => {
    const currentSavedData = getSavedFormData();
    if (currentSavedData) {
      // Mettre à jour l'état local
      setSavedData(currentSavedData);

      // Réinitialiser le formulaire avec les données sauvegardées
      reset({
        ...FORM_CONFIG.DEFAULT_VALUES, // Commencer avec les valeurs par défaut
        typeConsultation: professionnels_types_consultation_enum.EN_CABINET,
        ...currentSavedData,
        sexe: currentSavedData.sexe as sexe_enum, // Convertir explicitement en enum
      });

      // Recalculer la progression
      setTimeout(() => {
        calculateProgress();
      }, 100);

      toast.success("Vos données sauvegardées ont été restaurées.");
    } else {
      toast.error("Aucune donnée sauvegardée n'a été trouvée.");
    }
  }, [getSavedFormData, setSavedData, reset, calculateProgress, toast]);

  // Sauvegarder les données du formulaire lorsqu'elles changent
  useEffect(() => {
    // Référence pour le timeout pour pouvoir le nettoyer
    let saveTimeoutId: NodeJS.Timeout | null = null;

    const subscription = watch((_, { name, type }) => {
      if (type === "change" && name) {
        // Nettoyer le timeout précédent s'il existe
        if (saveTimeoutId) {
          clearTimeout(saveTimeoutId);
        }

        // Attendre plus longtemps avant de sauvegarder pour réduire les écritures
        saveTimeoutId = setTimeout(() => {
          saveFormData();

          // Vérifier si le champ modifié est un champ requis ou un tableau requis
          // pour éviter de recalculer la progression inutilement
          const isRequiredField =
            requiredFields.has(name as string) ||
            arrayFields.has(name as string);
          if (isRequiredField) {
            calculateProgress(); // Calculer la progression uniquement si nécessaire
          }

          saveTimeoutId = null;
        }, PERSISTENCE_CONFIG.AUTO_SAVE_DELAY_MS); // Délai configurable pour réduire la fréquence des sauvegardes
      }
    });

    // Nettoyer à la fois la souscription et le timeout en cours
    return () => {
      subscription.unsubscribe();
      if (saveTimeoutId) {
        clearTimeout(saveTimeoutId);
      }
    };
  }, [watch, saveFormData, calculateProgress, requiredFields, arrayFields]);

  const onSubmit = methods.handleSubmit(async (data) => {
    try {
      // Vérifier s'il y a des erreurs d'images avant de soumettre
      if (data.cabinetImagesErrors && data.cabinetImagesErrors.length > 0) {
        handleError(
          "Certaines images dépassent la limite de taille. Veuillez les remplacer avant de soumettre le formulaire.",
        );
        return;
      }

      // Vérifier la taille de l'image de profil si elle existe
      if (data.profileImageFile) {
        const maxSizeBytes = UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB * 1024 * 1024;
        if (data.profileImageFile.size > maxSizeBytes) {
          handleError(
            `L'image de profil (${(
              data.profileImageFile.size /
              1024 /
              1024
            ).toFixed(
              2,
            )}MB) dépasse la limite de ${UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB`,
          );
          return;
        }
      }

      // Vérifier la taille des images du cabinet
      if (data.cabinetImages && data.cabinetImages.length > 0) {
        const maxSizeBytes = UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB * 1024 * 1024;
        const oversizedImages = data.cabinetImages.filter(
          (img) => img.size > maxSizeBytes,
        );

        if (oversizedImages.length > 0) {
          handleError(
            `${oversizedImages.length} image(s) dépassent la limite de ${UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB. Veuillez les remplacer avant de soumettre le formulaire.`,
          );
          return;
        }
      }

      setIsLoading(true);

      const result = await registerCabinetMedicalUsecase.execute(
        data,
        professionalInvitationId,
      );

      if (!result.success) {
        console.log("Error", result?.message);
      } else {
        toast.success("Votre cabinet médical a été enregistré avec succès !");
        navigate(`/${PublicRoutesNavigation.LOGIN_PAGE}`);
      }

      setIsLoading(false);

      // Note: Quand l'API sera implémentée, decommenter:
      // setSubmissionSuccess(true);
    } catch (error) {
      // Utiliser notre nouvelle fonction de gestion d'erreurs
      handleError(error);
    }
  });

  // Gestion de la soumission réussie
  useEffect(() => {
    if (state.isSubmissionSuccess) {
      toast.success("Votre cabinet médical a été enregistré avec succès !");

      clearSavedData();

      const redirectTimer = setTimeout(() => {
        navigate("/professionnels/dashboard");
      }, PERSISTENCE_CONFIG.REDIRECT_DELAY_MS);

      return () => clearTimeout(redirectTimer);
    }
  }, [state.isSubmissionSuccess, toast, navigate, clearSavedData]);

  // Gestion des erreurs
  useEffect(() => {
    if (state.error) {
      toast.error(state.error);
      setError("");
    }
  }, [state.error, toast, setError]);

  // Mettre à jour l'état UI et calculer la progression initiale
  useEffect(() => {
    if (savedData) {
      updateUIState("hasSavedData", true);

      // Calculer la progression initiale seulement si elle n'a pas déjà été calculée
      if (state.overallProgress === 0) {
        // Utiliser un délai pour s'assurer que le formulaire est complètement initialisé
        setTimeout(() => {
          calculateProgress();
        }, 500);
      }
    }
  }, [savedData, updateUIState, calculateProgress, state.overallProgress]);

  return {
    // État de l'UI
    state, // Exposer l'état complet pour accéder à toutes les propriétés
    isLoading: state.isLoading,
    error: state.error,
    isSubmissionSuccess: state.isSubmissionSuccess,
    hasSavedData: state.hasSavedData,
    overallProgress: state.overallProgress,
    expandedPanel: state.panelExpanded,

    // Méthodes pour le formulaire
    methods,
    onSubmit,

    // Méthodes pour l'état de l'UI
    setIsLoading,
    setSubmissionSuccess,
    setError,
    handleError,

    // Méthodes pour la persistance
    saveFormData,
    clearSavedData,
    restoreSavedData,
  };
};

export default useCabinetMedicalForm;
