import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { mot_cles } from "@/domain/models/MotCles";
import useMotCle from "../use-mot-cle";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

export const useMotCleSelection = () => {
  const { setValue, getValues } = useFormContext<CabinetMedicalFormDTO>();
  const [motClesList, setMotClesList] = useState<mot_cles[]>([]);
  const [selectedMotCles, setSelectedMotCles] = useState<mot_cles[]>(
    getValues("motCles") || [],
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { getMotCles } = useMotCle();

  // Charger la liste des mots-clés
  useEffect(() => {
    const fetchMotCles = async () => {
      setIsLoading(true);
      try {
        if (!motClesList || motClesList.length == 0) {
          const data = await getMotCles();
          if (data) {
            setMotClesList(data);
          }
        }
      } catch (error) {
        console.error("Erreur lors du chargement des mots-clés:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMotCles();
  }, []);

  // Mettre à jour le formulaire lorsque les mots-clés changent
  const handleMotClesChange = (motCles: mot_cles[]) => {
    setSelectedMotCles(motCles);
    setValue("motCles", motCles, { shouldValidate: true });
  };

  // Initialiser les mots-clés à partir des valeurs sauvegardées
  useEffect(() => {
    const savedMotCles = getValues("motCles");
    if (savedMotCles && savedMotCles.length > 0) {
      setSelectedMotCles(savedMotCles);
    }
  }, [getValues]);

  return {
    motClesList,
    selectedMotCles,
    handleMotClesChange,
    isLoading,
  };
};

export default useMotCleSelection;
