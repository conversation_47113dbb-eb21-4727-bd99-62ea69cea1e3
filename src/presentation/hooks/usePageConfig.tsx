import { Trash2, User<PERSON><PERSON><PERSON>, <PERSON>, UserX } from "lucide-react";
import { useMemo } from "react";

const usePageConfig = (type: string) => {
  const config = useMemo(() => {
    switch (type) {
      case "actif":
        return {
          title: "Employés Actifs",
          subtitle: "Liste des employés en cours de traitement",
          icon: (
            <UserCheck
              size={18}
              className="text-emerald-500 dark:text-emerald-400"
            />
          ),
          color: "text-emerald-600 dark:text-emerald-400",
          bgColor: "bg-emerald-50 dark:bg-emerald-900/20",
          borderColor: "border-emerald-200 dark:border-emerald-800",
        };
      case "decede":
        return {
          title: "Employés Décédés",
          subtitle: "Liste des employés décédés",
          icon: (
            <UserX size={24} className="text-rose-500 dark:text-rose-400" />
          ),
          color: "text-rose-600 dark:text-rose-400",
          bgColor: "bg-rose-50 dark:bg-rose-900/20",
          borderColor: "border-rose-200 dark:border-rose-800",
        };
      case "supprimer":
        return {
          title: "Employés Supprimés",
          subtitle: "Liste des employés retirés de votre suivi",
          icon: (
            <Trash2 size={18} className="text-amber-500 dark:text-amber-400" />
          ),
          color: "text-amber-600 dark:text-amber-400",
          bgColor: "bg-amber-50 dark:bg-amber-900/20",
          borderColor: "border-amber-200 dark:border-amber-800",
        };
      default:
        return {
          title: "Tous les Employés",
          subtitle: "Liste complète de vos employés",
          icon: <Users size={18} className="text-blue-500" />,
          color: "text-blue-600 dark:text-blue-400",
        };
    }
  }, [type]);
  return config;
};

export default usePageConfig;
