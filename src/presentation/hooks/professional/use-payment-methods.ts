import { useMemo } from "react";
import { PAYMENT_METHODS } from "@/shared/constants/cabinetMedicalConfig";
import { PaymentMethod } from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";

/**
 * Hook personnalisé pour traiter et gérer les méthodes de paiement professionnelles
 *
 * @description Ce hook traite le champ modes_paiement_acceptes du professionnel
 * et le convertit en un tableau de PaymentMethod utilisable par l'interface.
 * Il gère différents formats de données (JSON, chaîne de caractères) et
 * fait correspondre les méthodes avec les options prédéfinies.
 *
 * @param modesPaymentAcceptes - Le champ modes_paiement_acceptes du professionnel (string | undefined)
 *
 * @returns {PaymentMethod[]} Un tableau des méthodes de paiement formatées
 *
 * @example
 * ```tsx
 * const paymentMethods = usePaymentMethods(professionalData.modes_paiement_acceptes);
 *
 * return (
 *   <div>
 *     <h3>Méthodes de paiement acceptées :</h3>
 *     {paymentMethods.map(method => (
 *       <span key={method.id} className="payment-method">
 *         {method.name}
 *       </span>
 *     ))}
 *   </div>
 * );
 * ```
 *
 * @architecture
 * Ce hook respecte les principes de séparation des responsabilités :
 * - Isole la logique de traitement des méthodes de paiement
 * - Utilise useMemo pour optimiser les performances
 * - Gère les différents formats de données de manière robuste
 * - Fournit une interface cohérente pour les composants
 */
export const usePaymentMethods = (modesPaymentAcceptes?: string): PaymentMethod[] => {
  return useMemo(() => {
    const paymentMethods: PaymentMethod[] = [];

    if (!modesPaymentAcceptes) {
      return paymentMethods;
    }

    try {
      // Tenter de parser le JSON si c'est un format JSON
      const parsedMethods = JSON.parse(modesPaymentAcceptes);
      if (Array.isArray(parsedMethods)) {
        // Valider que chaque élément a la structure PaymentMethod
        parsedMethods.forEach(method => {
          if (method && typeof method === 'object' && method.id && method.name) {
            paymentMethods.push(method as PaymentMethod);
          }
        });
        return paymentMethods;
      }
    } catch {
      // Si ce n'est pas du JSON, traiter comme une chaîne de caractères
    }

    // Traiter comme une chaîne de caractères séparée par des virgules
    const methodNames = modesPaymentAcceptes
      .split(',')
      .map(m => m.trim())
      .filter(name => name.length > 0);

    methodNames.forEach((name, index) => {
      // Chercher dans les méthodes prédéfinies
      const existingMethod = PAYMENT_METHODS.find(
        pm => pm.name.toLowerCase() === name.toLowerCase()
      );

      if (existingMethod) {
        // Utiliser la méthode prédéfinie si trouvée
        paymentMethods.push(existingMethod);
      } else {
        // Créer une nouvelle méthode personnalisée
        paymentMethods.push({
          id: `custom_${index}_${name.toLowerCase().replace(/\s+/g, '_')}`,
          name: name
        });
      }
    });

    return paymentMethods;
  }, [modesPaymentAcceptes]);
};

/**
 * Hook personnalisé pour obtenir toutes les méthodes de paiement disponibles
 *
 * @description Ce hook retourne la liste complète des méthodes de paiement
 * prédéfinies dans la configuration du projet.
 *
 * @returns {PaymentMethod[]} La liste complète des méthodes de paiement disponibles
 *
 * @example
 * ```tsx
 * const availablePaymentMethods = useAvailablePaymentMethods();
 *
 * return (
 *   <select>
 *     {availablePaymentMethods.map(method => (
 *       <option key={method.id} value={method.id}>
 *         {method.name}
 *       </option>
 *     ))}
 *   </select>
 * );
 * ```
 */
export const useAvailablePaymentMethods = (): PaymentMethod[] => {
  return useMemo(() => PAYMENT_METHODS, []);
};
