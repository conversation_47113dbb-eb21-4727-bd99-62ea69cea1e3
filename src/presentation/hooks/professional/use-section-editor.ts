import { useState, useCallback } from "react";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/presentation/hooks/use-error-handler";

/**
 * Interface pour les options du hook useSectionEditor
 */
interface SectionEditorOptions<T> {
  /** Données initiales de la section */
  initialData: T;
  /** Fonction de validation des données */
  validate?: (data: T) => string | null;
  /** Fonction de sauvegarde */
  onSave: (data: T) => Promise<boolean>;
  /** Fonction appelée après une sauvegarde réussie */
  onSaveSuccess?: (data: T) => void;
  /** Nom de la section pour les messages d'erreur */
  sectionName: string;
}

/**
 * Hook personnalisé pour gérer l'édition par section
 *
 * @description Ce hook encapsule la logique commune à toutes les sections
 * éditables : gestion de l'état local, validation, sauvegarde et gestion d'erreurs.
 * Il fournit une interface cohérente pour l'édition de toutes les sections du profil.
 *
 * @example
 * ```tsx
 * const {
 *   data,
 *   updateData,
 *   save,
 *   cancel,
 *   isSaving,
 *   hasChanges
 * } = useSectionEditor({
 *   initialData: { presentation: "..." },
 *   validate: (data) => data.presentation.length > 0 ? null : "La présentation est requise",
 *   onSave: async (data) => await updatePresentation(data.presentation),
 *   sectionName: "présentation"
 * });
 * ```
 */
export const useSectionEditor = <T>({
  initialData,
  validate,
  onSave,
  onSaveSuccess,
  sectionName,
}: SectionEditorOptions<T>) => {
  const [data, setData] = useState<T>(initialData);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const { handleValidationError, handleApiError, handleSuccess } =
    useErrorHandler();

  /**
   * Met à jour les données locales
   */
  const updateData = useCallback((newData: Partial<T>) => {
    setData((prev) => ({ ...prev, ...newData }));
    setHasChanges(true);
  }, []);

  /**
   * Met à jour un champ spécifique
   */
  const updateField = useCallback(
    (field: keyof T, value: T[keyof T]) => {
      updateData({ [field]: value } as Partial<T>);
    },
    [updateData]
  );

  /**
   * Sauvegarde les modifications
   */
  const save = useCallback(async (): Promise<void> => {
    try {
      setIsSaving(true);

      // Validation côté client
      if (validate) {
        const validationError = validate(data);
        if (validationError) {
          handleValidationError(validationError, sectionName);
          return;
        }
      }

      // Sauvegarde
      const success = await onSave(data);

      if (success) {
        setHasChanges(false);
        handleSuccess(`Modification de ${sectionName}`);

        if (onSaveSuccess) {
          onSaveSuccess(data);
        }
      } else {
        throw new Error("Échec de la sauvegarde");
      }
    } catch (error) {
      handleApiError(error, `la modification de ${sectionName}`);
      throw error; // Re-throw pour que le composant puisse gérer l'état d'édition
    } finally {
      setIsSaving(false);
    }
  }, [
    data,
    validate,
    onSave,
    onSaveSuccess,
    sectionName,
    handleValidationError,
    handleApiError,
    handleSuccess,
  ]);

  /**
   * Annule les modifications
   */
  const cancel = useCallback(() => {
    setData(initialData);
    setHasChanges(false);
  }, [initialData]);

  /**
   * Réinitialise avec de nouvelles données
   */
  const reset = useCallback((newInitialData: T) => {
    setData(newInitialData);
    setHasChanges(false);
  }, []);

  return {
    data,
    updateData,
    updateField,
    save,
    cancel,
    reset,
    isSaving,
    hasChanges,
  };
};
