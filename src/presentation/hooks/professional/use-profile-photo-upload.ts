import { useState, useCallback } from "react";
import { useToast } from "@/presentation/hooks/use-toast";
import { Photo } from "@/domain/models/Photo";
import { PhotoTypeEnum } from "@/domain/models/enums";
import {
  uploadProfilePhoto as uploadProfilePhotoAction,
  getProfilePhotoByUserId,
  deleteProfilePhoto as deleteProfilePhotoAction,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";
import { useAppDispatch } from "../redux.ts";

/**
 * Hook personnalisé pour la gestion de l'upload de photo de profil
 *
 * @description
 * Ce hook encapsule toute la logique d'upload, de mise à jour et de suppression
 * des photos de profil professionnelles. Il gère :
 * - Upload de nouvelle photo de profil
 * - Mise à jour d'une photo existante
 * - Suppression de photo de profil
 * - États de chargement et gestion d'erreurs
 * - Feedback utilisateur via toasts
 */
export const useProfilePhotoUpload = (id?: number) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const toast = useToast();
  const dispatch = useAppDispatch();

  /**
   * Upload une nouvelle photo de profil
   *
   * @param userId - ID de l'utilisateur
   * @param file - Fichier image à uploader
   * @returns Photo uploadée ou null en cas d'erreur
   */
  const uploadProfilePhoto = useCallback(
    async (file: File): Promise<boolean> => {
      let currentPhoto: Photo = null;

      try {
        setIsUploading(true);

        // 1. Récupérer la photo de profil actuelle
        currentPhoto = await getCurrentProfilePhoto(id);

        if (currentPhoto) {
          // 2. Supprimer la photo actuelle
          await dispatch(deleteProfilePhotoAction(currentPhoto.id));
        }

        // 3. Uploader la nouvelle photo
        const uploadedPhoto = await dispatch(
          uploadProfilePhotoAction({ userId: id, file })
        );

        return uploadedPhoto !== null;
      } catch (error) {
        console.error("Erreur lors de la mise à jour de la photo:", error);
        toast.error("Erreur inattendue lors de la mise à jour");
        return false;
      } finally {
        setIsUploading(false);
      }
    },
    [toast]
  );

  /**
   * Supprime la photo de profil actuelle
   *
   * @param photoId - ID de la photo à supprimer
   * @returns true si la suppression a réussi
   */
  const deleteProfilePhoto = useCallback(
    async (photoId: number): Promise<boolean> => {
      try {
        setIsDeleting(true);

        const success = await dispatch(deleteProfilePhotoAction(photoId));

        if (success.payload) {
          toast.success("Photo de profil supprimée avec succès");
          return true;
        } else {
          toast.error("Erreur lors de la suppression de la photo");
          return false;
        }
      } catch (error) {
        console.error("Erreur lors de la suppression de la photo:", error);
        toast.error("Erreur inattendue lors de la suppression");
        return false;
      } finally {
        setIsDeleting(false);
      }
    },
    [toast]
  );

  /**
   * Récupère la photo de profil actuelle d'un utilisateur
   *
   * @param userId - ID de l'utilisateur
   * @returns Photo de profil ou null si aucune
   */
  const getCurrentProfilePhoto = useCallback(
    async (userId: number): Promise<Photo | null> => {
      try {
        const photos = await dispatch(getProfilePhotoByUserId(userId));

        const data = photos.payload as Photo[];

        const out = data.find((photo) => photo.type === PhotoTypeEnum.PROFILE);

        return out || null;
      } catch (error) {
        console.error(
          "Erreur lors de la récupération de la photo de profil:",
          error
        );
        return null;
      }
    },
    [getProfilePhotoByUserId]
  );

  return {
    // Actions
    uploadProfilePhoto,
    deleteProfilePhoto,
    getCurrentProfilePhoto,

    // États
    isUploading,
    isDeleting,
    isLoading: isUploading || isDeleting,
  };
};
