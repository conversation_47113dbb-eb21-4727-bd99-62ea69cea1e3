import { useCallback, useEffect, useState } from "react";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel.ts";

/**
 * Hook local pour gérer l'état d'affichage et les opérations différées
 * liées aux diplômes (édition optimiste, suppressions/updates en attente).
 *
 * Ce hook ne fait aucun appel API. Il sépare la logique d'état/UI de l'affichage.
 */
export function useDiplomasCrudLocal(initialDiplomas: DiplomeProfessionnel[]) {
  // Diplôme en cours d'édition (id) ou null si aucun
  const [editingDiplomaId, setEditingDiplomaId] = useState<number | null>(null);
  const [isAdding, setIsAdding] = useState(false);

  // Synchroniser l'affichage lorsque la source change (ex: après persistance)
  useEffect(() => {
    setEditingDiplomaId(null);
    setIsAdding(false);
  }, [initialDiplomas]);

  /**
   * Démarre l'édition d'un diplôme
   */
  const startEditing = useCallback((diploma: DiplomeProfessionnel) => {
    setEditingDiplomaId(diploma.id);
  }, []);

  /**
   * Annule l'édition d'un diplôme
   */
  const cancelEditing = useCallback(() => {
    setEditingDiplomaId(null);
  }, []);

  return {
    // state
    editingDiplomaId,
    isAdding,
    // setters exposés si besoin
    setEditingDiplomaId,
    setIsAdding,
    // actions
    startEditing,
    cancelEditing,
  };
}
