import useSpecialitiesLists from "@/presentation/hooks/use-specialities-lists";
import { useAppDispatch, useAppSelector } from "@/presentation/hooks/redux";
import { ListeSpecialites } from "@/domain/models";
import { useCallback, useEffect } from "react";
import {
  createProfessionalSpeciality,
  deleteProfessionalSpeciality,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";

/**
 * Handlers spécialisés pour la gestion des spécialités du professionnel.
 *
 * - Ajout: convertit des IDs (liste maître) en payloads d’insertion
 * - Suppression: délègue au usecase dédié
 * - Récupère `professionalId` depuis le store d’authentification
 */
const useSpecialitiesHandlers = (userId?: number) => {
  const { listes, getSpecialitiesList } = useSpecialitiesLists();
  const dispatch = useAppDispatch();

  useEffect(() => {
    getSpecialitiesList();
  }, [getSpecialitiesList]);

  /**
   * Ajoute une ou plusieurs spécialités à un professionnel
   * @param specialityIds IDs dans la table liste des spécialités
   */
  const handleAddSpecialty = useCallback(
    async (specialityIds: number[]) => {
      try {
        if (!userId) {
          console.error("ID professionnel introuvable");
          return false;
        }

        // Mapper les IDs sélectionnés vers leurs libellés pour l'insertion
        const selectedFromList = specialityIds
          .map((id) => listes.find((s) => Number(s.id) === Number(id)))
          .filter((s): s is ListeSpecialites => Boolean(s));

        if (selectedFromList.length === 0) {
          return true; // rien à ajouter
        }

        const payload = selectedFromList.map((s) => ({
          nom_specialite: s.nom_specialite,
          id_professionnel: Number(userId),
        }));

        await dispatch(
          createProfessionalSpeciality({ speciality: payload })
        ).unwrap();
        return true;
      } catch (error) {
        console.error("Erreur lors de l'ajout de la spécialité:", error);
        return false;
      }
    },
    [userId, listes, dispatch]
  );

  /**
   * Supprime une spécialité par son ID (ligne dans `specialites_professionnel`)
   */
  const handleDeleteSpeciality = async (specialtyId: number) => {
    try {
      await dispatch(deleteProfessionalSpeciality(specialtyId)).unwrap();
      return true;
    } catch (error) {
      console.error("Erreur lors de la suppression de la spécialité:", error);
      return false;
    }
  };

  return {
    handleAddSpecialty,
    handleDeleteSpeciality,
    availableSpecialities: listes,
  };
};

export default useSpecialitiesHandlers;
