import { useState, useEffect, useCallback } from "react";
import { ListeAssurances } from "@/domain/models/AssuranceProfessionnel";
import { GetInsurancesRepository } from "@/infrastructure/repositories/insurance";
import GetInsurancesUsecase from "@/domain/usecases/insurance/GetInsurancesUsecase";

/**
 * Hook personnalisé pour gérer la liste des assurances disponibles
 *
 * @description Ce hook récupère et gère la liste globale des assurances
 * disponibles dans le système. Il peut être utilisé pour proposer des
 * suggestions d'assurances lors de l'ajout.
 *
 * @returns {Object} Objet contenant les assurances disponibles et les fonctions de gestion
 */
export const useAvailableInsurances = () => {
  const [availableInsurances, setAvailableInsurances] = useState<ListeAssurances[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Récupère la liste des assurances disponibles
   */
  const fetchAvailableInsurances = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const repository = new GetInsurancesRepository();
      const usecase = new GetInsurancesUsecase(repository);
      const insurances = await usecase.execute();

      if (insurances) {
        setAvailableInsurances(insurances);
      } else {
        setAvailableInsurances([]);
      }
    } catch (err) {
      console.error("Erreur lors de la récupération des assurances:", err);
      setError("Erreur lors de la récupération des assurances disponibles");
      setAvailableInsurances([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Filtre les assurances disponibles selon un terme de recherche
   */
  const filterInsurances = useCallback(
    (searchTerm: string): ListeAssurances[] => {
      if (!searchTerm.trim()) {
        return availableInsurances;
      }

      return availableInsurances.filter((insurance) =>
        insurance.nom.toLowerCase().includes(searchTerm.toLowerCase())
      );
    },
    [availableInsurances]
  );

  /**
   * Vérifie si une assurance existe déjà dans la liste globale
   */
  const insuranceExists = useCallback(
    (insuranceName: string): ListeAssurances | undefined => {
      return availableInsurances.find(
        (insurance) => insurance.nom.toLowerCase() === insuranceName.toLowerCase()
      );
    },
    [availableInsurances]
  );

  // Charger les assurances au montage du composant
  useEffect(() => {
    fetchAvailableInsurances();
  }, [fetchAvailableInsurances]);

  return {
    availableInsurances,
    isLoading,
    error,
    fetchAvailableInsurances,
    filterInsurances,
    insuranceExists,
  };
};
