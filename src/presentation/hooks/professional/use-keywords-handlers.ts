import { mot_cles } from "@/domain/models/MotCles.ts";
import { useAppDispatch } from "../redux.ts";
import useMotCle from "../use-mot-cle.ts";
import { useEffect, useState } from "react";
import {
  addProfessionalMotCles,
  removeProfessionalMotCles,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";

const useKeywordsHandlers = (userId?: number) => {
  const dispatch = useAppDispatch();
  const [availableKeywords, setAvailableKeywords] = useState<mot_cles[]>([]);
  const { getMotCles } = useMotCle();

  const fetchAvailableKeywords = async () => {
    try {
      const keywords = await getMotCles();
      if (keywords) {
        setAvailableKeywords(keywords);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des mots-clés:", error);
    }
  };

  useEffect(() => {
    if (availableKeywords.length === 0) {
      fetchAvailableKeywords();
    }
  }, [fetchAvailableKeywords]);

  const handleAddKeywords = async (keywordIds: number[]) => {
    const matchingKeywords = availableKeywords.filter((k) =>
      keywordIds.includes(k.id)
    );
    const formatedKeywords = matchingKeywords.map((k) => ({
      symptome: k.symptome,
      id_professionnel: userId,
    }));

    const data = await dispatch(
      addProfessionalMotCles({
        professionalId: userId,
        motCles: formatedKeywords,
      })
    ).unwrap();
    return data !== null;
  };

  const handleDeleteKeyword = async (keywordId: number) => {
    const data = await dispatch(removeProfessionalMotCles(keywordId)).unwrap();
    return data !== null;
  };

  return {
    availableKeywords,
    handleAddKeywords,
    handleDeleteKeyword,
  };
};

export default useKeywordsHandlers;
