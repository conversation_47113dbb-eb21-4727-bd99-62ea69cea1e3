import { useCallback } from "react";
import { EstablishmentFormData } from "@/shared/schemas/ProfessionalProfileSchemas";
import { useAppDispatch } from "../redux.ts";
import {
  updateProfessionalEtablishment,
  updateProfessional,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";

/**
 * Hook personnalisé pour gérer les handlers d'établissement
 *
 * @description Ce hook encapsule toute la logique métier liée aux informations
 * d'établissement du professionnel.
 */
export const useEstablishmentHandlers = (userId?: number) => {
  const dispatch = useAppDispatch();
  /**
   * Handler pour la sauvegarde des informations d'établissement
   */
  const handleSaveEstablishment = useCallback(
    async (
      etablishmentId: number,
      establishmentData: EstablishmentFormData
    ) => {
      try {
        const result = await dispatch(
          updateProfessionalEtablishment({
            etablishmentId: etablishmentId,
            etablishment: {
              ...establishmentData,
            },
          })
        ).unwrap();

        return result !== null;
      } catch (error) {
        console.error(
          "Erreur lors de la sauvegarde de l'établissement:",
          error
        );
        return false;
      }
    },
    [dispatch, updateProfessionalEtablishment, updateProfessionalEtablishment]
  );

  const updateGeolocation = useCallback(
    async (geolocation: string) => {
      if (!userId) {
        console.error("ID professionnel introuvable");
        return false;
      }
      try {
        const result = await dispatch(
          updateProfessional({
            professionalId: userId,
            baseInfo: {
              geolocalisation: geolocation,
            },
          })
        ).unwrap();

        return result !== null;
      } catch (error) {
        console.error(
          "Erreur lors de la sauvegarde de l'établissement:",
          error
        );
        return false;
      }
    },
    [dispatch]
  );

  return {
    handleSaveEstablishment,
    updateGeolocation,
  };
};
