import { fetchProfessionalProfile } from "@/application/slices/professionnal/professionalProfileSlice.ts";
import { useAppSelector, useAppDispatch } from "../redux.ts";
import { useCallback, useEffect } from "react";
import { useProfessionalProfileHandlers } from "./use-professional-profile-handlers.ts";

const useProfessionalProfile = () => {
  const { profileData, isLoading, error } = useAppSelector(
    (state) => state.professionalProfile
  );
  const dispatch = useAppDispatch();
  const { id } = useAppSelector((state) => state.authentification.user);
  const { id: professionalId } = useAppSelector(
    (state) => state.authentification.userData
  );

  const fetchProfile = useCallback(
    async (professionalId: number) => {
      await dispatch(fetchProfessionalProfile(professionalId)).unwrap();
    },
    [dispatch]
  );

  useEffect(() => {
    if (id) {
      fetchProfile(id);
    }
  }, [id, fetchProfile]);

  /**
   * Handlers
   */
  const handlers = useProfessionalProfileHandlers(id, professionalId);

  return {
    profileData,
    isLoading,
    error,
    handlers,
  };
};

export default useProfessionalProfile;
