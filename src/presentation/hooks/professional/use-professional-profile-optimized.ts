import { useEffect, useState } from "react";
import { ProfessionalProfileDTO } from "@/domain/DTOS/CabinetMedicalFormDTO";
import { useToast } from "../use-toast";
import { GetProfessionalProfileCompleteRepository } from "@/infrastructure/repositories/professionals/GetProfessionalProfileCompleteRepository";
import { GetProfessionalProfileCompleteUsecase } from "@/domain/usecases/professional/GetProfessionals/GetProfessionalProfileCompleteUsecase";

/**
 * Hook personnalisé optimisé pour récupérer les données complètes du profil professionnel
 *
 * @description Ce hook utilise une architecture optimisée qui récupère toutes les données
 * du profil professionnel en une seule requête au lieu de multiples requêtes séparées.
 * Cela améliore considérablement les performances et réduit la latence réseau.
 *
 * @param professionalId - L'identifiant unique du professionnel
 *
 * @returns {Object} Un objet contenant :
 * - profileData: Les données complètes du profil (ProfessionalProfileDTO | null)
 * - isLoading: État de chargement (boolean)
 * - error: Message d'erreur éventuel (string | null)
 * - refetch: Fonction pour relancer la récupération des données
 *
 * @example
 * ```tsx
 * const { profileData, isLoading, error, refetch } = useProfessionalProfileOptimized(123);
 *
 * if (isLoading) return <LoadingSpinner />;
 * if (error) return <ErrorMessage message={error} />;
 * if (!profileData) return <NotFound />;
 *
 * return (
 *   <div>
 *     <h1>{profileData.baseInfo.titre} {profileData.baseInfo.nom}</h1>
 *     <p>Spécialités: {profileData.specialities.map(s => s.nom_specialite).join(', ')}</p>
 *     <p>Méthodes de paiement: {profileData.paymentMethods.map(pm => pm.name).join(', ')}</p>
 *     <p>Photos: {profileData.photos.length} image(s)</p>
 *     <button onClick={refetch}>Actualiser</button>
 *   </div>
 * );
 * ```
 *
 * @architecture
 * Ce hook respecte l'architecture Clean Architecture du projet :
 * - Utilise un use case pour orchestrer la logique métier
 * - Sépare la logique de présentation de la logique métier
 * - Gère les états de chargement et d'erreur de manière centralisée
 * - Optimise les performances avec une seule requête
 * - Facilite la maintenance et les tests
 *
 * @performance
 * Améliorations par rapport au hook non optimisé :
 * - Réduction de ~12 requêtes à 1 seule requête
 * - Diminution significative de la latence réseau
 * - Réduction de la charge sur le serveur
 * - Amélioration de l'expérience utilisateur (chargement plus rapide)
 */
export const useProfessionalProfileOptimized = (professionalId: number) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<ProfessionalProfileDTO | null>(null);
  const toast = useToast();

  /**
   * Récupère les données du profil professionnel de manière optimisée
   * 
   * @description Cette fonction utilise le use case optimisé pour récupérer
   * toutes les données nécessaires en une seule opération.
   */
  const fetchProfileData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Validation des paramètres d'entrée
      if (!professionalId || professionalId <= 0) {
        throw new Error("L'identifiant du professionnel doit être un nombre positif valide");
      }

      // Initialisation du repository et du use case optimisés
      const repository = new GetProfessionalProfileCompleteRepository();
      const usecase = new GetProfessionalProfileCompleteUsecase(repository);

      // Récupération des données via le use case optimisé
      const data = await usecase.execute(professionalId);

      if (!data) {
        throw new Error("Professionnel non trouvé");
      }

      setProfileData(data);

    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Une erreur est survenue lors de la récupération du profil";
      
      setError(errorMessage);
      toast.error(errorMessage);
      
      // Réinitialiser l'erreur après un délai pour éviter qu'elle reste affichée
      setTimeout(() => {
        setError(null);
      }, 5000);

    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Fonction pour relancer la récupération des données
   * 
   * @description Permet de rafraîchir les données du profil,
   * utile après des modifications ou en cas d'erreur.
   */
  const refetch = () => {
    fetchProfileData();
  };

  // Effet pour récupérer les données au montage du composant et lors du changement d'ID
  useEffect(() => {
    if (professionalId) {
      fetchProfileData();
    }
  }, [professionalId]);

  return {
    profileData,
    isLoading,
    error,
    refetch,
  };
};
