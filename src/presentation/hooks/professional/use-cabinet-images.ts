import { useEffect, useState } from "react";
import { useToast } from "../use-toast";
import GetPhotosByUserIdRepository from "@/infrastructure/repositories/photos/GetPhotosByUserIdRepository";
import GetPhotosByUserIdUsecase from "@/domain/usecases/photos/GetPhotosByUserIdUsecase";
import { Photo } from "@/domain/models/Photo";
import { PhotoTypeEnum } from "@/domain/models/enums";

/**
 * Hook personnalisé pour récupérer et gérer les images du cabinet médical
 *
 * @description Ce hook récupère spécifiquement les images de présentation du cabinet
 * médical (type PRESENTATION) pour un utilisateur donné. Il filtre automatiquement
 * les photos pour ne retourner que celles du cabinet, excluant les photos de profil.
 *
 * @param userId - L'identifiant unique de l'utilisateur
 *
 * @returns {Object} Un objet contenant :
 * - cabinetImages: Les images du cabinet (Photo[])
 * - isLoading: État de chargement (boolean)
 * - error: Message d'erreur éventuel (string | null)
 * - refetch: Fonction pour relancer la récupération des données
 *
 * @example
 * ```tsx
 * const { cabinetImages, isLoading, error, refetch } = useCabinetImages(userId);
 *
 * if (isLoading) return <LoadingSpinner />;
 * if (error) return <ErrorMessage message={error} />;
 *
 * return (
 *   <div className="cabinet-gallery">
 *     {cabinetImages.map(image => (
 *       <img key={image.id} src={image.path} alt="Image du cabinet" />
 *     ))}
 *   </div>
 * );
 * ```
 *
 * @architecture
 * Ce hook respecte l'architecture Clean Architecture du projet :
 * - Utilise les repositories et usecases appropriés
 * - Sépare la logique métier de la présentation
 * - Gère les états de chargement et d'erreur
 * - Fournit une fonction de refetch pour la réactivité
 */
export const useCabinetImages = (userId: number) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cabinetImages, setCabinetImages] = useState<Photo[]>([]);
  const toast = useToast();

  const fetchCabinetImages = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Initialiser le repository et usecase
      const getPhotosByUserIdRepository = new GetPhotosByUserIdRepository();
      const getPhotosByUserIdUsecase = new GetPhotosByUserIdUsecase(getPhotosByUserIdRepository);

      // Récupérer toutes les photos de l'utilisateur
      const allPhotos = await getPhotosByUserIdUsecase.execute(userId);

      // Filtrer pour ne garder que les images du cabinet
      const cabinetPhotos = allPhotos?.filter(
        photo => photo.type === PhotoTypeEnum.PRESENTATION
      ) || [];

      setCabinetImages(cabinetPhotos);

    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Une erreur est survenue lors de la récupération des images du cabinet";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchCabinetImages();
    }
  }, [userId]);

  return {
    cabinetImages,
    isLoading,
    error,
    refetch: fetchCabinetImages,
  };
};
