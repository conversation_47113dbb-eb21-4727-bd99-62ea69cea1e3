import { useCallback } from "react";
import { useProfilePhotoUpload } from "./use-profile-photo-upload";
import { useCabinetImagesCRUD } from "./use-cabinet-images-crud";
import { useAppSelector } from "../redux";

/**
 * Hook personnalisé pour gérer les handlers de médias
 *
 * @description Ce hook encapsule toute la logique métier liée aux médias
 * (photos de profil, images du cabinet) du professionnel.
 */
export const useMediaHandlers = () => {
  const { user } = useAppSelector((state) => state.authentification);
  const { uploadOrUpdatePhoto, deletePhoto, getCurrentProfilePhoto } =
    useProfilePhotoUpload();
  const { uploadImages, deleteImage } = useCabinetImagesCRUD();

  /**
   * Handler pour l'upload de photo de profil
   */
  const handlePhotoUpload = useCallback(
    async (file: File) => {
      try {
        if (!user?.id) {
          console.error("ID utilisateur non trouvé");
          return false;
        }

        const result = await uploadOrUpdatePhoto(user.id, file);
        return result;
      } catch (error) {
        console.error("Erreur lors de l'upload de la photo:", error);
        return false;
      }
    },
    [user?.id, uploadOrUpdatePhoto]
  );

  /**
   * Handler pour la suppression de photo de profil
   */
  const handlePhotoRemove = useCallback(async () => {
    try {
      if (!user?.id) {
        console.error("ID utilisateur non trouvé");
        return false;
      }

      // Récupérer la photo de profil actuelle
      const currentPhoto = await getCurrentProfilePhoto(user.id);
      if (!currentPhoto) {
        return false;
      }

      const success = await deletePhoto(currentPhoto.id);
      return success;
    } catch (error) {
      console.error("Erreur lors de la suppression de la photo:", error);
      return false;
    }
  }, [user?.id, getCurrentProfilePhoto, deletePhoto]);

  /**
   * Handler pour l'ajout d'images du cabinet
   */
  const handleAddImages = useCallback(
    async (files: File[]) => {
      try {
        if (!user?.id) {
          console.error("ID utilisateur non trouvé");
          return false;
        }

        const result = await uploadImages(user.id, files);
        return result !== null;
      } catch (error) {
        console.error("Erreur lors de l'ajout des images:", error);
        return false;
      }
    },
    [user?.id, uploadImages]
  );

  /**
   * Handler pour la suppression d'une image du cabinet
   */
  const handleRemoveImage = useCallback(
    async (imageId: number) => {
      try {
        const success = await deleteImage(imageId);
        return success;
      } catch (error) {
        console.error("Erreur lors de la suppression de l'image:", error);
        return false;
      }
    },
    [deleteImage]
  );

  return {
    handlePhotoUpload,
    handlePhotoRemove,
    handleAddImages,
    handleRemoveImage,
  };
};
