import { ExperienceProfessionnel } from "@/domain/models/ExperienceProfessionnel.ts";
import { useAppDispatch } from "../redux.ts";
import {
  createProfessionalExperience,
  deleteProfessionalExperience,
  updateProfessionalExperience,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";
import { useCallback } from "react";

const useExperienceHandlers = (userId?: number) => {
  const dispatch = useAppDispatch();

  const handleAddExperience = useCallback(
    async (data: Omit<ExperienceProfessionnel, "id" | "id_professionnel">) => {
      if (!userId) return false;

      try {
        const result = await dispatch(
          createProfessionalExperience({
            professionalId: userId,
            experience: {
              ...data,
              id_professionnel: userId,
            },
          })
        );

        return result !== null;
      } catch (error) {
        console.error("Erreur lors de l'ajout de l'expérience:", error);
        return false;
      }
    },
    [dispatch, userId]
  );

  const handleUpdateExperience = useCallback(
    async (experienceId: number, data: Partial<ExperienceProfessionnel>) => {
      try {
        const result = await dispatch(
          updateProfessionalExperience({ experienceId, experience: data })
        );
        return result !== null;
      } catch (error) {
        console.error("Erreur lors de la mise à jour de l'expérience:", error);
        return false;
      }
    },
    [dispatch]
  );

  const handleDeleteExperience = useCallback(
    async (experienceId: number) => {
      try {
        await dispatch(deleteProfessionalExperience(experienceId));
        return true;
      } catch (error) {
        console.error("Erreur lors de la suppression de l'expérience:", error);
        return false;
      }
    },
    [dispatch]
  );

  return {
    handleAddExperience,
    handleUpdateExperience,
    handleDeleteExperience,
  };
};

export default useExperienceHandlers;
