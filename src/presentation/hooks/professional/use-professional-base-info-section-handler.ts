import { professionnels_titre_enum } from "@/domain/models/enums/professionnelsTitreEnum.ts";
import { updateProfessional } from "@/application/slices/professionnal/professionalProfileSlice.ts";
import { useAppDispatch } from "../redux.ts";

export interface BaseInfoSectionData {
  titre: professionnels_titre_enum;
  nom: string;
  prenom: string;
}

const useProfessionalBaseSectionHandler = (professionalId?: number) => {
  const dispatch = useAppDispatch();

  const handleSaveBaseInfo = async (baseInfo: Partial<BaseInfoSectionData>) => {
    if (!professionalId) {
      console.error("ID professionnel introuvable");
      return false;
    }
    try {
      const result = await dispatch(
        updateProfessional({
          professionalId,
          baseInfo: {
            ...baseInfo,
          },
        })
      ).unwrap();

      return result !== null;
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour des informations de base:",
        error
      );
      return false;
    }
  };
  return { handleSaveBaseInfo };
};

export default useProfessionalBaseSectionHandler;
