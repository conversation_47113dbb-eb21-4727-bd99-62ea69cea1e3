import { ProfessionalInvitationDTO } from "@/domain/DTOS";
import GetInvitationByTokenUsecase from "@/domain/usecases/professionalInvitation/GetInvitationByTokenUsecase";
import GetInvitationByTokenRepository from "@/infrastructure/repositories/professionalInvitation/GetInvitationByTokenRepository";
import { useEffect, useState } from "react";

const useVerifyProfessionalToken = (token?: string) => {
  const [isLoading, setIsLoading] = useState<boolean>(true); // NOTE: true par defaut pour eviter les problemes d'asyncrone dans l'utilisation. Pensez bien avant de modifier
  const [isTokenValid, setIsTokenValid] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [professionalInvitationData, setProfessionalInvitationData] =
    useState<ProfessionalInvitationDTO | null>(null);

  useEffect(() => {
    const doTokenVerification = async () => {
      if (!token || token.trim() == "") {
        setIsLoading(false);
        return;
      }

      const getInvitationByTokenRepository =
        new GetInvitationByTokenRepository();
      const getInvitationByTokenUsecase = new GetInvitationByTokenUsecase(
        getInvitationByTokenRepository,
      );

      setIsLoading(true);
      try {
        const result = await getInvitationByTokenUsecase.execute(token.trim());

        if (result) {
          if (!result.est_utilisee) {
            setIsTokenValid(true);
            setProfessionalInvitationData(result);
          }
        }
      } catch (err) {
        console.log(err);

        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("Une erreur inconnue est survenue.");
        }
      } finally {
        setIsLoading(false);
      }
    };

    doTokenVerification();
  }, [token]);

  return {
    professionalInvitationData,
    isLoading,
    isTokenValid,
    error,
  };
};

export default useVerifyProfessionalToken;
