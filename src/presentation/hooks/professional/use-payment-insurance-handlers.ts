import { useCallback, useEffect, useState } from "react";
import { ListeAssurances } from "@/domain/models/AssuranceProfessionnel.ts";
import { useAppDispatch } from "../redux.ts";
import {
  addProfessionalInsurance,
  removeProfessionalInsurance,
  updatePaymentMethods,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";
import useInsuranceProfessional from "../use-insurance.ts";

/**
 * Hook personnalisé pour gérer les handlers de paiement et d'assurance
 *
 * @description Ce hook encapsule toute la logique métier liée aux modes
 * de paiement et aux assurances du professionnel. Il utilise l'architecture
 * Domain-Driven Design avec des usecases et repositories spécialisés.
 */
export const usePaymentInsuranceHandlers = (id?: number) => {
  const dispatch = useAppDispatch();
  const [availableInsurances, setAvailableInsurances] = useState<
    ListeAssurances[]
  >([]);
  const { getInsurances } = useInsuranceProfessional();

  /**
   * Recupere la liste des assurances disponibles
   */
  useEffect(() => {
    const fetchInsurances = async () => {
      try {
        const assurancesData = await getInsurances();
        if (assurancesData) {
          setAvailableInsurances(assurancesData);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des assurances:", error);
      }
    };

    if (availableInsurances.length === 0) {
      fetchInsurances();
    }
  }, [getInsurances]);

  /**
   * Handler pour la sauvegarde des modes de paiement
   * Met à jour le champ modes_paiement_acceptes du professionnel
   */
  const handleSavePaymentMethods = useCallback(
    async (paymentMethods: string) => {
      try {
        const result = await dispatch(
          updatePaymentMethods({
            professionalId: id,
            paymentMethods,
          })
        );
        return result !== null;
      } catch (error) {
        console.error(
          "Erreur lors de la sauvegarde des modes de paiement:",
          error
        );
        return false;
      }
    },
    [id, dispatch]
  );

  /**
   * Handler pour l'ajout d'une assurance
   */
  const handleAddInsurance = useCallback(
    async (newInsurances: ListeAssurances) => {
      if (!id) {
        console.error("ID professionnel introuvable");
        return false;
      }
      try {
        const result = await dispatch(
          addProfessionalInsurance({
            assurance: {
              id_professionnel: id,
              id_assurance: newInsurances.id,
            },
          })
        ).unwrap();
        return result !== null;
      } catch (error) {
        console.error("Erreur lors de l'ajout de l'assurance:", error);
        return false;
      }
    },
    [id, dispatch]
  );

  /**
   * Handler pour la suppression d'une assurance
   * Supprime l'association entre le professionnel et l'assurance
   */
  const handleRemoveInsurance = useCallback(
    async (insuranceId: number) => {
      try {
        const result = await dispatch(
          removeProfessionalInsurance(insuranceId)
        ).unwrap();
        return result !== null;
      } catch (error) {
        console.error("Erreur lors de la suppression de l'assurance:", error);
        return false;
      }
    },
    [dispatch]
  );

  return {
    availableInsurances,
    handleSavePaymentMethods,
    handleAddInsurance,
    handleRemoveInsurance,
  };
};
