import { useCallback } from "react"
import { useAppDispatch } from "../redux"
import { addExpertise } from "@/application/slices/professionnal/professionalProfileSlice"
import { ExpertiseProfessionnel } from "@/domain/models/ExpertiseProfessionnel"

const useExpertiseHandlers = (idProfessionnel?: number) => {
  const dispatch = useAppDispatch()

  const createExpertise = useCallback(async (newExpertise: Omit<ExpertiseProfessionnel, "id" | "id_professionnel">) => {
    if (!idProfessionnel) return false
    try {
      const formatedExpertise: Omit<ExpertiseProfessionnel, "id"> = {
        ...newExpertise,
        id_professionnel: idProfessionnel
      }
      const data = await dispatch(addExpertise(formatedExpertise))

      return data !== null

    } catch (error) {
      console.log("Erreur lors de la creation de l'expertise", error)
      return false
    }
  }, [dispatch, idProfessionnel])

  const deleteExpertise = useCallback(async (expertiseId: number) => {
    if (!idProfessionnel) return
    try {
      const data = await dispatch(deleteExpertise(expertiseId))

      return data !== null
    } catch (error) {
      console.log("Erreur lors de la suppression de l'expertise", error)
      return false
    }
  }, [dispatch, idProfessionnel])

  return {
    createExpertise,
    deleteExpertise
  }
}

export default useExpertiseHandlers

