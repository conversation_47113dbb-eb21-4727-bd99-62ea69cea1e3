import { useEffect, useState } from "react";
import GetProfessionalSpecialitiesRepository from "@/infrastructure/repositories/professionalSpecialities/GetProfessionalSpecialitiesRepository";
import { SpecialiteProfessionnel } from "@/domain/models";
import { useToast } from "../use-toast";

const useProfessionalSpecialities = (professionalId: number) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [specialities, setSpecialities] = useState<SpecialiteProfessionnel[]>(
    []
  );
  const toast = useToast();

  const specialitiesRepository = new GetProfessionalSpecialitiesRepository();

  const fetchSpecialities = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await specialitiesRepository.execute(professionalId);
      setSpecialities(data);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Une erreur est survenue";
      setError(errorMessage);
      toast.error(errorMessage);
      setError("");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSpecialities();
  }, []);

  return {
    specialities,
    isLoading,
    error,
    refetch: fetchSpecialities,
  };
};

export default useProfessionalSpecialities;
