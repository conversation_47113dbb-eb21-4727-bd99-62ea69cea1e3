import { useForm, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useCallback, useState } from "react";
import { useError<PERSON>and<PERSON> } from "@/presentation/hooks/use-error-handler";

/**
 * Interface pour les options du hook useSectionForm
 */
interface SectionFormOptions<T extends z.ZodType> {
  /** Schéma Zod pour la validation */
  schema: T;
  /** Données initiales du formulaire */
  defaultValues: z.infer<T>;
  /** Fonction de sauvegarde */
  onSave: (data: z.infer<T>) => Promise<boolean>;
  /** Fonction appelée après une sauvegarde réussie */
  onSaveSuccess?: (data: z.infer<T>) => void;
  /** Nom de la section pour les messages d'erreur */
  sectionName: string;
  /** Mode de validation (par défaut: "onChange") */
  mode?: "onChange" | "onBlur" | "onSubmit" | "onTouched" | "all";
  /**
   * Permet de sauter la validation (ex: lorsqu'on ne fait que supprimer)
   * Si la fonction retourne true, la sauvegarde bypass la validation et appelle onSave directement.
   */
  canSkipValidation?: () => boolean;
}

/**
 * Interface de retour du hook useSectionForm
 */
interface SectionFormReturn<T extends z.ZodType>
  extends UseFormReturn<z.infer<T>> {
  /** Fonction de sauvegarde avec gestion d'erreurs */
  save: () => Promise<void>;
  /** Fonction d'annulation */
  cancel: () => void;
  /** Indique si la sauvegarde est en cours */
  isSaving: boolean;
  /** Indique si le formulaire a des modifications */
  hasChanges: boolean;
  /** Réinitialise le formulaire avec de nouvelles valeurs */
  resetWithValues: (newValues: z.infer<T>) => void;
}

/**
 * Hook personnalisé pour gérer les formulaires de section avec react-hook-form
 *
 * @description Ce hook encapsule la logique commune à toutes les sections
 * éditables en utilisant react-hook-form et Zod pour la validation.
 * Il fournit une interface cohérente pour l'édition de toutes les sections du profil.
 *
 * @example
 * ```tsx
 * const {
 *   control,
 *   register,
 *   formState: { errors },
 *   save,
 *   cancel,
 *   isSaving,
 *   hasChanges
 * } = useSectionForm({
 *   schema: presentationSchema,
 *   defaultValues: { presentation: "..." },
 *   onSave: async (data) => await updatePresentation(data.presentation),
 *   sectionName: "présentation"
 * });
 * ```
 */
export const useSectionForm = <T extends z.ZodType>({
  schema,
  defaultValues,
  onSave,
  onSaveSuccess,
  sectionName,
  mode = "onSubmit",
  canSkipValidation,
}: SectionFormOptions<T>): SectionFormReturn<T> => {
  const [isSaving, setIsSaving] = useState(false);
  const { handleApiError, handleSuccess } = useErrorHandler();

  // Configuration du formulaire react-hook-form
  const form = useForm<z.infer<T>>({
    resolver: zodResolver(schema),
    mode,
    defaultValues,
  });

  const { handleSubmit, reset, formState, watch } = form;
  const { isDirty } = formState;

  // Surveille les changements pour déterminer si le formulaire a été modifié
  const hasChanges = isDirty;

  /**
   * Fonction de sauvegarde avec gestion d'erreurs
   */
  const save = useCallback(async (): Promise<void> => {
    try {
      setIsSaving(true);

      // Si une condition de bypass est définie et vraie, on saute la validation
      if (canSkipValidation && canSkipValidation()) {
        const values = form.getValues();
        const success = await onSave(values);
        if (success) {
          handleSuccess(`Modification de ${sectionName}`);
          reset(values);
          if (onSaveSuccess) {
            onSaveSuccess(values);
          }
          return; // fin de la sauvegarde sans passer par handleSubmit
        } else {
          throw new Error("Échec de la sauvegarde");
        }
      }

      // handleSubmit retourne une fonction qui doit être appelée
      // Cette fonction ne s'exécute que si la validation réussit
      const submitHandler = handleSubmit(async (data) => {
        const success = await onSave(data);

        if (success) {
          handleSuccess(`Modification de ${sectionName}`);

          // Réinitialise le formulaire avec les nouvelles valeurs pour marquer comme "non modifié"
          reset(data);

          if (onSaveSuccess) {
            onSaveSuccess(data);
          }
        } else {
          throw new Error("Échec de la sauvegarde");
        }
      });

      // Appeler la fonction retournée par handleSubmit
      // Si la validation échoue, submitHandler ne fait rien
      // Nous devons détecter cela en vérifiant les erreurs du formulaire
      await submitHandler();

      // Vérifier s'il y a des erreurs de validation
      // Si des erreurs existent, cela signifie que la validation a échoué
      const formErrors = form.formState.errors;
      if (Object.keys(formErrors).length > 0) {
        throw new Error("Validation échouée");
      }
    } catch (error) {
      // Les erreurs de validation sont automatiquement gérées par react-hook-form
      // On ne gère ici que les erreurs de l'API
      if (
        error instanceof Error &&
        error.message !== "Échec de la sauvegarde" &&
        error.message !== "Validation échouée"
      ) {
        handleApiError(error, `la modification de ${sectionName}`);
      }
      throw error; // Re-throw pour que le composant puisse gérer l'état d'édition
    } finally {
      setIsSaving(false);
    }
  }, [
    handleSubmit,
    onSave,
    onSaveSuccess,
    sectionName,
    handleApiError,
    handleSuccess,
    reset,
    form.formState.errors,
    form,
    canSkipValidation,
  ]);

  /**
   * Fonction d'annulation
   */
  const cancel = useCallback(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  /**
   * Réinitialise le formulaire avec de nouvelles valeurs
   */
  const resetWithValues = useCallback(
    (newValues: z.infer<T>) => {
      reset(newValues);
    },
    [reset]
  );

  return {
    ...form,
    save,
    cancel,
    isSaving,
    hasChanges,
    resetWithValues,
  };
};
