import { useCallback } from "react";
import { useAppDispatch } from "../redux.ts";
import {
  createProfessionalLanguage,
  deleteProfessionalLanguage,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";

const useProfessionalLanguageHandlers = (userId?: number) => {
  const dispatch = useAppDispatch();

  const handleAddLanguages = useCallback(
    async (languageNames: string[]) => {
      if (!userId) return false;
      try {
        const languages = languageNames.map((name) => ({
          nom_langue: name.trim(),
          id_professionnel: userId,
        }));

        const result = await dispatch(
          createProfessionalLanguage({
            languages,
          })
        );

        return result !== null;
      } catch (error) {
        console.error("Erreur lors de l'ajout des langues:", error);
        return false;
      }
    },
    [dispatch, userId]
  );

  const handleDeleteLanguage = useCallback(
    async (languageId: number) => {
      try {
        await dispatch(deleteProfessionalLanguage(languageId));
        return true;
      } catch (error) {
        console.error("Erreur lors de la suppression de la langue:", error);
        return false;
      }
    },
    [dispatch]
  );

  return {
    handleAddLanguages,
    handleDeleteLanguage,
  };
};

export default useProfessionalLanguageHandlers;
