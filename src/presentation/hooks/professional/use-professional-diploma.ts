import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel.ts";
import { useCallback, useEffect, useRef, useState } from "react";
import { useToast } from "../use-toast.ts";
import {
  CreateProfessionalDiplomaRepository,
  DeleteProfessionalDiplomaRepository,
  GetProfessionalDiplomasByProfessionalIdRepository,
  UpdateProfessionalDiplomaRepository,
} from "@/infrastructure/repositories/professionalDiploma";
import GetProfessionalDiplomasByProfessionalIdUsecase from "@/domain/usecases/professionalDiploma/GetProfessionalDiplomasByProfessionalIdUsecase.ts";
import CreateProfessionalDiplomaUsecase from "@/domain/usecases/professionalDiploma/CreateProfessionalDiplomaUsecase.ts";
import DeleteProfessionalDiplomaUsecase from "@/domain/usecases/professionalDiploma/DeleteProfessionalDiplomaUsecase.ts";
import UpdateProfessionalDiplomaUsecase from "@/domain/usecases/professionalDiploma/UpdateProfessionalDiplomaUsecase.ts";

/**
 * Hook dédié à la gestion des diplômes d’un professionnel
 *
 * - Récupération des diplômes par `professionalId`
 * - Création, mise à jour, suppression
 * - Fournit un `refetch` pour synchroniser l’UI après opérations
 */
const useProfessionalDiploma = (professionalId?: number) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [diplomas, setDiplomas] = useState<DiplomeProfessionnel[]>([]);
  const toast = useToast();
  const toastRef = useRef(toast);
  useEffect(() => {
    toastRef.current = toast;
  }, [toast]);

  /**
   * Récupère les diplômes du professionnel
   */
  const fetchDiplomas = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const getRepo = new GetProfessionalDiplomasByProfessionalIdRepository();
      const getUsecase = new GetProfessionalDiplomasByProfessionalIdUsecase(
        getRepo
      );
      const data = await getUsecase.execute(professionalId);
      setDiplomas(data);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Une erreur est survenue";
      setError(errorMessage);
      toastRef.current.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [professionalId]);

  useEffect(() => {
    if (professionalId) {
      fetchDiplomas();
    }
  }, [professionalId, fetchDiplomas]);

  /**
   * Ajoute un diplôme
   */
  const handleAddDiploma = async (
    diploma: Omit<DiplomeProfessionnel, "id">
  ): Promise<DiplomeProfessionnel | null> => {
    try {
      const repo = new CreateProfessionalDiplomaRepository();
      const usecase = new CreateProfessionalDiplomaUsecase(repo);
      const result = await usecase.execute([diploma]);
      return result[0];
    } catch (err) {
      console.error("Erreur lors de l'ajout du diplôme:", err);
      toastRef.current.error("Erreur lors de l'ajout du diplôme");
      return null;
    }
  };

  /**
   * Supprime un diplôme
   */
  const handleDeleteDiploma = async (diplomaId: number): Promise<boolean> => {
    try {
      const repo = new DeleteProfessionalDiplomaRepository();
      const usecase = new DeleteProfessionalDiplomaUsecase(repo);
      await usecase.execute(diplomaId);
      return true;
    } catch (err) {
      console.error("Erreur lors de la suppression du diplôme:", err);
      toastRef.current.error("Erreur lors de la suppression du diplôme");
      return false;
    }
  };

  /**
   * Met à jour un diplôme
   */
  const handleUpdateDiploma = async (
    diplomaId: number,
    diploma: Partial<DiplomeProfessionnel>
  ): Promise<boolean> => {
    try {
      const repo = new UpdateProfessionalDiplomaRepository();
      const usecase = new UpdateProfessionalDiplomaUsecase(repo);
      await usecase.execute(diplomaId, diploma);
      return true;
    } catch (err) {
      console.error("Erreur lors de la mise à jour du diplôme:", err);
      toastRef.current.error("Erreur lors de la mise à jour du diplôme");
      return false;
    }
  };

  return {
    diplomas,
    isLoading,
    error,
    refetch: fetchDiplomas,
    handleAddDiploma,
    handleDeleteDiploma,
    handleUpdateDiploma,
  };
};

export default useProfessionalDiploma;
