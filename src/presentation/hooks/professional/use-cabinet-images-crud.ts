import { useState, useCallback } from "react";
import { useToast } from "@/presentation/hooks/use-toast";
import { Photo } from "@/domain/models/Photo";
import { PhotoTypeEnum } from "@/domain/models/enums";

/**
 * Hook personnalisé pour les opérations CRUD des images du cabinet
 *
 * @description
 * Ce hook encapsule toute la logique de gestion des images du cabinet médical :
 * - Upload multiple d'images
 * - Suppression d'images individuelles
 * - Récupération des images existantes
 * - États de chargement et gestion d'erreurs
 * - Feedback utilisateur via toasts
 */
export const useCabinetImagesCRUD = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const toast = useToast();

  /**
   * Upload plusieurs images du cabinet
   *
   * @param userId - ID de l'utilisateur
   * @param files - Fichiers images à uploader
   * @returns Photos uploadées ou null en cas d'erreur
   */
  const uploadImages = useCallback(
    async (userId: number, files: File[]): Promise<Photo[] | null> => {
      try {
        setIsUploading(true);

        const result = await uploadCabinetImagesUsecase.execute(userId, files);

        if (result.success && result.photos) {
          if (result.partialSuccess && result.errors) {
            // Succès partiel
            toast.success(
              `${result.photos.length} image(s) uploadée(s) avec succès`
            );
            toast.error(`Erreurs : ${result.errors.join(", ")}`);
          } else {
            // Succès complet
            toast.success(
              `${result.photos.length} image(s) uploadée(s) avec succès`
            );
          }
          return result.photos;
        } else {
          // Échec complet
          const errorMessage =
            result.errors?.join(", ") || "Erreur lors de l'upload des images";
          toast.error(errorMessage);
          return null;
        }
      } catch (error) {
        console.error("Erreur lors de l'upload des images:", error);
        toast.error("Erreur inattendue lors de l'upload");
        return null;
      } finally {
        setIsUploading(false);
      }
    },
    [uploadCabinetImagesUsecase, toast]
  );

  /**
   * Supprime une image du cabinet
   *
   * @param photoId - ID de la photo à supprimer
   * @returns true si la suppression a réussi
   */
  const deleteImage = useCallback(
    async (photoId: number): Promise<boolean> => {
      try {
        setIsDeleting(true);

        const result = await deleteCabinetImageUsecase.execute(photoId);

        if (result.success) {
          toast.success("Image supprimée avec succès");
          return true;
        } else {
          toast.error(
            result.error || "Erreur lors de la suppression de l'image"
          );
          return false;
        }
      } catch (error) {
        console.error("Erreur lors de la suppression de l'image:", error);
        toast.error("Erreur inattendue lors de la suppression");
        return false;
      } finally {
        setIsDeleting(false);
      }
    },
    [deleteCabinetImageUsecase, toast]
  );

  /**
   * Récupère les images du cabinet d'un utilisateur
   *
   * @param userId - ID de l'utilisateur
   * @returns Liste des images du cabinet
   */
  const getCabinetImages = useCallback(
    async (userId: number): Promise<Photo[]> => {
      try {
        const photos = await getPhotosByUserRepository.execute(
          userId,
          PhotoTypeEnum.PRESENTATION
        );

        return photos || [];
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des images du cabinet:",
          error
        );
        return [];
      }
    },
    [getPhotosByUserRepository]
  );

  /**
   * Valide les fichiers avant upload
   *
   * @param files - Fichiers à valider
   * @param currentImageCount - Nombre d'images actuelles
   * @param maxImages - Nombre maximum d'images autorisées
   * @returns Résultat de la validation
   */
  const validateFiles = useCallback(
    (
      files: File[],
      currentImageCount: number,
      maxImages: number
    ): { valid: boolean; errors: string[] } => {
      const errors: string[] = [];

      // Vérifier le nombre total d'images
      if (currentImageCount + files.length > maxImages) {
        errors.push(
          `Vous ne pouvez ajouter que ${maxImages - currentImageCount} image(s) supplémentaire(s).`
        );
      }

      // Valider chaque fichier
      files.forEach((file, index) => {
        // Validation du type de fichier
        const supportedTypes = [
          "image/jpeg",
          "image/jpg",
          "image/png",
          "image/webp",
        ];
        if (!supportedTypes.includes(file.type)) {
          errors.push(
            `Fichier ${index + 1} (${file.name}): Type non supporté. Utilisez JPG, PNG ou WebP.`
          );
        }

        // Validation de la taille du fichier (max 5MB)
        const maxSizeBytes = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSizeBytes) {
          errors.push(
            `Fichier ${index + 1} (${file.name}): Trop volumineux (${(file.size / 1024 / 1024).toFixed(2)}MB). Taille maximale : 5MB.`
          );
        }
      });

      return {
        valid: errors.length === 0,
        errors,
      };
    },
    []
  );

  return {
    // Actions
    uploadImages,
    deleteImage,
    getCabinetImages,
    validateFiles,

    // États
    isUploading,
    isDeleting,
    isLoading: isUploading || isDeleting,
  };
};
