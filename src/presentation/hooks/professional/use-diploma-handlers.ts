import { useCallback } from "react";
import { useAppDispatch } from "../redux.ts";
import {
  createProfessionalDiploma,
  deleteProfessionalDiploma,
  updateProfessionalDiploma,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";
import { DiplomeProfessionnel } from "@/domain/models";

const useDiplomaHandlers = (userId?: number) => {
  const dispatch = useAppDispatch();

  const handleAddDiploma = useCallback(
    async (data: Omit<DiplomeProfessionnel, "id" | "id_professionnel">) => {
      if (!userId) return false;

      try {
        const result = await dispatch(
          createProfessionalDiploma({
            diploma: {
              ...data,
              id_professionnel: userId,
            },
          })
        );

        return result !== null;
      } catch (error) {
        console.error("Erreur lors de l'ajout du diplôme:", error);
        return false;
      }
    },
    [dispatch, userId]
  );

  const handleDeleteDiploma = useCallback(
    async (diplomaId: number) => {
      try {
        await dispatch(deleteProfessionalDiploma(diplomaId));
        return true;
      } catch (error) {
        console.error("Erreur lors de la suppression du diplôme:", error);
        return false;
      }
    },
    [dispatch]
  );

  const handleUpdateDiploma = useCallback(
    async (diplomaId: number, data: Partial<DiplomeProfessionnel>) => {
      try {
        const result = await dispatch(
          updateProfessionalDiploma({ diplomaId, diploma: data })
        );
        return result !== null;
      } catch (error) {
        console.error("Erreur lors de la mise à jour du diplôme:", error);
        return false;
      }
    },
    [dispatch]
  );

  return {
    handleAddDiploma,
    handleDeleteDiploma,
    handleUpdateDiploma,
  };
};

export default useDiplomaHandlers;
