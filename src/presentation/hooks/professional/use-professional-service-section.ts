import { professionnels_types_consultation_enum } from "@/domain/models/enums/professionnelTypeConsultation.ts";
import { useAppDispatch } from "../redux.ts";
import { updateServices } from "@/application/slices/professionnal/professionalProfileSlice.ts";
import { useCallback } from "react";

/**
 * Interface pour les données des services
 */
export interface ServicesData {
  types_consultation: professionnels_types_consultation_enum;
  nouveau_patient_acceptes: boolean;
}

const useProfessionalServiceSection = (userId?: number) => {
  const dispatch = useAppDispatch();

  const handleUpdateProfessionalServiceSection = useCallback(
    async (servicesData: Partial<ServicesData>) => {
      if (!userId) {
        console.error("ID professionnel introuvable");
        return false;
      }
      try {
        const result = await dispatch(
          updateServices({
            professionalId: userId,
            servicesData: { ...servicesData },
          })
        ).unwrap();

        return result !== null;
      } catch (error) {
        console.error("Erreur lors de la mise à jour des services:", error);
        return false;
      }
    },
    [dispatch, userId]
  );
  return { handleUpdateProfessionalServiceSection };
};

export default useProfessionalServiceSection;
