import { useContactHandlers } from "./use-contact-handlers";
import { useEstablishmentHandlers } from "./use-establishment-handlers";
import { useMediaHandlers } from "./use-media-handlers";
import { usePaymentInsuranceHandlers } from "./use-payment-insurance-handlers";
import usePresentationHandlers from "./use-presentation-handlers.ts";
import useProfessionalInformationSectionHandler from "./use-professional-information-section-handler.ts";
import useProfessionalServiceSection from "./use-professional-service-section.ts";
import useProfessionalBaseSectionHandler from "./use-professional-base-info-section-handler.ts";
import useSpecialitiesHandlers from "./use-specialities-handlers.ts";
import { useAppSelector } from "../redux.ts";
import { useProfilePhotoUpload } from "./use-profile-photo-upload.ts";
import useDiplomaHandlers from "./use-diploma-handlers.ts";
import useExperienceHandlers from "./use-experience-handlers.ts";
import usePublicationHandlers from "./use-publication-handlers.ts";
import useProfessionalLanguageHandlers from "./use-professional-language-handlers.ts";
import { useCabinetImagesHandlers } from "./use-cabinet-images-handlers.ts";
import useKeywordsHandlers from "./use-keywords-handlers.ts";

/**
 * Hook principal pour gérer tous les handlers du profil professionnel
 *
 * @description Ce hook orchestre tous les handlers spécialisés et fournit
 * une interface unifiée pour la gestion du profil professionnel.
 */
export const useProfessionalProfileHandlers = (
  userId?: number,
  professionalId?: number
) => {
  // Hooks spécialisés
  const baseInfoHandlers = useProfessionalBaseSectionHandler(professionalId);
  const contactHandlers = useContactHandlers(userId, professionalId);
  const profilePhotoHandlers = useProfilePhotoUpload(userId);
  const presentationHandlers = usePresentationHandlers(professionalId);
  const specialitiesHandlers = useSpecialitiesHandlers(professionalId);
  const professionalInformationHandlers =
    useProfessionalInformationSectionHandler(professionalId);
  const serviceHandlers = useProfessionalServiceSection(professionalId);
  const establishmentHandlers = useEstablishmentHandlers(professionalId);
  const diplomaHandlers = useDiplomaHandlers(professionalId);
  const experienceHandlers = useExperienceHandlers(professionalId);
  const publicationHandlers = usePublicationHandlers(professionalId);
  const languageHandlers = useProfessionalLanguageHandlers(professionalId);
  const paymentInsuranceHandlers = usePaymentInsuranceHandlers(professionalId);
  const presentationPhotoHandlers = useCabinetImagesHandlers(userId);
  const keywordsHandlers = useKeywordsHandlers(professionalId);

  return {
    ...baseInfoHandlers,
    ...contactHandlers,
    ...profilePhotoHandlers,
    ...presentationHandlers,
    ...specialitiesHandlers,
    ...professionalInformationHandlers,
    ...serviceHandlers,
    ...establishmentHandlers,
    ...diplomaHandlers,
    ...experienceHandlers,
    ...publicationHandlers,
    ...languageHandlers,
    ...paymentInsuranceHandlers,
    ...presentationPhotoHandlers,
    ...keywordsHandlers,
  };
};
