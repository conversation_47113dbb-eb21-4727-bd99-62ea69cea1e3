import { useCallback } from "react";
import { ContactFormData } from "@/shared/schemas/ProfessionalProfileSchemas";
import {
  createProfessionalContact,
  deleteProfessionalContact,
  updateProfessionalContact,
  updateAuthentificationUser,
  updateUser,
  updateProfessional,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";
import { useAppDispatch } from "../redux.ts";

/**
 * Hook personnalisé pour gérer les handlers de contact
 *
 * @description Ce hook encapsule toute la logique métier liée aux informations
 * de contact du professionnel, séparant ainsi les responsabilités.
 */
export const useContactHandlers = (
  userId?: number,
  professionalId?: number
) => {
  const dispatch = useAppDispatch();
  /**
   * Handler pour la sauvegarde des informations de contact
   */
  const handleCreateContact = useCallback(
    async (
      contactData: ContactFormData,
      contactId: number,
      utilisateur_id: number,
      professionalId: number
    ) => {
      if (!professionalId || !utilisateur_id) {
        console.error("ID professionnel introuvable");
        return null;
      }
      try {
        // 1. Authentification supabase
        const updatedAuthentificationUser = await dispatch(
          updateAuthentificationUser({
            email: contactData.email,
          })
        );

        // 2. Table 'utilisateurs':
        const updatedUser = await dispatch(
          updateUser({
            userId: utilisateur_id,
            userData: {
              email: contactData.email,
            },
          })
        );

        // 3. Table 'professionnels':
        const updatedProfessional = await dispatch(
          updateProfessional({
            professionalId,
            baseInfo: {
              adresse: contactData.adresse,
              fokontany: contactData.fokontany,
              informations_acces: contactData.infoAcces,
            },
          })
        );

        // 4. Table 'contacts':
        if (contactId === 0) {
          // Création d'un nouveau contact
          const newContact = await dispatch(
            createProfessionalContact({
              contact: {
                numero: contactData.telephone,
                utilisateur_id: utilisateur_id,
              },
            })
          );

          const out = {
            contact: newContact[0],
            user: updatedUser,
            professional: updatedProfessional,
            authentification: updatedAuthentificationUser,
          };

          return out !== null;
        } else {
          // Mise à jour d'un contact existant
          const updatedContact = await dispatch(
            updateProfessionalContact({
              contactId,
              contactData: {
                numero: contactData.telephone,
                utilisateur_id: utilisateur_id,
              },
            })
          );

          const out = {
            contact: updatedContact,
            user: updatedUser,
            professional: updatedProfessional,
            authentification: updatedAuthentificationUser,
          };
          return out !== null;
        }
      } catch (error) {
        console.error(
          "Erreur lors de la sauvegarde des informations de contact:",
          error
        );
        return false;
      }
    },
    []
  );

  const handleDeleteContact = useCallback(async (contactId: number) => {
    try {
      await dispatch(deleteProfessionalContact(contactId));
      return true;
    } catch (error) {
      console.error("Erreur lors de la suppression du contact:", error);
      return false;
    }
  }, []);

  return {
    handleCreateContact,
    handleDeleteContact,
  };
};
