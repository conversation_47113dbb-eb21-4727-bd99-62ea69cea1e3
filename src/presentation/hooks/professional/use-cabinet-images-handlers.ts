import { useState, useCallback } from "react";
import { useToast } from "@/presentation/hooks/use-toast";
import { useAppDispatch } from "../redux.ts";
import {
  uploadPresentationPhoto as uploadPresentationPhotoAction,
  deletePresentationPhoto as deletePresentationPhotoAction,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";

/**
 * Hook personnalisé pour la gestion de l'upload de photo de presentation
 *
 * @description
 * Ce hook encapsule toute la logique d'upload, de mise à jour et de suppression
 * des photos de presentation professionnelles. Il gère :
 * - Upload de nouvelle photo de presentation
 * - Mise à jour d'une photo existante
 * - Suppression de photo de presentation
 * - États de chargement et gestion d'erreurs
 * - Feedback utilisateur via toasts
 */
export const useCabinetImagesHandlers = (id?: number) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const toast = useToast();
  const dispatch = useAppDispatch();

  /**
   * Upload une nouvelle photo de presentation
   *
   * @param userId - ID de l'utilisateur
   * @param file - Fichier image à uploader
   * @returns Photo uploadée ou null en cas d'erreur
   */
  const uploadPresentationPhoto = useCallback(
    async (file: File): Promise<boolean> => {
      if (!id) {
        console.error("ID utilisateur non trouvé");
        return false;
      }

      try {
        setIsUploading(true);

        const result = await dispatch(
          uploadPresentationPhotoAction({ userId: id, file })
        );

        if (result) {
          toast.success("Photo de presentation uploadée avec succès");
          return result !== null;
        } else {
          return false;
        }
      } catch (error) {
        console.error("Erreur lors de l'upload de la photo:", error);
        toast.error("Erreur inattendue lors de l'upload");
        return false;
      } finally {
        setIsUploading(false);
      }
    },
    [toast]
  );

  /**
   * Supprime la photo de presentation actuelle
   *
   * @param photoId - ID de la photo à supprimer
   * @returns true si la suppression a réussi
   */
  const deletePresentationPhoto = useCallback(
    async (photoId: number): Promise<boolean> => {
      try {
        setIsDeleting(true);

        const success = await dispatch(deletePresentationPhotoAction(photoId));

        if (success.payload) {
          toast.success("Photo de presentation supprimée avec succès");
          return true;
        } else {
          toast.error("Erreur lors de la suppression de la photo");
          return false;
        }
      } catch (error) {
        console.error("Erreur lors de la suppression de la photo:", error);
        toast.error("Erreur inattendue lors de la suppression");
        return false;
      } finally {
        setIsDeleting(false);
      }
    },
    [toast]
  );

  return {
    // Actions
    uploadPresentationPhoto,
    deletePresentationPhoto,

    // États
    isUploading,
    isDeleting,
    isLoading: isUploading || isDeleting,
  };
};
