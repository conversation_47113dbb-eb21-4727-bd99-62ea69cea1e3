import { updateProfessionalInformationSection } from "@/application/slices/professionnal/professionalProfileSlice.ts";
import { useAppDispatch } from "../redux.ts";

const useProfessionalInformationSectionHandler = (userId?: number) => {
  const dispatch = useAppDispatch();
  const handleSaveProfessionalInformation = async (professionalData: {
    numero_ordre?: string;
    raison_sociale?: string;
    nif?: string;
    stat?: string;
  }) => {
    if (!userId) {
      console.error("ID professionnel introuvable");
      return false;
    }
    try {
      const result = await dispatch(
        updateProfessionalInformationSection({
          professionalId: userId,
          professionalData: { ...professionalData },
        })
      ).unwrap();

      return result !== null;
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour des informations professionnelles:",
        error
      );
      return false;
    }
  };
  return { handleSaveProfessionalInformation };
};

export default useProfessionalInformationSectionHandler;
