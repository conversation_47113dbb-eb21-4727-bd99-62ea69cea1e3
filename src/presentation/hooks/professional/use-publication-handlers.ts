import { useCallback } from "react";
import { useAppDispatch } from "../redux.ts";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel.ts";
import {
  createProfessionalPublication,
  deleteProfessionalPublication,
  updateProfessionalPublication,
} from "@/application/slices/professionnal/professionalProfileSlice.ts";

const usePublicationHandlers = (userId?: number) => {
  const dispatch = useAppDispatch();

  const handleAddPublication = useCallback(
    async (data: Omit<PublicationProfessionnel, "id" | "id_professionnel">) => {
      if (!userId) return false;

      try {
        const result = await dispatch(
          createProfessionalPublication({
            professionalId: userId,
            publication: {
              ...data,
              id_professionnel: userId,
            },
          })
        );

        return result !== null;
      } catch (error) {
        console.error("Erreur lors de l'ajout de la publication:", error);
        return false;
      }
    },
    [dispatch, userId]
  );

  const handleUpdatePublication = useCallback(
    async (publicationId: number, data: Partial<PublicationProfessionnel>) => {
      try {
        const result = await dispatch(
          updateProfessionalPublication({ publicationId, publication: data })
        );
        return result !== null;
      } catch (error) {
        console.error(
          "Erreur lors de la mise à jour de la publication:",
          error
        );
        return false;
      }
    },
    [dispatch]
  );

  const handleDeletePublication = useCallback(
    async (publicationId: number) => {
      try {
        await dispatch(deleteProfessionalPublication(publicationId));
        return true;
      } catch (error) {
        console.error(
          "Erreur lors de la suppression de la publication:",
          error
        );
        return false;
      }
    },
    [dispatch]
  );

  return {
    handleAddPublication,
    handleUpdatePublication,
    handleDeletePublication,
  };
};

export default usePublicationHandlers;
