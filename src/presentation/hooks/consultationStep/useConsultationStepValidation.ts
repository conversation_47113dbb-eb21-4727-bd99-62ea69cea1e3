import { useCallback } from "react";
import { FieldErrors, UseFormTrigger } from "react-hook-form";
import { useToast } from "@/presentation/hooks/use-toast";
import { getStepErrorCount, scrollToFirstError } from "../authentification";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import { CONULTATION_STEP_FIELDS } from "@/shared/constants/ConsultaionStepConfig";

interface UseConsultationStepValidationProps {
  trigger: UseFormTrigger<ConsultationStepFormData>;
  errors: FieldErrors<ConsultationStepFormData>;
}

interface UseConsultationStepValidationReturn {
  validateCurrentStep: (step: number) => Promise<boolean>;
  validateAllSteps: () => Promise<boolean>;
  validateNotProche: () => Promise<boolean>;
  showValidationError: (step: number) => void;
}

/**
 * Hook personnalisé pour gérer la validation du formulaire d'inscription patient
 *
 * Responsabilités :
 * - Validation des étapes individuelles
 * - Validation complète du formulaire
 * - Gestion des messages d'erreur de validation
 * - Défilement vers les erreurs
 */
export const useConsultationStepValidation = ({
  trigger,
  errors,
}: UseConsultationStepValidationProps): UseConsultationStepValidationReturn => {
  const toast = useToast();

  /**
   * Valide une étape spécifique du formulaire
   */
  const validateCurrentStep = useCallback(
    async (step: number): Promise<boolean> => {
      const fieldsToValidate =
        CONULTATION_STEP_FIELDS[step as keyof typeof CONULTATION_STEP_FIELDS];
      if (!fieldsToValidate) return true;

      // Déclencher la validation pour les champs de l'étape actuelle
      const isValid = await trigger(fieldsToValidate);
      return isValid;
    },
    [trigger]
  );

  /**
   * Valide tous les champs du formulaire
   */
  const validateAllSteps = useCallback(async (): Promise<boolean> => {
    const isFormValid = await trigger();
    return isFormValid;
  }, [trigger]);
  /**
   * Validation sans autentification
   */
  const validateNotProche = useCallback(async (): Promise<boolean> => {
    const isFormValid = await trigger(["categorie", "consultationMotif"]);
    return isFormValid;
  }, [trigger]);

  /**
   * Affiche un message d'erreur de validation et fait défiler vers la première erreur
   */
  const showValidationError = useCallback(
    (step: number): void => {
      toast.error(
        "Cyertaines informations sont manquantes ou incorrectes. Merci de les vérifier."
      );
      // Faire défiler vers le premier champ en erreur
      scrollToFirstError(step, errors);
    },
    [errors, toast]
  );

  return {
    validateCurrentStep,
    validateAllSteps,
    validateNotProche,
    showValidationError,
  };
};
