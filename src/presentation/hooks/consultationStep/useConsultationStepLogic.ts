import { useFormStepper } from "@/presentation/hooks/useFormStepper";
import useConsultationStep from "./useConsultationStep";
import { useConsultationStepSubmission } from "./useConsultationStepSubmission";
import { useConsultationStepValidation } from "./useConsultationStepValidation";
import { useConsultationStepNavigation } from "./useConsultationStepNavigation";
import { CONSULTATION_STEPS } from "@/shared/constants/ConsultationtSteps";

export const useConsultationStepLogic = () => {
  // Gestion du stepper
  const { activeStep, handleNext, handleBack } = useFormStepper(
    CONSULTATION_STEPS.length
  );

  // Gestion du formulaire et de l'API
  const {
    isLoading,
    control,
    formState,
    confirmeAppointment,
    getValues,
    handleSubmit,
    register,
    setValue,
    watch,
    trigger,
  } = useConsultationStep();

  const errors = formState.errors;

  // Hooks spécialisés pour la logique métier
  const validation = useConsultationStepValidation({
    trigger,
    errors,
  });

  const navigation = useConsultationStepNavigation({
    activeStep,
    handleNext,
    handleBack,
    getValues,
    trigger,
    errors,
  });

  const submission = useConsultationStepSubmission({
    confirmeAppointment,
    trigger,
    getValues,
    errors,
  });

  // Interface unifiée pour le composant
  return {
    // État du stepper
    activeStep,

    // Contrôles du formulaire
    control,
    errors,
    getValues,
    register,
    setValue,
    watch,
    handleSubmit,
    isLoading,

    // Actions de navigation
    handleNextStep: navigation.handleNextStep,
    handlePreviousStep: navigation.handlePreviousStep,
    canNavigateToStep: navigation.canNavigateToStep,

    // Actions de validation
    validateCurrentStep: validation.validateCurrentStep,
    validateAllSteps: validation.validateAllSteps,

    // Action de soumission
    onSubmit: submission.onSubmit,

    // Utilitaires
    formatFormDataForSubmission: submission.formatFormDataForSubmission,
  };
};
