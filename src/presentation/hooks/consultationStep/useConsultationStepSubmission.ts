import { useCallback } from "react";
import { FieldErrors, UseFormGetValues, UseFormTrigger } from "react-hook-form";
import { useLocation } from "react-router-dom";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import { useToast } from "@/presentation/hooks/use-toast";
import { useConsultationStepValidation } from "./useConsultationStepValidation";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { formatDataReturn } from "./formatDataReturn";
import { RendezVous } from "@/domain/models";
import { getSelectedTimeSlot } from "./useSelectedTimeSlot.ts";

interface UseConsultationStepSubmissionProps {
  confirmeAppointment: (data: formatDataReturn) => Promise<RendezVous>;
  trigger: UseFormTrigger<ConsultationStepFormData>;
  getValues: UseFormGetValues<ConsultationStepFormData>;
  errors: FieldErrors<ConsultationStepFormData>;
}

interface UseConsultationStepSubmissionReturn {
  onSubmit: (patient_id: number) => Promise<RendezVous>;
  formatFormDataForSubmission: (
    formData: ConsultationStepFormData,
    patient_id: number
  ) => formatDataReturn;
}

/**
 * Hook personnalisé pour gérer la soumission du formulaire d'inscription patient
 *
 * Responsabilités :
 * - Validation finale avant soumission
 * - Formatage des données pour l'API
 * - Gestion de la soumission
 * - Navigation après succès
 * - Gestion des erreurs de soumission
 */
export const useConsultationStepSubmission = ({
  confirmeAppointment,
  trigger,
  getValues,
  errors,
}: UseConsultationStepSubmissionProps): UseConsultationStepSubmissionReturn => {
  const selectedTimeSlot = getSelectedTimeSlot();
  const toast = useToast();
  const { validateAllSteps, validateNotProche } = useConsultationStepValidation(
    {
      trigger,
      errors,
    }
  );

  /**
   * Formate les données du formulaire pour la soumission
   */
  const formatFormDataForSubmission = useCallback(
    (
      formData: ConsultationStepFormData,
      patient_id: number
    ): formatDataReturn => {
      const appointmentDate = new Date(selectedTimeSlot.date);
      const [hour, minute] = selectedTimeSlot.start.split(":");

      appointmentDate.setHours(
        Number.parseInt(hour),
        Number.parseInt(minute),
        0,
        0
      );

      return {
        rdv: {
          id_professionnel: selectedTimeSlot.id_professionnel,
          patient_id: patient_id,
          categorie: formData.categorie,
          date_rendez_vous: appointmentDate.toISOString(),
          est_absent: false,
          raison: formData.consultationReason || "",
          rappel_envoye: false,
          statut: rendez_vous_statut_enum.A_VENIR,
          cree_a: new Date(),
          mis_a_jour_a: new Date(),
          motif: formData.consultationMotif,
        },
        proche:
          formData.forWhom === "other"
            ? {
                id_patient: patient_id,
                date_naissance:
                  formData.procheInfo.date_naissance.toISOString(),
                nom: formData.procheInfo.nom,
                prenom: formData.procheInfo.prenom,
                sexe: formData.procheInfo.sexe,
                lien_parente: formData.procheInfo.lien_parente,
              }
            : null,
      };
    },
    []
  );

  /**
   * Fonction principale de soumission du formulaire
   */
  const onSubmit = useCallback(
    async (patient_id: number) => {
      try {
        // Validation finale de tous les champs
        const formData = getValues();
        console.log(formData);
        let isFormValid: boolean = false;
        if (formData.forWhom === "other") {
          isFormValid = await validateAllSteps();
        } else {
          isFormValid = await validateNotProche();
        }
        if (!isFormValid) {
          toast.error("Veuillez corriger les erreurs dans le formulaire");
          return;
        }

        // Récupération et formatage des données
        const appointmentData = formatFormDataForSubmission(
          formData,
          patient_id
        );

        // Soumission
        const response = await confirmeAppointment(appointmentData);
        return response;
      } catch (error) {
        console.error("Erreur lors de l'enregistrement:", error);
        toast.error(
          error?.message || "Une erreur est survenue lors de l'enregistrement"
        );
      }
    },
    [
      validateAllSteps,
      validateNotProche,
      getValues,
      formatFormDataForSubmission,
      confirmeAppointment,
      toast,
    ]
  );

  return {
    onSubmit,
    formatFormDataForSubmission,
  };
};
