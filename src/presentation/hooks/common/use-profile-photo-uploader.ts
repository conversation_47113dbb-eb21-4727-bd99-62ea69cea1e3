import { useCallback, useEffect, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";
import { UPLOAD_CONFIG } from "@/shared/constants/cabinetMedicalConfig";

/**
 * Hook personnalisé pour le composant ProfilePhotoUploader
 */
export const useProfilePhotoUploader = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const { watch, setValue, setError: setFormError, clearErrors } =
    useFormContext();
  const profileImageFile = watch("profileImageFile");
  const profileImagePreviewUrl = watch("profileImagePreviewUrl");

  // Synchroniser le previewUrl local avec celui du formulaire
  useEffect(() => {
    if (profileImagePreviewUrl && profileImagePreviewUrl !== previewUrl) {
      setPreviewUrl(profileImagePreviewUrl);
    }
  }, [profileImagePreviewUrl, previewUrl]);

  // Nettoyer l'URL de prévisualisation lorsque le composant est démonté
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const triggerFileInput = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleImageUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        // Vérifier la taille du fichier (max 5MB)
        const maxSizeBytes = UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB * 1024 * 1024;
        if (file.size > maxSizeBytes) {
          const errorMessage = `L'image (${
            (file.size / 1024 / 1024).toFixed(2)
          }MB) dépasse la limite de ${UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB`;
          setError(errorMessage);
          setFormError("profileImageFile", {
            type: "maxSize",
            message: errorMessage,
          });
          return;
        }

        // Effacer les erreurs précédentes
        setError(null);
        clearErrors("profileImageFile");

        // Stocker l'objet File directement dans le formulaire
        setValue("profileImageFile", file, {
          shouldValidate: true,
          shouldDirty: true,
        });

        // Créer une URL pour la prévisualisation
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl);
        }
        const objectUrl = URL.createObjectURL(file);
        setPreviewUrl(objectUrl);

        // Stocker l'URL de prévisualisation dans le formulaire pour la preview
        setValue("profileImagePreviewUrl", objectUrl, {
          shouldValidate: false,
          shouldDirty: true,
        });
      }
    },
    [setValue, previewUrl, setError, setFormError, clearErrors],
  );

  // Fonction pour supprimer l'image de profil
  const removeProfileImage = useCallback(() => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    setValue("profileImageFile", undefined, {
      shouldValidate: true,
      shouldDirty: true,
    });
    setValue("profileImagePreviewUrl", undefined, {
      shouldValidate: false,
      shouldDirty: true,
    });
    setError(null);
    clearErrors("profileImageFile");
  }, [previewUrl, setValue, setError, clearErrors]);

  return {
    profileImageFile,
    previewUrl,
    fileInputRef,
    triggerFileInput,
    handleImageUpload,
    removeProfileImage,
    error,
  };
};
