import { useTheme } from "@mui/material";
import { useCabinetMedicalPreview } from "@/presentation/hooks/preview";
import { useCabinetMedicalStateContext } from "@/presentation/contexts/useContext";

/**
 * Hook personnalisé pour le composant FormFooter
 */
export const useFormFooter = () => {
  const { state, dispatch } = useCabinetMedicalStateContext();

  // Utiliser notre hook personnalisé pour la prévisualisation
  const { openPreview: handlePreview } = useCabinetMedicalPreview();

  return {
    isLoading: state.isLoading,
    handlePreview,
  };
};
