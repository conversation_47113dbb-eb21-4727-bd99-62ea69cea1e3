import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { DispositifMedicaux } from '@/domain/models'
import {
  createDispositifMedicaux,
  getDispositifMedicaux,
  updateDispositifMedicaux,
  deleteDispositifMedicaux,
  setSelectedDispositifMedicaux,
  setMarque,
  setModele,
  setReference,
  setDateAcquisition,
  setProchaineDate,
  setRemarks,
  resetDispositifMedicauxState,
  clearSelectedDispositifMedicaux
} from '@/application/slices/professionnal/dispositifMedicauxSlice'
import { getLocalISOString } from '@/shared/utils/getLocalISOString'

export const useDispositifMedicaux = () => {
  const dispatch = useDispatch<AppDispatch>()
  const {
    dispositifMedicaux,
    selectedDispositifMedicaux,
    dispositifMedicauxState,
    loading,
    error
  } = useSelector((state: RootState) => state.dispositifMedicaux)

  const create = useCallback(
    async (data: Omit<DispositifMedicaux, "id">[]) => {
      const register = await dispatch(createDispositifMedicaux(data))
      const dispositifs = register.payload as DispositifMedicaux[]
      return dispositifs.map(dispositif => {
        return {
          id: dispositif.id,
          detail: dispositif.nom
        }
      })
    },
    [dispatch]
  )

  const getAll = useCallback(async (carnetId: number) => {
    await dispatch(getDispositifMedicaux(carnetId))
  }, [dispatch])

  const update = useCallback(
    async (id: number, data: Partial<DispositifMedicaux>) => {
      await dispatch(updateDispositifMedicaux({ id, data }))
    },
    [dispatch]
  )

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteDispositifMedicaux(id))
    },
    [dispatch]
  )

  const select = useCallback(
    (dispositifMedicaux: DispositifMedicaux | null) => {
      dispatch(setSelectedDispositifMedicaux(dispositifMedicaux))
    },
    [dispatch]
  )
  
  const handleMarqueChange = (item: string, value: string) => {
    dispatch(setMarque({item, value}))
  };
  
  const handleModeleChange = (item: string, value: string) => {
    dispatch(setModele({item, value}))
  };
  
  const handleReferenceChange = (item: string, value: string) => {
    dispatch(setReference({item, value}))
  };

  const handleDateAcquisitionChange = (item: string, value: Date | null) => {
    if (!value) return;
    dispatch(setDateAcquisition({item, value: getLocalISOString(value)}));
  };
  
  const handleProchaineDateChange = (item: string, value: Date | null) => {
    if (!value) return;
    dispatch(setProchaineDate({item, value: getLocalISOString(value)}));
  };

  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({item, value}))
  };

  const resetState = () => {
    dispatch(resetDispositifMedicauxState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedDispositifMedicaux())
  }, [dispatch])

  return {
    dispositifMedicaux,
    selectedDispositifMedicaux,
    dispositifMedicauxState,
    loading,
    error,
    create,
    getAll,
    update,
    remove,
    select,
    handleMarqueChange,
    handleModeleChange,
    handleReferenceChange,
    handleDateAcquisitionChange,
    handleProchaineDateChange,
    handleRemarksChange,
    resetState,
    clearSelected
  }
}
