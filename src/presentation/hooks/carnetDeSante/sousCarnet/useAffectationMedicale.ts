import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AffectationMedical } from "@/domain/models";
import { RootState, AppDispatch } from "@/store";
import {
  createAffectationMedicale,
  getAffectationMedicales,
  updateAffectationMedicale,
  deleteAffectationMedicale,
  setSelectedAffectationMedicale,
  setDateAcquisition,
  setRemarks,
  resetAffectationMedicaleState,
  clearSelectedAffectationMedicale,
  getAffectationMedicaleByEmployer,
  getMaladieByProfessional,
} from "@/application/slices/professionnal/affectationMedicaleSlice";
import { getLocalISOString } from "@/shared/utils/getLocalISOString";
import { useAppDispatch, useAppSelector } from "@/presentation/hooks/redux";

export const useAffectationMedicale = () => {
  const dispatch = useAppDispatch();
  const {
    affectationMedicales,
    selectedAffectationMedicale,
    affectationMedicaleState,
    loading,
    error,
  } = useAppSelector((state) => state.affectationMedicale);

  const create = useCallback(
    async (data: Omit<AffectationMedical, "id">[]) => {
      const register = await dispatch(createAffectationMedicale(data));
      const affectations = register.payload as AffectationMedical[];
      return affectations.map((affectation) => {
        return {
          id: affectation.id,
          detail: affectation.maladie,
        };
      });
    },
    [dispatch]
  );

  const getAll = useCallback(
    async (carnetId: number) => {
      await dispatch(getAffectationMedicales(carnetId));
    },
    [dispatch]
  );
  const getByEmployer = useCallback(async () => {
    const result = await dispatch(getAffectationMedicaleByEmployer());
    return result.payload; // Ce sera un tableau de AffectationMedicaleEmployerStats
  }, [dispatch]);

  const getByProfessional = useCallback(async (professionalId: number) => {
    const result = await dispatch(getMaladieByProfessional(professionalId));
    return result.payload; // Ce sera un tableau de MaladieProfessionalStats
  }, [dispatch]);

  const update = useCallback(
    async (id: number, data: Partial<AffectationMedical>) => {
      await dispatch(updateAffectationMedicale({ id, data }));
    },
    [dispatch]
  );

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteAffectationMedicale(id));
    },
    [dispatch]
  );

  const select = useCallback(
    (affectationMedicale: AffectationMedical | null) => {
      dispatch(setSelectedAffectationMedicale(affectationMedicale));
    },
    [dispatch]
  );

  const handleDateAcquisitionChange = (item: string, value: Date | null) => {
    if (!value) return;
    dispatch(setDateAcquisition({ item, value: getLocalISOString(value) }));
  };

  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({ item, value }));
  };

  const resetState = () => {
    dispatch(resetAffectationMedicaleState());
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedAffectationMedicale());
  }, [dispatch]);

  return {
    affectationMedicales,
    selectedAffectationMedicale,
    affectationMedicaleState,
    loading,
    error,
    create,
    getAll,
    getByEmployer,
    getByProfessional,
    update,
    remove,
    select,
    handleDateAcquisitionChange,
    handleRemarksChange,
    resetState,
    clearSelected,
  };
};
