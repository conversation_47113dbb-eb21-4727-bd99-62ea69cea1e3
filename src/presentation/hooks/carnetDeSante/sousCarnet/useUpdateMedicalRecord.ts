import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { action_carnet_de_sante_enum } from "@/domain/models/enums";
import { getTableNameByTitle } from "@/shared/utils/getTableNameByTitle";
import { useAllergie } from "./useAllergie";
import { useMedicament } from "./useMedicament";
import { useAffectationMedicale } from "./useAffectationMedicale";
import { useDispositifMedicaux } from "./useDispositifMedicaux";
import { useAntecedantChirurgicaux } from "./useAntecedantChirurgicaux";
import { useAntecedantFamiliaux } from "./useAntecedantFamiliaux";
import { useAntecedentSociaux } from "./useAntecedentSociaux";
import { useVaccination } from "./useVaccination";
import { useAntecedentGrossesse } from "./useAntecedentGrossesse";
import { useConditionGynecologique } from "./useConditionGynecologique";
import { useDiagnostic } from "./useDiagnostic";
import { useHistoriqueCarnetSante } from "./useHistoriqueCarnetSante";
import { Antecedant_sociaux } from "@/domain/models";

export const useUpdateMedicalRecord = () => {
  const { allergieState, update: updateAllergie } = useAllergie();
  const { medicamentState, update: updateMedicament } = useMedicament();
  const { affectationMedicaleState, update: updateAffectationMedicale } =
    useAffectationMedicale();
  const { dispositifMedicauxState, update: updateDispositifMedicaux } =
    useDispositifMedicaux();
  const { antecedantChirurgicauxState, update: updateAntecedantChirurgicaux } =
    useAntecedantChirurgicaux();
  const { antecedantFamiliauxState, update: updateAntecedantFamiliaux } =
    useAntecedantFamiliaux();
  const { antecedentSociauxState, update: updateAntecedentSociaux } =
    useAntecedentSociaux();
  const { vaccinationState, update: updateVaccination } = useVaccination();
  const { antecedentGrossesseState, update: updateAntecedentGrossesse } =
    useAntecedentGrossesse();
  const { conditionGynecologiqueState, update: updateConditionGynecologique } =
    useConditionGynecologique();
  const { diagnosticState, update: updateDiagnostic } = useDiagnostic();
  const { create: createHistoriqueCarnetSante } = useHistoriqueCarnetSante();

  const { idCarnetSante } = useSelector(
    (state: RootState) => state.carnetSante
  );

  const handleUpdate = async (
    id: number,
    type: string,
    item: string,
    selectedFile: File | null
  ) => {
    if (!idCarnetSante) return;
    let tableConcernee: string | null = null;
    switch (type) {
      case TITRES_CARNET_DE_SANTE.allergies: {
        const data = {
          id_carnet: idCarnetSante,
          nom: item,
          remarques: allergieState.remarks[item],
          reaction: Object.entries(allergieState.selectedReactions[item] || {})
            .filter(([_, checked]) => checked)
            .map(([reaction]) => reaction)
            .join(", "),
          confidentialite: false,
        };
        console.log(data);

        await updateAllergie(id, data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.allergies);
        break;
      }
      case TITRES_CARNET_DE_SANTE.medicaments: {
        const data = {
          id_carnet: idCarnetSante,
          nom: item,
          force: medicamentState.force[item],
          posologie: medicamentState.posologie[item],
          duree_jour: medicamentState.duree[item],
          type_consommation: medicamentState.typeConsommation[item],
          frequence_dose: medicamentState.frequence[item],
          quantite_par_dosage: medicamentState.quantite[item],
          remarques: medicamentState.remarks[item],
          calendrier_de_dose: medicamentState.calendrier[item],
          confidentialite: false,
        };
        await updateMedicament(id, data);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.medicaments
        );
        break;
      }
      case TITRES_CARNET_DE_SANTE.affectationMedicales: {
        const data = {
          id_carnet: idCarnetSante,
          maladie: item,
          date: affectationMedicaleState.dateAcquisition[item],
          confidentialite: false,
          remarques: affectationMedicaleState.remarks[item],
        };
        await updateAffectationMedicale(id, data);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.affectationMedicales
        );
        break;
      }
      case TITRES_CARNET_DE_SANTE.dispositifMedicaux: {
        const data = {
          id_carnet: idCarnetSante,
          nom: item,
          marque: dispositifMedicauxState.marque[item],
          modele: dispositifMedicauxState.modele[item],
          reference_appareil: dispositifMedicauxState.reference[item],
          date_acquisition: dispositifMedicauxState.dateAcquisition[item],
          prochaine_mise_a_jour: dispositifMedicauxState.prochaineDate[item],
          remarques: dispositifMedicauxState.remarks[item],
          confidentialite: false,
        };
        await updateDispositifMedicaux(id, data);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.dispositifMedicaux
        );
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux: {
        const data = {
          id_carnet: idCarnetSante,
          nom: item,
          date: antecedantChirurgicauxState.dateDeChirurgie[item],
          description: antecedantChirurgicauxState.descriptions[item],
          confidentialite: false,
          remarques: antecedantChirurgicauxState.remarks[item],
        };
        await updateAntecedantChirurgicaux(id, data);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.antecedantChirurgicaux
        );
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedantFamiliaux: {
        const data = {
          id_carnet: idCarnetSante,
          nom_lien: item,
          decede: antecedantFamiliauxState.decede[item],
          affections_medicales: antecedantFamiliauxState.affection[item],
          confidentialite: false,
          remarques: antecedantFamiliauxState.remarks[item],
        };
        await updateAntecedantFamiliaux(id, data);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.antecedantFamiliaux
        );
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedentsSociaux: {
        const data: Omit<Antecedant_sociaux, "id"> = {
          id_carnet: idCarnetSante,
          nom: item,
          frequence: antecedentSociauxState.qualite[item],
          consommation: antecedentSociauxState.estActive[item] === "oui",
          quantite_consomer: antecedentSociauxState.quantite[item],
          confidentielite: false,
          remarques: antecedentSociauxState.remarks[item],
        };
        await updateAntecedentSociaux(id, data);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.antecedentsSociaux
        );
        break;
      }
      case TITRES_CARNET_DE_SANTE.vaccination: {
        const data = {
          id_carnet: idCarnetSante,
          nom_vaccin: item,
          date_administration: vaccinationState.dateAdministration[item],
          prochaine_date_echeance: vaccinationState.prochaineDate[item],
          confidentialite: false,
          remarques: vaccinationState.remarks[item],
        };
        await updateVaccination(id, data);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.vaccination
        );
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedentGrossesse: {
        const data = {
          id_carnet: idCarnetSante,
          parite: item,
          date: new Date(antecedentGrossesseState.date[item]),
          est_enceinte: antecedentGrossesseState.estEnceinte[item],
          nombre_enfants: antecedentGrossesseState.nombreEnfants[item],
          confidentialite: false,
          remarques: antecedentGrossesseState.remarks[item],
        };
        await updateAntecedentGrossesse(id, data);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.antecedentGrossesse
        );
        break;
      }
      case TITRES_CARNET_DE_SANTE.conditionGynecologique: {
        const data = {
          id_carnet: idCarnetSante,
          maladie: item,
          date: new Date(conditionGynecologiqueState.date[item]),
          confidentialite: false,
          remarques: conditionGynecologiqueState.remarks[item],
        };
        await updateConditionGynecologique(id, data);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.antecedentGrossesse
        );
        break;
      }
      case TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage: {
        const data = {
          id_carnet: idCarnetSante,
          titre: diagnosticState.titre,
          type_fichier: diagnosticState.type_fichier,
          date: new Date().toISOString(),
          path: diagnosticState.path,
          resultat: diagnosticState.impression_resultat,
          remarque: diagnosticState.remarks,
        };
        await updateDiagnostic(id, data, selectedFile);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
        );
        break;
      }
      default:
        break;
    }
    if (id) {
      // creer l'historique
      createHistoriqueCarnetSante(
        idCarnetSante,
        [{ id, detail: item }],
        tableConcernee,
        action_carnet_de_sante_enum.modification
      );
    }
  };

  return { handleUpdate };
};
