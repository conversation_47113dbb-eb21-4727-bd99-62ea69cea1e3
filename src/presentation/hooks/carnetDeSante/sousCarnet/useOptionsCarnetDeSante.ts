import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import * as constants from "@/presentation/hooks/carnetDeSante/sousCarnet/constants";
import { useCarnetDeSanteData } from "@/presentation/hooks/carnetDeSante";
import { getContentForType } from "@/shared/utils/getContentForType";
import { useCallback } from "react";

export const useOptionsCarnetDeSante = (type: string) => {
  const { data } = useCarnetDeSanteData();
  let options: { id: number; nom: string; dosage?: string; forme?: string }[] =
    [];

  const getOption = useCallback((type: string) => {
    // Get existing items for the given type
    const existingItems = getContentForType(type, data);

    // Get all available options for the type
    switch (type) {
      case TITRES_CARNET_DE_SANTE.allergies:
        options = constants.AllergyOptions;
        break;
      case TITRES_CARNET_DE_SANTE.medicaments:
        options = constants.MedicamentsOptions;
        break;
      case TITRES_CARNET_DE_SANTE.affectationMedicales:
        options = constants.AffectationMedicalesOptions;
        break;
      case TITRES_CARNET_DE_SANTE.dispositifMedicaux:
        options = constants.DispositifMedicauxOptions;
        break;
      case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux:
        options = constants.AntecedantChirurgicauxOptions;
        break;
      case TITRES_CARNET_DE_SANTE.antecedantFamiliaux:
        options = constants.AntecedantFamiliauxOptions;
        break;
      case TITRES_CARNET_DE_SANTE.antecedentsSociaux:
        options = constants.AntecedantSociauxOptions;
        break;
      case TITRES_CARNET_DE_SANTE.vaccination:
        options = constants.VaccinationsOptions;
        break;
      case TITRES_CARNET_DE_SANTE.antecedentGrossesse:
        options = constants.AntecedentGrossesseOptions;
        break;
      case TITRES_CARNET_DE_SANTE.conditionGynecologique:
        options = constants.ConditionGynecologiqueOptions;
        break;
      default:
        return [];
    }

    // Filter out options that already exist in the patient's data
    // return options.filter((option) => !existingItems.includes(option));
    const result = options.filter((option) => {
      const isExists = existingItems.find((item) => item.nom === option.nom);

      return !isExists;
    });

    return result;
  }, []);

  return {
    getOption,
  };
};
