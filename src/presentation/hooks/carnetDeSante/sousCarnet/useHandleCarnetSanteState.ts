import { useAffectationMedicale } from "./useAffectationMedicale";
import { useAllergie } from "./useAllergie";
import { useAntecedantChirurgicaux } from "./useAntecedantChirurgicaux";
import { useAntecedantFamiliaux } from "./useAntecedantFamiliaux";
import { useAntecedentGrossesse } from "./useAntecedentGrossesse";
import { useAntecedentSociaux } from "./useAntecedentSociaux";
import { useConditionGynecologique } from "./useConditionGynecologique";
import { useDiagnostic } from "./useDiagnostic";
import { useDispositifMedicaux } from "./useDispositifMedicaux";
import { useMedicament } from "./useMedicament";
import { useVaccination } from "./useVaccination";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";

export const useHandleCarnetSanteState = () => {
  const {
    allergies,
    handleReactionsChange,
    handleRemarksChange: handleRemarksAllergiesChange,
  } = useAllergie();
  const {
    medicaments,
    handleForceChange,
    handlePosologieChange,
    handleTypeConsommationChange,
    handleFrequenceChange,
    handleQuantiteChange,
    handleCalendrierChange,
    handleDureeChange,
    handleRemarksChange: handleRemarksMedicamentsChange,
  } = useMedicament();
  const {
    affectationMedicales,
    handleDateAcquisitionChange,
    handleRemarksChange: handleRemarksAffectationMedicalesChange,
  } = useAffectationMedicale();
  const {
    dispositifMedicaux,
    handleMarqueChange,
    handleModeleChange,
    handleReferenceChange,
    handleProchaineDateChange,
    handleDateAcquisitionChange: handleDateAcquisitionDispositifMedicauxChange,
    handleRemarksChange: handleRemarksDispositifMedicauxChange,
  } = useDispositifMedicaux();
  const {
    antecedantChirurgicaux,
    handleDescriptionsChange,
    handleDateDeChirurgieChange,
    handleRemarksChange: handleRemarksAntecedantChirurgicauxChange,
  } = useAntecedantChirurgicaux();
  const {
    antecedantFamiliaux,
    handleDecedeChange,
    handleAffectionChange,
    handleRemarksChange: handleRemarksAntecedantFamiliauxChange,
  } = useAntecedantFamiliaux();
  const {
    antecedentSociaux,
    handleEstActiveChange,
    handltQualiteChange,
    handletQuantiteChange,
    handleRemarksChange: handleRemarksAntecedantSociauxChange,
  } = useAntecedentSociaux();
  const {
    vaccinations,
    handleDateAdministrationChange,
    handleProchaineDateChange: handleProchaineDateVaccinationsChange,
    handleRemarksChange: handleRemarksVaccinationsChange,
  } = useVaccination();
  const {
    antecedentGrossesse,
    handleDateChange: handleDateantecedentGrossesseChange,
    handleEstEnceinteChange,
    handleNombreEnfantsChange,
    handlePariteChange,
    handleRemarksChange: handleRemarksAntecedentGrossesseChange,
  } = useAntecedentGrossesse();
  const {
    conditionGynecologique,
    handleDateChange: handleDateConditionGynecologiqueChange,
    handleMaladieChange,
    handleRemarksChange: handleRemarksConditionGynecologiqueChange,
  } = useConditionGynecologique();
  const {
    diagnostics,
    handleTypeFichierChange,
    handleTitleChange,
    handleImpressionResultatChange,
    handlePathChange,
    handleRemarksChange: handleRemarksDiagnosticChange,
  } = useDiagnostic();

  const initialiseState = (item: string, itemId: number, type: string) => {
    switch (type) {
      case TITRES_CARNET_DE_SANTE.allergies: {
        const allergy = allergies.find((al) => al.id === itemId);
        if (allergy) {
          const reactions = allergy.reaction.split(",");
          reactions.map((reaction) => {
            handleReactionsChange(item, reaction.trim(), true);
            handleRemarksAllergiesChange(item, allergy.remarques);
          });
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.medicaments: {
        const medicament = medicaments.find((m) => m.id === itemId);
        console.log(medicament);
        if (medicament) {
          handleForceChange(item, medicament.force);
          handlePosologieChange(item, medicament.posologie);
          handleDureeChange(item, String(medicament.duree_jour));
          handleTypeConsommationChange(item, medicament.type_consommation);
          handleFrequenceChange(item, medicament.frequence_dose);
          handleQuantiteChange(item, medicament.quantite_par_dosage);
          handleRemarksMedicamentsChange(item, medicament.remarques);
          handleCalendrierChange(item, medicament.calendrier_de_dose);
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.affectationMedicales: {
        const affectation = affectationMedicales.find((am) => am.id === itemId);
        if (affectation) {
          handleDateAcquisitionChange(item, new Date(affectation.date));
          handleRemarksAffectationMedicalesChange(item, affectation.remarques);
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.dispositifMedicaux: {
        const dispositif = dispositifMedicaux.find((dm) => dm.id === itemId);
        if (dispositif) {
          handleMarqueChange(item, dispositif.marque);
          handleModeleChange(item, dispositif.modele);
          handleReferenceChange(item, dispositif.reference_appareil);
          handleProchaineDateChange(
            item,
            new Date(dispositif.prochaine_mise_a_jour)
          );
          handleDateAcquisitionDispositifMedicauxChange(
            item,
            new Date(dispositif.date_acquisition)
          );
          handleRemarksDispositifMedicauxChange(item, dispositif.remarques);
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux: {
        const antecedant = antecedantChirurgicaux.find(
          (ac) => ac.id === itemId
        );
        if (antecedant) {
          handleDescriptionsChange(item, antecedant.description);
          handleDateDeChirurgieChange(
            item,
            antecedant.date ? new Date(antecedant.date) : null
          );
          handleRemarksAntecedantChirurgicauxChange(item, antecedant.remarques);
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedantFamiliaux: {
        const antecedant = antecedantFamiliaux.find((af) => af.id === itemId);
        if (antecedant) {
          handleDecedeChange(item, antecedant.decede);
          handleAffectionChange(item, antecedant.affections_medicales);
          handleRemarksAntecedantFamiliauxChange(item, antecedant.remarques);
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedentsSociaux: {
        const antecedant = antecedentSociaux.find((af) => af.id === itemId);
        if (antecedant) {
          // handleEstActiveChange(item, antecedant.consommation ? "oui" : "non");
          // handltQualiteChange(item, antecedant.frequence);
          // handletQuantiteChange(item, antecedant.quantite_consomer);
          // handleRemarksAntecedantSociauxChange(item, antecedant.remarques);
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.vaccination: {
        const vaccination = vaccinations.find((vcc) => vcc.id === itemId);
        if (vaccination) {
          handleDateAdministrationChange(
            item,
            vaccination.date_administration
              ? new Date(vaccination.date_administration)
              : null
          );
          handleProchaineDateVaccinationsChange(
            item,
            vaccination.prochaine_date_echeance != null
              ? new Date(vaccination.prochaine_date_echeance)
              : null
          );
          handleRemarksVaccinationsChange(item, vaccination.remarques);
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedentGrossesse: {
        const antecedent = antecedentGrossesse.find((vcc) => vcc.id === itemId);
        if (antecedent) {
          handleDateantecedentGrossesseChange(
            item,
            antecedent.date ? new Date(antecedent.date) : null
          );
          handleNombreEnfantsChange(item, antecedent.nombre_enfants);
          handleEstEnceinteChange(
            item,
            antecedent.est_enceinte === true ? "oui" : "non"
          );
          handlePariteChange(item, antecedent.parite);
          handleRemarksAntecedentGrossesseChange(item, antecedent.remarques);
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.conditionGynecologique: {
        const condition = conditionGynecologique.find((cg) => cg.id === itemId);
        if (condition) {
          handleDateConditionGynecologiqueChange(
            item,
            condition.date ? new Date(condition.date) : null
          );
          handleMaladieChange(item, condition.maladie);
          handleRemarksConditionGynecologiqueChange(item, condition.remarques);
        }
        break;
      }
      case TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage: {
        const diagnostic = diagnostics.find((d) => d.id === itemId);
        if (diagnostic) {
          handleTitleChange(diagnostic.titre);
          handleTypeFichierChange(diagnostic.type_fichier);
          handleImpressionResultatChange(diagnostic.resultat);
          handlePathChange(diagnostic.path);
          handleRemarksDiagnosticChange(diagnostic.remarque);
        }
        break;
      }
      default:
        break;
    }
  };

  return { initialiseState };
};
