/**
 * Hook personnalisé pour la récupération des données complètes d'un profil professionnel
 *
 * @fileoverview Ce hook implémente l'architecture Domain-Driven Design (DDD) pour récupérer
 * toutes les informations nécessaires à l'affichage d'un profil professionnel de santé.
 * Il orchestre plusieurs use cases et services pour construire un DTO complet.
 */

import { ProfessionalCardDTO } from "@/domain/DTOS";
import DateSplitter from "@/domain/services/DateSplitter";
import LoadEvents from "@/domain/services/LoadEventService";
import ProfessionalAvailabilitiesFilter from "@/domain/services/ProfessionalAvailabilitiesFilter";
import GetEventsByUserIdUsecase from "@/domain/usecases/evenements/GetEventsByUserIdUsecase.ts";
import GetPhotosByUserIdUsecase from "@/domain/usecases/photos/GetPhotosByUserIdUsecase";
import { SearchProfessionalByIdUsecase } from "@/domain/usecases/professional/GetProfessionnalInformations/SearchProfessionalByIdUsecase";
import { GetUserByIdUsecase } from "@/domain/usecases/user";
import { GetContactRepository } from "@/infrastructure/repositories/contact";
import GetEventsByUserIdRepository from "@/infrastructure/repositories/evenement/GetEventsByUserIdRepository.ts";
import GetPhotosByUserIdRepository from "@/infrastructure/repositories/photos/GetPhotosByUserIdRepository";
import { GetProfessionalDiplomasByProfessionalIdRepository } from "@/infrastructure/repositories/professionalDiploma";
import { GetProfessionalExperiencesByProfessionalIdRepository } from "@/infrastructure/repositories/professionalExperience";
import { GetLanguagesByProfessionalIdRepository } from "@/infrastructure/repositories/professionalLanguage";
import { GetProfessionalPublicationsByProfessionalIdRepository } from "@/infrastructure/repositories/professionalPublication";
import SearchProfessionalByIdRepository from "@/infrastructure/repositories/searchProfessinals/SearchProfessionalByIdRepository";
import GetUserByIdRepository from "@/infrastructure/repositories/user/GetUserByIdRepository";
import { useEffect, useState } from "react";

/**
 * Hook personnalisé pour récupérer les données complètes d'un profil professionnel
 *
 * @description Ce hook implémente le pattern Domain-Driven Design pour orchestrer
 * la récupération de toutes les informations nécessaires à l'affichage d'un profil
 * professionnel de santé. Il combine plusieurs sources de données et services
 * pour construire un objet ProfessionalCardDTO complet.
 *
 * @architecture
 * - Use Cases : Logique métier encapsulée (SearchProfessionalByIdUsecase, GetUserByIdUsecase, etc.)
 * - Repositories : Accès aux données avec abstraction de la source
 * - Services : Traitements spécialisés (filtrage disponibilités, gestion dates, photos)
 * - DTOs : Objets de transfert de données typés et validés
 *
 * @performance
 * - Chargement unique au montage du composant
 * - Gestion d'état optimisée avec useState
 * - Gestion d'erreurs centralisée
 * - Évite les re-rendus inutiles
 *
 * @param {Object} params - Paramètres du hook
 * @param {number} params.id - Identifiant unique du professionnel à récupérer
 *
 * @returns {Object} Objet contenant :
 * - profileData: Données complètes du profil (ProfessionalCardDTO | null | undefined)
 * - loading: État de chargement (boolean)
 * - error: Message d'erreur éventuel (string | undefined)
 * - setLoading: Fonction pour modifier l'état de chargement manuellement
 *
 * @exemple
 * ```tsx
 * const { profileData, loading, error } = useProfileData({ id: 123 });
 *
 * if (loading) return <LoadingSpinner />;
 * if (error) return <ErrorMessage message={error} />;
 * if (!profileData) return <NotFound />;
 *
 * return <ProfileDisplay professional={profileData} />;
 * ```
 */
const useProfileData = ({ id }: { id: number }) => {
  /**
   * État des données du profil professionnel
   *
   * - null : Données non encore chargées
   * - undefined : Erreur de chargement ou professionnel inexistant
   * - ProfessionalCardDTO : Données complètes chargées avec succès
   */
  const [profileData, setProfileData] = useState<ProfessionalCardDTO | null>();

  /**
   * État de chargement pour afficher les indicateurs visuels appropriés
   *
   * Permet aux composants consommateurs d'afficher des spinners,
   * skeletons ou autres indicateurs de chargement.
   */
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * État d'erreur pour la gestion centralisée des erreurs
   *
   * Contient le message d'erreur à afficher à l'utilisateur
   * en cas de problème lors de la récupération des données.
   */
  const [error, setError] = useState<string>();

  /**
   * Effet pour charger les données du profil au montage du composant
   *
   * Ce useEffect orchestre la récupération complète des données en :
   * 1. Initialisant tous les repositories et services nécessaires
   * 2. Configurant les use cases avec leurs dépendances
   * 3. Exécutant la requête principale avec gestion d'erreurs
   * 4. Mettant à jour les états en conséquence
   */
  useEffect(() => {
    /**
     * Fonction asynchrone pour récupérer les données du profil
     *
     * Cette fonction implémente le pattern Repository et Use Case pour :
     * - Séparer la logique métier de l'accès aux données
     * - Permettre la testabilité et la maintenabilité
     * - Centraliser la gestion des erreurs
     * - Optimiser les performances avec des services spécialisés
     */
    const getProfileData = async () => {
      try {
        /**
         * Initialisation des repositories (couche d'accès aux données)
         *
         * Chaque repository encapsule l'accès à une source de données spécifique
         * et peut être facilement mocké pour les tests ou remplacé par une autre implémentation.
         */

        // Repository principal pour les données de base du professionnel
        const searchProfessionalByIdRepository =
          new SearchProfessionalByIdRepository();

        // Services spécialisés pour le traitement des données temporelles
        const dateSpliter = new DateSplitter();
        const loadEvent = new LoadEvents();

        // Service de filtrage des disponibilités avec gestion des créneaux
        const professionalAvailabilitiesFilter =
          new ProfessionalAvailabilitiesFilter(dateSpliter, loadEvent);

        // Repository pour les informations de contact (téléphone, email, adresse)
        const getContactRepository = new GetContactRepository();

        // Repositories pour les données académiques et professionnelles
        const getProfessionalPublicationRepository =
          new GetProfessionalPublicationsByProfessionalIdRepository();
        const getProfessionalExperiencesRepository =
          new GetProfessionalExperiencesByProfessionalIdRepository();
        const getProfessionalDiplomasRepository =
          new GetProfessionalDiplomasByProfessionalIdRepository();
        const getProfessionalLanguagesRepository =
          new GetLanguagesByProfessionalIdRepository();

        // Repositories pour les données utilisateur et médias
        const getUserByIdRepository = new GetUserByIdRepository();
        const getPhotosByUserIdRepository = new GetPhotosByUserIdRepository();

        /**
         * Initialisation des use cases (couche logique métier)
         *
         * Les use cases encapsulent la logique métier et orchestrent
         * les interactions entre les différents repositories et services.
         */

        // Use case pour récupérer les données utilisateur de base
        const getUserByIdUsecase = new GetUserByIdUsecase(
          getUserByIdRepository
        );

        // Use case pour récupérer et traiter les photos du professionnel
        const getPhotosByUserIdUsecase = new GetPhotosByUserIdUsecase(
          getPhotosByUserIdRepository
        );

        const getEventsByUserIdRepository = new GetEventsByUserIdRepository();
        const getEventsByUserIdUsecase = new GetEventsByUserIdUsecase(
          getEventsByUserIdRepository
        );

        /**
         * Use case principal qui orchestre toute la récupération de données
         *
         * Ce use case combine toutes les sources de données pour construire
         * un objet ProfessionalCardDTO complet avec :
         * - Informations personnelles et professionnelles
         * - Diplômes, expériences et publications
         * - Langues parlées et spécialités
         * - Photos et informations de contact
         * - Disponibilités filtrées et optimisées
         */
        const searchProfessionalByIdUsecase = new SearchProfessionalByIdUsecase(
          searchProfessionalByIdRepository,
          professionalAvailabilitiesFilter,
          getContactRepository,
          getProfessionalPublicationRepository,
          getProfessionalExperiencesRepository,
          getProfessionalDiplomasRepository,
          getProfessionalLanguagesRepository,
          getUserByIdUsecase,
          getPhotosByUserIdUsecase,
          getEventsByUserIdUsecase
        );

        /**
         * Début du processus de chargement
         *
         * Mise à jour de l'état pour déclencher l'affichage des indicateurs
         * de chargement dans les composants consommateurs.
         */
        setLoading(true);

        /**
         * Exécution du use case principal
         *
         * Paramètres :
         * - id : Identifiant du professionnel à récupérer
         * - today : Date actuelle pour le calcul des disponibilités
         */
        const result = await searchProfessionalByIdUsecase.execute({
          id: id,
          today: new Date().toString(),
        });

        // Mise à jour des données récupérées
        setProfileData(result);

        // Fin du processus de chargement
        setLoading(false);
      } catch (error) {
        /**
         * Gestion centralisée des erreurs
         *
         * En cas d'erreur lors de la récupération des données :
         * 1. Log de l'erreur pour le debugging et le monitoring
         * 2. Mise à jour de l'état d'erreur pour l'affichage utilisateur
         * 3. Arrêt du chargement pour éviter les états incohérents
         */
        console.log(
          "Erreur lors de la récupération des données du professionnel",
          error
        );

        // Conversion de l'erreur en message utilisateur
        setError(
          error instanceof Error
            ? error.message
            : "Erreur inconnue lors du chargement du profil"
        );

        /**
         * Arrêt du chargement en cas d'erreur
         *
         * Vérification de l'état loading pour éviter les mises à jour
         * d'état inutiles si le chargement était déjà terminé.
         */
        if (loading) {
          setLoading(false);
        }
      }
    };

    /**
     * Déclenchement du chargement des données
     *
     * Appel immédiat de la fonction de récupération des données
     * dès le montage du composant.
     */
    getProfileData();
  }, [id]); // Dépendance sur 'id' pour recharger si l'ID change

  /**
   * Valeurs de retour du hook
   *
   * Interface publique exposée aux composants consommateurs
   * pour accéder aux données et états du profil professionnel.
   */
  return {
    /**
     * Données complètes du profil professionnel
     *
     * - null : Chargement en cours ou pas encore démarré
     * - undefined : Erreur de chargement ou professionnel inexistant
     * - ProfessionalCardDTO : Données complètes chargées avec succès
     */
    profileData,

    /**
     * État de chargement pour l'affichage des indicateurs visuels
     *
     * Permet aux composants d'afficher des spinners, skeletons
     * ou autres indicateurs de chargement appropriés.
     */
    loading,

    /**
     * Message d'erreur pour l'affichage utilisateur
     *
     * Contient un message d'erreur lisible en cas de problème
     * lors de la récupération des données.
     */
    error,

    /**
     * Fonction pour modifier manuellement l'état de chargement
     *
     * Utile pour les composants qui ont besoin de contrôler
     * l'état de chargement dans des cas spécifiques.
     */
    setLoading,
  };
};

/**
 * Export par défaut du hook useProfileData
 *
 * Ce hook représente la couche d'interface entre les composants React
 * et la logique métier Domain-Driven Design pour la récupération
 * des données de profil professionnel.
 */
export default useProfileData;
