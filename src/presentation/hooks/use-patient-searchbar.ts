import { SupabaseError } from "@/infrastructure/supabase/supabaseError";
import {
  getSpecialities,
  setIsPopOverOpen as setIsPopOverOpenAction,
} from "@/application/slices/professionnal/listeSpecialitesSlice";

import { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { useToast } from '../components/common/toast/Toast'

const usePatientSearchbar = () => {
  const toast = useToast();
  const { error, listes, isPopoverOpen, loading } = useSelector(
    (state: RootState) => state.listeSpecialites,
  );
  const [isLocationPopoverOpen, setIsLocationPopoverOpen] = useState(false);

  const dispatch = useDispatch<AppDispatch>();

  const setIsPopoverOpen = (isOpen: boolean) => {
    dispatch(setIsPopOverOpenAction(isOpen));
  };

  const openNextPopOver = () => {
    setIsPopoverOpen(false);
    setIsLocationPopoverOpen(true);
  };

  const getSpecialitiesList = async () => {
    try {
      if (listes.length == 0) await dispatch(getSpecialities());
    } catch (error) {
      toast.error(
        error instanceof SupabaseError
          ? error.message
          : "Erreur lors de la recuperation des lites de specialitees",
      );
    }
  };

  return {
    setIsPopoverOpen,
    error,
    listes,
    isPopoverOpen,
    loading,
    getSpecialitiesList,
    isLocationPopoverOpen,
    setIsLocationPopoverOpen,
    openNextPopOver,
  };
};

export default usePatientSearchbar;
