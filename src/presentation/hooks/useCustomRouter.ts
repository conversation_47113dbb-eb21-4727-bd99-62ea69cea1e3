import * as React from 'react';

// Définir un routeur personnalisé
export default function useCustomRouter(initialPath: string) {
    const [pathname, setPathname] = React.useState(initialPath);
    const [searchParams, setSearchParams] = React.useState(new URLSearchParams());

    const router = React.useMemo(() => {
        return {
            pathname,
            searchParams,
            navigate: (path: string | URL) => {
                if (typeof path === 'string') {
                    const [newPathname, params] = path.split('?');
                    setPathname(newPathname || '/');
                    setSearchParams(new URLSearchParams(params || ''));
                } else {
                    setPathname(path.pathname || '/');
                    setSearchParams(path.searchParams || new URLSearchParams());
                }
            },
        };
    }, [pathname, searchParams]);

    return router;
}