import { SousCategories } from "@/domain/models/SousCategories";
import GetStockSubCategoriesByCategoryIdUsecase from "@/domain/usecases/subCategories/GetStockSubCategoriesByCategoryIdUsecase";
import GetStockSubCategoryByIdUsecase from "@/domain/usecases/subCategories/GetStockSubCategoryByIdUsecase.ts";
import GetStockSubCategoriesByCategoryIdRepository from "@/infrastructure/repositories/subCategories/GetStockSubCategoriesByCategoryIdRepository";
import GetStockSubCategoryByIdRepository from "@/infrastructure/repositories/subCategories/GetStockSubCategoryByIdRepository.ts";
import { useEffect, useState } from "react";

const useStockSubCategories = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchSubCategories = async (categoryId: number) => {
    try {
      const getStockSubCategoriesRepository =
        new GetStockSubCategoriesByCategoryIdRepository();
      const getStockSubCategoriesUsecase =
        new GetStockSubCategoriesByCategoryIdUsecase(
          getStockSubCategoriesRepository,
        );
      const subCategories =
        await getStockSubCategoriesUsecase.execute(categoryId);

      return subCategories;
    } catch (error) {
      setError(error as Error);
    } finally {
      setIsLoading(false);
    }
  };

  const getSubCategoryById = async (subCategoryId: number) => {
    const getSubCategoryByIdRepository =
      new GetStockSubCategoryByIdRepository();

    const getSubCategoryByIdUsecase = new GetStockSubCategoryByIdUsecase(
      getSubCategoryByIdRepository,
    );

    try {
      const result = await getSubCategoryByIdUsecase.execute(subCategoryId);

      return result;
    } catch (error) {
      console.log(
        "Erreur lors de la recuperation des donnees du sous-categorie: ",
        error,
      );
      setError(error);
    }
  };

  return { fetchSubCategories, isLoading, error, getSubCategoryById };
};

export default useStockSubCategories;
