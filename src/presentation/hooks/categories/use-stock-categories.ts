import { Categories } from "@/domain/models/Categories";
import GetStockCategoriesUsecase from "@/domain/usecases/categories/GetStockCategoriesUsecase";
import GetStockCategoryByIdUsecase from "@/domain/usecases/categories/GetStockCategoryByIdUsecase.ts";
import GetStockCategoriesRepository from "@/infrastructure/repositories/categories/GetStockCategoriesRepository";
import GetStockCategoryByIdRepository from "@/infrastructure/repositories/categories/GetStockCategoryByIdRepository.ts";
import { useEffect, useState } from "react";
import { useToast } from "../use-toast.ts";

const useStockCategories = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<Categories[]>([]);

  const toast = useToast();

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const getStockCategoriesRepository = new GetStockCategoriesRepository();

        const getStockCategoriesUsecase = new GetStockCategoriesUsecase(
          getStockCategoriesRepository
        );
        setIsLoading(true);

        const categories = await getStockCategoriesUsecase.execute();
        setData(categories);
        setIsLoading(false);
      } catch (error) {
        setError(error as Error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchCategories();
  }, []);

  const getCategoryById = async (categoryId: number) => {
    const getStockCategoryByIdRepository = new GetStockCategoryByIdRepository();
    const getStockCategoryByIdUsecase = new GetStockCategoryByIdUsecase(
      getStockCategoryByIdRepository
    );

    try {
      const result = await getStockCategoryByIdUsecase.execute(categoryId);
      return result;
    } catch (error) {
      console.log("Erreur lors de la recuperation du categorie: ", error);
      setError(error);
    }
  };

  return { data, isLoading, error, getCategoryById };
};

export default useStockCategories;
