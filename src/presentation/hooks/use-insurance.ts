import { useToast } from "./use-toast";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";
import GetInsurancesRepository from "@/infrastructure/repositories/insurance/GetInsurancesRepository";
import GetInsurancesUsecase from "@/domain/usecases/insurance/GetInsurancesUsecase";
import GetProfessionalInsurancesRepository from "@/infrastructure/repositories/insurance/GetProfessionalInsurancesRepository";
import GetProfessionalInsurancesUsecase from "@/domain/usecases/insurance/GetProfessionalInsurancesUsecase";
import { useCallback } from "react";

const useInsuranceProfessional = () => {
  const toast = useToast();

  const getInsurances = useCallback(async () => {
    try {
      const getInsurancesRepository = new GetInsurancesRepository();
      const getInsurancesUsecase = new GetInsurancesUsecase(
        getInsurancesRepository
      );

      const data = await getInsurancesUsecase.execute().then((res) => {
        return res;
      });

      return data;
    } catch (error) {
      toast.error(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }, [toast]);

  const getProfessionalInsurances = useCallback(
    async (id: number) => {
      try {
        const getProfessionalInsurancesRepository =
          new GetProfessionalInsurancesRepository();
        const getProfessionalInsurancesUsecase =
          new GetProfessionalInsurancesUsecase(
            getProfessionalInsurancesRepository
          );

        const data = await getProfessionalInsurancesUsecase
          .execute(id)
          .then((res) => {
            return res;
          });

        return data;
      } catch (error) {
        toast.error(error.message || ErrorMessages.UNKNOWN_ERROR);
      }
    },
    [toast]
  );

  return {
    getInsurances,
    getProfessionalInsurances,
  };
};

export default useInsuranceProfessional;
