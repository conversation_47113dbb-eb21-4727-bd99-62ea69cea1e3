import { useState } from "react";
import { Dash, signe_vitaux } from "@/domain/models";
import { PrintService } from "@/domain/services/PrintService";
import { useProfessionnelPatient } from "../use-professionnelPatient";
import useSearchProfessional from "../use-search-professional";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { useAppSelector } from "../redux";
import { useEmployer } from "../employer/useEmployer";
import { GetLocaleDate } from "@/shared/utils/getLocaleDate";

interface SignesVitauxForm {
  taille: number | null;
  poids: number | null;
  temperature: number | null;
  imc: number | null;
  circonferenceTete: number | null;
  frequenceCardiaque: number | null;
  frequenceRespiratoire: number | null;
  sao2: number | null;
  glucose: number | null;
  tension_arterielle: string | null;
  diastolique: number | null;
  systolique: number | null;
  date_visite: Date;
}

export const useSigneVitauxForm = (idCarnetSante?: number) => {
  const role = useAppSelector((state) => state.authentification.user?.role);
  const roleUserSelected = useAppSelector(
    (state) => state.user?.roleUserSelected
  );
  const user = useAppSelector((state) => state.authentification.userData);
  const selectedProche = useAppSelector(
    (state) => state.prochePatient?.selectedProche
  );
  const [selectedSignes, setSelectedSignes] = useState<string[]>(signes);
  const [selectAll, setSelectAll] = useState(true);
  const [formData, setFormData] = useState<SignesVitauxForm>({
    taille: null,
    poids: null,
    temperature: null,
    imc: null,
    circonferenceTete: null,
    frequenceCardiaque: null,
    frequenceRespiratoire: null,
    sao2: null,
    glucose: null,
    date_visite: null,
    tension_arterielle: null,
    diastolique: null,
    systolique: null,
  });

  const { selectedDataProfessionalPatient } = useProfessionnelPatient();
  const { currentProfessional } = useSearchProfessional();
  const { selectedEmployerSlice } = useEmployer();

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectAll(event.target.checked);
    if (event.target.checked) {
      setSelectedSignes(signes);
    } else {
      setSelectedSignes([]);
    }
  };

  const handleSigneChange =
    (signe: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
      if (event.target.checked) {
        setSelectedSignes([...selectedSignes, signe]);
      } else {
        setSelectedSignes(selectedSignes.filter((s) => s !== signe));
        setSelectAll(false);
      }
    };

  const handleInputChange =
    (field: keyof SignesVitauxForm) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = Number(event.target.value);
      const updatedFormData = {
        ...formData,
        [field]: newValue,
      };

      if (field === "taille" || field === "poids") {
        const poids = field === "poids" ? newValue : formData.poids;
        const taille = field === "taille" ? newValue : formData.taille;

        if (poids && taille) {
          // IMC = poids / (taille en mètres)²
          const tailleEnMetres = taille / 100; // conversion en mètres
          const imc = poids / (tailleEnMetres * tailleEnMetres);
          updatedFormData.imc = Number(imc.toFixed(2));
        }
      }

      if (field === "systolique" || field === "diastolique") {
        const diastolique =
          field === "diastolique" ? newValue : formData.diastolique;
        const systolique =
          field === "systolique" ? newValue : formData.systolique;

        if (diastolique && systolique) {
          const tensionArterielle = `${diastolique}/${systolique}`;
          updatedFormData.tension_arterielle = tensionArterielle;
        }
      }

      setFormData(updatedFormData);
    };

  const getSignesVitauxData = (): Omit<signe_vitaux, "id"> => ({
    id_carnet: idCarnetSante,
    taille: selectedSignes.includes("Taille") ? formData.taille : null,
    poid: selectedSignes.includes("Poids") ? formData.poids : null,
    temperature: selectedSignes.includes("Température")
      ? formData.temperature
      : null,
    indice_masse_corporel: selectedSignes.includes("Indice de masa corporal")
      ? formData.imc
      : null,
    circonference_tete: selectedSignes.includes("Circonférence de la tête")
      ? formData.circonferenceTete
      : null,
    frequence_cardiaque: selectedSignes.includes("Fréquence cardiaque")
      ? formData.frequenceCardiaque
      : null,
    frequence_respiratoire: selectedSignes.includes("Fréquence respiratoire")
      ? formData.frequenceRespiratoire
      : null,
    sa02: selectedSignes.includes("SA02") ? formData.sao2 : null,
    niveau_glucose: selectedSignes.includes("Niveau de glucose")
      ? formData.glucose
      : null,
    tension_arterielle: selectedSignes.includes("Tension artérielle")
      ? formData.tension_arterielle
      : null,
    date_visite: GetLocaleDate(),
  });

  const resetForm = () => {
    setFormData({
      taille: null,
      poids: null,
      temperature: null,
      imc: null,
      circonferenceTete: null,
      frequenceCardiaque: null,
      frequenceRespiratoire: null,
      sao2: null,
      glucose: null,
      tension_arterielle: null,
      diastolique: null,
      systolique: null,
      date_visite: null,
    });
    setSelectedSignes([]);
    setSelectAll(false);
  };

  const initialiseState = (signe: signe_vitaux) => {
    setFormData({
      taille: signe.taille || null,
      poids: signe.poid || null,
      temperature: signe.temperature || null,
      imc: signe.indice_masse_corporel || null,
      circonferenceTete: signe.circonference_tete || null,
      frequenceCardiaque: signe.frequence_cardiaque || null,
      frequenceRespiratoire: signe.frequence_respiratoire || null,
      sao2: signe.sa02 || null,
      glucose: signe.niveau_glucose || null,
      tension_arterielle: signe.tension_arterielle || null,
      diastolique: Number(signe.tension_arterielle?.split("/")[0]) || null,
      systolique: Number(signe.tension_arterielle?.split("/")[1]) || null,
      date_visite: signe.date_visite,
    });

    // Détermine quels signes sont présents dans les données
    const signesPresents = [];
    if (signe.taille) signesPresents.push("Taille");
    if (signe.poid) signesPresents.push("Poids");
    if (signe.temperature) signesPresents.push("Température");
    if (signe.indice_masse_corporel)
      signesPresents.push("Indice de masa corporal");
    if (signe.circonference_tete)
      signesPresents.push("Circonférence de la tête");
    if (signe.frequence_cardiaque) signesPresents.push("Fréquence cardiaque");
    if (signe.frequence_respiratoire)
      signesPresents.push("Fréquence respiratoire");
    if (signe.sa02) signesPresents.push("SA02");
    if (signe.niveau_glucose) signesPresents.push("Niveau de glucose");
    if (signe.tension_arterielle) signesPresents.push("Tension artérielle");
    setSelectedSignes(signesPresents);
    setSelectAll(signesPresents.length === signes.length);
  };

  const handlePrint = () => {
    const signesVitaux = getSignesVitauxData();
    if (role === utilisateurs_role_enum.PROFESSIONNEL) {
      if (roleUserSelected === utilisateurs_role_enum.PATIENT) {
        PrintService.printSignesVitaux(
          signesVitaux,
          selectedDataProfessionalPatient?.patient,
          currentProfessional,
          null,
          null,
          null
        );
      } else if (roleUserSelected === utilisateurs_role_enum.PROCHE) {
        PrintService.printSignesVitaux(
          signesVitaux,
          null,
          currentProfessional,
          null,
          selectedProche,
          null
        );
      }
    } else if (role === utilisateurs_role_enum.DASH) {
      if (roleUserSelected === utilisateurs_role_enum.EMPLOYER) {
        PrintService.printSignesVitaux(
          signesVitaux,
          null,
          null,
          selectedEmployerSlice,
          null,
          user as Dash
        );
      } else if (roleUserSelected === utilisateurs_role_enum.PROCHE) {
        PrintService.printSignesVitaux(
          signesVitaux,
          null,
          null,
          null,
          selectedProche,
          user as Dash
        );
      }
    }
  };

  const isFormValid = () => {
    return Object.values(formData).some((value) => value !== null);
  };

  return {
    formData,
    selectedSignes,
    selectAll,
    handleSelectAll,
    handleSigneChange,
    handleInputChange,
    getSignesVitauxData,
    resetForm,
    initialiseState,
    handlePrint,
    isFormValid: isFormValid(),
  };
};

export const signes = [
  "Taille",
  "Poids",
  "Température",
  "Indice de masa corporal",
  "Circonférence de la tête",
  "Fréquence cardiaque",
  "Fréquence respiratoire",
  "SA02",
  "Niveau de glucose",
  "Tension artérielle",
];
