import { useCallback, useRef, useEffect } from 'react';

type CallbackFunction = (...args: any[]) => void;

function useDebouncedCallback<T extends CallbackFunction>(
  callback: T,
  delay: number = 300 // délai par défaut de 300ms
): (...args: Parameters<T>) => void {
  // On utilise useRef pour garder en mémoire le timer entre les rendus.
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const debouncedFunction = useCallback((...args: Parameters<T>) => {
    // Si un timer existe déjà, on l'efface.
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    // On démarre un nouveau timer
    timerRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);

  // On s'assure de nettoyer le timer lors du démontage du composant.
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return debouncedFunction;
}

export default useDebouncedCallback;
