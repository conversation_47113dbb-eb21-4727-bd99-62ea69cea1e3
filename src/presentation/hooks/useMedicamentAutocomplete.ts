import { useState, useEffect, useCallback } from "react";
import { Medicament } from "@/domain/models/Medicament.ts";
import GetMedicamentListRepository from "@/infrastructure/repositories/medicament/GetMedicamentListRepository.ts";
import GetMedicamentListUsecase from "@/domain/usecases/medicament/GetMedicamentListUsecase.ts";
import { useThrottle } from "./useThrottle.ts";

/**
 * Hook personnalisé pour la gestion de l'autocomplétion des médicaments
 * Utilise le throttling pour optimiser les requêtes API
 */
export const useMedicamentAutocomplete = () => {
  const [medicaments, setMedicaments] = useState<Medicament[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fonction de recherche des médicaments
   * @param searchTerm - Terme de recherche
   */
  const searchMedicaments = useCallback(async (searchTerm: string) => {
    if (!searchTerm || searchTerm.trim().length < 2) {
      setMedicaments([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Initialisation du repository et usecase à chaque recherche
      const medicamentRepository = new GetMedicamentListRepository();
      const getMedicamentListUsecase = new GetMedicamentListUsecase(
        medicamentRepository
      );

      const results = await getMedicamentListUsecase.execute(searchTerm.trim());
      setMedicaments(results);
    } catch (err) {
      console.error("Erreur lors de la recherche de médicaments:", err);
      setError("Erreur lors de la recherche des médicaments");
      setMedicaments([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Application du throttling avec un délai de 300ms
  const throttledSearch = useThrottle(searchMedicaments, 300);

  /**
   * Fonction pour déclencher la recherche avec throttling
   * @param searchTerm - Terme de recherche
   */
  const search = useCallback(
    (searchTerm: string) => {
      throttledSearch(searchTerm);
    },
    [throttledSearch]
  );

  /**
   * Fonction pour réinitialiser l'état
   */
  const reset = useCallback(() => {
    setMedicaments([]);
    setError(null);
    setLoading(false);
  }, []);

  return {
    medicaments,
    loading,
    error,
    search,
    reset,
  };
};

export default useMedicamentAutocomplete;
