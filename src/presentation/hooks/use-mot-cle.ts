import GetMotClesUsecase from "@/domain/usecases/motCles/GetMotClesUsecase";
import GetMotClesRepository from "@/infrastructure/repositories/motCles/GetMotClesRepository";
import { useToast } from "./use-toast";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";

const useMotCle = () => {
  const toast = useToast();
  const getMotCles = async () => {
    try {
      const getMotClesRepository = new GetMotClesRepository();
      const getMotClesUsecase = new GetMotClesUsecase(getMotClesRepository);

      const data = await getMotClesUsecase.execute().then((res) => {
        return res;
      });

      return data;
    } catch (error) {
      toast.error(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  };

  return {
    getMotCles,
  };
};

export default useMotCle;
