import { useLocation, useNavigate } from "react-router-dom";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import {
  buildSearchPath,
  isValidRouteParam,
  sanitizeRouteParam,
} from "@/shared/utils/routeParamUtils";
import useSearchProfessional from "./use-search-professional";
import slugify from "slugify";

/**
 * Hook personnalisé pour gérer la navigation et la synchronisation des paramètres de recherche
 *
 * Ce hook encapsule toute la logique métier liée à :
 * - La construction des URLs de recherche
 * - La navigation entre les pages
 * - La synchronisation des paramètres d'URL
 * - La validation et le nettoyage des paramètres
 */
const useSearchNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { searchProfessional } = useSearchProfessional();
  const redirectToBaseUrl = `/${PublicRoutesNavigation.PROFILE_PAGE.split("/:slug")[0]}`;

  /**
   * Supprime les variables de route d'un chemin (ex: "medecins/:localization" → "medecins")
   */
  const removeRouteVariable = (route: string): string => {
    return route.split("/:")[0];
  };

  /**
   * Vérifie si nous sommes actuellement sur la page de recherche de professionnels
   */
  const isOnSearchPage = (): boolean => {
    return location.pathname.includes(
      PublicRoutesNavigation.FIND_PROFESSIONAL.split("/:")[0]
    );
  };

  /**
   * Met à jour l'URL du navigateur avec les nouveaux paramètres de recherche
   * Utilise replace: true pour éviter d'ajouter une entrée dans l'historique
   */
  const updateUrlParams = (location?: string, speciality?: string): void => {
    const sanitizedLocation = sanitizeRouteParam(location);
    const sanitizedSpeciality = sanitizeRouteParam(speciality);


    try {
      const basePath = `/${removeRouteVariable(PublicRoutesNavigation.FIND_PROFESSIONAL)}`;
      const searchPath = buildSearchPath(
        basePath,
        sanitizedLocation,
        sanitizedSpeciality
      );
      navigate(searchPath, { replace: true });
    } catch (error) {

      // Solution de repli : naviguer avec seulement la localisation si disponible
      if (isValidRouteParam(sanitizedLocation)) {
        const basePath = `/${removeRouteVariable(PublicRoutesNavigation.FIND_PROFESSIONAL)}`;
        navigate(`${basePath}/${encodeURIComponent(sanitizedLocation)}`, {
          replace: true,
        });
      }
    }
  };

  /**
   * Navigue vers la page de recherche avec les paramètres spécifiés
   * Utilisé quand on n'est pas encore sur la page de recherche
   */
  const navigateToSearchPage = (
    location: string,
    speciality?: string
  ): void => {
    const sanitizedLocation = sanitizeRouteParam(location);
    const sanitizedSpeciality = sanitizeRouteParam(speciality);

    try {
      const basePath = `/${removeRouteVariable(PublicRoutesNavigation.FIND_PROFESSIONAL)}`;
      const searchPath = buildSearchPath(
        basePath,
        sanitizedLocation,
        sanitizedSpeciality
      );
      navigate(searchPath);
    } catch (error) {

      // Solution de repli : naviguer avec seulement la localisation si disponible
      if (isValidRouteParam(sanitizedLocation)) {
        const basePath = `/${removeRouteVariable(PublicRoutesNavigation.FIND_PROFESSIONAL)}`;
        navigate(`${basePath}/${encodeURIComponent(sanitizedLocation)}`);
      }
    }
  };

  /**
   * Valide les paramètres de recherche
   * Retourne true si au moins un paramètre valide est fourni
   */
  const validateSearchParams = (
    location?: string,
    speciality?: string
  ): boolean => {
    return isValidRouteParam(location) || isValidRouteParam(speciality);
  };

  /**
   * Fonction principale pour gérer la recherche et la navigation
   * Détermine automatiquement s'il faut naviguer ou mettre à jour l'URL
   */
  const handleSearch = async (
    location?: string,
    speciality?: string
  ): Promise<void> => {
    // Valider que nous avons au moins un paramètre de recherche valide
    if (!validateSearchParams(location, speciality)) {

      return;
    }

    // Nettoyer les paramètres de recherche
    const sanitizedLocation = sanitizeRouteParam(location);
    const sanitizedSpeciality = sanitizeRouteParam(speciality);

    if (isOnSearchPage()) {
      // Nous sommes sur la page de recherche - exécuter la recherche et mettre à jour l'URL
      await searchProfessional({
        name: sanitizedSpeciality,
        localization: sanitizedLocation,
      });

      // Mettre à jour l'URL avec les nouveaux paramètres
      updateUrlParams(sanitizedLocation, sanitizedSpeciality);
    } else {
      // Nous ne sommes pas sur la page de recherche - naviguer vers la page de recherche
      navigateToSearchPage(sanitizedLocation, sanitizedSpeciality);
    }
  };

  /**
   * Navigue vers le profil d'un professionnel spécifique
   */
  const navigateToProfessionalProfile = ({
    id,
    nom,
    prenom,
    titre,
    adresse,
    specialite,
  }: {
    id: number;
    nom: string;
    prenom: string;
    titre: string;
    adresse: string;
    specialite: string;
  }): void => {
    const speciality = `/${specialite}`;
    const slug = slugify(`/${titre}-${nom}-${prenom}-${adresse}-${id}`, {
      lower: true,
    });

    const url = `${speciality}/${slug}`;
    navigate(url);
  };

  return {
    handleSearch,
    navigateToProfessionalProfile,
    updateUrlParams,
    navigateToSearchPage,
    validateSearchParams,
    isOnSearchPage: isOnSearchPage(),
  };
};

export default useSearchNavigation;
