// import { useCallback, useMemo } from "react";
import DateClass from "@/shared/utils/DateClass";
import { ScheduleDay } from "@/domain/types/schedule";
import { TimeSlotProffessionalCard } from "@/domain/DTOS";
import {
  EMPTY_STATE_MESSAGES,
  EmptyStateType,
} from "@/domain/types/empty-state";

interface UseEmptyStateProps {
  schedule: ScheduleDay[];
  startDate: DateClass;
  maxDate?: Date;
  minDate?: Date;
  onNavigateToDate: (date: DateClass) => void;
  events: TimeSlotProffessionalCard[];
}

interface UseEmptyStateResult {
  nextAvailableDate: Date | null;
  shouldDisplay: boolean;
  isAfterMaxDate: (currentDate: DateClass) => boolean;
  isCurrentDateAfterMax: boolean;
  handleNextAvailabilityClick: () => void;
  stateType: EmptyStateType;
  formattedDate: string | null;
  message: typeof EMPTY_STATE_MESSAGES[EmptyStateType];
}

/**
 * Hook personnalisé pour gérer la logique de l'état vide du calendrier
 * @param schedule - Le planning avec les créneaux disponibles
 * @param startDate - La date de départ
 * @param maxDate - La date maximale autorisée
 */
export const useEmptyState = ({
  schedule,
  startDate,
  maxDate,
  minDate,
  onNavigateToDate,
  events,
}: UseEmptyStateProps): UseEmptyStateResult => {
  // Normalise la date minimale pour qu'elle ne soit pas dans le passé
  const normalizedMinDate = () => {
    if (!minDate) return null;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return minDate < today ? today : minDate;
  };
  // Vérifie si une date est après la date maximale
  const isAfterMaxDate = (currentDate: DateClass): boolean => {
    if (!maxDate) return false;
    const currentClassicDate = currentDate.toClassicDate();
    return currentClassicDate > maxDate;
  };

  // Vérifie si la date de départ est après la date maximale
  const isCurrentDateAfterMax = () => {
    return isAfterMaxDate(startDate);
  };

  const compareDays = (date1: Date, date2: Date): number => {
    // Normaliser les dates en ignorant l'heure
    const d1 = new Date(date1);
    d1.setHours(0, 0, 0, 0);

    const d2 = new Date(date2);
    d2.setHours(0, 0, 0, 0);

    // Calculer la différence en millisecondes et convertir en jours
    const diffMs = d1.getTime() - d2.getTime();
    const diffDays = Math.round(diffMs / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  // Trouve la prochaine date disponible après la date courante
  const nextAvailableDate = () => {
    if (!events?.length) return null;

    const currentDate = startDate.toClassicDate();

    // Trie les événements par date et trouve le premier disponible après la date courante
    const sortedEvents = [...events]
      .find((event) => compareDays(new Date(event.date), currentDate) > 0);

    return sortedEvents?.date || null;
  };

  // Détermine si l'état vide doit être affiché
  const shouldDisplay = () => {
    return schedule.every((day) => day.horaires.length === 0);
  };

  const handleNextAvailabilityClick = () => {
    const availableDate = nextAvailableDate();
    const minDate = normalizedMinDate();
    const targetDate = availableDate
      ? new Date(availableDate)
      : minDate
      ? new Date(minDate)
      : null;
    if (!targetDate) return;

    onNavigateToDate(
      new DateClass(
        targetDate.getFullYear(),
        targetDate.getMonth() + 1,
        targetDate.getDate(),
      ),
    );
  };

  // Détermine le type d'état actuel
  const stateType = () => {
    if (isCurrentDateAfterMax()) return EmptyStateType.AFTER_MAX_DATE;
    if (!nextAvailableDate() && !normalizedMinDate()) {
      return EmptyStateType.NO_SLOTS;
    }
    return EmptyStateType.HAS_NEXT_SLOT;
  };

  // Formate la date pour l'affichage
  const formattedDate = () => {
    const availableDate = nextAvailableDate();
    const minDate = normalizedMinDate();
    const targetDate = availableDate
      ? new Date(availableDate)
      : minDate
      ? new Date(minDate)
      : null;
    if (!targetDate) return null;

    return targetDate.toLocaleDateString("fr-FR", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  // Récupère l'état et le message correspondant
  const currentStateType = stateType();
  const message = EMPTY_STATE_MESSAGES[currentStateType];

  return {
    nextAvailableDate: nextAvailableDate()
      ? new Date(nextAvailableDate())
      : null,
    shouldDisplay: shouldDisplay(),
    isAfterMaxDate,
    isCurrentDateAfterMax: isCurrentDateAfterMax(),
    handleNextAvailabilityClick,
    stateType: currentStateType,
    formattedDate: formattedDate(),
    message,
  };
};
