import {
  getProcheByPatientId,
  getProchePatient,
  getProcheById as getProcheByIdAction,
  getProchePatientById,
  getProcheEmployerById,
  getProcheEmployer,
  updateProche,
  deleteProcheByPatientId,
  createProche,
  createProcheByDass,
  createProcheByProfessional,
} from "@/application/slices/patient/prochePatientSlice";
import { useAppDispatch, useAppSelector } from "./redux";
import { useCallback } from "react";
import { Employer, Patient, Proche } from "@/domain/models";

export const useProche = () => {
  const dispatch = useAppDispatch();
  const {
    selectedProcheEmployer,
    selectedProchePatient,
    selectedProche,
    proches,
    prochePatient,
    procheEmployer,
    loading,
    error,
  } = useAppSelector((state) => state.prochePatient);

  const handleCreateProche = useCallback(
    async (procheData: Proche) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(createProche(procheData)).unwrap();
    },
    [dispatch]
  );

  const handleCreateProcheByProfessional = useCallback(
    async (procheData: Proche, patient?: Patient | null) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(
        createProcheByProfessional({ procheData, patient })
      ).unwrap();
    },
    [dispatch]
  );

  const handleCreateProcheByDass = useCallback(
    async (procheData: Proche, employees?: Employer | null) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(createProcheByDass({ procheData, employees })).unwrap();
    },
    [dispatch]
  );

  const handleGetProchePatient = useCallback(
    async (id: number[]) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(getProchePatient(id)).unwrap();
    },
    [dispatch]
  );

  const handleGetProchePatientById = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(getProchePatientById(id)).unwrap();
    },
    [dispatch]
  );

  const handleGetProcheEmployer = useCallback(
    async (id: number[]) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(getProcheEmployer(id)).unwrap();
    },
    [dispatch]
  );

  const handleGetProcheEmployerById = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(getProcheEmployerById(id)).unwrap();
    },
    [dispatch]
  );

  const handleGetProcheById = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }

      await dispatch(getProcheByIdAction(id)).unwrap();
    },
    [dispatch]
  );

  const handleGetProcheByPatientId = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(getProcheByPatientId(id)).unwrap();
    },
    [dispatch]
  );

  const handleEditProche = useCallback(
    async (id: number, procheData: Partial<Proche>) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(updateProche({ id, procheData })).unwrap();
    },
    [dispatch]
  );

  const handleDeleteProcheByPatientId = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(deleteProcheByPatientId(id)).unwrap();
    },
    [dispatch]
  );

  return {
    proches,
    prochePatient,
    procheEmployer,
    loading,
    error,
    selectedProche,
    selectedProchePatient,
    selectedProcheEmployer,
    handleGetProchePatient,
    handleGetProcheByPatientId,
    handleDeleteProcheByPatientId,
    handleEditProche,
    handleCreateProche,
    handleGetProcheById,
    handleGetProcheEmployer,
    handleCreateProcheByDass,
    handleCreateProcheByProfessional,
    handleGetProchePatientById,
    handleGetProcheEmployerById,
  };
};
