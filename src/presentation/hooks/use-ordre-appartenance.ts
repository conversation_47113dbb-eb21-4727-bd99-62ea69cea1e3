import { useState } from "react";
import { ordre_appartenance } from "@/domain/models";
import {
  GetOrdreAppartenanceListUsecase,
  GetOrdreAppartenanceByIdUsecase,
  CreateOrdreAppartenanceUsecase,
  EditOrdreAppartenanceUsecase,
  DeleteOrdreAppartenanceUsecase,
} from "@/domain/usecases/ordreAppartenance";
import GetOrdreAppartenanceListRepository from "@/infrastructure/repositories/ordreAppartenance/GetOrdreAppartenanceListRepository";
import GetOrdreAppartenanceByIdRepository from "@/infrastructure/repositories/ordreAppartenance/GetOrdreAppartenanceByIdRepository";
import CreateOrdreAppartenanceRepository from "@/infrastructure/repositories/ordreAppartenance/CreateOrdreAppartenanceRepository";
import EditOrdreAppartenanceRepository from "@/infrastructure/repositories/ordreAppartenance/EditOrdreAppartenanceRepository";
import DeleteOrdreAppartenanceRepository from "@/infrastructure/repositories/ordreAppartenance/DeleteOrdreAppartenanceRepository";

export const useOrdreAppartenance = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [ordreAppartenanceList, setOrdreAppartenanceList] = useState<
    ordre_appartenance[]
  >([]);
  const [selectedOrdreAppartenance, setSelectedOrdreAppartenance] =
    useState<ordre_appartenance | null>(null);

  // Repositories
  const getOrdreAppartenanceListRepository =
    new GetOrdreAppartenanceListRepository();
  const getOrdreAppartenanceByIdRepository =
    new GetOrdreAppartenanceByIdRepository();
  const createOrdreAppartenanceRepository =
    new CreateOrdreAppartenanceRepository();
  const editOrdreAppartenanceRepository = new EditOrdreAppartenanceRepository();
  const deleteOrdreAppartenanceRepository =
    new DeleteOrdreAppartenanceRepository();

  // Usecases
  const getOrdreAppartenanceListUsecase = new GetOrdreAppartenanceListUsecase(
    getOrdreAppartenanceListRepository
  );
  const getOrdreAppartenanceByIdUsecase = new GetOrdreAppartenanceByIdUsecase(
    getOrdreAppartenanceByIdRepository
  );
  const createOrdreAppartenanceUsecase = new CreateOrdreAppartenanceUsecase(
    createOrdreAppartenanceRepository
  );
  const editOrdreAppartenanceUsecase = new EditOrdreAppartenanceUsecase(
    editOrdreAppartenanceRepository
  );
  const deleteOrdreAppartenanceUsecase = new DeleteOrdreAppartenanceUsecase(
    deleteOrdreAppartenanceRepository
  );

  const getOrdreAppartenanceList = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getOrdreAppartenanceListUsecase.execute();
      setOrdreAppartenanceList(data);
      return data;
    } catch (err) {
      setError(
        err.message ||
          "Une erreur est survenue lors de la récupération des ordres d'appartenance"
      );
      return [];
    } finally {
      setLoading(false);
    }
  };

  const getOrdreAppartenanceById = async (id: number) => {
    setLoading(true);
    setError(null);
    try {
      const data = await getOrdreAppartenanceByIdUsecase.execute(id);
      setSelectedOrdreAppartenance(data);
      return data;
    } catch (err) {
      setError(
        err.message ||
          "Une erreur est survenue lors de la récupération de l'ordre d'appartenance"
      );
      return null;
    } finally {
      setLoading(false);
    }
  };

  const createOrdreAppartenance = async (
    ordreAppartenanceData: Omit<ordre_appartenance, "id">
  ) => {
    setLoading(true);
    setError(null);
    try {
      const data = await createOrdreAppartenanceUsecase.execute(
        ordreAppartenanceData
      );
      // Refresh the list
      await getOrdreAppartenanceList();
      return data;
    } catch (err) {
      setError(
        err.message ||
          "Une erreur est survenue lors de la création de l'ordre d'appartenance"
      );
      return null;
    } finally {
      setLoading(false);
    }
  };

  const editOrdreAppartenance = async (
    id: number,
    ordreAppartenanceData: Partial<ordre_appartenance>
  ) => {
    setLoading(true);
    setError(null);
    try {
      const data = await editOrdreAppartenanceUsecase.execute(
        id,
        ordreAppartenanceData
      );
      // Refresh the list
      await getOrdreAppartenanceList();
      return data;
    } catch (err) {
      setError(
        err.message ||
          "Une erreur est survenue lors de la modification de l'ordre d'appartenance"
      );
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deleteOrdreAppartenance = async (id: number) => {
    setLoading(true);
    setError(null);
    try {
      const data = await deleteOrdreAppartenanceUsecase.execute(id);
      // Refresh the list
      await getOrdreAppartenanceList();
      return data;
    } catch (err) {
      setError(
        err.message ||
          "Une erreur est survenue lors de la suppression de l'ordre d'appartenance"
      );
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    ordreAppartenanceList,
    selectedOrdreAppartenance,
    getOrdreAppartenanceList,
    getOrdreAppartenanceById,
    createOrdreAppartenance,
    editOrdreAppartenance,
    deleteOrdreAppartenance,
  };
};
