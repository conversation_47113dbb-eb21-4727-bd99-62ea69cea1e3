import { useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { useFormContext } from "react-hook-form";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

// Actions pour gérer l'état de la prévisualisation
const OPEN_PREVIEW = "cabinetMedical/openPreview";
const CLOSE_PREVIEW = "cabinetMedical/closePreview";

// Action creators
export const openPreviewAction = () => ({ type: OPEN_PREVIEW });
export const closePreviewAction = () => ({ type: CLOSE_PREVIEW });

// Interface pour l'état de la prévisualisation
interface PreviewState {
  open: boolean;
}

// Reducer pour gérer l'état de la prévisualisation
export const previewReducer = (
  state: PreviewState = { open: false },
  action: { type: string },
): PreviewState => {
  switch (action.type) {
    case OPEN_PREVIEW:
      return { ...state, open: true };
    case CLOSE_PREVIEW:
      return { ...state, open: false };
    default:
      return state;
  }
};

/**
 * Hook personnalisé pour le composant CabinetMedicalPreview
 */
export const useCabinetMedicalPreview = () => {
  const { watch, getValues } = useFormContext<CabinetMedicalFormDTO>();
  const dispatch = useDispatch();

  // Utiliser le state global pour l'état de la prévisualisation
  const previewOpen = useSelector((state: RootState) =>
    state.preview ? state.preview.open : false
  );

  // Fonction pour ouvrir la prévisualisation
  const openPreview = useCallback(() => {
    dispatch(openPreviewAction());
  }, [dispatch]);

  // Fonction pour fermer la prévisualisation
  const closePreview = useCallback(() => {
    dispatch(closePreviewAction());
  }, [dispatch]);

  const {
    titre,
    nom,
    prenom,
    specialities,
    raison_sociale,
    adresse,
    region,
    district,
    commune,
    fokotany,
    infoAcces,
    email,
    telephone,
    presentation,
    profileImagePreviewUrl,
    cabinetImagesPreviewUrls,
    typeConsultation,
    nouveauPatientAcceptes,
    paymentMethods,
    insurances,
    motCles,
    diplomes,
    experiences,
    publications,
    langues,
  } = getValues();

  // Créer l'objet de données pour la prévisualisation avec useMemo pour éviter des recréations inutiles
  const previewData = useMemo(
    () => ({
      titre,
      nom,
      prenom,
      specialities,
      raisonSociale: raison_sociale,
      adresse,
      region,
      district,
      commune,
      fokotany,
      infoAcces,
      email,
      telephone,
      presentation,
      profileImage: profileImagePreviewUrl,
      cabinetImages: cabinetImagesPreviewUrls,
      typeConsultation,
      nouveauPatientAcceptes,
      paymentMethods,
      insurances,
      motCles,
      diplomes,
      experiences,
      publications,
      langues,
    }),
    [
      titre,
      nom,
      prenom,
      specialities,
      raison_sociale,
      adresse,
      region,
      district,
      commune,
      fokotany,
      infoAcces,
      email,
      telephone,
      presentation,
      profileImagePreviewUrl,
      cabinetImagesPreviewUrls,
      typeConsultation,
      nouveauPatientAcceptes,
      paymentMethods,
      insurances,
      motCles,
      diplomes,
      experiences,
      publications,
      langues,
    ],
  );

  return {
    previewOpen,
    openPreview,
    closePreview,
    previewData,
  };
};
