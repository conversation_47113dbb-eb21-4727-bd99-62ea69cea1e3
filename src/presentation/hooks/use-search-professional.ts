import { AppDispatch, RootState } from "@/store";
import { searchProfessionalsSlice } from "@/application/slices/professionnal/searchProfessionalSlice";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  searchProfessional as searchProfessionalAction,
  searchProfessionalById as searchProfessionalByIdAction,
  setCurrentProfessional,
} from "@/application/slices/professionnal/searchProfessionalSlice";
import {
  searchProfessionalByIdParams,
  searchProfessionalsParams,
} from "@/domain/usecases/professional/GetProfessionnalInformations/types";
import { toast } from "sonner";
import { useParams } from "react-router-dom";
import { extractSearchParams } from "@/shared/utils/routeParamUtils";

const useSearchProfessional = () => {
  const dispatch = useDispatch<AppDispatch>();

  const routeParams = useParams<{
    localization?: string;
    speciality?: string;
  }>();

  // Extraire et nettoyer les paramètres de route en utilisant la fonction utilitaire
  const { localization: localizationFilter, speciality: specialityFilter } = extractSearchParams(routeParams);

  const [searchLocationKey, setSearchLocationKey] =
    useState<string>(localizationFilter);
  const [searchSpecialityKey, setSearchSpecialityKey] =
    useState<string>(specialityFilter);

  const { professionals, currentProfessional, loading, error, searchType } =
    useSelector((state: RootState) => state.searchProfessionals);

  const setSearchType = (type: "symptome" | "name") =>
    dispatch(searchProfessionalsSlice.actions.setSearchType(type));

  const searchProfessional = async (
    searchTerm: Omit<searchProfessionalsParams, "today">,
  ) => {
    await dispatch(searchProfessionalAction(searchTerm));
  };

  const searchProfessionalById = async (
    searchParams: Omit<searchProfessionalByIdParams, "today">,
  ) => {
    await dispatch(searchProfessionalByIdAction(searchParams));
  };

  const setCurrentProfessionale = (id: number) => {
    const professional = professionals?.find((p) => p.id === id) || null;
    dispatch(setCurrentProfessional(professional));
    return professional;
  };

  useEffect(() => {
    toast.error(error);
  }, [error]);

  return {
    loading,
    professionals,
    currentProfessional,
    setCurrentProfessionale,
    searchProfessional,
    searchProfessionalById,
    searchSpecialityKey,
    setSearchSpecialityKey,
    searchType,
    setSearchType,
    searchLocationKey,
    setSearchLocationKey,
  };
};

export default useSearchProfessional;
