import * as XLSX from 'xlsx';
import { useToast } from '@/presentation/components/common/toast/Toast';
import { Employer } from "@/domain/models";
import { useAppSelector } from '../redux';
import { useEffect, useState } from 'react';

type EmployerImportable = Omit<
    Employer,
    'id' | 'id_utilisateur' | 'id_dash' | 'cree_a' | 'mis_a_jour_a'
>;

interface UseLogicImportEmployeeProps {
    onImport: (employees: EmployerImportable[]) => Promise<void>;
    existingEmployees: Employer[];
    options: { onProgress?: (percent: number) => void };
}

export const useLogicImportEmployee = ({ onImport, existingEmployees, options }: UseLogicImportEmployeeProps) => {
    const toast = useToast();
    const [employerCount, setEmpoyerCount] = useState(0);

    const { counter } = useAppSelector((state) => state.employer);

    useEffect(() => {
        if (counter > 0 && employerCount > 0) {
            const progress = 51 + Math.min(49, Math.round((counter / employerCount) * 49));
            options?.onProgress?.(progress);
        }
    }, [counter, employerCount]);

    const convertExcelDate = (value: any): Date | undefined => {
        if (!value) return undefined;
        if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
            const date = new Date(value);
            return isNaN(date.getTime()) ? undefined : date;
        }
        if (typeof value === 'number') {
            const excelEpoch = new Date(1899, 11, 30);
            const date = new Date(excelEpoch.getTime() + value * 86400000);
            return (date.getFullYear() > 1900 && date.getFullYear() < 2100) ? date : undefined;
        }
        const date = new Date(value);
        return (!isNaN(date.getTime()) && date.getFullYear() > 1900 && date.getFullYear() < 2100) ? date : undefined;
    };

    const validateEmployeeData = (data: any): EmployerImportable | null => {
        const employee: EmployerImportable = {
            nom: String(data.nom || data.Nom || data.NOM || '').trim(),
            prenom: String(data.prenom || data.Prenom || data.PRENOM || data.prénom || data.Prénom || '').trim(),
            fonction: String(data.fonction || data.Fonction || data.FONCTION || '').trim(),
            direction: String(data.direction || data.Direction || data.DIRECTION || '').trim(),
            matricule: data.matricule || data.Matricule || data.MATRICULE || undefined,
            date_de_naissance: convertExcelDate(data.date_de_naissance || data.Date_de_naissance || data.DATE_DE_NAISSANCE),
            date_entree_en_fonction: convertExcelDate(data.date_entree_en_fonction || data.Date_entree_en_fonction || data.DATE_ENTREE_EN_FONCTION),
            status_administratif: data.status_administratif || data.Status_administratif || data.STATUS_ADMINISTRATIF,
            sexe: data.sexe || data.Sexe || data.SEXE || undefined,
            photo: data.photo || data.Photo || data.PHOTO || undefined,
        };

        if (!employee.nom || !employee.prenom || !employee.fonction || !employee.direction) {
            return null;
        }

        return employee;
    };

    const isEmployeeExists = (employee: EmployerImportable): boolean => {
        return existingEmployees.some(existing => {
            if (employee.matricule && existing.matricule === employee.matricule) return true;
            return (
                existing.nom.toLowerCase() === employee.nom.toLowerCase() &&
                existing.prenom.toLowerCase() === employee.prenom.toLowerCase()
            );
        });
    };

    const handleFileImport = async (file: File) => {
        try {
            options?.onProgress?.(0);

            const data = await file.arrayBuffer();
            options?.onProgress?.(1);

            const workbook = XLSX.read(data);
            options?.onProgress?.(4);

            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);
            options?.onProgress?.(10);

            if (jsonData.length === 0) {
                toast.error("Fichier vide");
                return;
            }

            const validEmployees: EmployerImportable[] = [];
            const invalidRows: number[] = [];
            const duplicateRows: number[] = [];

            jsonData.forEach((row, index) => {
                const employee = validateEmployeeData(row);
                const currentProgress = 10 + Math.round((index / jsonData.length) * 40);
                options?.onProgress?.(currentProgress);

                if (employee) {
                    if (isEmployeeExists(employee)) {
                        duplicateRows.push(index + 2);
                    } else {
                        validEmployees.push(employee);
                    }
                } else {
                    invalidRows.push(index + 2);
                }
            });

            if (validEmployees.length === 0) {
                const message = duplicateRows.length > 0
                    ? 'Tous les employés du fichier existent déjà dans la base de données'
                    : 'Aucune donnée valide trouvée dans le fichier. Vérifiez les colonnes: nom, prenom, fonction, direction';
                toast.info(message);
                return;
            }

            if (invalidRows.length > 0 || duplicateRows.length > 0) {
                const messages = [];
                if (invalidRows.length > 0) {
                    messages.push(`${invalidRows.length} ligne(s) invalide(s) (lignes: ${invalidRows.join(', ')})`);
                }
                if (duplicateRows.length > 0) {
                    messages.push(`${duplicateRows.length} doublon(s) ignoré(s) (lignes: ${duplicateRows.join(', ')})`);
                }
                toast.info(`${validEmployees.length} employé(s) valide(s) trouvé(s). ${messages.join('. ')}`);
            }

            setEmpoyerCount(validEmployees.length);
            options?.onProgress?.(51); // Marqueur avant import réel

            await onImport(validEmployees);

            options?.onProgress?.(100);
            toast.success(`${validEmployees.length} employé(s) importé(s) avec succès`);
        } catch (error) {
            console.error("Erreur lors de l'import :", error);
            toast.error("Erreur lors de la lecture du fichier Excel");
            options?.onProgress?.(0);
        }
    };

    return {
        handleFileImport,
    };
};
