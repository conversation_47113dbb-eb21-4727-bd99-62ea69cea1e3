import { Employer } from "@/domain/models";
import { useCallback } from "react";
import {
  createEmployerThunk,
  getEmployersByDashIdThunk,
  getEmployerByIdThunk,
  updateEmployerThunk,
  deleteEmployerThunk,
  setSelectedEmployer,
  clearSelectedEmployer,
  createMultipleEmployes,
} from "@/application/slices/employer/employerSlice";
import { useAppDispatch, useAppSelector } from "@/presentation/hooks/redux";
import { EmployerFormData } from "@/shared/schemas/EmployerSchema";
import { useToast } from "../use-toast";
import { GetLocaleDate } from "@/shared/utils/getLocaleDate";

export const useEmployer = () => {
  const dispatch = useAppDispatch();
  const toast = useToast();
  const {
    employers,
    selectedEmplyerSlice: selectedEmployerSlice,
    loading,
    error,
  } = useAppSelector((state) => state.employer);
  const dashId = useAppSelector((state) => state.authentification.userData?.id);
  const createEmploye = useCallback(
    async (data: EmployerFormData, profilePhoto: File) => {
      if (!dashId) {
        toast.error("Dash non conectee");
        return false;
      }

      const register = await dispatch(
        createEmployerThunk({
          data: {
            date_de_naissance: data.date_de_naissance,
            date_entree_en_fonction: data.date_entree_en_fonction,
            fonction: data.fonction,
            id_dash: dashId,
            matricule: data.matricule,
            nom: data.nom,
            photo: data.photo,
            prenom: data.prenom,
            sexe: data.sexe,
            status_administratif: data.status_administratif,
            direction: data.direction,
            cree_a: GetLocaleDate().toISOString(),
            mis_a_jour_a: GetLocaleDate().toISOString(),
          },
          profilePhoto: profilePhoto,
        })
      );

      const success = register.meta.requestStatus === "fulfilled";

      if (success) {
        toast.success("Nouveau employé ajouté.");
      } else {
        const errorMessage = register.payload as string;
        toast.error(errorMessage);
        return false;
      }

      return true;
    },
    [dispatch]
  );

  const handleCreateMultipleEmployes = useCallback(
    async (data: Omit<Employer, "id" | "id_utilisateur">[]) => {
      if (!dashId) {
        toast.error("Dash non connectée");
        return false;
      }

      const formatedData = data.map((emp) => ({
        ...emp,
        id_dash: emp.id_dash ?? dashId,
        cree_a: GetLocaleDate().toISOString(),
        mis_a_jour_a: GetLocaleDate().toISOString(),
      }));

      const result = await dispatch(
        createMultipleEmployes({
          data: formatedData,
          profilePhoto: null,
        })
      );

      const success = result.meta.requestStatus === "fulfilled";

      if (success) {
        toast.success("Employés ajoutés avec succès.");
      } else {
        const errorMessage = result.payload as string;
        toast.error(errorMessage);
        return false;
      }

      return true;
    },
    [dispatch, dashId]
  );

  const get = useCallback(
    async (id: number) => {
      await dispatch(getEmployerByIdThunk(id));
    },
    [dispatch]
  );

  const getAll = useCallback(
    async (dashId: number) => {
      await dispatch(getEmployersByDashIdThunk(dashId));
    },
    [dispatch]
  );

  const updateEmployer = useCallback(
    async (
      userId: number,
      id: number,
      data: EmployerFormData,
      profilePhoto: File,
      lastPath: string
    ) => {
      const formatedData: Omit<Employer, "id"> = {
        id_utilisateur: userId,
        date_de_naissance: data.date_de_naissance,
        date_entree_en_fonction: data.date_entree_en_fonction,
        fonction: data.fonction,
        id_dash: dashId,
        matricule: data.matricule,
        nom: data.nom,
        photo: data.photo,
        prenom: data.prenom,
        sexe: data.sexe,
        status_administratif: data.status_administratif,
        direction: data.direction,
        cree_a: GetLocaleDate().toISOString(),
        mis_a_jour_a: GetLocaleDate().toISOString(),
      };

      const update = await dispatch(
        updateEmployerThunk({
          id: id,
          data: formatedData,
          profilePhoto: profilePhoto,
          lastPath: lastPath,
        })
      );

      const success = update.meta.requestStatus === "fulfilled";

      if (!success) {
        toast.error(
          "Une erreur a ete rencontree lors de la mise a jour de l'employer"
        );
        return false;
      } else {
        toast.success("Mise a jour de l'employer bien prise en compte.");
      }

      return true;
    },
    [dispatch]
  );

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteEmployerThunk(id));
    },
    [dispatch]
  );

  const select = useCallback(
    (employer: Employer | null) => {
      dispatch(setSelectedEmployer(employer));
    },
    [dispatch]
  );

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedEmployer());
  }, [dispatch]);

  return {
    loading,
    error,
    employers,
    selectedEmployerSlice,
    createMultipleEmployes: handleCreateMultipleEmployes,
    createEmploye,
    select,
    get,
    getAll,
    updateEmployer,
    remove,
    clearSelected,
  };
};
