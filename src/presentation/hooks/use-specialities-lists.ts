import { getSpecialities } from "@/application/slices/professionnal/listeSpecialitesSlice";
import { SupabaseError } from "@/infrastructure/supabase/supabaseError";
import { AppDispatch, RootState } from "@/store";
import { useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";

const useSpecialitiesLists = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { listes, loading, error, isPopoverOpen } = useSelector(
    (state: RootState) => state.listeSpecialites
  );

  const getSpecialitiesList = useCallback(async () => {
    try {
      if (listes.length == 0) await dispatch(getSpecialities());
    } catch (error) {
      toast.error(
        error instanceof SupabaseError
          ? error.message
          : "Erreur lors de la recuperation des lites de specialitees"
      );
    }
  }, [dispatch]);

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  return {
    listes,
    loading,
    isPopoverOpen,
    getSpecialitiesList,
  };
};

export default useSpecialitiesLists;
