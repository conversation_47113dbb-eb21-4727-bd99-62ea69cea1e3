import { useMemo, useState } from "react";
import { FacturationDTO } from "@/domain/DTOS/FacturationDTO";

/**
 * Interface représentant un patient dans le contexte du filtrage des facturations
 */
export interface PatientOption {
  /** Identifiant unique du patient */
  id: number;
  /** Nom de famille du patient */
  nom: string;
  /** Prénom du patient */
  prenom: string;
}

/**
 * Interface de retour du hook useFacturationFilters
 */
export interface FacturationFiltersState {
  /** Patient actuellement sélectionné pour le filtrage */
  selectedPatient: PatientOption | null;
  /** Liste des patients disponibles extraits des facturations */
  availablePatients: PatientOption[];
  /** Facturations filtrées selon le patient sélectionné */
  filteredFacturations: FacturationDTO[];
  /** Solde total calculé à partir des facturations filtrées */
  totalBalance: number;
  /** Fonction pour modifier le patient sélectionné */
  handlePatientChange: (patient: PatientOption | null) => void;
  /** Fonction pour réinitialiser le filtre */
  clearFilter: () => void;
  /** Indique si un filtre est actuellement appliqué */
  isFilterActive: boolean;
}

/**
 * Hook personnalisé pour gérer la logique de filtrage des facturations par patient
 *
 * Ce hook centralise toute la logique métier liée au filtrage des facturations :
 * - Extraction des patients uniques depuis les facturations
 * - Gestion de l'état du patient sélectionné
 * - Filtrage des facturations selon le patient sélectionné
 * - Calcul automatique du solde total
 *
 * @param facturations - Liste des facturations à filtrer
 * @returns État et fonctions de gestion du filtrage
 *
 * @example
 * ```typescript
 * const {
 *   selectedPatient,
 *   availablePatients,
 *   filteredFacturations,
 *   totalBalance,
 *   handlePatientChange
 * } = useFacturationFilters(facturations);
 * ```
 */
export const useFacturationFilters = (
  facturations: FacturationDTO[]
): FacturationFiltersState => {
  // État local pour le patient sélectionné
  const [selectedPatient, setSelectedPatient] = useState<PatientOption | null>(
    null
  );

  /**
   * Extrait la liste des patients uniques depuis les facturations
   * Utilise une Map pour éviter les doublons de manière efficace
   */
  const availablePatients = useMemo((): PatientOption[] => {
    if (!facturations || facturations.length === 0) {
      return [];
    }

    const patientsMap = new Map<number, PatientOption>();

    facturations.forEach((facturation) => {
      const patient = facturation.patient;
      if (patient && !patientsMap.has(facturation.id_patient)) {
        patientsMap.set(facturation.id_patient, {
          id: facturation.id_patient,
          nom: patient.nom,
          prenom: patient.prenom,
        });
      }
    });

    return Array.from(patientsMap.values());
  }, [facturations]);

  /**
   * Filtre les facturations selon le patient sélectionné.
   * Si aucun patient n'est sélectionné, retourne toutes les facturations
   */
  const filteredFacturations = useMemo((): FacturationDTO[] => {
    if (!facturations) {
      return [];
    }

    if (selectedPatient === null) {
      return facturations;
    }

    return facturations.filter(
      (facturation) => facturation.id_patient === selectedPatient.id
    );
  }, [facturations, selectedPatient]);

  /**
   * Calcule le solde total des facturations filtrées
   * Formule : Somme(montant - total_payé) pour chaque facturation
   */
  const totalBalance = useMemo((): number => {
    return filteredFacturations.reduce(
      (acc, facturation) =>
        acc + (facturation.montant - facturation.total_paye),
      0
    );
  }, [filteredFacturations]);

  /**
   * Gère le changement de patient sélectionné
   * @param patient - Nouveau patient à sélectionner ou null pour désélectionner
   */
  const handlePatientChange = (patient: PatientOption | null) => {
    setSelectedPatient(patient);
  };

  /**
   * Réinitialise le filtre en désélectionnant le patient
   */
  const clearFilter = () => {
    setSelectedPatient(null);
  };

  /**
   * Indique si un filtre est actuellement appliqué
   */
  const isFilterActive = selectedPatient !== null;

  return {
    selectedPatient,
    availablePatients,
    filteredFacturations,
    totalBalance,
    handlePatientChange,
    clearFilter,
    isFilterActive,
  };
};
