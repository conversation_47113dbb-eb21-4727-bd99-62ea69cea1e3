import { useState } from "react";
import { Facturation } from "@/domain/models";

/**
 * Interface pour les données du formulaire de facturation
 */
export interface FacturationFormData {
  /** Montant de la facturation */
  montant: number | null;
  /** Montant total payé par le patient */
  totalPaye: number | null;
  /** Num<PERSON>ro ou référence du reçu */
  recu: string;
  /** Informations complémentaires sur la facturation */
  informations: string;
  /** Date de paiement de la facturation */
  datePaiement: Date | null;
  /** Fonction pour modifier le montant */
  setMontant: (value: number | null) => void;
  /** Fonction pour modifier le total payé */
  setTotalPaye: (value: number | null) => void;
  /** Fonction pour modifier le reçu */
  setRecu: (value: string) => void;
  /** Fonction pour modifier les informations */
  setInformations: (value: string) => void;
  /** Fonction pour modifier la date de paiement */
  setDatePaiement: (value: Date | null) => void;
}

/**
 * Hook personnalisé pour la gestion du formulaire de facturation
 *
 * Ce hook centralise la logique de gestion d'état du formulaire de facturation,
 * incluant la validation, l'initialisation et la réinitialisation des données.
 * Il fournit une interface cohérente pour créer et modifier des facturations.
 *
 * Fonctionnalités :
 * - Gestion de l'état de tous les champs du formulaire
 * - Validation automatique des données saisies
 * - Initialisation du formulaire avec des données existantes
 * - Réinitialisation complète du formulaire
 * - Génération des données formatées pour l'API
 *
 * @param professionalId - Identifiant du professionnel (optionnel)
 * @param patientId - Identifiant du patient (optionnel)
 * @returns Objet contenant les données et fonctions du formulaire
 *
 * @example
 * ```typescript
 * const {
 *   formData,
 *   isFormValid,
 *   getFacturationData,
 *   resetForm,
 *   initialiseState
 * } = useFacturationForm(professionalId, patientId);
 * ```
 */
export const useFacturationForm = (
  professionalId?: number,
  patientId?: number
) => {
  // États locaux pour chaque champ du formulaire
  const [montant, setMontant] = useState<number | null>(null);
  const [totalPaye, setTotalPaye] = useState<number | null>(null);
  const [recu, setRecu] = useState("");
  const [informations, setInformations] = useState("");
  const [datePaiement, setDatePaiement] = useState<Date | null>(null);

  /**
   * Vérifie si le formulaire est valide
   * Tous les champs obligatoires doivent être remplis
   * @returns true si le formulaire est valide, false sinon
   */
  const isFormValid = () => {
    return montant && recu && totalPaye && informations && datePaiement;
  };

  /**
   * Initialise le formulaire avec les données d'une facturation existante
   * Utilisé pour l'édition d'une facturation
   * @param facturation - Données de la facturation à charger
   */
  const initialiseState = (facturation: Facturation) => {
    setMontant(facturation.montant);
    setTotalPaye(facturation.total_paye);
    setRecu(facturation.recu);
    setInformations(facturation.informations);
    // TODO: Gérer la conversion de la date de paiement
    // setDatePaiement(facturation.date_paiement);
  };

  /**
   * Génère les données de facturation formatées pour l'API
   * @returns Objet Facturation sans l'ID (pour création)
   */
  const getFacturationData = (): Omit<Facturation, "id"> => ({
    id_professionnel: professionalId,
    id_patient: patientId,
    montant: montant!,
    total_paye: totalPaye!,
    recu: recu,
    informations: informations,
    date_paiement: datePaiement!,
    date_creation: new Date(),
  });

  /**
   * Réinitialise tous les champs du formulaire à leurs valeurs par défaut
   * Utilisé après soumission ou annulation du formulaire
   */
  const resetForm = () => {
    setMontant(null);
    setTotalPaye(null);
    setRecu("");
    setInformations("");
    setDatePaiement(null);
  };

  return {
    formData: {
      montant,
      totalPaye,
      recu,
      informations,
      datePaiement,
      setMontant,
      setTotalPaye,
      setRecu,
      setInformations,
      setDatePaiement,
    },
    isFormValid: isFormValid(),
    getFacturationData,
    resetForm,
    initialiseState,
  };
};
