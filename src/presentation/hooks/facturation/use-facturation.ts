import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "../redux";
import {
  fetchFacturationById,
  fetchFacturationsByProfessionalId,
  fetchFacturationsByPatientId,
  createFacturation,
  updateFacturation,
  deleteFacturation,
  clearCurrentFacturation,
  clearError,
} from "@/application/slices/professionnal/facturationSlice";
import { Facturation } from "@/domain/models";

/**
 * Hook personnalisé pour la gestion des facturations
 *
 * Ce hook centralise toutes les opérations CRUD (Create, Read, Update, Delete)
 * liées aux facturations, ainsi que la gestion de l'état global des facturations
 * via Redux. Il fournit des fonctions pour interagir avec l'API de facturation
 * et gère automatiquement les états de chargement et d'erreur.
 *
 * Fonctionnalités :
 * - Récupération des facturations par professionnel ou patient
 * - Création, modification et suppression de facturations
 * - Gestion automatique des états de chargement et d'erreur
 * - Vérification de la connectivité réseau
 * - Intégration avec le store Redux
 *
 * @returns Objet contenant les données et fonctions de gestion des facturations
 *
 * @example
 * ```typescript
 * const {
 *   listeFacturationProfessional,
 *   loading,
 *   error,
 *   getFacturationsByProfessionalId,
 *   handleCreateFacturation
 * } = useFacturation();
 * ```
 */
export const useFacturation = () => {
  // Dispatch Redux pour déclencher les actions
  const dispatch = useAppDispatch();

  // Sélection des données de facturation depuis le store Redux
  const {
    listeFacturationPatient,
    listeFacturationProfessional,
    currentFacturation,
    loading,
    error,
  } = useAppSelector((state) => state.facturation);

  /**
   * Récupère une facturation spécifique par son ID
   * @param id - Identifiant unique de la facturation
   * @throws {Error} Si aucune connexion internet n'est disponible
   */
  const getFacturationById = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(fetchFacturationById(id)).unwrap();
    },
    [dispatch]
  );

  /**
   * Récupère toutes les facturations d'un professionnel de santé
   * @param professionalId - Identifiant unique du professionnel
   * @throws {Error} Si aucune connexion internet n'est disponible
   */
  const getFacturationsByProfessionalId = useCallback(
    async (professionalId: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(
        fetchFacturationsByProfessionalId(professionalId)
      ).unwrap();
    },
    [dispatch]
  );

  /**
   * Récupère toutes les facturations d'un patient
   * @param patientId - Identifiant unique du patient
   * @throws {Error} Si aucune connexion internet n'est disponible
   */
  const getFacturationsByPatientId = useCallback(
    async (patientId: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(fetchFacturationsByPatientId(patientId)).unwrap();
    },
    [dispatch]
  );

  const handleCreateFacturation = useCallback(
    async (
      facturation: Omit<Facturation, "id">,
      patient: { id: number; nom: string; prenom: string },
      professionnel: { id: number; nom: string; prenom: string }
    ) => {
      const result = await dispatch(
        createFacturation({ facturation, patient, professionnel })
      ).unwrap();
      return result;
    },
    [dispatch]
  );

  const handleUpdateFacturation = useCallback(
    async (id: number, facturationData: Partial<Facturation>) => {
      const result = await dispatch(
        updateFacturation({ id, facturation: facturationData })
      ).unwrap();
      return result;
    },
    [dispatch]
  );

  const handleDeleteFacturation = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      const result = await dispatch(deleteFacturation(id)).unwrap();
      return result;
    },
    [dispatch]
  );

  return {
    listeFacturationPatient,
    listeFacturationProfessional,
    currentFacturation,
    loading,
    error,
    getFacturationById,
    getFacturationsByProfessionalId,
    getFacturationsByPatientId,
    handleCreateFacturation,
    handleUpdateFacturation,
    handleDeleteFacturation,
  };
};
