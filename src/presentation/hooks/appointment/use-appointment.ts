import {
  markAppointmentAsDone,
  setSelectedAppointment as setSelectedAppointmentAction,
} from "@/application/slices/professionnal/appointmentSlice";
import { useAppDispatch } from "../redux";
import { RendezVous } from "@/domain/models";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const useAppointment = () => {
  const dispatch = useAppDispatch();
  const { selectedAppointment } = useSelector(
    (state: RootState) => state.appointment,
  );

  const setSelectedAppointment = (appointment: RendezVous) => {
    dispatch(setSelectedAppointmentAction(appointment));
  };

  const terminateAppointment = async (appointmentId: number) => {
    await dispatch(markAppointmentAsDone({ id: appointmentId }));
  };

  return { terminateAppointment, selectedAppointment, setSelectedAppointment };
};

export default useAppointment;
