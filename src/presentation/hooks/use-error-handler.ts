import { useCallback } from "react";
import { useToast } from "./use-toast";

/**
 * Hook personnalisé pour la gestion centralisée des erreurs
 *
 * @description Ce hook fournit des méthodes standardisées pour gérer
 * les erreurs dans l'application avec des messages utilisateur appropriés
 * en français et un logging détaillé pour le débogage.
 */
export const useErrorHandler = () => {
  const toast = useToast();

  /**
   * Gère les erreurs de validation côté client
   *
   * @param message - Message d'erreur à afficher à l'utilisateur
   * @param field - Champ concerné par l'erreur (optionnel)
   */
  const handleValidationError = useCallback(
    (message: string, field?: string) => {
      const fullMessage = field
        ? `Erreur de validation pour ${field}: ${message}`
        : `Erreur de validation: ${message}`;

      if (toast) {
        toast.error(fullMessage);
      }
      console.warn("Validation Error:", { message, field });
    },
    [toast]
  );

  /**
   * <PERSON><PERSON> les erreurs de réseau ou d'API
   *
   * @param error - Erreur capturée
   * @param operation - Nom de l'opération qui a échoué
   * @param userMessage - Message personnalisé pour l'utilisateur (optionnel)
   */
  const handleApiError = useCallback(
    (error: unknown, operation: string, userMessage?: string) => {
      const defaultMessage = `Erreur lors de ${operation}. Veuillez réessayer.`;
      const displayMessage = userMessage || defaultMessage;

      if (toast) {
        toast.error(displayMessage);
      }
      console.error(`API Error in ${operation}:`, error);
    },
    [toast]
  );

  /**
   * Gère les erreurs de permissions ou d'autorisation
   *
   * @param operation - Opération qui a été refusée
   */
  const handlePermissionError = useCallback(
    (operation: string) => {
      const message = `Vous n'avez pas les permissions nécessaires pour ${operation}`;
      if (toast) {
        toast.error(message);
      }
      console.warn("Permission Error:", { operation });
    },
    [toast]
  );

  /**
   * Gère les erreurs génériques avec logging approprié
   *
   * @param error - Erreur capturée
   * @param context - Contexte où l'erreur s'est produite
   * @param userMessage - Message pour l'utilisateur (optionnel)
   */
  const handleGenericError = useCallback(
    (error: unknown, context: string, userMessage?: string) => {
      const defaultMessage =
        "Une erreur inattendue s'est produite. Veuillez réessayer.";
      const displayMessage = userMessage || defaultMessage;

      if (toast) {
        toast.error(displayMessage);
      }
      console.error(`Error in ${context}:`, error);
    },
    [toast]
  );

  /**
   * Affiche un message de succès standardisé
   *
   * @param operation - Opération qui a réussi
   * @param customMessage - Message personnalisé (optionnel)
   */
  const handleSuccess = useCallback(
    (operation: string, customMessage?: string) => {
      const defaultMessage = `${operation} effectué avec succès`;
      const displayMessage = customMessage || defaultMessage;

      if (toast) {
        toast.success(displayMessage);
      }
    },
    [toast]
  );

  return {
    handleValidationError,
    handleApiError,
    handlePermissionError,
    handleGenericError,
    handleSuccess,
  };
};
