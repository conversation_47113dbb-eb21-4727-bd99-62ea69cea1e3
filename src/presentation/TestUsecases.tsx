import React, { useState } from "react";

// Import des usecases EntreesStocks
import CreateEntreesStocksUsecase from "@/domain/usecases/entreesStocks/CreateEntreesStocksUsecase";
import GetEntreesStocksUsecase from "@/domain/usecases/entreesStocks/GetEntreesStocksUsecase";
import GetEntreesStocksByIdUsecase from "@/domain/usecases/entreesStocks/GetEntreesStocksByIdUsecase";
import UpdateEntreesStocksUsecase from "@/domain/usecases/entreesStocks/UpdateEntreesStocksUsecase";
import DeleteEntreesStocksUsecase from "@/domain/usecases/entreesStocks/DeleteEntreesStocksUsecase";

// Import des usecases SortiesStocks
import CreateSortiesStocksUsecase from "@/domain/usecases/sortiesStocks/CreateSortiesStocksUsecase";
import GetSortiesStocksUsecase from "@/domain/usecases/sortiesStocks/GetSortiesStocksUsecase";
import GetSortiesStocksByIdUsecase from "@/domain/usecases/sortiesStocks/GetSortiesStocksByIdUsecase";
import UpdateSortiesStocksUsecase from "@/domain/usecases/sortiesStocks/UpdateSortiesStocksUsecase";
import DeleteSortiesStocksUsecase from "@/domain/usecases/sortiesStocks/DeleteSortiesStocksUsecase";

// Import des usecases Stocks
import GetStocksUsecase from "@/domain/usecases/stocks/GetStocksUsecase";
import GetStockByIdUsecase from "@/domain/usecases/stocks/GetStockByIdUsecase";
import UpdateStocksUsecase from "@/domain/usecases/stocks/UpdateStocksUsecase";
import DeleteStocksUsecase from "@/domain/usecases/stocks/DeleteStocksUsecase";

// Import des repositories EntreesStocks
import CreateEntreesStocksRepository from "@/infrastructure/repositories/entreesStocks/CreateEntreesStocksRepository";
import GetEntreesStocksRepository from "@/infrastructure/repositories/entreesStocks/GetEntreesStocksRepository";
import GetEntreesStocksByIdRepository from "@/infrastructure/repositories/entreesStocks/GetEntreesStocksByIdRepository";
import UpdateEntreesStocksRepository from "@/infrastructure/repositories/entreesStocks/UpdateEntreesStocksRepository";
import DeleteEntreesStocksRepository from "@/infrastructure/repositories/entreesStocks/DeleteEntreesStocksRepository";

// Import des repositories SortiesStocks
import CreateSortiesStocksRepository from "@/infrastructure/repositories/sortiesStocks/CreateSortiesStocksRepository";
import GetSortiesStocksRepository from "@/infrastructure/repositories/sortiesStocks/GetSortiesStocksRepository";
import GetSortiesStocksByIdRepository from "@/infrastructure/repositories/sortiesStocks/GetSortiesStocksByIdRepository";
import UpdateSortiesStocksRepository from "@/infrastructure/repositories/sortiesStocks/UpdateSortiesStocksRepository";
import DeleteSortiesStocksRepository from "@/infrastructure/repositories/sortiesStocks/DeleteSortiesStocksRepository";

// Import des repositories Stocks
import CreateStocksRepository from "@/infrastructure/repositories/stocks/CreateStocksRepository";
import GetStocksRepository from "@/infrastructure/repositories/stocks/GetStocksRepository";
import GetStockByIdRepository from "@/infrastructure/repositories/stocks/GetStockByIdRepository";
import GetStocksByProductNameRepository from "@/infrastructure/repositories/stocks/GetStocksByProductNameRepository";
import UpdateStocksRepository from "@/infrastructure/repositories/stocks/UpdateStocksRepository";
import DeleteStocksRepository from "@/infrastructure/repositories/stocks/DeleteStocksRepository";

// Import des types
import { EntreesStocks } from "@/domain/models/EntreesStocks";
import { SortiesStocks } from "@/domain/models/SortiesStocks";
import { Stocks } from "@/domain/models/Stocks";

interface TestResult {
  success: boolean;
  message: string;
  data?:
    | EntreesStocks[]
    | SortiesStocks[]
    | Stocks[]
    | EntreesStocks
    | SortiesStocks
    | Stocks
    | boolean
    | null;
  businessLogicInfo?: string;
}

/**
 * Composant de test pour les usecases de gestion des entrées et sorties de stock
 *
 * Ce composant permet de tester la logique métier des usecases :
 * - Validation des données d'entrée
 * - Mise à jour automatique des stocks lors des entrées
 * - Logique FIFO lors des sorties de stock
 * - Gestion des cas d'erreur
 */
const TestUsecases: React.FC = () => {
  const [results, setResults] = useState<{ [key: string]: TestResult }>({});
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});

  // Données de test pour les entrées de stock
  const sampleEntreesStocks: Omit<EntreesStocks, "id">[] = [
    {
      id_produit: 1,
      fournisseur_id: 1,
      quantite: 100,
      prix_unitaire: 15.5,
      prix_entree: 1550,
    },
    {
      id_produit: 2,
      fournisseur_id: 2,
      quantite: 75,
      prix_unitaire: 22.0,
      prix_entree: 1650,
    },
  ];

  // Données de test pour les sorties de stock
  const sampleSortiesStocks: Omit<SortiesStocks, "id">[] = [
    {
      produit_id: 1,
      quantite: 25,
      type_sortie: "vente",
      date_sortie: "2024-01-15T10:00:00Z",
    },
    {
      produit_id: 2,
      quantite: 10,
      type_sortie: "consommation",
      date_sortie: "2024-01-16T14:30:00Z",
    },
  ];

  // ID de test pour les opérations
  const TEST_PROFESSIONAL_ID = 1;

  /**
   * Fonction utilitaire pour exécuter un test et gérer les états
   */
  const executeTest = async (
    testName: string,
    testFunction: () => Promise<{
      result:
        | EntreesStocks[]
        | SortiesStocks[]
        | Stocks[]
        | EntreesStocks
        | SortiesStocks
        | Stocks
        | boolean
        | null;
      businessLogicInfo?: string;
    }>
  ) => {
    setLoading((prev) => ({ ...prev, [testName]: true }));

    try {
      const { result, businessLogicInfo } = await testFunction();
      setResults((prev) => ({
        ...prev,
        [testName]: {
          success: true,
          message: `Test réussi pour ${testName}`,
          data: result,
          businessLogicInfo,
        },
      }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        [testName]: {
          success: false,
          message: `Erreur dans ${testName}: ${error instanceof Error ? error.message : "Erreur inconnue"}`,
          data: null,
        },
      }));
    } finally {
      setLoading((prev) => ({ ...prev, [testName]: false }));
    }
  };

  // Initialisation des repositories pour les usecases EntreesStocks
  const createEntreesStocksRepository = new CreateEntreesStocksRepository();
  const getEntreesStocksRepository = new GetEntreesStocksRepository();
  const getEntreesStocksByIdRepository = new GetEntreesStocksByIdRepository();
  const updateEntreesStocksRepository = new UpdateEntreesStocksRepository();
  const deleteEntreesStocksRepository = new DeleteEntreesStocksRepository();

  // Initialisation des repositories pour les usecases SortiesStocks
  const createSortiesStocksRepository = new CreateSortiesStocksRepository();
  const getSortiesStocksRepository = new GetSortiesStocksRepository();
  const getSortiesStocksByIdRepository = new GetSortiesStocksByIdRepository();
  const updateSortiesStocksRepository = new UpdateSortiesStocksRepository();
  const deleteSortiesStocksRepository = new DeleteSortiesStocksRepository();

  // Initialisation des repositories pour les stocks
  const createStocksRepository = new CreateStocksRepository();
  const getStocksRepository = new GetStocksRepository();
  const getStockByIdRepository = new GetStockByIdRepository();
  const getStocksByProductNameRepository =
    new GetStocksByProductNameRepository();
  const updateStocksRepository = new UpdateStocksRepository();
  const deleteStocksRepository = new DeleteStocksRepository();

  // Tests pour les usecases EntreesStocks
  const testCreateEntreesStocksUsecase = () => {
    const usecase = new CreateEntreesStocksUsecase(
      createEntreesStocksRepository,
      createStocksRepository,
      getStocksRepository,
      updateStocksRepository
    );

    return executeTest("CreateEntreesStocksUsecase", async () => {
      const result = await usecase.execute(sampleEntreesStocks);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Création d'entrées + mise à jour automatique des stocks correspondants",
      };
    });
  };

  const testGetEntreesStocksUsecase = () => {
    const usecase = new GetEntreesStocksUsecase(getEntreesStocksRepository);

    return executeTest("GetEntreesStocksUsecase", async () => {
      const result = await usecase.execute(TEST_PROFESSIONAL_ID);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation de l'ID professionnel + récupération sécurisée",
      };
    });
  };

  const testGetEntreesStocksByIdUsecase = () => {
    const usecase = new GetEntreesStocksByIdUsecase(
      getEntreesStocksByIdRepository
    );

    return executeTest("GetEntreesStocksByIdUsecase", async () => {
      const result = await usecase.execute(1);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation de l'ID + gestion des cas non trouvés",
      };
    });
  };

  const testUpdateEntreesStocksUsecase = () => {
    const usecase = new UpdateEntreesStocksUsecase(
      updateEntreesStocksRepository
    );
    const updateData = { quantite: 120, prix_unitaire: 16.0 };

    return executeTest("UpdateEntreesStocksUsecase", async () => {
      const result = await usecase.execute(1, updateData);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation des données + vérification quantité positive",
      };
    });
  };

  const testDeleteEntreesStocksUsecase = () => {
    const usecase = new DeleteEntreesStocksUsecase(
      deleteEntreesStocksRepository
    );

    return executeTest("DeleteEntreesStocksUsecase", async () => {
      const result = await usecase.execute(1);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation de l'ID + suppression sécurisée",
      };
    });
  };

  // Tests pour les usecases SortiesStocks
  const testCreateSortiesStocksUsecase = () => {
    const usecase = new CreateSortiesStocksUsecase(
      createSortiesStocksRepository,
      getStocksByProductNameRepository,
      updateStocksRepository
    );

    return executeTest("CreateSortiesStocksUsecase", async () => {
      const result = await usecase.execute(sampleSortiesStocks);
      return {
        result,
        businessLogicInfo:
          "🔥 Logique métier FIFO : Vérification stock disponible + déduction par ordre d'expiration + mise à jour automatique",
      };
    });
  };

  const testGetSortiesStocksUsecase = () => {
    const usecase = new GetSortiesStocksUsecase(getSortiesStocksRepository);

    return executeTest("GetSortiesStocksUsecase", async () => {
      const result = await usecase.execute(TEST_PROFESSIONAL_ID);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation de l'ID professionnel + récupération sécurisée",
      };
    });
  };

  const testGetSortiesStocksByIdUsecase = () => {
    const usecase = new GetSortiesStocksByIdUsecase(
      getSortiesStocksByIdRepository
    );

    return executeTest("GetSortiesStocksByIdUsecase", async () => {
      const result = await usecase.execute(1);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation de l'ID + gestion des cas non trouvés",
      };
    });
  };

  const testUpdateSortiesStocksUsecase = () => {
    const usecase = new UpdateSortiesStocksUsecase(
      updateSortiesStocksRepository
    );
    const updateData = { quantite: 15, type_sortie: "ajustement" };

    return executeTest("UpdateSortiesStocksUsecase", async () => {
      const result = await usecase.execute(1, updateData);
      return {
        result,
        businessLogicInfo:
          "⚠️ Logique métier : Validation des données (Note: recalcul des stocks non implémenté pour les mises à jour)",
      };
    });
  };

  const testDeleteSortiesStocksUsecase = () => {
    const usecase = new DeleteSortiesStocksUsecase(
      deleteSortiesStocksRepository
    );

    return executeTest("DeleteSortiesStocksUsecase", async () => {
      const result = await usecase.execute(1);
      return {
        result,
        businessLogicInfo:
          "⚠️ Logique métier : Validation de l'ID (Note: restauration des stocks non implémentée pour les suppressions)",
      };
    });
  };

  // Tests pour les usecases Stocks
  const testGetStocksUsecase = () => {
    const usecase = new GetStocksUsecase(getStocksRepository);

    return executeTest("GetStocksUsecase", async () => {
      const result = await usecase.execute(TEST_PROFESSIONAL_ID);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation de l'ID utilisateur + enrichissement des données (statut, jours avant expiration)",
      };
    });
  };

  const testGetStockByIdUsecase = () => {
    const usecase = new GetStockByIdUsecase(getStockByIdRepository);

    return executeTest("GetStockByIdUsecase", async () => {
      const result = await usecase.execute(1);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation de l'ID + enrichissement complet (statut, valeur totale, criticité)",
      };
    });
  };

  const testUpdateStocksUsecase = () => {
    const usecase = new UpdateStocksUsecase(
      updateStocksRepository,
      getStockByIdRepository
    );
    const updateData = { stock_actuel: 50, seuil_alerte: 10 };

    return executeTest("UpdateStocksUsecase", async () => {
      const result = await usecase.execute(1, updateData);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation complète + règles métier (seuils, dates) + alertes automatiques",
      };
    });
  };

  const testDeleteStocksUsecase = () => {
    const usecase = new DeleteStocksUsecase(
      deleteStocksRepository,
      getStockByIdRepository
    );

    return executeTest("DeleteStocksUsecase", async () => {
      const result = await usecase.execute(1);
      return {
        result,
        businessLogicInfo:
          "✅ Logique métier : Validation existence + règles de suppression + vérification dépendances",
      };
    });
  };

  /**
   * Composant pour afficher le résultat d'un test avec informations sur la logique métier
   */
  const TestResult: React.FC<{
    testName: string;
    result?: TestResult;
    isLoading?: boolean;
  }> = ({ result, isLoading }) => {
    if (isLoading) {
      return <div className="text-blue-600 text-sm">⏳ Test en cours...</div>;
    }

    if (!result) {
      return <div className="text-gray-500 text-sm">Aucun test exécuté</div>;
    }

    return (
      <div
        className={`text-sm p-3 rounded-lg ${result.success ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"}`}
      >
        <div
          className={`font-medium ${result.success ? "text-green-700" : "text-red-700"}`}
        >
          {result.success ? "✅" : "❌"} {result.message}
        </div>

        {result.businessLogicInfo && (
          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-blue-700 text-xs">
            <strong>Logique métier :</strong> {result.businessLogicInfo}
          </div>
        )}

        {result.data && (
          <div className="mt-2 text-xs text-gray-600">
            <strong>Données retournées :</strong>
            <pre className="mt-1 p-2 bg-gray-100 rounded overflow-x-auto">
              {JSON.stringify(result.data, null, 2).substring(0, 300)}
              {JSON.stringify(result.data).length > 300 && "\n..."}
            </pre>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">
        🧪 Tests des Usecases - Logique Métier des Stocks
      </h1>

      <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">
          ⚠️ Tests de logique métier
        </h3>
        <div className="text-sm text-yellow-700 space-y-1">
          <p>
            • <strong>Entrées de stock :</strong> Validation + création
            automatique des stocks
          </p>
          <p>
            • <strong>Sorties de stock :</strong> Logique FIFO + vérification
            disponibilité + mise à jour automatique
          </p>
          <p>
            • <strong>Validation :</strong> Contrôle des données d'entrée et
            gestion des erreurs
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
        {/* Section Usecases EntreesStocks */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-blue-600 mb-6 flex items-center">
            📥 Usecases EntreesStocks
            <span className="ml-2 text-sm bg-blue-100 text-blue-700 px-2 py-1 rounded">
              Logique Métier
            </span>
          </h2>

          <div className="space-y-6">
            <div>
              <button
                onClick={testCreateEntreesStocksUsecase}
                disabled={loading.CreateEntreesStocksUsecase}
                className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                🔄 Tester CreateEntreesStocksUsecase
                <div className="text-xs mt-1 opacity-90">
                  Création + Mise à jour stocks
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="CreateEntreesStocksUsecase"
                  result={results.CreateEntreesStocksUsecase}
                  isLoading={loading.CreateEntreesStocksUsecase}
                />
              </div>
            </div>

            <div>
              <button
                onClick={testGetEntreesStocksUsecase}
                disabled={loading.GetEntreesStocksUsecase}
                className="w-full bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                📋 Tester GetEntreesStocksUsecase
                <div className="text-xs mt-1 opacity-90">
                  Récupération avec validation
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="GetEntreesStocksUsecase"
                  result={results.GetEntreesStocksUsecase}
                  isLoading={loading.GetEntreesStocksUsecase}
                />
              </div>
            </div>

            <div>
              <button
                onClick={testGetEntreesStocksByIdUsecase}
                disabled={loading.GetEntreesStocksByIdUsecase}
                className="w-full bg-purple-500 hover:bg-purple-600 disabled:bg-purple-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                🔍 Tester GetEntreesStocksByIdUsecase
                <div className="text-xs mt-1 opacity-90">
                  Récupération par ID
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="GetEntreesStocksByIdUsecase"
                  result={results.GetEntreesStocksByIdUsecase}
                  isLoading={loading.GetEntreesStocksByIdUsecase}
                />
              </div>
            </div>

            <div>
              <button
                onClick={testUpdateEntreesStocksUsecase}
                disabled={loading.UpdateEntreesStocksUsecase}
                className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-orange-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                ✏️ Tester UpdateEntreesStocksUsecase
                <div className="text-xs mt-1 opacity-90">
                  Mise à jour avec validation
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="UpdateEntreesStocksUsecase"
                  result={results.UpdateEntreesStocksUsecase}
                  isLoading={loading.UpdateEntreesStocksUsecase}
                />
              </div>
            </div>

            <div>
              <button
                onClick={testDeleteEntreesStocksUsecase}
                disabled={loading.DeleteEntreesStocksUsecase}
                className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                🗑️ Tester DeleteEntreesStocksUsecase
                <div className="text-xs mt-1 opacity-90">
                  Suppression sécurisée
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="DeleteEntreesStocksUsecase"
                  result={results.DeleteEntreesStocksUsecase}
                  isLoading={loading.DeleteEntreesStocksUsecase}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Section Usecases SortiesStocks */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-red-600 mb-6 flex items-center">
            📤 Usecases SortiesStocks
            <span className="ml-2 text-sm bg-red-100 text-red-700 px-2 py-1 rounded">
              Logique FIFO
            </span>
          </h2>

          <div className="space-y-6">
            <div>
              <button
                onClick={testCreateSortiesStocksUsecase}
                disabled={loading.CreateSortiesStocksUsecase}
                className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                🔥 Tester CreateSortiesStocksUsecase
                <div className="text-xs mt-1 opacity-90">
                  Logique FIFO + Vérification stock
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="CreateSortiesStocksUsecase"
                  result={results.CreateSortiesStocksUsecase}
                  isLoading={loading.CreateSortiesStocksUsecase}
                />
              </div>
            </div>

            <div>
              <button
                onClick={testGetSortiesStocksUsecase}
                disabled={loading.GetSortiesStocksUsecase}
                className="w-full bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                📋 Tester GetSortiesStocksUsecase
                <div className="text-xs mt-1 opacity-90">
                  Récupération avec validation
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="GetSortiesStocksUsecase"
                  result={results.GetSortiesStocksUsecase}
                  isLoading={loading.GetSortiesStocksUsecase}
                />
              </div>
            </div>

            <div>
              <button
                onClick={testGetSortiesStocksByIdUsecase}
                disabled={loading.GetSortiesStocksByIdUsecase}
                className="w-full bg-purple-500 hover:bg-purple-600 disabled:bg-purple-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                🔍 Tester GetSortiesStocksByIdUsecase
                <div className="text-xs mt-1 opacity-90">
                  Récupération par ID
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="GetSortiesStocksByIdUsecase"
                  result={results.GetSortiesStocksByIdUsecase}
                  isLoading={loading.GetSortiesStocksByIdUsecase}
                />
              </div>
            </div>

            <div>
              <button
                onClick={testUpdateSortiesStocksUsecase}
                disabled={loading.UpdateSortiesStocksUsecase}
                className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-orange-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                ✏️ Tester UpdateSortiesStocksUsecase
                <div className="text-xs mt-1 opacity-90">
                  Mise à jour (sans recalcul)
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="UpdateSortiesStocksUsecase"
                  result={results.UpdateSortiesStocksUsecase}
                  isLoading={loading.UpdateSortiesStocksUsecase}
                />
              </div>
            </div>

            <div>
              <button
                onClick={testDeleteSortiesStocksUsecase}
                disabled={loading.DeleteSortiesStocksUsecase}
                className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
              >
                🗑️ Tester DeleteSortiesStocksUsecase
                <div className="text-xs mt-1 opacity-90">
                  Suppression (sans restauration)
                </div>
              </button>
              <div className="mt-3">
                <TestResult
                  testName="DeleteSortiesStocksUsecase"
                  result={results.DeleteSortiesStocksUsecase}
                  isLoading={loading.DeleteSortiesStocksUsecase}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section Usecases Stocks */}
      <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold text-indigo-600 mb-6 flex items-center">
          🏪 Usecases Stocks
          <span className="ml-2 text-sm bg-indigo-100 text-indigo-700 px-2 py-1 rounded">
            CRUD Complet
          </span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <button
              onClick={testGetStocksUsecase}
              disabled={loading.GetStocksUsecase}
              className="w-full bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
            >
              📋 Tester GetStocksUsecase
              <div className="text-xs mt-1 opacity-90">
                Récupération + enrichissement données
              </div>
            </button>
            <div className="mt-3">
              <TestResult
                testName="GetStocksUsecase"
                result={results.GetStocksUsecase}
                isLoading={loading.GetStocksUsecase}
              />
            </div>
          </div>

          <div>
            <button
              onClick={testGetStockByIdUsecase}
              disabled={loading.GetStockByIdUsecase}
              className="w-full bg-purple-500 hover:bg-purple-600 disabled:bg-purple-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
            >
              🔍 Tester GetStockByIdUsecase
              <div className="text-xs mt-1 opacity-90">
                Récupération par ID + analyse complète
              </div>
            </button>
            <div className="mt-3">
              <TestResult
                testName="GetStockByIdUsecase"
                result={results.GetStockByIdUsecase}
                isLoading={loading.GetStockByIdUsecase}
              />
            </div>
          </div>

          <div>
            <button
              onClick={testUpdateStocksUsecase}
              disabled={loading.UpdateStocksUsecase}
              className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-orange-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
            >
              ✏️ Tester UpdateStocksUsecase
              <div className="text-xs mt-1 opacity-90">
                Mise à jour + validation métier
              </div>
            </button>
            <div className="mt-3">
              <TestResult
                testName="UpdateStocksUsecase"
                result={results.UpdateStocksUsecase}
                isLoading={loading.UpdateStocksUsecase}
              />
            </div>
          </div>

          <div>
            <button
              onClick={testDeleteStocksUsecase}
              disabled={loading.DeleteStocksUsecase}
              className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white px-4 py-3 rounded-lg transition-colors font-medium"
            >
              🗑️ Tester DeleteStocksUsecase
              <div className="text-xs mt-1 opacity-90">
                Suppression + vérification dépendances
              </div>
            </button>
            <div className="mt-3">
              <TestResult
                testName="DeleteStocksUsecase"
                result={results.DeleteStocksUsecase}
                isLoading={loading.DeleteStocksUsecase}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Section d'informations détaillées */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Informations sur les données de test */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">
            📊 Données de test utilisées
          </h3>
          <div className="text-sm text-gray-600 space-y-3">
            <div>
              <strong>Entrées de stock :</strong>
              <ul className="mt-1 ml-4 list-disc space-y-1">
                <li>Produit 1 : 100 unités à 15.50€</li>
                <li>Produit 2 : 75 unités à 22.00€</li>
              </ul>
            </div>
            <div>
              <strong>Sorties de stock :</strong>
              <ul className="mt-1 ml-4 list-disc space-y-1">
                <li>Produit 1 : 25 unités (vente)</li>
                <li>Produit 2 : 10 unités (consommation)</li>
              </ul>
            </div>
            <div>
              <strong>ID Professionnel :</strong> {TEST_PROFESSIONAL_ID}
            </div>
          </div>
        </div>

        {/* Avertissements et notes */}
        <div className="bg-red-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-700 mb-3">
            ⚠️ Avertissements importants
          </h3>
          <div className="text-sm text-red-600 space-y-2">
            <p>
              • <strong>Base de données réelle :</strong> Les tests modifient
              les données Supabase
            </p>
            <p>
              • <strong>Logique FIFO :</strong> Les sorties déduisent
              automatiquement des stocks existants
            </p>
            <p>
              • <strong>Validation métier :</strong> Les usecases appliquent des
              règles de validation strictes
            </p>
            <p>
              • <strong>Dépendances :</strong> Certains tests nécessitent des
              données préexistantes
            </p>
            <p>
              • <strong>Ordre d'exécution :</strong> Tester les créations avant
              les lectures pour avoir des données
            </p>
          </div>
        </div>
      </div>

      {/* Section de statut global */}
      <div className="mt-8 bg-indigo-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-indigo-700 mb-3">
          📈 Statut des tests
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Object.values(results).filter((r) => r.success).length}
            </div>
            <div className="text-green-600">Réussis</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {Object.values(results).filter((r) => !r.success).length}
            </div>
            <div className="text-red-600">Échoués</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {Object.values(loading).filter((l) => l).length}
            </div>
            <div className="text-blue-600">En cours</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">
              {14 - Object.keys(results).length}
            </div>
            <div className="text-gray-600">Non testés</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestUsecases;
