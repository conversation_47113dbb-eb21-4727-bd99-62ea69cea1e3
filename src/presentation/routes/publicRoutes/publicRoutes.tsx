import { lazy } from "react";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import SearchProfessional from "@/presentation/pages/searchProfessional/SearchProfessional";
import AdhesionSuccess from "@/presentation/pages/authentification/adhesionProfessional/AdhesionSuccess";
import Helps from "@/presentation/pages/shared/help/Helps";

// Lazy loading des pages
const Login = lazy(
  () => import("@/presentation/pages/authentification/login/Login")
);
const ForgotPassword = lazy(
  () =>
    import(
      "@/presentation/pages/authentification/forgotPassword/ForgotPassword"
    )
);
const ResetPassword = lazy(
  () =>
    import("@/presentation/pages/authentification/resetPassword/ResetPassword")
);
const LandingPage = lazy(
  () => import("@/presentation/pages/landingPage/LandingPage")
);
const RegisterPatient = lazy(
  () =>
    import(
      "@/presentation/pages/authentification/register/registerPatient/RegisterPatient"
    )
);
const AdhesionProfessional = lazy(
  () =>
    import(
      "@/presentation/pages/authentification/adhesionProfessional/AdhesionProfessional"
    )
);
const EmailConfirmation = lazy(
  () => import("@/presentation/components/EmailConfirmation")
);
const Consultation = lazy(
  () => import("@/presentation/pages/patient/consultation/Consultation")
);
const Profile = lazy(
  () => import("@/presentation/pages/professional/profile/Profile")
);

const UserProfile = lazy(
  () => import("@/presentation/pages/shared/profile/Profile")
);

const RegisterPatientSuccess = lazy(
  () =>
    import(
      "@/presentation/pages/authentification/register/registerPatient/RegisterPatientSuccess"
    )
);

const RegisterProfessional = lazy(
  () => import("@/presentation/pages/professional/signup/SignUpProfessional")
);

const DASHInvitation = lazy(
  () => import("@/presentation/pages/dash/register/RegisterDash")
);

export const publicRoutes = [
  { path: PublicRoutesNavigation.MAIN_PAGE, element: <LandingPage /> },
  {
    path: `/${PublicRoutesNavigation.LOGIN_PAGE}`,
    element: <Login />,
  },
  {
    path: `/${PublicRoutesNavigation.FORGOT_PASSWORD_PAGE}`,
    element: <ForgotPassword />,
  },
  {
    path: `/${PublicRoutesNavigation.RESET_PASSWORD_PAGE}`,
    element: <ResetPassword />,
  },
  {
    path: `/${PublicRoutesNavigation.FIND_PROFESSIONAL}`,
    element: <SearchProfessional />,
  },
  {
    path: `/${PublicRoutesNavigation.FIND_PROFESSIONAL_WITH_SPECIALITY}`,
    element: <SearchProfessional />,
  },
  {
    path: `/${PublicRoutesNavigation.REGISTER_PATIENT_PAGE}`,
    element: <RegisterPatient />,
  },
  {
    path: `/${PublicRoutesNavigation.PROFESSIONAL_ADHESION_REQUEST}`,
    element: <AdhesionProfessional />,
  },
  {
    path: `/${PublicRoutesNavigation.EMAIL_CONFIRMATION_PAGE}`,
    element: <EmailConfirmation />,
  },
  {
    path: `/${PublicRoutesNavigation.CONSULTATION_PAGE}`,
    element: <Consultation />,
  },
  {
    path: `/${PublicRoutesNavigation.PROFILE_PAGE}`,
    element: <Profile />,
  },
  {
    path: `/${PublicRoutesNavigation.USER_PROFILE_PAGE}`,
    element: <UserProfile />,
  },
  {
    path: `/${PublicRoutesNavigation.ADHESION_SUCCESS}`,
    element: <AdhesionSuccess />,
  },
  {
    path: `/${PublicRoutesNavigation.REGISTER_PATIENT_SUCCESS}`,
    element: <RegisterPatientSuccess />,
  },
  {
    path: `/${PublicRoutesNavigation.HELP}`,
    element: <Helps />,
  },
  {
    path: `/${PublicRoutesNavigation.REGISTER_PROFESSIONAL}`,
    element: <RegisterProfessional />,
  },
  {
    path: `/${PublicRoutesNavigation.DASH_INVITATION}`,
    element: <DASHInvitation />,
  },
];
