import { Suspense, useEffect, useState } from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import ScrollToTop from "@/presentation/components/common/Navigation/ScrollToTop";
import { useRestoreAuth } from "@/presentation/hooks/use-restore-auth";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import PageNotFound from "../components/common/NotFoundPage";
import { publicRoutes } from "./publicRoutes/publicRoutes";
import patientRoutes from "./protectedRoute/patientsRoutes";
import BaseLayout from "../components/layouts/BaseLayout";
import { PATIENT_NAVIGATION } from "../components/layouts/navigations/patientNavigation";
import { ADMIN_NAVIGATION } from "../components/layouts/navigations/adminNavigation";
import { PROFESSIONAL_NAVIGATION } from "../components/layouts/navigations/profesisonalNavigation";
import professionalRoutes from "./protectedRoute/professionalRoutes";
import adminRoutes from "./protectedRoute/adminRoutes";
import dassRoutes from "./protectedRoute/dassRoutes";
import { DASH_NAVIGATION } from "../components/layouts/navigations/dashNavigation.tsx";
import Test from "../Test.tsx";
import { useToast } from "../hooks/use-toast.ts";

const AppRoutes = () => {
  const { isRestoreDone } = useRestoreAuth();
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isDisconnectedOnce, setIsDisconnectedOnce] = useState<boolean>(false);
  const toast = useToast();

  useEffect(() => {
    const updateConnectionStatus = () => {
      setIsConnected(navigator.onLine);
    };

    window.addEventListener("online", updateConnectionStatus);
    window.addEventListener("offline", updateConnectionStatus);

    // Initial check
    updateConnectionStatus();

    return () => {
      window.removeEventListener("online", updateConnectionStatus);
      window.removeEventListener("offline", updateConnectionStatus);
    };
  }, []);

  useEffect(() => {
    if (isConnected === false) {
      setIsDisconnectedOnce(true);
      toast.warning("Vous êtes actuellement hors ligne.");
    } else if (isConnected === true && isDisconnectedOnce) {
      toast.success("Connexion rétablie !");
    }
  }, [isConnected]);

  if (!isRestoreDone) {
    return <LoadingSpinner />;
  }

  return (
    <BrowserRouter>
      <ScrollToTop />
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          {/* Routes publiques */}
          {publicRoutes.map((route, index) => {
            return (
              <Route key={index} path={route.path} element={route.element} />
            );
          })}

          {/* Routes protégées */}
          <Route element={<BaseLayout navigation={PATIENT_NAVIGATION} />}>
            {patientRoutes.map((route, index) => {
              return (
                <Route key={index} path={route.path} element={route.element} />
              );
            })}
          </Route>

          <Route element={<BaseLayout navigation={PROFESSIONAL_NAVIGATION} />}>
            {professionalRoutes.map((route, index) => {
              return (
                <Route key={index} path={route.path} element={route.element} />
              );
            })}
          </Route>

          <Route element={<BaseLayout navigation={ADMIN_NAVIGATION} />}>
            {adminRoutes.map((route, index) => {
              return (
                <Route key={index} path={route.path} element={route.element} />
              );
            })}
          </Route>

          <Route element={<BaseLayout navigation={DASH_NAVIGATION} />}>
            {dassRoutes.map((route, index) => {
              return (
                <Route key={index} path={route.path} element={route.element} />
              );
            })}
          </Route>
          <Route path="/test" element={<Test />} />

          <Route path="*" element={<PageNotFound />} />
        </Routes>
      </Suspense>
    </BrowserRouter>
  );
};

export default AppRoutes;
