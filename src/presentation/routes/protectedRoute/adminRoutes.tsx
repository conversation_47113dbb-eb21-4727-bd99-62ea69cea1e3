import { AdminRoutesNavigations } from "@/shared/constants/AppRoutesNavigation";
import { lazy } from "react";

const Dashboard = lazy(
  () => import("@/presentation/pages/admin/dashboard/Dashboard")
);

const ProfessionnalListsPage = lazy(
  () => import("@/presentation/pages/admin/professionals/Professionals")
);

const AdhesionRequestsPage = lazy(
  () => import("@/presentation/pages/admin/adhesionRequests/AdhesionRequests")
);

const PatientsPage = lazy(
  () => import("@/presentation/pages/shared/patients/MyPatients")
);

const HelpPage = lazy(() => import("@/presentation/pages/shared/help/Helps"));
const Dash = lazy(() => import("@/presentation/pages/admin/dash/Dash"));

const adminRoutes = [
  { path: `/${AdminRoutesNavigations.DASHBOARD}`, element: <Dashboard /> },
  {
    path: `/${AdminRoutesNavigations.MANAGE_PROFESSIONALS}`,
    element: <ProfessionnalListsPage />,
  },
  {
    path: `/${AdminRoutesNavigations.MANAGE_ADHESION_REQUESTS}`,
    element: <AdhesionRequestsPage />,
  },
  {
    path: `/${AdminRoutesNavigations.MANAGE_PATIENTS}`,
    element: <PatientsPage />,
  },
  { path: `/${AdminRoutesNavigations.HELP}`, element: <HelpPage /> },
  { path: `/${AdminRoutesNavigations.DASH}`, element: <Dash /> },
];

export default adminRoutes;
