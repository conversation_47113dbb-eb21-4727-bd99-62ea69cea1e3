import { ProfessionalRoutesNavigations } from "@/shared/constants/AppRoutesNavigation";
import { lazy } from "react";

const Dashboard = lazy(
  () => import("@/presentation/pages/professional/dashboard/Dashboard")
);

const AgendaPage = lazy(
  () => import("@/presentation/pages/professional/agenda/Agenda")
);

const AppointmentsPage = lazy(
  () => import("@/presentation/pages/professional/appointments/Appointments")
);

const ProchePage = lazy(
  () => import("@/presentation/pages/shared/patients/ProchePatient")
);

const ProcheInfo = lazy(
  () => import("@/presentation/pages/shared/patients/PatientInfo")
);

const PatientPage = lazy(
  () => import("@/presentation/pages/shared/patients/MyPatients")
);

const PatientInfo = lazy(
  () => import("@/presentation/pages/shared/patients/PatientInfo")
);

const Messages = lazy(
  () => import("@/presentation/pages/shared/messages/Messages")
);

const FacturationPage = lazy(
  () => import("@/presentation/pages/professional/facturation/Facturation")
);

const HelpPage = lazy(
  () => import("@/presentation/pages/professional/help/Helps")
);

const ProfessionalStock = lazy(
  () => import("@/presentation/pages/professional/stock/ProfessionalStock")
);

const ProfilePage = lazy(
  () => import("@/presentation/pages/shared/profile/Profile")
);

const professionalRoutes = [
  {
    path: `/${ProfessionalRoutesNavigations.DASHBOARD}`,
    element: <Dashboard />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.AGENDA}`,
    element: <AgendaPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.APPOINTMENTS}`,
    element: <AppointmentsPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS}`,
    element: <PatientPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_ACTIF}`,
    element: <PatientPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PROCHE_PATIENTS}`,
    element: <ProchePage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PROCHE_PATIENTS_PAGE}`,
    element: <ProcheInfo />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_DECEDE}`,
    element: <PatientPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_SUPPRIMER}`,
    element: <PatientPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_PAGE}`,
    element: <PatientInfo />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MESSAGES}`,
    element: <Messages />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.FACTURATION}`,
    element: <FacturationPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.HELP}`,
    element: <HelpPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.STOCK}`,
    element: <ProfessionalStock />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.PROFILE}`,
    element: <ProfilePage />,
  },
];

export default professionalRoutes;
