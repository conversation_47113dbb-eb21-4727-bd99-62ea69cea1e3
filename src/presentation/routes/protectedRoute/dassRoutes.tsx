import Stock from "@/presentation/components/features/professional/stock/Stock";
import { DashRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { lazy } from "react";

const Dashboard = lazy(
  () => import("@/presentation/pages/dash/dashboard/Dashboard")
);

const AgendaPage = lazy(
  () => import("@/presentation/pages/professional/agenda/Agenda")
);

const ProchePage = lazy(
  () => import("@/presentation/pages/dash/employer/ProcheEmployer")
);

const ProcheInfo = lazy(
  () => import("@/presentation/pages/dash/employer/EmployedInfo")
);

const HelpPage = lazy(() => import("@/presentation/pages/shared/help/Helps"));

const Employer = lazy(
  () => import("@/presentation/pages/dash/employer/MyEmployees")
);

const EmployedInfo = lazy(
  () => import("@/presentation/pages/dash/employer/EmployedInfo")
);

const dassRoutes = [
  { path: `/${DashRoutesNavigation.DASHBOARD}`, element: <Dashboard /> },
  {
    path: `/${DashRoutesNavigation.EMPLOYER}`,
    element: <Employer />,
  },
  {
    path: `/${DashRoutesNavigation.AGENDA}`,
    element: <AgendaPage />,
  },
  {
    path: `/${DashRoutesNavigation.PROCHE_EMPLOYER}`,
    element: <ProchePage />,
  },
  {
    path: `/${DashRoutesNavigation.PROCHE_EMPLOYER_PAGE}`,
    element: <ProcheInfo />,
  },
  {
    path: `/${DashRoutesNavigation.EMPLOYER_ACTIF}`,
    element: <Employer />,
  },
  {
    path: `/${DashRoutesNavigation.EMPLOYER_DECEDE}`,
    element: <Employer />,
  },
  {
    path: `/${DashRoutesNavigation.EMPLOYER_SUPPRIMER}`,
    element: <Employer />,
  },
  {
    path: `/${DashRoutesNavigation.STOCK}`,
    element: <Stock />,
  },
  {
    path: `/${DashRoutesNavigation.MANAGE_EMPLOYED_PAGE}`,
    element: <EmployedInfo />,
  },
  { path: `/${DashRoutesNavigation.HELP}`, element: <HelpPage /> },
];

export default dassRoutes;
