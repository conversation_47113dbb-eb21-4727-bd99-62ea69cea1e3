import { patients_groupe_sanguin_enum, sexe_enum } from "@/domain/models/enums";

export type RegisterPatientState = {
  email: string;
  confirmEmail: string;
  password: string;
  nom: string;
  prenom: string;
  sexe: sexe_enum;
  date_naissance: Date | null;
  adresse: string;
  profession: string;
  nb_enfant: number;
  region: string;
  district: string;
  commune: string;
  fokontany: string;
  donneur_sang: boolean;
  groupe_sanguin: patients_groupe_sanguin_enum;
  nationalite: string;
  pays: string;
  situation_matrimonial: string;
  autre_profession: string;
  telephone: string;
  contacts: {
    numero: string;
  }[];
  urgences: {
    contact_urgence_nom: string;
    contact_urgence_prenom?: string;
    contact_urgence_telephone: string;
    relation: string;
    adresse: string;
  }[];
  acceptConditions: boolean;
  errors: { [key: string]: string };
};

export type RegisterPatientStateAction =
  | {
      type: "UPDATE_FIELD";
      payload: {
        field: string;
        value:
          | string
          | number
          | boolean
          | sexe_enum
          | Date
          | {
              [key: string]: string;
            }
          | { numero: string }[]
          | {
              contact_urgence_nom: string;
              contact_urgence_prenom?: string;
              contact_urgence_telephone: string;
              relation?: string;
              adresse?: string;
            }[];
      };
    }
  | { type: "RESET_STATE" }
  | { type: "SET_ERRORS"; payload: { errors: { [key: string]: string } } };

export type RegisterPatientStateContextType = {
  state: RegisterPatientState;
  dispatch: React.Dispatch<RegisterPatientStateAction>;
};
