import { active_tab_enum } from "@/domain/models/enums";

export type CarnetDeSanteState = {
  activeTab: active_tab_enum;
  isProfile: boolean;
  isAddForm: boolean;
  confidentialite: boolean;
  selectedFile: File | null;
};

export type CarnetDeSanteAction =
  | {
      type: "UPDATE_FIELD";
      payload: { field: string; value: active_tab_enum | boolean | File };
    }
  | { type: "RESET_STATE" }
  | { type: "RESET_ACTIVE_TAB" };

export type CarnetDeSanteStateContextType = {
  state: CarnetDeSanteState;
  dispatch: React.Dispatch<CarnetDeSanteAction>;
};
