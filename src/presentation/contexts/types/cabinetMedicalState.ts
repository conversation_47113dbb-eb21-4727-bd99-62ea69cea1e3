export type SectionProgressState = {
  panel1: boolean; // Informations professionnelles
  panel2: boolean; // Localisation
  panel3: boolean; // Contact
  panel4: boolean; // Présentation
  panel5: boolean; // Authentification
  panel6: boolean; // Spécialités
  panel7: boolean; // Services
};

export type cabinetMedicalState = {
  // État de l'UI uniquement
  isLoading: boolean;
  error: string;
  isSubmissionSuccess: boolean;
  overallProgress: number;
  hasSavedData: boolean;
  panelExpanded: string;
  sectionProgress: SectionProgressState;
};

export type CabinetMedicalStateAction = {
  type: "UPDATE_FIELD";
  payload: {
    field: keyof cabinetMedicalState;
    value:
      | boolean
      | string
      | number
      | SectionProgressState
      | Partial<SectionProgressState>;
  };
};

export type CabinetMedicalStateContextType = {
  state: cabinetMedicalState;
  dispatch: React.Dispatch<CabinetMedicalStateAction>;
};
