import { Event } from "@/shared/types/SettingsType";
import { useState } from "react";
import { View } from 'react-big-calendar';

export type AgendaState = {
    currentView: View;
    events: Event[];
    isSaveSettings: boolean;
    isSettingsModalOpen: boolean;
    isAddEventModalOpen: boolean;
    isAppointmentModalOpen: boolean;
    isDeleteEventModalOpen: boolean;
    isDeleteSettingsModalOpen: boolean;
    isDisponibilites: boolean;
    isEvenement: boolean;
    isAppointments: boolean;
    timeEvent: Event | null;
    idCurrentEvent: number;
    anchorEl: HTMLElement | null;
    selectedEvent: Event | null;
}

export type AgendaAction =
    | { type: 'UPDATE_FIELD'; payload: { field: string; value: View | Event[] | Event | boolean | number | HTMLElement | null} }

export type AgendaStateContextType = {
    state: AgendaState;
    dispatch: React.Dispatch<AgendaAction>;
};
