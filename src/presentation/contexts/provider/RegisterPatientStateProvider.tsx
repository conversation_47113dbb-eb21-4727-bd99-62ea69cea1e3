import { createContext, useReducer, ReactNode } from "react";
import { RegisterPatientStateContextType } from "../types";
import { registerPatientStateReducer } from "../reducers";
import { initialRegisterPatientState } from "../constants";

export const RegisterPatientStateContext = createContext<RegisterPatientStateContextType | null>(
  null
);

export const RegisterPatientStateProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(registerPatientStateReducer, initialRegisterPatientState);

  return (
    <RegisterPatientStateContext.Provider value={{ state, dispatch }}>
      {children}
    </RegisterPatientStateContext.Provider>
  );
};
