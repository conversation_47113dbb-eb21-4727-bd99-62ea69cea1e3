import { createContext, useReducer, ReactNode } from "react";
import { CarnetDeSanteStateContextType } from "../types";
import { carnetDeSanteStateReducer } from "../reducers";
import { initialCarnetDeSanteState } from "../constants";

export const CarnetDeSanteStateContext =
  createContext<CarnetDeSanteStateContextType | null>(null);

export const CarnetDeSanteStateProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [state, dispatch] = useReducer(
    carnetDeSanteStateReducer,
    initialCarnetDeSanteState
  );

  return (
    <CarnetDeSanteStateContext.Provider value={{ state, dispatch }}>
      {children}
    </CarnetDeSanteStateContext.Provider>
  );
};
