import { createContext, useReducer, ReactNode } from "react";
import { AgendaStateContextType } from "../types";
import { agendaStateReducer } from "../reducers";
import { initialAgendaState } from "../constants";

export const AgendaStateContext = createContext<AgendaStateContextType | null>(
  null
);

export const AgendaStateProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(agendaStateReducer, initialAgendaState);

  return (
    <AgendaStateContext.Provider value={{ state, dispatch }}>
      {children}
    </AgendaStateContext.Provider>
  );
};
