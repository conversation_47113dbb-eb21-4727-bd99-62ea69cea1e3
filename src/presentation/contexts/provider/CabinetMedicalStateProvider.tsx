import { createContext, useReducer, ReactNode } from "react";
import { cabinetMedicalStateReducer } from "../reducers";
import { initialCabinetMedicalState } from "../constants";
import { CabinetMedicalStateContextType } from "../types";

export const CabinetMedicalStateContext =
  createContext<CabinetMedicalStateContextType | null>(null);

export const CabinetMedicalStateProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [state, dispatch] = useReducer(
    cabinetMedicalStateReducer,
    initialCabinetMedicalState,
  );

  return (
    <CabinetMedicalStateContext.Provider value={{ state, dispatch }}>
      {children}
    </CabinetMedicalStateContext.Provider>
  );
};
