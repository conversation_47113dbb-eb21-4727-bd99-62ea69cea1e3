import React, { ReactNode } from "react";
import { AgendaStateProvider } from "./AgendaStateProvider";
import { RegisterPatientStateProvider } from "./RegisterPatientStateProvider";
import { CarnetDeSanteStateProvider } from "./CarnetDeSanteStateProvider";
import { CabinetMedicalStateProvider } from "./CabinetMedicalStateProvider";

// Import d'autres providers ici

interface AppProviderProps {
  children: ReactNode;
}

const combineProviders = (
  providers: Array<React.JSXElementConstructor<{ children: ReactNode }>>
) => {
  return ({ children }: AppProviderProps) => {
    return providers.reduceRight((acc, Provider) => {
      return <Provider>{acc}</Provider>;
    }, children);
  };
};

// Ajouter les nouveaux providers dans ce tableau
const providers = [
  AgendaStateProvider,
  RegisterPatientStateProvider,
  CarnetDeSanteStateProvider,
  CabinetMedicalStateProvider,
];

export const AppProvider = combineProviders(providers);
