import { cabinetMedicalState, CabinetMedicalStateAction } from "../types";

export const cabinetMedicalStateReducer = (
  state: cabinetMedicalState,
  action: CabinetMedicalStateAction
): cabinetMedicalState => {
  switch (action.type) {
    case "UPDATE_FIELD":
      // Vérifier que le champ existe dans le type cabinetMedicalState
      if (action.payload.field in state) {
        // Cas spécial pour sectionProgress qui est un objet
        if (
          action.payload.field === "sectionProgress" &&
          typeof action.payload.value === "object"
        ) {
          return {
            ...state,
            sectionProgress: {
              ...state.sectionProgress,
              ...action.payload.value,
            },
          };
        }

        // Cas général pour les autres champs
        return {
          ...state,
          [action.payload.field]: action.payload.value,
        };
      }
      console.warn(
        `Le champ ${action.payload.field} n'existe pas dans cabinetMedicalState`
      );
      return state;
    default:
      return state;
  }
};
