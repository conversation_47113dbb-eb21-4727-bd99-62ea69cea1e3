import { CarnetDeSanteAction, CarnetDeSanteState } from "../types";
import { initialCarnetDeSanteState } from "../constants";
import { active_tab_enum } from "@/domain/models/enums";

export const carnetDeSanteStateReducer = (
  state: CarnetDeSanteState,
  action: CarnetDeSanteAction
): CarnetDeSanteState => {
  switch (action.type) {
    case "UPDATE_FIELD":
      return {
        ...state,
        [action.payload.field]: action.payload.value,
      };
    case "RESET_STATE":
      return {
        ...state,
        selectedFile: null,
      };
    case "RESET_ACTIVE_TAB":
      return {
        ...state,
        activeTab: active_tab_enum.carnetDeSante,
      };
    default:
      return state;
  }
};
