import { RegisterPatientStateAction, RegisterPatientState } from '../types';
import { initialRegisterPatientState } from '../constants';

export const registerPatientStateReducer = (
  state: RegisterPatientState,
  action: RegisterPatientStateAction
): RegisterPatientState => {
  switch (action.type) {
    case 'UPDATE_FIELD':
      return {
        ...state,
        [action.payload.field]: action.payload.value
      };
    case 'RESET_STATE':
      return {
        ...initialRegisterPatientState
      };
    case 'SET_ERRORS':
      return {
        ...state,
        errors: action.payload.errors
      };
    default:
      return state;
  }
};
