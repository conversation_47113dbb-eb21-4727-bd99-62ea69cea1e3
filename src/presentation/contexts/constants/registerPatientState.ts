import { sexe_enum } from "@/domain/models/enums";
import { RegisterPatientState } from "../types";

export const initialRegisterPatientState: RegisterPatientState = {
    email: "",
    password: "",
    confirmEmail: "",
    nom: "",
    prenom: "",
    sexe: "" as sexe_enum,
    date_naissance: null,
    adresse: "",
    region: "",
    district: "",
    commune: "",
    fokontany: "",
    donneur_sang: false,
    groupe_sanguin: null,
    nationalite: "Malagasy",
    telephone: "",
    pays: "",
    profession: "",
    situation_matrimonial: "celibataire",
    autre_profession: "",
    nb_enfant: 0,
    contacts: [{numero: ""}],
    urgences: [{contact_urgence_nom: "", contact_urgence_telephone: ""}],
    acceptConditions: false,
    errors: {},
};