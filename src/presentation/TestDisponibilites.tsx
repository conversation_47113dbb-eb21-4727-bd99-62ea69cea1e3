import SearchProfessionalsUsecase from "@/domain/usecases/professional/GetProfessionnalInformations/SearchProfessionalsUsecase.ts";
import SearchProfessionalsRepository from "@/infrastructure/repositories/SearchProfessionalsRepository.ts";
import { useEffect, useState } from "react";

const TestDisponibilites = () => {
  const [data, setData] = useState<string>("");

  useEffect(() => {
    const test = async () => {
      const repo = new SearchProfessionalsRepository();
      const usecase = new SearchProfessionalsUsecase(repo);

      console.log("=== TEST DISPONIBILITÉS ===");
      
      const result = await usecase.execute({
        name: "razafi",
        localization: "antana",
      });

      console.log("Résultat complet:", result);
      
      if (result.length > 0) {
        const prof = result[0];
        console.log("=== ANALYSE DISPONIBILITÉS ===");
        console.log("1. Paramètres disponibilité:", prof.parametre_disponibilite);
        console.log("2. Horaires hebdomadaires:", prof.horaire_hebdomadaire);
        console.log("3. Disponibilités générées:", prof.disponibilite);
        
        // Test manuel de génération de créneaux
        if (prof.horaire_hebdomadaire && prof.horaire_hebdomadaire.length > 0) {
          console.log("=== TEST MANUEL GÉNÉRATION CRÉNEAUX ===");
          
          const today = new Date();
          const creneauxManuels: any[] = [];
          
          prof.horaire_hebdomadaire.forEach((horaire: any) => {
            console.log("Traitement horaire:", horaire);
            
            if (horaire.creneau_horaire && horaire.creneau_horaire.length > 0) {
              console.log("Créneaux trouvés pour", horaire.jour, ":", horaire.creneau_horaire);
              
              // Génération pour les 7 prochains jours
              for (let i = 0; i < 7; i++) {
                const date = new Date(today);
                date.setDate(date.getDate() + i);
                
                const joursSemaine = ["Dim.", "Lun.", "Mar.", "Mer.", "Jeu.", "Ven.", "Sam."];
                const jourDate = joursSemaine[date.getDay()];
                
                console.log("Date:", date.toISOString().split('T')[0], "Jour:", jourDate, "Horaire jour:", horaire.jour);
                
                if (jourDate === horaire.jour) {
                  horaire.creneau_horaire.forEach((creneau: any) => {
                    const creneauManuel = {
                      id_professionnel: prof.id,
                      date: date.toISOString().split('T')[0],
                      start: creneau.heure_debut,
                      end: creneau.heure_fin,
                    };
                    creneauxManuels.push(creneauManuel);
                    console.log("✅ Créneau manuel généré:", creneauManuel);
                  });
                }
              }
            }
          });
          
          console.log("📊 Total créneaux manuels générés:", creneauxManuels.length);
          console.log("Créneaux manuels:", creneauxManuels);
        }
      }

      setData(JSON.stringify(result, null, 2));
    };

    test();
  }, []);

  return (
    <div style={{ padding: "20px", fontFamily: "monospace" }}>
      <h2>Test Disponibilités - Diagnostic</h2>
      <p>Ouvrir la console pour voir les logs détaillés</p>
      <pre style={{ background: "#f5f5f5", padding: "10px", overflow: "auto", maxHeight: "400px" }}>
        {data}
      </pre>
    </div>
  );
};

export default TestDisponibilites;
