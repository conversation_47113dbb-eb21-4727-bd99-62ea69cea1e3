import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HelmetProvider } from "react-helmet-async";
import AppRoutes from "./routes/AppRoutes";
import { Toast } from "./components/common/toast/Toast";
import { AppProvider } from "./contexts/provider/AppProvider";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => (
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <Toast>
        <AppProvider>
          <AppRoutes />
        </AppProvider>
      </Toast>
    </QueryClientProvider>
  </HelmetProvider>
);

export default App;
