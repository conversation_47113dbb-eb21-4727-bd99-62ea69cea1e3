/**
 * Utilitaire pour calculer le score de santé général d'un patient
 * Basé sur les données disponibles dans le système
 */

import { PatientDTO } from "@/domain/DTOS";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { Allergie, signe_vitaux } from "@/domain/models";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

export interface HealthScoreResult {
  score: number;
  status: "Excellente" | "Bonne" | "Moyenne" | "À surveiller" | "Préoccupante";
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
    label: string;
  };
}

export interface HealthData {
  // Signes vitaux récents
  latestVitals?: signe_vitaux;

  // Historique des rendez-vous
  appointmentHistory?: AppointmentPatientDTO[];

  // Prescriptions
  prescriptions?: Array<{
    isActive: boolean;
    endDate: string;
  }>;

  // Conditions médicales
  allergies?: Allergie[];
  chronicConditions?: any[];

  // Informations patient
  age?: number;
}

/**
 * Calcule le score de santé général basé sur plusieurs facteurs
 */
export const calculateHealthScore = (data: HealthData): HealthScoreResult => {
  let totalScore = 0;
  let factorCount = 0;

  // 1. Score des signes vitaux (40% du score total)
  if (data.latestVitals) {
    const vitalScore = calculateVitalScore(data.latestVitals);
    totalScore += vitalScore * 0.4;
    factorCount++;
  }

  // 2. Score du suivi médical (30% du score total)
  if (data.appointmentHistory) {
    const followUpScore = calculateFollowUpScore(data.appointmentHistory);
    totalScore += followUpScore * 0.3;
    factorCount++;
  }

  // 3. Score de gestion des médicaments (20% du score total)
  if (data.prescriptions) {
    const medicationScore = calculateMedicationScore(data.prescriptions);
    totalScore += medicationScore * 0.2;
    factorCount++;
  }

  // 4. Score des facteurs de risque (10% du score total)
  const riskScore = calculateRiskScore(
    data.allergies,
    data.chronicConditions,
    data.age,
  );
  totalScore += riskScore * 0.1;
  factorCount++;

  // Score final (si pas de données, score neutre de 70)
  const finalScore = factorCount > 0 ? Math.round(totalScore) : 70;

  return {
    score: finalScore,
    status: getHealthStatus(finalScore),
    color: getHealthColor(finalScore),
    trend: calculateTrend(data),
  };
};

/**
 * Calcule le score basé sur les signes vitaux
 */
const calculateVitalScore = (vitals: HealthData["latestVitals"]): number => {
  let score = 100;

  // IMC (Indice de Masse Corporelle)
  if (vitals.indice_masse_corporel) {
    if (
      vitals.indice_masse_corporel < 18.5 ||
      vitals.indice_masse_corporel > 30
    )
      score -= 20;
    else if (
      vitals.indice_masse_corporel < 20 ||
      vitals.indice_masse_corporel > 25
    )
      score -= 10;
  } else if (vitals.poid && vitals.taille) {
    const imc = vitals.poid / Math.pow(vitals.taille / 100, 2);
    if (imc < 18.5 || imc > 30) score -= 20;
    else if (imc < 20 || imc > 25) score -= 10;
  }

  // Tension artérielle
  if (vitals.tension_arterielle) {
    const [systolique, diastolique] = vitals.tension_arterielle
      .split("/")
      .map(Number);
    if (systolique > 140 || diastolique > 90) score -= 25;
    else if (systolique > 130 || diastolique > 85) score -= 15;
    else if (systolique < 90 || diastolique < 60) score -= 15;
  }

  // Fréquence cardiaque
  if (vitals.frequence_cardiaque) {
    if (vitals.frequence_cardiaque < 60 || vitals.frequence_cardiaque > 100)
      score -= 15;
  }

  // Température
  if (vitals.temperature) {
    if (vitals.temperature < 36 || vitals.temperature > 37.5) score -= 20;
  }

  // Glucose
  if (vitals.niveau_glucose) {
    if (vitals.niveau_glucose > 126)
      score -= 25; // Diabète
    else if (vitals.niveau_glucose > 100) score -= 15; // Pré-diabète
  }

  return Math.max(0, Math.min(100, score));
};

/**
 * Calcule le score basé sur le suivi médical
 */
const calculateFollowUpScore = (
  appointments: HealthData["appointmentHistory"],
): number => {
  const now = new Date();
  const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);

  const recentAppointments = appointments.filter(
    (apt) =>
      new Date(apt.date_rendez_vous) >= sixMonthsAgo &&
      apt.statut === rendez_vous_statut_enum.TERMINER,
  );

  const cancelledAppointments = appointments.filter(
    (apt) =>
      new Date(apt.date_rendez_vous) >= sixMonthsAgo &&
      apt.statut === rendez_vous_statut_enum.MANQUER,
  );

  let score = 50; // Score de base

  // Bonus pour suivi régulier
  if (recentAppointments.length >= 3) score += 50;
  else if (recentAppointments.length >= 2) score += 30;
  else if (recentAppointments.length >= 1) score += 15;

  // Pénalité pour rendez-vous annulés
  score -= cancelledAppointments.length * 10;

  return Math.max(0, Math.min(100, score));
};

/**
 * Calcule le score basé sur la gestion des médicaments
 */
const calculateMedicationScore = (
  prescriptions: HealthData["prescriptions"],
): number => {
  let score = 100;

  const activePrescriptions = prescriptions.filter((p) => p.isActive);
  const expiredPrescriptions = prescriptions.filter(
    (p) => !p.isActive && new Date(p.endDate) < new Date(),
  );

  // Pénalité pour trop de médicaments actifs (polymédication)
  if (activePrescriptions.length > 5) score -= 25;
  else if (activePrescriptions.length > 3) score -= 15;

  // Pénalité pour médicaments expirés non renouvelés récemment
  const recentlyExpired = expiredPrescriptions.filter((p) => {
    const expiredDate = new Date(p.endDate);
    const oneMonthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    return expiredDate >= oneMonthAgo;
  });

  score -= recentlyExpired.length * 10;

  return Math.max(0, score);
};

/**
 * Calcule le score basé sur les facteurs de risque
 */
const calculateRiskScore = (
  allergies?: any[],
  chronicConditions?: any[],
  age?: number,
): number => {
  let score = 100;

  // Pénalité pour allergies multiples
  if (allergies) {
    if (allergies.length > 3) score -= 20;
    else if (allergies.length > 1) score -= 10;
  }

  // Pénalité pour conditions chroniques
  if (chronicConditions) {
    if (chronicConditions.length > 2) score -= 30;
    else if (chronicConditions.length > 0) score -= 15;
  }

  // Ajustement selon l'âge
  if (age) {
    if (age > 70) score -= 10;
    else if (age > 60) score -= 5;
    else if (age < 18) score += 5;
  }

  return Math.max(0, score);
};

/**
 * Détermine le statut de santé basé sur le score
 */
const getHealthStatus = (score: number): HealthScoreResult["status"] => {
  if (score >= 85) return "Excellente";
  if (score >= 70) return "Bonne";
  if (score >= 55) return "Moyenne";
  if (score >= 40) return "À surveiller";
  return "Préoccupante";
};

/**
 * Détermine la couleur associée au score de santé
 */
const getHealthColor = (score: number): string => {
  if (score >= 85) return "#10B981"; // Vert
  if (score >= 70) return "#059669"; // Vert foncé
  if (score >= 55) return "#F59E0B"; // Orange
  if (score >= 40) return "#EF4444"; // Rouge
  return "#DC2626"; // Rouge foncé
};

/**
 * Calcule la tendance (simplifié pour l'exemple)
 */
const calculateTrend = (data: HealthData): HealthScoreResult["trend"] => {
  // Pour l'instant, retourne une tendance neutre
  // Dans une vraie implémentation, on comparerait avec les données précédentes
  return {
    value: Math.floor(Math.random() * 10), // Exemple aléatoire
    isPositive: Math.random() > 0.5,
    label: "évolution",
  };
};

/**
 * Fonction utilitaire pour obtenir un score de santé avec des données mockées
 */
export const getMockHealthScore = (
  signeVitaux: signe_vitaux,
  appointment: AppointmentPatientDTO[],
): HealthScoreResult => {
  const mockData: HealthData = {
    latestVitals: signeVitaux,
    appointmentHistory: appointment,
    prescriptions: [{ isActive: true, endDate: "2025-03-15" }],
    allergies: [],
    chronicConditions: [],
    age: 35,
  };

  return calculateHealthScore(mockData);
};

/**
 * Fonction utilitaire pour calculer le score de santé à partir d'un PatientDTO
 * avec carnet de santé intégré
 *
 * @param patient - PatientDTO avec carnet de santé
 * @param appointments - Historique des rendez-vous
 * @returns Score de santé calculé
 */
export const calculateHealthScoreFromPatient = (
  patient: PatientDTO,
  appointments?: AppointmentPatientDTO[],
): HealthScoreResult => {
  const signeVitaux = patient?.carnet_sante?.signe_vitaux;

  // Récupération sécurisée du dernier signe vital
  const lastSigneVitaux = signeVitaux && signeVitaux.length > 0
    ? signeVitaux[signeVitaux.length - 1]
    : undefined;

  // Calculer l'âge du patient
  const age = patient.date_naissance
    ? new Date().getFullYear() - new Date(patient.date_naissance).getFullYear()
    : undefined;

  // Extraire les données du carnet de santé si disponible
  const carnetSante = patient.carnet_sante;

  const healthData: HealthData = {
    latestVitals: lastSigneVitaux,
    appointmentHistory: appointments || [],
    allergies: carnetSante?.allergie || [],
    chronicConditions: [
      ...(carnetSante?.affectation_medical || []),
      ...(carnetSante?.antecedant_familliaux || []),
    ],
    prescriptions:
      carnetSante?.medicament?.map((med) => ({
        isActive: true, // Simplification - dans la vraie vie, vérifier les dates
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // +30 jours
      })) || [],
    age,
  };

  return calculateHealthScore(healthData);
};
