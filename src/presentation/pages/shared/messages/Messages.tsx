import { FC, memo, useState } from "react";
import { MessageCircle, Plus } from "lucide-react";
import {
  CARD_STYLES,
  TEXT_STYLES,
  BUTTON_STYLES,
} from "@/presentation/styles/common";
import { useDarkMode } from "@/presentation/hooks/use-dark-mode";
import { useMessagingData } from "@/presentation/hooks/messaging/useMessagingData";
import MessageList from "@/presentation/components/features/messaging/MessageList";
import MessageComposer from "@/presentation/components/features/messaging/MessageComposer";
import ConversationHeader from "@/presentation/components/features/messaging/ConversationHeader";
import MessageHeader from "@/presentation/components/features/messaging/MessageHeader";
import MessageSearchBar from "@/presentation/components/features/messaging/MessageSearchBar";
import ConversationsList from "@/presentation/components/features/messaging/ConversationsList";
import { ContactSelectorModal } from "@/presentation/components/common/Modal/ContactSelectorModal";
import { useAppSelector } from "@/presentation/hooks/redux";

/**
 * Messages component - Main messaging interface for the application
 * Provides a complete messaging experience with conversation management,
 * message composition, and contact selection
 *
 * @returns {JSX.Element} The messaging interface component
 */
const Messages: FC = memo(() => {
  const isDarkMode = useDarkMode();
  const [searchQuery, setSearchQuery] = useState("");
  const [showContactSelector, setShowContactSelector] = useState(false);
  const currentUserId = useAppSelector(
    (state) => state.authentification.user?.id
  );

  const {
    conversations,
    activeConversation,
    messages,
    contacts,
    loading,
    setActiveConversation,
    sendMessage,
    markAsRead,
    searchConversations,
    createNewConversation,
  } = useMessagingData();

  /**
   * Handle starting a new conversation
   */
  const handleNewConversation = () => {
    setShowContactSelector(true);
  };

  /**
   * Handle contact selection for new conversation
   * @param contactId - ID of the selected contact
   */
  const handleContactSelect = (contactId: number) => {
    createNewConversation(contactId);
    setShowContactSelector(false);
  };

  /**
   * Handle search input change
   * @param query - Search query string
   */
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    searchConversations(query);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Page Header */}
      <MessageHeader setShowContactSelector={setShowContactSelector} />

      {/* Main Messaging Interface */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-0">
        {/* Conversations Sidebar */}
        <div
          className={`${CARD_STYLES.container} bg-white dark:bg-gray-800 flex flex-col h-[calc(100vh-200px)]`}
        >
          {/* Search Bar */}
          <MessageSearchBar
            searchQuery={searchQuery}
            handleSearchChange={handleSearchChange}
          />

          {/* Conversations List */}
          <ConversationsList
            activeConversation={activeConversation}
            conversations={conversations}
            handleNewConversation={handleNewConversation}
            setActiveConversation={setActiveConversation}
          />
        </div>

        {/* Message Area */}
        <div className="lg:col-span-2 flex flex-col">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-meddoc-primary"></div>
            </div>
          ) : activeConversation ? (
            <div
              className={`${CARD_STYLES.container} bg-white dark:bg-gray-800 flex flex-col h-[calc(100vh-200px)]`}
            >
              {/* Conversation Header */}
              <ConversationHeader
                conversation={activeConversation}
                onMarkAsRead={() => markAsRead(activeConversation.id)}
              />

              {/* Messages List */}
              <MessageList
                messages={messages}
                currentUserId={currentUserId} // This should come from auth context
                loading={loading}
              />

              {/* Message Composer */}
              <MessageComposer
                onSendMessage={(content) =>
                  sendMessage(activeConversation.id, content)
                }
                disabled={loading}
              />
            </div>
          ) : (
            <div
              className={`${CARD_STYLES.container} bg-white dark:bg-gray-800 flex items-center justify-center h-[calc(100vh-200px)]`}
            >
              <div className="text-center">
                <MessageCircle className="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                <h3
                  className={`text-lg font-medium ${TEXT_STYLES.primary} mb-2`}
                >
                  Sélectionnez une conversation
                </h3>
                <p className={`${TEXT_STYLES.secondary} mb-4`}>
                  Choisissez une conversation existante ou commencez-en une
                  nouvelle
                </p>
                <button
                  onClick={handleNewConversation}
                  className={`${BUTTON_STYLES.base} ${BUTTON_STYLES.primary} flex items-center gap-2 mx-auto`}
                >
                  <Plus className="h-4 w-4" />
                  Nouveau message
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Contact Selector Modal */}
      {showContactSelector && (
        <ContactSelectorModal
          isOpen={showContactSelector}
          contacts={contacts}
          loading={loading}
          onSelectContact={handleContactSelect}
          handleClose={() => setShowContactSelector(false)}
        />
      )}
    </div>
  );
});

Messages.displayName = "Messages";

export default Messages;
