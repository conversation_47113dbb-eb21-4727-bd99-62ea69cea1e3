import { Patient } from "@/domain/models";
import {
  patients_groupe_sanguin_enum,
  utilisateurs_role_enum,
} from "@/domain/models/enums";
import { GetProfessionalPatientByIdRepository } from "@/infrastructure/repositories/ProfesionnalPatient/GetProfessionalPatientByIdRepository";
import FormField from "@/presentation/components/common/ui/FormField";
import { RegisterStep1 } from "@/presentation/components/features/patient/registerPatienStepper";
import { useUpdatePatientLogic } from "@/presentation/hooks/authentification/patient/useUpdatePatientLogic";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { PRIMARY } from "@/shared/constants/Color";
import { Divider, But<PERSON> } from "@mui/material";
import { Phone, Syringe } from "lucide-react";
import { useParams } from "react-router-dom";
import { GetLocaleDate } from "@/shared/utils/getLocaleDate";

const ProfilePatient = ({
  handleBack,
  patient,
}: {
  handleBack?: () => void;
  patient: Patient;
}) => {
  const { id } = useParams();
  const patientId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const {
    control,
    errors,
    register,
    setValue,
    handleSubmit,
    onSubmit: updatePatient,
  } = useUpdatePatientLogic(patient);
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { loading } = useCarnetDeSante();
  const { handleUpdateProfessionnelPatient, selectedDataProfessionalPatient } = useProfessionnelPatient();
  const onSubmit = async () => {
    const data = updatePatient(
      role === utilisateurs_role_enum.PATIENT ? patientId : patient.id
    );
    if (data) {
      // Mettre à jour le champ updated_date dans la table ProfessionnelPatient
      if (selectedDataProfessionalPatient && selectedDataProfessionalPatient.id) {
        try {
          await handleUpdateProfessionnelPatient(selectedDataProfessionalPatient.id, {
            updated_date: GetLocaleDate()
          });
          console.log("Date de mise à jour actualisée")
        } catch (error) {
          console.error("Erreur lors de la mise à jour de la date:", error);
        }
      }

      if (handleBack) {
        handleBack();
      }
    }
  };

  const handleSaveAndClose = async () => {
    await onSubmit();
    if (handleBack) {
      handleBack();
    }
  };

  return (
    <>
      <div>
        <div className="my-4">
          {/* <ContactUrgence /> */}
          <RegisterStep1
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            register={register}
            setValue={setValue}
            patient={patient}
          />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-4">
            <FormField
              id="groupe_sanguin"
              label="Groupe sanguin"
              type="select"
              placeholder="Entrez votre groupe sanguin"
              icon={Syringe}
              register={register}
              required
              options={Object.values(patients_groupe_sanguin_enum).map(
                (groupe) => {
                  return {
                    label: groupe,
                    value: groupe,
                  };
                }
              )}
              error={errors.groupe_sanguin}
              validation={{
                required: "Le groupe sanguin est requis",
              }}
            />
            {/* <FormField
              id="telephone"
              label="Téléphone"
              type="tel"
              placeholder="Ex: 032XXXXXXX ou +261XXXXXXXXX"
              icon={Phone}
              register={register}
              required
              error={errors.telephone}
              helpText="Format accepté: 032XXXXXXX ou +261XXXXXXXXX (opérateurs: 32, 33, 34, 39)"
              validation={{
                required: "Le numéro de téléphone est requis",
                pattern: {
                  value: /^(0(32|33|34|39)\d{7}|\+261(32|33|34|39)\d{7})$/,
                  message:
                    "Format de téléphone invalide. Utilisez: 032XXXXXXX ou +261XXXXXXXXX",
                },
              }}
            /> */}
          </div>
        </div>
        <Divider />
        <div className="flex justify-end space-x-4 mt-4">
          <Button
            variant="outlined"
            color="primary"
            sx={{ textTransform: "none", borderRadius: 5 }}
            onClick={handleBack}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            color="primary"
            sx={{
              textTransform: "none",
              borderRadius: 5,
              backgroundColor: PRIMARY,
            }}
            loading={loading}
            onClick={handleSaveAndClose}
          >
            Enregistrer
          </Button>
        </div>
      </div>
    </>
  );
};

export default ProfilePatient;
