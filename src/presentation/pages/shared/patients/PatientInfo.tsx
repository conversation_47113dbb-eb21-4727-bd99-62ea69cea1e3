import { Box } from "@mui/material";
import { useEffect } from "react";
import CarnetDeSante from "@/presentation/pages/professional/patients/CarnetDeSante";
import HealthRecord from "@/presentation/components/features/patient/PatientCard/HealthRecord";
import ConsultationMedicale from "@/presentation/pages/professional/patients/ConsultationMedicale";
import SigneVitaux from "@/presentation/pages/professional/patients/SigneVitaux";
import { active_tab_enum } from "@/domain/models/enums";
import Facturation from "@/presentation/pages/professional/patients/Facturation";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useAppSelector } from "@/presentation/hooks/redux";
import Diagnostic from "../../professional/patients/Diagnostic";
import PharmacieEtMedicament from "../../professional/patients/PharmacieEtMedicament";
import useAuth from "@/presentation/hooks/use-auth";
import { useParams } from "react-router-dom";
import PageTitle from "@/presentation/components/common/PageTitle";

const RenderPatientInfo = (activeTab: active_tab_enum) => {
  switch (activeTab) {
    case active_tab_enum.carnetDeSante:
      return (
        <CarnetDeSante className="grid grid-cols-1 lg:grid-cols-2 gap-4" />
      );
    case active_tab_enum.consultationMedicale:
      return <ConsultationMedicale />;
    case active_tab_enum.signeVitaux:
      return <SigneVitaux />;
    case active_tab_enum.diagnostic:
      return <Diagnostic />;
    case active_tab_enum.facturation:
      return <Facturation />;
    case active_tab_enum.pharmacie:
      return <PharmacieEtMedicament />;
    default:
      return <p>Aucune rendue disponible</p>;
  }
};

const PatientInfo = () => {
  const { searchProfessionalById } = useSearchProfessional();
  const { activeTab, resetActiveTab } = useCarnetDeSanteState();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const { id } = useParams();
  const { getRoleUserSelected } = useAuth();

  useEffect(() => {
    if (id) {
      getRoleUserSelected(Number(id));
    }
    resetActiveTab();
  }, [id]);

  useEffect(() => {
    if (professionalId) {
      searchProfessionalById({ id: professionalId });
    }
  }, [professionalId]);

  return (
    <Box className="w-full">
      <PageTitle
        titre="Informations"
        description="Toutes les informations sur le patient et ses proches"
      />
      <HealthRecord />
      {RenderPatientInfo(activeTab)}
    </Box>
  );
};

export default PatientInfo;
