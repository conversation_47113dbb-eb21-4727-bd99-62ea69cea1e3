import { usePatientProfileLogic } from "@/presentation/hooks/patient/use-patient-profile-logic";
import {
    PatientProfileHeader,
    PatientInformationGrid,
    MedicalSummaryCard,
    SimpleEditProfileModal,
    ProfileLoadingState,
    ProfileUnauthenticatedState,
    ProfileNotFoundState,
} from "./components";

/**
 * Composant principal du profil patient
 * Affiche les informations personnelles du patient connecté
 * et permet la modification via un modal
 */
const PatientProfile = () => {
    // Utilisation du hook personnalisé pour centraliser toute la logique
    const {
        currentPatient,
        communeName,
        regionName,
        districtName,
        provinceName,
        isEditModalOpen,
        isLoading,
        isUnauthenticated,
        isPatientNotFound,
        calculateAge,
        handleOpenEditModal,
        handleCloseEditModal,
        handleProfileUpdateSuccess,
    } = usePatientProfileLogic();

    // Gestion des états de chargement et d'erreur
    if (isLoading) {
        return <ProfileLoadingState />;
    }

    if (isUnauthenticated) {
        return <ProfileUnauthenticatedState />;
    }

    if (isPatientNotFound) {
        return <ProfileNotFoundState />;
    }


    // Rendu principal du profil
    return (
        <div className="max-w-7xl mx-auto space-y-8">
            {/* En-tête du profil patient */}
            <PatientProfileHeader
                patient={currentPatient!}
                calculateAge={calculateAge}
                onEdit={handleOpenEditModal}
            />

            {/* Grille d'informations du patient */}
            <PatientInformationGrid
                patient={currentPatient!}
                communeName={communeName}
                districtName={districtName}
                regionName={regionName}
                provinceName={provinceName}
            />

            {/* Résumé médical */}
            <MedicalSummaryCard
                patient={currentPatient!}
                calculateAge={calculateAge}
            />

            {/* Modal de modification du profil */}
            <SimpleEditProfileModal
                isOpen={isEditModalOpen}
                handleClose={handleCloseEditModal}
                patient={currentPatient!}
                onSuccess={handleProfileUpdateSuccess}
            />
        </div>
    );
};

export default PatientProfile;
