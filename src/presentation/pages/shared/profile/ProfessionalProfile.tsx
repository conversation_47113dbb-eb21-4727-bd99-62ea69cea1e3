import { Helmet } from "react-helmet-async";
import { motion } from "framer-motion";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import {
  ProfileEditHeaderModern,
  ProfileEditPhotoModern,
  PresentationEditSection,
  ProfessionalInformationEdit,
  ServicesEditSection,
  EstablishmentCRUDSection,
  PaymentAndInsuranceCRUDSection,
  KeywordsCRUDSection,
  LanguagesCRUDSection,
  CabinetImagesCRUDSection,
  DiplomasCRUDSection,
  ExperiencesCRUDSection,
  PublicationsCRUDSection,
  SpecialtiesCRUDSection,
  ContactCRUDSection,
} from "@/presentation/components/features/professional/profile/edit";

import { PhotoTypeEnum } from "@/domain/models/enums/photo_type_enum.ts";
import { Photo } from "@/domain/models/Photo";

import useProfessionalProfile from "@/presentation/hooks/professional/use-professional-profile.ts";
import { useEffect, useState } from "react";
import ExpertiseCRUDSection from "@/presentation/components/features/professional/profile/edit/ExpertiseCRUDSection";

const ProfessionalProfile = () => {
  const { isLoading, error, profileData, handlers } = useProfessionalProfile();
  const [profileImage, setProfileImage] = useState<Photo | null>(null);

  useEffect(() => {
    if (profileData && profileData.photos) {
      const photo = profileData.photos.find(
        (photo) => photo.type === PhotoTypeEnum.PROFILE,
      );
      setProfileImage(photo || null);
    }
  }, [profileData, profileData?.photos]);

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 justify-center items-center min-h-screen">
        <LoadingSpinner className="h-auto" />
        <p>Chargement des donnees</p>
      </div>
    );
  }

  if (!profileData && !isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-gray-600">Aucune donnée trouvée</div>
      </div>
    );
  }

  const pageTitle = `${profileData.baseInfo.titre} ${profileData.baseInfo.nom} ${profileData.baseInfo.prenom} - ${profileData.specialities[0]?.nom_specialite || "Médecin"} à ${profileData.baseInfo.fokontany}`;
  const pageDescription = `${profileData.baseInfo.titre} ${profileData.baseInfo.nom} ${profileData.baseInfo.prenom}, ${profileData.specialities.map((s) => s.nom_specialite).join(", ")} à ${profileData.baseInfo.fokontany}. ${profileData.baseInfo.presentation_generale.substring(0, 160)}...`;
  const keywords = `${profileData.specialities.map((s) => s.nom_specialite).join(", ")}, médecin ${profileData.baseInfo.fokontany}, ${profileData.baseInfo.titre} ${profileData.baseInfo.nom}`;

  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta name="keywords" content={keywords} />
        {/* Pour le partage sur Facebook */}
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:image" content={profileImage?.path} />
      </Helmet>

      <div className="w-full mx-auto px-2 sm:px-3 lg:px-4 py-4">
        {/* En-tête du profil avec animation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <ProfileEditHeaderModern
            titre={profileData.baseInfo.titre}
            nom={profileData.baseInfo.nom}
            prenom={profileData.baseInfo.prenom}
            specialites={profileData.specialities}
            fokontany={profileData.baseInfo.fokontany}
            adresse={profileData.baseInfo.adresse}
            onBaseInfoUpdate={(baseInfo) =>
              handlers.handleSaveBaseInfo(baseInfo)
            }
          />
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
          {/* Colonne de gauche - Photo et informations principales */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
            className="lg:col-span-1 space-y-4"
          >
            {/* Photo de profil */}
            <ProfileEditPhotoModern
              photo={profileImage}
              fullName={`${profileData.baseInfo.titre} ${profileData.baseInfo.nom} ${profileData.baseInfo.prenom}`}
              onPhotoUpload={handlers.uploadProfilePhoto}
              onPhotoRemove={handlers.deleteProfilePhoto}
              maxSizeMB={5}
            />

            {/* Informations de contact */}
            <ContactCRUDSection
              email={profileData?.userData?.email || ""}
              telephone={profileData?.contact?.[0] || null}
              adresse={profileData.baseInfo?.adresse || ""}
              fokontany={profileData.baseInfo?.fokontany || ""}
              infoAcces={profileData.baseInfo?.informations_acces || ""}
              onSave={(data) =>
                handlers.handleCreateContact(
                  data,
                  profileData?.contact?.[0]?.id || 0,
                  profileData.baseInfo.utilisateur_id,
                  profileData.baseInfo.id,
                )
              }
            />
          </motion.div>

          {/* Colonne de droite - Informations détaillées */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
            className="lg:col-span-2 space-y-4"
          >
            {/* Présentation */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <PresentationEditSection
                presentationGenerale={
                  profileData.baseInfo.presentation_generale || ""
                }
                onSave={handlers.handleSavePresentation}
              />
            </motion.div>

            {/* Spécialités */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <ExpertiseCRUDSection
                availableExpertisesLists={[]}
                expertises={[]}
                onAddExpertise={handlers.createExpertise}
                onDeleteSpeciality={handlers.deleteExpertise}
              />
              <SpecialtiesCRUDSection
                availableSpecialitiesLists={handlers.availableSpecialities}
                specialties={profileData.specialities}
                onAddSpecialty={handlers.handleAddSpecialty}
                onDeleteSpecialty={handlers.handleDeleteSpeciality}
              />
            </motion.div>

            {/* Informations professionnelles */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <ProfessionalInformationEdit
                numeroOrdre={profileData.baseInfo.numero_ordre || ""}
                raisonSociale={profileData.baseInfo.raison_sociale || ""}
                nif={profileData.baseInfo.nif || ""}
                stat={profileData.baseInfo.stat || ""}
                onSave={handlers.handleSaveProfessionalInformation}
              />
            </motion.div>

            {/* Services */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <ServicesEditSection
                typesConsultation={profileData.baseInfo.types_consultation}
                nouveauPatientAcceptes={
                  profileData.baseInfo.nouveau_patient_acceptes
                }
                onSave={handlers.handleUpdateProfessionalServiceSection}
              />
            </motion.div>

            {/* Informations de l'établissement */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <EstablishmentCRUDSection
                etablishmentData={profileData.etablishments[0]}
                geolocation={String(profileData.baseInfo.geolocalisation) || ""}
                handleGeolocationChange={handlers.updateGeolocation}
                onSave={handlers.handleSaveEstablishment}
              />
            </motion.div>

            {/* Diplômes */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <DiplomasCRUDSection
                diplomas={profileData.diplomas}
                professionalId={profileData.baseInfo.id || 0}
                addDiploma={handlers.handleAddDiploma}
                updateDiploma={handlers.handleUpdateDiploma}
                deleteDiploma={handlers.handleDeleteDiploma}
              />
            </motion.div>

            {/* Expériences */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <ExperiencesCRUDSection
                experiences={profileData.experiences}
                professionalId={profileData.baseInfo.id || 0}
                addExperience={handlers.handleAddExperience}
                updateExperience={handlers.handleUpdateExperience}
                deleteExperience={handlers.handleDeleteExperience}
              />
            </motion.div>

            {/* Publications */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <PublicationsCRUDSection
                publications={profileData.publications}
                onAddPublication={handlers.handleAddPublication}
                onUpdatePublication={handlers.handleUpdatePublication}
                onDeletePublication={handlers.handleDeletePublication}
              />
            </motion.div>

            {/* Langues parlées */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.65 }}
            >
              <LanguagesCRUDSection
                languages={profileData.languages}
                onAddLanguages={handlers.handleAddLanguages}
                onDeleteLanguage={handlers.handleDeleteLanguage}
              />
            </motion.div>

            {/* Modes de paiement et assurances */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <PaymentAndInsuranceCRUDSection
                availableInsurances={handlers.availableInsurances}
                paymentMethods={profileData.paymentMethods}
                insurances={profileData.insurances}
                onSavePaymentMethods={handlers.handleSavePaymentMethods}
                onAddInsurance={handlers.handleAddInsurance}
                onRemoveInsurance={handlers.handleRemoveInsurance}
              />
            </motion.div>

            {/* Mots-clés */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <KeywordsCRUDSection
                keywords={profileData.keywords}
                onAddKeywords={handlers.handleAddKeywords}
                onDeleteKeyword={handlers.handleDeleteKeyword}
                onDeleteKeywordSilent={handlers.handleDeleteKeyword}
              />
            </motion.div>

            {/* Galerie d'images du cabinet */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.9 }}
            >
              <CabinetImagesCRUDSection
                images={profileData.photos.filter(
                  (photo) => photo.type === PhotoTypeEnum.PRESENTATION,
                )}
                onAddImages={handlers.uploadPresentationPhoto}
                onRemoveImage={handlers.deletePresentationPhoto}
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </>
  );
};

export default ProfessionalProfile;
