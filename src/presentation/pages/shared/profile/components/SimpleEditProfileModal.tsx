import { useState } from "react";
import { useForm } from "react-hook-form";
import { Commune, District, Patient, Province, Region } from "@/domain/models";
import { sexe_enum, patients_groupe_sanguin_enum } from "@/domain/models/enums";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";
import { useToast } from "@/presentation/hooks/use-toast";
import FormField from "@/presentation/components/common/ui/FormField";
import DateField from "@/presentation/components/common/ui/DateField";
import { PRIMARY } from "@/shared/constants/Color";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    IconButton,
    Typography,
    Tooltip,
    Button,
    Grid,
    Divider,
} from "@mui/material";
import { X, User, Mail, Phone, MapPin, Calendar, Heart, Globe, Briefcase, Syringe } from "lucide-react";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import SelectProvince from "@/presentation/components/locationSelector/SelectProvince";
import SelectRegion from "@/presentation/components/locationSelector/SelectRegion";
import SelectDistrict from "@/presentation/components/locationSelector/SelectDistrict";
import SelectCommune from "@/presentation/components/locationSelector/SelectCommune";

interface SimpleEditProfileModalProps {
    isOpen: boolean;
    handleClose: () => void;
    patient: Patient;
    onSuccess?: () => void;
}

export const SimpleEditProfileModal = ({
    isOpen,
    handleClose,
    patient,
    onSuccess
}: SimpleEditProfileModalProps) => {
    const {
        selectedProvince,
        handleProvinceChange,
        selectedRegion,
        handleRegionChange,
        selectedDistrict,
        handleDistrictChange,
        selectedCommune,
        handleCommuneChange,
    } = useLocationSelector();

    const [loading, setLoading] = useState(false);
    const { handleUpdatePatient } = useCarnetDeSante();
    const toast = useToast();
    const patientId = useAppSelector((state) => state.authentification.userData?.id);

    const {
        register,
        handleSubmit,
        formState: { errors },
        control,
    } = useForm({
        defaultValues: {
            nom: patient.nom || "",
            prenom: patient.prenom || "",
            email: patient.email || "",
            telephone: patient.telephone || "",
            adresse: patient.adresse || "",
            sexe: patient.sexe === "homme" ? sexe_enum.homme : patient.sexe === "femme" ? sexe_enum.femme : patient.sexe || "",
            date_naissance: patient.date_naissance
                ? new Date(patient.date_naissance)
                : null,
            groupe_sanguin: patient.groupe_sanguin || "",
            situation_matrimonial: patient.situation_matrimonial || "",
            nationalite: patient.nationalite || "",
            profession: patient.profession || "",
            nb_enfant: patient.nb_enfant || 0,
        },
    });

    const onSubmit = async (data: any) => {
        if (!patientId) {
            toast.error("Erreur: ID patient non trouvé");
            return;
        }

        setLoading(true);
        try {
            const formattedData = {
                ...data,
                date_naissance: new Date(data.date_naissance),
            };

            await handleUpdatePatient(patientId, formattedData);
            toast.success("Profil mis à jour avec succès");
            if (onSuccess) {
                onSuccess();
            }
            handleClose();
        } catch (error) {
            console.error("Erreur lors de la mise à jour:", error);
            toast.error("Erreur lors de la mise à jour du profil");
        } finally {
            setLoading(false);
        }
    };

    return (
        <Dialog
            open={isOpen}
            onClose={handleClose}
            maxWidth="md"
            fullWidth
            PaperProps={{
                sx: {
                    borderRadius: 2,
                    maxHeight: '90vh'
                }
            }}
        >
            <DialogTitle className="flex justify-between items-center bg-gradient-to-br from-meddoc-fonce to-meddoc-primary text-white">
                <Typography variant="h6" component="div" className="font-semibold text-center w-full ">
                    Modifier mon profil
                </Typography>
                <Tooltip title="Fermer">
                    <IconButton
                        onClick={handleClose}
                        size="small"
                        sx={{ color: 'white' }}
                    >
                        <X className="h-5 w-5" />
                    </IconButton>
                </Tooltip>
            </DialogTitle>

            <DialogContent sx={{ p: 3 }}>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Grid container spacing={3}>
                        {/* Informations personnelles */}
                        <Grid item xs={12}>
                            <Typography variant="h6" gutterBottom color="primary" className="p-5 text-center">
                                Informations personnelles
                            </Typography>
                            <Divider sx={{ mb: 2 }} />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="nom"
                                label="Nom"
                                type="text"
                                placeholder="Entrez votre nom"
                                icon={User}
                                register={register}
                                required
                                error={errors.nom}
                                validation={{
                                    required: "Le nom est requis",
                                }}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="prenom"
                                label="Prénom"
                                type="text"
                                placeholder="Entrez votre prénom"
                                icon={User}
                                register={register}
                                required
                                error={errors.prenom}
                                validation={{
                                    required: "Le prénom est requis",
                                }}
                            />
                        </Grid>

                        {/* <Grid item xs={12} sm={6}>
                            <FormField
                                id="email"
                                label="Email"
                                type="email"
                                placeholder="<EMAIL>"
                                icon={Mail}
                                register={register}
                                error={errors.email}
                                validation={{
                                    pattern: {
                                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                        message: "Adresse email invalide",
                                    },
                                }}
                            />
                        </Grid> */}

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="telephone"
                                label="Téléphone"
                                type="tel"
                                placeholder="Ex: 032XXXXXXX ou +261XXXXXXXXX"
                                icon={Phone}
                                register={register}
                                required
                                error={errors.telephone}
                                helpText="Format accepté: 032XXXXXXX ou +261XXXXXXXXX"
                                validation={{
                                    required: "Le numéro de téléphone est requis",
                                    pattern: {
                                        value: /^(\+261|0)(32|33|34|39)\d{7}$/,
                                        message: "Format de téléphone invalide",
                                    },
                                }}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="adresse"
                                label="Adresse"
                                type="text"
                                placeholder="Entrez votre adresse complète"
                                icon={MapPin}
                                register={register}
                                error={errors.adresse}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="sexe"
                                label="Sexe"
                                type="select"
                                placeholder="Sélectionnez votre sexe"
                                icon={User}
                                register={register}
                                required
                                error={errors.sexe}
                                validation={{
                                    required: "Le sexe est requis",
                                }}
                                options={[
                                    { value: sexe_enum.homme, label: "Homme" },
                                    { value: sexe_enum.femme, label: "Femme" },
                                ]}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <DateField
                                id="date_naissance"
                                label="Date de naissance"
                                icon={Calendar}
                                control={control}
                                error={errors.date_naissance}
                                maxDate={new Date()}
                                required
                            />
                        </Grid>

                        {/* Informations supplémentaires */}
                        <Grid item xs={12}>
                            <Typography variant="h6" gutterBottom color="primary" className="text-center" sx={{ mt: 2 }}>
                                Informations supplémentaires
                            </Typography>
                            <Divider sx={{ mb: 2 }} />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="groupe_sanguin"
                                label="Groupe sanguin"
                                type="select"
                                placeholder="Sélectionnez votre groupe sanguin"
                                icon={Syringe}
                                register={register}
                                error={errors.groupe_sanguin}
                                options={[
                                    { value: "", label: "Aucun" },
                                    ...Object.values(patients_groupe_sanguin_enum).map((groupe) => ({
                                        value: groupe,
                                        label: groupe,
                                    }))
                                ]}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="nb_enfant"
                                label="Nombre d'enfant"
                                type="number"
                                placeholder="Entrez le nombre d'enfant"
                                icon={User}
                                register={register}
                                error={errors.nb_enfant}
                                validation={{
                                    validate: (value: any) => {
                                        const num = parseInt(value);
                                        if (isNaN(num)) {
                                            return "Veuillez entrer un nombre valide";
                                        }
                                        if (num < 0) {
                                            return "Le nombre d'enfants ne peut pas être négatif";
                                        }
                                        return true;
                                    },
                                }}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="situation_matrimonial"
                                label="Situation matrimoniale"
                                type="select"
                                placeholder="Sélectionnez votre situation"
                                icon={Heart}
                                register={register}
                                error={errors.situation_matrimonial}
                                options={[
                                    { value: "", label: "Aucune" },
                                    { value: "celibataire", label: "Célibataire" },
                                    { value: "marie", label: "Marié(e)" },
                                    { value: "divorce", label: "Divorcé(e)" },
                                    { value: "veuf", label: "Veuf/Veuve" },
                                ]}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="nationalite"
                                label="Nationalité"
                                type="text"
                                placeholder="Ex: Malagasy, Française..."
                                icon={Globe}
                                register={register}
                                error={errors.nationalite}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormField
                                id="profession"
                                label="Profession"
                                type="text"
                                placeholder="Ex: Médecin, Enseignant..."
                                icon={Briefcase}
                                register={register}
                                error={errors.profession}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <Typography variant="h6" gutterBottom color="primary" className="text-center" sx={{ mt: 2 }}>
                                Localisation
                            </Typography>
                            <Divider sx={{ mb: 2 }} />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <SelectProvince
                                register={register}
                                errors={errors}
                                value={selectedProvince}
                                onChange={(province: Province) => {
                                    handleProvinceChange(province);
                                }}
                                province_id={patient?.province}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <SelectRegion
                                register={register}
                                errors={errors}
                                value={selectedRegion}
                                onChange={(region: Region) => {
                                    handleRegionChange(region);
                                }}
                                isDisabled={!selectedProvince}
                                region_id={patient?.region}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <SelectDistrict
                                register={register}
                                errors={errors}
                                value={selectedDistrict}
                                onChange={(district: District) => {
                                    handleDistrictChange(district);
                                }}
                                isDisabled={!selectedRegion}
                                district_id={patient?.district}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <SelectCommune
                                register={register}
                                errors={errors}
                                value={selectedCommune}
                                onChange={(commune: Commune) => {
                                    handleCommuneChange(commune);
                                }}
                                isDisabled={!selectedDistrict}
                                commune_id={patient?.commune}
                            />
                        </Grid>

                        {/* Boutons d'action */}
                        <Grid item xs={12}>
                            <div className="flex justify-end space-x-4 mt-4">
                                <Button
                                    variant="outlined"
                                    color="primary"
                                    sx={{ textTransform: "none", borderRadius: 5 }}
                                    onClick={handleClose}
                                    disabled={loading}
                                >
                                    Annuler
                                </Button>
                                <Button
                                    type="submit"
                                    variant="contained"
                                    color="primary"
                                    sx={{
                                        textTransform: "none",
                                        borderRadius: 5,
                                        backgroundColor: PRIMARY,
                                    }}
                                    disabled={loading}
                                >
                                    {loading ? "Enregistrement..." : "Enregistrer"}
                                </Button>
                            </div>
                        </Grid>
                    </Grid>
                </form>
            </DialogContent>
        </Dialog >
    );
};
