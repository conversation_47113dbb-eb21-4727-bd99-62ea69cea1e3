import { User } from "lucide-react";

/**
 * Composant pour l'état de chargement du profil
 */
export const ProfileLoadingState = () => (
    <div className="max-w-7xl mx-auto space-y-8 p-8">
        <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Chargement du profil...</p>
            </div>
        </div>
    </div>
);

/**
 * Composant pour l'état d'utilisateur non authentifié
 */
export const ProfileUnauthenticatedState = () => (
    <div className="max-w-7xl mx-auto space-y-8 p-8">
        <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
                <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-gray-700 mb-2">Non authentifié</h2>
                <p className="text-gray-600">Veuillez vous connecter pour accéder à votre profil.</p>
            </div>
        </div>
    </div>
);

/**
 * Composant pour l'état de profil non trouvé
 */
export const ProfileNotFoundState = () => (
    <div className="max-w-7xl mx-auto space-y-8 p-8">
        <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
                <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-gray-700 mb-2">Profil non trouvé</h2>
                <p className="text-gray-600">Impossible de charger les informations du patient.</p>
            </div>
        </div>
    </div>
);
