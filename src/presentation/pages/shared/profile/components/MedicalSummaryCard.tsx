import { Card, CardContent } from "@mui/material";
import { Activity } from "lucide-react";
import { Patient } from "@/domain/models";

interface MedicalSummaryCardProps {
    patient: Patient;
    calculateAge: (birthDate: Date) => number;
}

/**
 * Composant pour afficher le résumé médical du patient
 * Contient les informations importantes comme le groupe sanguin, l'âge, etc.
 */
const MedicalSummaryCard = ({ patient, calculateAge }: MedicalSummaryCardProps) => {
    return (
        <Card 
            className="border-0 bg-gradient-to-r from-emerald-500 via-teal-600 to-cyan-600 text-white shadow-2xl"
            style={{
                background: 'linear-gradient(to bottom right, #27aae1, #027f3b)'
            }}
        >
            <CardContent className="p-8">
                <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                    {/* En-tête avec icône et titre */}
                    <div className="flex items-center gap-6">
                        <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                            <Activity className="h-8 w-8 text-white" />
                        </div>
                        <div>
                            <h3 className="text-2xl font-bold mb-1">Résumé Médical</h3>
                            <p className="text-emerald-100">Informations de santé importantes</p>
                        </div>
                    </div>

                    {/* Informations médicales */}
                    <div className="flex flex-wrap gap-4">
                        {/* Groupe sanguin */}
                        {patient?.groupe_sanguin && (
                            <div className="bg-white/20 backdrop-blur-sm rounded-xl px-4 py-3 text-center">
                                <p className="text-2xl font-bold">{patient.groupe_sanguin}</p>
                                <p className="text-xs text-emerald-100">Groupe sanguin</p>
                            </div>
                        )}
                        
                        {/* Statut donneur */}
                        <div className="bg-white/20 backdrop-blur-sm rounded-xl px-4 py-3 text-center">
                            <p className="text-2xl font-bold">{patient?.donneur_sang ? '✓' : '✗'}</p>
                            <p className="text-xs text-emerald-100">Donneur</p>
                        </div>

                        {/* Âge */}
                        <div className="bg-white/20 backdrop-blur-sm rounded-xl px-4 py-3 text-center">
                            <p className="text-2xl font-bold">{calculateAge(patient.date_naissance)}</p>
                            <p className="text-xs text-emerald-100">Âge</p>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default MedicalSummaryCard;
