import { Patient } from "@/domain/models";
import { useUpdatePatientLogic } from "@/presentation/hooks/authentification/patient/useUpdatePatientLogic";
import { RegisterStep1 } from "@/presentation/components/features/patient/registerPatienStepper";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { PRIMARY } from "@/shared/constants/Color";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    IconButton,
    Typography,
    Tooltip,
    Button,
    Divider,
} from "@mui/material";
import { X } from "lucide-react";

interface EditProfileModalProps {
    isOpen: boolean;
    handleClose: () => void;
    patient: Patient;
    onSuccess?: () => void;
}

export const EditProfileModal = ({
    isOpen,
    handleClose,
    patient,
    onSuccess
}: EditProfileModalProps) => {
    // Hooks au top-level (règles des hooks)
    const patientId = useAppSelector((state) => state.authentification.userData?.id);
    const {
        control,
        errors,
        register,
        setValue,
        handleSubmit,
        onSubmit: updatePatient,
    } = useUpdatePatientLogic(patient);
    const role = useAppSelector((state) => state.authentification.user?.role);
    const { loading } = useCarnetDeSante();

    const onSubmit = async () => {
        const data = updatePatient(
            role === utilisateurs_role_enum.PATIENT ? patientId : patientId
        );
        if ((await data)?.success) {
            onSuccess?.();
            handleClose();
        }
    };

    const handleSaveAndClose = async () => {
        await onSubmit();
    };

    return (
        <Dialog
            open={isOpen}
            onClose={handleClose}
            maxWidth="md"
            fullWidth
            PaperProps={{
                sx: {
                    borderRadius: 2,
                    maxHeight: '90vh'
                }
            }}
        >
            <DialogTitle className="flex justify-between items-center bg-gradient-to-r from-blue-600 to-blue-700 text-white">
                <Typography variant="h6" component="div" className="font-semibold">
                    Modifier mon profil
                </Typography>
                <Tooltip title="Fermer">
                    <IconButton
                        onClick={handleClose}
                        size="small"
                        sx={{ color: 'white' }}
                    >
                        <X className="h-5 w-5" />
                    </IconButton>
                </Tooltip>
            </DialogTitle>

            <DialogContent sx={{ p: 3 }}>
                <div className="space-y-4">
                    {/* Formulaire avec RegisterStep1 */}
                    <div className="my-4">
                        <RegisterStep1
                            control={control}
                            errors={errors}
                            onSubmit={onSubmit}
                            register={register}
                            setValue={setValue}
                            patient={patient}
                        />
                    </div>

                    <Divider />

                    {/* Boutons d'action */}
                    <div className="flex justify-end space-x-4 mt-4">
                        <Button
                            variant="outlined"
                            color="primary"
                            sx={{ textTransform: "none", borderRadius: 5 }}
                            onClick={handleClose}
                            disabled={loading}
                        >
                            Annuler
                        </Button>
                        <Button
                            variant="contained"
                            color="primary"
                            sx={{
                                textTransform: "none",
                                borderRadius: 5,
                                backgroundColor: PRIMARY,
                            }}
                            onClick={handleSaveAndClose}
                            disabled={loading}
                        >
                            {loading ? "Enregistrement..." : "Enregistrer"}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};
