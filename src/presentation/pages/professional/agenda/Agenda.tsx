import "react-big-calendar/lib/css/react-big-calendar.css";
import "@/styles/calendar.css";
import { Box, Button } from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useAgendaState } from "@/presentation/hooks/agenda";
import {
  useAvailabilitySettings,
  useAvailabilitySettingState,
} from "@/presentation/hooks/agenda/settings";
import { useEvenement } from "@/presentation/hooks/agenda/events";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import EventPopover from "@/presentation/components/common/Popover/EventPopover";
import { AgendaCalendar } from "@/presentation/components/features/professional/agenda/component/AgendaCalendar";
import { AgendaModals } from "@/presentation/components/features/professional/agenda/component/AgendaModals";
import { useEffect } from "react";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import PageTitle from "@/presentation/components/common/PageTitle";

// icon
import logoDass from "@/assets/cua/cua.png";
import { Plus } from "lucide-react";
const Agenda = () => {
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );

  const userId = useAppSelector((state) => state.authentification.user?.id);
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { currentProfessional, searchProfessionalById } =
    useSearchProfessional();

  const { settings, getSettings } = useAvailabilitySettings();
  const { evenements, fetchEvenements } = useEvenement();
  const { initializeSettingsState } = useAvailabilitySettingState();
  const { appointmentProfessional, fetchAppointmentListByProfessionalId } =
    useConsultationState();

  const { isSaveSettings, selectedEvent, settingsLocal, handleIsAddEventModalOpen } = useAgendaState(
    settings,
    evenements,
    appointmentProfessional
  );
  const boutton = (
    <Button
      variant="contained"
      sx={{
        textTransform: "none",
        borderRadius: 2,
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        color: "white",
        fontWeight: "600",
        px: 3,
        py: 1,
        "&:hover": {
          backgroundColor: "rgba(255, 255, 255, 0.3)",
        },
      }}
      onClick={() => handleIsAddEventModalOpen(true)}
    >
      + Ajouter un événement
    </Button>
  );

  useEffect(() => {
    if (professionalId && role === utilisateurs_role_enum.PROFESSIONNEL) {
      getSettings(professionalId);
      fetchAppointmentListByProfessionalId(professionalId);
      if (!currentProfessional) {
        searchProfessionalById({ id: professionalId });
      }
    }
    if (userId) {
      fetchEvenements(userId);
    }
  }, [isSaveSettings]);

  useEffect(() => {
    if (settings) {
      initializeSettingsState(settings, settingsLocal);
    }
  }, [settings, settingsLocal]);

  return (
    <Box>
      <PageTitle
        titre="Agenda"
        description="Planification des évènements et formations programmées"
        logo={logoDass}
        boutton={boutton}
        role={role}
      />
      <AgendaCalendar />
      {selectedEvent && <EventPopover />}
      <AgendaModals />
    </Box>
  );
};

export default Agenda;
