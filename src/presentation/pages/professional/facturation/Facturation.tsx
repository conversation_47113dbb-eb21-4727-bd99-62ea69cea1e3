import { useEffect } from "react";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { Typography } from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";
import {
  useFacturation,
  useFacturationFilters,
} from "@/presentation/hooks/facturation";
import FacturationFilters from "@/presentation/components/common/historiqueCarnetSante/component/FacturationFilters";
import PageTitle from "@/presentation/components/common/PageTitle";

/**
 * Page de gestion des facturations pour les professionnels de santé
 *
 * Cette page permet aux professionnels de visualiser et filtrer leurs facturations.
 * Elle affiche un tableau des facturations avec possibilité de filtrage par patient,
 * ainsi que le calcul automatique du solde total.
 *
 * Fonctionnalités :
 * - Affichage de la liste des facturations du professionnel connecté
 * - Filtrage des facturations par patient
 * - Calcul automatique du solde total (montant - total payé)
 * - Interface responsive avec grille adaptative
 *
 * @returns Composant React de la page de facturation
 *
 * @example
 * ```tsx
 * // Utilisation dans le routeur
 * <Route path="/facturation" component={Facturation} />
 * ```
 */
const Facturation = () => {
  // Récupération des données de facturation depuis le store Redux
  const {
    listeFacturationProfessional,
    getFacturationsByProfessionalId,
    loading,
  } = useFacturation();

  // ID du professionnel connecté récupéré depuis l'état d'authentification
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.utilisateur_id
  );

  // Hook personnalisé pour gérer la logique de filtrage des facturations
  const {
    selectedPatient,
    availablePatients,
    filteredFacturations,
    totalBalance,
    handlePatientChange,
  } = useFacturationFilters(listeFacturationProfessional || []);

  /**
   * Effet pour charger les facturations du professionnel au montage du composant
   * Se déclenche uniquement si l'ID du professionnel est disponible
   */
  useEffect(() => {
    if (professionalId) {
      getFacturationsByProfessionalId(professionalId);
    }
  }, [professionalId, getFacturationsByProfessionalId]);

  return (
    <div className="space-y-6">
      {/* Entete */}
      <PageTitle
        titre="Facturation"
        description="Gestion des facturations"
      />
      {/* En-tête avec solde total et filtres */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
        {/* Affichage du solde total calculé automatiquement */}
        <Typography
          variant="subtitle1"
          color="textSecondary"
          className="font-medium"
        >
          Solde total : {totalBalance.toLocaleString("fr-FR")} MGA
        </Typography>

        {/* Composant de filtrage par patient */}
        <div className="flex justify-end">
          <FacturationFilters
            selectedPatient={selectedPatient}
            availablePatients={availablePatients}
            onSelectedPatientChange={handlePatientChange}
            loading={loading}
          />
        </div>
      </div>

      {/* Tableau des facturations filtrées */}
      <div>
        <ListDataGrid
          data={filteredFacturations}
          type="facturation_professional"
        />
      </div>
    </div>
  );
};

export default Facturation;
