import { useAppSelector } from "@/presentation/hooks/redux";
import {
  Calendar,
  TrendingUp,
  Users,
  Clock,
  Activity,
  CheckCircle,
  AlertCircle,
  BarChart2,
  <PERSON><PERSON>hart,
  Stethoscope,
  UserPlus,
  Edit,
  Filter,
  Plus,
  FileText,
  BarChart3,
  CalendarRangeIcon,
  Settings2Icon,
  CalendarCheck2,
} from "lucide-react";
import {
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Bar,
  BarChart,
} from "recharts";

// Composants
import StatCard from "@/presentation/components/common/StatCard";
import AppointmentList from "@/presentation/components/features/professional/appointment/AppointmentList";
import UpcomingAppointments from "@/presentation/components/features/professional/appointment/UpcomingAppointments";
import PatientDistributionChart from "@/presentation/components/features/professional/appointment/stats/PatientDistributionChart";

// Hooks
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useDashboardData } from "@/presentation/hooks/dashboard/professionnel/use-dashboard-data";
import React, { useEffect, useState, useMemo } from "react";
import { useDarkMode } from "@/presentation/hooks/use-dark-mode";
import { useAvailabilitySettings } from "@/presentation/hooks/agenda/settings";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import PrincipauxMedicaments from "@/presentation/components/features/dash/dashboard/PrincipauxMedicaments";
import PrincipalesPathologies from "@/presentation/components/features/dash/dashboard/PrincipalesPathologies";
import { motion } from "framer-motion";
import AddPatientModal from "@/presentation/components/common/Modal/AddPatientModal";
import useRegister from "@/presentation/hooks/use-register";
import { StatistiqueProfessionnelModal } from "@/presentation/components/common/Modal/StatistiqueProfessionnelModal";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { Menu, MenuItem, Stack, Typography } from "@mui/material";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { AgendaModals } from "@/presentation/components/features/professional/agenda/component/AgendaModals";

const Dashboard = () => {
  const { currentProfessional, searchProfessionalById } =
    useSearchProfessional();
  const { getSettings } = useAvailabilitySettings();
  const isDarkMode = useDarkMode();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const userData = useAppSelector((state) => state.authentification.userData);
  const role = useAppSelector((state) => state.authentification.user?.role);

  // Gestion des modales
  const { handleToggleModal } = useRegister();
  const handleAddPatientOpen = () => {
    handleToggleModal(true);
  };
  const { isOpen } = useRegister();

  // Gestion de modale Agenda
  const {
    handleIsSettingsModalOpen,
    handleIsAddEventModalOpen,
    handleIsAppointmentModalOpen,
  } = useAgendaState();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAddEventClick = () => {
    handleIsAddEventModalOpen(true);
    handleClose();
  };

  const handleSettingsClick = () => {
    handleIsSettingsModalOpen(true);
    handleClose();
  };

  const handleAppointmentClick = () => {
    handleIsAppointmentModalOpen(true);
    handleClose();
  };

  // Utiliser notre hook personnalisé pour récupérer toutes les données du dashboard
  const {
    isStatistiqueModalOpen,
    setIsStatistiqueModalOpen,
    top5Medicaments,
    top5Maladies,
    patientMetrics,
    recentPatient,
    recentUpdatedPatient,
    revenue,
    revenueData,
    patientDistribution,
    appointmentStats,
    todayAppointments,
    totalAppointments,
    completionRate,
    totalConsultations,
    todayConsultations,
  } = useDashboardData(professionalId);

  // Définir les styles du tooltip en fonction du mode
  const tooltipContentStyle = isDarkMode
    ? {
      backgroundColor: "rgba(31, 41, 55, 0.9)", // dark:bg-gray-800
      borderColor: "#374151", // dark:border-gray-700
      color: "#f9fafb", // dark:text-gray-100
    }
    : {
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      borderColor: "#e5e7eb",
      color: "#111827",
    };

  const tooltipItemStyle = isDarkMode
    ? { color: "#f3f4f6" } // dark:text-gray-200
    : { color: "#111827" };

  const tooltipLabelStyle = isDarkMode
    ? { color: "#f3f4f6" } // dark:text-gray-200
    : { color: "#111827" };

  // Couleurs pour le graphique
  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#A569BD",
    "#5DADE2",
    "#45B39D",
    "#F4D03F",
    "#DC7633",
    "#EC7063",
  ];

  // Charger les données supplémentaires une seule fois
  useEffect(() => {
    if (professionalId) {
      // Mettre un drapeau dans sessionStorage pour éviter les appels répétés
      const settingsLoaded = sessionStorage.getItem(
        `settings_loaded_${professionalId}`
      );
      const professionalLoaded = sessionStorage.getItem(
        `professional_loaded_${professionalId}`
      );

      if (!settingsLoaded) {
        getSettings(professionalId);
        sessionStorage.setItem(`settings_loaded_${professionalId}`, "true");
      }
      if (!currentProfessional) {
        searchProfessionalById({ id: professionalId });
      }
      if (!professionalLoaded) {
        sessionStorage.setItem(`professional_loaded_${professionalId}`, "true");
      }
    }
  }, [professionalId]); // Dépendances réduites

  // Transformation des données réelles de médicaments
  const medicamentsData = useMemo(() => {
    if (!top5Medicaments || top5Medicaments.length === 0) {
      return [];
    }

    return top5Medicaments.map((medicament, index) => ({
      name: medicament.nom.replace(",", ""),
      count: medicament.repetitions,
      color: COLORS[index] || COLORS[0],
    }));
  }, [top5Medicaments]);

  // Transformation des données réelles de maladies
  const maladiesData = useMemo(() => {
    if (!top5Maladies || top5Maladies.length === 0) {
      return [];
    }

    return top5Maladies.map((maladie, index) => ({
      name: maladie.maladie.replace(",", ""),
      count: maladie.repetitions,
      color: COLORS[index] || COLORS[0],
    }));
  }, [top5Maladies]);

  const [activePatientTab, setActivePatientTab] = useState("recent"); // 'recent' ou 'updated'

  return (
    <div className="min-h-screen bg-meddoc-light dark:bg-meddoc-fonce transition-colors duration-300">
      <div className="p-3 sm:p-4 lg:p-6 max-w-7xl mx-auto">
        {/* En-tête compact avec couleurs MEDDoC */}
        <header className="mb-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-meddoc-primary/20 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-meddoc-primary to-meddoc-secondary bg-clip-text text-transparent">
                Bonjour,{" "}
                {(() => {
                  // Essayer d'abord currentProfessional, puis userData, puis fallback
                  if (
                    currentProfessional?.titre &&
                    currentProfessional?.nom &&
                    currentProfessional?.prenom
                  ) {
                    return `${currentProfessional.titre} ${currentProfessional.nom} ${currentProfessional.prenom}`;
                  } else if (
                    userData &&
                    "nom" in userData &&
                    "prenom" in userData &&
                    userData.nom &&
                    userData.prenom
                  ) {
                    return `Dr. ${userData.nom} ${userData.prenom}`;
                  } else if (userData && "nom" in userData && userData.nom) {
                    return `Dr. ${userData.nom}`;
                  } else {
                    return "Docteur";
                  }
                })()}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1 text-sm">
                Voici un aperçu de votre activité professionnelle
              </p>
            </div>
            <div className="hidden sm:flex items-center justify-center w-12 h-12 bg-gradient-to-r from-meddoc-primary to-meddoc-secondary rounded-full">
              <Stethoscope className="w-6 h-6 text-white" />
            </div>
          </div>
        </header>

        {/* Cartes de statistiques avec design moderne et couleurs douces */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Patients */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="group bg-white dark:bg-slate-800 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-xl group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 transition-colors">
                <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-2xl font-bold text-slate-800 dark:text-slate-100">
                {patientMetrics?.todayPatients || 0}
              </span>
            </div>
            <h3 className="font-semibold text-slate-700 dark:text-slate-300 mb-2">Patients</h3>
            <div className="space-y-1">
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Aujourd'hui: <span className="font-medium text-slate-800 dark:text-slate-200">{patientMetrics?.todayPatients || 0}</span>
              </div>
              <div className="text-xs text-slate-500 dark:text-slate-500">
                Total: {patientMetrics?.totalPatients || 0}
              </div>
            </div>
          </motion.div>

          {/* Consultations */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="group bg-white dark:bg-slate-800 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-slate-200 dark:border-slate-700 hover:border-emerald-300 dark:hover:border-emerald-600"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-emerald-50 dark:bg-emerald-900/20 rounded-xl group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/30 transition-colors">
                <Activity className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
              </div>
              <span className="text-2xl font-bold text-slate-800 dark:text-slate-100">
                {todayConsultations?.length || 0}
              </span>
            </div>
            <h3 className="font-semibold text-slate-700 dark:text-slate-300 mb-2">Consultations</h3>
            <div className="space-y-1">
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Aujourd'hui: <span className="font-medium text-slate-800 dark:text-slate-200">{todayConsultations?.length || 0}</span>
              </div>
              <div className="text-xs text-slate-500 dark:text-slate-500">
                Total: {totalConsultations || 0}
              </div>
            </div>
          </motion.div>

          {/* Rendez-vous */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="group bg-white dark:bg-slate-800 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-slate-200 dark:border-slate-700 hover:border-amber-300 dark:hover:border-amber-600"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-xl group-hover:bg-amber-100 dark:group-hover:bg-amber-900/30 transition-colors">
                <Calendar className="w-6 h-6 text-amber-600 dark:text-amber-400" />
              </div>
              <span className="text-2xl font-bold text-slate-800 dark:text-slate-100">
                {todayAppointments?.length || 0}
              </span>
            </div>
            <h3 className="font-semibold text-slate-700 dark:text-slate-300 mb-2">Rendez-vous</h3>
            <div className="space-y-1">
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Aujourd'hui: <span className="font-medium text-slate-800 dark:text-slate-200">{todayAppointments?.length || 0}</span>
              </div>
              <div className="text-xs text-slate-500 dark:text-slate-500">
                Total: {totalAppointments || 0}
              </div>
            </div>
          </motion.div>

          {/* Rendez-vous Annulés */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="group bg-white dark:bg-slate-800 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-slate-200 dark:border-slate-700 hover:border-red-300 dark:hover:border-red-600"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-xl group-hover:bg-red-100 dark:group-hover:bg-red-900/30 transition-colors">
                <AlertCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <span className="text-2xl font-bold text-slate-800 dark:text-slate-100">
                {appointmentStats.cancelled}
              </span>
            </div>
            <h3 className="font-semibold text-slate-700 dark:text-slate-300 mb-2">Annulés</h3>
            <div className="text-sm text-slate-600 dark:text-slate-400">
              Total: <span className="font-medium text-slate-800 dark:text-slate-200">{appointmentStats.cancelled}</span>
            </div>
          </motion.div>
        </div>

        {/* Actions Rapides */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 mb-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Actions Rapides
            </h2>
            <Filter size={20} className="text-gray-400" />
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-meddoc-primary to-meddoc-primary/80 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              onClick={handleAddPatientOpen}
              title="Enregistrer un nouveau patient"
            >
              <Plus size={24} />
              <span className="text-sm font-medium">Ajouter Patient</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-green-500 to-green-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              onClick={() => setIsStatistiqueModalOpen(true)}
              title="Consulter les données détaillées"
            >
              <BarChart3 size={24} />
              <span className="text-sm font-medium">Statistiques</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-orange-500 to-orange-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              onClick={handleClick}
              title="Prochaines consultations, évènements et formations programmées"
            >
              <Calendar size={24} />
              <span className="text-sm font-medium">Agenda</span>
            </motion.button>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "right",
              }}
              transformOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
            >
              <MenuItem
                onClick={handleAddEventClick}
                className="flex items-center gap-2"
              >
                <CalendarRangeIcon />
                <Typography>Evenements</Typography>
              </MenuItem>
              {role === utilisateurs_role_enum.PROFESSIONNEL && (
                <>
                  <MenuItem
                    onClick={handleSettingsClick}
                    className="flex items-center gap-2"
                  >
                    <Settings2Icon />
                    <Typography>Planning de rendez-vous</Typography>
                  </MenuItem>
                  <MenuItem
                    onClick={handleAppointmentClick}
                    className="flex items-center gap-2"
                  >
                    <CalendarCheck2 />
                    <Typography>Reserver un rendez-vous</Typography>
                  </MenuItem>
                </>
              )}
            </Menu>
          </div>
        </motion.div>

        {/* Section principale compacte avec couleurs MEDDoC */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Patients récents - 2 colonnes */}
          <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-primary/20 dark:border-gray-700 overflow-hidden">
            <div className="px-4 py-3 bg-gradient-to-r from-meddoc-primary to-meddoc-fonce dark:from-meddoc-primary/20 dark:to-meddoc-primary/10 border-b border-meddoc-primary/20 dark:border-gray-600">
              <h2 className="text-lg font-semibold text-white dark:text-white flex items-center">
                <Users className="w-4 h-4 mr-2 text-white" />
                Liste des patients récents
              </h2>
            </div>
            {/* Onglets pour filtrer les patients */}
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                className={`flex-1 py-3 px-4 text-center font-medium text-sm border-b-2 ${activePatientTab === "recent" ? "border-meddoc-primary text-meddoc-primary" : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`}
                onClick={() => setActivePatientTab("recent")}
              >
                <div className="flex items-center justify-center">
                  <UserPlus className="w-4 h-4 mr-2" />
                  Créés récemment
                </div>
              </button>
              <button
                className={`flex-1 py-3 px-4 text-center font-medium text-sm border-b-2 ${activePatientTab === "updated" ? "border-meddoc-primary text-meddoc-primary" : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`}
                onClick={() => setActivePatientTab("updated")}
              >
                <div className="flex items-center justify-center">
                  <Edit className="w-4 h-4 mr-2" />
                  Mis à jour récemment
                </div>
              </button>
            </div>
            <div className="p-4">
              {useMemo(() => {
                if (activePatientTab === "recent") {
                  return (
                    <ListDataGrid data={recentPatient} type="recent_patient" />
                  );
                } else if (activePatientTab === "updated") {
                  return (
                    <ListDataGrid
                      data={recentUpdatedPatient}
                      type="recent_updated_patient"
                    />
                  );
                }
              }, [activePatientTab, recentPatient, recentUpdatedPatient])}
            </div>
          </div>

          {/* Revenus - 1 colonne */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-secondary/20 dark:border-gray-700 overflow-hidden">
            <div className="px-4 py-3 bg-gradient-to-r from-meddoc-primary to-meddoc-fonce p-4">
              <h2 className="text-lg font-semibold text-white dark:text-white flex items-center">
                <Calendar className="w-4 h-4 mr-2 text-white" />
                Rendez-vous Aujourd'hui
                <span className="ml-auto text-xs text-white dark:text-gray-400">
                  {new Date().toLocaleDateString("fr-FR", {
                    weekday: "long",
                    day: "numeric",
                    month: "long",
                  })}
                </span>
              </h2>
            </div>
            <div className="p-4">
              <AppointmentList appointments={todayAppointments} />
            </div>
          </div>
        </div>
        {/* Rendez-vous du jour compact */}
        <section className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-orange/20 dark:border-gray-700 mb-6 overflow-hidden">
          <div className="px-4 py-3 bg-gradient-to-r from-meddoc-orange/10 to-meddoc-orange/5 dark:from-meddoc-orange/20 dark:to-meddoc-orange/10 border-b border-meddoc-orange/20 dark:border-gray-600">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <TrendingUp className="w-4 h-4 mr-2 text-meddoc-secondary" />
              Revenus
            </h2>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="bg-gradient-to-r from-meddoc-primary via-blue-500 to-cyan-600 rounded-lg p-3 text-white">
                <div className="text-xs opacity-90 mb-1">Aujourd'hui</div>
                <div className="text-xl font-bold">
                  {revenue?.today || 0} MGA
                </div>
              </div>
              <div className="bg-gradient-to-r from-meddoc-secondary via-green-600 to-emerald-700 rounded-lg p-3 text-white">
                <div className="text-xs opacity-90 mb-1">Ce mois</div>
                <div className="text-xl font-bold">{revenue.monthly} MGA</div>
              </div>
              <div className="bg-gradient-to-r from-meddoc-orange via-orange-600 to-red-500 rounded-lg p-3 text-white">
                <div className="text-xs opacity-90 mb-1">Cette année</div>
                <div className="text-xl font-bold">{revenue.yearly} MGA</div>
              </div>
            </div>
          </div>
        </section>

        {/* Graphiques compacts avec couleurs MEDDoC */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Graphique de revenus */}
          <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-primary/20 dark:border-gray-700 overflow-hidden">
            <div className="px-4 py-3 bg-gradient-to-r from-meddoc-primary/10 to-meddoc-primary/5 dark:from-meddoc-primary/20 dark:to-meddoc-primary/10 border-b border-meddoc-primary/20 dark:border-gray-600">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                  <BarChart2 className="w-4 h-4 mr-2 text-meddoc-primary" />
                  Évolution des Revenus
                </h2>
                <span className="text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 px-2 py-1 rounded-full">
                  6 derniers mois
                </span>
              </div>
            </div>
            <div className="p-4">
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={revenueData}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke={isDarkMode ? "#374151" : "#f3f4f6"}
                    />
                    <XAxis
                      dataKey="name"
                      stroke={isDarkMode ? "#9ca3af" : "#6b7280"}
                      fontSize={11}
                    />
                    <YAxis
                      stroke={isDarkMode ? "#9ca3af" : "#6b7280"}
                      fontSize={11}
                    />
                    <RechartsTooltip
                      contentStyle={tooltipContentStyle}
                      itemStyle={tooltipItemStyle}
                      labelStyle={tooltipLabelStyle}
                      wrapperStyle={{ outline: "none" }}
                    />
                    <Bar
                      dataKey="revenue"
                      fill={isDarkMode ? "#27aae1" : "#27aae1"}
                      radius={[3, 3, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Distribution des patients */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-secondary/20 dark:border-gray-700 overflow-hidden">
            <div className="px-4 py-3 bg-gradient-to-r from-meddoc-secondary/10 to-meddoc-secondary/5 dark:from-meddoc-secondary/20 dark:to-meddoc-secondary/10 border-b border-meddoc-secondary/20 dark:border-gray-600">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <PieChart className="w-4 h-4 mr-2 text-meddoc-secondary" />
                Distribution Patients
              </h2>
            </div>
            <div className="p-4">
              <div className="h-64 flex items-center justify-center">
                <PatientDistributionChart data={patientDistribution} />
              </div>
            </div>
          </div>
        </div>

        {/* Statistiques compactes avec couleurs MEDDoC */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Statistiques des rendez-vous */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-fonce/20 dark:border-gray-700 overflow-hidden">
            <div className="px-4 py-3 bg-gradient-to-r from-meddoc-fonce/10 to-meddoc-fonce/5 dark:from-meddoc-fonce/20 dark:to-meddoc-fonce/10 border-b border-meddoc-fonce/20 dark:border-gray-600">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <Activity className="w-4 h-4 mr-2 text-meddoc-fonce" />
                Statistiques des Rendez-vous
              </h2>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gradient-to-br from-meddoc-secondary/10 to-meddoc-secondary/5 rounded-lg p-3 text-center border border-meddoc-secondary/20">
                  <CheckCircle className="h-6 w-6 text-meddoc-secondary mx-auto mb-1" />
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {appointmentStats.completed}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Complétés
                  </p>
                </div>
                <div className="bg-gradient-to-br from-meddoc-primary/10 to-meddoc-primary/5 rounded-lg p-3 text-center border border-meddoc-primary/20">
                  <Clock className="h-6 w-6 text-meddoc-primary mx-auto mb-1" />
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {appointmentStats.upcoming}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    À venir
                  </p>
                </div>
                <div className="bg-gradient-to-br from-meddoc-orange/10 to-meddoc-orange/5 rounded-lg p-3 text-center border border-meddoc-orange/20">
                  <AlertCircle className="h-6 w-6 text-meddoc-orange mx-auto mb-1" />
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {appointmentStats.cancelled}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Annulés
                  </p>
                </div>
                <div className="bg-gradient-to-br from-meddoc-fonce/10 to-meddoc-fonce/5 rounded-lg p-3 text-center border border-meddoc-fonce/20">
                  <Calendar className="h-6 w-6 text-meddoc-fonce mx-auto mb-1" />
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {appointmentStats.totalThisMonth}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Total du mois
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Prochains rendez-vous */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-primary/20 dark:border-gray-700 overflow-hidden">
            <div className="px-4 py-3 bg-gradient-to-r from-meddoc-primary/10 to-meddoc-primary/5 dark:from-meddoc-primary/20 dark:to-meddoc-primary/10 border-b border-meddoc-primary/20 dark:border-gray-600">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <Clock className="w-4 h-4 mr-2 text-meddoc-primary" />
                Prochains Rendez-vous
              </h2>
            </div>
            <div className="p-4">
              <UpcomingAppointments
                appointments={todayAppointments.slice(0, 3)}
              />
            </div>
          </div>
          {/* Medicament */}
          <PrincipauxMedicaments medicamentsData={medicamentsData} />

          {/* Principales pathologies */}
          <PrincipalesPathologies pathologiesData={maladiesData} />
        </div>
      </div>

      {/* MODALE ACTION RAPIDE */}
      {isOpen && <AddPatientModal />}
      {/* MODALE STATISTIQUE */}
      {isStatistiqueModalOpen && (
        <StatistiqueProfessionnelModal
          isModalOpen={isStatistiqueModalOpen}
          handleCloseModal={() => setIsStatistiqueModalOpen(false)}
          pathologiesData={maladiesData}
          medicamentsData={medicamentsData}
          data={patientDistribution}
          appointmentStats={appointmentStats}
        />
      )}
      <AgendaModals />
    </div>
  );
};

export default Dashboard;
