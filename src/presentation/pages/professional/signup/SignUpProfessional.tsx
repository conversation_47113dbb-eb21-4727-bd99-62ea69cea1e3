import { professionnels_categories_enum } from "@/domain/models/enums";
import CabinetMedical from "./CabinetMedical/CabinetMedical";
import { useNavigate, useParams } from "react-router-dom";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import BMH from "./BMH/BMH";
import CentreImagerieMedical from "./CentreImagerieMedical/CentreImagerieMedical";
import EtablissementSantePrive from "./EtablissementSantePrive/EtablissementSantePrive";
import LaboratoireAnalyseMedical from "./LaboratoireAnalyseMedical/LaboratoireAnalyseMedical";
import useVerifyProfessionalToken from "@/presentation/hooks/professional/use-verify-professional-inscription-token";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import { useEffect } from "react";

const SignUpProfessional = () => {
  const params = useParams<{ token: string }>();
  const navigate = useNavigate();

  const { professionalInvitationData, isLoading, isTokenValid, error } =
    useVerifyProfessionalToken(params?.token);

  useEffect(() => {
    if (!isLoading && !isTokenValid) {
      navigate(PublicRoutesNavigation.MAIN_PAGE);
    }
  }, [isLoading, isTokenValid, navigate]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div>
        <p>Une erreur s'est produite</p>
        <p className="text-red-500">{error}</p>
        <a href={PublicRoutesNavigation.MAIN_PAGE}>Retourner a l'acceuil</a>
      </div>
    );
  }

  if (professionalInvitationData) {
    const type = professionalInvitationData.demande_adhesion
      .type_etablissement as professionnels_categories_enum;

    switch (type) {
      case professionnels_categories_enum.BMH:
        return <BMH />;

      case professionnels_categories_enum.CABINET_MEDICAL:
        return (
          <CabinetMedical
            professionalInvitationId={professionalInvitationData.id}
          />
        );

      case professionnels_categories_enum.CENTRE_IMAGERIE:
        return <CentreImagerieMedical />;

      case professionnels_categories_enum.ETABLISSEMENT_SANTE_PRIVE:
        return <EtablissementSantePrive />;

      case professionnels_categories_enum.LABORATOIRE_ANALYSES:
        return <LaboratoireAnalyseMedical />;

      default:
        navigate(PublicRoutesNavigation.MAIN_PAGE);
        return null;
    }
  }

  return null;
};

export default SignUpProfessional;
