import { Box, Container, Paper } from "@mui/material";
import { styled } from "@mui/material/styles";
import { memo, useMemo } from "react";
import CabinetMedicalPreview from "@/presentation/components/preview/CabinetMedicalPreview";
import FormHeader from "@/presentation/components/professional/signupProfessional/common/FormHeader";
import ProfilePhotoUploader from "@/presentation/components/professional/signupProfessional/common/ProfilePhotoUploader";
import FormFooter from "@/presentation/components/professional/signupProfessional/common/FormFooter";
import ProfessionalInfoSection from "@/presentation/components/professional/signupProfessional/sections/ProfessionalInfoSection";
import LocationSection from "@/presentation/components/professional/signupProfessional/sections/LocationSection";
import ContactSection from "@/presentation/components/professional/signupProfessional/sections/ContactSection";
import PresentationSection from "@/presentation/components/professional/signupProfessional/sections/PresentationSection";
import AuthenticationSection from "@/presentation/components/professional/signupProfessional/sections/AuthenticationSection";
import SpecialitiesSection from "@/presentation/components/professional/signupProfessional/sections/SpecialitiesSection";
import ServicesSection from "@/presentation/components/professional/signupProfessional/sections/ServicesSection";
import { FormProvider } from "react-hook-form";
import useCabinetMedicalForm from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";
import { UI_CONFIG } from "@/shared/constants/cabinetMedicalConfig";

// Déplacer le composant stylisé en dehors du composant principal pour éviter sa recréation à chaque rendu
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  backgroundColor: "white",
  borderRadius: theme.spacing(1),
  width: "100%",
  boxSizing: "border-box",
  [theme.breakpoints.down("sm")]: {
    padding: theme.spacing(2),
  },
}));

interface CabinetMedicalProps {
  professionalInvitationId: number;
}

const CabinetMedical = ({ professionalInvitationId }: CabinetMedicalProps) => {
  const { onSubmit, methods } = useCabinetMedicalForm(professionalInvitationId);

  // Utiliser les styles depuis le fichier de configuration
  const boxStyles = useMemo(() => UI_CONFIG.BOX_STYLES, []);

  return (
    <FormProvider {...methods}>
      <Container maxWidth="md">
        <Box sx={boxStyles}>
          <FormHeader />
          <StyledPaper elevation={3}>
            <form onSubmit={onSubmit}>
              <ProfilePhotoUploader />
              <ProfessionalInfoSection />
              <LocationSection />
              <ContactSection />
              <PresentationSection />
              <AuthenticationSection />
              <SpecialitiesSection />
              <ServicesSection />
              <FormFooter />
            </form>
          </StyledPaper>
        </Box>
      </Container>

      <CabinetMedicalPreview />
    </FormProvider>
  );
};

// Utiliser memo pour éviter les re-rendus inutiles du composant
export default memo(CabinetMedical);
