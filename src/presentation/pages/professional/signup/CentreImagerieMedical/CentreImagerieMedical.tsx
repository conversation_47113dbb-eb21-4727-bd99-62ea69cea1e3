import Button from "@/presentation/components/common/Button/Button";
import {
  Box,
  Container,
  Paper,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
  IconButton,
  Avatar,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { useState, useRef } from "react";
import { Camera, Upload } from "lucide-react";

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  backgroundColor: "white",
  borderRadius: theme.spacing(1),
  width: "100%",
  boxSizing: "border-box",
  [theme.breakpoints.down("sm")]: {
    padding: theme.spacing(2),
  },
}));

const CentreImagerieMedical = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [error, setError] = useState<string>("");
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    // Logique de soumission du formulaire à implémenter
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setProfileImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <Container maxWidth="md">
      <Box
        sx={{
          mt: { xs: 1, sm: 2 },
          mb: { xs: 3, sm: 4 },
          px: { xs: 1, sm: 0 },
          width: "100%",
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          sx={{
            mb: { xs: 2, sm: 3, md: 4 },
            fontWeight: 600,
            fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2.125rem" },
            color: "#07294A",
            letterSpacing: "-0.01em",
            textAlign: { xs: "center", sm: "left" },
          }}
        >
          Inscription Centre d'imagerie Médical
        </Typography>

        <Typography
          variant="body1"
          sx={{
            mb: { xs: 3, sm: 4 },
            fontSize: { xs: "0.875rem", sm: "1rem", md: "1.125rem" },
            lineHeight: 1.6,
            color: "#07294A",
            fontWeight: 400,
            textAlign: { xs: "center", sm: "left" },
          }}
        >
          Veuillez remplir ce formulaire pour inscrire votre centre d'imagerie
          médical sur MEDDoC Pro. Tous les champs marqués d'un astérisque (*)
          sont obligatoires.
        </Typography>

        <StyledPaper elevation={3}>
          <form onSubmit={handleSubmit}>
            {/* Photo de profil */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                mb: 3,
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  mb: 2,
                  textAlign: "center",
                  color: "#07294A",
                  fontWeight: 500,
                  fontSize: { xs: "0.9rem", sm: "1rem" },
                }}
              >
                Photo de profil (optionnelle)
              </Typography>
              <Box sx={{ position: "relative" }}>
                <Avatar
                  src={profileImage || undefined}
                  sx={{
                    width: { xs: 100, sm: 120 },
                    height: { xs: 100, sm: 120 },
                    border: "2px solid #e0e0e0",
                  }}
                />
                <IconButton
                  onClick={triggerFileInput}
                  sx={{
                    position: "absolute",
                    bottom: 0,
                    right: 0,
                    backgroundColor: theme.palette.primary.main,
                    color: "white",
                    "&:hover": {
                      backgroundColor: theme.palette.primary.dark,
                    },
                    width: 36,
                    height: 36,
                  }}
                >
                  <Camera size={18} />
                </IconButton>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleImageUpload}
                  accept="image/*"
                  style={{ display: "none" }}
                />
              </Box>
            </Box>

            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: { xs: "1fr", sm: "1fr 1fr" },
                gap: { xs: 2, md: 3 },
                width: "100%",
              }}
            >
              <Box sx={{ gridColumn: "1" }}>
                <TextField
                  fullWidth
                  label="Nom etablissement*"
                  variant="outlined"
                  error={error !== ""}
                  helperText={error}
                  size={isMobile ? "small" : "medium"}
                  margin={isMobile ? "dense" : "normal"}
                />
              </Box>
              <Box sx={{ gridColumn: { xs: "1", sm: "2" } }}>
                <TextField
                  fullWidth
                  label="Nom du responsable*"
                  variant="outlined"
                  error={error !== ""}
                  helperText={error}
                  size={isMobile ? "small" : "medium"}
                  margin={isMobile ? "dense" : "normal"}
                />
              </Box>
              <Box sx={{ gridColumn: "1" }}>
                <TextField
                  fullWidth
                  label="Prénom du responsable*"
                  variant="outlined"
                  error={error !== ""}
                  helperText={error}
                  size={isMobile ? "small" : "medium"}
                  margin={isMobile ? "dense" : "normal"}
                />
              </Box>
              <Box sx={{ gridColumn: { xs: "1", sm: "2" } }}>
                <TextField
                  fullWidth
                  label="Specialite ou domaine"
                  variant="outlined"
                  error={error !== ""}
                  helperText={error}
                  size={isMobile ? "small" : "medium"}
                  margin={isMobile ? "dense" : "normal"}
                />
              </Box>
              <Box sx={{ gridColumn: "1" }}>
                <TextField
                  fullWidth
                  label="Raison sociale"
                  variant="outlined"
                  error={error !== ""}
                  helperText={error}
                  size={isMobile ? "small" : "medium"}
                  margin={isMobile ? "dense" : "normal"}
                />
              </Box>
              <Box sx={{ gridColumn: { xs: "1", sm: "2" } }}>
                <TextField
                  fullWidth
                  label="NIF"
                  variant="outlined"
                  error={error !== ""}
                  helperText={error}
                  size={isMobile ? "small" : "medium"}
                  margin={isMobile ? "dense" : "normal"}
                />
              </Box>
              <Box sx={{ gridColumn: "1" }}>
                <TextField
                  fullWidth
                  label="STAT"
                  variant="outlined"
                  error={error !== ""}
                  helperText={error}
                  size={isMobile ? "small" : "medium"}
                  margin={isMobile ? "dense" : "normal"}
                />
              </Box>
              <Box sx={{ gridColumn: { xs: "1", sm: "1 / span 2" }, mt: 2 }}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  sx={{
                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                    textAlign: { xs: "center", sm: "left" },
                  }}
                >
                  MEDDoC a besoin de vos informations professionnelles pour
                  valider votre compte et vous offrir les services adaptés à
                  votre cabinet médical. Consultez notre politique de
                  confidentialité pour en savoir plus sur nos pratiques en
                  matière de protection des données.
                </Typography>
              </Box>
              <Box sx={{ gridColumn: { xs: "1", sm: "1 / span 2" }, mt: 3 }}>
                <Button
                  className="w-full"
                  style={{ padding: isMobile ? "0.75rem 0" : "1rem 0" }}
                >
                  Enregistrer mon centre d'imagerie
                </Button>
              </Box>
            </Box>
          </form>
        </StyledPaper>
      </Box>
    </Container>
  );
};

export default CentreImagerieMedical;
