import { useRef, Suspense, lazy } from "react";
import { useParams } from "react-router-dom";
import { motion } from "framer-motion";
import UnathenticatedLayout from "@/presentation/components/layouts/UnauthenticatedLayout";
import { ProfileHeader } from "@/presentation/components/features/professional/profile/ProfileHeader";
import { NavigationTabs } from "@/presentation/components/features/professional/profile/NavigationTabs";
import { ExpertiseSection } from "@/presentation/components/features/professional/profile/sections/ExpertiseSection";
import { Summary } from "@/presentation/components/features/professional/profile/Summary";
import { useProfileNavigation } from "@/presentation/hooks/useProfileNavigation";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import useProfileData from "@/presentation/hooks/useProfileData";
import { professionnels_titre_enum } from "@/domain/models/enums/professionnelsTitreEnum.ts";
import PageNotFound from "@/presentation/components/common/NotFoundPage";

/**
 * Chargement paresseux des composants lourds pour optimiser les performances
 *
 * Ces composants sont chargés de manière asynchrone uniquement lorsqu'ils sont nécessaires,
 * ce qui améliore le temps de chargement initial de la page et réduit la taille du bundle principal.
 * Chaque import utilise la syntaxe .then() pour extraire le composant nommé du module.
 */

/** Composant de section cartographique avec géolocalisation du professionnel */
const MapSection = lazy(() =>
  import(
    "@/presentation/components/features/professional/profile/sections/MapSection"
  ).then((module) => ({ default: module.MapSection }))
);

/** Composant de présentation détaillée du professionnel (biographie, spécialités) */
const PresentationSection = lazy(() =>
  import(
    "@/presentation/components/features/professional/profile/sections/PresentationSection"
  ).then((module) => ({ default: module.PresentationSection }))
);

/** Composant d'affichage des horaires hebdomadaires du professionnel */
const ScheduleSection = lazy(() =>
  import(
    "@/presentation/components/features/professional/profile/sections/ScheduleSection"
  ).then((module) => ({ default: module.ScheduleSection }))
);

/** Composant d'affichage des diplômes, certifications et qualifications */
const CredentialsSection = lazy(() =>
  import(
    "@/presentation/components/features/professional/profile/sections/CredentialsSection"
  ).then((module) => ({ default: module.CredentialsSection }))
);

/**
 * Composant principal de la page de profil professionnel
 *
 * @description Ce composant affiche le profil complet d'un professionnel de santé avec :
 * - En-tête avec informations principales et photo
 * - Navigation par onglets avec défilement automatique
 * - Sections détaillées (expertise, diplômes, carte, présentation, horaires)
 * - Résumé latéral avec informations de contact
 * - Animations fluides et design responsive
 * - Chargement paresseux des composants lourds pour optimiser les performances
 *
 * @architecture
 * - Suit le pattern Domain-Driven Design avec séparation claire des responsabilités
 * - Utilise des hooks personnalisés pour la logique métier (useProfileData, useProfileNavigation)
 * - Composants de présentation purs recevant les données via props
 * - Gestion d'état centralisée dans les hooks
 *
 * @performance
 * - Lazy loading des sections non critiques
 * - Animations optimisées avec Framer Motion
 * - Navigation sticky avec détection automatique de section visible
 * - Fallbacks de chargement avec squelettes animés
 *
 * @exemple
 * Route: /professionnel/dr-martin-dupont-123
 * Affiche le profil du professionnel avec l'ID 123
 *
 * @returns {JSX.Element} Page de profil professionnel complète
 */
const Profile = () => {
  // Récupération du slug depuis l'URL (format: nom-prenom-id)
  const { slug } = useParams();

  /**
   * Extraction de l'ID professionnel depuis le slug
   *
   * Le slug suit le format "dr-martin-dupont-123" où 123 est l'ID
   * Cette approche permet des URLs SEO-friendly tout en conservant l'identifiant unique
   */
  const slugArray = slug?.split("-") || [];
  const id = slugArray[slugArray.length - 1]; // ID: dernière cellule du slug

  /**
   * Hook personnalisé pour récupérer les données complètes du profil
   *
   * Utilise l'architecture Domain-Driven Design avec :
   * - SearchProfessionalByIdUsecase pour la logique métier
   * - Repositories pour l'accès aux données
   * - Services pour les traitements spécialisés (disponibilités, photos, etc.)
   *
   * @returns {Object} profileData - Données complètes du professionnel (ProfessionalCardDTO)
   * @returns {boolean} loading - État de chargement
   * @returns {string} error - Message d'erreur éventuel
   */
  const { profileData, loading, error } = useProfileData({ id: parseInt(id) });

  /**
   * Références pour la navigation sticky et le résumé
   *
   * stickyRef: Référence vers la barre de navigation pour calculer les offsets de défilement
   * stickyResumeRef: Référence vers le résumé latéral pour les interactions
   */
  const stickyRef = useRef<HTMLDivElement>(null);
  const stickyResumeRef = useRef<HTMLDivElement>(null);

  /**
   * Hook personnalisé pour la navigation intelligente entre sections
   *
   * Fonctionnalités :
   * - Détection automatique de la section visible lors du défilement
   * - Mise à jour de l'onglet actif en temps réel
   * - Défilement fluide vers les sections lors du clic sur les onglets
   * - Gestion des offsets pour la navigation sticky
   * - Optimisation avec throttling pour les performances
   *
   * @param {React.RefObject<HTMLDivElement>} stickyRef - Référence vers la navigation
   * @returns {Object} activeTab - Onglet actuellement actif
   * @returns {Function} handleTabClick - Gestionnaire de clic sur les onglets
   */
  const { activeTab, handleTabClick } = useProfileNavigation(stickyRef);

  /**
   * Gestion de l'état de chargement
   *
   * Affiche un spinner de chargement centré pendant la récupération des données.
   * Le composant LoadingSpinner utilise les animations CSS pour une expérience fluide.
   */
  if (loading) {
    return <LoadingSpinner />;
  }
  if (!profileData && !loading) {
    return <PageNotFound />;
  }

  /**
   * Gestion des erreurs de chargement
   *
   * Affiche un message d'erreur centré avec design cohérent si :
   * - Le professionnel n'existe pas
   * - Erreur de réseau ou de serveur
   * - Données corrompues ou invalides
   *
   * Utilise le layout non-authentifié pour maintenir la cohérence visuelle.
   */
  if (error) {
    return (
      <UnathenticatedLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Erreur</h2>
            <p className="text-gray-600">{error}</p>
          </div>
        </div>
      </UnathenticatedLayout>
    );
  }

  return (
    <UnathenticatedLayout>
      {/*
        Section Hero avec design premium inspiré de la page d'accueil

        Cette section utilise un dégradé de couleurs de la marque MEDDoC avec des éléments
        décoratifs subtils pour créer une ambiance professionnelle et moderne.
        Les effets de flou et la superposition créent de la profondeur visuelle.
      */}
      <div className="relative overflow-hidden bg-gradient-to-br from-meddoc-fonce via-meddoc-fonce/90 to-meddoc-fonce">
        {/*
          Éléments décoratifs d'arrière-plan optimisés pour les performances

          - Pattern de points subtil en SVG encodé en base64 pour éviter les requêtes réseau
          - Formes floues positionnées stratégiquement pour créer de la profondeur
          - Opacité réduite pour ne pas interférer avec le contenu principal
        */}
        <div className="absolute inset-0 z-0">
          {/* Pattern de texture subtile */}
          <div className="absolute inset-0 opacity-5 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJ3aGl0ZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSIyIiBjeT0iMiIgcj0iMiIvPjwvZz48L3N2Zz4=')]"></div>
          {/* Forme décorative primaire - coin supérieur gauche */}
          <div className="absolute top-[-10%] left-[-5%] w-[40%] h-[40%] rounded-full bg-meddoc-primary/20 blur-[120px]"></div>
          {/* Forme décorative secondaire - coin inférieur droit */}
          <div className="absolute bottom-[-15%] right-[-10%] w-[50%] h-[50%] rounded-full bg-meddoc-secondary/15 blur-[150px]"></div>
        </div>

        {/*
          En-tête du profil avec informations principales

          Composant réutilisable qui affiche :
          - Photo du professionnel
          - Nom, titre et spécialités
          - Informations de contact principales
          - Évaluations et avis
        */}
        <ProfileHeader professional={profileData} />
      </div>

      {/*
        Navigation sticky avec onglets

        Barre de navigation qui reste fixée en haut lors du défilement.
        Utilise un effet de flou d'arrière-plan (backdrop-blur) pour maintenir
        la lisibilité tout en conservant une transparence élégante.

        Le z-index élevé (z-50) assure qu'elle reste au-dessus de tout le contenu.
      */}
      <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <NavigationTabs
          activeTab={activeTab}
          onTabClick={handleTabClick}
          stickyRef={stickyRef}
        />
      </div>

      {/*
        Contenu principal avec animations d'entrée fluides

        Utilise Framer Motion pour créer une animation d'apparition progressive :
        - Fade-in (opacity 0 → 1) pour l'apparition en douceur
        - Slide-up (y: 20 → 0) pour un effet de montée élégant
        - Durée de 0.6s pour un timing naturel et professionnel

        Le fond utilise un dégradé subtil pour créer de la profondeur visuelle
        sans distraire de l'information principale.
      */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative bg-gradient-to-br from-slate-50 to-slate-100 min-h-screen"
      >
        {/*
          Éléments décoratifs de fond pour enrichir le design

          Formes géométriques subtiles positionnées aux coins pour :
          - Ajouter de l'intérêt visuel sans surcharger
          - Maintenir la cohérence avec le design system MEDDoC
          - Créer une hiérarchie visuelle claire (z-0 = arrière-plan)
        */}
        <div className="absolute top-10 right-10 w-32 h-32 rounded-full bg-meddoc-primary/5 z-0"></div>
        <div className="absolute bottom-10 left-10 w-40 h-40 rounded-full bg-meddoc-secondary/5 z-0"></div>

        {/*
          Grille responsive principale avec layout adaptatif

          Structure :
          - Mobile (grid-cols-1) : Une seule colonne, sections empilées verticalement
          - Desktop (lg:grid-cols-3) : Colonne principale (2/3) + sidebar (1/3)
          - Espacement optimisé avec gap-8 pour une lecture confortable
          - Conteneur centré avec max-width et padding responsive
          - z-10 pour s'assurer que le contenu reste au-dessus des éléments décoratifs
        */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-12 pt-8 relative z-10">
          {/*
            Colonne principale avec contenu détaillé (2/3 de la largeur sur desktop)

            Animation décalée (delay: 0.2s) pour créer un effet de cascade visuelle.
            Slide-in depuis la gauche (x: -20 → 0) pour guider l'œil naturellement.
            Espacement vertical (space-y-8) entre les sections pour une lecture fluide.
          */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-2 space-y-8"
          >
            {/*
              Section d'expertise - Toujours visible en premier

              Affiche les spécialités, domaines d'expertise et compétences clés.
              Chargement immédiat car information critique pour l'utilisateur.
            */}
            <ExpertiseSection professional={profileData} />

            {/*
              Section des diplômes et certifications - Chargement paresseux

              Utilise React.Suspense pour le lazy loading avec fallback animé.
              Le skeleton loader maintient la structure visuelle pendant le chargement.
              Hauteur fixe (h-32) pour éviter les décalages de layout (CLS).
            */}
            <Suspense
              fallback={
                <div className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 animate-pulse h-32"></div>
              }
            >
              <CredentialsSection professional={profileData} />
            </Suspense>

            {/*
              Section cartographique - Chargement paresseux

              Composant lourd contenant :
              - Carte interactive avec géolocalisation
              - Calculs de distance et itinéraires
              - Intégrations avec services de cartographie

              Fallback plus haut (h-64) car contenu plus volumineux.
              Chargé en différé pour optimiser les performances initiales.
            */}
            <Suspense
              fallback={
                <div className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 animate-pulse h-64"></div>
              }
            >
              <MapSection professional={profileData} />
            </Suspense>

            {/*
              Section de présentation détaillée - Chargement paresseux

              Contient la biographie, la philosophie de soins, les approches thérapeutiques.
              Information importante mais non critique pour la première impression.
              Hauteur moyenne (h-48) adaptée au contenu textuel.
            */}
            <Suspense
              fallback={
                <div className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 animate-pulse h-48"></div>
              }
            >
              <PresentationSection professional={profileData} />
            </Suspense>

            {/*
              Section des horaires hebdomadaires - Chargement paresseux

              Affiche les créneaux de disponibilité par jour de la semaine.
              Utilise les données horaire_hebdomadaire du profil avec gestion des valeurs nulles.
              Hauteur compacte (h-40) pour un affichage tabulaire des horaires.
            */}
            <Suspense
              fallback={
                <div className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 animate-pulse h-40"></div>
              }
            >
              <ScheduleSection
                horaire_hebdomadaire={profileData?.horaire_hebdomadaire}
              />
            </Suspense>
          </motion.div>

          {/*
            Sidebar avec résumé et informations de contact (1/3 de la largeur sur desktop)

            Animation décalée (delay: 0.4s) pour créer un effet de cascade après le contenu principal.
            Slide-in depuis la droite (x: 20 → 0) pour équilibrer visuellement avec la colonne principale.

            Sur mobile, cette section apparaît en bas de page dans l'ordre naturel de lecture.
          */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="lg:col-span-1"
          >
            {/*
              Composant de résumé avec informations essentielles

              Contient :
              - Informations de contact (téléphone, email, adresse)
              - Boutons d'action (prendre rendez-vous, contacter)
              - Évaluations et avis récents
              - Informations pratiques (tarifs, moyens de paiement)

              Le composant peut devenir sticky sur desktop pour rester visible
              lors du défilement des sections principales.
            */}
            <Summary
              professional={profileData}
              stickyResumeRef={stickyResumeRef}
            />
          </motion.div>
        </div>
      </motion.div>
    </UnathenticatedLayout>
  );
};

/**
 * Export par défaut du composant Profile
 *
 * Ce composant représente une page complète dans l'architecture de l'application
 * et suit les conventions de nommage React pour les composants de page.
 */
export default Profile;
