import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { ProfessionnelPatientDTO } from "@/domain/DTOS";
import { ProfessionalRoutesNavigations } from "@/shared/constants/AppRoutesNavigation";
import { useNavigate } from "react-router-dom";

export const RecentUpdatedPatientColumns = (): GridColDef[] => {
    const navigate = useNavigate();

    return [
        {
            field: "fullName",
            headerName: "Nom et Prénom",
            flex: 1,
            renderCell: (params: GridRenderCellParams<ProfessionnelPatientDTO>) => {
                const patient = params.row.patient;
                if (!patient) return "";

                const fullName = `${patient.nom || ""} ${patient.prenom || ""}`.trim();

                const patientId = patient.id;

                return (
                    <a
                        className="text-blue-600 hover:underline cursor-pointer"
                        onClick={() => {
                            console.log("Ligne cliquée :", params.row.patient.utilisateur_id);
                            navigate(
                                `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_PAGE.split("/:id")[0]}/${params.row.patient.utilisateur_id}`
                            );
                        }}
                    >
                        {fullName}
                    </a>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "age",
            headerName: "Âge",
            flex: 1,
            renderCell: (params: GridRenderCellParams<ProfessionnelPatientDTO>) => {
                const dob = params.row.patient?.date_naissance;
                if (!dob) return "";

                const birthDate = new Date(dob);
                const today = new Date();
                let age = today.getFullYear() - birthDate.getFullYear();
                const m = today.getMonth() - birthDate.getMonth();

                if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }

                return age + " ans";
            },
            headerClassName: "font-semibold",
        },
        {
            field: "updated_date",
            headerName: "Mis à jour le",
            flex: 1,
            renderCell: (params: GridRenderCellParams<ProfessionnelPatientDTO>) =>
                params.row.updated_date
                    ? new Date(params.row.updated_date).toLocaleString('fr-FR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    })
                    : "",
            headerClassName: "font-semibold",
        },
    ];
};
