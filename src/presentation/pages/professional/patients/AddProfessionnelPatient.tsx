import FormField from "@/presentation/components/common/ui/FormField";
import RegisterStep1 from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1";
import { PatientFormData } from "@/shared/schemas/PatientShema";
import { Phone } from "lucide-react";
import {
  Control,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";

interface AddProfessionnelPatientProps {
  control: Control<PatientFormData>;
  errors: FieldErrors<PatientFormData>;
  onSubmit: () => Promise<void>;
  register: UseFormRegister<PatientFormData>;
  setValue: UseFormSetValue<PatientFormData>;
}

const AddProfessionnelPatient = ({
  control,
  errors,
  onSubmit,
  register,
  setValue,
}: AddProfessionnelPatientProps) => {
  return (
    <div>
      <RegisterStep1
        control={control}
        errors={errors}
        onSubmit={onSubmit}
        register={register}
        setValue={setValue}
        patient={null}
      />
      {/* <FormField
        id="telephone"
        label="Téléphone"
        type="tel"
        placeholder="Ex: 032XXXXXXX ou +261XXXXXXXXX"
        icon={Phone}
        register={register}
        required
        error={errors.telephone}
        helpText="Format accepté: 032XXXXXXX ou +261XXXXXXXXX (opérateurs: 32, 33, 34, 39)"
        validation={{
          required: "Le numéro de téléphone est requis",
          pattern: {
            value: /^(0(32|33|34|39)\d{7}|\+261(32|33|34|39)\d{7})$/,
            message:
              "Format de téléphone invalide. Utilisez: 032XXXXXXX ou +261XXXXXXXXX",
          },
        }}
      /> */}
    </div>
  );
};

export default AddProfessionnelPatient;
