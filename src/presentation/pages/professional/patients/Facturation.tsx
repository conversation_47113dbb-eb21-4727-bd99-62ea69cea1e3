import FacturationDataGrid from "@/presentation/components/common/historiqueCarnetSante/component/FacturationDataGrid";
import { But<PERSON> } from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";
import AddFacturationModal from "@/presentation/components/common/Modal/AddFacturationModal";

const Facturation = () => {
  const { isAddForm, setIsAddForm } = useCarnetDeSanteState();

  const handleClose = () => {
    setIsAddForm(false);
  };

  return (
    <div>
      <div className="flex items-center gap-2 my-4">
        <h2 className="text-xl font-semibold">Facturation</h2>
        <Button
          variant="contained"
          sx={{
            textTransform: "none",
            ml: 2,
            borderRadius: 5,
            backgroundColor: PRIMARY,
          }}
          onClick={() => setIsAddForm(true)}
        >
          Ajouter une nouvelle visite
        </Button>
      </div>
      <FacturationDataGrid />
      {isAddForm && (
        <AddFacturationModal
          isAddFacturationModalOpen={isAddForm}
          handleClose={handleClose}
        />
      )}
    </div>
  );
};

export default Facturation;
