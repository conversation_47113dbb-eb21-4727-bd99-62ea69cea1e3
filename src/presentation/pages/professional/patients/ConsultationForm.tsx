import { Box, TextField, Typography } from "@mui/material";
import { HelpCircle } from "lucide-react";
import Tooltip from "@/shared/utils/tooltipe";

interface ConsultationFormProps {
  raisonsVisite: string;
  plaintePrincipale: string;
  remarques: string;
  examenSystemes: string;
  diagnostic: string;
  planSoins: string;
  date_consultation: Date | null;
  setRaisonsVisite: (value: string) => void;
  setPlaintePrincipale: (value: string) => void;
  setRemarques: (value: string) => void;
  setExamenSystemes: (value: string) => void;
  setDiagnostic: (value: string) => void;
  setPlanSoins: (value: string) => void;
  hideSigneVitaux?: boolean; // Nouvelle prop pour masquer les signes vitaux
}

export const ConsultationForm = ({
  raisonsVisite,
  plaintePrincipale,
  remarques,
  examenSystemes,
  diagnostic,
  planSoins,
  date_consultation,
  setRaisonsVisite,
  setPlaintePrincipale,
  setRemarques,
  setExamenSystemes,
  setDiagnostic,
  setPlanSoins,
  hideSigneVitaux = false,
}: ConsultationFormProps) => {
  return (
    <>
      {/* La section des signes vitaux est maintenant gérée dans le parent */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Box>
          <Typography
            variant="subtitle2"
            className="flex items-center gap-1 text-gray-500 dark:text-white"
            sx={{
              fontWeight: "bold",
            }}
          >
            Motif de la consultation
            <Tooltip
              content="Motif principal de la consultation et symptômes présentés "
              maxWidth="max-w-none"
              minWidth="min-w-48"
              position="right"
            >
              <HelpCircle
                className="w-4 h-4 text-gray-500 dark:text-white"
                strokeWidth={2.5}
              />
            </Tooltip>
          </Typography>

          <TextField
            placeholder="Raisons pour la visite"
            fullWidth
            size="small"
            value={raisonsVisite}
            onChange={(e) => setRaisonsVisite(e.target.value)}
          />
        </Box>
        <Box>
          <Typography
            variant="subtitle2"
            className="flex items-center gap-1 text-gray-500 dark:text-white"
            sx={{
              fontWeight: "bold",
            }}
          >
            Problème principal (facultatif)
            <Tooltip
              content="Symptôme ou problème principal rapporté par le patient  "
              maxWidth="max-w-none"
              minWidth="min-w-48"
              position="right"
            >
              <HelpCircle
                className="w-4 h-4 text-gray-500 dark:text-white"
                strokeWidth={2.5}
              />
            </Tooltip>
          </Typography>
          <TextField
            placeholder="Plainte principale"
            fullWidth
            size="small"
            value={plaintePrincipale}
            onChange={(e) => setPlaintePrincipale(e.target.value)}
          />
        </Box>
      </div>
      <Box>
        <Typography
          variant="subtitle2"
          className="flex items-center gap-1 text-gray-500 dark:text-white"
          sx={{
            fontWeight: "bold",
          }}
        >
          Observations générales
          <Tooltip
            content="état global, apparence, comportement et remarques utiles."
            maxWidth="max-w-none"
            minWidth="min-w-48"
            position="right"
          >
            <HelpCircle
              className="w-4 h-4 text-gray-500 dark:text-white"
              strokeWidth={2.5}
            />
          </Tooltip>
        </Typography>
        <TextField
          placeholder="Remarques"
          multiline
          rows={2}
          fullWidth
          value={remarques}
          onChange={(e) => setRemarques(e.target.value)}
        />
      </Box>
      <Box>
        <Typography
          variant="subtitle2"
          className="flex items-center gap-1 text-gray-500 dark:text-white"
          sx={{
            fontWeight: "bold",
          }}
        >
          Examen médical détaillé (facultatif)
          <Tooltip
            content="Résultats de l'examen clinique détaillé"
            maxWidth="max-w-none"
            minWidth="min-w-48"
            position="right"
          >
            <HelpCircle
              className="w-4 h-4 text-gray-500 dark:text-white"
              strokeWidth={2.5}
            />
          </Tooltip>
        </Typography>
        <TextField
          placeholder="Examen des systèmes"
          multiline
          rows={2}
          fullWidth
          value={examenSystemes}
          onChange={(e) => setExamenSystemes(e.target.value)}
        />
      </Box>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Box>
          <Typography
            variant="subtitle2"
            className="flex items-center gap-1 text-gray-500 dark:text-white"
            sx={{
              fontWeight: "bold",
            }}
          >
            Conclusion médicale
            <Tooltip
              content=" Diagnostic principal et diagnostics secondaires"
              maxWidth="max-w-none"
              minWidth="min-w-48"
              position="right"
            >
              <HelpCircle
                className="w-4 h-4 text-gray-500 dark:text-white"
                strokeWidth={2.5}
              />
            </Tooltip>
          </Typography>
          <TextField
            placeholder="Diagnostic"
            multiline
            rows={2}
            fullWidth
            value={diagnostic}
            onChange={(e) => setDiagnostic(e.target.value)}
          />
        </Box>
        <Box>
          <Typography
            variant="subtitle2"
            className="flex items-center gap-1 text-gray-500 dark:text-white"
            sx={{
              fontWeight: "bold",
            }}
          >
            Traitement prescrit et recommandations
            <Tooltip
              content=" médicaments prescrits, soins recommandés et conseils à suivre."
              maxWidth="max-w-none"
              minWidth="min-w-48"
              position="right"
            >
              <HelpCircle
                className="w-4 h-4 text-gray-500 dark:text-white"
                strokeWidth={2.5}
              />
            </Tooltip>
          </Typography>
          <TextField
            placeholder="Plan de soins"
            multiline
            rows={2}
            fullWidth
            value={planSoins}
            onChange={(e) => setPlanSoins(e.target.value)}
          />
        </Box>
      </div>
    </>
  );
};
