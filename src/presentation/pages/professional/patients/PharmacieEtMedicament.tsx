import { Dash } from "@/domain/models";
import { active_tab_enum, utilisateurs_role_enum } from "@/domain/models/enums";
import { PrintService } from "@/domain/services/PrintService";
import { ALLERGIE_TABLE_NAME } from "@/infrastructure/repositories/allergie/Constant";
import { MEDICAMENT_TABLE_NAME } from "@/infrastructure/repositories/medicament/Constant";
import { VACCINATION_TABLE_NAME } from "@/infrastructure/repositories/vaccination/Constant";
import CarnetDeSanteCard from "@/presentation/components/common/historiqueCarnetSante/component/CarnetDeSanteCard";
import { AddCarnetDeSanteModal } from "@/presentation/components/common/Modal/AddCarnetDeSanteModal";
import { HistoriqueCarnetDeSanteModal } from "@/presentation/components/common/Modal/HistoriqueCarnetDeSanteModal";
import {
  useCarnetDeSante,
  useCarnetDeSanteData,
} from "@/presentation/hooks/carnetDeSante";
import { useHistoriqueCarnetSante } from "@/presentation/hooks/carnetDeSante/sousCarnet/useHistoriqueCarnetSante";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import { useAppSelector } from "@/presentation/hooks/redux";
import useAuth from "@/presentation/hooks/use-auth";
import { useProche } from "@/presentation/hooks/use-proches";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { getDataCard } from "@/shared/constants/cardData";
import { Button } from "@mui/material";
import { useState } from "react";

const PharmacieEtMedicament = () => {
  const [type, setType] = useState("");
  const [isCarnetDeSanteModalOpen, setIsCarnetDeSanteModalOpen] =
    useState(false);
  const [isHistoriqueCarnetDeSanteModal, setIsHistoriqueCarnetDeSanteModal] =
    useState(false);
  const { resetSearch } = useCarnetDeSante();
  const { data } = useCarnetDeSanteData();
  const { currentProfessional } = useSearchProfessional();
  const { selectedProchePatient, selectedProcheEmployer } = useProche();
  const { selectedEmployerSlice } = useEmployer();
  const { selectedDataProfessionalPatient } = useProfessionnelPatient();
  const { clearSelected } = useHistoriqueCarnetSante();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const user = useAppSelector((state) => state.authentification.userData);
  const { roleUserSelected } = useAuth();
  const DATA_CARD = getDataCard(data, active_tab_enum.consultationMedicale, [
    ALLERGIE_TABLE_NAME,
    MEDICAMENT_TABLE_NAME,
    VACCINATION_TABLE_NAME,
  ]);

  const handleIsCarnetDeSanteModalOpen = (type: string) => {
    setIsCarnetDeSanteModalOpen(true);
    setType(type);
  };
  const handleCloseCarnetDeSanteModal = () => {
    setIsCarnetDeSanteModalOpen(false);
    resetSearch();
  };
  const handleCloseHistoriqueCarnetDeSanteModal = () => {
    setIsHistoriqueCarnetDeSanteModal(false);
    clearSelected();
  };
  const handleIsHistoriqueCarnetDeSanteModalOpen = (type: string) => {
    setIsHistoriqueCarnetDeSanteModal(true);
    setType(type);
  };
  const handlePrint = () => {
    if (role === utilisateurs_role_enum.PROFESSIONNEL) {
      if (roleUserSelected === utilisateurs_role_enum.PATIENT) {
        PrintService.printMedicament(
          [...DATA_CARD],
          selectedDataProfessionalPatient?.patient,
          null,
          null,
          currentProfessional,
          null
        );
      } else if (roleUserSelected === utilisateurs_role_enum.PROCHE) {
        PrintService.printMedicament(
          [...DATA_CARD],
          null,
          selectedProchePatient,
          null,
          currentProfessional,
          null
        );
      }
    } else if (role === utilisateurs_role_enum.DASH) {
      if (roleUserSelected === utilisateurs_role_enum.EMPLOYER) {
        PrintService.printMedicament(
          [...DATA_CARD],
          null,
          null,
          selectedEmployerSlice,
          null,
          user as Dash
        );
      } else {
        PrintService.printMedicament(
          [...DATA_CARD],
          null,
          selectedProcheEmployer,
          null,
          null,
          user as Dash
        );
      }
    }
  };
  return (
    <div className="my-4">
      <div className="bg-gradient-to-r from-meddoc-primary to-meddoc-fonce text-white p-3 shadow-lg mb-4 my-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">Pharmacie et Medicament</h1>
            <p className="text-sm opacity-90 mt-1">
              Suivi complet du carnet de sante -{" "}
              {roleUserSelected === utilisateurs_role_enum.EMPLOYER
                ? selectedEmployerSlice?.nom +
                  " " +
                  selectedEmployerSlice?.prenom
                : roleUserSelected === utilisateurs_role_enum.PATIENT
                  ? selectedDataProfessionalPatient?.patient?.nom +
                    " " +
                    selectedDataProfessionalPatient?.patient?.prenom
                  : role === utilisateurs_role_enum.PROFESSIONNEL
                    ? selectedProchePatient?.nom +
                      " " +
                      selectedProchePatient?.prenom
                    : selectedProcheEmployer?.nom +
                      " " +
                      selectedProcheEmployer?.prenom}
            </p>
          </div>
          <Button
            variant="outlined"
            sx={{ textTransform: "none" }}
            onClick={handlePrint}
          >
            Imprimer
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {DATA_CARD.map((data, index) => (
          <CarnetDeSanteCard
            key={index}
            data={data}
            setIsCarnetDeSanteModalOpen={() =>
              handleIsCarnetDeSanteModalOpen(data.title)
            }
            setIsHistoriqueCarnetDeSanteModalOpen={() =>
              handleIsHistoriqueCarnetDeSanteModalOpen(data.title)
            }
            className="lg:mx-2 my-2"
          />
        ))}
      </div>
      {isCarnetDeSanteModalOpen && (
        <AddCarnetDeSanteModal
          type={type}
          isCarnetDeSanteModalOpen={isCarnetDeSanteModalOpen}
          handleCloseModal={handleCloseCarnetDeSanteModal}
        />
      )}
      {isHistoriqueCarnetDeSanteModal && (
        <HistoriqueCarnetDeSanteModal
          type={type}
          isHistoriqueCarnetDeSanteModalOpen={isHistoriqueCarnetDeSanteModal}
          handleCloseModal={handleCloseHistoriqueCarnetDeSanteModal}
        />
      )}
    </div>
  );
};

export default PharmacieEtMedicament;
