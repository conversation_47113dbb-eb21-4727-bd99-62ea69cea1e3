import { ComponentProps, useState } from "react";
import CarnetDeSanteCard from "@/presentation/components/common/historiqueCarnetSante/component/CarnetDeSanteCard";
import { AddCarnetDeSanteModal } from "@/presentation/components/common/Modal/AddCarnetDeSanteModal";
import { useParams } from "react-router-dom";
import {
  useCarnetDeSanteData,
  useCarnetDeSante,
  useCarnetDeSanteState,
} from "@/presentation/hooks/carnetDeSante";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import { Button, Box, Typography, Paper, Fade } from "@mui/material";
import { getDataCard } from "@/shared/constants/cardData";
import { HistoriqueCarnetDeSanteModal } from "@/presentation/components/common/Modal/HistoriqueCarnetDeSanteModal";
import { PrintService } from "@/domain/services/PrintService";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { PRIMARY } from "@/shared/constants/Color";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { useDiagnostic } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDiagnostic";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useHistoriqueCarnetSante } from "@/presentation/hooks/carnetDeSante/sousCarnet/useHistoriqueCarnetSante";
import { twMerge } from "tailwind-merge";
import { useAppSelector } from "@/presentation/hooks/redux";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { EditConfidentialiteModal } from "@/presentation/components/common/Modal/EditConfidentialiteModal";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import { CarnetSante, Dash } from "@/domain/models";

import useAuth from "@/presentation/hooks/use-auth";
import { useProche } from "@/presentation/hooks/use-proches";

import { FileText, Printer, Plus } from "lucide-react";
import { useProfilePatientData } from "@/presentation/hooks/useProfilePatientData";

type CarnetDeSanteProps = ComponentProps<"div"> & {
  isEdit?: boolean;
};

const CarnetDeSante: React.FC<CarnetDeSanteProps> = ({
  isEdit,
  className,
  ...props
}) => {
  const [isCarnetDeSanteModalOpen, setIsCarnetDeSanteModalOpen] =
    useState(false);
  const [isHistoriqueCarnetDeSanteModal, setIsHistoriqueCarnetDeSanteModal] =
    useState(false);
  const [type, setType] = useState("");
  const { id } = useParams();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const userData = useAppSelector((state) => state.authentification.userData);
  const id_patient = useAppSelector((state) => state.authentification.user?.id);
  const {
    idCarnetSante,
    loading: loadingIdCarnet,
    data,
    handleSubmit,
  } = useCarnetDeSanteData(
    role === utilisateurs_role_enum.PROFESSIONNEL ||
      role === utilisateurs_role_enum.DASH
      ? Number(id)
      : id_patient
  );
  const { resetState } = useDiagnostic();
  const { patientData } = useProfilePatientData();
  const { setConfidentialite, resetSelectedFileState } =
    useCarnetDeSanteState();
  const { selectedDataProfessionalPatient } = useProfessionnelPatient();
  const { selectedEmployerSlice } = useEmployer();
  const { currentProfessional } = useSearchProfessional();
  const { roleUserSelected } = useAuth();
  const { selectedProcheEmployer, selectedProchePatient } = useProche();
  const { loading: loadingCreatCarnet, resetSearch } = useCarnetDeSante();
  const { clearSelected } = useHistoriqueCarnetSante();
  const handleIsCarnetDeSanteModalOpen = (type: string) => {
    setIsCarnetDeSanteModalOpen(true);
    setType(type);
  };

  const handleIsHistoriqueCarnetDeSanteModalOpen = (type: string) => {
    setIsHistoriqueCarnetDeSanteModal(true);
    setType(type);
  };

  const handleCloseCarnetDeSanteModal = () => {
    setIsCarnetDeSanteModalOpen(false);
    if (type === TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage) {
      resetState();
      resetSelectedFileState();
    } else {
      resetSearch();
    }
    if (isEdit) {
      setConfidentialite(false);
    }
  };

  const handleCloseHistoriqueCarnetDeSanteModal = () => {
    setIsHistoriqueCarnetDeSanteModal(false);
    // resetSearch();
    clearSelected();
  };

  const DATA_CARD = getDataCard(
    data,
    role === utilisateurs_role_enum.PATIENT
      ? patientData?.sexe
      : roleUserSelected === utilisateurs_role_enum.EMPLOYER
        ? selectedEmployerSlice?.sexe
        : roleUserSelected === utilisateurs_role_enum.PATIENT
          ? selectedDataProfessionalPatient?.patient.sexe
          : roleUserSelected === utilisateurs_role_enum.PROCHE
            ? role === utilisateurs_role_enum.PROFESSIONNEL
              ? selectedProchePatient?.sexe
              : selectedProcheEmployer?.employees?.sexe
            : null
  );
  const DATA_CARD_DIAGNOSTICS = getDataCard(
    data,
    TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
  );

  const handlePrint = () => {
    if (role === utilisateurs_role_enum.PROFESSIONNEL) {
      if (roleUserSelected === utilisateurs_role_enum.PATIENT) {
        PrintService.printCarnetSante(
          [...DATA_CARD, ...DATA_CARD_DIAGNOSTICS],
          selectedDataProfessionalPatient?.patient,
          null,
          null,
          currentProfessional,
          null
        );
      } else if (roleUserSelected === utilisateurs_role_enum.PROCHE) {
        PrintService.printCarnetSante(
          [...DATA_CARD, ...DATA_CARD_DIAGNOSTICS],
          null,
          null,
          selectedProchePatient,
          currentProfessional,
          null
        );
      }
    } else if (role === utilisateurs_role_enum.DASH) {
      if (roleUserSelected === utilisateurs_role_enum.EMPLOYER) {
        PrintService.printCarnetSante(
          [...DATA_CARD, ...DATA_CARD_DIAGNOSTICS],
          null,
          selectedEmployerSlice,
          null,
          null,
          userData as Dash
        );
      } else {
        PrintService.printCarnetSante(
          [...DATA_CARD, ...DATA_CARD_DIAGNOSTICS],
          null,
          null,
          selectedProcheEmployer,
          null,
          userData as Dash
        );
      }
    }
  };

  return (
    <Fade in timeout={600}>
      <div>
        {idCarnetSante ? (
          <div className="py-4">
            {/* En-tête amélioré */}
            <div
              className="
             bg-gradient-to-r from-meddoc-primary to-meddoc-fonce text-white p-3 shadow-lg mb-4 my-4
            "
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <FileText size={24} />
                  <div>
                    <h2 className="text-xl font-bold">
                      Carnet de Santé - Historique médical complet
                    </h2>
                    <p className="text-sm opacity-90">
                      Dossier médical complet et sécurisé -{" "}
                      {roleUserSelected === utilisateurs_role_enum.EMPLOYER
                        ? selectedEmployerSlice?.nom +
                          " " +
                          selectedEmployerSlice?.prenom
                        : roleUserSelected === utilisateurs_role_enum.PATIENT
                          ? selectedDataProfessionalPatient?.patient?.nom +
                            " " +
                            selectedDataProfessionalPatient?.patient?.prenom
                          : role === utilisateurs_role_enum.PROFESSIONNEL
                            ? selectedProchePatient?.nom +
                              " " +
                              selectedProchePatient?.prenom
                            : selectedProcheEmployer?.nom +
                              " " +
                              selectedProcheEmployer?.prenom}
                    </p>
                  </div>
                </div>
                {(role === utilisateurs_role_enum.PROFESSIONNEL ||
                  role === utilisateurs_role_enum.DASH) && (
                  <button
                    onClick={handlePrint}
                    className="
                      flex items-center gap-2 px-4 py-2 rounded-xl
                      bg-white/20 hover:bg-white/30 dark:bg-white/10 dark:hover:bg-white/20
                      text-white font-semibold text-sm
                      transition-all duration-300 ease-out
                      hover:-translate-y-0.5 hover:shadow-lg
                    "
                  >
                    <Printer size={16} />
                    Imprimer
                  </button>
                )}
              </div>
            </div>

            {/* Grille des cartes principales */}
            <div
              className={twMerge(
                "grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 auto-rows-fr",
                className
              )}
              {...props}
            >
              {DATA_CARD.map((data, index) => (
                <Fade in timeout={800 + index * 100} key={index}>
                  <div>
                    <CarnetDeSanteCard
                      data={data}
                      isEdit={isEdit}
                      setIsCarnetDeSanteModalOpen={() =>
                        handleIsCarnetDeSanteModalOpen(data.title)
                      }
                      setIsHistoriqueCarnetDeSanteModalOpen={() =>
                        handleIsHistoriqueCarnetDeSanteModalOpen(data.title)
                      }
                    />
                  </div>
                </Fade>
              ))}
            </div>

            {/* Section diagnostics séparée */}
            <div className="mt-6">
              <h3
                className="
                flex items-center gap-2 mb-3 text-lg font-semibold
                text-gray-700 dark:text-gray-300
              "
              >
                <FileText size={20} />
                Examens et Diagnostics
              </h3>
              <Fade in timeout={1200}>
                <div>
                  <CarnetDeSanteCard
                    data={DATA_CARD_DIAGNOSTICS[0]}
                    isEdit={isEdit}
                    setIsCarnetDeSanteModalOpen={() =>
                      handleIsCarnetDeSanteModalOpen(
                        TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
                      )
                    }
                    setIsHistoriqueCarnetDeSanteModalOpen={() =>
                      handleIsHistoriqueCarnetDeSanteModalOpen(
                        TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
                      )
                    }
                  />
                </div>
              </Fade>
            </div>
            {isCarnetDeSanteModalOpen &&
              (isEdit ? (
                <EditConfidentialiteModal
                  type={type}
                  isCarnetDeSanteModalOpen={isCarnetDeSanteModalOpen}
                  handleCloseModal={handleCloseCarnetDeSanteModal}
                />
              ) : (
                <AddCarnetDeSanteModal
                  type={type}
                  isCarnetDeSanteModalOpen={isCarnetDeSanteModalOpen}
                  handleCloseModal={handleCloseCarnetDeSanteModal}
                />
              ))}
            {isHistoriqueCarnetDeSanteModal && (
              <HistoriqueCarnetDeSanteModal
                type={type}
                isHistoriqueCarnetDeSanteModalOpen={
                  isHistoriqueCarnetDeSanteModal
                }
                handleCloseModal={handleCloseHistoriqueCarnetDeSanteModal}
              />
            )}
          </div>
        ) : (
          <div
            className="
            flex flex-col items-center justify-center 
            w-full min-h-[45vh] text-center p-2
          "
          >
            {loadingIdCarnet ? (
              <LoadingSpinner />
            ) : (
              <div
                className="
                p-5 rounded-xl max-w-xs w-full mx-auto
                bg-gradient-to-br from-gray-50 to-slate-100
                dark:from-gray-800 dark:to-slate-900
                border border-dashed border-slate-300 dark:border-slate-600
                shadow-md
              "
              >
                <div className="mb-3">
                  <div className="flex justify-center">
                    <FileText
                      size={36}
                      className="text-meddoc-primary dark:text-meddoc-secondary mb-2"
                    />
                  </div>
                  <h3
                    className="
                    text-lg font-semibold mb-1
                    text-gray-800 dark:text-white
                  "
                  >
                    Aucun carnet de santé
                  </h3>
                  {role !== utilisateurs_role_enum.PATIENT && (
                    <p
                      className="
                      text-sm text-gray-600 dark:text-gray-300 mb-3
                      max-w-xs mx-auto
                    "
                    >
                      Créez un nouveau carnet de santé pour commencer à
                      enregistrer les informations médicales.
                    </p>
                  )}
                </div>
                {role !== utilisateurs_role_enum.PATIENT && (
                  <div className="flex justify-center">
                    <button
                      onClick={() => {
                        const data: Omit<CarnetSante, "id"> = {
                          id_proprietaire: Number(id),
                          date_creation: new Date(),
                          confidentialite: false,
                        };
                        handleSubmit(data);
                      }}
                      disabled={loadingCreatCarnet}
                      className="
                      flex items-center justify-center gap-1 px-4 py-2 rounded-lg
                      bg-gradient-to-r from-meddoc-primary to-meddoc-secondary
                      hover:from-meddoc-primary hover:to-meddoc-secondary/90
                      dark:from-meddoc-primary dark:to-meddoc-secondary
                      dark:hover:from-meddoc-primary dark:hover:to-meddoc-secondary/90
                      text-white font-medium text-sm
                      transition-all duration-200 ease-out
                      hover:shadow-md
                      disabled:opacity-50 disabled:cursor-not-allowed
                      disabled:hover:translate-y-0 disabled:hover:shadow-none
                      w-full
                    "
                    >
                      <Plus size={16} />
                      {loadingCreatCarnet
                        ? "Création..."
                        : "Créer le carnet de santé"}
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </Fade>
  );
};

export default CarnetDeSante;
