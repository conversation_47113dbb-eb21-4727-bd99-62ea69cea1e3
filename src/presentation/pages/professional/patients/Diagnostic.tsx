import { useEffect, useState } from "react";
import { But<PERSON> } from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import {
  useCarnetDeSante,
  useCarnetDeSanteData,
  useCarnetDeSanteState,
} from "@/presentation/hooks/carnetDeSante";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import DiagnosticCard from "@/presentation/components/common/historiqueCarnetSante/component/DiagnosticCard";
import { useDiagnosticForm } from "@/presentation/hooks/diagnostic/useDiagnosticForm";
import AddDiagnosticModal from "@/presentation/components/common/Modal/AddDiagnosticModal";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import useAuth from "@/presentation/hooks/use-auth";
import { useProche } from "@/presentation/hooks/use-proches";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";

const Diagnostic = () => {
  const { isAddForm, setIsAddForm } = useCarnetDeSanteState();
  const { idCarnetSante } = useCarnetDeSante();
  const { resetForm, initialiseState } = useDiagnosticForm(idCarnetSante);

  const { selectedEmployerSlice } = useEmployer();
  const { selectedDataProfessionalPatient } = useProfessionnelPatient();
  const { roleUserSelected } = useAuth();
  const { selectedProche } = useProche();

  const { data } = useCarnetDeSanteData();
  const [idDiagnostic, setIdDiagnostic] = useState<number | null>(null);

  //disable button voir
  const [isDesableButtonVoir, setIsDesableButtonVoir] = useState(false);

  const handleClose = () => {
    setIsAddForm(false);
    setIdDiagnostic(null);
    resetForm();
  };

  const handleViewLaboratoireDiagnostic = (diagnostic: any) => {
    initialiseState(diagnostic);
    handleAddDiagnosticModalOpen();
    setIdDiagnostic(diagnostic.id);
    setIsDesableButtonVoir(true);
  };

  const handleAddDiagnosticModalOpen = () => {
    setIsAddForm(true);
    setIsDesableButtonVoir(false);
    console.log("okok");
  };
  return (
    <>
      <div className="bg-gradient-to-r from-meddoc-primary to-meddoc-fonce text-white p-3 shadow-lg mb-4 my-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">Historique des Diagnostic</h1>
            <p className="text-sm opacity-90 mt-1">
              Suivi complet des diagnostics -  {
                (roleUserSelected === utilisateurs_role_enum.EMPLOYER)
                  ? `${selectedEmployerSlice?.nom} ${selectedEmployerSlice?.prenom}`
                  : (roleUserSelected === utilisateurs_role_enum.PATIENT)
                    ? `${selectedDataProfessionalPatient?.patient?.nom} ${selectedDataProfessionalPatient?.patient?.prenom}`
                    : (roleUserSelected === utilisateurs_role_enum.PROCHE)
                      ? `${selectedProche?.nom} ${selectedProche?.prenom}`
                      : null
              }
            </p>
          </div>
          <Button
            variant="contained"
            sx={{
              textTransform: "none",
              borderRadius: 2,
              backgroundColor: "rgba(255, 255, 255, 0.2)",
              color: "white",
              fontWeight: "600",
              px: 3,
              py: 1,
              '&:hover': {
                backgroundColor: "rgba(255, 255, 255, 0.3)",
              }
            }}
            onClick={handleAddDiagnosticModalOpen}
          >
            + Nouvelle diagnostic
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-1 md:grid-cols-1 gap-4">
        <ListDataGrid
          data={data?.diagnostic}
          type="laboratoire_diagnostics"
          onViewLaboratoireDiagnostics={handleViewLaboratoireDiagnostic}
        />
      </div>
      {isAddForm && (
        <AddDiagnosticModal
          isAddDiagnosticModalOpen={isAddForm}
          handleClose={handleClose}
          diagnostic={data?.diagnostic?.find((d) => d.id === idDiagnostic)}
          isDesableButtonVoir={isDesableButtonVoir}
        />
      )}
    </>
  );
};

export default Diagnostic;
