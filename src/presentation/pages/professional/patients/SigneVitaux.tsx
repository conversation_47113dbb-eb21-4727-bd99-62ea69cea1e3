import { useState } from "react";
import { But<PERSON> } from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import SigneVitauxCard from "@/presentation/components/common/historiqueCarnetSante/component/SigneVitauxCard";
import {
  useCarnetDeSante,
  useCarnetDeSanteData,
  useCarnetDeSanteState,
} from "@/presentation/hooks/carnetDeSante";
import { useSigneVitauxForm } from "@/presentation/hooks/signeVitaux";
import AddSigneVitauxModal from "@/presentation/components/common/Modal/AddSigneVitauxModal";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import useAuth from "@/presentation/hooks/use-auth";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useProche } from "@/presentation/hooks/use-proches";
const SigneVitaux = () => {
  const { isAddForm, setIsAddForm } = useCarnetDeSanteState();
  const { idCarnetSante } = useCarnetDeSante();
  const { resetForm, initialiseState } = useSigneVitauxForm(idCarnetSante);
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { signeVitaux } = useCarnetDeSanteData();
  const [idSigneVitaux, setIdSigneVitaux] = useState<number | null>(null);

  // pour desable le bouton enregistrer quand on est en mode voir
  const [isDesableButtonVoir, setIsDesableButtonVoir] = useState(false);

  const { selectedEmployerSlice } = useEmployer();
  const { selectedProcheEmployer, selectedProchePatient } = useProche();
  const { selectedDataProfessionalPatient } = useProfessionnelPatient();
  const { roleUserSelected } = useAuth();
  const { selectedProche } = useProche();

  const handleClose = () => {
    setIsAddForm(false);
    setIdSigneVitaux(null);
    resetForm();
  };

  const handleAddSigneVitauxButton = () => {
    setIsAddForm(true);
    setIsDesableButtonVoir(false);
  };

  // Fonction pour gérer l'ouverture des détails des signes vitaux
  const handleViewSigneVitaux = (signe: any) => {
    initialiseState(signe);
    handleAddSigneVitauxButton();
    setIdSigneVitaux(signe.id);
    setIsDesableButtonVoir(true);
  };

  return (
    <>
      <div className="bg-gradient-to-r from-meddoc-primary to-meddoc-fonce text-white p-3 shadow-lg mb-4 my-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">
              Historique des Signe Vitaux
            </h1>
            <p className="text-sm opacity-90 mt-1">
              Suivi complet des rendez-vous et examens médicaux -{" "}
              {roleUserSelected === utilisateurs_role_enum.EMPLOYER
                ? selectedEmployerSlice?.nom +
                  " " +
                  selectedEmployerSlice?.prenom
                : roleUserSelected === utilisateurs_role_enum.PATIENT
                  ? selectedDataProfessionalPatient?.patient?.nom +
                    " " +
                    selectedDataProfessionalPatient?.patient?.prenom
                  : role === utilisateurs_role_enum.PROFESSIONNEL
                    ? selectedProchePatient?.nom +
                      " " +
                      selectedProchePatient?.prenom
                    : selectedProcheEmployer?.nom +
                      " " +
                      selectedProcheEmployer?.prenom}
            </p>
          </div>
          <Button
            variant="contained"
            sx={{
              textTransform: "none",
              borderRadius: 2,
              backgroundColor: "rgba(255, 255, 255, 0.2)",
              color: "white",
              fontWeight: "600",
              px: 3,
              py: 1,
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.3)",
              },
            }}
            onClick={handleAddSigneVitauxButton}
          >
            + Nouvelle Signe
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-1 gap-4">
        <ListDataGrid
          data={signeVitaux}
          type="signe_vitaux"
          onViewSigneVitaux={handleViewSigneVitaux}
        />
      </div>
      {isAddForm && (
        <AddSigneVitauxModal
          canPrint
          isAddSigneVitauxModalOpen={isAddForm}
          handleCloseAddSigneVitauxModal={handleClose}
          isDesableButtonVoir={isDesableButtonVoir}
          signeVitaux={signeVitaux?.find((signe) => signe.id === idSigneVitaux)}
        />
      )}
    </>
  );
};

export default SigneVitaux;
