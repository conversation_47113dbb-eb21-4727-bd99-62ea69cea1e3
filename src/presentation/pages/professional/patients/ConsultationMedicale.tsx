import { useAppSelector } from "@/presentation/hooks/redux";
import {
  useCarnetDeSante,
  useCarnetDeSanteData,
  useCarnetDeSanteState,
} from "@/presentation/hooks/carnetDeSante";
import { useConsultationForm } from "@/presentation/hooks/consultationMedicale/useConsultationForm";
import { Button } from "@mui/material";
import { useEffect, useState } from "react";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { PrintService } from "@/domain/services/PrintService";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { Dash } from "@/domain/models";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import MedicalConsultationModal from "@/presentation/components/common/Modal/MedicalConsultationModal";
import useAuth from "@/presentation/hooks/use-auth";
import { useProche } from "@/presentation/hooks/use-proches";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";

const ConsultationMedicale = () => {
  const [consultationDetail, setConsultationDetail] = useState<{
    id: number;
    date_visite: Date;
  }>(null);
  // pour desable le bouton enregistrer quand on est en mode voir
  const [isDesableButtonVoir, setIsDesableButtonVoir] = useState(false);

  const { isAddForm, setIsAddForm } = useCarnetDeSanteState();
  const { idCarnetSante, resetSearch } = useCarnetDeSante();
  const { selectedProchePatient, selectedProcheEmployer } = useProche();
  const { consultations, data, signeVitaux } = useCarnetDeSanteData();
  const professionalId = useAppSelector(
    (state) => state.authentification.user?.id
  );

  const {
    formData,
    isFormValid,
    getConsultationData,
    resetForm,
    initialiseState,
  } = useConsultationForm(professionalId, idCarnetSante);
  const { currentProfessional } = useSearchProfessional();
  const { selectedDataProfessionalPatient } = useProfessionnelPatient();
  const { selectedEmployerSlice } = useEmployer();

  const role = useAppSelector((state) => state.authentification.user?.role);
  const user = useAppSelector((state) => state.authentification.userData);
  const { roleUserSelected } = useAuth();

  const handlePrint = () => {
    if (role === utilisateurs_role_enum.PROFESSIONNEL) {
      if (roleUserSelected === utilisateurs_role_enum.PATIENT) {
        PrintService.printConsultation({
          carnet: data,
          signeVitaux: signeVitaux[signeVitaux.length - 1],
          consultations: consultations,
          patient: selectedDataProfessionalPatient?.patient,
          employer: null,
          proche: null,
          professionnel: currentProfessional,
          dash: null,
        });
      } else if (roleUserSelected === utilisateurs_role_enum.PROCHE) {
        PrintService.printConsultation({
          carnet: data,
          signeVitaux: signeVitaux[signeVitaux.length - 1],
          consultations: consultations,
          patient: null,
          employer: null,
          proche: selectedProchePatient,
          professionnel: null,
          dash: null,
        });
      }
    } else if (role === utilisateurs_role_enum.DASH) {
      if (roleUserSelected === utilisateurs_role_enum.EMPLOYER) {
        PrintService.printConsultation({
          carnet: data,
          signeVitaux: signeVitaux[signeVitaux.length - 1],
          consultations: consultations,
          patient: null,
          employer: selectedEmployerSlice,
          proche: null,
          professionnel: null,
          dash: user as Dash,
        });
      } else if (roleUserSelected === utilisateurs_role_enum.PROCHE) {
        PrintService.printConsultation({
          carnet: data,
          signeVitaux: signeVitaux[signeVitaux.length - 1],
          consultations: consultations,
          patient: null,
          employer: null,
          proche: selectedProcheEmployer,
          professionnel: null,
          dash: user as Dash,
        });
      }
    }
  };

  useEffect(() => {
    console.log(selectedEmployerSlice);
  }, []);

  const handleClose = () => {
    resetForm();
    setIsAddForm(false);
    setConsultationDetail(null);
  };

  const handleAddConsultationButton = () => {
    setIsAddForm(true);
    setIsDesableButtonVoir(false);
  };

  // Fonction pour gérer l'ouverture des détails de consultation
  const handleViewConsultation = (consultation: any) => {
    initialiseState(consultation);
    setIsDesableButtonVoir(true);
    setConsultationDetail({
      id: consultation.id,
      date_visite: consultation.date_visite,
    });
    setIsAddForm(true);
  };

  return (
    <div>
      <div className="bg-gradient-to-r from-meddoc-primary to-meddoc-fonce text-white p-3 shadow-lg mb-4 my-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">
              Historique des Consultations Médicales
            </h1>
            <p className="text-sm opacity-90 mt-1">
              Suivi complet des rendez-vous et examens médicaux -{" "}
              {roleUserSelected === utilisateurs_role_enum.EMPLOYER
                ? selectedEmployerSlice?.nom +
                  " " +
                  selectedEmployerSlice?.prenom
                : roleUserSelected === utilisateurs_role_enum.PATIENT
                  ? selectedDataProfessionalPatient?.patient?.nom +
                    " " +
                    selectedDataProfessionalPatient?.patient?.prenom
                  : role === utilisateurs_role_enum.PROFESSIONNEL
                    ? selectedProchePatient?.nom +
                      " " +
                      selectedProchePatient?.prenom
                    : selectedProcheEmployer?.nom +
                      " " +
                      selectedProcheEmployer?.prenom}
            </p>
          </div>
          <Button
            variant="contained"
            sx={{
              textTransform: "none",
              borderRadius: 2,
              backgroundColor: "rgba(255, 255, 255, 0.2)",
              color: "white",
              fontWeight: "600",
              px: 3,
              py: 1,
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.3)",
              },
            }}
            onClick={handleAddConsultationButton}
          >
            + Nouvelle Consultation
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-1 md:grid-cols-1 gap-4">
        <ListDataGrid
          data={consultations}
          type="historique_consultation"
          onViewConsultation={handleViewConsultation}
        />
      </div>
      {isAddForm && (
        <MedicalConsultationModal
          isOpen={isAddForm}
          handleClose={handleClose}
          handlePrint={handlePrint}
          data={data}
          consultationDetail={consultationDetail}
          isDesableButtonVoir={isDesableButtonVoir}
          getConsultationData={getConsultationData}
          formData={formData}
          isFormValid={isFormValid}
        />
      )}
    </div>
  );
};

export default ConsultationMedicale;
