import DashInscriptionForm from "@/presentation/components/features/dash/DashInscriptionForm.tsx";
import useVerifyDashToken from "@/presentation/hooks/dash/use-verify-dash-token";
import { useToast } from "@/presentation/hooks/use-toast.ts";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation.ts";
import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import UnathenticatedLayout from "@/presentation/components/layouts/UnauthenticatedLayout";
import { motion } from "framer-motion";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";

/**
 * Page d'inscription pour les organismes via invitation
 *
 * Cette page permet aux organismes invités de créer leur compte
 * en utilisant un token d'invitation sécurisé. Elle suit le design
 * pattern de l'application avec un layout moderne et des animations.
 *
 * @component
 * @page
 * @route /dash/register/:token
 *
 * @example
 * ```tsx
 * // Utilisation dans le routeur
 * <Route path="/dash/register/:token" element={<RegisterDash />} />
 * ```
 *
 * @returns {JSX.Element} Page complète d'inscription organisme avec layout non-authentifié
 *
 * @features
 * - Vérification sécurisée du token d'invitation
 * - Gestion des états de chargement et d'erreur
 * - Redirection automatique en cas de token invalide
 * - Interface responsive et moderne
 */
const RegisterDash = (): JSX.Element => {
  const { token } = useParams();
  const toast = useToast();
  const navigate = useNavigate();

  const { isLoading, isTokenValid, error, invitationData, setError } =
    useVerifyDashToken(token);

  useEffect(() => {
    if (error) {
      toast.error(error);
      setError("");
    }
  }, [error, toast, setError]);

  if (isLoading) {
    return (
      <UnathenticatedLayout>
        <div className="min-h-screen bg-gradient-to-br from-blue-400 via-teal-400 to-green-400">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="flex items-center justify-center min-h-screen"
          >
            <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
              <LoadingSpinner
                size={48}
                color="border-blue-600"
                className="h-auto mb-4"
              />
              <p className="text-gray-600 font-medium">
                Vérification de votre invitation...
              </p>
            </div>
          </motion.div>
        </div>
      </UnathenticatedLayout>
    );
  }

  if (!isTokenValid && !isLoading) {
    navigate(PublicRoutesNavigation.MAIN_PAGE);
    return <></>;
  }

  if (invitationData && !isLoading && isTokenValid) {
    return (
      <UnathenticatedLayout>
        {/* Fond dégradé pour toute la page */}
        <div className="min-h-screen bg-gradient-to-br from-blue-400 via-teal-400 to-green-400 py-12 md:px-4">
          <div className="md:container md:mx-auto max-w-4xl">
            {/* Titre principal centré */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8"
            >
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
                Inscription Organisme
              </h1>
              <p className="text-xl text-white/90 max-w-2xl mx-auto">
                Finalisez la création de votre compte organisme pour accéder à
                la plateforme MEDDoC
              </p>
            </motion.div>

            {/* Carte principale avec design moderne */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-3xl shadow-2xl overflow-hidden"
            >
              <div className="p-8 md:p-12">
                {/* Informations sur l'invitation */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="text-center mb-8"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full mb-4">
                    <svg
                      className="w-8 h-8 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6"
                      />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-2">
                    Créez votre compte organisme
                  </h2>
                  <p className="text-gray-600">
                    Vous avez été invité à rejoindre la plateforme MEDDoC.
                    Complétez les informations ci-dessous pour finaliser votre
                    inscription.
                  </p>
                </motion.div>

                {/* Formulaire d'inscription */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                >
                  <DashInscriptionForm
                    invitationId={invitationData?.id}
                    isInvitationUsed={invitationData?.est_utilisee}
                    defaultValue={invitationData}
                  />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </UnathenticatedLayout>
    );
  }

  return <></>;
};

export default RegisterDash;
