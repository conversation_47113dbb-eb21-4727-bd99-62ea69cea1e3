import { useEffect, useMemo, useState } from "react";
import { Box } from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";

import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import AddEmployedModal from "@/presentation/components/common/Modal/AddEmployedModal";
import { sexe_enum } from "@/domain/models/enums";
import {
  EmployeesSearchControls,
  EmployeesStatsCards,
  EmployeesGrid,
  EmployeesMobileControl,
  EmployeesSearchBar,
} from "@/presentation/components/features/dash/employees";
import usePageConfig from "@/presentation/hooks/usePageConfig";
import useAvailableDirections from "./useAvailableDirections";
import PageTitle from "@/presentation/components/common/PageTitle";

// icon
import logoDass from "@/assets/cua/cua.png";

const MyEmployees = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [totalEmployeesMale, setTotalEmployeesMale] = useState(0);
  const [totalEmployeesFemale, setTotalEmployeesFemale] = useState(0);
  const [genderFilter, setGenderFilter] = useState<"all" | "homme" | "femme">(
    "all"
  );
  const [directionFilter, setDirectionFilter] = useState<string>("all");
  const dashId = useAppSelector((state) => state.authentification.userData?.id);
  const [isAddEmployeeFormOpen, setIsAddEmployeeFormOpen] =
    useState<boolean>(false);

  const employerType = useMemo(() => {
    if (location.pathname.includes("/actif")) return "actif";
    if (location.pathname.includes("/decede")) return "decede";
    if (location.pathname.includes("/supprimer")) return "supprimer";
    return "all";
  }, [location.pathname]);

  const { employers, getAll } = useEmployer();

  const handleAddEmployedOpen = () => {
    setIsAddEmployeeFormOpen(true);
  };

  // filtrer employees
  const filteredEmployees = useMemo(() => {
    return (
      employers?.filter((employee) => {
        const matchesSearch =
          employee.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
          employee.prenom.toLowerCase().includes(searchQuery.toLowerCase()) ||
          employee.matricule
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          employee.direction.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesGender =
          genderFilter === "all" || employee.sexe === genderFilter;

        const matchesDirection =
          directionFilter === "all" || employee.direction === directionFilter;
        const employerArchive = employee.est_supprimee;
        const matchesStatus =
          employerType === "all" ||
          (employerType === "actif" && !employee.decede && !employerArchive) ||
          (employerType === "decede" && employee.decede) ||
          (employerType === "supprimer" && employee.est_supprimee);

        return (
          matchesSearch && matchesGender && matchesDirection && matchesStatus
        );
      }) || []
    );
  }, [employers, searchQuery, genderFilter, directionFilter, employerType]);

  // Obtenir la liste unique des directions
  const availableDirections = useAvailableDirections();

  useEffect(() => {
    if (dashId) {
      getAll(dashId);
    }
  }, [dashId, getAll]);

  useEffect(() => {
    if (filteredEmployees) {
      const totalMale = filteredEmployees.filter(
        (employee) => employee.sexe === sexe_enum.homme
      ).length;
      const totalFemale = filteredEmployees.filter(
        (employee) => employee.sexe === sexe_enum.femme
      ).length;
      setTotalEmployeesMale(totalMale);
      setTotalEmployeesFemale(totalFemale);
    }
  }, [filteredEmployees]);
  
  // Titre et icône dynamiques
  const pageConfig = usePageConfig(employerType);
  
  return (
    <Box className="w-full">
      {/* Entete */}
       <PageTitle  
        titre= "Gestion des Employés - DASS/CUA"
        description="Base de données complète du personnel et suivi médicale"
        logo={logoDass}
        />
      {/* Barre de recherche et contrôles modernes */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 sm:p-6 mb-6 transition-colors duration-200">
        {/* Version mobile - layout vertical */}
        <div className="block sm:hidden space-y-4">
          {/* Barre de recherche mobile */}
          <EmployeesSearchBar
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            />

          {/* Contrôles mobile */}
          <EmployeesMobileControl
            genderFilter={genderFilter}
            setGenderFilter={setGenderFilter}
            directionFilter={directionFilter}
            setDirectionFilter={setDirectionFilter}
            availableDirections={availableDirections}
            handleAddEmployedOpen={handleAddEmployedOpen}
          />
        </div>

        {/* Version desktop - layout horizontal */}
        <EmployeesSearchControls
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          genderFilter={genderFilter}
          setGenderFilter={setGenderFilter}
          directionFilter={directionFilter}
          setDirectionFilter={setDirectionFilter}
          availableDirections={availableDirections}
          onAddEmployee={handleAddEmployedOpen}
        />
      </div>

      {/* Cartes de statistiques */}
      <EmployeesStatsCards
        totalEmployees={filteredEmployees.length}
        totalEmployeesMale={totalEmployeesMale}
        totalEmployeesFemale={totalEmployeesFemale}
        totalDirections={availableDirections.length}
      />

      {/* Liste des employés */}
      <EmployeesGrid
        employees={filteredEmployees}
        searchQuery={searchQuery}
        pageConfig={pageConfig}
      />

      {isAddEmployeeFormOpen && (
        <AddEmployedModal
          isOpen={isAddEmployeeFormOpen}
          setIsOpen={setIsAddEmployeeFormOpen}
        />
      )}
    </Box>
  );
};

export default MyEmployees;
