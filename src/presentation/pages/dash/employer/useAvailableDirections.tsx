import { useAppSelector } from "@/presentation/hooks/redux";
import { useMemo } from "react";

const useAvailableDirections = () => {
  const { employers } = useAppSelector((state) => state.employer);
  const availableDirections = useMemo(() => {
    if (!employers) return [];
    const directions = employers.map((employee) => employee.direction);
    return [...new Set(directions)].sort();
  }, [employers]);

  return availableDirections;
};

export default useAvailableDirections;
