import { Box } from "@mui/material";
import CarnetDeSante from "@/presentation/pages/professional/patients/CarnetDeSante";
import HealthRecord from "@/presentation/components/features/patient/PatientCard/HealthRecord";
import ConsultationMedicale from "@/presentation/pages/professional/patients/ConsultationMedicale";
import SigneVitaux from "@/presentation/pages/professional/patients/SigneVitaux";
import { active_tab_enum } from "@/domain/models/enums";
import Facturation from "@/presentation/pages/professional/patients/Facturation";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";
import Diagnostic from "../../professional/patients/Diagnostic";
import PharmacieEtMedicament from "../../professional/patients/PharmacieEtMedicament";
import { useEffect } from "react";
import { useParams } from "react-router-dom";
import useAuth from "@/presentation/hooks/use-auth";
import PageTitle from "@/presentation/components/common/PageTitle";

const RenderEmployedInfo = (activeTab: active_tab_enum) => {
  switch (activeTab) {
    case active_tab_enum.carnetDeSante:
      return (
        <CarnetDeSante className="grid grid-cols-1 lg:grid-cols-2 gap-4" />
      );
    case active_tab_enum.consultationMedicale:
      return <ConsultationMedicale />;
    case active_tab_enum.signeVitaux:
      return <SigneVitaux />;
    case active_tab_enum.diagnostic:
      return <Diagnostic />;
    case active_tab_enum.pharmacie:
      return <PharmacieEtMedicament />;
    default:
      return <p>Aucune rendue disponible</p>;
  }
};

const EmployedInfo = () => {
  const { activeTab, resetActiveTab } = useCarnetDeSanteState();
  const { id } = useParams();
  const { getRoleUserSelected } = useAuth();
  useEffect(() => {
    if (id) {
      getRoleUserSelected(Number(id));
    }
    resetActiveTab();
  }, [id]);
  return (
    <Box className="w-full">
      <PageTitle
        titre="Informations"
        description="Toutes les informations sur l’employé et son proche "
      />
      <HealthRecord />
      {RenderEmployedInfo(activeTab)}
    </Box>
  );
};

export default EmployedInfo;
