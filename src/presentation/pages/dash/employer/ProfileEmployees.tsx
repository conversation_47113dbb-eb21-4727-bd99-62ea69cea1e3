import { utilisate<PERSON>_role_enum } from "@/domain/models/enums";
import ContactUrgence from "@/presentation/components/features/patient/registerPatienStepper/AutresInformations/ContactUrgence";
import DonneurSang from "@/presentation/components/features/patient/registerPatienStepper/AutresInformations/DonneurSang";
import GroupeSanguin from "@/presentation/components/features/patient/registerPatienStepper/AutresInformations/GroupeSanguin";
import Nationalite from "@/presentation/components/features/patient/registerPatienStepper/AutresInformations/Nationalite";
import NumeroTelephone from "@/presentation/components/features/patient/registerPatienStepper/AutresInformations/NumeroTelephone";
import Pays from "@/presentation/components/features/patient/registerPatienStepper/AutresInformations/Pays";
import RegisterStep1Birthday from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Birthday";
import RegisterStep1Email from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Email";
import RegisterStep1FirstName from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1FirstName";
import RegisterStep1LastName from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1LastName";
import RegisterStep1Password from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Password";
import RegisterStep1Sexe from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Sexe";
import RegisterStep2Adresse from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2Adresse";
import RegisterStep2Commune from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2Commune";
import RegisterStep2District from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2District";
import RegisterStep2Fokontany from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2Fokontany";
import RegisterStep3NbEnfant from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep3/RegisterStep3NbEnfant";
import RegisterStep3Profession from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep3/RegisterStep3Profession";
import RegisterStep3SituationMatrimoniale from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep3/RegisterStep3SituationMatrimoniale";
import RegisterStep3Telephones from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep3/RegisterStep3Telephones";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { useUrgence } from "@/presentation/hooks/useUrgence";
import { PRIMARY } from "@/shared/constants/Color";
import { Divider, Box, Button } from "@mui/material";
import { useParams } from "react-router-dom";

const ProfileEmployed = ({ handleBack }: { handleBack?: () => void }) => {
  const { id } = useParams();
  const patientId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { loading, handleUpdatePatient } = useCarnetDeSante();
  const { create: createUrgence } = useUrgence();
  const { getPatientData, getContactUrgence } = useRegisterPatientState();
  const handleSubmit = async () => {
    const data = getPatientData();
    const contactUrgence = getContactUrgence(
      role === utilisateurs_role_enum.PATIENT ? patientId : Number(id)
    );
    await handleUpdatePatient(Number(patientId), data);
    if (contactUrgence.length > 0) {
      await createUrgence(contactUrgence);
    }
    if (handleBack) {
      handleBack();
    }
  };

  return (
    <>
      {handleBack && (
        <Button
          variant="contained"
          color="primary"
          sx={{
            textTransform: "none",
            marginTop: 1,
            ml: 2,
            borderRadius: 5,
            backgroundColor: PRIMARY,
          }}
          onClick={handleBack}
        >
          retour
        </Button>
      )}
      <Box>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-4">
          <RegisterStep1FirstName />
          <RegisterStep1LastName />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <RegisterStep1Sexe />
          <RegisterStep1Birthday />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <RegisterStep1Email />
          <RegisterStep1Password />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <RegisterStep2Adresse />
          <RegisterStep3SituationMatrimoniale />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <RegisterStep3Profession />
          <RegisterStep3NbEnfant />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <Pays />
          <NumeroTelephone />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <RegisterStep2District />
          <RegisterStep2Commune />
          <RegisterStep2Fokontany />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <GroupeSanguin />
          <Nationalite />
        </div>
        <div className="grid grid-cols-1 gap-4 mb-4">
          <DonneurSang />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <RegisterStep3Telephones />
        </div>
        <div className="mb-4">
          <ContactUrgence />
        </div>
        <Divider />
        <div className="flex justify-end space-x-4 mt-4">
          <Button
            variant="outlined"
            color="primary"
            sx={{ textTransform: "none", borderRadius: 5 }}
            onClick={handleBack}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            color="primary"
            sx={{
              textTransform: "none",
              borderRadius: 5,
              backgroundColor: PRIMARY,
            }}
            loading={loading}
            onClick={handleSubmit}
          >
            Enregistrer
          </Button>
        </div>
      </Box>
    </>
  );
};

export default ProfileEmployed;
