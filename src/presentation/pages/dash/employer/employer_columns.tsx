import { Employer } from "@/domain/models";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { status_administratif_enum } from "@/domain/models/enums";
import {
  <PERSON><PERSON>,
  Person,
  Star,
  Cake,
  Work,
  Business,
  CalendarToday,
  CheckCircle,
  People,
  MoreVert,
  Edit,
  Delete
} from "@mui/icons-material";
import { IconButton, Tooltip } from "@mui/material";
import { useState } from "react";
import { EditEmployedModal } from "@/presentation/components/common/Modal/EditEmployedModal.tsx";
import { DeleteEmployedModal } from "@/presentation/components/common/Modal/DeleteEmployedModal.tsx";
import { Edit2, SquarePen, Trash2 } from "lucide-react";

export const EmployerColumns = (): GridColDef[] => {
  return [
    {
      field: "matricule",
      headerName: "Matricule",
      width: 120,
      headerAlign: 'center',
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <Badge sx={{ fontSize: 18 }} />
          <span>Matricule</span>
        </div>
      ),
    },
    {
      field: "nom",
      headerName: "Nom",
      width: 200,
      renderCell: (params: GridRenderCellParams<Employer>) => `${params.row.nom}`,
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <Person sx={{ fontSize: 18 }} />
          <span>Nom</span>
        </div>
      ),
    },
    {
      field: "prenom",
      headerName: "Prénom",
      width: 180,
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <Star sx={{ fontSize: 18 }} />
          <span>Prénom</span>
        </div>
      ),
    },
    {
      field: "date_de_naissance",
      headerName: "Date de naissance",
      width: 160,
      valueFormatter: (params) => new Date(params).toLocaleString(
        "fr-FR",
        {
          year: "numeric",
          month: "long",
          day: "numeric",
        }
      ),
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <Cake sx={{ fontSize: 18 }} />
          <span>Date de naissance</span>
        </div>
      ),
    },
    {
      field: "fonction",
      headerName: "Fonction",
      width: 200,
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <Work sx={{ fontSize: 18 }} />
          <span>Fonction</span>
        </div>
      ),
    },
    {
      field: "direction",
      headerName: "Direction",
      width: 180,
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <Business sx={{ fontSize: 18 }} />
          <span>Direction</span>
        </div>
      ),
    },
    {
      field: "date_entree_en_fonction",
      headerName: "Date d'entrée",
      width: 150,
      valueFormatter: (params) => new Date(params).toLocaleString(
        "fr-FR",
        {
          year: "numeric",
          month: "long",
          day: "numeric",
        }
      ),
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <CalendarToday sx={{ fontSize: 18 }} />
          <span>Date d'entrée</span>
        </div>
      ),
    },
    {
      field: "status_administratif",
      headerName: "Statut",
      width: 150,
      renderCell: (params: GridRenderCellParams<Employer>) => (
        <span className={`px-2 py-1 rounded text-sm ${params.row.status_administratif === status_administratif_enum.PERMANENT
          ? 'bg-green-100 text-green-800'
          : 'bg-gray-100 text-gray-800'
          }`}>
          {params.row.status_administratif}
        </span>
      ),
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <CheckCircle sx={{ fontSize: 18 }} />
          <span>Statut</span>
        </div>
      ),
    },
    {
      field: "sexe",
      headerName: "Sexe",
      width: 100,
      headerAlign: 'center',
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <People sx={{ fontSize: 18 }} />
          <span>Sexe</span>
        </div>
      ),
    },
    // Nouvelle colonne Actions
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      headerAlign: 'center',
      align: 'center',
      renderHeader: () => (
        <div className="flex items-center gap-2">
          <MoreVert sx={{ fontSize: 18 }} />
          <span>Actions</span>
        </div>
      ),
      renderCell: (params: GridRenderCellParams<Employer>) => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isEditModalOpen, setIsEditModalOpen] = useState(false);

        const handleEditClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          setIsEditModalOpen(true);
        };

        const handleDeleteClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          setIsDeleteModalOpen(true);
        };

        return (
          <div className="flex items-center justify-center gap-1 my-2">
            <Tooltip title="Modifier" placement="top">
              <IconButton
                onClick={handleEditClick}
                sx={{
                  color: '#3b82f6',
                  '&:hover': {
                    backgroundColor: '#dbeafe',
                    color: '#1d4ed8'
                  }
                }}
              >
                <SquarePen size={16} />
              </IconButton>
            </Tooltip>

            <Tooltip title="Supprimer" placement="top">
              <IconButton
                onClick={handleDeleteClick}
                sx={{
                  color: '#ef4444',
                  '&:hover': {
                    backgroundColor: '#fef2f2',
                    color: '#dc2626'
                  }
                }}
              >
                <Trash2 size={16} />
              </IconButton>
            </Tooltip>

            {/* Modales */}
            {isEditModalOpen && (
              <EditEmployedModal
                employed={params.row}
                isOpen={isEditModalOpen}
                handleCloseModal={() => setIsEditModalOpen(false)}
              />
            )}

            {isDeleteModalOpen && (
              <DeleteEmployedModal
                isOpen={isDeleteModalOpen}
                employed={params.row}
                handleClose={() => setIsDeleteModalOpen(false)}
              />
            )}
          </div>
        );
      },
    },
  ];
};

