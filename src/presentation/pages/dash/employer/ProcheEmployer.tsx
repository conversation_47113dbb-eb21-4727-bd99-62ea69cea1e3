import { useEffect, useMemo, useState } from "react";
import {
  Box,
  TextField,
  Card,
  CardContent,
  Typography,
  FormControl,
  Select,
  MenuItem,
  InputAdornment,
} from "@mui/material";
import { Plus, Users, User, Filter, Search, UsersIcon } from "lucide-react";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useNavigate } from "react-router-dom";
import { DashRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { sexe_enum } from "@/domain/models/enums";
import { AddProcheModal } from "@/presentation/components/common/Modal/AddProchePatientModal";
import { useProche } from "@/presentation/hooks/use-proches";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import ProcheEmployerCard from "@/presentation/components/features/proche/procheCard/ProcheEmployerCard";
import PageTitle from "@/presentation/components/common/PageTitle";
// icon
import logoDass from "@/assets/cua/cua.png";
const ProcheEmployer = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [totalProchesMale, setTotalProchesMale] = useState(0);
  const [totalProchesFemale, setTotalProchesFemale] = useState(0);
  const [allId, setAllId] = useState<number[]>([]);
  const [genderFilter, setGenderFilter] = useState<"all" | "homme" | "femme">(
    "all"
  );
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );

  const { procheEmployer, handleGetProcheEmployer } = useProche();
  const { employers, getAll: getEmployers } = useEmployer();

  const handleAddProcheModalOpen = () => {
    setIsOpen(true);
  };

  // filtrer proches
  const filteredProches = useMemo(() => {
    return (
      procheEmployer?.filter((patient) => {
        const matchesSearch =
          patient.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
          patient.prenom.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesGender =
          genderFilter === "all" || patient.sexe === genderFilter;

        return matchesSearch && matchesGender;
      }) || []
    );
  }, [procheEmployer, searchQuery, genderFilter]);

  useEffect(() => {
    if (professionalId) {
      getEmployers(professionalId);
    }
  }, [professionalId]);

  useEffect(() => {
    if (employers) {
      setAllId(employers.map((employer) => employer.id_utilisateur)); // recuperer tous les id_utilisateur venant
    }
  }, [employers]);

  useEffect(() => {
    if (allId.length != 0) {
      handleGetProcheEmployer(allId);
    }
  }, [allId]);

  useEffect(() => {
    if (filteredProches) {
      const totalMale = filteredProches.filter(
        (patient) => patient.sexe === sexe_enum.homme
      ).length;
      const totalFemale = filteredProches.filter(
        (patient) => patient.sexe === sexe_enum.femme
      ).length;
      setTotalProchesMale(totalMale);
      setTotalProchesFemale(totalFemale);
    }
  }, [filteredProches]);

  const navigate = useNavigate();

  return (
    <Box className="w-full ">
      <PageTitle titre="Liste des proches" description="Gestion des proches" logo={logoDass} />
      {/* Barre de recherche et contrôles modernes */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 sm:p-6 mb-6 transition-colors duration-200">
        {/* Version mobile - layout vertical */}
        <div className="block sm:hidden space-y-4">
          {/* Barre de recherche mobile */}
          <div className="relative">
            <TextField
              fullWidth
              placeholder="Rechercher un patient..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search
                      size={18}
                      className="text-gray-400 dark:text-gray-500"
                    />
                  </InputAdornment>
                ),
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "12px",
                  backgroundColor: "#f8fafc",
                  border: "1px solid #e2e8f0",
                  transition: "all 0.2s ease",
                  color: "#1f2937",
                  "& fieldset": {
                    border: "none",
                  },
                  "&:hover": {
                    backgroundColor: "#f1f5f9",
                    borderColor: "#cbd5e1",
                  },
                  "&.Mui-focused": {
                    backgroundColor: "#ffffff",
                    borderColor: "#27aae1",
                    boxShadow: "0 0 0 3px rgba(39, 170, 225, 0.1)",
                  },
                },
                "& .MuiInputBase-input": {
                  "&::placeholder": {
                    color: "#9ca3af",
                    opacity: 1,
                  },
                },
              }}
              className="[&_.MuiOutlinedInput-root]:dark:!bg-gray-700 [&_.MuiOutlinedInput-root]:dark:!border-gray-600 [&_.MuiOutlinedInput-root]:dark:!text-gray-100 [&_.MuiOutlinedInput-root:hover]:dark:!bg-gray-600 [&_.MuiOutlinedInput-root.Mui-focused]:dark:!bg-gray-700 [&_.MuiInputBase-input::placeholder]:dark:!text-gray-400"
            />
          </div>

          {/* Contrôles mobile */}
          <div className="flex gap-3">
            <FormControl size="small" sx={{ flex: 1 }}>
              <Select
                value={genderFilter}
                onChange={(e) =>
                  setGenderFilter(e.target.value as "all" | "homme" | "femme")
                }
                displayEmpty
                sx={{
                  borderRadius: "12px",
                  backgroundColor: "#f8fafc",
                  border: "1px solid #e2e8f0",
                  color: "#1f2937",
                  "& .MuiOutlinedInput-notchedOutline": {
                    border: "none",
                  },
                  "&:hover": {
                    backgroundColor: "#f1f5f9",
                  },
                  "&.Mui-focused": {
                    backgroundColor: "#ffffff",
                    borderColor: "#27aae1",
                    boxShadow: "0 0 0 3px rgba(39, 170, 225, 0.1)",
                  },
                }}
                className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
              >
                <MenuItem value="all">
                  <div className="flex items-center gap-2">
                    <Filter
                      size={16}
                      className="text-gray-500 dark:text-gray-400"
                    />
                    <span className="text-gray-700 dark:text-gray-200">
                      Tous
                    </span>
                  </div>
                </MenuItem>
                <MenuItem value="homme">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 dark:text-gray-200">
                      Hommes
                    </span>
                  </div>
                </MenuItem>
                <MenuItem value="femme">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                    <span className="text-gray-700 dark:text-gray-200">
                      Femmes
                    </span>
                  </div>
                </MenuItem>
              </Select>
            </FormControl>

            <button
              onClick={handleAddProcheModalOpen}
              className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-4 py-2 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center gap-2 whitespace-nowrap"
            >
              <Plus size={18} />
              <span>Ajouter</span>
            </button>
          </div>
        </div>

        {/* Version desktop - layout horizontal */}
        <div className="hidden sm:flex items-center gap-4">
          {/* Barre de recherche desktop */}
          <div className="flex-1 max-w-md">
            <TextField
              fullWidth
              placeholder="Rechercher un proche par nom ou prénom"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              size="medium"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search
                      size={20}
                      className="text-gray-400 dark:text-gray-500"
                    />
                  </InputAdornment>
                ),
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "16px",
                  backgroundColor: "#f8fafc",
                  border: "1px solid #e2e8f0",
                  transition: "all 0.3s ease",
                  fontSize: "0.95rem",
                  color: "#1f2937",
                  "& fieldset": {
                    border: "none",
                  },
                  "&:hover": {
                    backgroundColor: "#f1f5f9",
                    borderColor: "#cbd5e1",
                    transform: "translateY(-1px)",
                  },
                  "&.Mui-focused": {
                    backgroundColor: "#ffffff",
                    borderColor: "#27aae1",
                    boxShadow: "0 0 0 4px rgba(39, 170, 225, 0.1)",
                    transform: "translateY(-1px)",
                  },
                },
                "& .MuiInputBase-input": {
                  "&::placeholder": {
                    color: "#9ca3af",
                    opacity: 1,
                  },
                },
              }}
              className="[&_.MuiOutlinedInput-root]:dark:!bg-gray-700 [&_.MuiOutlinedInput-root]:dark:!border-gray-600 [&_.MuiOutlinedInput-root]:dark:!text-gray-100 [&_.MuiOutlinedInput-root:hover]:dark:!bg-gray-600 [&_.MuiOutlinedInput-root.Mui-focused]:dark:!bg-gray-700 [&_.MuiInputBase-input::placeholder]:dark:!text-gray-400"
            />
          </div>

          {/* Filtre desktop */}
          <FormControl size="medium" sx={{ minWidth: 140 }}>
            <Select
              value={genderFilter}
              onChange={(e) =>
                setGenderFilter(e.target.value as "all" | "homme" | "femme")
              }
              displayEmpty
              sx={{
                borderRadius: "16px",
                backgroundColor: "#f8fafc",
                border: "1px solid #e2e8f0",
                transition: "all 0.3s ease",
                color: "#1f2937",
                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none",
                },
                "&:hover": {
                  backgroundColor: "#f1f5f9",
                  borderColor: "#cbd5e1",
                  transform: "translateY(-1px)",
                },
                "&.Mui-focused": {
                  backgroundColor: "#ffffff",
                  borderColor: "#27aae1",
                  boxShadow: "0 0 0 4px rgba(39, 170, 225, 0.1)",
                },
              }}
              className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
            >
              <MenuItem value="all">
                <div className="flex items-center gap-2">
                  <Filter
                    size={16}
                    className="text-gray-500 dark:text-gray-400"
                  />
                  <span className="text-gray-700 dark:text-gray-200">
                    Tous les proches
                  </span>
                </div>
              </MenuItem>
              <MenuItem value="homme">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-200">
                    Proches hommes
                  </span>
                </div>
              </MenuItem>
              <MenuItem value="femme">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-200">
                    Proches femmes
                  </span>
                </div>
              </MenuItem>
            </Select>
          </FormControl>

          {/* Bouton d'ajout desktop */}
          <button
            onClick={handleAddProcheModalOpen}
            className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-6 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3 whitespace-nowrap group"
          >
            <Plus
              size={20}
              className="group-hover:rotate-90 transition-transform duration-300"
            />
            <span className="hidden md:inline">Ajouter une proche</span>
            <span className="md:hidden">Ajouter</span>
          </button>
        </div>
      </div>

      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        {/* Total Proches */}
        <Card
          sx={{
            borderRadius: "8px",
            boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
            backgroundColor: "white",
          }}
          className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: "0.7rem",
                    lineHeight: 1,
                    color: "#6b7280",
                  }}
                  className="dark:!text-gray-400"
                >
                  TOTAL PROCHES
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: "bold",
                    color: "#2c3e50",
                    mt: 0.25,
                  }}
                  className="dark:!text-gray-100"
                >
                  {filteredProches.length}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: "8px",
                  backgroundColor: "#4285f4",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 2px 8px rgba(66, 133, 244, 0.3)",
                }}
              >
                <Users size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>

        {/* Hommes */}
        <Card
          sx={{
            borderRadius: "8px",
            boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
            backgroundColor: "white",
          }}
          className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: "0.7rem",
                    lineHeight: 1,
                    color: "#6b7280",
                  }}
                  className="dark:!text-gray-400"
                >
                  HOMMES
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: "bold",
                    color: "#2c3e50",
                    mt: 0.25,
                  }}
                  className="dark:!text-gray-100"
                >
                  {totalProchesMale}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: "8px",
                  backgroundColor: "#6c5ce7",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 2px 8px rgba(108, 92, 231, 0.3)",
                }}
              >
                <User size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>

        {/* Femmes */}
        <Card
          sx={{
            borderRadius: "8px",
            boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
            backgroundColor: "white",
          }}
          className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: "0.7rem",
                    lineHeight: 1,
                    color: "#6b7280",
                  }}
                  className="dark:!text-gray-400"
                >
                  FEMMES
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: "bold",
                    color: "#2c3e50",
                    mt: 0.25,
                  }}
                  className="dark:!text-gray-100"
                >
                  {totalProchesFemale}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: "8px",
                  backgroundColor: "#fd79a8",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 2px 8px rgba(253, 121, 168, 0.3)",
                }}
              >
                <User size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des proches */}
      <div className="mb-4">
        <div className="mx-2 mb-10">
          <div className="flex items-center gap-3 mb-2 ">
            <UsersIcon className="text-meddoc-primary" />
            <Typography
              variant="h1"
              className="font-bol text-meddoc-primary"
              sx={{ fontSize: "1.2rem" }}
            >
              Tous les Proches ({filteredProches.length})
            </Typography>
          </div>
          <Typography
            variant="body1"
            className="text-gray-600 dark:text-gray-400"
            sx={{ fontSize: "0.95rem" }}
          >
            Liste complète des proches patient
          </Typography>
        </div>

        {filteredProches.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={8}
            sx={{
              backgroundColor: "rgba(0, 0, 0, 0.02)",
              borderRadius: 2,
              border: "1px dashed rgba(0, 0, 0, 0.12)",
            }}
            className="dark:!bg-white/5 dark:!border-white/20"
          >
            <Typography
              variant="h6"
              sx={{
                color: "#6b7280",
              }}
              className="dark:!text-gray-400"
              gutterBottom
            >
              Aucun proche trouvé
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "#6b7280",
              }}
              className="dark:!text-gray-400"
              textAlign="center"
            >
              {searchQuery
                ? "Aucun proche ne correspond à votre recherche"
                : "Vous n'avez aucun proche pour le moment"}
            </Typography>
          </Box>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredProches.map((proche) => (
              <div
                style={{ cursor: "pointer" }}
                key={proche.id}
                onClick={() =>
                  navigate(
                    `/${DashRoutesNavigation.PROCHE_EMPLOYER.split("/:id")[0]}/${proche.utilisateur_id}`
                  )
                }
              >
                <ProcheEmployerCard proche={proche} />
              </div>
            ))}
          </div>
        )}
      </div>
      {isOpen && (
        <AddProcheModal
          employer={employers?.map((employer) => employer)}
          isProcheModalOpen={isOpen}
          handleCloseModal={() => setIsOpen(false)}
        />
      )}
    </Box>
  );
};

export default ProcheEmployer;
