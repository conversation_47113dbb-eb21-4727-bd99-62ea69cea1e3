import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import AddEmployedModal from "@/presentation/components/common/Modal/AddEmployedModal";
import AddEventModal from "@/presentation/components/common/Modal/AddEventModal";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { formatDate } from "@/presentation/pages/patient/profilePatients/formatDate";
import useDashboardDass from "@/presentation/hooks/dashboard/dass/use-dashboard-dass";
import { motion } from "framer-motion";
import {
  Users,
  Building2,
  UserPlus,
  TrendingUp,
  Calendar,
  Bell,
  FileText,
  BarChart3,
  Pie<PERSON>hart as PieChartIcon,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Download,
  Filter,
  Heart,
  Pill,
  Edit,
} from "lucide-react";
import { useMemo, useState, useRef } from "react";
import { format } from "date-fns";
import LogoDass from "@/presentation/components/features/dash/dashboard/LogoDass";
import PrincipauxMedicaments from "@/presentation/components/features/dash/dashboard/PrincipauxMedicaments";
import PrincipalesPathologies from "@/presentation/components/features/dash/dashboard/PrincipalesPathologies";
import PatientsActifs from "@/presentation/components/features/dash/dashboard/PatientsActifs";
import RepartitionParDirection from "@/presentation/components/features/dash/dashboard/RepartitionParDirection";
import { StatistiqueDassModal } from "@/presentation/components/common/Modal/StatistiqueDassModal";
import PageTitle from "@/presentation/components/common/PageTitle";
import logoDass from "@/assets/cua/cua.png";

const Dashboard = () => {
  const { isAddEventModalOpen, handleIsAddEventModalOpen } = useAgendaState();
  const {
    isAddEmployeeFormOpen,
    isStatistiqueDassModal,
    setIsAddEmployeeFormOpen,
    setIsStatistiqueDassModal,
    stats,
    employers,
    availableDirections,
    top5Maladies,
    top5Medicaments,
    isLoadingMaladies,
    isLoadingMedicament,
    upcomingEvents,
    recentEmploye,
    recentEmployeUpdate,
    totalConsultationsThisMonth,
  } = useDashboardDass();

  // Couleurs pour le graphique
  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#A569BD",
    "#5DADE2",
    "#45B39D",
    "#F4D03F",
    "#DC7633",
    "#EC7063",
  ];
  // Transformation des données réelles de pathologies
  const pathologiesData = useMemo(() => {
    if (!top5Maladies || top5Maladies.length === 0) {
      return [];
    }

    return top5Maladies.map((maladie, index) => ({
      name: maladie.maladie.replace(",", ""),
      count: maladie.repetitions,
      color: COLORS[index] || COLORS[0],
    }));
  }, [top5Maladies]);

  // Transformation des données réelles de médicaments
  const medicamentsData = useMemo(() => {
    if (!top5Medicaments || top5Medicaments.length === 0) {
      return [];
    }

    return top5Medicaments.map((medicament, index) => ({
      name: medicament.nom.replace(",", ""),
      count: medicament.repetitions,
      color: COLORS[index] || COLORS[0],
    }));
  }, [top5Medicaments]);

  const [activeEmployeTab, setActiveEmployeTab] = useState("recent");
  const [showAllEvents, setShowAllEvents] = useState(false);

  return (
    <div className="min-h-screen">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <PageTitle
          titre="Tableau de Bord - CUA/DASS"
          description="Vue d'ensemble de la gestion des ressources humaines et de la santé"
          logo={logoDass}
        />
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"
        >
          {/* Total Employés */}
          <motion.div
            whileHover={{ scale: 1.02, y: -5 }}
            className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 dark:text-gray-400 text-sm font-bold">
                  Total Employés
                </p>
                <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  {stats.totalEmployees.toLocaleString()}
                </p>
                <div className="flex items-center gap-1 mt-2">
                  <span className="text-gray-500 text-xs">
                    Nombre total d'employés en activité dans l'institution
                  </span>
                </div>
              </div>
              <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                <Users size={24} className="text-meddoc-primary" />
              </div>
            </div>
          </motion.div>

          {/* Total Directions */}
          <motion.div
            whileHover={{ scale: 1.02, y: -5 }}
            className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 dark:text-gray-400 text-sm font-bold">
                  Nombres de directions
                </p>
                <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  {stats.totalDirections}
                </p>
                <div className="flex items-center gap-1 mt-2">
                  <span className="text-gray-500 text-xs">
                    Nombre de directions opérationnelles ayant du personnel en
                    service
                  </span>
                </div>
              </div>
              <div className="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-lg">
                <Building2 size={24} className="text-purple-600" />
              </div>
            </div>
          </motion.div>

          {/* Nouveaux Employés */}
          <motion.div
            whileHover={{ scale: 1.02, y: -5 }}
            className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 dark:text-gray-400 text-sm font-bold">
                  Consultations ce mois
                </p>
                <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  {totalConsultationsThisMonth}
                </p>
                <div className="flex items-center gap-1 mt-2">
                  <span className="text-gray-500 text-xs">
                    Total des consultations médicales réalisées au cours du mois
                    en cours
                  </span>
                </div>
              </div>
              <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                <UserPlus size={24} className="text-green-600" />
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Actions Rapides */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Actions Rapides
            </h2>
            <Filter size={20} className="text-gray-400" />
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-meddoc-primary to-meddoc-primary/80 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              onClick={() => setIsAddEmployeeFormOpen(true)}
              title="Enregistrer un nouveau membre du personnel"
            >
              <Plus size={24} />
              <span className="text-sm font-medium">Ajouter Employé</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              title="Générer les rapports (en version PDF)
"
            >
              <FileText size={24} />
              <span className="text-sm font-medium">Générer Rapport</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-green-500 to-green-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              onClick={() => setIsStatistiqueDassModal(true)}
              title="Consulter les données détaillées"
            >
              <BarChart3 size={24} />
              <span className="text-sm font-medium">Statistiques</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-orange-500 to-orange-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              onClick={() => handleIsAddEventModalOpen(true)}
              title="Prochaines consultations, évènements et formations programmées"
            >
              <Calendar size={24} />
              <span className="text-sm font-medium">Agenda</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Contenu Principal - 2 colonnes */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Activité Récente */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="lg:col-span-2 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700"
          >
            {/* Employés récents - 2 colonnes */}
            <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-primary/20 dark:border-gray-700 overflow-hidden">
              <div className="px-4 py-3 bg-gradient-to-r from-meddoc-primary to-meddoc-fonce dark:from-meddoc-primary/20 dark:to-meddoc-primary/10 border-b border-meddoc-primary/20 dark:border-gray-600">
                <h2 className="text-lg font-semibold text-white dark:text-white flex items-center">
                  <Users className="w-4 h-4 mr-2 text-meddoc-primary" />
                  Liste des employés récents
                </h2>
              </div>
              {/* Onglets pour filtrer les employés */}
              <div className="flex border-b border-gray-200 dark:border-gray-700">
                <button
                  className={`flex-1 py-3 px-4 text-center font-medium text-sm border-b-2 ${activeEmployeTab === "recent" ? "border-meddoc-primary text-meddoc-primary" : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`}
                  onClick={() => setActiveEmployeTab("recent")}
                >
                  <div className="flex items-center justify-center">
                    <UserPlus className="w-4 h-4 mr-2" />
                    Créés récemment
                  </div>
                </button>
                <button
                  className={`flex-1 py-3 px-4 text-center font-medium text-sm border-b-2 ${activeEmployeTab === "updated" ? "border-meddoc-primary text-meddoc-primary" : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`}
                  onClick={() => setActiveEmployeTab("updated")}
                >
                  <div className="flex items-center justify-center">
                    <Edit className="w-4 h-4 mr-2" />
                    Mis à jour récemment
                  </div>
                </button>
              </div>
              <div className="p-4">
                {useMemo(() => {
                  if (activeEmployeTab === "recent") {
                    return (
                      <ListDataGrid
                        data={recentEmploye}
                        type="recent_employe"
                      />
                    );
                  } else if (activeEmployeTab === "updated") {
                    return (
                      <ListDataGrid
                        data={recentEmployeUpdate}
                        type="updated_employe"
                      />
                    );
                  }
                }, [activeEmployeTab, recentEmploye, recentEmployeUpdate])}
              </div>
            </div>
          </motion.div>

          {/* Événements à venir */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden"
          >
            <div className="bg-gradient-to-r from-meddoc-primary to-meddoc-fonce p-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-white">
                  Événements à venir
                </h2>
                <Calendar size={20} className="text-white/80" />
              </div>
            </div>
            <div className="p-6">
              <div
                className={`space-y-4 ${showAllEvents ? "max-h-80 overflow-y-auto pr-2" : ""}`}
              >
                {(showAllEvents
                  ? upcomingEvents
                  : upcomingEvents.slice(0, 3)
                ).map((event) => (
                  <motion.div
                    key={event.id}
                    whileHover={{ scale: 1.02 }}
                    className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {event.description}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          {formatDate(event.date_debut)} à{" "}
                          {format(new Date(event.date_debut), "HH:mm")}
                        </p>
                      </div>
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${
                          event.titre === "formation"
                            ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                            : event.titre === "reunion"
                              ? "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400"
                              : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                        }`}
                      >
                        {event.titre}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
              {upcomingEvents.length > 3 && (
                <motion.button
                  onClick={() => setShowAllEvents(!showAllEvents)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full mt-4 py-2 text-meddoc-primary hover:text-meddoc-primary/80 font-medium text-sm transition-colors duration-200"
                >
                  {showAllEvents
                    ? "Afficher moins"
                    : "Voir tous les événements"}
                </motion.button>
              )}
            </div>
          </motion.div>
        </div>
      </div>
      {isAddEmployeeFormOpen && (
        <AddEmployedModal
          isOpen={isAddEmployeeFormOpen}
          setIsOpen={setIsAddEmployeeFormOpen}
        />
      )}
      {isAddEventModalOpen && <AddEventModal />}
      {isStatistiqueDassModal && (
        <StatistiqueDassModal
          isModalOpen={isStatistiqueDassModal}
          handleCloseModal={() => setIsStatistiqueDassModal(false)}
          pathologiesData={pathologiesData}
          medicamentsData={medicamentsData}
          employers={employers}
          availableDirections={availableDirections}
          COLORS={COLORS}
        />
      )}
    </div>
  );
};

export default Dashboard;
