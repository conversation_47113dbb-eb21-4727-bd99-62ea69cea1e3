import { FC, useCallback, useState } from 'react';
import { Users, Calendar, TrendingUp, Clock } from 'lucide-react';
import StatsCard from '@/presentation/components/features/admin/stats/StatsCard';
import AppointmentsList from '@/presentation/components/features/admin/appointments/AppointmentsList';
import AppointmentsChart from '@/presentation/components/features/admin/charts/AppointmentsChart';

import { DashboardStats, AppointmentStatus } from '@/presentation/types/admin.types';

const Dashboard: FC = () => {
  // Ces données viendraient normalement d'une API
  const [stats] = useState<DashboardStats>({
    totalPatients: 1234,
    totalAppointments: 5678,
    completionRate: 92,
    averageWaitTime: '15min',
  });

  const [appointments] = useState([
    {
      id: '1',
      patientName: '<PERSON>',
      date: '28 Fév 2025',
      time: '09h00',
      status: 'confirmed' as const,
    },
    {
      id: '2',
      patientName: '<PERSON>',
      date: '28 Fév 2025',
      time: '10h30',
      status: 'pending' as const,
    },
    {
      id: '3',
      patientName: '<PERSON>',
      date: '28 Fév 2025',
      time: '14h00',
      status: 'cancelled' as const,
    },
  ]);

  const [chartData] = useState([
    { date: 'Lundi', appointments: 12 },
    { date: 'Mardi', appointments: 19 },
    { date: 'Mercredi', appointments: 15 },
    { date: 'Jeudi', appointments: 22 },
    { date: 'Vendredi', appointments: 18 },
    { date: 'Samedi', appointments: 10 },
    { date: 'Dimanche', appointments: 5 },
  ].map(item => ({
    ...item,
    // Assurer que la date est affichée sur une seule ligne
    date: item.date.substring(0, 3)
  })));

  const handleStatusChange = useCallback((id: string, status: AppointmentStatus) => {
    // Implémenter la logique de mise à jour du statut ici
    console.log('Statut modifié:', { id, status });
  }, []);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <h1 className="text-2xl font-bold mb-8">Tableau de bord administrateur</h1>
      
      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatsCard
          title="Total Patients"
          value={stats.totalPatients}
          icon={<Users className="h-6 w-6" />}
          trend={{ value: 12, isPositive: true }}
        />
        <StatsCard
          title="Total Rendez-vous"
          value={stats.totalAppointments}
          icon={<Calendar className="h-6 w-6" />}
          trend={{ value: 8, isPositive: true }}
        />
        <StatsCard
          title="Taux de réalisation"
          value={`${stats.completionRate}%`}
          icon={<TrendingUp className="h-6 w-6" />}
          trend={{ value: 3, isPositive: true }}
        />
        <StatsCard
          title="Temps d'attente moyen"
          value={stats.averageWaitTime}
          icon={<Clock className="h-6 w-6" />}
          trend={{ value: 2, isPositive: false }}
        />
      </div>

      {/* Graphique et Liste */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AppointmentsChart data={chartData} />
        <AppointmentsList
          appointments={appointments}
          onStatusChange={handleStatusChange}
        />
      </div>
    </div>
  );
};

export default Dashboard;
