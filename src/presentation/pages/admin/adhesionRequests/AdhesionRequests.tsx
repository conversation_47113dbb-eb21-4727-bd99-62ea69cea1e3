import { useEffect } from "react";
import { Box, CircularProgress, Alert } from "@mui/material";
import { AdhesionRequestHeader } from "@/presentation/components/common/adhesionRequests/AdhesionRequestHeader";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { AdhesionRequestDetailModal } from "@/presentation/components/features/admin/adhesionRequests/AdhesionRequestDetailModal";
import { useAdhesionRequestUI } from "@/presentation/hooks/adhesionRequest";

/**
 * Page d'administration des demandes d'adhésion
 * Utilise le composant ListDataGrid existant avec des hooks personnalisés
 * pour la gestion des données et des colonnes
 */
const AdhesionRequests = () => {
  // Utilisation du hook personnalisé pour la gestion de l'UI
  const {
    searchQuery,
    statusFilter,
    detailModalOpen,
    selectedRequest,
    filteredRequests,
    loading,
    error,
    setSearchQuery,
    setStatusFilter,
    handleGetAdhesionRequestList,
    handleViewDetails,
    handleMarkAsRead,
    handleApprove,
    handleReject,
    handleDelete,
    closeDetailModal,
  } = useAdhesionRequestUI();

  // Les colonnes sont gérées par le hook useAdhesionRequestUI via AdhesionRequestColumnsWithHook

  // Chargement initial des données
  useEffect(() => {
    handleGetAdhesionRequestList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Box className="p-6 mx-auto max-w-full overflow-hidden">
      <AdhesionRequestHeader
        searchQuery={searchQuery}
        statusFilter={statusFilter}
        onSearchChange={setSearchQuery}
        onStatusFilterChange={setStatusFilter}
      />

      {error && (
        <Alert severity="error" className="mb-4">
          {error}
        </Alert>
      )}

      {loading && filteredRequests.length === 0 ? (
        <Box className="flex justify-center items-center h-64">
          <CircularProgress />
        </Box>
      ) : (
        <Box className="overflow-x-auto">
          <ListDataGrid
            data={filteredRequests}
            type="adhesion_requests"
            className="overflow-y-auto w-full"
          />
        </Box>
      )}

      <AdhesionRequestDetailModal
        open={detailModalOpen}
        onClose={closeDetailModal}
        data={selectedRequest}
        onMarkAsRead={handleMarkAsRead}
        onApprove={handleApprove}
        onReject={handleReject}
        onDelete={handleDelete}
      />
    </Box>
  );
};

const AdhesionRequestsPage = AdhesionRequests;
export default AdhesionRequestsPage;
