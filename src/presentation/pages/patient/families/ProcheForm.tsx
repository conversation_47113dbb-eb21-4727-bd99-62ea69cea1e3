import {
  TextField,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { fr } from "date-fns/locale";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { sexe_enum } from "@/domain/models/enums";

const ProcheForm = () => {
  const {
    procheInfo,
    handleProcheInfoSexChange,
    handleProcheInfoFirstNameChange,
    handleProcheInfoLastNameChange,
    handleProcheInfoBirthDateChange,
    handleProcheInfoLienParenteChange,
  } = useConsultationState();
  return (
    <>
      <FormControl fullWidth margin="normal">
        <InputLabel>Sexe*</InputLabel>
        <Select
          value={procheInfo?.sexe || ""}
          onChange={(e) =>
            handleProcheInfoSexChange(e.target.value as sexe_enum)
          }
          label="Sexe*"
        >
          <MenuItem value={sexe_enum.homme}>Homme</MenuItem>
          <MenuItem value={sexe_enum.femme}>Femme</MenuItem>
        </Select>
      </FormControl>

      <TextField
        fullWidth
        label="Prénom*"
        value={procheInfo?.prenom || ""}
        onChange={(e) => handleProcheInfoFirstNameChange(e.target.value)}
        margin="normal"
      />

      <TextField
        fullWidth
        label="Nom de famille*"
        value={procheInfo?.nom || ""}
        onChange={(e) => handleProcheInfoLastNameChange(e.target.value)}
        margin="normal"
      />

      <FormControl fullWidth margin="normal">
        <InputLabel>Lien parente*</InputLabel>
        <Select
          value={procheInfo?.lien_parente || ""}
          onChange={(e) => handleProcheInfoLienParenteChange(e.target.value)}
          label="Lien parente*"
        >
          <MenuItem value="Mari">Mari (époux)</MenuItem>
          <MenuItem value="Femme">Femme (épouse)</MenuItem>
          <MenuItem value="Fils">Fils</MenuItem>
          <MenuItem value="Fille">Fille</MenuItem>
        </Select>
      </FormControl>
      <FormControl fullWidth margin="normal">
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
          <DatePicker
            label="Date de naissance*"
            sx={{ width: "100%" }}
            value={
              procheInfo?.date_naissance
                ? new Date(procheInfo.date_naissance)
                : null
            }
            onChange={(date) => {
              if (date) {
                const isoDate = date.toISOString();
                handleProcheInfoBirthDateChange(isoDate);
              }
            }}
            maxDate={new Date()}
            slotProps={{
              textField: {
                fullWidth: true,
                margin: "normal",
              },
            }}
          />
        </LocalizationProvider>
      </FormControl>
    </>
  );
};

export default ProcheForm;
