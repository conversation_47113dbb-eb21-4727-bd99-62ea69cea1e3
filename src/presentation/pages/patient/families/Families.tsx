import { useEffect, useMemo, useState } from "react";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useProche } from "@/presentation/hooks/use-proches";
import ProcheFilters from "./ProcheFilters";
import { Button, Typography } from "@mui/material";
import { Plus } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { AddProcheModal } from "@/presentation/components/common/Modal/AddProchePatientModal";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";

const Families = () => {
  const [selectedProche, setSelectedProche] = useState<{
    id: number;
    nom: string;
    prenom: string;
  }>(null);
  const [isProcheModalOpen, setIsProcheModalOpen] = useState(false);
  const { proches, handleGetProcheByPatientId } = useProche();
  const patientlId = useAppSelector((state) => state.authentification.user?.id);
  const { resetProcheData } = useConsultationState();

  const handleCloseModal = () => {
    setIsProcheModalOpen(false);
    resetProcheData(patientlId);
  };
  const filteredProches = useMemo(() => {
    return proches?.filter((prs) => {
      const matchesProches =
        selectedProche === null || prs.id === selectedProche.id;

      return matchesProches;
    });
  }, [proches, selectedProche]);

  useEffect(() => {
    if (patientlId) {
      handleGetProcheByPatientId(patientlId);
    }
  }, [patientlId]);

  return (
    <div>
      <Typography variant="h5" component="h1">
        Liste de vos proche
      </Typography>
      <div className="grid grid-cols-1 md:grid-cols-2 my-4">
        <div className="flex justify-between items-center">
          <ProcheFilters
            selectedProche={selectedProche}
            onSelectedProcheChange={setSelectedProche}
          />
        </div>
        <div className="flex justify-end">
          <Button
            variant="contained"
            sx={{ textTransform: "none", backgroundColor: PRIMARY }}
            startIcon={<Plus fontSize={16} />}
            onClick={() => setIsProcheModalOpen(true)}
          >
            Ajouter un proche
          </Button>
        </div>
      </div>
      <div>
        <ListDataGrid data={filteredProches} type={"proche"} />
      </div>
      {isProcheModalOpen && (
        <AddProcheModal
          isProcheModalOpen={isProcheModalOpen}
          handleCloseModal={handleCloseModal}
        />
      )}
    </div>
  );
};

export default Families;
