import { Proche } from "@/domain/models";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import ProcheAction from "./ProcheAction";

export const ProcheColumns = (): GridColDef[] => {
  return [
    {
      field: "date_naissance",
      headerName: "Date de naissance",
      width: 160,
      valueFormatter: (params) => new Date(params).toLocaleString(),
      headerClassName: "font-semibold",
    },
    {
      field: "nom",
      headerName: "Nom",
      width: 250,
      renderCell: (params: GridRenderCellParams<Proche>) => `${params.row.nom}`,
      headerClassName: "font-semibold",
    },
    {
      field: "prenom",
      headerName: "Prenom",
      width: 150,
      headerClassName: "font-semibold",
    },
    {
      field: "sexe",
      headerName: "Sexe",
      width: 150,
      headerClassName: "font-semibold",
    },
    {
      field: "lien_parente",
      headerName: "Lien parenté",
      width: 150,
      headerClassName: "font-semibold",
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      renderCell: (params) => <ProcheAction proche={params.row} />,
      headerClassName: "font-semibold",
    },
  ];
};
