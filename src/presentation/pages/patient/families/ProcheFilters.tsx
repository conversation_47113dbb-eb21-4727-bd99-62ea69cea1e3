import React from "react";
import {
  Autocomplete,
  Avatar,
  Box,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import { Search } from "lucide-react";
import { useProche } from "@/presentation/hooks/use-proches";

interface PatientOption {
  id: number;
  nom: string;
  prenom: string;
}

interface ProcheFiltersProps {
  selectedProche: PatientOption;
  onSelectedProcheChange: (value: PatientOption) => void;
}

const ProcheFilters: React.FC<ProcheFiltersProps> = ({
  selectedProche,
  onSelectedProcheChange,
}) => {
  const { proches } = useProche();
  const PROCHEFILTRE: PatientOption[] = Array.from(
    new Map(
      (proches || []).map((prs) => [
        prs.id,
        {
          id: prs.id,
          nom: prs.nom,
          prenom: prs.prenom,
          avatar: "",
        },
      ])
    ).values()
  );
  const handlePatientChange = (newValue: PatientOption | null) => {
    onSelectedProcheChange(newValue);
  };
  return (
    <Autocomplete
      size="medium"
      value={selectedProche}
      onChange={(_, newValue) => handlePatientChange(newValue)}
      options={PROCHEFILTRE}
      className="w-full md:w-[250px]"
      getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
      renderOption={(props, option) => (
        <Box
          component="li"
          sx={{ display: "flex", alignItems: "center", gap: 2 }}
          {...props}
          key={option.id}
        >
          <Avatar sx={{ width: 24, height: 24 }} />
          <Typography>
            {option.nom} {option.prenom}
          </Typography>
        </Box>
      )}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder="Chercher par le nom du patient..."
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      )}
    />
  );
};

export default ProcheFilters;
