import {
  Typography,
  Box,
  Autocomplete,
  TextField,
  InputAdornment,
} from "@mui/material";
import { useEffect, useState } from "react";
import { Search } from "lucide-react";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useParams } from "react-router-dom";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";

interface ListeEmployerProps {
  onEmployerSelect: (employerId: number | null) => void;
}

const ListeEmployer = ({ onEmployerSelect }: ListeEmployerProps) => {
  const { id: employerId } = useParams();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const { employers: dataEmployers, getAll: getEmployers } = useEmployer();

  interface EmployerOption {
    id: number;
    nom: string;
    prenom: string;
  }

  const EMPLOYERS: EmployerOption[] =
    dataEmployers?.map((employer) => ({
      id: employer.id,
      nom: employer.nom,
      prenom: employer.prenom,
    })) || [];

  const currentEmployer = EMPLOYERS?.find((p) => p.id === Number(employerId));
  const [selectedEmployer, setSelectedEmployer] =
    useState<EmployerOption | null>(currentEmployer ? currentEmployer : null);

  useEffect(() => {
    if (professionalId && dataEmployers?.length === 0) {
      getEmployers(professionalId);
    }
  }, [professionalId, dataEmployers?.length, getEmployers]);

  const handleEmployerChange = (newValue: EmployerOption | null) => {
    setSelectedEmployer(newValue);
    onEmployerSelect(newValue?.id || null);
  };

  return (
    <div className="my-4">
      <Autocomplete
        disabled={currentEmployer ? true : false}
        value={selectedEmployer}
        onChange={(_, newValue) => handleEmployerChange(newValue)}
        options={EMPLOYERS}
        getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
        renderOption={(props, option) => (
          <Box
            component="li"
            sx={{ display: "flex", alignItems: "center", gap: 2 }}
            {...props}
            key={option.id}
          >
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: "50%",
                bgcolor: "grey.300",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              {option.nom[0]}
            </Box>
            <Typography>
              {option.nom} {option.prenom}
            </Typography>
          </Box>
        )}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder="Chercher par le nom du patient..."
            InputProps={{
              ...params.InputProps,
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        )}
      />
    </div>
  );
};

export default ListeEmployer;
