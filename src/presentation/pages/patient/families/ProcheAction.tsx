import React, { useState } from "react";
import { Ellipsis } from "lucide-react";
import { Menu, MenuItem, IconButton } from "@mui/material";
import { Proche } from "@/domain/models";
import { DeleteProcheModal } from "@/presentation/components/common/Modal/DeleteProcheModal";
import { EditProcheModal } from "@/presentation/components/common/Modal/EditProcheModal";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { useNavigate } from "react-router-dom";
import { DashRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useAppSelector } from "@/presentation/hooks/redux";
import { utilisateurs_role_enum } from "@/domain/models/enums";

interface ProcheActionProps {
  proche: Proche;
}

export default function ProcheAction({ proche }: ProcheActionProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const open = Boolean(anchorEl);
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { resetProcheData } = useConsultationState();
  const navigate = useNavigate();

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    // resetProcheData(patientId);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <IconButton onClick={handleClick}>
        <Ellipsis size={20} />
      </IconButton>
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        <MenuItem
          onClick={() => {
            setIsDeleteModalOpen(true);
            setAnchorEl(null);
          }}
        >
          Supprimer
        </MenuItem>
        <MenuItem
          onClick={() => {
            setIsEditModalOpen(true);
            setAnchorEl(null);
          }}
        >
          Modiffier
        </MenuItem>
        {(role === utilisateurs_role_enum.PROFESSIONNEL ||
          role === utilisateurs_role_enum.DASH) && (
          <MenuItem
            onClick={() =>
              navigate(
                `/${DashRoutesNavigation.PROCHE_EMPLOYER_PAGE.split("/:id")[0]}/${proche.utilisateur_id}`
              )
            }
          >
            Consulter
          </MenuItem>
        )}
      </Menu>
      {isDeleteModalOpen && (
        <DeleteProcheModal
          isOpen={isDeleteModalOpen}
          handleClose={handleCloseDeleteModal}
          proche={proche}
        />
      )}
      {isEditModalOpen && (
        <EditProcheModal
          isOpen={isEditModalOpen}
          handleClose={handleCloseEditModal}
          proche={proche}
        />
      )}
    </div>
  );
}
