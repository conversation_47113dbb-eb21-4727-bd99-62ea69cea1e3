import { useState, useMemo, useEffect } from "react";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import AppointmentFilters from "@/presentation/components/common/appointment/AppointmentFilters";
import AppointmentCard from "@/presentation/components/features/patient/appointment/AppointmentCard";
import { Typography, Box, IconButton, Tooltip } from "@mui/material";
import { Apps as AppsIcon, List as ListIcon } from "@mui/icons-material";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { useAppSelector } from "@/presentation/hooks/redux";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

const Appointments = () => {
  const { appointmentPatient, fetchAppointmentListByPatientId } =
    useConsultationState();
  const [searchQuery, setSearchQuery] = useState("");

  const [isGridView, setIsGridView] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>("today");
  // =======
  //   const [selectedStatus, setSelectedStatus] = useState<string>(
  //     rendez_vous_statut_enum.A_VENIR,
  //   );
  // >>>>>>> fb84ddf3bd66e42e41e3b9e64bfaec0f8e4a29a2
  const patientId = useAppSelector(
    (state) => state.authentification.userData?.id,
  );
  const role = useAppSelector((state) => state.authentification.user?.role);

  const filteredAppointments = useMemo(() => {
    if (!appointmentPatient || appointmentPatient.length === 0) {
      return [];
    }

    const filtered = appointmentPatient.filter((appointment) => {
      // recherche
      const matchesSearch = searchQuery === "" ||
        (appointment.professional?.nom && appointment.professional.nom
          .toLowerCase()
          .includes(searchQuery.toLowerCase())) ||
        (appointment.professional?.prenom && appointment.professional.prenom
          .toLowerCase()
          .includes(searchQuery.toLowerCase())) ||
        (appointment.motif && appointment.motif.toLowerCase().includes(searchQuery.toLowerCase()));


      const matchesFilter = (() => {
        if (selectedStatus === "all") return true;
        if (selectedStatus === "today") {
          const today = new Date();
          const appointmentDate = new Date(appointment.date_rendez_vous);
          return (
            appointmentDate.getDate() === today.getDate() &&
            appointmentDate.getMonth() === today.getMonth() &&
            appointmentDate.getFullYear() === today.getFullYear()
          );
        }

        return appointment.statut === selectedStatus;
      })();

      return matchesSearch && matchesFilter;
    });

    // Si le filtre "today" ne retourne aucun résultat et qu'il n'y a pas de recherche,
    // basculer automatiquement sur "A venir"
    if (filtered.length === 0 && selectedStatus === "today" && searchQuery === "") {
      setSelectedStatus(rendez_vous_statut_enum.A_VENIR);
      return appointmentPatient.filter((appointment) =>
        appointment.statut === rendez_vous_statut_enum.A_VENIR
      );
    }

    return filtered;
  }, [appointmentPatient, searchQuery, selectedStatus]);

  useEffect(() => {
    if (patientId) {
      fetchAppointmentListByPatientId(patientId);
    }
  }, [patientId]);

  return (
    <div>
      <div className="mb-2">
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
          <Typography variant="h5" component="h1">
            Rendez-vous
          </Typography>
          <Tooltip title="Changer la vue">
            <IconButton onClick={() => setIsGridView(!isGridView)}
              size="medium"
              sx={{
                backgroundColor: 'rgba(39, 170, 225, 0.1)',
                color: '#27aae1',
                '&:hover': {
                  backgroundColor: 'rgba(39, 170, 225, 0.2)',
                },
                border: '1px solid rgba(39, 170, 225, 0.3)',
              }}
            >
              {!isGridView ? <ListIcon /> : <AppsIcon />}
            </IconButton>
          </Tooltip>
        </Box>
        <Typography variant="subtitle1" color="textSecondary">
          Total: {filteredAppointments.length} rendez-vous
        </Typography>
      </div>

      <div>
        <AppointmentFilters
          searchQuery={searchQuery}
          selectedStatus={selectedStatus}
          onSearchChange={setSearchQuery}
          onStatusChange={setSelectedStatus}
        />
        {filteredAppointments.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={8}
            sx={{
              backgroundColor: 'rgba(0, 0, 0, 0.02)',
              borderRadius: 2,
              border: '1px dashed rgba(0, 0, 0, 0.12)',
            }}
          >
            <Typography variant="h6" color="textSecondary" gutterBottom>
              Pas de rendez-vous
            </Typography>
            <Typography variant="body2" color="textSecondary" textAlign="center">
              Aucune render-vous {selectedStatus}
            </Typography>
          </Box>
        ) : !isGridView ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredAppointments.map((appointment) => (
              <AppointmentCard
                key={appointment.id}
                appointment={appointment}
              />
            ))}
          </div>
        ) : (
          <ListDataGrid data={filteredAppointments} type={role} />
        )}
      </div>
    </div>
  );
};

export default Appointments;
