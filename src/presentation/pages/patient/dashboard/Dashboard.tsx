import { FC, useEffect, useState } from "react";
import { useAppSelector } from "@/presentation/hooks/redux";
import {
  Calendar,
  TrendingUp,
  Users,
  Clock,
  Activity,
  CheckCircle,
  AlertCircle,
  BarChart2,
  Heart,
  Scale,
  User,
  CalendarCheck,
  CalendarX,
  Plus,
  FileText,
  Stethoscope,
  UserCheck,
  Search,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Bar,
  BarChart,
} from "recharts";
import { motion } from "framer-motion";

// Composants spécialisés du tableau de bord patient
import NextAppointment from "@/presentation/components/features/patient/appointments/NextAppointment";

// Hooks métier et utilitaires
import { useDarkMode } from "@/presentation/hooks/use-dark-mode";
import {
  useDashboardPatient,
  useHealthScore,
} from "@/presentation/hooks/dashboard/patient";

// Types
import { signe_vitaux } from "@/domain/models";
import { CarnetDeSanteModale } from "@/presentation/components/common/Modal/CarnetDeSanteModale";
import { HistoriquePatientModale } from "@/presentation/components/common/Modal/HistoriquePatientModale";
import { Navigate } from "react-router-dom";
import { PatientRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useNavigate } from "react-router-dom";
import { AddProcheModal } from "@/presentation/components/common/Modal/AddProchePatientModal";
import { useConsultationState, useMedicalConsultation } from "@/presentation/hooks/consultationMedicale";

/**
 * Composant Dashboard Patient - Interface Principale du Tableau de Bord
 * Design moderne inspiré du dashboard professionnel avec animations et couleurs MEDDoC
 */
const Dashboard: FC = () => {
  // ═══════════════════════════════════════════════════════════════════════════
  // HOOKS ET ÉTAT LOCAL
  // ═══════════════════════════════════════════════════════════════════════════

  const isDarkMode = useDarkMode();

  const patientId = useAppSelector(
    (state) => state.authentification.userData?.id,
  );

  const currentPatient = useAppSelector(
    (state) => state?.authentification.userData,
  );

  const {
    patient,
    signeVitauxPatient,
    consultationsPatient,
    signeVitauxByPatient,
    diagnosticPatient,
    patientMetrics,
    totalAppointments,
    todayAppointment,
    appointmentStats,
    appointmentData,
    appointmentPatient,
  } = useDashboardPatient(patientId);

  const { getMedicalConsultationsByPatient } = useMedicalConsultation();

  const [lastSigneVitaux, setLastSigneVitaux] = useState<signe_vitaux | null>(
    null,
  );

  const [isCarnetDeSanteModalOpen, setIsCarnetDeSanteModalOpen] = useState<boolean>(false);
  const [isHistoriquePatientModalOpen, setIsHistoriquePatientModalOpen] = useState<boolean>(false);
  const [isProcheModalOpen, setIsProcheModalOpen] = useState<boolean>(false);

  const carnetSante = patient?.carnet_sante;
  const healthScore = useHealthScore(
    carnetSante?.signe_vitaux,
    appointmentPatient,
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // EFFETS ET LOGIQUE MÉTIER
  // ═══════════════════════════════════════════════════════════════════════════

  useEffect(() => {
    if (carnetSante && carnetSante?.signe_vitaux) {
      const signeVitauxCount = carnetSante.signe_vitaux.length;
      setLastSigneVitaux(
        signeVitauxCount > 0
          ? carnetSante.signe_vitaux[signeVitauxCount - 1]
          : null,
      );
    }
  }, [carnetSante]);

  // Configuration du tooltip pour le graphique
  const tooltipContentStyle = isDarkMode
    ? {
      backgroundColor: "rgba(31, 41, 55, 0.9)",
      borderColor: "#374151",
      color: "#f9fafb",
    }
    : {
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      borderColor: "#e5e7eb",
      color: "#111827",
    };

  const tooltipItemStyle = isDarkMode
    ? { color: "#f3f4f6" }
    : { color: "#111827" };

  const tooltipLabelStyle = isDarkMode
    ? { color: "#f3f4f6" }
    : { color: "#111827" };

  const patientName = currentPatient && 'nom' in currentPatient
    ? `${(currentPatient as any).nom} ${(currentPatient as any).prenom || ''}`.trim()
    : "Patient";

  const navigate = useNavigate();


  // PROCHE ACTION RAPIDE

  const patientlId = useAppSelector((state) => state.authentification.user?.id);
  const { resetProcheData } = useConsultationState();

  const handleCloseModal = () => {
    setIsProcheModalOpen(false);
    resetProcheData(patientlId);
  };

  return (
    <div className="min-h-screen bg-meddoc-light dark:bg-meddoc-fonce transition-colors duration-300">
      <div className="p-3 sm:p-4 lg:p-6 max-w-7xl mx-auto">
        {/* En-tête moderne avec couleurs MEDDoC */}
        <header className="mb-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-meddoc-primary/20 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-meddoc-primary to-meddoc-secondary bg-clip-text text-transparent">
                Bonjour, {patientName}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1 text-sm">
                Voici un aperçu de votre suivi médical et de votre santé
              </p>
            </div>
            <div className="hidden sm:flex items-center justify-center w-12 h-12 bg-gradient-to-r from-meddoc-primary to-meddoc-secondary rounded-full">
              <Heart className="w-6 h-6 text-white" />
            </div>
          </div>
        </header>

        {/* Cartes de statistiques avec design moderne et animations */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Total Rendez-vous */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="group bg-white dark:bg-slate-800 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-xl group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 transition-colors">
                <Calendar className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-2xl font-bold text-slate-800 dark:text-slate-100">
                {totalAppointments || 0}
              </span>
            </div>
            <h3 className="font-semibold text-slate-700 dark:text-slate-300 mb-2">Rendez-vous</h3>
            <div className="text-sm text-slate-600 dark:text-slate-400">
              Total: <span className="font-medium text-slate-800 dark:text-slate-200">{totalAppointments || 0}</span>
            </div>
          </motion.div>

          {/* Rendez-vous Complétés */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="group bg-white dark:bg-slate-800 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-slate-200 dark:border-slate-700 hover:border-emerald-300 dark:hover:border-emerald-600"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-emerald-50 dark:bg-emerald-900/20 rounded-xl group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/30 transition-colors">
                <CheckCircle className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
              </div>
              <span className="text-2xl font-bold text-slate-800 dark:text-slate-100">
                {appointmentStats?.completed || 0}
              </span>
            </div>
            <h3 className="font-semibold text-slate-700 dark:text-slate-300 mb-2">RDV Complétés</h3>
            <div className="text-sm text-slate-600 dark:text-slate-400">
              Consultations: <span className="font-medium text-slate-800 dark:text-slate-200">{appointmentStats?.completed || 0}</span>
            </div>
          </motion.div>

          {/* Statut de Santé */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="group bg-white dark:bg-slate-800 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-slate-200 dark:border-slate-700 hover:border-amber-300 dark:hover:border-amber-600"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-xl group-hover:bg-amber-100 dark:group-hover:bg-amber-900/30 transition-colors">
                <Clock className="w-6 h-6 text-amber-600 dark:text-amber-400" />
              </div>
              <span className="text-lg font-bold text-slate-800 dark:text-slate-100">
                {appointmentStats?.upcoming || 0}
              </span>
            </div>
            <h3 className="font-semibold text-slate-700 dark:text-slate-300 mb-2">RDV à venir</h3>
            <div className="text-sm text-slate-600 dark:text-slate-400">
              Total: <span className="font-medium text-slate-800 dark:text-slate-200">{appointmentStats?.upcoming || 0}</span>
            </div>
          </motion.div>

          {/* Poids Actuel */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="group bg-white dark:bg-slate-800 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-slate-200 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-xl group-hover:bg-purple-100 dark:group-hover:bg-purple-900/30 transition-colors">
                <Scale className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <span className="text-2xl font-bold text-slate-800 dark:text-slate-100">
                {lastSigneVitaux?.poid || "--"}
              </span>
            </div>
            <h3 className="font-semibold text-slate-700 dark:text-slate-300 mb-2">Poids</h3>
            <div className="text-sm text-slate-600 dark:text-slate-400">
              Actuel: <span className="font-medium text-slate-800 dark:text-slate-200">{lastSigneVitaux?.poid ? `${lastSigneVitaux.poid} kg` : "Non renseigné"}</span>
            </div>
          </motion.div>
        </div>

        {/* Actions Rapides */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 mb-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Actions Rapides
            </h2>
            <UserCheck size={20} className="text-gray-400" />
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsProcheModalOpen(true)}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-meddoc-primary to-meddoc-primary/80 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              title="Ajouter un proche"
            >
              <Plus size={24} />
              <span className="text-sm font-medium">Ajouter un proche</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(`/${PatientRoutesNavigation.FIND_DOCTOR}`)}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-orange-500 to-orange-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              title="Trouver un professionnel de santé"
            >
              <Search size={24} />
              <span className="text-sm font-medium">Trouver un professionnel</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-green-500 to-green-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              title="Consulter mon carnet de santé"
              onClick={() => setIsCarnetDeSanteModalOpen(true)}
            >
              <FileText size={24} />
              <span className="text-sm font-medium">Carnet Santé</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center gap-3 p-4 bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              title="Voir mon historique médical"
              onClick={() => setIsHistoriquePatientModalOpen(true)}
            >
              <Activity size={24} />
              <span className="text-sm font-medium">Historique</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Section principale avec graphiques */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Graphique d'historique des rendez-vous - 2 colonnes */}
          <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-primary/20 dark:border-gray-700 overflow-hidden">
            <div className="px-4 py-3 bg-gradient-to-r from-meddoc-primary/10 to-meddoc-primary/5 dark:from-meddoc-primary/20 dark:to-meddoc-primary/10 border-b border-meddoc-primary/20 dark:border-gray-600">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                  <BarChart2 className="w-4 h-4 mr-2 text-meddoc-primary" />
                  Historique des Rendez-vous
                </h2>
                <span className="text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 px-2 py-1 rounded-full">
                  6 derniers mois
                </span>
              </div>
            </div>
            <div className="p-4">
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={appointmentData}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke={isDarkMode ? "#374151" : "#f3f4f6"}
                    />
                    <XAxis
                      dataKey="month"
                      stroke={isDarkMode ? "#9ca3af" : "#6b7280"}
                      fontSize={11}
                    />
                    <YAxis
                      stroke={isDarkMode ? "#9ca3af" : "#6b7280"}
                      fontSize={11}
                    />
                    <RechartsTooltip
                      contentStyle={tooltipContentStyle}
                      itemStyle={tooltipItemStyle}
                      labelStyle={tooltipLabelStyle}
                      wrapperStyle={{ outline: "none" }}
                    />
                    <Bar
                      dataKey="appointments"
                      fill={isDarkMode ? "#27aae1" : "#27aae1"}
                      radius={[3, 3, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Statistiques personnelles - 1 colonne */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-secondary/20 dark:border-gray-700 overflow-hidden">
            <div className="px-4 py-3 bg-gradient-to-r from-meddoc-secondary/10 to-meddoc-secondary/5 dark:from-meddoc-secondary/20 dark:to-meddoc-secondary/10 border-b border-meddoc-secondary/20 dark:border-gray-600">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <User className="w-4 h-4 mr-2 text-meddoc-secondary" />
                Mes Statistiques
              </h2>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gradient-to-br from-meddoc-secondary/10 to-meddoc-secondary/5 rounded-lg p-3 text-center border border-meddoc-secondary/20">
                  <CalendarCheck className="h-6 w-6 text-meddoc-secondary mx-auto mb-1" />
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {appointmentStats?.completed || 0}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    RDV Complétés
                  </p>
                </div>
                <div className="bg-gradient-to-br from-meddoc-primary/10 to-meddoc-primary/5 rounded-lg p-3 text-center border border-meddoc-primary/20">
                  <Clock className="h-6 w-6 text-meddoc-primary mx-auto mb-1" />
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {appointmentStats?.upcoming || 0}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    RDV à venir
                  </p>
                </div>
                <div className="bg-gradient-to-br from-red-500/10 to-red-500/5 rounded-lg p-3 text-center border border-red-500/20">
                  <CalendarX className="h-6 w-6 text-red-500 mx-auto mb-1" />
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {appointmentStats?.cancelled || 0}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    RDV Annulés
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-500/10 to-purple-500/5 rounded-lg p-3 text-center border border-purple-500/20">
                  <Scale className="h-6 w-6 text-purple-500 mx-auto mb-1" />
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {lastSigneVitaux?.poid || "--"}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    kg
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section du prochain rendez-vous */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-orange/20 dark:border-gray-700 overflow-hidden"
        >
          <div className="px-4 py-3 bg-gradient-to-r from-meddoc-orange/10 to-meddoc-orange/5 dark:from-meddoc-orange/20 dark:to-meddoc-orange/10 border-b border-meddoc-orange/20 dark:border-gray-600">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Calendar className="w-4 h-4 mr-2 text-meddoc-orange" />
              Mon Rendez-vous Aujourd'hui
              <span className="ml-auto text-xs text-gray-500 dark:text-gray-400">
                {new Date().toLocaleDateString("fr-FR", {
                  weekday: "long",
                  day: "numeric",
                  month: "long",
                })}
              </span>
            </h2>
          </div>
          <div className="p-6">
            <div className="h-80">
              {todayAppointment ? (
                <NextAppointment
                  appointment={todayAppointment}
                  onCancel={() => console.log("Annulation RDV - fonctionnalité en développement")}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-full">
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-full mb-4">
                    <Calendar className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                  </div>
                  <p className="text-center text-gray-500 dark:text-gray-400 text-lg font-medium">
                    Aucun rendez-vous aujourd'hui
                  </p>
                  <p className="text-center text-gray-400 dark:text-gray-500 text-sm mt-2">
                    Profitez de cette journée libre
                  </p>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
      <CarnetDeSanteModale
        isModalOpen={isCarnetDeSanteModalOpen}
        handleCloseModal={() => setIsCarnetDeSanteModalOpen(false)}
      />
      <HistoriquePatientModale
        isModalOpen={isHistoriquePatientModalOpen}
        handleCloseModal={() => setIsHistoriquePatientModalOpen(false)}
        consultationsPatient={consultationsPatient}
        signeVitauxPatient={signeVitauxPatient}
        diagnosticPatient={diagnosticPatient}
      />
      {isProcheModalOpen && (
        <AddProcheModal
          isProcheModalOpen={isProcheModalOpen}
          handleCloseModal={handleCloseModal}
        />
      )}
    </div>
  );
};

Dashboard.displayName = 'PatientDashboard';

export default Dashboard;