import { FC, Suspense } from 'react';
import ErrorBoundary from '@/presentation/components/common/ErrorBoundary';
import Dashboard from './Dashboard';

/**
 * Composant de chargement moderne pour le dashboard
 */
const DashboardSkeleton: FC = () => {
  return (
    <div className="p-6 max-w-7xl mx-auto animate-pulse">
      {/* En-tête skeleton */}
      <div className="mb-8">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mb-2"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-96"></div>
      </div>

      {/* Grille de statistiques skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
              <div className="h-6 w-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16 mb-2"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
          </div>
        ))}
      </div>

      {/* Graphiques skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-6"></div>
          <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-6"></div>
          <div className="grid grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                <div className="h-6 w-6 bg-gray-200 dark:bg-gray-600 rounded mx-auto mb-2"></div>
                <div className="h-6 bg-gray-200 dark:bg-gray-600 rounded w-8 mx-auto mb-1"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-12 mx-auto"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Wrapper moderne pour le Dashboard Patient
 * 
 * Fournit une couche de protection avec ErrorBoundary et Suspense
 * pour une expérience utilisateur robuste et moderne.
 * 
 * @component
 * @example
 * ```tsx
 * // Utilisation dans le routeur
 * <Route path="/dashboard" element={<DashboardWrapper />} />
 * ```
 * 
 * @features
 * - Error Boundary pour capturer les erreurs React
 * - Suspense pour le chargement asynchrone
 * - Skeleton loading moderne et accessible
 * - Gestion d'erreurs avec retry automatique
 * - Monitoring des erreurs intégré
 * 
 * @architecture
 * - Couche de protection autour du Dashboard principal
 * - Séparation des préoccupations entre wrapper et logique métier
 * - Interface de fallback élégante pour les erreurs
 * - Chargement progressif avec feedback visuel
 * 
 * @accessibility
 * - Skeleton loading avec animations respectueuses
 * - Messages d'erreur accessibles
 * - Navigation au clavier pour les actions de récupération
 * - Support des lecteurs d'écran
 */
const DashboardWrapper: FC = () => {
  /**
   * Gestionnaire d'erreurs pour l'ErrorBoundary
   */
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log l'erreur pour le debugging
    console.error('Dashboard Error Boundary:', error, errorInfo);

    // TODO: Envoyer l'erreur à un service de monitoring
    // sendErrorToMonitoring({
    //   error: error.message,
    //   stack: error.stack,
    //   componentStack: errorInfo.componentStack,
    //   timestamp: new Date().toISOString(),
    //   userAgent: navigator.userAgent,
    //   url: window.location.href,
    // });
  };

  return (
    <ErrorBoundary
      onError={handleError}
      fallbackMessage="Le tableau de bord patient rencontre un problème technique. Notre équipe a été notifiée et travaille sur une solution."
      showRetry={true}
      showHomeButton={true}
      className="min-h-screen bg-gray-50 dark:bg-gray-900"
    >
      <Suspense fallback={<DashboardSkeleton />}>
        <Dashboard />
      </Suspense>
    </ErrorBoundary>
  );
};

export default DashboardWrapper;
