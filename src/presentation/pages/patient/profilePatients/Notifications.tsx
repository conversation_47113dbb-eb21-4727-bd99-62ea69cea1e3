import { FC } from "react";
import { <PERSON><PERSON>, Snackbar } from "@mui/material";

interface NotificationsProps {
  notification: {
    open: boolean;
    message: string;
    severity: "success" | "error" | "info" | "warning";
  };
  handleCloseNotification: () => void;
}

const Notifications: FC<NotificationsProps> = ({
  notification,
  handleCloseNotification,
}) => {
  return (
    <Snackbar
      open={notification.open}
      autoHideDuration={6000}
      onClose={handleCloseNotification}
      anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
    >
      <Alert
        onClose={handleCloseNotification}
        severity={notification.severity}
        sx={{ width: "100%" }}
      >
        {notification.message}
      </Alert>
    </Snackbar>
  );
};

export default Notifications;
