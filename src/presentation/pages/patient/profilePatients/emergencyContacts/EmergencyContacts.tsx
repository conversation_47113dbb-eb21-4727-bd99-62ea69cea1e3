import { FC, useEffect } from "react";
import { TabPanel } from "../TabPanel";
import {
  Box,
  Button,
  Card,
  CardContent,
  IconButton,
  Typography,
} from "@mui/material";
import { Edit as EditIcon, Phone as PhoneIcon } from "@mui/icons-material";
import { PatientDTO } from "@/domain/DTOS";
import { Urgence } from "@/domain/models";

interface EmergencyContactsProps {
  tabValue: number;
  editMode: boolean;
  urgency: Urgence[];
  handleOpenDialog: () => void;
}

const EmergencyContacts: FC<EmergencyContactsProps> = ({
  tabValue,
  editMode,
  urgency,
  handleOpenDialog,
}) => {
  return (
    <TabPanel value={tabValue} index={2}>
      <div className="flex item-center justify-between mb-3">
        <Typography variant="h6">Contacts d'urgence</Typography>
        {editMode && (
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={handleOpenDialog}
            sx={{ textTransform: "none" }}
          >
            Ajouter un contact
          </Button>
        )}
      </div>

      {urgency?.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          {urgency?.map((contact) => (
            <div key={contact.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {contact.contact_urgence_nom}
                  </Typography>
                  <Typography
                    variant="subtitle2"
                    color="text.secondary"
                    gutterBottom
                  >
                    {contact.relation}
                  </Typography>

                  <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                    <PhoneIcon sx={{ mr: 1, color: "text.secondary" }} />
                    <Typography variant="body1">
                      {contact.contact_urgence_telephone}
                    </Typography>
                  </Box>

                  {editMode && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        mt: 2,
                      }}
                    >
                      <IconButton size="small" color="primary">
                        <EditIcon />
                      </IconButton>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      ) : (
        <Typography variant="body1" color="text.secondary">
          Aucun contact d'urgence n'a été ajouté.
        </Typography>
      )}
    </TabPanel>
  );
};

export default EmergencyContacts;
