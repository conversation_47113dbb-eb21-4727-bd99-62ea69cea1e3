import { FC, useEffect } from "react";
import { Avatar, Box, Button, Grid, Paper, Typography } from "@mui/material";
import {
  Edit as EditIcon,
  Cancel as CancelIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Home as HomeIcon,
  Cake as CakeIcon,
} from "@mui/icons-material";
import { PatientProfile } from "./type";
import { formatDate } from "./formatDate";
import { PatientDTO } from "@/domain/DTOS";

interface HeaderProfileProps {
  profile: PatientDTO;
  editMode: boolean;
  handleEditToggle: () => void;
}

const HeaderProfile: FC<HeaderProfileProps> = ({
  profile,
  editMode,
  handleEditToggle,
}) => {
  return (
    <Paper sx={{ p: 3, mb: 4 }} elevation={2}>
      <Grid container spacing={3} alignItems="center">
        <Grid item xs={12} md={2}>
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <Avatar
              // src={profile?.profilePicture}
              alt={`${profile?.nom} ${profile?.prenom}`}
              sx={{ width: 100, height: 100 }}
            >
              {profile?.nom.charAt(0)}
              {profile?.prenom.charAt(0)}
            </Avatar>
          </Box>
        </Grid>

        <Grid item xs={12} md={8}>
          <Typography variant="h4" component="h1" gutterBottom>
            {profile?.nom} {profile?.prenom}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                <CakeIcon sx={{ mr: 1, color: "text.secondary" }} />
                <Typography variant="body1">
                  Né(e) le {formatDate(profile?.date_naissance)}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                <PhoneIcon sx={{ mr: 1, color: "text.secondary" }} />
                <Typography variant="body1">{profile?.telephone}</Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                <EmailIcon sx={{ mr: 1, color: "text.secondary" }} />
                <Typography variant="body1">{profile?.email}</Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                <HomeIcon sx={{ mr: 1, color: "text.secondary" }} />
                <Typography variant="body1">{profile?.adresse}</Typography>
              </Box>
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12} md={2}>
          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            <Button
              variant="contained"
              color={editMode ? "error" : "primary"}
              startIcon={editMode ? <CancelIcon /> : <EditIcon />}
              onClick={handleEditToggle}
            >
              {editMode ? "Annuler" : "Modifier"}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default HeaderProfile;
