import { FC } from "react";
import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  Divider,
  Grid,
  Typography,
} from "@mui/material";
import { formatDate } from "../formatDate";
import { Patient } from "@/domain/models";

interface PersonalInformationContentProps {
  profile: Patient;
}

const PersonalInformationContent: FC<PersonalInformationContentProps> = ({
  profile,
}) => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12} sm={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Informations de base
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Prénom
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {profile?.prenom}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Nom
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {profile?.nom}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Date de naissance
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {formatDate(profile?.date_naissance)}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Genre
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {profile?.sexe}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Coordonnées
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Email
              </Typography>
              <Typography variant="body1" gutterBottom>
                {profile?.email}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Téléphone
              </Typography>
              <Typography variant="body1" gutterBottom>
                {profile?.telephone}
              </Typography>
            </Box>

            <Box>
              <Typography variant="subtitle2" color="text.secondary">
                Adresse
              </Typography>
              <Typography variant="body1">{profile?.adresse}</Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default PersonalInformationContent;
