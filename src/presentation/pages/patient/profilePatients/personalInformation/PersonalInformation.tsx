import { FC } from "react";
import { TabPanel } from "../TabPanel";
import EditModePersonalInformation from "./EditModePersonalInformation";
import PersonalInformationContent from "./PersonalInformationContent";
import { Patient } from "@/domain/models";

interface PersonalInformationProps {
  tabValue: number;
  editMode: boolean;
  profile: Patient;
}

const PersonalInformation: FC<PersonalInformationProps> = ({
  tabValue,
  editMode,
  profile,
}) => {
  return (
    <TabPanel value={tabValue} index={1}>
      {editMode ? (
        <EditModePersonalInformation />
      ) : (
        <PersonalInformationContent profile={profile} />
      )}
    </TabPanel>
  );
};

export default PersonalInformation;
