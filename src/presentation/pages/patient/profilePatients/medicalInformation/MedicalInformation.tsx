import { FC } from "react";
import { TabPanel } from "../TabPanel";
import CarnetDeSante from "@/presentation/pages/professional/patients/CarnetDeSante";

interface MedicalInformationProps {
  tabValue: number;
  editMode: boolean;
}

const MedicalInformation: FC<MedicalInformationProps> = ({
  tabValue,
  editMode,
}) => {
  return (
    <TabPanel value={tabValue} index={0}>
      <CarnetDeSante
        isEdit={editMode}
        className="grid grid-cols-1 lg:grid-cols-2 gap-4"
      />
    </TabPanel>
  );
};

export default MedicalInformation;
