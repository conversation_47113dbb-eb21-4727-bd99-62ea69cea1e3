import React, { FC, useState } from "react";
import { Box, Paper, Tab, Tabs, CircularProgress } from "@mui/material";
import PersonalInformation from "./personalInformation/PersonalInformation";
import MedicalInformation from "./medicalInformation/MedicalInformation";
import EmergencyContacts from "./emergencyContacts/EmergencyContacts";
import Notifications from "./Notifications";
import { useAppSelector } from "@/presentation/hooks/redux";
import { AddContactUrgenceModal } from "@/presentation/components/common/Modal/AddContactUrgenceModal";
import { useProfilePatientData } from "@/presentation/hooks/useProfilePatientData";
import PatientHeader from "@/presentation/components/features/patient/PatientCard/PatientHeader";

const ProfilePatients: FC = () => {
  const [editMode, setEditMode] = useState<boolean>(false);
  const [tabValue, setTabValue] = useState<number>(0);
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "error" | "info" | "warning";
  }>({
    open: false,
    message: "",
    severity: "info",
  });

  const { loading, patientData } = useProfilePatientData();

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleEditToggle = () => {
    setEditMode(!editMode);
  };

  const handleCloseNotification = () => {
    setNotification((prev) => ({ ...prev, open: false }));
  };

  const handleOpenDialog = () => {
    setShowDialog(true);
  };

  const handleCloseDialog = () => {
    setShowDialog(false);
  };

  // Render loading state
  if (loading && !patientData) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box className="p-6 w-full mx-auto">
      {/* Header with profile summary */}
      <PatientHeader handleEditToggle={handleEditToggle} editMode={editMode} />

      {/* Tabs for different sections */}
      <Paper sx={{ width: "100%" }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="profile tabs"
          variant="fullWidth"
        >
          <Tab label="Informations médicales" />
          <Tab label="Informations personnelles" />
          <Tab label="Contacts d'urgence" />
        </Tabs>

        {/* Tab 1: Medical Information */}
        <MedicalInformation tabValue={tabValue} editMode={editMode} />

        {/* Tab 2: Personal Information */}
        <PersonalInformation
          tabValue={tabValue}
          editMode={editMode}
          profile={patientData}
        />

        {/* Tab 3: Emergency Contacts */}
        <EmergencyContacts
          tabValue={tabValue}
          editMode={editMode}
          urgency={patientData.urgence}
          handleOpenDialog={handleOpenDialog}
        />
      </Paper>

      {/* Dialogs for adding/editing items */}
      {showDialog && (
        <AddContactUrgenceModal
          isOpen={showDialog}
          patientId={patientData.id}
          handleClose={handleCloseDialog}
        />
      )}

      {/* Notifications */}
      <Notifications
        notification={notification}
        handleCloseNotification={handleCloseNotification}
      />
    </Box>
  );
};

export default ProfilePatients;
