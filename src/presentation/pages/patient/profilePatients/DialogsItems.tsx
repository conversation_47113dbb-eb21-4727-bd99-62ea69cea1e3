import { FC } from "react";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from "@mui/material";

interface DialogsItemsProps {
  dialogType: string;
  showDialog: boolean;
  handleCloseDialog: () => void;
  handleAddItem: (type: string, item: any) => void;
}

const DialogsItems: FC<DialogsItemsProps> = ({
  dialogType,
  showDialog,
  handleCloseDialog,
  handleAddItem,
}) => {
  return (
    <Dialog open={showDialog} onClose={handleCloseDialog}>
      <DialogTitle>
        {dialogType === "allergy" && "Ajouter une allergie"}
        {dialogType === "condition" && "Ajouter une condition chronique"}
        {dialogType === "medication" && "Ajouter un médicament"}
        {dialogType === "contact" && "Ajouter un contact d'urgence"}
      </DialogTitle>

      <DialogContent>
        {/* Dialog content would be implemented based on the dialog type */}
        <Typography variant="body1" sx={{ my: 2 }}>
          Formulaire d'ajout pour: {dialogType}
        </Typography>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleCloseDialog}>Annuler</Button>
        <Button onClick={() => handleAddItem(dialogType, {})}>Ajouter</Button>
      </DialogActions>
    </Dialog>
  );
};

export default DialogsItems;
