interface Medication {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  startDate: string;
  endDate?: string;
  isActive: boolean;
}

interface EmergencyContact {
  id: string;
  name: string;
  relationship: string;
  phone: string;
}

// Types
export interface PatientProfile {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  email: string;
  phone: string;
  address: string;
  profilePicture?: string;
  bloodType?: string;
  height?: number;
  weight?: number;
  allergies: string[];
  chronicConditions: string[];
  medications: Medication[];
  emergencyContacts: EmergencyContact[];
}
