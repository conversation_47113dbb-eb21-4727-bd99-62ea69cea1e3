import { CONSULTATION_STEPS } from "@/shared/constants/ConsultationtSteps";
import SumUp from "./SumUp";
import { UseFormGetValues } from "react-hook-form";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";

interface ConsultationInformationsProps {
  getValues: UseFormGetValues<ConsultationStepFormData>;
  activeStep: number;
}

const ConsultationInformations = ({
  activeStep,
  getValues,
}: ConsultationInformationsProps): JSX.Element => {
  return (
    <div className="lg:w-2/5 bg-gradient-to-br from-meddoc-primary to-meddoc-secondary p-8 lg:p-12 text-white relative overflow-hidden">
      {/* Éléments décoratifs */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-10 -mt-10" />
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -ml-8 -mb-8" />

      <div className="relative z-10 h-full flex flex-col">
        <SumUp getValues={getValues} activeStep={activeStep} />
        {/* Étape actuelle */}
        <div className="mt-8 pt-6 border-t border-white/20">
          <p className="text-white/80 text-sm">
            Étape {activeStep + 1} sur {CONSULTATION_STEPS.length}
          </p>
          <p className="text-white font-medium">
            {CONSULTATION_STEPS[activeStep]}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ConsultationInformations;
