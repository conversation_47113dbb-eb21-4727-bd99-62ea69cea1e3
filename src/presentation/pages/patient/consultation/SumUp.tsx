import { Avatar, Paper, Typography } from "@mui/material";
import { motion } from "framer-motion";
import {
  MapPin,
  Calendar,
  Stethoscope,
  User,
  FileText,
  Clock,
  CheckCircle,
  Heart,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import useProfessionals from "@/presentation/hooks/use-professionals";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useLocation } from "react-router-dom";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { UseFormGetValues } from "react-hook-form";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import { TimeSlotProffessionalCard } from "@/domain/DTOS";
import { getSelectedTimeSlot } from "@/presentation/hooks/consultationStep/getSelectedTimeSlot";

interface SumUpProps {
  children?: React.ReactNode;
  activeStep: number;
  getValues: UseFormGetValues<ConsultationStepFormData>;
}

const SumUp: React.FC<SumUpProps> = ({ children, activeStep, getValues }) => {
  const { selectedProfessionnal } = useProfessionals();
  const [speciality, setSpeciality] = useState<string | null>(null);
  const [consultationMotif, setConsultationMotif] = useState<string | null>(
    null
  );
  const selectedTimeSlot = getSelectedTimeSlot();

  useEffect(() => {
    if (getValues) {
      const value = getValues();
      setSpeciality(value.speciality);
      setConsultationMotif(value.consultationMotif);
    }
  }, [activeStep]);

  return (
    <div className="lg:col-span-1">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        {/* Titre avec icône */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="relative z-10"
        >
          <div className="flex items-center justify-center text-lg lg:text-xl font-bold mb-4">
            Récapitulatif
          </div>
        </motion.div>

        {/* Section Praticien */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="relative z-10 mb-4"
        >
          <div className="text-center mb-4">
            <div className="relative inline-block">
              <Avatar
                sx={{
                  width: 50,
                  height: 50,
                  margin: "0 auto",
                  border: "3px solid white",
                  boxShadow: "0 8px 25px rgba(39, 170, 225, 0.2)",
                  background:
                    "linear-gradient(135deg, #27aae1 0%, #00bfa5 100%)",
                }}
              />
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                <CheckCircle size={12} className="text-white" />
              </div>
            </div>
          </div>

          <div className="text-center">
            <p className="text-white/90 text-lg font-semibold leading-relaxed">
              {selectedProfessionnal?.titre} {selectedProfessionnal?.nom}{" "}
              {selectedProfessionnal?.prenom}
            </p>
            {speciality && (
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full border border-blue-200">
                <Stethoscope size={14} className="text-meddoc-primary" />
                <Typography
                  variant="body2"
                  sx={{
                    color: "#27aae1",
                    fontWeight: 600,
                    fontSize: "0.875rem",
                  }}
                >
                  {speciality}
                </Typography>
              </div>
            )}
          </div>
        </motion.div>

        {/* Section Enfants (Avertissements) */}
        {children && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="relative z-10 mb-6"
          >
            {children}
          </motion.div>
        )}

        {/* Détails du RDV */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          className="relative z-10 space-y-4"
        >
          <div className="border-t border-white/20 pt-4">
            <div className="space-y-3">
              {/* Date */}
              <motion.div
                className="flex items-center space-x-4"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
              >
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <Calendar size={16} className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="font-semibold">Date de consultation</h3>
                  <p className="text-white/80 text-sm">
                    {selectedTimeSlot?.date &&
                      format(
                        new Date(selectedTimeSlot.date),
                        "EEEE dd MMMM yyyy",
                        {
                          locale: fr,
                        }
                      )}
                  </p>
                </div>
              </motion.div>

              {/* Heure */}
              <motion.div
                className="flex items-center space-x-4"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
              >
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <Clock size={16} className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="font-semibold">Heure de consultation</h3>
                  <p className="text-white/80 text-sm">
                    du {selectedTimeSlot?.start} à {selectedTimeSlot?.end}{" "}
                  </p>
                </div>
              </motion.div>

              {/* Motif */}
              {consultationMotif && (
                <motion.div
                  className="flex items-center space-x-4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8 }}
                >
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <Stethoscope size={16} className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Motif</h3>
                    <p className="text-white/80 text-sm">{consultationMotif}</p>
                  </div>
                </motion.div>
              )}

              {/* Adresse */}
              <motion.div
                className="flex items-center space-x-4"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
              >
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <MapPin className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="font-semibold">Lieu de consultation</h3>
                  <p className="text-white/80 text-sm">
                    {selectedProfessionnal?.adresse}
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default SumUp;
