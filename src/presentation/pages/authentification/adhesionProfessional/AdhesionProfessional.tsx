import { Commune, demande_adhesion, District } from "@/domain/models";
import useTypeEtablissement from "@/presentation/hooks/use-type-etablisement";
import {
  Autocomplete,
  Box,
  Container,
  Paper,
  TextField,
  Typography,
  FormHelperText,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import React, { useEffect } from "react";
import { toast } from "sonner";
import Button from "@/presentation/components/common/Button/Button";
import UnathenticatedLayout from "@/presentation/components/layouts/UnauthenticatedLayout";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import { useAdhesionForm } from "@/presentation/hooks/use-adhesion-form";
import { motion } from "framer-motion";
import {
  User,
  Phone,
  Mail,
  Building2,
  MapPin,
  FileText,
  CheckCircle,
} from "lucide-react";
import useAdhesionRequest from "@/presentation/hooks/adhesionRequest/use-adhesion-request";
import SelectDistrict from "@/presentation/components/locationSelector/SelectDistrict";
import SelectCommune from "@/presentation/components/locationSelector/SelectCommune";
import { GetLocaleDate } from "@/shared/utils/getLocaleDate";

// Style amélioré pour le conteneur du formulaire
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(1.5),
  [theme.breakpoints.up("sm")]: {
    padding: theme.spacing(3),
  },
  [theme.breakpoints.up("md")]: {
    padding: theme.spacing(4),
  },
  backgroundColor: "white",
  borderRadius: theme.spacing(1),
  [theme.breakpoints.up("sm")]: {
    borderRadius: theme.spacing(2),
  },
  boxShadow: "0 5px 15px rgba(0, 0, 0, 0.08)",
  [theme.breakpoints.up("sm")]: {
    boxShadow: "0 10px 30px rgba(0, 0, 0, 0.08)",
  },
  position: "relative",
  overflow: "hidden",
  width: "100%",
  maxWidth: "100%",
  margin: "0 auto",
}));

// Style pour les icônes des champs
const IconWrapper = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  marginBottom: theme.spacing(0.5),
  [theme.breakpoints.up("sm")]: {
    marginBottom: theme.spacing(1),
  },
  flexWrap: "wrap",
  "& svg": {
    marginRight: theme.spacing(0.75),
    [theme.breakpoints.down("sm")]: {
      width: 16,
      height: 16,
    },
  },
  "& .MuiTypography-root": {
    [theme.breakpoints.down("sm")]: {
      fontSize: "0.8rem",
    },
  },
}));

// Style pour les champs de formulaire (supprimé car non utilisé)

// Style global pour les sélecteurs (District et Commune)
const selectStyles = {
  ".MuiOutlinedInput-root": {
    height: "48px",
    "@media (min-width: 600px)": {
      height: "52px",
    },
    borderRadius: "8px",
    "& .MuiOutlinedInput-input": {
      padding: "14px 16px",
      "@media (max-width: 600px)": {
        padding: "12px 14px",
      },
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderColor: "#27aae1",
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      borderColor: "#27aae1",
      borderWidth: "1px",
    },
  },
};

// Style pour les champs de texte
const StyledTextField = styled(TextField)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    borderRadius: "8px",
    height: { xs: "48px", sm: "52px" },
    [theme.breakpoints.down("sm")]: {
      fontSize: "0.875rem",
    },
    "& .MuiOutlinedInput-input": {
      padding: "14px 16px",
      [theme.breakpoints.down("sm")]: {
        padding: "12px 14px",
      },
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderColor: "#27aae1",
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      borderColor: "#27aae1",
      borderWidth: "1px",
    },
  },
  "& .MuiFormHelperText-root": {
    [theme.breakpoints.down("sm")]: {
      fontSize: "0.7rem",
      marginTop: 2,
      marginBottom: 0,
    },
  },
}));

const AdhesionProfessional = () => {
  const { listes: etablissements, getTypeEtablissementList } =
    useTypeEtablissement();
  const { loading, handleCreateAdhesionRequest } = useAdhesionRequest();
  const {
    formData,
    errors,
    handleChange,
    handleTypeEtablissementChange,
    handleDistrictChange,
    handleCommuneChange,
    validateForm,
    register,
  } = useAdhesionForm();
  const {
    selectedDistrict,
    selectedCommune,
    handleDistrictChange: onDistrictChange,
    handleCommuneChange: onCommuneChange,
  } = useLocationSelector();

  useEffect(() => {
    getTypeEtablissementList();
  }, []);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    console.log(errors);
    if (Object.keys(errors).length > 0) return;

    const validation = validateForm();
    if (!validation.isValid || !validation.data) return;

    const data: Omit<demande_adhesion, "id" | "status"> = {
      nom: validation.data.firstName,
      prenom: validation.data.lastName,
      adresse: validation.data.villeCabinet,
      district: validation.data.district,
      commune: validation.data.commune,
      telephone: validation.data.phone,
      type_etablissement: validation.data.typeEtablissement,
      email: validation.data.email,
      date_creation: GetLocaleDate().toISOString(),
    };

    try {
      await handleCreateAdhesionRequest(data);
    } catch (error) {
      toast.error("Une erreur s'est produite lors de l'envoi de votre demande");
    }
  };

  // Animation variants pour les éléments
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 },
    },
  };

  return (
    <UnathenticatedLayout>
      {/* Fond dégradé pour toute la page */}
      <div className="min-h-screen bg-gradient-to-br from-blue-400 via-teal-400 to-green-400 py-12 px-4">
        <div className="container mx-auto max-w-6xl">
          {/* Titre principal centré */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-8"
          >
            <h1 className="text-4xl font-bold text-white mb-2">
              Adhésion Professionnel
            </h1>
            <p className="text-white/90 text-lg">
              Demandez votre adhésion à MEDDoC Pro
            </p>
          </motion.div>

          {/* Carte principale avec design contact */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white rounded-3xl shadow-2xl overflow-hidden"
          >
            <div className="flex flex-col lg:flex-row min-h-[600px]">
              {/* Section gauche - Information */}
              <div className="lg:w-2/5 bg-gradient-to-br from-meddoc-primary to-meddoc-secondary p-8 lg:p-12 text-white relative overflow-hidden">
                {/* Éléments décoratifs */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-10 -mt-10" />
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -ml-8 -mb-8" />
                <div className="relative z-10 h-full flex flex-col">
                  <div className="mb-8">
                    <h2 className="text-2xl lg:text-3xl font-bold mb-4">
                      Rejoignez MEDDoC Pro
                    </h2>
                    <p className="text-white/90 text-lg leading-relaxed">
                      Simplifiez la gestion de votre cabinet et accédez à tous
                      nos outils professionnels.
                    </p>
                  </div>
                  {/* Avantages */}
                  <div className="space-y-6 flex-1">
                    <motion.div
                      className="flex items-center space-x-4"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 }}
                    >
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <User className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold">Gestion simplifiée</h3>
                        <p className="text-white/80 text-sm">
                          Rendez-vous, dossiers, équipe, tout en un
                        </p>
                      </div>
                    </motion.div>
                    <motion.div
                      className="flex items-center space-x-4"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.8 }}
                    >
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <Mail className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold">
                          Communication efficace
                        </h3>
                        <p className="text-white/80 text-sm">
                          Rappels SMS, notifications, suivi patient
                        </p>
                      </div>
                    </motion.div>
                    <motion.div
                      className="flex items-center space-x-4"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 1.0 }}
                    >
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <CheckCircle className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold">
                          Sécurité & confidentialité
                        </h3>
                        <p className="text-white/80 text-sm">
                          Données protégées et accès sécurisé
                        </p>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </div>
              {/* Section droite - Formulaire */}
              <div className="lg:w-3/5 p-8 lg:p-12">
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                >
                  {/* On garde le formulaire existant, mais on retire le Container et StyledPaper pour utiliser le nouveau design */}
                  <form
                    onSubmit={handleSubmit}
                    className="h-full flex flex-col"
                  >
                    <div className="flex-1">
                      {/* On garde la grille et tous les champs existants */}
                      <Box component="div" sx={{ ...selectStyles }}>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 md:gap-5">
                          {/* Prénom */}
                          <div className="col-span-full sm:col-span-1 mb-3 sm:mb-0">
                            <IconWrapper>
                              <User
                                size={20}
                                className="text-meddoc-primary mr-2"
                              />
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 500 }}
                              >
                                Prénom*
                              </Typography>
                            </IconWrapper>
                            <StyledTextField
                              fullWidth
                              placeholder="Entrez votre prénom"
                              variant="outlined"
                              value={formData.firstName}
                              onChange={handleChange("firstName")}
                              error={!!errors.firstName}
                              helperText={errors.firstName}
                            />
                          </div>
                          {/* Nom */}
                          <div className="col-span-full sm:col-span-1 mb-3 sm:mb-0">
                            <IconWrapper>
                              <User
                                size={20}
                                className="text-meddoc-primary mr-2"
                              />
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 500 }}
                              >
                                Nom*
                              </Typography>
                            </IconWrapper>
                            <StyledTextField
                              fullWidth
                              placeholder="Entrez votre nom"
                              variant="outlined"
                              value={formData.lastName}
                              onChange={handleChange("lastName")}
                              error={!!errors.lastName}
                              helperText={errors.lastName}
                            />
                          </div>
                          {/* Téléphone */}
                          <div className="col-span-full sm:col-span-1 mb-3 sm:mb-0">
                            <IconWrapper>
                              <Phone
                                size={20}
                                className="text-meddoc-primary mr-2"
                              />
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 500 }}
                              >
                                Téléphone*
                              </Typography>
                            </IconWrapper>
                            <StyledTextField
                              fullWidth
                              placeholder="Entrez votre numéro de téléphone"
                              variant="outlined"
                              value={formData.phone}
                              onChange={handleChange("phone")}
                              error={!!errors.phone}
                              helperText={errors.phone}
                            />
                          </div>
                          {/* Email */}
                          <div className="col-span-full sm:col-span-1 mb-3 sm:mb-0">
                            <IconWrapper>
                              <Mail
                                size={20}
                                className="text-meddoc-primary mr-2"
                              />
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 500 }}
                              >
                                E-mail*
                              </Typography>
                            </IconWrapper>
                            <StyledTextField
                              fullWidth
                              placeholder="Entrez votre adresse email"
                              type="email"
                              variant="outlined"
                              value={formData.email}
                              onChange={handleChange("email")}
                              error={!!errors.email}
                              helperText={errors.email}
                            />
                          </div>
                          {/* Type d'établissement */}
                          <div className="col-span-full sm:col-span-1 mb-3 sm:mb-0">
                            <IconWrapper>
                              <Building2
                                size={20}
                                className="text-meddoc-primary mr-2"
                              />
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 500 }}
                              >
                                Type d'établissement*
                              </Typography>
                            </IconWrapper>
                            <Autocomplete
                              disablePortal
                              options={etablissements.map(
                                (etablissement) =>
                                  etablissement.nom_etablissement
                              )}
                              sx={{
                                width: "100%",
                                ...selectStyles[".MuiOutlinedInput-root"],
                              }}
                              value={formData.typeEtablissement}
                              onChange={handleTypeEtablissementChange}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  placeholder="Sélectionnez un type d'établissement"
                                  error={!!errors.typeEtablissement}
                                  helperText={errors.typeEtablissement}
                                />
                              )}
                            />
                          </div>
                          {/* Adresse du cabinet */}
                          <div className="col-span-full sm:col-span-1 mb-3 sm:mb-0">
                            <IconWrapper>
                              <MapPin
                                size={20}
                                className="text-meddoc-primary mr-2"
                              />
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 500 }}
                              >
                                Adresse*
                              </Typography>
                            </IconWrapper>
                            <StyledTextField
                              fullWidth
                              placeholder="Entrez l'adresse de votre cabinet"
                              variant="outlined"
                              value={formData.adresse}
                              onChange={handleChange("adresse")}
                              error={!!errors.adresse}
                              helperText={errors.adresse}
                            />
                          </div>
                          {/* District */}
                          <SelectDistrict
                            register={register}
                            errors={errors}
                            value={selectedDistrict}
                            onChange={(district: District) => {
                              onDistrictChange(district);
                              handleDistrictChange(district.libelle);
                            }}
                            allNeeded
                          />
                          {/* Commune */}
                          <SelectCommune
                            register={register}
                            errors={errors}
                            value={selectedCommune}
                            onChange={(commune: Commune) => {
                              onCommuneChange(commune);
                              handleCommuneChange(commune.nom);
                            }}
                            isDisabled={!selectedDistrict}
                          />
                          {/* Politique de confidentialité */}
                          <div className="col-span-2 mt-1 sm:mt-2">
                            <Box
                              sx={{
                                p: { xs: 1, sm: 1.5, md: 2 },
                                bgcolor: "rgba(39, 170, 225, 0.05)",
                                borderRadius: { xs: 1, sm: 1.5 },
                                border: "1px solid rgba(39, 170, 225, 0.1)",
                              }}
                            >
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{
                                  display: "flex",
                                  alignItems: "flex-start",
                                  fontSize: {
                                    xs: "0.7rem",
                                    sm: "0.75rem",
                                    md: "0.875rem",
                                  },
                                  lineHeight: { xs: 1.3, sm: 1.5 },
                                }}
                              >
                                <CheckCircle
                                  size={14}
                                  className="text-meddoc-primary mr-2 mt-1 flex-shrink-0"
                                />
                                <span>
                                  MEDDoC a besoin de vos coordonnées pour vous
                                  recontacter au sujet de nos produits et
                                  services. Consultez notre politique de
                                  confidentialité pour en savoir plus sur nos
                                  pratiques en matière de protection des données
                                  et notre engagement vis-à-vis de la protection
                                  et de la vie privée.
                                </span>
                              </Typography>
                            </Box>
                          </div>
                          {/* Bouton d'envoi */}
                          <div className="col-span-2 mt-1.5 sm:mt-2.5">
                            <motion.div
                              whileHover={{ scale: 1.01 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              <Button className="w-full py-2 sm:py-2.5 md:py-3 bg-gradient-to-r from-meddoc-primary to-meddoc-secondary hover:from-meddoc-primary/90 hover:to-meddoc-secondary/90 text-white font-medium rounded-md sm:rounded-lg shadow-sm sm:shadow-md transition-all duration-200 text-xs sm:text-sm md:text-base">
                                {loading ? (
                                  <div className="flex items-center justify-center">
                                    <svg
                                      className="animate-spin -ml-1 mr-1.5 h-3 w-3 sm:h-3.5 sm:w-3.5 md:h-4 md:w-4 text-white"
                                      xmlns="http://www.w3.org/2000/svg"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                    >
                                      <circle
                                        className="opacity-25"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="currentColor"
                                        strokeWidth="4"
                                      ></circle>
                                      <path
                                        className="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                      ></path>
                                    </svg>
                                    <span className="text-xs sm:text-sm md:text-base">
                                      Traitement en cours...
                                    </span>
                                  </div>
                                ) : (
                                  <span className="text-xs sm:text-sm md:text-base">
                                    Envoyer ma demande d'adhésion
                                  </span>
                                )}
                              </Button>
                            </motion.div>
                          </div>
                        </div>
                      </Box>
                    </div>
                  </form>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </UnathenticatedLayout>
  );
};

export default AdhesionProfessional;
