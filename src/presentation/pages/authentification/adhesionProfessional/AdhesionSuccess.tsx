import { Box, Container, Typography, Paper } from '@mui/material'
import { styled } from '@mui/material/styles'
import { Link } from 'react-router-dom'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import UnathenticatedLayout from '@/presentation/components/layouts/UnauthenticatedLayout'

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  backgroundColor: 'white',
  borderRadius: theme.spacing(1),
  textAlign: 'center',
  marginTop: theme.spacing(4),
  marginBottom: theme.spacing(4)
}))

const StyledIcon = styled(CheckCircleIcon)(({ theme }) => ({
  fontSize: '4rem',
  color: theme.palette.success.main,
  marginBottom: theme.spacing(2)
}))

const AdhesionSuccess = () => {
  return (
    <UnathenticatedLayout vous_ete="Je suis professionnel">
      <Container maxWidth="md">
        <StyledPaper elevation={3}>
          <Box>
            <StyledIcon />
            <Typography variant="h4" component="h1" gutterBottom>
              Demande d'adhésion envoyée avec succès !
            </Typography>
            <Typography variant="body1" sx={{ mb: 3 }}>
              Merci d'avoir soumis votre demande d'adhésion à MEDDoC Pro. Notre équipe examinera votre
              demande et vous contactera par téléphone dans les prochaines 24 heures.
            </Typography>
            <Typography variant="body1" sx={{ mb: 3 }}>
              En attendant, nous vous invitons à vérifier régulièrement votre boîte e-mail car nous
              pourrions vous envoyer des informations importantes concernant votre demande.
            </Typography>
            <Typography variant="body2" className='text-meddoc-fonce-meddoc'>
              Vous pouvez maintenant{' '}
              <Link to="/" style={{ textDecoration: 'none' }} className='text-meddoc-fonce-meddoc'>
                retourner à la page d'accueil
              </Link>
            </Typography>
          </Box>
        </StyledPaper>
      </Container>
    </UnathenticatedLayout>
  )
}

export default AdhesionSuccess
