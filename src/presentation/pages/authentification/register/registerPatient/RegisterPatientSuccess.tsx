import React from 'react';
import { Box, Typography, Container, Paper } from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import EmailIcon from '@mui/icons-material/Email';
import UnathenticatedLayout from '@/presentation/components/layouts/UnauthenticatedLayout';
import { BLACK, PRIMARY } from '@/shared/constants/Color';

const RegisterPatientSuccess: React.FC = () => {
  return (
    <UnathenticatedLayout>
    <Container maxWidth="sm">
      <Box
        sx={{
          mt: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            borderRadius: 2,
          }}
        >
          <CheckCircleOutlineIcon
            sx={{ fontSize: 64, color: 'success.main', mb: 2 }}
          />
          <Typography variant="h5" component="h1" gutterBottom align="center">
            Inscription réussie !
          </Typography>
          <Typography variant="body1" align="center" sx={{ mb: 3 }}>
            Votre compte a été créé avec succès.
          </Typography>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              bgcolor: PRIMARY,
              p: 2,
              borderRadius: 1,
            }}
          >
            <EmailIcon sx={{ mr: 1, color: 'text-meddoc-fonce' }} />
            <Typography variant="body2" color={BLACK}>
              Un email de confirmation a été envoyé à votre adresse email.
              Veuillez vérifier votre boîte de réception pour activer votre compte.
            </Typography>
          </Box>
        </Paper>
      </Box>
    </Container>
    </UnathenticatedLayout>
  );
};

export default RegisterPatientSuccess;
