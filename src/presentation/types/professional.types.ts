export interface ProfessionalAppointment {
  id: string;
  time: string;
  patientName: string;
  duration: number;
  type: string;
  isConfirmed: boolean;
}

export interface RevenueTrend {
  percentage: number;
  isPositive: boolean;
}

export interface Revenue {
  today: number;
  monthly: number;
  yearly: number;
  trend: RevenueTrend;
}

export interface PatientTrends {
  newPatients: number;
  returningPatients: number;
}

export interface PatientMetricsData {
  totalPatients: number;
  todayPatients: number;
  newPatients: number;
  returningPatients: number;
  averageVisitTime: string;
  trends: PatientTrends;
}
