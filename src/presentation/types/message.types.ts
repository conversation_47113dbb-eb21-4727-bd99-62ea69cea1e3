import {
  message_status_enum,
  type_message_enum,
  utilisateurs_role_enum,
} from "@/domain/models/enums";

/**
 * Message status enumeration
 */
export type MessageStatus = "sent" | "delivered" | "read" | "failed";

/**
 * Message type enumeration
 */
export type MessageType = "text" | "image" | "file" | "system";

/**
 * Conversation type enumeration
 */
export type ConversationType = "direct" | "group";

/**
 * User role in messaging context
 */
export type UserRole = "patient" | "professional" | "admin";

/**
 * Individual message interface
 */
export interface MessageDTO {
  /** Unique message identifier */
  id: number;
  /** ID of the conversation this message belongs to */
  conversationId: number;
  /** ID of the user who sent the message */
  senderId: number;
  /** Name of the sender */
  senderName: string;
  /** Role of the sender */
  senderRole: utilisateurs_role_enum;
  /** Message content */
  content: string;
  /** Type of message */
  type: type_message_enum;
  /** Message status */
  status: message_status_enum;
  /** Timestamp when message was sent */
  sentAt: Date;
  /** Timestamp when message was read */
  readAt?: Date;
  /** Whether this message is from the current user */
  isOwn?: boolean;
  /** Optional attachment information */
  attachment?: MessageAttachment;
}

/**
 * Message attachment interface
 */
export interface MessageAttachment {
  /** Attachment ID */
  id: string;
  /** File name */
  name: string;
  /** File size in bytes */
  size: number;
  /** MIME type */
  type: string;
  /** Download URL */
  url: string;
  /** Thumbnail URL for images */
  thumbnailUrl?: string;
}

/**
 * Conversation participant interface
 */
export interface ConversationParticipant {
  /** User ID */
  id: number;
  /** User name */
  name: string;
  /** User role */
  role: utilisateurs_role_enum;
  /** Avatar URL */
  avatarUrl?: string;
  /** Whether user is currently online */
  isOnline?: boolean;
  /** Last seen timestamp */
  lastSeen?: Date;
}

/**
 * Conversation interface
 */
export interface ConversationDTO {
  /** Unique conversation identifier */
  id: number;
  idParticipant: number;
  idExpediteur: number;
  /** Conversation title/name */
  title?: string;
  /** List of participants */
  participant: ConversationParticipant;
  /** Last message in the conversation */
  lastMessage?: string;
  /** Timestamp of last message */
  lastMessageTime?: string;
  /** Number of unread messages */
  unreadCount: number;
  /** Whether conversation is archived */
  isArchived?: boolean;
  /** Whether conversation is muted */
  isMuted?: boolean;
  /** Conversation creation timestamp */
  createdAt: Date;
  /** Last activity timestamp */
  updatedAt: Date;
}

/**
 * Contact interface for user selection
 */
export interface Contact {
  /** Contact ID */
  id: number;
  /** Contact name */
  name: string;
  /** Contact role */
  role: utilisateurs_role_enum;
  /** Contact email */
  email?: string;
  /** Avatar URL */
  avatarUrl?: string;
  /** Whether contact is online */
  isOnline?: boolean;
  /** Specialty (for professionals) */
  specialty?: string;
  /** Department or organization */
  department?: string;
}

/**
 * Message composition state interface
 */
export interface MessageCompositionState {
  /** Message content */
  content: string;
  /** Whether message is being sent */
  isSending: boolean;
  /** Attached files */
  attachments: File[];
  /** Error message if any */
  error?: string;
}

/**
 * Typing user interface
 */
export interface TypingUser {
  userId: number;
  userName?: string;
  timestamp: number;
}

/**
 * Messaging data hook return type
 */
export interface MessagingDataHook {
  /** List of conversations */
  conversations: ConversationDTO[];
  /** Currently active conversation */
  activeConversation: ConversationDTO | null;
  /** Messages in active conversation */
  messages: MessageDTO[];
  /** Available contacts */
  contacts: Contact[];
  /** Loading state */
  loading: boolean;
  /** Error state */
  error: string | null;
  /** Set active conversation */
  setActiveConversation: (conversationId: number) => void;
  /** Send a message */
  sendMessage: (
    conversationId: number,
    content: string,
    attachments?: File[]
  ) => Promise<void>;
  /** Mark conversation as read */
  markAsRead: (conversationId: number) => void;
  /** Search conversations */
  searchConversations: (query: string) => void;
  /** Create new conversation */
  createNewConversation: (contactId: number) => void;
  /** Archive conversation */
  archiveConversation: (conversationId: number) => void;
  /** Delete conversation */
  deleteConversation: (conversationId: number) => void;
  /** Refresh conversations */
  refreshConversations: () => void;
  /** List of users currently typing */
  typingUsers: TypingUser[];
  /** Send typing indicator */
  sendTyping: () => void;
  /** Whether anyone is typing */
  isAnyoneTyping: boolean;
  /** Formatted typing message */
  typingMessage: string;
}

/**
 * Message composition hook return type
 */
export interface MessageCompositionHook {
  /** Composition state */
  state: MessageCompositionState;
  /** Update message content */
  setContent: (content: string) => void;
  /** Add attachment */
  addAttachment: (file: File) => void;
  /** Remove attachment */
  removeAttachment: (index: number) => void;
  /** Send message */
  sendMessage: () => Promise<void>;
  /** Reset composition state */
  reset: () => void;
  handleKeyDown: (event: React.KeyboardEvent) => void;
  handleFileInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  triggerFileSelection: () => void;
  formatFileSize: (bytes: number) => string;
  getFileIcon: (fileType: string) => string;
  canSendMessage: () => boolean;
  fileInputRef: React.RefObject<HTMLInputElement>;
}

/**
 * Message search filters
 */
export interface MessageSearchFilters {
  /** Search query */
  query?: string;
  /** Filter by sender */
  senderId?: string;
  /** Filter by date range */
  dateFrom?: Date;
  dateTo?: Date;
  /** Filter by message type */
  messageType?: type_message_enum;
  /** Filter by conversation */
  conversationId?: string;
}

/**
 * Pagination interface for messages
 */
export interface MessagePagination {
  /** Current page */
  page: number;
  /** Items per page */
  limit: number;
  /** Total items */
  total: number;
  /** Whether there are more items */
  hasMore: boolean;
}

/**
 * Real-time message event types
 */
export type MessageEventType =
  | "message_sent"
  | "message_delivered"
  | "message_read"
  | "user_typing"
  | "user_online"
  | "user_offline"
  | "conversation_created"
  | "conversation_updated";

/**
 * Real-time message event interface
 */
export interface MessageEvent {
  /** Event type */
  type: MessageEventType;
  /** Event data */
  data: any;
  /** Timestamp */
  timestamp: Date;
  /** User who triggered the event */
  userId?: string;
  /** Conversation ID if applicable */
  conversationId?: string;
}
