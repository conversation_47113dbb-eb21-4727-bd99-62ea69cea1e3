export interface PatientAppointment {
  id: string;
  doctorName: string;
  specialty: string;
  date: string;
  time: string;
  duration: number;
  status: AppointmentStatus;
  location: string;
}

export type AppointmentStatus = "upcoming" | "completed" | "cancelled";

export interface HealthMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  date: string;
  trend: {
    value: number;
    isPositive: boolean;
  };
}

export interface Prescription {
  id: string;
  medication: string;
  dosage: string;
  frequency: string;
  startDate: string;
  endDate: string;
  doctorName: string;
  isActive: boolean;
  refillsLeft: number;
}

export interface Document {
  id: string;
  title: string;
  type: "prescription" | "report" | "analysis" | "other";
  date: string;
  doctorName: string;
  isNew: boolean;
}

export interface PatientTrends {
  newPatients: number;
}

export interface PatientMetricsData {
  totalPatients: number;
  newPatients: number;
  trends: PatientTrends;
}
