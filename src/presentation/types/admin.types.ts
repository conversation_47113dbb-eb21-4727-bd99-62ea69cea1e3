export type AppointmentStatus = 'pending' | 'confirmed' | 'cancelled';

export interface Appointment {
  id: string;
  patientName: string;
  date: string;
  time: string;
  status: AppointmentStatus;
}

export interface DashboardStats {
  totalPatients: number;
  totalAppointments: number;
  completionRate: number;
  averageWaitTime: string;
}

export interface AppointmentData {
  date: string;
  appointments: number;
}
