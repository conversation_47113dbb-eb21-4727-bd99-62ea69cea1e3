import GetMedicamentListUsecase from "@/domain/usecases/medicament/GetMedicamentListUsecase.ts";
import GetStocksDataUsecase from "@/domain/usecases/stocks/GetStocksDataUsecase";
import GetMedicamentListRepository from "@/infrastructure/repositories/medicament/GetMedicamentListRepository.ts";
import GetStocksDataRepository from "@/infrastructure/repositories/stocks/GetStocksDataRepository";
import { useEffect, useState } from "react";

const Test = () => {
  const [data, setData] = useState<string>("");

  useEffect(() => {
    const test = async () => {
      try {
        const repository = new GetMedicamentListRepository();
        const usecase = new GetMedicamentListUsecase(repository);
        const data = await usecase.execute("glu");
        console.log(data);
        setData(JSON.stringify(data, null, 2));
      } catch (error) {
        console.log("Error", error);
        setData("Error: " + error.message);
      }
    };
    test();
  }, []);

  return (
    <div style={{ padding: "20px", fontFamily: "monospace" }}>
      <h2>Test</h2>
      <pre style={{ background: "#f5f5f5", padding: "10px", overflow: "auto" }}>
        {data}
      </pre>
    </div>
  );
};

export default Test;
