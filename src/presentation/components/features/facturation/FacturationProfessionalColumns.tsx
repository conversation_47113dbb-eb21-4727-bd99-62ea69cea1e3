import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { FacturationDTO } from "@/domain/DTOS/FacturationDTO";

export const FacturationProfessionalColumns = (): GridColDef[] => {
  return [
    {
      field: "date_creation",
      headerName: "Date",
      width: 160,
      valueFormatter: (params) => new Date(params).toLocaleString(),
      headerClassName: "font-semibold",
    },
    {
      field: "patient",
      headerName: "Patient",
      width: 250,
      renderCell: (params: GridRenderCellParams<FacturationDTO>) =>
        `${params.row.patient.nom} ${params.row.patient.prenom}`,
      headerClassName: "font-semibold",
    },
    {
      field: "montant",
      headerName: "Montant",
      width: 150,
      headerClassName: "font-semibold",
    },
    {
      field: "total_paye",
      headerName: "Total payé",
      width: 150,
      headerClassName: "font-semibold",
    },
    {
      field: "informations",
      headerName: "Informations",
      width: 150,
      headerClassName: "font-semibold",
    },
    {
      field: "equilibre du",
      headerName: "Equilibre du",
      width: 100,
      renderCell: (params: GridRenderCellParams<FacturationDTO>) =>
        `${params.row.montant - params.row.total_paye}`,
      headerClassName: "font-semibold",
    },
  ];
};
