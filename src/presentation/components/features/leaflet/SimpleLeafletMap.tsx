import { ProfessionalCardDTO, ProfessionalProfileData } from "@/domain/DTOS";
import { EtablissementProfessionnel } from "@/domain/models/EtablissementProfessionnel.ts";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { useMemo, useRef, ComponentProps } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ileL<PERSON>er } from "react-leaflet";
import { twMerge } from "tailwind-merge";

interface LeafletMapProps extends ComponentProps<"div"> {
  geolocation: string; // A parser
  etablishment: EtablissementProfessionnel[];
}

const SimpleLeafletMap: React.FC<LeafletMapProps> = ({
  geolocation,
  etablishment,
  className,
  ...props
}) => {
  const mapRef = useRef<L.Map | null>(null);

  const parseGeolocation = (
    geolocation: string | null | undefined
  ): [number, number] => {
    if (!geolocation) return [0, 0];

    // Enlever les parenthèses et séparer lat/long
    const coords = geolocation.replace(/[()]/g, "").split(",");
    if (coords.length !== 2) return [0, 0];

    // Parser en nombres: on considère la forme (latitude, longitude)
    const [latRaw, lngRaw] = coords;
    const lat = Number(latRaw);
    const lng = Number(lngRaw);

    if (Number.isNaN(lat) || Number.isNaN(lng)) return [0, 0];

    return [lat, lng];
  };

  // Create marker icons
  const defaultIcon = L.icon({
    iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
  });

  // Memoize map markers
  const MapMarkers = useMemo(
    () =>
      etablishment.map((et) => {
        const [lat, lng] = parseGeolocation(String(geolocation));

        return (
          <Marker key={`${et.id}`} position={[lat, lng]} icon={defaultIcon}>
            <Popup>
              <strong>{et.nom_etablissement}</strong>
              <br />
              {et.nom_etablissement && (
                <>
                  <strong>{et.nom_responsable}</strong>
                  <br />
                </>
              )}
              {et.equipe}
            </Popup>
          </Marker>
        );
      }),
    [etablishment, geolocation, defaultIcon]
  );
  return (
    <MapContainer
      center={[-18.91, 47.525]}
      zoom={6}
      className={twMerge(
        "w-full h-full animate-fade-in z-0 border rounded-lg",
        className
      )}
      {...props}
      ref={mapRef}
    >
      <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
      {MapMarkers}
    </MapContainer>
  );
};

export default SimpleLeafletMap;
