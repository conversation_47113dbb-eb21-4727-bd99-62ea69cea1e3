import React, {
  useEffect,
  useRef,
  useState,
  useMemo,
  useCallback,
} from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  useMapE<PERSON>s,
  useMap,
} from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { Search } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";

interface LocationPickerMapProps {
  value: string;
  onChange: (value: string) => void;
}

interface SearchResult {
  display_name: string;
  lat: string;
  lon: string;
}

// Composant pour gérer les événements de la carte
const MapEventHandler = React.memo(
  ({
    onLocationSelect,
  }: {
    onLocationSelect: (lat: number, lng: number) => void;
  }) => {
    useMapEvents({
      click: (e) => {
        onLocationSelect(e.latlng.lat, e.latlng.lng);
      },
    });
    return null;
  }
);

// Composant pour accéder à l'instance de la carte
const MapController = React.memo(
  ({ mapRef }: { mapRef: React.MutableRefObject<L.Map | null> }) => {
    const map = useMap();
    mapRef.current = map;
    return null;
  }
);

const LocationPickerMap: React.FC<LocationPickerMapProps> = ({
  value,
  onChange,
}) => {
  const [position, setPosition] = useState<[number, number]>([-18.91, 47.525]); // Position par défaut (Madagascar)
  const mapRef = useRef<L.Map | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [showResults, setShowResults] = useState<boolean>(false);

  // Icône du marqueur - mémorisée car elle ne change jamais
  const markerIcon = useMemo(
    () =>
      L.icon({
        iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
      }),
    []
  );

  // Analyser la valeur de géolocalisation existante
  useEffect(() => {
    if (value) {
      try {
        // Format attendu: "(latitude,longitude)"
        const coords = value.replace(/[()]/g, "").split(",");
        if (coords.length === 2) {
          const [lat, lng] = coords.map(Number);
          if (!isNaN(lat) && !isNaN(lng)) {
            setPosition([lat, lng]);
          }
        }
      } catch (error) {
        console.error("Erreur lors de l'analyse de la géolocalisation:", error);
      }
    }
  }, [value]);

  // Fonction pour rechercher une adresse - mémorisée pour éviter les recréations à chaque rendu
  const searchAddress = useCallback(async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setShowResults(true);

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
          searchQuery
        )}&limit=5&countrycodes=mg`
      );

      if (!response.ok) {
        throw new Error("Erreur lors de la recherche d'adresse");
      }

      const data = await response.json();
      setSearchResults(data);
    } catch (error) {
      console.error("Erreur lors de la recherche:", error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery]);

  // Gérer la sélection d'un résultat de recherche
  const handleSelectSearchResult = useCallback(
    (result: SearchResult) => {
      const lat = parseFloat(result.lat);
      const lng = parseFloat(result.lon);

      if (!isNaN(lat) && !isNaN(lng)) {
        setPosition([lat, lng]);
        onChange(`(${lat},${lng})`);

        // Centrer la carte sur la position sélectionnée
        if (mapRef.current) {
          mapRef.current.setView([lat, lng], 14);
        }
      }

      setShowResults(false);
      setSearchQuery("");
    },
    [onChange, mapRef]
  );

  // Gérer la sélection d'un emplacement sur la carte
  const handleLocationSelect = useCallback(
    (lat: number, lng: number) => {
      setPosition([lat, lng]);
      onChange(`(${lat},${lng})`);
    },
    [onChange]
  );

  // Utiliser la géolocalisation du navigateur
  const handleUseCurrentLocation = useCallback(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setPosition([latitude, longitude]);
          onChange(`(${latitude},${longitude})`);

          // Centrer la carte sur la position actuelle
          if (mapRef.current) {
            mapRef.current.setView([latitude, longitude], 14);
          }
        },
        (error) => {
          console.error("Erreur de géolocalisation:", error);
        }
      );
    } else {
      console.error(
        "La géolocalisation n'est pas prise en charge par ce navigateur."
      );
    }
  }, [onChange, mapRef]);

  return (
    <div className="flex flex-col w-full">
      <p className="text-sm mb-2">
        Recherchez une adresse ou cliquez sur la carte pour sélectionner votre
        emplacement
      </p>

      {/* Barre de recherche */}
      <div
        className="flex mb-4 relative"
        style={{ position: "relative", zIndex: 1000 }}
      >
        <input
          type="text"
          className="w-full h-10 px-3 py-2 rounded-lg border border-gray-200 text-gray-700 focus:outline-none focus:border-gray-300 mr-2"
          placeholder="Rechercher une adresse..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && searchAddress()}
        />
        <button
          className="min-w-[40px] h-10 bg-meddoc-primary text-white rounded-lg flex items-center justify-center"
          onClick={searchAddress}
          disabled={isSearching}
        >
          {isSearching ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          ) : (
            <Search size={20} />
          )}
        </button>

        {/* Résultats de recherche */}
        {showResults && searchResults.length > 0 && (
          <div
            className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-[200px] overflow-auto"
            style={{ position: "absolute", zIndex: 9999 }}
          >
            <ul className="divide-y divide-gray-100">
              {searchResults.map((result, index) => (
                <li
                  key={index}
                  className="px-3 py-2 hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleSelectSearchResult(result)}
                >
                  <p className="text-sm truncate" title={result.display_name}>
                    {result.display_name}
                  </p>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      <div
        className="relative h-[300px] w-full rounded-lg overflow-hidden border border-gray-200 mb-2"
        style={{ position: "relative", zIndex: 1 }}
      >
        <MapContainer
          center={position}
          zoom={14}
          style={{ height: "100%", width: "100%" }}
          className=""
        >
          <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
          <Marker position={position} icon={markerIcon} />
          <MapEventHandler onLocationSelect={handleLocationSelect} />
          <MapController mapRef={mapRef} />
        </MapContainer>
      </div>

      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-500">
          Coordonnées: {position[0].toFixed(6)}, {position[1].toFixed(6)}
        </p>
        <p
          className="text-sm text-meddoc-primary cursor-pointer underline"
          onClick={handleUseCurrentLocation}
        >
          Utiliser ma position actuelle
        </p>
      </div>
    </div>
  );
};

// Utilisation de React.memo pour éviter les re-rendus inutiles
export default React.memo(LocationPickerMap);
