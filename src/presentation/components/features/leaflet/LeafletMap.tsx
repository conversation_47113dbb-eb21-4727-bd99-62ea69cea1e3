import { ProfessionalCardDTO, ProfessionalProfileData } from "@/domain/DTOS";
import L from "leaflet";
import { useMemo, useRef, useEffect, ComponentProps } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, TileLayer } from "react-leaflet";
import { twMerge } from "tailwind-merge";

interface LeafletMapProps extends ComponentProps<"div"> {
  filteredData: ProfessionalCardDTO[] | ProfessionalProfileData[];
  hoveredId: number | null;
}

const LeafletMap: React.FC<LeafletMapProps> = ({
  filteredData,
  hoveredId,
  className,
  ...props
}) => {
  const scrollToProfessionalCard = (id: number) => {
    const element = document.getElementById(`professional-card-${id}`);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" });
      // Ajouter une classe temporaire pour la mise en évidence
      element.classList.add("border-meddoc-primary", "shadow-xl", "shadow-meddoc-primary");
      // Retirer la mise en évidence après 1.5 secondes
      setTimeout(
        () =>
          element.classList.remove(
            "border-meddoc-primary",
            "shadow-md",
            "shadow-meddoc-primary",
          ),
        1000,
      );
    }
  };
  const mapRef = useRef<L.Map | null>(null);

  const parseGeolocation = (
    geolocation: string | null | undefined,
  ): [number, number] => {
    if (!geolocation) return [0, 0];

    // Enlever les parenthèses et séparer lat/long
    const coords = geolocation.replace(/[()]/g, "").split(",");
    if (coords.length !== 2) return [0, 0];

    // Parser en nombres
    const [longitude, latitude] = coords.map(Number);

    return [longitude, latitude];
  };

  useEffect(() => {
    if (hoveredId && mapRef.current) {
      const hoveredProfessional = filteredData.find(
        (item) => item.id === hoveredId,
      );
      if (hoveredProfessional) {
        const [lat, lng] = parseGeolocation(
          String(hoveredProfessional.geolocalisation),
        );
        mapRef.current.setView([lat, lng], 14, {
          animate: true,
          duration: 1, // durée de l'animation en secondes
        });
      }
    }
  }, [hoveredId, filteredData]);
  // Create marker icons
  const defaultIcon = L.icon({
    iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
  });

  const highlightedIcon = L.icon({
    iconUrl:
      "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
  });

  // Memoize map markers
  const MapMarkers = useMemo(
    () =>
      filteredData.flatMap((item) =>
        item.etablissements_professionnel.map((etablissement) => {
          const [latitude, longitude] = parseGeolocation(
            String(item.geolocalisation),
          );

          return (
            <Marker
              key={`${item.id}-${etablissement.id}`}
              position={[latitude, longitude]}
              icon={hoveredId === item.id ? highlightedIcon : defaultIcon}
              eventHandlers={{
                click: () => scrollToProfessionalCard(item.id),
              }}
            >
              <Popup>
                <strong>
                  {item.titre} {item.nom} {item.prenom}
                </strong>
                <br />
                {etablissement.nom_etablissement && (
                  <>
                    <strong>{etablissement.nom_responsable}</strong>
                    <br />
                  </>
                )}
                {etablissement.equipe}
                <br />
                {item.district} {item.commune}
              </Popup>
            </Marker>
          );
        }),
      ),
    [filteredData, hoveredId, defaultIcon, highlightedIcon],
  );
  return (
    <MapContainer
      center={[-18.91, 47.525]}
      zoom={6}
      style={{ height: "100%", width: "100%" }}
      className={twMerge(
        "w-full h-full animate-fade-in z-0 border rounded-lg",
        className,
      )}
      {...props}
      ref={mapRef}
    >
      <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
      {MapMarkers}
    </MapContainer>
  );
};

export default LeafletMap;
