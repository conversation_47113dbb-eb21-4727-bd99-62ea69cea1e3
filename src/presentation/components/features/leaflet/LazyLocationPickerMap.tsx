import React, { Suspense, lazy, useState, useEffect, forwardRef } from "react";

// Import dynamique du composant LocationPickerMap
const LocationPickerMap = lazy(() => import("./LocationPickerMap"));

interface LazyLocationPickerMapProps {
  value: string;
  onChange: (value: string) => void;
  isVisible: boolean;
  name?: string;
}

// Utilisation de forwardRef pour permettre au composant de recevoir une ref
const LazyLocationPickerMap = forwardRef<
  HTMLDivElement,
  LazyLocationPickerMapProps
>(({ value, onChange, isVisible, ...rest }, ref) => {
  // État pour suivre si la carte a déjà été chargée
  const [hasBeenVisible, setHasBeenVisible] = useState(false);

  // Mettre à jour l'état lorsque le composant devient visible
  useEffect(() => {
    if (isVisible && !hasBeenVisible) {
      setHasBeenVisible(true);
    }
  }, [isVisible, hasBeenVisible]);

  // Si le composant n'a jamais été visible, on ne charge rien
  if (!isVisible && !hasBeenVisible) {
    return null;
  }

  // Si le composant a déjà été visible mais n'est plus visible, on cache la carte mais on la garde en mémoire
  if (!isVisible && hasBeenVisible) {
    return (
      <div ref={ref} className="hidden" {...rest}>
        <LocationPickerMap value={value} onChange={onChange} />
      </div>
    );
  }

  // Fallback pendant le chargement
  const loadingFallback = (
    <div
      ref={ref}
      className="h-[300px] w-full flex items-center justify-center border border-gray-200 rounded-lg bg-gray-50"
      {...rest}
    >
      <div className="w-10 h-10 border-4 border-meddoc-primary border-t-transparent rounded-full animate-spin"></div>
    </div>
  );

  return (
    <div ref={ref} {...rest}>
      <Suspense fallback={loadingFallback}>
        <LocationPickerMap value={value} onChange={onChange} />
      </Suspense>
    </div>
  );
});

// Ajout d'un displayName pour faciliter le débogage
LazyLocationPickerMap.displayName = "LazyLocationPickerMap";

// Utilisation de React.memo pour éviter les re-rendus inutiles
export default React.memo(LazyLocationPickerMap, (prevProps, nextProps) => {
  // Ne re-rendre que si les props importantes changent
  return (
    prevProps.isVisible === nextProps.isVisible &&
    prevProps.value === nextProps.value
  );
});
