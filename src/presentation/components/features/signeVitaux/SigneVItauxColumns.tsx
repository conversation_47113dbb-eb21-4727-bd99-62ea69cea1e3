import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { useNavigate } from "react-router-dom";

export interface signe_vitaux {
    id: number;
    id_carnet: number;
    taille: number;
    poid: number;
    indice_masse_corporel: number;
    temperature: number;
    circonference_tete: number;
    frequence_cardiaque: number;
    frequence_respiratoire: number;
    sa02: number;
    niveau_glucose: number;
    tension_arterielle: string;
    date_visite: Date;
    // confidentielite: boolean
}

export const SigneVitauxColumns = (onViewSigneVitaux?: (signeVitaux: any) => void): GridColDef[] => {
    const navigate = useNavigate();

    return [
        {
            field: "date_visite",
            headerName: "Date de visite",
            flex: 1.2,
            renderCell: (params: GridRenderCellParams<signe_vitaux>) => {
                const dateField = params.row.date_visite;
                if (!dateField) return "";

                const date = new Date(dateField);

                const dateOptions: Intl.DateTimeFormatOptions = {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                };

                const timeOptions: Intl.DateTimeFormatOptions = {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                };

                const dateStr = date.toLocaleDateString('fr-FR', dateOptions);
                const timeStr = date.toLocaleTimeString('fr-FR', timeOptions);

                return (
                    <div className="text-gray-700">
                        <div className="text-sm font-medium">{dateStr}</div>
                        <div className="text-xs text-gray-500">{timeStr}</div>
                    </div>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "taille",
            headerName: "Taille (cm)",
            flex: 1,
            renderCell: (params: GridRenderCellParams<signe_vitaux>) => {
                if (!params.row.taille) return "";

                return (
                    <span className="text-gray-800 font-medium">
                        {params.row.taille} cm
                    </span>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "poid",
            headerName: "Poids (kg)",
            flex: 1,
            renderCell: (params: GridRenderCellParams<signe_vitaux>) => {
                if (!params.row.poid) return "";

                return (
                    <span className="text-gray-800 font-medium">
                        {params.row.poid} kg
                    </span>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "indice_masse_corporel",
            headerName: "IMC",
            flex: 1,
            renderCell: (params: GridRenderCellParams<signe_vitaux>) => {
                if (!params.row.indice_masse_corporel) return "";

                const imc = params.row.indice_masse_corporel;
                let color = "text-gray-800";

                // Classification IMC standard
                if (imc < 18.5) color = "text-blue-600"; // Sous-poids
                else if (imc >= 18.5 && imc < 25) color = "text-green-600"; // Normal
                else if (imc >= 25 && imc < 30) color = "text-orange-600"; // Surpoids
                else if (imc >= 30) color = "text-red-600"; // Obésité

                return (
                    <span className={`font-medium ${color}`}>
                        {imc.toFixed(1)}
                    </span>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "temperature",
            headerName: "Température (°C)",
            flex: 1.2,
            renderCell: (params: GridRenderCellParams<signe_vitaux>) => {
                if (!params.row.temperature) return "";

                const temp = params.row.temperature;
                let color = "text-gray-800";

                // Classification température
                if (temp < 36.1) color = "text-blue-600"; // Hypothermie
                else if (temp >= 36.1 && temp <= 37.2) color = "text-green-600"; // Normal
                else if (temp > 37.2) color = "text-red-600"; // Fièvre

                return (
                    <span className={`font-medium ${color}`}>
                        {temp}°C
                    </span>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "tension_arterielle",
            headerName: "Tension artérielle",
            flex: 1.3,
            renderCell: (params: GridRenderCellParams<signe_vitaux>) => {
                if (!params.row.tension_arterielle) return "";

                return (
                    <span className="text-gray-800 font-medium">
                        {params.row.tension_arterielle} mmHg
                    </span>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "frequence_cardiaque",
            headerName: "FC (bpm)",
            flex: 1,
            renderCell: (params: GridRenderCellParams<signe_vitaux>) => {
                if (!params.row.frequence_cardiaque) return "";

                const fc = params.row.frequence_cardiaque;
                let color = "text-gray-800";

                // Classification fréquence cardiaque adulte au repos
                if (fc < 60) color = "text-blue-600"; // Bradycardie
                else if (fc >= 60 && fc <= 100) color = "text-green-600"; // Normal
                else if (fc > 100) color = "text-red-600"; // Tachycardie

                return (
                    <span className={`font-medium ${color}`}>
                        {fc}
                    </span>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "sa02",
            headerName: "SpO₂ (%)",
            flex: 1,
            renderCell: (params: GridRenderCellParams<signe_vitaux>) => {
                if (!params.row.sa02) return "";

                const spo2 = params.row.sa02;
                let color = "text-gray-800";

                // Classification SpO2
                if (spo2 < 90) color = "text-red-600"; // Critique
                else if (spo2 >= 90 && spo2 < 95) color = "text-orange-600"; // Faible
                else if (spo2 >= 95) color = "text-green-600"; // Normal

                return (
                    <span className={`font-medium ${color}`}>
                        {spo2}%
                    </span>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "actions",
            headerName: "Actions",
            flex: 1.2,
            renderCell: (params: GridRenderCellParams<signe_vitaux>) => {
                return (
                    <div className="flex gap-1 justify-center items-center h-full min-h-[40px]">
                        <button
                            className="bg-blue-500 text-white px-2 py-1 text-xs rounded hover:bg-blue-600 transition-colors"
                            onClick={() => {
                                if (onViewSigneVitaux) {
                                    onViewSigneVitaux(params.row);
                                }
                            }}
                        >
                            Voir
                        </button>
                    </div>
                );
            },
            headerClassName: "font-semibold",
            sortable: false,
        },
    ];
};