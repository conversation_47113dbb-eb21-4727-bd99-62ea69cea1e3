import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { VitalSignsDetails } from "@/domain/DTOS/VitalSignsDetailsDTO";

export const SigneVitauxPatientColumns = (): GridColDef[] => {
    return [
        {
            field: "dateConsultation",
            headerName: "Date consultation",
            flex: 1.2,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) => {
                if (!params.row.dateConsultation) return "";

                const date = new Date(params.row.dateConsultation);

                const dateOptions: Intl.DateTimeFormatOptions = {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                };

                const timeOptions: Intl.DateTimeFormatOptions = {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: false,
                };

                const dateStr = date.toLocaleDateString("fr-FR", dateOptions);
                const timeStr = date.toLocaleTimeString("fr-FR", timeOptions);

                return (
                    <div className="text-gray-700">
                        <div className="text-sm font-medium">{dateStr}</div>
                        <div className="text-xs text-gray-500">{timeStr}</div>
                    </div>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "taille",
            headerName: "Taille (cm)",
            flex: 1,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.taille ? `${params.row.taille} cm` : "-",
            headerClassName: "font-semibold",
        },
        {
            field: "poids",
            headerName: "Poids (kg)",
            flex: 1,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.poids ? `${params.row.poids} kg` : "-",
            headerClassName: "font-semibold",
        },
        {
            field: "indiceMasseCorporel",
            headerName: "IMC",
            flex: 1,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.indiceMasseCorporel ? params.row.indiceMasseCorporel.toFixed(1) : "-",
            headerClassName: "font-semibold",
        },
        {
            field: "temperature",
            headerName: "Température (°C)",
            flex: 1,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.temperature ? `${params.row.temperature} °C` : "-",
            headerClassName: "font-semibold",
        },
        {
            field: "circonferenceTete",
            headerName: "Tour de tête (cm)",
            flex: 1.2,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.circonferenceTete ? `${params.row.circonferenceTete} cm` : "-",
            headerClassName: "font-semibold",
        },
        {
            field: "frequenceCardiaque",
            headerName: "Fréquence cardiaque (bpm)",
            flex: 1.5,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.frequenceCardiaque ? `${params.row.frequenceCardiaque} bpm` : "-",
            headerClassName: "font-semibold",
        },
        {
            field: "frequenceRespiratoire",
            headerName: "Fréquence respiratoire (/min)",
            flex: 1.5,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.frequenceRespiratoire ? `${params.row.frequenceRespiratoire} /min` : "-",
            headerClassName: "font-semibold",
        },
        {
            field: "sa02",
            headerName: "SaO2 (%)",
            flex: 1,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.sa02 ? `${params.row.sa02}%` : "-",
            headerClassName: "font-semibold",
        },
        {
            field: "niveauGlucose",
            headerName: "Glucose (mg/dL)",
            flex: 1.2,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.niveauGlucose ? `${params.row.niveauGlucose} mg/dL` : "-",
            headerClassName: "font-semibold",
        },
        {
            field: "tensionArterielle",
            headerName: "Tension artérielle",
            flex: 1.5,
            renderCell: (params: GridRenderCellParams<VitalSignsDetails>) =>
                params.row.tensionArterielle ? params.row.tensionArterielle : "-",
            headerClassName: "font-semibold",
        }
    ];
};
