import {
  memo,
  useRef,
  useEffect,
  useState,
  useCallback,
  ComponentProps,
} from "react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import { useAvailability } from "@/presentation/hooks/use-availability";
import { useResponsiveGrid } from "@/presentation/hooks/use-responsive-grid";
import Navigation from "./components/Navigation";
import DayColumn from "./components/DayColumn";
import EmptyState from "./components/EmptyState";
import ShowMoreButton from "./components/ShowMoreButton";
import { twMerge } from "tailwind-merge";
import { useEmptyState } from "@/presentation/hooks/use-empty-state";
import ShowLessButton from "./components/ShowLessButton";

interface AvailabilityProps extends ComponentProps<"div"> {
  professionalInformation: ProfessionalCardDTO;
  horairesRef: React.MutableRefObject<HTMLDivElement>;
}

const Availability = memo(
  ({
    professionalInformation,
    horairesRef,
    className,
    ...props
  }: AvailabilityProps) => {
    const [isLoading, setIsLoading] = useState(false);

    const {
      schedule,
      grid,
      voirPlus,
      startDate,
      handlePrevious,
      handleNext,
      isDisabled,
      toggleVoirPlus,
      updateDateCount,
      handleSelectTimeSlot,
      setStartDate,
      extremum,
      events,
    } = useAvailability(professionalInformation.disponibilite);

    const { elementRef } = useResponsiveGrid({
      onWidthChange: updateDateCount,
    });

    // On passe une fonction vide car on n'utilise pas la navigation dans ce contexte
    const { shouldDisplay: isScheduleTimeSlotEmpty } = useEmptyState({
      schedule,
      startDate,
      maxDate: extremum?.max || undefined,
      minDate: extremum?.min || undefined,
      onNavigateToDate: () => {},
      events: professionalInformation.disponibilite,
    });

    const isVisible = () => {
      return !voirPlus && schedule.some((day) => day.horaires.length > 3);
    };

    return (
      <div
        className={twMerge("relative h-full flex flex-col mx-8", className)}
        {...props}
      >
        <div className="flex flex-col flex-1">
          <div
            ref={elementRef}
            className={`grid ${grid} bg-white dark:bg-transparent border relative h-full`}
          >
            <Navigation
              onPrevious={handlePrevious}
              onNext={handleNext}
              isDisabled={isDisabled()}
            />
            {schedule.map((day, index) => (
              <DayColumn
                key={index}
                day={day}
                voirPlus={voirPlus}
                isScheduleTimeSlotEmpty={isScheduleTimeSlotEmpty}
                professionalInformation={professionalInformation}
                onTimeSlotSelect={handleSelectTimeSlot}
              />
            ))}
            <EmptyState
              schedule={schedule}
              startDate={startDate}
              minDate={extremum?.min}
              maxDate={extremum?.max}
              isLoading={isLoading}
              events={professionalInformation.disponibilite}
              onNavigateToDate={(date) => {
                setIsLoading(true);
                setStartDate(date);
                setTimeout(() => setIsLoading(false), 500);
              }}
            />
          </div>
          {!voirPlus ? (
            <ShowMoreButton
              isVisible={isVisible()}
              onClick={() => toggleVoirPlus(horairesRef)}
            />
          ) : (
            <ShowLessButton
              isVisible={isVisible()}
              onClick={() => toggleVoirPlus(horairesRef)}
            />
          )}
        </div>
      </div>
    );
  }
);

Availability.displayName = "Availability";

export default Availability;
