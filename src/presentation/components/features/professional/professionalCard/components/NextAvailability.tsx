import Button from "@/presentation/components/common/Button/Button";
import { AVAILABILITY_CLASSNAMES } from "@/shared/constants/availabilityConstants";
import { ChevronRight, EyeIcon } from "lucide-react";
import { memo } from "react";

const NextAvailabilityButton = memo<{
  nextTimeFormated: string;
  onNextAvailabilityClick: () => void;
}>(({ nextTimeFormated, onNextAvailabilityClick }) => (
  <Button
    onClick={onNextAvailabilityClick}
    className={AVAILABILITY_CLASSNAMES.BUTTON.NEXT_AVAILABILITY}
    aria-label={`Voir le prochain rendez-vous disponible le ${nextTimeFormated}`}
  >
    <EyeIcon />
    Prochain date de RDV disponible le {nextTimeFormated}
    <ChevronRight />
  </Button>
));

export default NextAvailabilityButton