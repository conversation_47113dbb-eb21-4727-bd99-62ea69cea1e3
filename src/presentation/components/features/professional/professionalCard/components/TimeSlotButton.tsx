import React, { memo } from 'react';
import Button from "@/presentation/components/common/Button/Button";

interface TimeSlotButtonProps {
  time: string;
  onSelect: () => void;
}

const TimeSlotButton = memo(({ time, onSelect }: TimeSlotButtonProps) => (
  <Button
    onClick={onSelect}
    className="block bg-gray-100 text-gray-700 py-2 text-sm hover:bg-gray-200 transition-colors"
    aria-label={`Sélectionner le créneau de ${time.split(' - ')[0]}`}
  >
    {time.split(' - ')[0]}
  </Button>
));

TimeSlotButton.displayName = 'TimeSlotButton';

export default TimeSlotButton;
