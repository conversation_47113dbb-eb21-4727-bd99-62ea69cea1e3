import { memo, useEffect } from "react";
import { CalendarOff, ChevronRight, EyeIcon } from "lucide-react";
import Button from "@/presentation/components/common/Button/Button";
import DateClass from "@/shared/utils/DateClass";
import { useEmptyState } from "@/presentation/hooks/use-empty-state";
import { ScheduleDay } from "@/domain/types/schedule";
import { TimeSlotProffessionalCard } from "@/domain/DTOS";
import { EmptyStateType } from "@/domain/types/empty-state";

interface EmptyStateProps {
  schedule: ScheduleDay[];
  startDate: DateClass;
  minDate?: Date;
  maxDate?: Date;
  isLoading: boolean;
  onNavigateToDate: (date: DateClass) => void;
  events: TimeSlotProffessionalCard[];
}

/**
 * Composant pour afficher l'état vide du calendrier
 * et le bouton de navigation vers la prochaine disponibilité
 */
const EmptyState = memo<EmptyStateProps>(
  ({
    schedule,
    startDate,
    minDate,
    maxDate,
    isLoading,
    onNavigateToDate,
    events,
  }) => {
    const { shouldDisplay, stateType, message, formattedDate, handleNextAvailabilityClick } = useEmptyState({
      schedule,
      startDate,
      maxDate,
      minDate,
      onNavigateToDate,
      events,
    });

    // Si on ne doit pas afficher l'état vide, on ne rend rien
    if (!shouldDisplay) return null;

    return (
      <div className="w-[97%] h-[75%] flex flex-col justify-center align-middle bg-gray-100/90 backdrop-blur-sm rounded-md shadow-md left-1/2 -translate-x-1/2 text-center absolute bottom-2">
        <div className="flex flex-col items-center justify-center gap-2">
          {stateType !== EmptyStateType.HAS_NEXT_SLOT && (
            <>
              <CalendarOff size={20} />
              {/* Message principal */}
              <span className="text-sm">{message.title}</span>
              {message.description && (
                <span className="text-xs text-gray-500">{message.description}</span>
              )}
            </>
          )}



          {/* Bouton d'action (uniquement pour HAS_NEXT_SLOT) */}
          {stateType === EmptyStateType.HAS_NEXT_SLOT && formattedDate && (
            <div className="flex flex-col gap-2 w-full items-center">
              <Button
                onClick={handleNextAvailabilityClick}
                className="flex items-center gap-2 px-4 py-2 bg-meddoc-primary text-white rounded-md transition-colors"
                disabled={isLoading}
              >
                <EyeIcon size={20} />
                <span>Prochaine RDV le {formattedDate}</span>
                <ChevronRight size={20} className="ml-1" />
              </Button>

              {/* Loader */}
              {isLoading && (
                <div className="w-full max-w-xs bg-gray-200 rounded-full h-1">
                  <div
                    className="bg-meddoc-primary h-1 rounded-full animate-[loading_0.5s_ease-in-out]"
                    style={{ width: "100%" }}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  },
);

EmptyState.displayName = "EmptyState";

export default EmptyState;
