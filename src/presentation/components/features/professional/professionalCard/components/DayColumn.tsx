import { memo } from "react";
import Button from "@/presentation/components/common/Button/Button";
import { ProfessionalCardDTO, TimeSlotProffessionalCard } from "@/domain/DTOS";
import DateClass from "@/shared/utils/DateClass";
import { AVAILABILITY_CONSTANTS } from "@/shared/constants/availabilityConstants";
import {
  filterFutureTimeSlots,
  areAllTimeSlotsEmpty,
} from "@/shared/utils/timeSlotUtils";

interface DayColumnProps {
  day: {
    date: DateClass;
    horaires: string[];
  };
  voirPlus: boolean;
  isScheduleTimeSlotEmpty: boolean;
  professionalInformation: ProfessionalCardDTO;
  onTimeSlotSelect: (
    professionalID: number,
    selectedProfessional: ProfessionalCardDTO,
    selectedTimeSlot: TimeSlotProffessionalCard
  ) => void;
}

/**
 * Composant représentant une colonne de jour avec ses créneaux horaires
 */
const DayColumn = memo<DayColumnProps>(
  ({
    day,
    voirPlus,
    isScheduleTimeSlotEmpty,
    professionalInformation,
    onTimeSlotSelect,
  }) => {
    /**
     * Rendu d'un créneau horaire vide
     */
    const EmptyTimeSlot = () => (
      <div className="block bg-gray-50 text-gray-400 dark:bg-gray-400/50 dark:text-white py-2 text-sm cursor-not-allowed">
        -
      </div>
    );

    /**
     * Gestion de la sélection d'un créneau horaire
     */
    const handleTimeSelection = (time: string) => {
      const currentDate = `${day.date.getAnnee()}-${String(day.date.getMois()).padStart(2, "0")}-${String(day.date.getJour()).padStart(2, "0")}`;
      const [startTime] = time.split(" - ");
      const selectedTimeSlot = professionalInformation.disponibilite.find(
        (slot) => slot.date === currentDate && slot.start === startTime
      );

      if (selectedTimeSlot) {
        onTimeSlotSelect(
          professionalInformation.id,
          professionalInformation,
          selectedTimeSlot
        );
      }
    };

    return (
      <div className="text-center border-r last:border-r-0 h-full flex flex-col">
        <div className="text-sm font-medium text-gray-600 dark:text-white py-2 border-b bg-gray-50 dark:bg-transparent">
          <div>
            {day.date.dayName.charAt(0).toUpperCase() +
              day.date.dayName.slice(1).toLowerCase()}
          </div>
          <div>{day.date.day}</div>
        </div>
        <div className="flex flex-col gap-1 p-2 flex-1">
          {filterFutureTimeSlots(day.horaires, day.date)
            .slice(
              0,
              voirPlus
                ? undefined
                : AVAILABILITY_CONSTANTS.AVAILABILITY_ROW_COUNT
            )
            .map((time, timeIndex) => {
              const [start] = time.split(" - ");
              return time === "-" ? (
                <EmptyTimeSlot key={timeIndex} />
              ) : (
                <Button
                  variant="primary"
                  key={timeIndex}
                  onClick={() => handleTimeSelection(time)}
                  className="block bg-meddoc-primary/20 text-gray-700 dark:text-white py-2 text-sm hover:bg-meddoc-primary hover:text-white transition-colors font-semibold"
                >
                  {start}
                </Button>
              );
            })}
          {!isScheduleTimeSlotEmpty &&
            areAllTimeSlotsEmpty(day.horaires, day.date) && (
              <>
                {Array.from({
                  length: AVAILABILITY_CONSTANTS.AVAILABILITY_ROW_COUNT,
                }).map((_, index) => (
                  <EmptyTimeSlot key={index} />
                ))}
              </>
            )}
        </div>
      </div>
    );
  }
);

DayColumn.displayName = "DayColumn";

export default DayColumn;
