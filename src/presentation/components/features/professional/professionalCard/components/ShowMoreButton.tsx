import { memo } from "react";

interface ShowMoreButtonProps {
  isVisible: boolean;
  onClick: () => void;
}

/**
 * Bouton pour afficher plus d'horaires disponibles
 */
const ShowMoreButton = memo<ShowMoreButtonProps>(({ isVisible, onClick }) => {
  if (!isVisible) return null;

  return (
    <button
      onClick={onClick}
      className="text-meddoc-primary font-bold hover:underline mt-3 mb-4 text-center"
    >
      Voir plus d'horaires disponibles
    </button>
  );
});

ShowMoreButton.displayName = "ShowMoreButton";

export default ShowMoreButton;
