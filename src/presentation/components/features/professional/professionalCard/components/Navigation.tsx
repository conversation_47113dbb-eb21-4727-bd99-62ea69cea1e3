import { memo } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Button from "@/presentation/components/common/Button/Button";

interface NavigationProps {
  onPrevious: () => void;
  onNext: () => void;
  isDisabled: boolean;
}

/**
 * Composant de navigation entre les périodes
 */
const Navigation = memo<NavigationProps>(
  ({ onPrevious, onNext, isDisabled }) => (
    <div className="flex justify-between items-center absolute -left-9 -right-9 top-3 z-10">
      <Button
        variant="primary"
        className={`bg-transparent text-meddoc-fonce hover:bg-meddoc-primary hover:text-white dark:text-primary ${isDisabled ? "text-gray-400" : ""} `}
        onClick={onPrevious}
        disabled={isDisabled}
      >
        <ChevronLeft size={24} />
      </Button>
      <Button
        variant="primary"
        className="bg-transparent text-meddoc-fonce hover:bg-meddoc-primary hover:text-white dark:text-primary"
        onClick={onNext}
      >
        <ChevronRight size={24} />
      </Button>
    </div>
  )
);

Navigation.displayName = "Navigation";

export default Navigation;
