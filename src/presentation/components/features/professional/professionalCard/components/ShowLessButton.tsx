import { memo } from "react";

interface ShowLessButtonProps {
  isVisible: boolean;
  onClick: () => void;
}

/**
 * Bouton pour afficher moin d'horaires disponibles
 */
const ShowLessButton = memo<ShowLessButtonProps>(({ isVisible, onClick }) => {
  if (isVisible) return null;

  return (
    <button
      onClick={onClick}
      className="text-meddoc-primary font-bold hover:underline mt-3 mb-4 text-center"
    >
      Voir moins d'horaires disponibles
    </button>
  );
});

ShowLessButton.displayName = "ShowLessButton";

export default ShowLessButton;
