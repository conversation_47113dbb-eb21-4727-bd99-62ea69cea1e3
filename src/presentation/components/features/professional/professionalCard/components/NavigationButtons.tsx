import React, { memo } from 'react';
import Button from "@/presentation/components/common/Button/Button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { AVAILABILITY_CLASSNAMES } from "@/shared/constants/availabilityConstants";

interface NavigationButtonsProps {
  onPrevious: () => void;
  onNext: () => void;
  isDisabled: boolean;
}

const NavigationButtons = memo(({ onPrevious, onNext, isDisabled }: NavigationButtonsProps) => (
  <div className={AVAILABILITY_CLASSNAMES.NAVIGATION}>
    <Button
      onClick={onPrevious}
      className={`${AVAILABILITY_CLASSNAMES.BUTTON.NAV} ${AVAILABILITY_CLASSNAMES.BUTTON.NAV_PREV}`}
      disabled={isDisabled}
      aria-label="Voir les créneaux précédents"
    >
      <ChevronLeft size={24} />
    </Button>
    <Button
      onClick={onNext}
      className={`${AVAILABILITY_CLASSNAMES.BUTTON.NAV} ${AVAILABILITY_CLASSNAMES.BUTTON.NAV_NEXT}`}
      aria-label="Voir les créneaux suivants"
    >
      <ChevronRight size={24} />
    </Button>
  </div>
));

NavigationButtons.displayName = 'NavigationButtons';

export default NavigationButtons;
