import { memo, useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Avatar } from "@mui/material";
import { MapPin } from "lucide-react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import { PROFESSIONAL_CARD_CLASSNAMES } from "@/shared/constants/professionalCardConstants";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation.ts";
import slugify from "slugify";
import { PhotoTypeEnum } from "@/domain/models/enums/photo_type_enum.ts";
import { Photo } from "@/domain/models/Photo.ts";

interface ProfessionalInfoProps {
  professional: ProfessionalCardDTO;
}

const ProfessionalInfo = memo<ProfessionalInfoProps>(({ professional }) => {
  const { HEADER, ADDRESS, BUTTON, CONTAINER } =
    PROFESSIONAL_CARD_CLASSNAMES.INFO_SECTION;
  const [professionalProfile, setProfessinalProfile] = useState<Photo | null>(
    null
  );

  useEffect(() => {
    if (
      professional &&
      professional.photos &&
      professional.photos.length > 0 &&
      professional.photos.find((photo) => photo.type === PhotoTypeEnum.PROFILE)
    ) {
      setProfessinalProfile(
        professional.photos?.find(
          (photo) => photo.type === PhotoTypeEnum.PROFILE
        )
      );
    }
  }, []);

  const generateURLToProfessionalProfile = (
    id: number,
    nom: string,
    prenom: string,
    titre: string,
    adresse: string,
    specialite: string
  ) => {
    const speciality = `/${specialite}`;
    const slug = slugify(`/${titre}-${nom}-${prenom}-${adresse}-${id}`, {
      lower: true,
    });

    const url = `${speciality}/${slug}`;
    return url;
  };

  return (
    <div className={CONTAINER}>
      <div className={HEADER.CONTAINER}>
        <div className={HEADER.AVATAR_CONTAINER}>
          {professionalProfile?.path ? (
            <img
              src={professionalProfile?.path}
              alt={`Photo de ${professional.nom} ${professional.prenom}`}
              className={HEADER.AVATAR}
            />
          ) : (
            <Avatar style={{ width: "100%", height: "100%" }}>
              {professional.nom.charAt(0)}
              {professional.prenom.charAt(0)}
            </Avatar>
          )}
        </div>
        <div className={HEADER.CONTENT}>
          <Link
            to={generateURLToProfessionalProfile(
              professional.id,
              professional.nom,
              professional.prenom,
              professional.titre,
              professional.adresse,
              professional.specialite[0].nom_specialite
            )}
          >
            <h2 className={HEADER.TITLE}>
              {professional.nom} {professional.prenom}
            </h2>
          </Link>
          <p className={HEADER.SPECIALITY}>
            {professional.specialite
              .map((speciality) => speciality.nom_specialite)
              .join(", ")}
          </p>
          <div className={ADDRESS.CONTAINER}>
            <MapPin className={ADDRESS.ICON} aria-hidden="true" />
            <div>
              <p className={ADDRESS.TEXT}>{professional.adresse}</p>
              {/* <p className={ADDRESS.TEXT}>
                {professional.commune}, {professional.district},{" "}
                {professional.region}
              </p> */}
            </div>
          </div>
          <Link
            to={generateURLToProfessionalProfile(
              professional.id,
              professional.nom,
              professional.prenom,
              professional.titre,
              professional.adresse,
              professional.specialite[0].nom_specialite
            )}
            className={BUTTON}
            aria-label={`Prendre rendez-vous avec ${professional.nom} ${professional.prenom}`}
          >
            Réserver un RDV
          </Link>
        </div>
      </div>
    </div>
  );
});

ProfessionalInfo.displayName = "ProfessionalInfo";

export default ProfessionalInfo;
