import { ComponentProps, memo } from "react";
import { PROFESSIONAL_CARD_CLASSNAMES } from "@/shared/constants/professionalCardConstants";
import { Skeleton } from "@/presentation/components/common/ui/skeleton";
import { twMerge } from "tailwind-merge";

const ProfessionalCardSkeleton = memo(
  ({ className, ...props }: ComponentProps<"div">) => {
    return (
      <div
        className={twMerge(PROFESSIONAL_CARD_CLASSNAMES.CONTAINER, className)}
        role="article"
        aria-label="Chargement de la carte du professionnel"
        {...props}
      >
        {/* Professional Info Skeleton */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            {/* Avatar Skeleton */}
            <Skeleton className="w-16 h-16 rounded-full" />

            {/* Name and Info Skeleton */}
            <div className="flex-1">
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>

          {/* Address Skeleton */}
          <div className="mt-8">
            <Skeleton className="h-3 w-full mb-2" />
            <Skeleton className="h-3 w-4/5" />
          </div>

          {/* Button Skeleton */}
          <div className="mt-16">
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        {/* Availability Section Skeleton */}
        <div className={PROFESSIONAL_CARD_CLASSNAMES.AVAILABILITY_SECTION}>
          <div className="h-[270px] p-4">
            <div className="w-full h-full">
              <div className="grid grid-cols-5 gap-4">
                {[...Array(5)].map((_, index) => (
                  <div key={index} className="space-y-2">
                    {/* Day Header */}
                    <Skeleton className="h-8 w-full mb-4" />
                  </div>
                ))}
              </div>
              <Skeleton className="h-[80%] w-full mb-2" />
            </div>
          </div>
        </div>
      </div>
    );
  },
);

ProfessionalCardSkeleton.displayName = "ProfessionalCardSkeleton";

export default ProfessionalCardSkeleton;
