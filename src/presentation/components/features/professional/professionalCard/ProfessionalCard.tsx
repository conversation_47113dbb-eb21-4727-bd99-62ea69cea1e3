import { memo, useRef } from "react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import ProfessionalInfo from "./components/ProfessionalInfo";
import { PROFESSIONAL_CARD_CLASSNAMES } from "@/shared/constants/professionalCardConstants";
import Availability from "./Availability";

interface ProfessionalCardProps {
  professionalInformations: ProfessionalCardDTO;
  setHoveredId: (id: number | null) => void;
}

const ProfessionalCard = memo<ProfessionalCardProps>(
  ({ professionalInformations, setHoveredId }) => {
    const horairesRef = useRef<HTMLDivElement>(null);
    return (
      <div
        ref={horairesRef}
        id={`professional-card-${professionalInformations.id}`}
        className={PROFESSIONAL_CARD_CLASSNAMES.CONTAINER}
        onMouseEnter={() => setHoveredId(professionalInformations.id)}
        onMouseLeave={() => setHoveredId(null)}
        role="article"
        aria-label={`Carte du professionnel ${professionalInformations.nom} ${professionalInformations.prenom}`}
      >
        <ProfessionalInfo professional={professionalInformations} />
        <div className={PROFESSIONAL_CARD_CLASSNAMES.AVAILABILITY_SECTION}>
          <Availability
            professionalInformation={professionalInformations}
            horairesRef={horairesRef}
            className="min-h-[270px]"
          />
        </div>
      </div>
    );
  }
);

ProfessionalCard.displayName = "ProfessionalCard";

export default ProfessionalCard;
