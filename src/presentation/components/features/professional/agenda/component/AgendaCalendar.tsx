import { format, getDay, parse, startOfWeek } from "date-fns";
import { fr } from "date-fns/locale";
import { dateFnsLocalizer } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import "@/styles/calendar.css";
import { Calendar } from "react-big-calendar";
import CustomToolbar from "@/presentation/components/features/professional/agenda/component/CustomToolbar";
import { calendarFormats } from "@/shared/utils/calendarFormats";
import { useAgendaState } from "@/presentation/hooks/agenda";
import CustomHeader from "./CustomHeader";

const locales = {
  fr: fr,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

export const AgendaCalendar = () => {
  const {
    events,
    currentView,
    handleViewChange,
    handleSelectEvent,
    handleSelectSlot,
  } = useAgendaState();
  return (
    <Calendar
      localizer={localizer}
      events={events}
      startAccessor="start"
      endAccessor="end"
      style={{
        minHeight: "90vh",
        minWidth: "650px",
      }}
      defaultView="month"
      view={currentView}
      onView={(view) => handleViewChange(view)}
      selectable
      onSelectEvent={handleSelectEvent}
      onSelectSlot={handleSelectSlot}
      eventPropGetter={(event) => ({
        style: {
          backgroundColor: event.backgroundColor,
          color: event?.color,
        },
      })}
      components={{
        toolbar: (toolbarProps) => <CustomToolbar {...toolbarProps} />,
        header: (props) => <CustomHeader {...props} />,
      }}
      messages={{
        showMore: (total: number) => `+${total} autres`,
      }}
      formats={calendarFormats}
      step={60}
      timeslots={1}
      className="custom-calendar"
    />
  );
};
