import {
  Typo<PERSON>,
  Box,
  Paper,
  Autocomplete,
  TextField,
  InputAdornment,
} from "@mui/material";
import { useEffect, useState } from "react";
import { Search } from "lucide-react";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useParams } from "react-router-dom";

interface ListePatientProps {
  onPatientSelect: (patientId: number | null) => void;
}

const ListePatient = ({ onPatientSelect }: ListePatientProps) => {
  const { id: patientId } = useParams();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const { dataProfessionalPatients, getProfessionnelPatient } =
    useProfessionnelPatient();

  interface PatientOption {
    id: number;
    nom: string;
    prenom: string;
    avatar: string;
  }

  const PATIENTS: PatientOption[] =
    dataProfessionalPatients?.map((patient) => ({
      id: patient.patient.id,
      nom: patient.patient.nom,
      prenom: patient.patient.prenom,
      avatar: patient.patient.avatar || "",
    })) || [];

  const currentPatient = PATIENTS?.find((p) => p.id === Number(patientId));
  const [selectedPatient, setSelectedPatient] = useState<PatientOption | null>(
    currentPatient ? currentPatient : null
  );

  useEffect(() => {
    if (professionalId && dataProfessionalPatients?.length === 0) {
      getProfessionnelPatient(professionalId);
    }
  }, [professionalId]);

  const handlePatientChange = (newValue: PatientOption | null) => {
    setSelectedPatient(newValue);
    onPatientSelect(newValue?.id || null);
  };

  return (
    <div className="my-4">
      <Autocomplete
        disabled={currentPatient ? true : false}
        value={selectedPatient}
        onChange={(_, newValue) => handlePatientChange(newValue)}
        options={PATIENTS}
        getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
        renderOption={(props, option) => (
          <Box
            component="li"
            sx={{ display: "flex", alignItems: "center", gap: 2 }}
            {...props}
            key={option.id}
          >
            {option.avatar ? (
              <img
                src={option.avatar}
                alt={`${option.nom} ${option.prenom}`}
                style={{ width: 32, height: 32, borderRadius: "50%" }}
              />
            ) : (
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: "50%",
                  bgcolor: "grey.300",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {option.nom[0]}
              </Box>
            )}
            <Typography>
              {option.nom} {option.prenom}
            </Typography>
          </Box>
        )}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder="Chercher par le nom du patient..."
            InputProps={{
              ...params.InputProps,
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        )}
      />
    </div>
  );
};

export default ListePatient;
