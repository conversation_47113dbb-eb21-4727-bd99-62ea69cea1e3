import { Stocks } from "@/domain/models/Stocks.ts";
import { motion, Variants } from "framer-motion";
import { Plus, Search, Package, Eye, Edit, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import ProduitCard from "../cards/ProduitCard.tsx";
import ProduitEmptyText from "../emptyData/ProduitEmptyText.tsx";
import StockFormModal from "../modals/StockFormModal.tsx";
import { Categories } from "@/domain/models/Categories.ts";
import { StockFormData } from "@/shared/schemas/SfockFormShema.ts";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";
import { ProduitWithQuantite } from "@/presentation/hooks/professionalStock/handlers/use-produits.ts";

/**
 * Composant de section Produits avec gestion CRUD
 */
interface ProduitsSectionProps {
  stocks: ProduitWithQuantite[];
  loading: boolean;
  containerVariants: Variants;
  itemVariants: Variants;
  cardHoverVariants: Variants;
  categories: Categories[];
  onAddProduit: (
    data: Omit<Stocks, "id" | "utilisateur_id">
  ) => Promise<boolean>;
  onUpdateProduit: (id: number, data: Omit<Stocks, "id">) => Promise<boolean>;
  onDeleteProduit: (id: number) => Promise<boolean>;
}

const ProduitsSection: React.FC<ProduitsSectionProps> = ({
  stocks,
  loading,
  containerVariants,
  itemVariants,
  cardHoverVariants,
  categories,
  onAddProduit,
  onUpdateProduit,
  onDeleteProduit,
}) => {
  const [searchTerm, setSearchTerm] = useState("");

  const [filteredProduit, setFilteredProduit] = useState(stocks);
  const [selectedProduit, setSelectedProduit] = useState<Stocks | null>(null);

  const [isAddProduitModalOpen, setIsAddProduitModalOpen] = useState(false);
  const [isEditProduitModalOpen, setIsEditProduitModalOpen] = useState(false);
  const [isDeleteProduitModalOpen, setIsDeleteProduitModalOpen] =
    useState(false);

  useEffect(() => {
    if (loading || !stocks) return;
    const filtered = stocks.filter(
      (produit) =>
        produit.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        produit.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    setFilteredProduit(filtered);
  }, [stocks, searchTerm, loading]);

  const handleStartEditProduit = (id: number) => {
    setIsEditProduitModalOpen(true);
    setSelectedProduit(stocks.find((p) => p.id === id));
  };

  const handleStartDeleteProduit = (id: number) => {
    setIsDeleteProduitModalOpen(true);
    setSelectedProduit(stocks.find((p) => p.id === id));
  };

  const handleDeleteProduit = async () => {
    if (!selectedProduit) return;
    const ok = await onDeleteProduit(selectedProduit.id);

    if (ok) {
      setIsDeleteProduitModalOpen(false);
      setSelectedProduit(null);
    }
    return ok;
  };

  const handleUpdateProduit = async (
    id: number,
    produit: Omit<Stocks, "id">
  ) => {
    const ok = await onUpdateProduit(id, produit);

    if (ok) {
      setIsEditProduitModalOpen(false);
      setSelectedProduit(null);
    }

    return ok;
  };

  const handleCreateProduit = async (
    produit: Omit<Stocks, "id" | "utilisateur_id">
  ) => {
    const ok = await onAddProduit({
      nom: produit.nom,
      description: produit.description,
      categorie_id: produit.categorie_id,
      unite: produit.unite,
      seuil_alerte: produit.seuil_alerte,
    });
    if (ok) {
      setIsAddProduitModalOpen(false);
      setSearchTerm("");
    }

    return ok;
  };

  const handleCancelDeleteProduit = () => {
    setSelectedProduit(null);
    setIsDeleteProduitModalOpen(false);
  };

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        className="space-y-6"
      >
        {/* Header avec recherche et bouton d'ajout */}
        <motion.div
          variants={itemVariants}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Gestion des Produits
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Gérez votre catalogue de produits et leurs stocks
              </p>
            </div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setIsAddProduitModalOpen(true)}
              className="flex items-center gap-2 bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-200"
            >
              <Plus size={18} />
              <span className="font-medium">Nouveau Produit</span>
            </motion.button>
          </div>

          {/* Barre de recherche */}
          <div className="mt-4 relative">
            <Search
              size={18}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"
            />
            <input
              type="text"
              placeholder="Rechercher un produit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-meddoc-primary focus:border-transparent"
            />
          </div>
        </motion.div>

        {/* Liste des produits */}
        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-6">
          {filteredProduit.map((produit, index) => (
            <ProduitCard
              key={produit.id}
              produit={produit}
              index={index}
              itemVariants={itemVariants}
              cardHoverVariants={cardHoverVariants}
              onEditProduit={() => handleStartEditProduit(produit.id)}
              onDeleteProduit={() => handleStartDeleteProduit(produit.id)}
            />
          ))}
        </div>

        {/* Message si aucun produit trouvé */}
        {filteredProduit.length === 0 && (
          <ProduitEmptyText
            onAddProduit={() => setIsAddProduitModalOpen(true)}
            searchTerm={searchTerm}
            itemVariants={itemVariants}
          />
        )}
      </motion.div>

      {/* Modals */}
      {isAddProduitModalOpen && (
        <StockFormModal
          title="Ajouter un produit"
          description=""
          isOpen={isAddProduitModalOpen}
          onClose={() => setIsAddProduitModalOpen(false)}
          onSubmit={handleCreateProduit}
          loading={loading}
          editionData={undefined}
          categories={categories}
        />
      )}

      {/* Modal de modification de produit */}
      {isEditProduitModalOpen && selectedProduit && (
        <StockFormModal
          title="Modifier le produit"
          description="Modifier les informations du produit"
          isOpen={isEditProduitModalOpen}
          onClose={() => setIsEditProduitModalOpen(false)}
          onSubmit={(data) => handleUpdateProduit(selectedProduit.id, data)}
          loading={loading}
          editionData={selectedProduit}
          categories={categories}
        />
      )}

      {/* Modal de suppression de produit */}
      {isDeleteProduitModalOpen && (
        <ConfirmationModal
          title="Supprimer le produit"
          message="Êtes-vous sûr de vouloir supprimer ce produit ? Cette action est irréversible."
          confirmButtonText={loading ? "Suppression..." : "Supprimer"}
          cancelButtonText="Annuler"
          loading={loading}
          isOpen={isDeleteProduitModalOpen}
          onClose={handleCancelDeleteProduit}
          onConfirm={handleDeleteProduit}
          confirmButtonColor="danger"
        />
      )}
    </>
  );
};

export default ProduitsSection;
