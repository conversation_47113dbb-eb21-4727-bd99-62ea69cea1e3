import { motion, Variants } from "framer-motion";
import { AlertTriangle, Calendar, Package } from "lucide-react";
import { useCallback } from "react";
import StockNearPeremptionDateCard from "../cards/StockNearPeremptionDateCard.tsx";
import LowStockCard from "../cards/LowStockCard.tsx";
import AlertStats from "../cards/AlertStats.tsx";
import {
  LotWithStockData,
  LowStockCardDTO,
  StockNearPeremptionDateCardDTO,
} from "@/domain/DTOS/StockDTO.ts";
import ExpiredStockCard from "../cards/ExpiredStockCard.tsx";

/**
 * Composant de section Alertes
 */
interface AlertesSectionProps {
  stockAlerts: StockNearPeremptionDateCardDTO[];
  lowStockAlerts: LowStockCardDTO[];
  expiredLots: LotWithStockData[];
  containerVariants: Variants;
  itemVariants: Variants;
  cardHoverVariants: Variants;
  isLoading: boolean;
}

const AlertesSection: React.FC<AlertesSectionProps> = ({
  stockAlerts,
  lowStockAlerts,
  expiredLots,
  containerVariants,
  itemVariants,
  cardHoverVariants,
  isLoading,
}) => {
  const getPrioriteColor = (priorite: string) => {
    switch (priorite) {
      case "haute":
        return "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800";
      case "moyenne":
        return "bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-300 border-orange-200 dark:border-orange-800";
      case "basse":
        return "bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800";
      default:
        return "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-600";
    }
  };

  const getPrioriteIcon = useCallback((priorite: string) => {
    switch (priorite) {
      case "haute":
        return (
          <AlertTriangle size={16} className="text-red-500 dark:text-red-400" />
        );
      case "moyenne":
        return (
          <AlertTriangle
            size={16}
            className="text-orange-500 dark:text-orange-400"
          />
        );
      case "basse":
        return (
          <AlertTriangle
            size={16}
            className="text-yellow-500 dark:text-yellow-400"
          />
        );
      default:
        return (
          <AlertTriangle
            size={16}
            className="text-gray-500 dark:text-gray-400"
          />
        );
    }
  }, []);

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="hidden"
      className="space-y-6"
    >
      {/* Header */}
      <motion.div
        variants={itemVariants}
        className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Centre d'Alertes
          </h2>
          <p className="text-gray-600 dark:text-gray-300 text-sm">
            Surveillance des stocks et dates d'expiration
          </p>
        </div>
      </motion.div>

      {/* Statistiques des alertes */}
      <AlertStats
        expiredLots={expiredLots}
        alertesPeremption={stockAlerts}
        alertesStock={lowStockAlerts}
        itemVariants={itemVariants}
      />

      {/* Stocks deja perimee */}
      <motion.div
        variants={itemVariants}
        className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Stocks Déjà Périmés
        </h3>
        <div className="space-y-3">
          {isLoading ? (
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Chargement...
            </p>
          ) : expiredLots && expiredLots.length > 0 ? (
            expiredLots.map((stock, index) => (
              <ExpiredStockCard
                key={stock.id}
                alerte={stock}
                index={index}
                getPrioriteColor={getPrioriteColor}
                getPrioriteIcon={getPrioriteIcon}
                itemVariants={itemVariants}
              />
            ))
          ) : (
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Rien à signaler pour le moment
            </p>
          )}
        </div>
      </motion.div>

      {/* Alertes de péremption */}
      <motion.div
        variants={itemVariants}
        className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Alertes de Péremption
        </h3>
        <div className="space-y-3">
          {stockAlerts && stockAlerts.length > 0 ? (
            stockAlerts.map((alerte, index) => (
              <StockNearPeremptionDateCard
                key={alerte.id}
                alerte={alerte}
                index={index}
                getPrioriteColor={getPrioriteColor}
                getPrioriteIcon={getPrioriteIcon}
                itemVariants={itemVariants}
              />
            ))
          ) : (
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Rien à signaler pour le moment
            </p>
          )}
        </div>
      </motion.div>
      {/* Alertes de stock faible */}
      <motion.div
        variants={itemVariants}
        className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Alertes de Stock Faible
        </h3>
        <div className="space-y-3">
          {lowStockAlerts && lowStockAlerts.length > 0 ? (
            lowStockAlerts.map((alerte, index) => (
              <LowStockCard
                key={alerte.id}
                alerte={alerte}
                index={index}
                getPrioriteColor={getPrioriteColor}
                getPrioriteIcon={getPrioriteIcon}
                itemVariants={itemVariants}
              />
            ))
          ) : (
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Rien à signaler pour le moment
            </p>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AlertesSection;
