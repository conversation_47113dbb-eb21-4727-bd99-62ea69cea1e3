import {
  CreateStockDTO,
  LotWithStockData,
  StockStatistics,
} from "@/domain/DTOS/StockDTO";
import { motion, Variants } from "framer-motion";
import {
  AlertTriangle,
  Archive,
  Download,
  FileText,
  Package,
  Plus,
  TrendingUp,
  Truck,
} from "lucide-react";
import { useCallback, useState } from "react";
import StockFormModal from "../modals/StockFormModal.tsx";
import EntreeStockFormModal from "../modals/EntreeStockFormModal.tsx";
import SortieStockFormModal from "../modals/SortieStockFormModal.tsx";
import { Stocks } from "@/domain/models/Stocks.ts";
import { SortieStockFormData } from "@/shared/schemas/SortieStockFormShema.ts";
import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import { Categories } from "@/domain/models/Categories.ts";
import StatisticsModal from "../modals/StatisticsModal.tsx";
import ShowInformationModal from "@/presentation/components/common/Modal/ShowInformationModal.tsx";

// Import des composants de tableaux
import ProduitTable from "../tables/ProduitTable.tsx";
import FournisseurTable from "../tables/FournisseurTable.tsx";
import AlertePeremptionTable from "../tables/AlertePeremptionTable.tsx";
import AlerteStockTable from "../tables/AlerteStockTable.tsx";
import ValeurTotaleTable from "../tables/ValeurTotaleTable.tsx";
import MouvementRecentTable from "../tables/MouvementRecentTable.tsx";
import { formatDate } from "@/presentation/pages/patient/profilePatients/formatDate.ts";

/**
 * Composant de section Dashboard avec statistiques et graphiques
 */
interface DashboardSectionProps {
  isLoading?: boolean;
  statistics: StockStatistics;
  onAddProduit: (
    data: Omit<Stocks, "id" | "utilisateur_id">
  ) => Promise<boolean>;
  onAddEntree: (data: CreateStockDTO) => Promise<boolean>;
  onAddSortie: (data: SortieStockFormData) => Promise<boolean>;

  stockList: Stocks[];
  fournisseurList: Fournisseurs[];
  categories: Categories[];

  containerVariants: Variants;
  itemVariants: Variants;
  cardHoverVariants: Variants;

  expiredLots?: LotWithStockData[];
}

const DashboardSection: React.FC<DashboardSectionProps> = ({
  isLoading = false,
  statistics,
  onAddProduit,
  onAddEntree,
  onAddSortie,
  stockList,
  fournisseurList,
  categories,
  containerVariants,
  itemVariants,
  cardHoverVariants,

  expiredLots = [],
}) => {
  const [isAddStockModalOpen, setIsAddStockModalOpen] = useState(false);
  const [isAddEntreeModalOpen, setIsAddEntreeModalOpen] = useState(false);
  const [isAddSortieModalOpen, setIsAddSortieModalOpen] = useState(false);

  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false);
  const [selectedCardInfo, setSelectedCardInfo] = useState<{
    title: string;
    content: React.ReactNode;
  } | null>(null);

  const handleCreateProduit = useCallback(
    async (data: Omit<Stocks, "id" | "utilisateur_id">) => {
      try {
        const ok = await onAddProduit(data);
        if (ok) {
          setIsAddStockModalOpen(false);
        }
        return ok;
      } catch (error) {
        console.log("error", error);
      }
    },
    [onAddProduit]
  );

  const handleAddEntree = useCallback(
    async (data: CreateStockDTO) => {
      try {
        const ok = await onAddEntree(data);
        if (ok) {
          setIsAddEntreeModalOpen(false);
        }
        return ok;
      } catch (error) {
        console.log("error", error);
      }
    },
    [onAddEntree]
  );

  const handleAddSortie = useCallback(
    async (data: SortieStockFormData) => {
      try {
        const ok = await onAddSortie(data);
        if (ok) {
          setIsAddSortieModalOpen(false);
        }
        return ok;
      } catch (error) {
        console.log("error", error);
      }
    },
    [onAddSortie]
  );

  /**
   * Génère le contenu détaillé pour chaque carte du dashboard
   * @param cardTitle - Titre de la carte sélectionnée
   * @returns Contenu JSX à afficher dans la modal
   */
  const getCardDetailContent = useCallback(
    (cardTitle: string): React.ReactNode => {
      switch (cardTitle) {
        case "Total Produits":
          return (
            <ProduitTable
              produits={stockList}
              height="h-[400px]"
              width="w-full"
            />
          );

        case "Fournisseurs":
          return (
            <FournisseurTable
              fournisseurs={fournisseurList}
              height="h-[400px]"
              width="w-full"
            />
          );

        case "Alertes Péremption": {
          const alertesPeremption = statistics?.alertesPeremption || 0;
          return (
            <AlertePeremptionTable
              data={expiredLots}
              height="h-[400px]"
              width="w-full"
            />
          );
        }

        case "Alertes Stock": {
          const alertesStock = statistics?.alertesStock || [];
          return (
            <AlerteStockTable
              alertes={alertesStock}
              height="h-[400px]"
              width="w-full"
            />
          );
        }

        case "Valeur Totale": {
          const valeur = statistics?.valeurTotaleStock || 0;
          return (
            <ValeurTotaleTable produits={stockList} valeurTotale={valeur} />
          );
        }

        case "Mouvements Récents": {
          const mouvements = statistics?.mouvementsRecents || [];
          return (
            <MouvementRecentTable
              mouvements={mouvements}
              height="h-[400px]"
              width="w-full"
            />
          );
        }

        default:
          return (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">
                Informations détaillées non disponibles pour cette statistique.
              </p>
            </div>
          );
      }
    },
    [statistics, stockList, fournisseurList, expiredLots]
  );

  /**
   * Gestionnaire de clic sur une carte de statistique
   */
  const handleCardClick = useCallback(
    (cardTitle: string) => {
      const content = getCardDetailContent(cardTitle);
      setSelectedCardInfo({
        title: `Détails - ${cardTitle}`,
        content,
      });
      setIsInfoModalOpen(true);
    },
    [getCardDetailContent]
  );

  /**
   * Ferme la modal d'information
   */
  const handleCloseInfoModal = useCallback(() => {
    setIsInfoModalOpen(false);
    setSelectedCardInfo(null);
  }, []);

  const statsCards = [
    {
      title: "Total Produits",
      value: statistics?.totalProduits || 0,
      icon: Package,
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      textColor: "text-blue-600",
      description: "Nombre total de produits en stock",
    },
    {
      title: "Fournisseurs",
      value: statistics?.totalFournisseurs || 0,
      icon: Truck,
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      textColor: "text-green-600",
      description: "Nombre total de fournisseurs",
    },
    {
      title: "Alertes Péremption",
      value: statistics?.alertesPeremption || 0,
      icon: AlertTriangle,
      color: "from-red-500 to-red-600",
      bgColor: "bg-red-50",
      textColor: "text-red-600",
      description: "Nombre total d'alertes de péremption",
    },
    {
      title: "Alertes Stock",
      value: statistics?.alertesStock.length || 0,
      icon: TrendingUp,
      color: "from-orange-500 to-orange-600",
      bgColor: "bg-orange-50",
      textColor: "text-orange-600",
      description: "Nombre total d'alertes de stock faible",
    },
    {
      title: "Valeur Totale",
      // Formatage de la valeur totale en MGA (separation des milliers par un espace)
      value: `${statistics?.valeurTotaleStock?.toLocaleString() || 0} MGA`,
      icon: FileText,
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      textColor: "text-purple-600",
      description: "Valeur totale des produits en stock",
    },
    {
      title: "Mouvements Récents",
      value: statistics?.mouvementsRecents?.length || 0,
      icon: Archive,
      color: "from-indigo-500 to-indigo-600",
      bgColor: "bg-indigo-50",
      textColor: "text-indigo-600",
      description: "Nombre total d'entree et sorties du jour",
    },
  ];

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        className="space-y-6"
      >
        {/* Cartes de statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {statsCards &&
            statsCards.map((card, index) => {
              const Icon = card.icon;
              return (
                <motion.div
                  key={card.title}
                  variants={itemVariants}
                  whileHover="hover"
                  whileTap="tap"
                  custom={index}
                  className={`${card.bgColor} dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 cursor-pointer`}
                  onClick={() => handleCardClick(card.title)}
                  {...cardHoverVariants}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">
                        {card.title}
                      </p>
                      <p className={`text-2xl font-bold ${card.textColor}`}>
                        {card.value}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {card.description}
                      </p>
                    </div>
                    <div
                      className={`p-3 rounded-lg bg-gradient-to-r ${card.color}`}
                    >
                      <Icon size={24} className="text-white" />
                    </div>
                  </div>
                </motion.div>
              );
            })}
        </div>

        {/* Actions rapides */}
        <motion.div
          variants={itemVariants}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Actions Rapides
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center gap-3 p-4 bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white rounded-lg hover:shadow-lg transition-all duration-200"
              onClick={() => setIsAddStockModalOpen(true)}
            >
              <Plus size={20} />
              <span className="font-medium">Nouveau Produit</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center gap-3 p-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              onClick={() => setIsAddEntreeModalOpen(true)}
            >
              <TrendingUp size={20} />
              <span className="font-medium">Entrée Stock</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center gap-3 p-4 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
              onClick={() => setIsAddSortieModalOpen(true)}
            >
              <Archive size={20} />
              <span className="font-medium">Sortie Stock</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Graphiques et tableaux récents */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <motion.div
            variants={itemVariants}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Mouvements Récents
            </h3>
            <div className="space-y-3">
              {statistics?.mouvementsRecents &&
              statistics.mouvementsRecents.length > 0 ? (
                statistics.mouvementsRecents.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-meddoc-primary rounded-full"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {item.name || "Aucun nom trouvé"}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {item.type} - {item.quantite} unités
                        </p>
                      </div>
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(new Date(item.date))}
                    </span>
                  </div>
                ))
              ) : (
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Aucun mouvement récent pour aujourd'hui
                </p>
              )}
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Alertes Stock
            </h3>
            <div className="space-y-3">
              {statistics &&
              statistics.alertesStock &&
              statistics.alertesStock.length > 0 ? (
                statistics.alertesStock.map((alerte) => (
                  <div
                    key={alerte.id}
                    className="flex items-center gap-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
                  >
                    <AlertTriangle
                      size={16}
                      className="text-red-500 dark:text-red-400"
                    />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Stock faible - {alerte.produit}
                      </p>
                      <p className="text-xs text-red-600 dark:text-red-400">
                        Seuil critique atteint ({alerte.stock_actuel} unités
                        restantes)
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Rien à signaler pour le moment
                </p>
              )}
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/** Modals d'action rapides */}
      {isAddStockModalOpen && (
        <StockFormModal
          isOpen={isAddStockModalOpen}
          onClose={() => setIsAddStockModalOpen(false)}
          onSubmit={handleCreateProduit}
          loading={false}
          editionData={undefined}
          categories={categories}
        />
      )}

      {isAddEntreeModalOpen && (
        <EntreeStockFormModal
          isOpen={isAddEntreeModalOpen}
          onClose={() => setIsAddEntreeModalOpen(false)}
          onSubmit={handleAddEntree}
          loading={false}
          stocks={stockList}
          fournisseurs={fournisseurList}
        />
      )}

      {isAddSortieModalOpen && (
        <SortieStockFormModal
          isOpen={isAddSortieModalOpen}
          onClose={() => setIsAddSortieModalOpen(false)}
          onSubmit={handleAddSortie}
          loading={false}
          stocks={stockList}
        />
      )}

      {/* Modal d'information détaillée */}
      {selectedCardInfo && (
        <ShowInformationModal
          isOpen={isInfoModalOpen}
          onClose={handleCloseInfoModal}
          title={selectedCardInfo.title}
          message=""
        >
          {selectedCardInfo.content}
        </ShowInformationModal>
      )}
    </>
  );
};

export default DashboardSection;
