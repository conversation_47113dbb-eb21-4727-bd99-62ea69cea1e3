import { motion, Variants } from "framer-motion";
import { TrendingUp } from "lucide-react";
import { CreateStockDTO, EntreeStockRowDTO } from "@/domain/DTOS/StockDTO.ts";
import { useState } from "react";
import EntreeStockFormModal from "../modals/EntreeStockFormModal.tsx";
import { Stocks } from "@/domain/models/Stocks.ts";
import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid.tsx";

/**
 * Composant de section Entrées de stock
 */
interface EntreesSectionProps {
  entreeList: EntreeStockRowDTO[];
  stockList: Stocks[];
  fournisseurList: Fournisseurs[];
  onAddEntree: (data: CreateStockDTO) => Promise<boolean>;
  containerVariants: Variants;
  itemVariants: Variants;
  cardHoverVariants: Variants;
  isLoading: boolean;
}

const EntreesSection: React.FC<EntreesSectionProps> = ({
  entreeList,
  stockList,
  fournisseurList,
  onAddEntree,
  containerVariants,
  itemVariants,
  cardHoverVariants,
  isLoading,
}) => {
  const [isAddEntreeModalOpen, setIsAddEntreeModalOpen] = useState(false);

  const handleAddEntree = async (data: CreateStockDTO) => {
    const result = await onAddEntree(data);

    if (result) {
      setIsAddEntreeModalOpen(false);
    }

    return result;
  };

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        className="space-y-6"
      >
        {/* Header */}
        <motion.div
          variants={itemVariants}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Entrées de Stock
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Historique des réceptions de marchandises
              </p>
            </div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setIsAddEntreeModalOpen(true)}
              className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-200"
            >
              <TrendingUp size={18} />
              <span className="font-medium">Nouvelle Entrée</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Tableau des entrées */}
        <motion.div
          variants={itemVariants}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
        >
          <div className="overflow-x-auto">
            <ListDataGrid data={entreeList} type="entree_stock" />
          </div>
        </motion.div>
      </motion.div>

      {/* Modal */}
      <EntreeStockFormModal
        isOpen={isAddEntreeModalOpen}
        onClose={() => setIsAddEntreeModalOpen(false)}
        onSubmit={handleAddEntree}
        loading={isLoading}
        stocks={stockList}
        fournisseurs={fournisseurList}
      />
    </>
  );
};

export default EntreesSection;
