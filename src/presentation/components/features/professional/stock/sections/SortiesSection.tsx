import { motion, Variants } from "framer-motion";
import { Archive } from "lucide-react";
import { Stocks } from "@/domain/models/Stocks.ts";
import { SortieStockRowDTO } from "@/domain/DTOS/StockDTO.ts";
import SortieStockFormModal from "../modals/SortieStockFormModal.tsx";
import { useState } from "react";
import { SortieStockFormData } from "@/shared/schemas/SortieStockFormShema.ts";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid.tsx";

/**
 * Composant de section Sorties de stock
 */
interface SortiesSectionProps {
  onAddSortie: (data: SortieStockFormData) => Promise<boolean>;
  sortieList: SortieStockRowDTO[];
  containerVariants: Variants;
  itemVariants: Variants;
  cardHoverVariants: Variants;
  stockList: Stocks[];
  isLoading: boolean;
}

const SortiesSection: React.FC<SortiesSectionProps> = ({
  onAddSortie,
  sortieList,
  containerVariants,
  itemVariants,
  cardHoverVariants,
  stockList,
  isLoading,
}) => {
  const [isAddSortieModalOpen, setIsAddSortieModalOpen] = useState(false);

  const handleStartAddSortie = () => {
    setIsAddSortieModalOpen(true);
  };

  const handleAddSortie = async (data: SortieStockFormData) => {
    try {
      const ok = await onAddSortie(data);
      if (ok) {
        setIsAddSortieModalOpen(false);
      }
      return ok;
    } catch (error) {
      console.log("error", error);
    }
  };

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        className="space-y-6"
      >
        {/* Header */}
        <motion.div
          variants={itemVariants}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Sorties de Stock
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Historique des sorties et consommations
              </p>
            </div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleStartAddSortie}
              className="flex items-center gap-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-200"
            >
              <Archive size={18} />
              <span className="font-medium">Nouvelle Sortie</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Tableau des sorties */}
        <motion.div
          variants={itemVariants}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
        >
          <div className="overflow-x-auto">
            <ListDataGrid data={sortieList} type="sortie_stock" />
          </div>
        </motion.div>
      </motion.div>

      {/* Modal */}
      {isAddSortieModalOpen && (
        <SortieStockFormModal
          isOpen={isAddSortieModalOpen}
          onClose={() => setIsAddSortieModalOpen(false)}
          onSubmit={handleAddSortie}
          loading={isLoading}
          stocks={stockList}
        />
      )}
    </>
  );
};

export default SortiesSection;
