import { Stocks } from "@/domain/models/Stocks.ts";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid.tsx";

/**
 * Props pour le composant ProduitTable
 */
interface ProduitTableProps {
  /** Liste des produits à afficher */
  produits: Stocks[];
  /** Hauteur personnalisée pour le tableau (optionnel) */
  height?: string;
  /** Largeur personnalisée pour le tableau (optionnel) */
  width?: string;
}

/**
 * Composant tableau pour afficher la liste des produits
 * Utilise ListDataGrid pour une interface uniforme
 * Respecte les principes SOLID avec une responsabilité unique
 */
const ProduitTable: React.FC<ProduitTableProps> = ({
  produits,
  height,
  width,
}) => {
  return (
    <div className="space-y-4 h-full w-full">
      <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        {produits.length} produit(s) en stock
      </div>
      {produits.length > 0 ? (
        <ListDataGrid
          data={produits}
          type="produit"
          height={height}
          width={width}
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
          <div className="text-center text-gray-500 dark:text-gray-400">
            Aucun produit en stock
          </div>
        </div>
      )}
    </div>
  );
};

export default ProduitTable;
