import { TrendingUp } from "lucide-react";
import { LowStockCardDTO } from "@/domain/DTOS/StockDTO";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid.tsx";

/**
 * Props pour le composant AlerteStockTable
 */
interface AlerteStockTableProps {
  /** Liste des alertes de stock */
  alertes: LowStockCardDTO[];
  /** Hauteur personnalisée pour le tableau (optionnel) */
  height?: string;
  /** Largeur personnalisée pour le tableau (optionnel) */
  width?: string;
}

/**
 * Composant tableau pour afficher les alertes de stock bas
 * Utilise ListDataGrid pour une interface uniforme
 * Respecte les principes SOLID avec une responsabilité unique
 */
const AlerteStockTable: React.FC<AlerteStockTableProps> = ({
  alertes,
  height,
  width,
}) => {
  return (
    <div className="space-y-4 w-full h-full">
      <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        {alertes.length} alerte(s) de stock faible détectée(s)
      </div>
      {alertes.length > 0 ? (
        <ListDataGrid
          data={alertes}
          type="alerte_stock"
          height={height}
          width={width}
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 mb-3">
              <TrendingUp
                size={24}
                className="text-green-600 dark:text-green-400"
              />
            </div>
            <p className="text-green-600 dark:text-green-400 font-medium">
              Aucune alerte de stock
            </p>
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              Tous vos stocks sont suffisants
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AlerteStockTable;
