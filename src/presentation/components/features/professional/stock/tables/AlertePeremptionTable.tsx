import { LotWithStockData } from "@/domain/DTOS/StockDTO";
import { AlertTriangle } from "lucide-react";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid.tsx";

/**
 * Props pour le composant AlertePeremptionTable
 */
interface AlertePeremptionTableProps {
  data: LotWithStockData[];
  /** Hauteur personnalisée pour le tableau (optionnel) */
  height?: string;
  /** Largeur personnalisée pour le tableau (optionnel) */
  width?: string;
}

/**
 * Composant tableau pour afficher les alertes de péremption
 * Utilise ListDataGrid pour une interface uniforme
 * Respecte les principes SOLID avec une responsabilité unique
 */
const AlertePeremptionTable: React.FC<AlertePeremptionTableProps> = ({
  data,
  height,
  width,
}) => {
  const alertesCount = data.length;
  return (
    <div className="space-y-4 w-full h-full">
      <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        {alertesCount} alerte(s) de péremption détectée(s)
      </div>

      {alertesCount === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 mb-3">
              <AlertTriangle
                size={24}
                className="text-green-600 dark:text-green-400"
              />
            </div>
            <p className="text-green-600 dark:text-green-400 font-medium">
              Aucune alerte de péremption
            </p>
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              Tous vos produits sont dans les délais
            </p>
          </div>
        </div>
      ) : (
        <ListDataGrid
          data={data}
          type="alerte_peremption"
          height={height}
          width={width}
        />
      )}
    </div>
  );
};

export default AlertePeremptionTable;
