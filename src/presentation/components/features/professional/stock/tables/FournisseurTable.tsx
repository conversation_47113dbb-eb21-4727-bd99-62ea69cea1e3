import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid.tsx";

/**
 * Props pour le composant FournisseurTable
 */
interface FournisseurTableProps {
  /** Liste des fournisseurs à afficher */
  fournisseurs: Fournisseurs[];
  /** Hauteur personnalisée pour le tableau (optionnel) */
  height?: string;
  /** Largeur personnalisée pour le tableau (optionnel) */
  width?: string;
}

/**
 * Composant tableau pour afficher la liste des fournisseurs
 * Utilise ListDataGrid pour une interface uniforme
 * Respecte les principes SOLID avec une responsabilité unique
 */
const FournisseurTable: React.FC<FournisseurTableProps> = ({
  fournisseurs,
  height,
  width,
}) => {
  return (
    <div className="space-y-4 w-full h-full">
      <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        {fournisseurs.length} fournisseur(s) partenaire(s)
      </div>
      {fournisseurs.length > 0 ? (
        <ListDataGrid
          data={fournisseurs}
          type="fournisseur"
          height={height}
          width={width}
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
          <div className="text-center text-gray-500 dark:text-gray-400">
            Aucun fournisseur enregistré
          </div>
        </div>
      )}
    </div>
  );
};

export default FournisseurTable;
