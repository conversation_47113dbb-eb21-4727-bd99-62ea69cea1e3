import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid.tsx";

/**
 * Interface pour un mouvement récent
 */
interface MouvementRecent {
  id?: number;
  name: string;
  type: "Entrée" | "Sortie";
  quantite: number;
  date: string;
}

/**
 * Props pour le composant MouvementRecentTable
 */
interface MouvementRecentTableProps {
  /** Liste des mouvements récents */
  mouvements: MouvementRecent[];
  /** Hauteur personnalisée pour le tableau (optionnel) */
  height?: string;
  /** Largeur personnalisée pour le tableau (optionnel) */
  width?: string;
}

/**
 * Composant tableau pour afficher les mouvements récents de stock
 * Utilise ListDataGrid pour une interface uniforme
 * Respecte les principes SOLID avec une responsabilité unique
 */
const MouvementRecentTable: React.FC<MouvementRecentTableProps> = ({
  mouvements,
  height,
  width,
}) => {
  return (
    <div className="space-y-4 w-full h-full">
      <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        {mouvements.length} mouvement(s) enregistré(s) aujourd'hui
      </div>
      {mouvements.length > 0 ? (
        <ListDataGrid
          data={mouvements}
          type="mouvement_recent"
          height={height}
          width={width}
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
          <div className="text-center text-gray-500 dark:text-gray-400">
            Aucun mouvement enregistré aujourd'hui
          </div>
        </div>
      )}
    </div>
  );
};

export default MouvementRecentTable;
