import { FileText } from "lucide-react";
import { Stocks } from "@/domain/models/Stocks.ts";

/**
 * Props pour le composant ValeurTotaleTable
 */
interface ValeurTotaleTableProps {
  /** Liste des produits pour calculer la valeur totale */
  produits: Stocks[];
  /** Valeur totale du stock */
  valeurTotale: number;
}

/**
 * Composant tableau pour afficher la valeur totale du stock
 * Respecte les principes SOLID avec une responsabilité unique
 */
const ValeurTotaleTable: React.FC<ValeurTotaleTableProps> = ({
  produits,
  valeurTotale,
}) => {
  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        Valeur totale estimée :{" "}
        {valeurTotale.toLocaleString("fr-FR", {
          style: "currency",
          currency: "MGA",
        })}
      </div>
    </div>
  );
};

export default ValeurTotaleTable;
