import { Categories } from "@/domain/models/Categories.ts";
import { Stocks } from "@/domain/models/Stocks.ts";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";
import FormField from "@/presentation/components/common/ui/FormField.tsx";
import MedicamentAutocompleteField from "@/presentation/components/common/ui/MedicamentAutocompleteField.tsx";
import { StockFormData, stockSchema } from "@/shared/schemas/SfockFormShema.ts";
import { zodResolver } from "@hookform/resolvers/zod";
import { Package, FileText, Archive, AlertTriangle } from "lucide-react";
import { useForm } from "react-hook-form";
import { useState, useCallback } from "react";

/**
 * Modal d'ajout/modification de produit
 */
interface StockFormModalProps {
  title?: string;
  description?: string;
  editionData?: Stocks;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Omit<Stocks, "id">) => Promise<boolean>;
  loading?: boolean;
  categories: Categories[];
}

const StockFormModal: React.FC<StockFormModalProps> = ({
  title = "Ajouter un produit",
  description = "",
  isOpen,
  onClose,
  editionData,
  onSubmit,
  loading = false,
  categories,
}) => {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
  } = useForm<StockFormData>({
    resolver: zodResolver(stockSchema),
    defaultValues: {
      nom: editionData?.nom || "",
      description: editionData?.description || "",
      categorie_id: String(editionData?.categorie_id || null),
      unite: editionData?.unite || "",
      seuil_alerte: editionData?.seuil_alerte || 10,
    },
  });

  // State pour surveiller si la catégorie sélectionnée est un médicament
  const [isMedicament, setIsMedicament] = useState(false);

  const handleFormSubmit = async (data: StockFormData) => {
    const result = await onSubmit({
      nom: data.nom,
      description: data.description,
      categorie_id: Number.parseInt(data.categorie_id),
      unite: data.unite,
      seuil_alerte: data.seuil_alerte,
      utilisateur_id: 1,
    });

    if (result) {
      reset();
    }

    return result;
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const uniteOptions = [
    { value: "boite", label: "Boîte" },
    { value: "flacon", label: "Flacon" },
    { value: "piece", label: "Pièce" },
    { value: "tube", label: "Tube" },
    { value: "ampoule", label: "Ampoule" },
    { value: "comprime", label: "Comprimé" },
  ];

  const categorieOptions = categories.map((cat) => ({
    value: cat.id.toString(),
    label: cat.nom,
  }));

  const checkIfMedicament = useCallback(
    (categoryId: string) => {
      const selectedCategory = categories.find(
        (cat) => cat.id.toString() === categoryId
      );
      setIsMedicament(
        selectedCategory?.nom.toLowerCase().includes("médicament") ||
          selectedCategory?.nom.toLowerCase().includes("medicament")
      );
    },
    [categories]
  );

  return (
    <>
      <ConfirmationModal
        isOpen={isOpen}
        onClose={handleClose}
        onConfirm={handleSubmit(handleFormSubmit)}
        title={title}
        message={description}
        confirmButtonText={loading ? "Enregistrement..." : "Enregistrer"}
        cancelButtonText="Annuler"
        loading={loading}
        disableBackdropClick={loading}
        confirmButtonColor="gradient"
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <FormField
            id="categorie_id"
            label="Catégorie"
            placeholder="Sélectionnez une catégorie"
            type="select"
            icon={Archive}
            required
            register={register}
            options={categorieOptions}
            onChange={(e) => {
              setValue("categorie_id", e.target.value);
              checkIfMedicament(e.target.value);
            }}
            error={errors.categorie_id}
            className="w-full"
          />

          {isMedicament ? (
            <MedicamentAutocompleteField
              id="nom"
              label="Nom du médicament"
              placeholder="Rechercher un médicament..."
              icon={Package}
              required
              error={errors.nom}
              className="w-full"
              register={register}
              setValue={setValue}
            />
          ) : (
            <FormField
              id="nom"
              label="Nom du produit"
              placeholder="Entrez le nom du produit"
              type="text"
              icon={Package}
              register={register}
              required
              error={errors.nom}
              className="w-full"
            />
          )}

          <FormField
            id="description"
            label="Description"
            placeholder="Description du produit (optionnel)"
            type="text"
            icon={FileText}
            register={register}
            error={errors.description}
            className="w-full"
          />

          <FormField
            id="unite"
            label="Unité"
            placeholder="Sélectionnez l'unité"
            type="select"
            icon={Package}
            register={register}
            required
            options={uniteOptions}
            error={errors.unite}
            className="w-full"
          />

          <FormField
            id="seuil_alerte"
            label="Seuil d'alerte"
            placeholder="Quantité minimale avant alerte"
            type="number"
            icon={AlertTriangle}
            register={register}
            required
            onChange={(e) =>
              setValue("seuil_alerte", Number.parseInt(e.target.value))
            }
            error={errors.seuil_alerte}
            className="w-full"
          />
        </div>
      </ConfirmationModal>
    </>
  );
};

export default StockFormModal;
