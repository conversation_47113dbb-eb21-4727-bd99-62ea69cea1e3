import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";
import FormField from "@/presentation/components/common/ui/FormField.tsx";
import {
  FournisseurFormData,
  fournisseurSchema,
} from "@/shared/schemas/FournisseurFormShema.ts";
import { zodResolver } from "@hookform/resolvers/zod";
import { Truck, Users } from "lucide-react";
import { useForm } from "react-hook-form";

/**
 * Modal d'ajout/modification de fournisseur
 */
interface FournisseurFormModalProps {
  title?: string;
  description?: string;
  editionData?: Fournisseurs;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: FournisseurFormData) => Promise<boolean>;
  loading?: boolean;
}

const FournisseurFormModal: React.FC<FournisseurFormModalProps> = ({
  title = "Ajouter un fournisseur",
  description = "Ajouter un nouveau fournisseur",
  isOpen,
  onClose,
  editionData,
  onSubmit,
  loading = false,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FournisseurFormData>({
    resolver: zodResolver(fournisseurSchema),
    defaultValues: {
      nom: editionData?.nom || "",
      adresse: editionData?.adresse || "",
      telephone: editionData?.telephone || "",
      courriel: editionData?.courriel || "",
    },
  });

  const handleFormSubmit = async (data: FournisseurFormData) => {
    const result = await onSubmit(data);
    if (result) {
      handleClose();
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <ConfirmationModal
      isOpen={isOpen}
      onClose={handleClose}
      onConfirm={handleSubmit(handleFormSubmit)}
      title={title}
      message={description}
      confirmButtonText={loading ? "Validation en cours..." : "Valider"}
      cancelButtonText="Annuler"
      loading={loading}
      disableBackdropClick={loading}
    >
      <div className="w-full max-w-md mx-auto space-y-4">
        <FormField
          id="nom"
          label="Nom du fournisseur"
          placeholder="Entrez le nom du fournisseur"
          type="text"
          icon={Truck}
          register={register}
          required
          error={errors.nom}
          className="w-full"
        />

        <FormField
          id="adresse"
          label="Adresse"
          placeholder="Adresse complète du fournisseur"
          type="text"
          icon={Users}
          register={register}
          error={errors.adresse}
          className="w-full"
        />

        <FormField
          id="telephone"
          label="Téléphone"
          placeholder="Numéro de téléphone"
          type="tel"
          icon={Users}
          register={register}
          error={errors.telephone}
          className="w-full"
        />

        <FormField
          id="courriel"
          label="Email"
          placeholder="<EMAIL>"
          type="email"
          icon={Users}
          register={register}
          error={errors.courriel}
          className="w-full"
        />
      </div>
    </ConfirmationModal>
  );
};

export default FournisseurFormModal;
