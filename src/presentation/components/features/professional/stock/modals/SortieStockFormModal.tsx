import { Lots } from "@/domain/models/Lots.ts";
import { Stocks } from "@/domain/models/Stocks.ts";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";
import AutocompleteSelect from "@/presentation/components/common/ui/AutocompleteField.tsx";
import <PERSON><PERSON>ield from "@/presentation/components/common/ui/FormField.tsx";
import {
  SortieStockFormData,
  sortieStockSchema,
} from "@/shared/schemas/SortieStockFormShema.ts";
import { zodResolver } from "@hookform/resolvers/zod";
import { Archive, TrendingUp, ShoppingCart, Users } from "lucide-react";
import { useForm } from "react-hook-form";

/**
 * Modal d'ajout de sortie de stock
 */
interface SortieStockFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: SortieStockFormData) => void;
  loading?: boolean;
  stocks: Stocks[];
}

const SortieStockFormModal: React.FC<SortieStockFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  loading = false,
  stocks,
}) => {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
  } = useForm<SortieStockFormData>({
    resolver: zodResolver(sortieStockSchema),
    defaultValues: {
      stock_id: null,
      quantite: 1,
      type_sortie: "vente",
      destinataire: "",
    },
  });

  const handleFormSubmit = (data: SortieStockFormData) => {
    onSubmit(data);
    reset();
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const stockOptions = stocks.map((stock) => ({
    id: stock.id,
    nom: stock.nom,
  }));

  const typeSortieOptions = [
    { value: "vente", label: "Vente" },
    { value: "utilisation", label: "Utilisation interne" },
    { value: "perte", label: "Perte/Destruction" },
  ];

  return (
    <ConfirmationModal
      isOpen={isOpen}
      onClose={handleClose}
      onConfirm={handleSubmit(handleFormSubmit)}
      title="Nouvelle sortie de stock"
      message=""
      confirmButtonText={loading ? "Enregistrement..." : "Enregistrer"}
      cancelButtonText="Annuler"
      loading={loading}
      disableBackdropClick={loading}
    >
      <div className="w-full max-w-md mx-auto space-y-4">
        <AutocompleteSelect
          id="stock_id"
          label="Produit"
          placeholder="Sélectionnez un produit"
          icon={Archive}
          setValue={setValue}
          required
          options={stockOptions}
          error={errors.stock_id}
          className="w-full"
        />

        <FormField
          id="quantite"
          label="Quantité"
          placeholder="Quantité à sortir"
          type="number"
          icon={TrendingUp}
          register={register}
          required
          error={errors.quantite}
          className="w-full"
        />

        <FormField
          id="type_sortie"
          label="Type de sortie"
          placeholder="Sélectionnez le type"
          type="select"
          icon={ShoppingCart}
          register={register}
          required
          options={typeSortieOptions}
          error={errors.type_sortie}
          className="w-full"
        />

        <FormField
          id="destinataire"
          label="Destinataire"
          placeholder="Nom du destinataire (optionnel)"
          type="text"
          icon={Users}
          register={register}
          error={errors.destinataire}
          className="w-full"
        />
      </div>
    </ConfirmationModal>
  );
};

export default SortieStockFormModal;
