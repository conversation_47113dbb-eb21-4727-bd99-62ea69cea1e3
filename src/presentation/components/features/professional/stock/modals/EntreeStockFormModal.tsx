import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import { Stocks } from "@/domain/models/Stocks.ts";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";
import AutocompleteSelect from "@/presentation/components/common/ui/AutocompleteField.tsx";
import DateField from "@/presentation/components/common/ui/DateField.tsx";
import FormField from "@/presentation/components/common/ui/FormField.tsx";
import {
  EntreeStockFormData,
  entreeStockSchema,
} from "@/shared/schemas/EntreeStockFormShema.ts";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Package,
  Truck,
  TrendingUp,
  FileText,
  Archive,
  Calendar,
} from "lucide-react";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

/**
 * Modal d'ajout d'entrée de stock
 */
interface EntreeStockFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: EntreeStockFormData) => Promise<boolean>;
  loading?: boolean;
  stocks: Stocks[];
  fournisseurs: Fournisseurs[];
}

const EntreeStockFormModal: React.FC<EntreeStockFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  loading = false,
  stocks,
  fournisseurs,
}) => {
  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors },
    reset,
  } = useForm<EntreeStockFormData>({
    resolver: zodResolver(entreeStockSchema),
    defaultValues: {
      stock_id: null,
      fournisseur_id: null,
      quantite: 1,
      prix_unitaire: 0,
      numero_lot: "",
      date_expiration: null,
    },
  });

  const handleFormSubmit = async (data: EntreeStockFormData) => {
    const result = await onSubmit(data);

    if (result) {
      reset();
    }

    return result;
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const stockOptions = stocks.map((stock) => ({
    id: stock.id,
    nom: stock.nom,
  }));

  const fournisseurOptions = fournisseurs.map((fournisseur) => ({
    id: fournisseur.id,
    nom: fournisseur.nom,
  }));

  return (
    <ConfirmationModal
      isOpen={isOpen}
      onClose={handleClose}
      onConfirm={handleSubmit(handleFormSubmit)}
      title="Nouvelle entrée de stock"
      message=""
      confirmButtonText={loading ? "Enregistrement..." : "Enregistrer"}
      cancelButtonText="Annuler"
      loading={loading}
      disableBackdropClick={loading}
    >
      <div className="w-full max-w-md mx-auto space-y-4">
        <AutocompleteSelect
          id="stock_id"
          label="Produit"
          placeholder="Sélectionnez un produit"
          icon={Package}
          setValue={setValue}
          required
          options={stockOptions}
          error={errors.stock_id}
          className="w-full"
        />

        <AutocompleteSelect
          id="fournisseur_id"
          label="Fournisseur"
          placeholder="Sélectionnez un fournisseur"
          icon={Truck}
          setValue={setValue}
          required
          options={fournisseurOptions}
          error={errors.fournisseur_id}
          className="w-full"
        />

        <FormField
          id="quantite"
          label="Quantité"
          placeholder="Quantité reçue"
          type="number"
          icon={TrendingUp}
          register={register}
          required
          error={errors.quantite}
          className="w-full"
        />

        <FormField
          id="prix_unitaire"
          label="Prix unitaire (MGA)"
          placeholder="Prix par unité"
          type="number"
          icon={FileText}
          register={register}
          required
          error={errors.prix_unitaire}
          className="w-full"
        />

        <FormField
          id="numero_lot"
          label="Numéro de lot"
          placeholder="Numéro de lot (optionnel)"
          type="text"
          icon={Archive}
          register={register}
          error={errors.numero_lot}
          className="w-full"
        />

        <DateField
          id="date_expiration"
          label="Date d'expiration"
          icon={Calendar}
          control={control}
          error={errors.date_expiration}
          description="Date d'expiration du lot (optionnelle)"
          className="w-full"
        />
      </div>
    </ConfirmationModal>
  );
};

export default EntreeStockFormModal;
