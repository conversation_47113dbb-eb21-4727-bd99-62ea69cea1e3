import React, { useState } from "react";
import { AnimatePresence } from "framer-motion";
import {
  Package,
  Truck,
  TrendingUp,
  AlertTriangle,
  BarChart3,
  Archive,
} from "lucide-react";

// Types et modèles
import { Stocks, Fournisseurs, Lots, Categories } from "@/domain/models";

import DashboardSection from "./sections/DashboardSection.tsx";
import FournisseursSection from "./sections/FournisseursSection.tsx";
import { StockFormData } from "@/shared/schemas/SfockFormShema.ts";
import ProduitsSection from "./sections/ProduitsSection.tsx";
import EntreesSection from "./sections/EntreesSection.tsx";
import SortiesSection from "./sections/SortiesSection.tsx";
import AlertesSection from "./sections/AlertesSection.tsx";
import AddStockModal from "./modals/StockFormModal.tsx";
import { EntreeStockFormData } from "@/shared/schemas/EntreeStockFormShema.ts";
import { SortieStockFormData } from "@/shared/schemas/SortieStockFormShema.ts";
import EntreeStockFormModal from "./modals/EntreeStockFormModal.tsx";
import SortieStockFormModal from "./modals/SortieStockFormModal.tsx";
import StockHeader from "./StockHeader.tsx";
import useProfessionalStock from "@/presentation/hooks/professionalStock/use-professional-stock.ts";
import FournisseurFormModal from "./modals/FournisseurFormModal.tsx";

/**
 * Types pour les sections actives
 */
type ActiveSection =
  | "dashboard"
  | "fournisseurs"
  | "produits"
  | "entrees"
  | "sorties"
  | "alertes";

/**
 * Composant principal de gestion de stock
 *
 * @description Interface complète pour la gestion de stock multi-produits avec lots et traçabilité.
 * Intègre toutes les fonctionnalités de gestion des fournisseurs, produits, entrées, sorties,
 * et alertes de péremption dans une interface moderne et responsive.
 *
 * @features
 * - Gestion des fournisseurs avec CRUD complet
 * - Gestion des produits/stocks avec catégorisation
 * - Enregistrement des entrées de stock avec lots
 * - Gestion des sorties (ventes, utilisations, pertes)
 * - Système d'alertes pour péremption et stock bas
 * - Dashboard avec statistiques et graphiques
 * - Interface responsive avec animations
 * - Validation en temps réel des formulaires
 * - Système de notifications
 *
 * @architecture
 * - Séparation de la logique métier dans des hooks personnalisés
 * - Utilisation de modals pour tous les formulaires
 * - Composants UI réutilisables
 * - Gestion d'état centralisée
 * - Validation avec Zod et react-hook-form
 *
 * @example
 * ```tsx
 * <Stock />
 * ```
 */
const Stock: React.FC = () => {
  // États pour la gestion des sections actives
  const [activeSection, setActiveSection] =
    useState<ActiveSection>("dashboard");

  const { handlers } = useProfessionalStock();

  // Configuration des animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12,
      },
    },
  };

  const cardHoverVariants = {
    hover: {
      y: -5,
      scale: 1.02,
      boxShadow: "0 20px 30px -10px rgba(0, 0, 0, 0.1)",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10,
      },
    },
    tap: {
      scale: 0.98,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10,
      },
    },
  };

  // Navigation des sections
  const navigationItems = [
    {
      id: "dashboard" as ActiveSection,
      label: "Tableau de Bord",
      icon: BarChart3,
      color: "from-blue-500 to-blue-600",
    },
    {
      id: "fournisseurs" as ActiveSection,
      label: "Fournisseurs",
      icon: Truck,
      color: "from-green-500 to-green-600",
    },
    {
      id: "produits" as ActiveSection,
      label: "Produits",
      icon: Package,
      color: "from-purple-500 to-purple-600",
    },
    {
      id: "entrees" as ActiveSection,
      label: "Entrées",
      icon: TrendingUp,
      color: "from-emerald-500 to-emerald-600",
    },
    {
      id: "sorties" as ActiveSection,
      label: "Sorties",
      icon: Archive,
      color: "from-orange-500 to-orange-600",
    },
    {
      id: "alertes" as ActiveSection,
      label: "Alertes",
      icon: AlertTriangle,
      color: "from-red-500 to-red-600",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header avec navigation */}
      <StockHeader
        navigationItems={navigationItems}
        activeSection={activeSection}
        setActiveSection={setActiveSection}
      />
      {/* Contenu principal */}
      <div className="max-w-7xl mx-auto p-6">
        <AnimatePresence mode="wait">
          {activeSection === "dashboard" && (
            <DashboardSection
              key="dashboard"
              isLoading={handlers.isLoadingStockData}
              statistics={handlers.dashboardData}
              onAddProduit={handlers.handleAddProduit}
              onAddEntree={handlers.handleAddEntree}
              onAddSortie={handlers.handleAddSortie}
              stockList={handlers.produitList}
              fournisseurList={handlers.fournisseurList}
              categories={handlers.categories}
              containerVariants={containerVariants}
              itemVariants={itemVariants}
              cardHoverVariants={cardHoverVariants}
              expiredLots={handlers.expiredLots}
            />
          )}
          {activeSection === "fournisseurs" && (
            <FournisseursSection
              key="fournisseurs"
              fournisseurs={handlers.fournisseurList}
              isLoading={handlers.isLoadingFournisseur}
              onAddFournisseur={handlers.handleAddFournisseur}
              onDeleteFournisseur={handlers.handleDeleteFournisseur}
              onUpdateFournisseur={handlers.handleUpdateFournisseur}
              containerVariants={containerVariants}
              itemVariants={itemVariants}
              cardHoverVariants={cardHoverVariants}
            />
          )}
          {activeSection === "produits" && (
            <ProduitsSection
              key="produits"
              stocks={handlers.produitList}
              categories={handlers.categories}
              onAddProduit={handlers.handleAddProduit}
              onUpdateProduit={handlers.handleUpdateProduit}
              onDeleteProduit={handlers.handleDeleteProduit}
              loading={handlers.isLoadingProduit}
              containerVariants={containerVariants}
              itemVariants={itemVariants}
              cardHoverVariants={cardHoverVariants}
            />
          )}
          {activeSection === "entrees" && (
            <EntreesSection
              key="entrees"
              entreeList={handlers.entreesStocks}
              stockList={handlers.produitList}
              isLoading={handlers.isLoadingEntree}
              fournisseurList={handlers.fournisseurList}
              onAddEntree={handlers.handleAddEntree}
              containerVariants={containerVariants}
              itemVariants={itemVariants}
              cardHoverVariants={cardHoverVariants}
            />
          )}
          {activeSection === "sorties" && (
            <SortiesSection
              key="sorties"
              sortieList={handlers.sortieList}
              stockList={handlers.produitList}
              isLoading={handlers.isLoadingSortie}
              onAddSortie={handlers.handleAddSortie}
              containerVariants={containerVariants}
              itemVariants={itemVariants}
              cardHoverVariants={cardHoverVariants}
            />
          )}
          {activeSection === "alertes" && (
            <AlertesSection
              key="alertes"
              isLoading={handlers.isLoadingAlertes}
              lowStockAlerts={handlers.lowStockAlerts}
              expiredLots={handlers.expiredLots}
              stockAlerts={handlers.stockAlerts}
              containerVariants={containerVariants}
              itemVariants={itemVariants}
              cardHoverVariants={cardHoverVariants}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Stock;
