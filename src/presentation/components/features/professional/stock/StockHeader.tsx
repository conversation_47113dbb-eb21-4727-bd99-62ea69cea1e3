import React, { Dispatch, SetStateAction } from "react";
import { motion } from "framer-motion";
import { MessageCircleQuestionIcon } from "lucide-react";

interface StockHeaderProps {
  navigationItems: {
    id: string;
    label: string;
    icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
    color: string;
  }[];
  activeSection: string;
  setActiveSection: Dispatch<SetStateAction<string>>;
}

const StockHeader = ({
  navigationItems,
  activeSection,
  setActiveSection,
}: StockHeaderProps) => {
  return (
    <>
      {/* Header avec navigation */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-meddoc-primary to-meddoc-fonce dark:from-gray-800 dark:to-gray-700 text-white p-6 shadow-lg"
      >
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">
                Gestion des stocks médicaux
              </h1>
              <p className="text-white/90 text-sm sm:text-base">
                Gestion complète des stocks, fournisseurs et mouvements
              </p>
            </div>
            <div className="flex items-center gap-3">
              <motion.button
                title="Aide"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white/20 hover:bg-white/30 p-2 rounded-lg transition-colors"
              >
                <MessageCircleQuestionIcon />
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Navigation des sections */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"
      >
        <div className="max-w-7xl">
          <div className="flex overflow-x-auto scrollbar-hidden">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeSection === item.id;

              return (
                <motion.button
                  key={item.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setActiveSection(item.id)}
                  className={`flex items-center gap-3 px-6 py-4 text-sm font-medium whitespace-nowrap transition-all duration-200 border-b-2 ${
                    isActive
                      ? "text-meddoc-primary border-meddoc-primary bg-meddoc-primary/5 dark:bg-meddoc-primary/10"
                      : "text-gray-600 dark:text-gray-300 border-transparent hover:text-meddoc-primary hover:border-meddoc-primary/30"
                  }`}
                >
                  <Icon width={18} />
                  <span className="hidden sm:inline">{item.label}</span>
                </motion.button>
              );
            })}
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default StockHeader;
