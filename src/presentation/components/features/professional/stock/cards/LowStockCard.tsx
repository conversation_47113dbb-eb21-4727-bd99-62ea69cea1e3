import { LowStockCardDTO } from "@/domain/DTOS/StockDTO.ts";
import { motion, Variants } from "framer-motion";

interface LowStockCardProps {
  alerte: LowStockCardDTO;
  index: number;
  itemVariants: Variants;
  getPrioriteColor: (priorite: string) => string;
  getPrioriteIcon: (priorite: string) => React.ReactNode;
}

const LowStockCard = ({
  alerte,
  index,
  itemVariants,
  getPrioriteColor,
  getPrioriteIcon,
}: LowStockCardProps) => {
  return (
    <motion.div
      key={alerte.id}
      variants={itemVariants}
      custom={index}
      className={`p-4 rounded-lg border ${getPrioriteColor(alerte.priorite)}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {getPrioriteIcon(alerte.priorite)}
          <div>
            <h4 className="font-medium text-gray-900 dark:text-gray-100">
              {alerte.produit}
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Stock actuel: {alerte.stock_actuel} {alerte.unite}
            </p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
            Seuil: {alerte.seuil_alerte} {alerte.unite}
          </p>
          <p className="text-xs text-red-600 dark:text-red-400">
            Réapprovisionnement nécessaire
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default LowStockCard;
