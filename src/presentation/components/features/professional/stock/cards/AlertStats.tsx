import { LotWithStockData, LowStockCardDTO } from "@/domain/DTOS/StockDTO.ts";
import { motion, Variants } from "framer-motion";
import { AlertTriangle, Calendar, Package } from "lucide-react";
import { memo } from "react";
import { StockNearPeremptionDateCardDTO } from "@/domain/DTOS/StockDTO.ts";

interface AlertStatsProps {
  expiredLots: LotWithStockData[];
  alertesPeremption: StockNearPeremptionDateCardDTO[];
  alertesStock: LowStockCardDTO[];
  itemVariants: Variants;
}

const AlertStats = ({
  expiredLots,
  alertesPeremption,
  alertesStock,
  itemVariants,
}: AlertStatsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <motion.div
        variants={itemVariants}
        className="bg-red-50 dark:bg-red-900/20 rounded-xl p-6 border border-red-200 dark:border-red-800"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-red-600 dark:text-red-400 mb-1">
              Alerte produits périmées
            </p>
            <p className="text-2xl font-bold text-red-700 dark:text-red-300">
              {expiredLots.length}
            </p>
          </div>
          <div className="p-3 rounded-lg bg-red-100 dark:bg-red-900/30">
            <AlertTriangle
              size={24}
              className="text-red-600 dark:text-red-400"
            />
          </div>
        </div>
      </motion.div>

      <motion.div
        variants={itemVariants}
        className="bg-orange-50 dark:bg-orange-900/20 rounded-xl p-6 border border-orange-200 dark:border-orange-800"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-orange-600 dark:text-orange-400 mb-1">
              Péremption proche
            </p>
            <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
              {alertesPeremption.length}
            </p>
          </div>
          <div className="p-3 rounded-lg bg-orange-100 dark:bg-orange-900/30">
            <Calendar
              size={24}
              className="text-orange-600 dark:text-orange-400"
            />
          </div>
        </div>
      </motion.div>

      <motion.div
        variants={itemVariants}
        className="bg-yellow-50 dark:bg-yellow-900/20 rounded-xl p-6 border border-yellow-200 dark:border-yellow-800"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400 mb-1">
              Stock faible
            </p>
            <p className="text-2xl font-bold text-yellow-700 dark:text-yellow-300">
              {alertesStock.length}
            </p>
          </div>
          <div className="p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900/30">
            <Package
              size={24}
              className="text-yellow-600 dark:text-yellow-400"
            />
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default memo(AlertStats);
