import { motion, Variants } from "framer-motion";
import { Package, Edit, Trash2 } from "lucide-react";
import { ProduitWithQuantite } from "@/presentation/hooks/professionalStock/handlers/use-produits.ts";

interface ProduitCardProps {
  produit: ProduitWithQuantite;
  index: number;
  itemVariants: Variants;
  cardHoverVariants: Variants;
  onEditProduit: () => void;
  onDeleteProduit: () => void;
}

const ProduitCard: React.FC<ProduitCardProps> = ({
  produit,
  index,
  itemVariants,
  cardHoverVariants,
  onEditProduit,
  onDeleteProduit,
}) => {
  return (
    <motion.div
      key={produit.id}
      variants={itemVariants}
      whileHover="hover"
      whileTap="tap"
      custom={index}
      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer"
      {...cardHoverVariants}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
            <Package
              size={20}
              className="text-purple-600 dark:text-purple-400"
            />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm">
              {produit.nom}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {produit.unite} • Seuil: {produit.seuil_alerte}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-1 text-gray-400 dark:text-gray-500 hover:text-blue-500 transition-colors"
            onClick={onEditProduit}
          >
            <Edit size={16} />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-1 text-gray-400 dark:text-gray-500 hover:text-red-500 transition-colors"
            onClick={onDeleteProduit}
          >
            <Trash2 size={16} />
          </motion.button>
        </div>
      </div>

      <div className="space-y-2 text-sm">
        {produit.description && (
          <p className="text-gray-600 dark:text-gray-300 line-clamp-2">
            📝 {produit.description}
          </p>
        )}
        <div className="flex items-center justify-between">
          <span className="text-gray-500 dark:text-gray-400">
            Stock actuel:
          </span>
          <span className="font-medium text-meddoc-primary">
            {produit.quantite} {produit.unite}
          </span>
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-500 dark:text-gray-400">
            Dernière entrée
          </span>
          <span className="text-meddoc-primary font-medium">
            {produit?.derniereEntree || "Aucune date trouvée"}
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default ProduitCard;
