import { motion, Variants } from "framer-motion";
import { LotWithStockData } from "@/domain/DTOS/StockDTO.ts";

interface ExpiredStockCardProps {
  alerte: LotWithStockData;
  index: number;
  itemVariants: Variants;
  getPrioriteColor: (priorite: string) => string;
  getPrioriteIcon: (priorite: string) => React.ReactNode;
}

const ExpiredStockCard = ({
  alerte,
  index,
  itemVariants,
  getPrioriteColor,
  getPrioriteIcon,
}: ExpiredStockCardProps) => {
  return (
    <motion.div
      key={alerte.id}
      variants={itemVariants}
      custom={index}
      className={`p-4 rounded-lg border ${getPrioriteColor("critique")}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {getPrioriteIcon("critique")}
          <div>
            <h4 className="font-medium text-gray-900 dark:text-gray-100">
              {alerte?.stocks?.nom}
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Lot {alerte?.numero_lot} • {alerte?.quantite} unités
            </p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
            Expiré le{" "}
            {new Date(alerte.date_expiration).toLocaleDateString("fr-FR")}
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default ExpiredStockCard;
