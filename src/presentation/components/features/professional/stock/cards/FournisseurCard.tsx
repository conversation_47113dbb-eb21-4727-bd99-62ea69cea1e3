import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import { motion, Variants } from "framer-motion";
import { Truck, Eye, Edit, Trash2 } from "lucide-react";

interface FournisseurCardProps {
  fournisseur: Fournisseurs;
  index: number;
  itemVariants: Variants;
  cardHoverVariants: Variants;
  onEditFournisseur: () => void;
  onDeleteFournisseur: () => void;
}

const FournisseurCard: React.FC<FournisseurCardProps> = ({
  fournisseur,
  index,
  itemVariants,
  cardHoverVariants,
  onEditFournisseur,
  onDeleteFournisseur,
}) => {
  return (
    <motion.div
      key={fournisseur.id}
      variants={itemVariants}
      whileHover="hover"
      whileTap="tap"
      custom={index}
      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer"
      {...cardHoverVariants}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
            <Truck size={20} className="text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm">
              {fournisseur.nom}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Fournisseur actif
            </p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-1 text-gray-400 dark:text-gray-500 hover:text-blue-500 transition-colors"
            onClick={onEditFournisseur}
          >
            <Edit size={16} />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-1 text-gray-400 dark:text-gray-500 hover:text-red-500 transition-colors"
            onClick={onDeleteFournisseur}
          >
            <Trash2 size={16} />
          </motion.button>
        </div>
      </div>

      <div className="space-y-2 text-sm">
        <p className="text-gray-600 dark:text-gray-300 line-clamp-2">
          📍 {fournisseur?.adresse || "Adresse non spécifié"}
        </p>
        <p className="text-gray-600 dark:text-gray-300">
          📞 {fournisseur?.telephone || "Numéro de téléphone non spécifié"}
        </p>
        <p className="text-gray-600 dark:text-gray-300">
          ✉️ {fournisseur?.courriel || "Couriel non spécifié"}
        </p>
      </div>
    </motion.div>
  );
};

export default FournisseurCard;
