import { motion, Variants } from "framer-motion";
import { StockNearPeremptionDateCardDTO } from "@/domain/DTOS/StockDTO.ts";

interface StockNearPeremptionDateCardProps {
  alerte: StockNearPeremptionDateCardDTO;
  index: number;
  itemVariants: Variants;
  getPrioriteColor: (priorite: string) => string;
  getPrioriteIcon: (priorite: string) => React.ReactNode;
}

const StockNearPeremptionDateCard = ({
  alerte,
  index,
  itemVariants,
  getPrioriteColor,
  getPrioriteIcon,
}: StockNearPeremptionDateCardProps) => {
  return (
    <motion.div
      key={alerte.id}
      variants={itemVariants}
      custom={index}
      className={`p-4 rounded-lg border ${getPrioriteColor(alerte.priorite)}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {getPrioriteIcon(alerte.priorite)}
          <div>
            <h4 className="font-medium text-gray-900 dark:text-gray-100">
              {alerte.produit}
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Lot {alerte.numero_lot} • {alerte.quantite} unités
            </p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
            Expire dans {alerte.jours_restants} jours
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {new Date(alerte.date_expiration).toLocaleDateString("fr-FR")}
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default StockNearPeremptionDateCard;
