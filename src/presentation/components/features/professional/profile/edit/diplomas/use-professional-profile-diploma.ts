import {
  DiplomaFormData,
  diplomaSchema,
} from "@/shared/schemas/ProfessionalProfileSchemas.ts";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

const useProfessionalProfileDiploma = (_professionalId?: number) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingDiplomaId, setEditingDiplomaId] = useState<number | null>(null);
  const [isLoading] = useState(false);

  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue,
  } = useForm<DiplomaFormData>({
    resolver: zodResolver(diplomaSchema),
    defaultValues: {
      titre: "",
      etablissement: "",
      annee: "",
      description: "",
    },
  });

  return {
    isAdding,
    setIsAdding,
    editingDiplomaId,
    setEditingDiplomaId,
    register,
    errors,
    handleSubmit,
    isLoading,
    setValue,
  };
};

export default useProfessionalProfileDiploma;
