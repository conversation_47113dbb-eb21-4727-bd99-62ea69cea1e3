import React from "react";
import { Building, Calendar, Edit3, X } from "lucide-react";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel.ts";

/**
 * Affichage d'un diplôme en mode lecture
 */
interface DiplomaViewItemProps {
  diploma: DiplomeProfessionnel;
  onEdit: (diploma: DiplomeProfessionnel) => void;
  onDelete: (id: number) => void;
}

const DiplomaViewItem: React.FC<DiplomaViewItemProps> = ({
  diploma,
  onEdit,
  onDelete,
}) => {
  return (
    <div className="flex items-start justify-between">
      <div className="flex-1">
        <h4 className="font-semibold text-gray-900 mb-1">{diploma.titre}</h4>
        <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
          <div className="flex items-center gap-1">
            <Building className="h-4 w-4" />
            {diploma.etablissement}
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            {diploma.annee}
          </div>
        </div>
        {diploma.description && (
          <p className="text-sm text-gray-700">{diploma.description}</p>
        )}
      </div>
      <div className="flex items-center gap-1 ml-4">
        <button
          type="button"
          onClick={() => onEdit(diploma)}
          className="p-1 text-blue-600 hover:bg-blue-100 rounded"
          title="Modifier"
        >
          <Edit3 className="h-4 w-4" />
        </button>
        <button
          type="button"
          onClick={() => onDelete(diploma.id)}
          className="p-1 text-red-600 hover:bg-red-100 rounded"
          title="Supprimer"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export default DiplomaViewItem;
