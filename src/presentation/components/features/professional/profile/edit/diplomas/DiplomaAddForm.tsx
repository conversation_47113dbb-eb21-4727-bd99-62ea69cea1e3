import React from "react";
import { GraduationCap, Building, Calendar, Text } from "lucide-react";
import FormField from "@/presentation/components/common/ui/FormField.tsx";
import Text<PERSON>reaField from "@/presentation/components/common/ui/TextAreaField.tsx";
import Button from "@/presentation/components/common/Button/Button.tsx";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { DiplomaFormData } from "@/shared/schemas/ProfessionalProfileSchemas";

/**
 * Formulaire d'ajout d'un diplôme.
 * Composant purement présentation, sans logique métier.
 */
interface DiplomaAddFormProps {
  register: UseFormRegister<DiplomaFormData>;
  errors: FieldErrors<DiplomaFormData>;
  onSubmit: React.FormEventHandler<HTMLFormElement>;
  onCancel: () => void;
  submitting?: boolean;
}

const DiplomaAddForm: React.FC<DiplomaAddFormProps> = ({
  register,
  errors,
  onSubmit,
  onCancel,
  submitting = false,
}) => {
  return (
    <form className="border-t pt-6" onSubmit={onSubmit}>
      <h3 className="text-lg font-medium text-gray-800 mb-3">
        Ajouter un nouveau diplôme
      </h3>
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            id="titre"
            label="Titre du diplôme"
            placeholder="Titre du diplôme"
            icon={GraduationCap}
            register={register}
            error={errors.titre}
            required
          />

          <FormField
            id="etablissement"
            label="Etablissement"
            placeholder="Etablissement"
            icon={Building}
            register={register}
            error={errors.etablissement}
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            id="annee"
            label="Année d'obtention"
            placeholder="Année d'obtention"
            icon={Calendar}
            register={register}
            error={errors.annee}
            required
          />
        </div>

        <TextAreaField
          id="description"
          label="Description"
          placeholder="Description"
          icon={Text}
          register={register}
          error={errors.description}
        />
      </div>
      <div className="flex justify-end gap-2 mt-4">
        <Button type="submit" disabled={submitting}>
          {submitting ? "Enregistrement..." : "Enregistrer"}
        </Button>
        <Button
          type="button"
          className="bg-gray-500 hover:bg-gray-600"
          onClick={onCancel}
          disabled={submitting}
        >
          Annuler
        </Button>
      </div>
    </form>
  );
};

export default DiplomaAddForm;
