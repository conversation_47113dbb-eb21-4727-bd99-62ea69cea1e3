import React from "react";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel.ts";
import DiplomaViewItem from "./DiplomaViewItem";
import DiplomaEditRow from "./DiplomaEditRow";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { DiplomaFormData } from "@/shared/schemas/ProfessionalProfileSchemas";

/**
 * Liste des diplômes avec switch lecture/édition élément par élément
 */
interface DiplomasListProps {
  diplomas: DiplomeProfessionnel[];
  editingDiplomaId: number | null;
  onStartEdit: (d: DiplomeProfessionnel) => void;
  onApplyEdit: (id: number) => React.FormEventHandler<HTMLFormElement>;
  onCancelEdit: () => void;
  onDelete: (id: number) => void;
  register: UseFormRegister<DiplomaFormData>;
  errors: FieldErrors<DiplomaFormData>;
}

const DiplomasList: React.FC<DiplomasListProps> = ({
  diplomas,
  editingDiplomaId,
  onStartEdit,
  onApplyEdit,
  onCancelEdit,
  onDelete,
  register,
  errors,
}) => {
  if (diplomas.length === 0) return null;

  return (
    <div>
      <h3 className="text-lg font-medium text-gray-800 mb-3">
        Diplômes existants
      </h3>
      <div className="space-y-3">
        {diplomas.map((diploma) => (
          <div
            key={diploma.id}
            className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
          >
            {editingDiplomaId === diploma.id ? (
              <DiplomaEditRow
                register={register}
                errors={errors}
                onEdit={onApplyEdit(diploma.id)}
                onCancel={onCancelEdit}
              />
            ) : (
              <DiplomaViewItem
                diploma={diploma}
                onEdit={onStartEdit}
                onDelete={onDelete}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DiplomasList;
