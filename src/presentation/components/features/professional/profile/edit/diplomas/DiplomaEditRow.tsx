import React from "react";
import {
  GraduationCap,
  Building,
  Calendar,
  Text,
  Check,
  X,
} from "lucide-react";
import <PERSON>Field from "@/presentation/components/common/ui/FormField.tsx";
import TextAreaField from "@/presentation/components/common/ui/TextAreaField.tsx";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { DiplomaFormData } from "@/shared/schemas/ProfessionalProfileSchemas";

/**
 * Ligne d'édition d'un diplôme (inputs)
 */
interface DiplomaEditRowProps {
  register: UseFormRegister<DiplomaFormData>;
  errors: FieldErrors<DiplomaFormData>;
  onEdit: React.FormEventHandler<HTMLFormElement>;
  onCancel: () => void;
}

const DiplomaEditRow: React.FC<DiplomaEditRowProps> = ({
  register,
  errors,
  onEdit,
  onCancel,
}) => {
  return (
    <form className="space-y-3" onSubmit={onEdit}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <FormField
          id="titre"
          label="Titre du diplôme *"
          type="text"
          placeholder="Titre du diplôme"
          icon={GraduationCap}
          register={register}
          error={errors.titre}
          helpText="Le titre du diplôme"
        />
        <FormField
          id="etablissement"
          label="Etablissement *"
          type="text"
          placeholder="Etablissement"
          icon={Building}
          register={register}
          error={errors.etablissement}
          helpText="L'etablissement"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <FormField
          id="annee"
          label="Année *"
          placeholder="Année"
          icon={Calendar}
          register={register}
          error={errors.annee}
          helpText="L'annee"
        />
      </div>
      <TextAreaField
        id="description"
        label="Description"
        placeholder="Description"
        icon={Text}
        register={register}
        error={errors.description}
        helpText="La description"
      />
      <div className="flex items-center gap-2">
        <button
          type="submit"
          className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center gap-1"
        >
          <Check className="h-4 w-4" />
          Sauvegarder
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 flex items-center gap-1"
        >
          <X className="h-4 w-4" />
          Annuler
        </button>
      </div>
    </form>
  );
};

export default DiplomaEditRow;
