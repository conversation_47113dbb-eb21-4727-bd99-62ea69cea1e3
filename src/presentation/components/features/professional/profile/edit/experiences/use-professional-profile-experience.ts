import {
  ExperienceFormData,
  experienceSchema,
} from "@/shared/schemas/ProfessionalProfileSchemas.ts";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

/**
 * Hook dédié à la gestion des expériences professionnelles d’un utilisateur.
 *
 * Responsabilités:
 * - Création, mise à jour et suppression immédiates via les usecases domaine
 * - Gestion du formulaire via react-hook-form (register, errors, handleSubmit, setValue)
 * - Gestion des toasts de feedback et de l’état de chargement
 *
 * SOLID:
 * - SRP: ce hook encapsule la logique métier UI/CRUD des expériences
 * - DIP: dépend de usecases/répositories injectés par construction locale
 *
 * @param professionalId Identifiant du professionnel auquel rattacher les expériences
 * @returns API du hook pour piloter la section expériences (actions CRUD + utilitaires de formulaire)
 */
const useProfessionalProfileExperience = (_professionalId?: number) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingExperienceId, setEditingExperienceId] = useState<number | null>(
    null
  );
  const [isLoading] = useState(false);

  // react-hook-form
  const {
    control,
    register,
    formState: { errors },
    handleSubmit,
    setValue,
  } = useForm<ExperienceFormData>({
    resolver: zodResolver(experienceSchema),
    defaultValues: {
      poste: "",
      etablissement: "",
      date_debut: null,
      date_fin: null,
      description: "",
    },
  });

  return {
    isAdding,
    setIsAdding,
    editingExperienceId,
    setEditingExperienceId,
    control,
    register,
    errors,
    handleSubmit,
    isLoading,
    setValue,
  };
};

export default useProfessionalProfileExperience;
