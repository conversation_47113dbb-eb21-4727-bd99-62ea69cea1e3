import React from "react";
import { Building, Calendar, Text, Briefcase, Check, X } from "lucide-react";
import FormField from "@/presentation/components/common/ui/FormField.tsx";
import DateField from "@/presentation/components/common/ui/DateField.tsx";
import Text<PERSON>reaField from "@/presentation/components/common/ui/TextAreaField.tsx";
import { Control, FieldErrors, UseFormRegister } from "react-hook-form";
import { ExperienceFormData } from "@/shared/schemas/ProfessionalProfileSchemas.ts";

/**
 * Ligne d'édition d'une expérience (UI pure)
 * - Utilise react-hook-form via `register`/`errors`
 * - `onEdit` est un React.FormEventHandler<HTMLFormElement>
 */
interface ExperienceEditRowProps {
  register: UseFormRegister<ExperienceFormData>;
  errors: FieldErrors<ExperienceFormData>;
  control: Control<ExperienceFormData>;
  onEdit: React.FormEventHandler<HTMLFormElement>;
  onCancel: () => void;
}

const ExperienceEditRow: React.FC<ExperienceEditRowProps> = ({
  register,
  errors,
  control,
  onEdit,
  onCancel,
}) => {
  return (
    <form className="space-y-3" onSubmit={onEdit}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <FormField
          id="poste"
          label="Poste occupé *"
          type="text"
          placeholder="Poste occupé"
          icon={Briefcase}
          register={register}
          error={errors.poste}
          helpText="Le poste occupé"
        />
        <FormField
          id="etablissement"
          label="Établissement *"
          type="text"
          placeholder="Établissement"
          icon={Building}
          register={register}
          error={errors.etablissement}
          helpText="L'établissement"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <DateField
          id="date_debut"
          label="Date de début *"
          icon={Calendar}
          control={control}
          error={errors.date_debut}
        />
        <DateField
          id="date_fin"
          label="Date de fin (optionnelle)"
          icon={Calendar}
          control={control}
          error={errors.date_fin}
        />
      </div>
      <TextAreaField
        id="description"
        label="Description"
        placeholder="Description"
        icon={Text}
        register={register}
        error={errors.description}
        helpText="Description des responsabilités et réalisations"
      />
      <div className="flex items-center gap-2">
        <button
          type="submit"
          className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center gap-1"
        >
          <Check className="h-4 w-4" />
          Sauvegarder
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 flex items-center gap-1"
        >
          <X className="h-4 w-4" />
          Annuler
        </button>
      </div>
    </form>
  );
};

export default ExperienceEditRow;
