import React from "react";
import { Building, Calendar, Text, Briefcase } from "lucide-react";
import FormField from "@/presentation/components/common/ui/FormField.tsx";
import DateField from "@/presentation/components/common/ui/DateField.tsx";
import Text<PERSON>reaField from "@/presentation/components/common/ui/TextAreaField.tsx";
import Button from "@/presentation/components/common/Button/Button.tsx";
import { Control, FieldErrors, UseFormRegister } from "react-hook-form";
import { ExperienceFormData } from "@/shared/schemas/ProfessionalProfileSchemas.ts";

/**
 * Formulaire d'ajout d'une expérience (UI pure)
 * - Utilise react-hook-form via `register`/`errors`
 * - `onSubmit` est un React.FormEventHandler<HTMLFormElement>
 */
interface ExperienceAddFormProps {
  register: UseFormRegister<ExperienceFormData>;
  errors: FieldErrors<ExperienceFormData>;
  control: Control<ExperienceFormData>;
  onSubmit: React.FormEventHandler<HTMLFormElement>;
  onCancel: () => void;
  submitting?: boolean;
}

const ExperienceAddForm: React.FC<ExperienceAddFormProps> = ({
  register,
  errors,
  control,
  onSubmit,
  onCancel,
  submitting = false,
}) => {
  return (
    <form className="border-t pt-6" onSubmit={onSubmit}>
      <h3 className="text-lg font-medium text-gray-800 mb-3">
        Ajouter une nouvelle expérience
      </h3>
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            id="poste"
            label="Poste occupé"
            placeholder="Ex: Médecin généraliste"
            icon={Briefcase}
            register={register}
            error={errors.poste}
            required
          />

          <FormField
            id="etablissement"
            label="Établissement"
            placeholder="Ex: Hôpital Central"
            icon={Building}
            register={register}
            error={errors.etablissement}
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DateField
            id="date_debut"
            label="Date de début"
            icon={Calendar}
            control={control}
            error={errors.date_debut}
            required
          />
          <DateField
            id="date_fin"
            label="Date de fin (optionnelle)"
            icon={Calendar}
            control={control}
            error={errors.date_fin}
          />
        </div>

        <TextAreaField
          id="description"
          label="Description (optionnelle)"
          placeholder="Décrivez vos responsabilités, réalisations et compétences..."
          icon={Text}
          register={register}
          error={errors.description}
        />
      </div>
      <div className="flex justify-end gap-2 mt-4">
        <Button type="submit" disabled={submitting}>
          {submitting ? "Enregistrement..." : "Enregistrer"}
        </Button>
        <Button
          type="button"
          className="bg-gray-500 hover:bg-gray-600"
          onClick={onCancel}
          disabled={submitting}
        >
          Annuler
        </Button>
      </div>
    </form>
  );
};

export default ExperienceAddForm;
