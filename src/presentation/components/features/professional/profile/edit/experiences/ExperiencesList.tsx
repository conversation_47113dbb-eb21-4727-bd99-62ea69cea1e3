import React from "react";
import { ExperienceProfessionnel } from "@/domain/models/ExperienceProfessionnel.ts";
import { Control, FieldErrors, UseFormRegister } from "react-hook-form";
import ExperienceEditRow from "./ExperienceEditRow";
import { Building, Calendar, Edit3, FileText, X } from "lucide-react";
import { ExperienceFormData } from "@/shared/schemas/ProfessionalProfileSchemas.ts";

interface ExperiencesListProps {
  experiences: ExperienceProfessionnel[];
  editingExperienceId: number | null;
  onStartEdit: (e: ExperienceProfessionnel) => void;
  onApplyEdit: (id: number) => React.FormEventHandler<HTMLFormElement>;
  onCancelEdit: () => void;
  onDelete: (id: number) => void;
  register: UseFormRegister<ExperienceFormData>;
  errors: FieldErrors<ExperienceFormData>;
  control: Control<ExperienceFormData>;
  formatPeriod: (dateDebut: string, dateFin?: string | null) => string;
}

const ExperiencesList: React.FC<ExperiencesListProps> = ({
  experiences,
  editingExperienceId,
  onStartEdit,
  onApplyEdit,
  onCancelEdit,
  onDelete,
  register,
  errors,
  control,
  formatPeriod,
}) => {
  if (experiences.length === 0) return null;
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-800 mb-3">
        Expériences existantes
      </h3>
      <div className="space-y-3">
        {experiences.map((exp) => (
          <div
            key={exp.id}
            className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
          >
            {editingExperienceId === exp.id ? (
              <ExperienceEditRow
                register={register}
                errors={errors}
                control={control}
                onEdit={onApplyEdit(exp.id)}
                onCancel={onCancelEdit}
              />
            ) : (
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {exp.poste}
                  </h4>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                    <div className="flex items-center gap-1">
                      <Building className="h-4 w-4" />
                      {exp.etablissement}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {formatPeriod(exp.date_debut, exp.date_fin)}
                    </div>
                  </div>
                  {exp.description && (
                    <div className="flex items-start gap-2">
                      <FileText className="h-4 w-4 mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-gray-700">{exp.description}</p>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-1 ml-4">
                  <button
                    onClick={() => onStartEdit(exp)}
                    className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                    title="Modifier"
                  >
                    <Edit3 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => onDelete(exp.id)}
                    className="p-1 text-red-600 hover:bg-red-100 rounded"
                    title="Supprimer"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExperiencesList;
