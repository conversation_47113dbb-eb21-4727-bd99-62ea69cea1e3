import { User } from "lucide-react";
import React from "react";

/**
 * Interface pour les propriétés du composant ProfileEditPhoto
 */
interface ProfileEditPhotoProps {
  /** URL de l'image de profil actuelle */
  imageUrl: string;
  /** Texte alternatif pour l'image */
  altText: string;
  /** Indique si le mode édition est actif */
  isEditing: boolean;
  /** Fonction appelée lors du changement d'image */
  onImageChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

/**
 * Composant de photo de profil éditable
 *
 * Ce composant affiche la photo de profil du professionnel avec
 * la possibilité de la modifier en mode édition. Il inclut des
 * recommandations pour le format et la taille des images.
 *
 * @example
 * ```tsx
 * <ProfileEditPhoto
 *   imageUrl="/default-profile.png"
 *   altText="Photo de Dr. Dupont Jean"
 *   isEditing={true}
 *   onImageChange={(e) => handleImageChange(e)}
 * />
 * ```
 *
 * @param props - Les propriétés du composant
 * @returns Élément JSX représentant la photo de profil éditable
 */
const ProfileEditPhoto: React.FC<ProfileEditPhotoProps> = ({
  imageUrl,
  altText,
  isEditing,
  onImageChange,
}) => {
  return (
    <section className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex flex-col items-center">
        <div className="relative">
          {imageUrl && imageUrl !== "" ? (
            <img
              src={imageUrl}
              alt={altText}
              className="w-48 h-48 rounded-full object-cover border-4 border-gray-200"
            />
          ) : (
            <div className="w-48 h-48 rounded-full object-cover border-4 border-gray-200 bg-gray-200 flex flex-col justify-center items-center align-center">
              <User size={60} className="text-gray-400" />
            </div>
          )}
          {isEditing && (
            <label className="absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700">
              <input
                type="file"
                accept="image/*"
                onChange={onImageChange}
                className="hidden"
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
            </label>
          )}
        </div>
        {isEditing && (
          <div className="mt-4 text-sm text-gray-500 text-center">
            <p>Format recommandé : JPG, PNG</p>
            <p>Taille maximale : 5MB</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProfileEditPhoto;
