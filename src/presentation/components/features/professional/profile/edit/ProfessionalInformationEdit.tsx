import React from "react";
import { Briefcase, Hash, Building } from "lucide-react";
import EditableSection from "./EditableSection";
import FormField from "@/presentation/components/common/ui/FormField.tsx";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form.ts";
import { professionalInfoSchema } from "@/shared/schemas/ProfessionalProfileSchemas.ts";

/**
 * Interface pour les données professionnelles
 */
interface ProfessionalData {
  numero_ordre: string;
  raison_sociale: string;
  nif: string;
  stat: string;
}

/**
 * Interface pour les propriétés du composant ProfessionalInformationEdit
 */
interface ProfessionalInformationEditProps {
  /** Numéro d'ordre du professionnel */
  numeroOrdre: string;
  /** Raison sociale du professionnel */
  raisonSociale: string;
  /** Numéro d'identification fiscale (NIF) */
  nif: string;
  /** Numéro statistique (STAT) */
  stat: string;
  /** Fonction appelée lors de la sauvegarde des informations professionnelles */
  onSave: (professionalData: Partial<ProfessionalData>) => Promise<boolean>;
}

/**
 * Composant d'informations professionnelles éditable
 *
 * Ce composant utilise le nouveau système d'édition par section pour
 * permettre la modification des informations professionnelles du médecin.
 * Il gère automatiquement l'état d'édition, la validation et la sauvegarde.
 *
 * @example
 * ```tsx
 * <ProfessionalInformationEdit
 *   numeroOrdre="12345"
 *   raisonSociale="Cabinet Dr. Dupont"
 *   nif="123456789"
 *   stat="987654321"
 *   onSave={async (data) => await updateProfessionalInfo(data)}
 * />
 * ```
 *
 * @param props - Les propriétés du composant
 * @returns Élément JSX représentant les informations professionnelles
 */
const ProfessionalInformationEdit: React.FC<
  ProfessionalInformationEditProps
> = ({ numeroOrdre, raisonSociale, nif, stat, onSave }) => {
  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    register,
    formState: { errors },
    save,
    cancel,
    isSaving,
  } = useSectionForm({
    schema: professionalInfoSchema,
    defaultValues: {
      nif: nif || "",
      stat: stat || "",
      raison_sociale: raisonSociale || "",
      numero_ordre: numeroOrdre || "",
    },
    onSave: async (data) => await onSave(data),
    sectionName: "informations de contact",
  });

  return (
    <EditableSection
      title="Informations professionnelles"
      icon={Briefcase}
      onSave={save}
      onCancel={cancel}
      isSaving={isSaving}
      editContent={
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            id="numero_ordre"
            label="Numéro d'ordre"
            placeholder="Entrez votre numéro d'ordre"
            icon={Hash}
            register={register}
            error={errors.numero_ordre}
          />

          <FormField
            id="raison_sociale"
            label="Raison sociale"
            placeholder="Entrez votre raison sociale"
            icon={Building}
            register={register}
            error={errors.raison_sociale}
          />

          <FormField
            id="nif"
            label="NIF"
            placeholder="Entrez votre NIF"
            icon={Hash}
            register={register}
            error={errors.nif}
          />

          <FormField
            id="stat"
            label="STAT"
            placeholder="Entrez votre STAT"
            icon={Hash}
            register={register}
            error={errors.stat}
          />
        </div>
      }
    >
      {/* Contenu en mode lecture */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-1">
            <Hash className="h-4 w-4 text-meddoc-primary" />
            Numéro d'ordre
          </h3>
          <p className="text-gray-900 dark:text-white">
            {numeroOrdre || "Non renseigné"}
          </p>
        </div>
        <div>
          <h3 className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-1">
            <Building className="h-4 w-4 text-meddoc-primary" />
            Raison sociale
          </h3>
          <p className="text-gray-900 dark:text-white">
            {raisonSociale || "Non renseignée"}
          </p>
        </div>
        <div>
          <h3 className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-1">
            <Hash className="h-4 w-4 text-meddoc-primary dark:text-white" />
            NIF
          </h3>
          <p className="text-gray-900 dark:text-white">
            {nif || "Non renseigné"}
          </p>
        </div>
        <div>
          <h3 className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-1">
            <Hash className="h-4 w-4 text-meddoc-primary" />
            STAT
          </h3>
          <p className="text-gray-900 dark:text-white">
            {stat || "Non renseigné"}
          </p>
        </div>
      </div>
    </EditableSection>
  );
};

export default ProfessionalInformationEdit;
