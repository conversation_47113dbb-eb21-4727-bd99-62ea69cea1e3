import React, { useEffect, useState } from "react";
import { Languages, X } from "lucide-react";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import { LangueParleeProfessionnel } from "@/domain/models";
import { z } from "zod";
import ConfirmActionModal from "@/presentation/components/common/Modal/ConfirmActionModal";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";

/**
 * Interface pour les propriétés du composant LanguagesCRUDSection
 */
interface LanguagesCRUDSectionProps {
  /** Liste des langues du professionnel */
  languages: LangueParleeProfessionnel[];
  /** Fonction pour ajouter des langues */
  onAddLanguages: (languageNames: string[]) => Promise<boolean>;
  /** Fonction pour supprimer une langue */
  onDeleteLanguage: (id: number) => Promise<boolean>;
}

/**
 * Composant de section langues avec opérations CRUD complètes
 *
 * @description
 * - Affiche les langues existantes du professionnel
 * - Permet l'ajout via un champ texte simple
 * - La suppression est différée: clic sur l'icône = retrait visuel + marquage,
 *   l'appel API effectif est réalisé uniquement au clic sur "Sauvegarder" (avec confirmation)
 * - Le parent rafraîchit les données au succès pour garder l'UI à jour
 */
const LanguagesCRUDSection: React.FC<LanguagesCRUDSectionProps> = ({
  languages,
  onAddLanguages,
  onDeleteLanguage,
}) => {
  // Sélection locale des nouvelles langues à ajouter
  const [newLanguageInput, setNewLanguageInput] = useState("");

  // Liste des langues à supprimer
  const [languagesToDelete, setLanguagesToDelete] = useState<
    LangueParleeProfessionnel[]
  >([]);

  // Suppressions différées
  const [pendingDeletionIds, setPendingDeletionIds] = useState<number[]>([]);

  // État pour la modale de confirmation
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Réinitialiser les suppressions en attente quand les props changent
  useEffect(() => {
    setPendingDeletionIds([]);
  }, [languages]);

  // Calculer les langues à afficher (exclure celles marquées pour suppression)
  const displayedLanguages = languages.filter(
    (language) => !pendingDeletionIds.includes(language.id)
  );

  // Gestion de l'état d'édition avec le hook spécialisé
  const {
    save: originalSave,
    cancel,
    isSaving,
    setValue,
    watch,
  } = useSectionForm({
    schema: z.object({
      newLanguages: z.array(z.string().min(1).max(100)).default([]),
    }),
    defaultValues: {
      newLanguages: [],
    },
    onSave: async (data) => {
      // Seulement gérer l'ajout de nouvelles langues ici
      if (data.newLanguages && data.newLanguages.length > 0) {
        const addOk = await onAddLanguages(data.newLanguages);
        if (addOk) {
          // Réinitialiser le formulaire après ajout réussi
          setValue("newLanguages", []);
          setNewLanguageInput("");
          setLanguagesToDelete([]);
          setPendingDeletionIds([]);
          setShowConfirmModal(false);
          cancel();
        }
        return addOk;
      }
      return true; // Pas de nouvelles langues à ajouter
    },
    sectionName: "langues",
  });

  // Fonction de sauvegarde personnalisée qui gère les suppressions
  const save = async () => {
    // S'il y a des suppressions en attente, ouvrir la modale de confirmation
    if (pendingDeletionIds.length > 0) {
      setShowConfirmModal(true);
      return;
    }

    // Sinon, procéder à la sauvegarde normale
    await originalSave();
  };

  // Callback d'annulation de la modification
  const handleCancel = () => {
    setPendingDeletionIds([]);
    // Remettre les langues supprimées dans la liste
    displayedLanguages.push(...languagesToDelete);
    setLanguagesToDelete([]);
    setShowConfirmModal(false);
    cancel();
  };

  // Marquer une langue pour suppression (sans appel API immédiat)
  const markLanguageForDeletion = (languageId: number) => {
    const matchingLanguage = languages.find((lang) => lang.id === languageId);
    if (!matchingLanguage) return;
    setPendingDeletionIds((prev) => [...prev, languageId]);
    setLanguagesToDelete((prev) => [...prev, matchingLanguage]);
  };

  // Retirer une langue de la liste des langues à supprimer
  const unmarkLanguageForDeletion = (languageId: number) => {
    const matchingLanguage = languages.find((lang) => lang.id === languageId);
    if (!matchingLanguage) return;
    setLanguagesToDelete((prev) =>
      prev.filter((lang) => lang.id !== languageId)
    );
    setPendingDeletionIds(pendingDeletionIds.filter((id) => id !== languageId));
  };

  // Ajouter une nouvelle langue à la liste locale
  const addNewLanguage = () => {
    if (newLanguageInput.trim().length === 0) return;

    const currentLanguages = watch("newLanguages") || [];
    const updatedLanguages = [...currentLanguages, newLanguageInput.trim()];
    setValue("newLanguages", updatedLanguages);
    setNewLanguageInput("");
  };

  // Gérer la confirmation de suppression
  const handleConfirmDeletion = async (): Promise<void> => {
    setIsDeleting(true);
    let overallSuccess = true;

    for (const id of pendingDeletionIds) {
      try {
        const delOk = await onDeleteLanguage(id);
        overallSuccess = overallSuccess && delOk;
      } catch (e) {
        overallSuccess = false;
      }
    }

    if (overallSuccess) {
      setPendingDeletionIds([]);
      // Après suppression réussie, procéder à la sauvegarde normale (pour les ajouts éventuels)
      await originalSave();
    }

    setIsDeleting(false);
    setShowConfirmModal(false);
  };

  return (
    <>
      <EditableSection
        title="Langues parlées"
        icon={Languages}
        onSave={save}
        onCancel={handleCancel}
        isSaving={isSaving}
        editContent={
          <div className="space-y-4">
            {/* Liste des langues existantes */}
            <div className="space-y-2">
              {displayedLanguages.map((language) => (
                <div
                  key={language.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                >
                  <>
                    <span className="text-gray-700">{language.nom_langue}</span>
                    <div className="flex items-center gap-1">
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          markLanguageForDeletion(language.id);
                        }}
                        className="p-1 text-red-600 hover:bg-red-100 rounded"
                        title="Supprimer"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </>
                </div>
              ))}
            </div>

            {/* Affichage des langues en attente d'ajout */}
            {watch("newLanguages")?.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">
                  Langues à ajouter :
                </h4>
                {watch("newLanguages").map((languageName, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200"
                  >
                    <span className="text-green-700">{languageName}</span>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        const currentLanguages = watch("newLanguages") || [];
                        const updatedLanguages = currentLanguages.filter(
                          (_, i) => i !== index
                        );
                        setValue("newLanguages", updatedLanguages);
                      }}
                      className="p-1 text-red-600 hover:bg-red-100 rounded"
                      title="Retirer"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Ajout d'une nouvelle langue */}
            <div className="border-t pt-4">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newLanguageInput}
                  onChange={(e) => setNewLanguageInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      addNewLanguage();
                    }
                  }}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:border-meddoc-primary focus:ring-meddoc-primary"
                  placeholder="Nom de la langue (ex: Français, Anglais, Malagasy...)"
                  maxLength={100}
                />
                <button
                  onClick={addNewLanguage}
                  disabled={!newLanguageInput.trim()}
                  className="px-4 py-2 bg-meddoc-primary text-white rounded-lg hover:bg-meddoc-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Ajouter à la liste"
                >
                  Ajouter
                </button>
              </div>
              {newLanguageInput.length > 0 && (
                <div className="mt-1 text-sm text-gray-500">
                  {newLanguageInput.length}/100 caractères
                </div>
              )}
            </div>

            {/* Affichage des langues à supprimer */}
            {languagesToDelete.length > 0 && (
              <div className="space-y-2 bg-red-100 p-4 rounded">
                <div className="mt-4">
                  <h2 className="text-lg font-semibold mb-2">
                    Langues à supprimer :
                  </h2>
                  {languagesToDelete.length > 0 && (
                    <div className="space-y-2">
                      {languagesToDelete.map((language) => (
                        <div
                          key={language.id}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                        >
                          <>
                            <span>{language.nom_langue}</span>
                            <div className="flex items-center gap-1">
                              <button
                                onClick={(e) => {
                                  e.preventDefault();
                                  unmarkLanguageForDeletion(language.id);
                                }}
                                className="p-1 text-red-600 hover:bg-red-100 rounded"
                                title="Supprimer"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                          </>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        }
      >
        {/* Contenu en mode lecture */}
        <div>
          {languages.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {languages.map((language) => (
                <span
                  key={language.id}
                  className="inline-flex items-center px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200"
                >
                  {language.nom_langue}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-gray-400 italic">
              Aucune langue renseignée. Cliquez sur l'icône d'édition pour
              ajouter des langues.
            </p>
          )}
        </div>
      </EditableSection>

      {/* Modale de confirmation pour les suppressions */}
      <ConfirmationModal
        isOpen={showConfirmModal}
        title="Confirmer la suppression"
        message={`Êtes-vous sûr de vouloir supprimer ${pendingDeletionIds.length} langue(s) ? Cette action est irréversible.`}
        confirmButtonText="Supprimer"
        cancelButtonText="Annuler"
        loading={isDeleting}
        onConfirm={handleConfirmDeletion}
        onClose={() => {
          if (!isDeleting) {
            setShowConfirmModal(false);
          }
        }}
      />
    </>
  );
};

export default LanguagesCRUDSection;
