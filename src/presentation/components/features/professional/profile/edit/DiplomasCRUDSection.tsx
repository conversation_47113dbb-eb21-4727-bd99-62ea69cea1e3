import React, { useCallback, useState } from "react";
import { GraduationCap, Plus } from "lucide-react";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel.ts";
import Button from "@/presentation/components/common/Button/Button.tsx";
import { useToast } from "@/presentation/hooks/use-toast.ts";
import DiplomasList from "@/presentation/components/features/professional/profile/edit/diplomas/DiplomasList";
import DiplomaAddForm from "@/presentation/components/features/professional/profile/edit/diplomas/DiplomaAddForm";
import { AnimatePresence, motion } from "framer-motion";
import useProfessionalProfileDiploma from "./diplomas/use-professional-profile-diploma.ts";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";

/**
 * Interface pour les propriétés du composant DiplomasCRUDSection
 */
interface DiplomasCRUDSectionProps {
  /** Liste des diplômes du professionnel */
  diplomas: DiplomeProfessionnel[];
  /** ID du professionnel */
  professionalId: number;
  /** Fonction de creation de diplôme */
  addDiploma: (
    data: Omit<DiplomeProfessionnel, "id" | "id_professionnel">
  ) => Promise<boolean>;
  /** Fonction de mise à jour d'un diplôme */
  updateDiploma: (
    diplomaId: number,
    data: Partial<DiplomeProfessionnel>
  ) => Promise<boolean>;
  /** Fonction de suppression d'un diplôme */
  deleteDiploma: (diplomaId: number) => Promise<boolean>;
}

/**
 * Composant de section diplômes avec actions directes (CRUD)
 *
 * @description Permet l'ajout, la modification et la suppression sans bouton
 * de sauvegarde global ni wrapper `EditableSection`. Les actions sont
 * déclenchées immédiatement avec confirmation pour la suppression.
 */
const DiplomasCRUDSection: React.FC<DiplomasCRUDSectionProps> = ({
  diplomas,
  professionalId,
  addDiploma,
  updateDiploma,
  deleteDiploma,
}) => {
  const [selectedDeleteId, setSelectedDeleteId] = useState<number | null>(null);
  const toast = useToast();
  const {
    isLoading,
    isAdding,
    setIsAdding,
    editingDiplomaId,
    setEditingDiplomaId,
    register,
    errors,
    handleSubmit,
    setValue,
  } = useProfessionalProfileDiploma(professionalId);

  const startUpdateDiploma = useCallback(
    (data: DiplomeProfessionnel) => {
      setValue("titre", data.titre);
      setValue("etablissement", data.etablissement);
      setValue("annee", String(data.annee));
      setValue("description", data.description);
    },
    [setValue]
  );

  return (
    <>
      <section
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700`}
      >
        {/* En-tête de la section */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-meddoc-primary/10 dark:bg-meddoc-primary/20 rounded-lg">
              <GraduationCap className="h-5 w-5 text-meddoc-primary" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Diplômes et formations
            </h2>
          </div>
        </div>

        {/* Contenu de la section */}
        <div className="p-4">
          <AnimatePresence mode="wait">
            <motion.div
              key="view"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="flex flex-col gap-4"
            >
              {/* Liste des diplômes existants */}
              {diplomas.length > 0 && (
                <DiplomasList
                  diplomas={diplomas}
                  editingDiplomaId={editingDiplomaId}
                  onStartEdit={(diploma) => {
                    startUpdateDiploma(diploma);
                    setEditingDiplomaId(diploma.id);
                  }}
                  onApplyEdit={(id) =>
                    handleSubmit(async (data) => {
                      const updatedDiploma = await updateDiploma(id, data);
                      if (updatedDiploma) {
                        setEditingDiplomaId(null);
                        toast.success("Diplôme mis à jour avec succès.");
                      }
                    })
                  }
                  onCancelEdit={() => setEditingDiplomaId(null)}
                  onDelete={(id) => setSelectedDeleteId(id)}
                  register={register}
                  errors={errors}
                />
              )}
              {/* Formulaire d'ajout */}
              {isAdding ? (
                <DiplomaAddForm
                  register={register}
                  errors={errors}
                  onSubmit={handleSubmit(async (data) => {
                    const formated: Omit<
                      DiplomeProfessionnel,
                      "id" | "id_professionnel"
                    > = {
                      titre: data.titre,
                      etablissement: data.etablissement,
                      annee: data.annee,
                      description: data.description,
                    };

                    const addedDiploma = await addDiploma(formated);
                    if (addedDiploma) {
                      setIsAdding(false);
                      toast.success("Diplôme ajouté avec succès.");
                    }
                  })}
                  onCancel={() => setIsAdding(false)}
                  submitting={isLoading}
                />
              ) : (
                editingDiplomaId === null && (
                  <Button
                    type="button"
                    className="ml-auto flex gap-1 items-center"
                    onClick={() => setIsAdding(true)}
                  >
                    <Plus />
                    Ajouter un diplôme
                  </Button>
                )
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </section>

      {/* Modale de confirmation de suppression */}
      <ConfirmationModal
        isOpen={typeof selectedDeleteId === "number"}
        title="Confirmer la suppression"
        message={`Êtes-vous sûr de vouloir supprimer ce diplôme ? Cette action est irréversible.`}
        confirmButtonText="Supprimer"
        cancelButtonText="Annuler"
        loading={isLoading}
        onConfirm={async () => {
          if (selectedDeleteId == null) return;
          const ok = await deleteDiploma(selectedDeleteId);
          if (ok) {
            deleteDiploma(selectedDeleteId);
            toast.success("Diplôme supprimé avec succès.");
          }
          setSelectedDeleteId(null);
        }}
        onClose={() => {
          if (isLoading) return;
          setSelectedDeleteId(null);
        }}
      />
    </>
  );
};

export default DiplomasCRUDSection;
