import React from "react";
import { Camera, X, Image } from "lucide-react";
import { UPLOAD_CONFIG } from "@/shared/constants/cabinetMedicalConfig.ts";
import { Photo } from "@/domain/models/Photo.ts";

/**
 * Interface pour les propriétés du composant CabinetImagesGallery
 */
interface CabinetImagesGalleryProps {
  /** Liste des images du cabinet */
  images: Photo[];
  /** Indique si le mode édition est actif */
  isEditing: boolean;
  /** Nombre maximum d'images autorisées */
  maxImages?: number;
  /** Fonction appelée lors de l'ajout d'images */
  onAddImages?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  /** Fonction appelée lors de la suppression d'une image */
  onRemoveImage?: (imageId: string | number) => void;
  /** Messages d'erreur pour les images */
  imageErrors?: string[];
}

/**
 * Composant de galerie d'images du cabinet médical
 *
 * Ce composant affiche une galerie d'images du cabinet médical
 * avec la possibilité d'ajouter et supprimer des images en mode édition.
 * Il inclut la gestion des erreurs et des limites de taille/nombre.
 *
 * @example
 * ```tsx
 * <CabinetImagesGallery
 *   images={[
 *     { id: 1, url: "/images/cabinet1.jpg", alt: "Salle d'attente" },
 *     { id: 2, url: "/images/cabinet2.jpg", alt: "Bureau de consultation" }
 *   ]}
 *   isEditing={true}
 *   maxImages={5}
 *   onAddImages={(e) => handleAddImages(e)}
 *   onRemoveImage={(id) => handleRemoveImage(id)}
 *   imageErrors={["Erreur de taille pour image1.jpg"]}
 * />
 * ```
 *
 * @param props - Les propriétés du composant
 * @returns Élément JSX représentant la galerie d'images
 */
const CabinetImagesGallery: React.FC<CabinetImagesGalleryProps> = ({
  images,
  isEditing,
  maxImages = UPLOAD_CONFIG.MAX_CABINET_IMAGES,
  onAddImages,
  onRemoveImage,
  imageErrors = [],
}) => {
  const canAddMore = images.length < maxImages;

  return (
    <section className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Camera size={20} className="text-indigo-600" />
          <h2 className="text-xl font-semibold text-gray-900">
            Images du cabinet
          </h2>
        </div>
        <span className="text-sm text-gray-500">
          {images.length}/{maxImages} images
        </span>
      </div>

      <p className="text-sm text-gray-600 mb-4">
        Présentez votre cabinet médical avec des photos de qualité
      </p>

      {/* Galerie d'images */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4">
        {images.map((image) => (
          <div
            key={image.id}
            className="relative group aspect-square bg-gray-100 rounded-lg overflow-hidden"
          >
            <img
              src={image.path}
              alt={`Image du cabinet ${image.id}`}
              className="w-full h-full object-cover"
            />
            {isEditing && onRemoveImage && (
              <button
                onClick={() => onRemoveImage(image.id)}
                className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
              >
                <X size={16} />
              </button>
            )}
            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity">
              {image.id || `Image ${image.id}`}
            </div>
          </div>
        ))}

        {/* Bouton d'ajout d'image */}
        {isEditing && canAddMore && onAddImages && (
          <label className="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-indigo-400 hover:bg-indigo-50 transition-colors">
            <Camera size={24} className="text-gray-400 mb-2" />
            <span className="text-sm text-gray-500 text-center">
              Ajouter une image
            </span>
            <input
              type="file"
              accept="image/*"
              multiple
              onChange={onAddImages}
              className="hidden"
            />
          </label>
        )}
      </div>

      {/* Messages d'information */}
      {isEditing && (
        <div className="text-xs text-gray-500 mb-2">
          <p>• Formats acceptés : JPG, PNG, WebP</p>
          <p>• Taille maximale : 5MB par image</p>
          <p>• Maximum {maxImages} images</p>
        </div>
      )}

      {/* Erreurs */}
      {imageErrors.length > 0 && (
        <div className="space-y-1">
          {imageErrors.map((error, index) => (
            <div key={index} className="flex items-start text-red-500 text-sm">
              <X size={16} className="mr-1 mt-0.5 flex-shrink-0" />
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}

      {/* État vide */}
      {images.length === 0 && (
        <div className="text-center py-12">
          <Image className="h-16 w-16 mx-auto mb-4 text-gray-300" />
          <p className="text-gray-500 mb-2">
            {isEditing
              ? "Aucune image ajoutée. Cliquez pour ajouter des photos de votre cabinet."
              : "Aucune image du cabinet disponible"}
          </p>
          {isEditing && onAddImages && (
            <label className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg cursor-pointer hover:bg-indigo-700 transition-colors">
              <Camera size={16} />
              Ajouter des images
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={onAddImages}
                className="hidden"
              />
            </label>
          )}
        </div>
      )}
    </section>
  );
};

export default CabinetImagesGallery;
