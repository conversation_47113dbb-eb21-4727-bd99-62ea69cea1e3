import React, { useState, useCallback } from "react";
import { Camera, X, Image, Upload, AlertCircle } from "lucide-react";
import { Photo } from "@/domain/models/Photo.ts";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import { cabinetImagesSchema } from "@/shared/schemas/ProfessionalProfileSchemas";
import { UPLOAD_CONFIG } from "@/shared/constants/cabinetMedicalConfig.ts";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";

/**
 * Interface pour les propriétés du composant CabinetImagesCRUDSection
 */
interface CabinetImagesCRUDSectionProps {
  /** Liste des images du cabinet */
  images: Photo[];
  /** Fonction pour ajouter des images */
  onAddImages: (files: File) => Promise<boolean>;
  /** Fonction pour supprimer une image */
  onRemoveImage: (imageId: number) => Promise<boolean>;
  /** Nombre maximum d'images autorisées */
  maxImages?: number;
}

/**
 * Composant de galerie d'images du cabinet avec CRUD complet
 *
 * @description Ce composant permet la gestion complète des images du cabinet :
 * ajout, suppression avec une interface intuitive utilisant react-hook-form.
 * Il gère la validation des fichiers, les limites de taille et le nombre maximum.
 */
const CabinetImagesCRUDSection: React.FC<CabinetImagesCRUDSectionProps> = ({
  images,
  onAddImages,
  onRemoveImage,
  maxImages = UPLOAD_CONFIG.MAX_CABINET_IMAGES,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  
  // États pour la modale de confirmation de suppression
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    register,
    formState: { errors },
    save,
    cancel,
    isSaving,
    setValue,
    watch,
  } = useSectionForm({
    schema: cabinetImagesSchema,
    defaultValues: { images: [] },
    onSave: async (data) => {
      if (selectedFiles.length > 0) {
        let success = true;
        for (const file of selectedFiles) {
          const uploadResult = await onAddImages(file);
          if (!uploadResult) {
            success = false;
          }
        }
        if (success) {
          setSelectedFiles([]);
          setValue("images", []);
        }
        return success;
      }
      return true;
    },
    sectionName: "images du cabinet",
  });

  const canAddMore = images.length < maxImages;

  /**
   * Valide un fichier image
   */
  const validateFile = (file: File): string | null => {
    // Vérifier le type de fichier
    if (!file.type.startsWith("image/")) {
      return `${file.name}: Type de fichier non supporté. Utilisez JPG, PNG ou WebP.`;
    }

    // Vérifier la taille
    const maxSizeBytes = UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return `${file.name}: Taille trop importante (${(file.size / 1024 / 1024).toFixed(1)}MB). Maximum ${UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB.`;
    }

    return null;
  };

  /**
   * Gère la sélection de fichiers
   */
  const handleFileSelection = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);
      const errors: string[] = [];
      const validFiles: File[] = [];

      // Vérifier le nombre total d'images
      if (images.length + files.length > maxImages) {
        errors.push(
          `Vous ne pouvez ajouter que ${maxImages - images.length} image(s) supplémentaire(s).`
        );
        setUploadErrors(errors);
        return;
      }

      // Valider chaque fichier
      files.forEach((file) => {
        const error = validateFile(file);
        if (error) {
          errors.push(error);
        } else {
          validFiles.push(file);
        }
      });

      setUploadErrors(errors);
      setSelectedFiles(validFiles);
      setValue("images", validFiles);

      // Réinitialiser l'input
      event.target.value = "";
    },
    [images.length, maxImages, setValue]
  );

  /**
   * Ouvre la modale de confirmation pour la suppression d'une image
   * @param {number} imageId - ID de l'image à supprimer
   */
  const handleRemoveImageRequest = (imageId: number) => {
    setImageToDelete(imageId);
    setShowDeleteModal(true);
  };
  
  /**
   * Supprime une image après confirmation via la modale
   */
  const handleConfirmImageDeletion = async () => {
    if (imageToDelete !== null) {
      setIsDeleting(true);
      try {
        await onRemoveImage(imageToDelete);
      } finally {
        setIsDeleting(false);
        setShowDeleteModal(false);
        setImageToDelete(null);
      }
    }
  };

  /**
   * Supprime un fichier sélectionné (avant upload)
   */
  const removeSelectedFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
    setValue("images", newFiles);
  };

  return (
    <>
      <EditableSection
        title="Images du cabinet"
        icon={Camera}
        onSave={save}
        onCancel={() => {
          cancel();
          setSelectedFiles([]);
          setUploadErrors([]);
        }}
        isSaving={isSaving}
        editContent={
          <div className="space-y-6">
            {/* Images existantes */}
            {images.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-3">
                  Images actuelles ({images.length}/{maxImages})
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {images.map((image) => (
                    <div
                      key={image.id}
                      className="relative group aspect-square bg-gray-100 rounded-lg overflow-hidden"
                    >
                      <img
                        src={image.path}
                        alt={`Image du cabinet ${image.id}`}
                        className="w-full h-full object-cover"
                      />
                      <button
                        onClick={() => handleRemoveImageRequest(image.id)}
                        className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Zone d'upload */}
            {canAddMore && (
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-3">
                  Ajouter de nouvelles images
                </h3>

                {/* Zone de drop */}
                <label className="block w-full p-8 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-meddoc-primary hover:bg-meddoc-primary/5 transition-colors">
                  <div className="text-center">
                    <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p className="text-lg font-medium text-gray-700 mb-2">
                      Cliquez pour sélectionner des images
                    </p>
                    <p className="text-sm text-gray-500 mb-4">
                      ou glissez-déposez vos fichiers ici
                    </p>
                    <div className="text-xs text-gray-400">
                      <p>• Formats acceptés : JPG, PNG, WebP</p>
                      <p>
                        • Taille maximale : {UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB
                        par image
                      </p>
                      <p>
                        • Maximum {maxImages - images.length} image(s)
                        supplémentaire(s)
                      </p>
                    </div>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleFileSelection}
                    className="hidden"
                  />
                </label>
              </div>
            )}

            {/* Aperçu des fichiers sélectionnés */}
            {selectedFiles.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-3">
                  Images sélectionnées ({selectedFiles.length})
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {selectedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="relative group aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-green-200"
                    >
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`Aperçu ${file.name}`}
                        className="w-full h-full object-cover"
                      />
                      <button
                        onClick={() => removeSelectedFile(index)}
                        className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                      >
                        <X size={16} />
                      </button>
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-2">
                        {file.name}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Erreurs d'upload */}
            {uploadErrors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-red-800 mb-2">
                      Erreurs de validation :
                    </h4>
                    <ul className="text-sm text-red-700 space-y-1">
                      {uploadErrors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        }
      >
        {/* Contenu en mode lecture */}
        <div>
          {images.length > 0 ? (
            <div>
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm text-gray-500">
                  {images.length}/{maxImages} images
                </span>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {images.map((image) => (
                  <div
                    key={image.id}
                    className="aspect-square bg-gray-100 rounded-lg overflow-hidden"
                  >
                    <img
                      src={image.path}
                      alt={`Image du cabinet ${image.id}`}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                    />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <Image className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <p className="text-gray-500 mb-2">
                Aucune image du cabinet disponible
              </p>
              <p className="text-sm text-gray-400">
                Cliquez sur l'icône d'édition pour ajouter des photos de votre
                cabinet
              </p>
            </div>
          )}
        </div>
      </EditableSection>

      {/* Modale de confirmation pour la suppression d'image */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => {
          if (!isDeleting) {
            setShowDeleteModal(false);
            setImageToDelete(null);
          }
        }}
        onConfirm={handleConfirmImageDeletion}
        title="Confirmer la suppression"
        message="Êtes-vous sûr de vouloir supprimer cette image ? Cette action est irréversible."
        confirmButtonText="Supprimer"
        cancelButtonText="Annuler"
        loading={isDeleting}
      />
    </>
  );
};

export default CabinetImagesCRUDSection;
