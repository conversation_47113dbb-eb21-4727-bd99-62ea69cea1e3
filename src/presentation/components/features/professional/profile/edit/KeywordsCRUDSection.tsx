import React, { useEffect, useMemo, useState } from "react";
import { Hash, X } from "lucide-react";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import { MotClesProfessionnel } from "@/domain/models";
import { z } from "zod";
import MultiSelect, {
  SelectableItem,
} from "@/presentation/components/common/MultiSelect.tsx";
import { useKeywordsSection } from "@/presentation/hooks/sections/use-keywords-section.ts";
import ConfirmActionModal from "@/presentation/components/common/Modal/ConfirmActionModal.tsx";
import { useToast } from "@/presentation/hooks/use-toast";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";

/**
 * Interface pour les propriétés du composant KeywordsCRUDSection
 */
interface KeywordsCRUDSectionProps {
  /** Liste des mots-clés du professionnel */
  keywords: MotClesProfessionnel[];
  /** Fonction pour ajouter des mots-clés */
  onAddKeywords: (keywordIds: number[]) => Promise<boolean>;
  /** Fonction pour supprimer un mot-clé (avec toast) */
  onDeleteKeyword: (id: number) => Promise<boolean>;
  /** Fonction pour supprimer un mot-clé silencieusement (sans toast) */
  onDeleteKeywordSilent?: (id: number) => Promise<boolean>;
  /** Fonction pour rafraîchir les données après suppressions multiples */
  onRefreshData?: () => Promise<void>;
}

/**
 * Interface pour les données des mots-clés
 */
interface KeywordsData {
  newKeywords: number[];
}

/**
 * Composant de section mots-clés avec opérations CRUD complètes
 *
 * @description
 * - Affiche les mots-clés existants du professionnel
 * - Permet l'ajout via MultiSelect (IDs de la table liste des mots-clés)
 * - La suppression est différée: clic sur l'icône = retrait visuel + marquage,
 *   l'appel API effectif est réalisé uniquement au clic sur "Sauvegarder" (avec confirmation)
 * - Le parent rafraîchit les données au succès pour garder l'UI à jour
 */
const KeywordsCRUDSection: React.FC<KeywordsCRUDSectionProps> = ({
  keywords,
  onAddKeywords,
  onDeleteKeyword,
  onDeleteKeywordSilent,
  onRefreshData,
}) => {
  const { keywords: availableKeywords } = useKeywordsSection();
  const toast = useToast();

  // Sélection locale des nouveaux mots-clés à ajouter (provenant de la liste maître)
  const [selectedNewKeywords, setSelectedNewKeywords] = useState<
    SelectableItem[]
  >([]);

  /**
   * États locaux pour la gestion des mots-clés
   *
   * Pattern de suppression différée:
   * 1. L'utilisateur clique sur l'icône de suppression d'un mot-clé
   * 2. Le mot-clé est retiré visuellement de la liste principale (displayedKeywords)
   * 3. Il est ajouté à la liste des suppressions en attente (keywordsToDelete)
   * 4. Son ID est stocké dans pendingDeletionIds pour traitement ultérieur
   * 5. L'utilisateur peut annuler la suppression avant de sauvegarder
   * 6. Au moment de la sauvegarde, une confirmation est demandée
   * 7. Les suppressions sont effectuées uniquement après confirmation
   */
  const [displayedKeywords, setDisplayedKeywords] =
    useState<MotClesProfessionnel[]>(keywords);
  const [pendingDeletionIds, setPendingDeletionIds] = useState<Set<number>>(
    new Set()
  );
  const [keywordsToDelete, setKeywordsToDelete] = useState<
    MotClesProfessionnel[]
  >([]);
  const [tempIdCounter, setTempIdCounter] = useState<number>(-1);

  // État pour la modale de confirmation
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  /**
   * Synchronise l'affichage lorsque les props changent (après sauvegarde)
   * Réinitialise également les listes de suppressions en attente
   */
  useEffect(() => {
    setDisplayedKeywords(keywords);
    setPendingDeletionIds(new Set());
    setKeywordsToDelete([]);
  }, [keywords]);

  // Options d'ajout: exclure celles déjà affichées (doublon par symptôme)
  const availableKeywordsForSelection = useMemo(() => {
    const assignedSymptoms = new Set(
      displayedKeywords.map((k) => (k.symptome || "").toLowerCase())
    );
    return availableKeywords.filter(
      (item) => !assignedSymptoms.has((item.symptome || "").toLowerCase())
    );
  }, [availableKeywords, displayedKeywords]);

  // Gestion de l'état d'édition avec le hook spécialisé
  const {
    register,
    formState: { errors },
    save,
    cancel,
    isSaving,
    setValue,
    watch,
  } = useSectionForm({
    schema: z.object({
      newKeywords: z.array(z.number().int().positive()).default([]),
    }),
    defaultValues: {
      newKeywords: [],
    },
    onSave: async (data) => {
      let overallSuccess = true;

      // Ajout des nouveaux mots-clés sélectionnés
      if (data.newKeywords && data.newKeywords.length > 0) {
        const addOk = await onAddKeywords(data.newKeywords);
        overallSuccess = overallSuccess && addOk;
        if (addOk) {
          // Mise à jour optimiste de l'affichage en ajoutant les éléments sélectionnés
          setDisplayedKeywords((prev) => {
            const additions: MotClesProfessionnel[] = selectedNewKeywords.map(
              (item, index) => ({
                id: tempIdCounter - index,
                symptome: String(
                  (item as { symptome?: string }).symptome || ""
                ),
                id_professionnel: 0,
              })
            );
            return [...prev, ...additions];
          });
          setTempIdCounter((c) => c - selectedNewKeywords.length);
          setValue("newKeywords", []);
          setSelectedNewKeywords([]);
        }
      }

      // Suppressions différées avec confirmation au moment de sauvegarder
      if (pendingDeletionIds.size > 0) {
        setShowConfirmModal(true);
        return false; // Arrêter la sauvegarde pour attendre la confirmation
      }

      return overallSuccess;
    },
    sectionName: "mots-clés",
  });

  /**
   * Marque un mot-clé pour suppression (sans appel API immédiat)
   * - Retire le mot-clé de l'affichage principal
   * - Ajoute son ID à la liste des suppressions en attente
   * - Conserve l'objet complet pour permettre l'annulation ultérieure
   *
   * @param {number} keywordId - ID du mot-clé à marquer pour suppression
   */
  const markKeywordForDeletion = (keywordId: number) => {
    const keywordToDelete = displayedKeywords.find((k) => k.id === keywordId);
    if (keywordToDelete) {
      setKeywordsToDelete((prev) => [...prev, keywordToDelete]);
    }

    setDisplayedKeywords((prev) => prev.filter((k) => k.id !== keywordId));
    setPendingDeletionIds((prev) => new Set(prev).add(keywordId));
  };

  /**
   * Annule le marquage d'un mot-clé pour suppression
   * - Remet le mot-clé dans la liste d'affichage principale
   * - Retire son ID de la liste des suppressions en attente
   * - Retire l'objet de la liste des mots-clés à supprimer
   *
   * @param {MotClesProfessionnel} keyword - Objet mot-clé complet à restaurer
   */
  const unmarkKeywordForDeletion = (keyword: MotClesProfessionnel) => {
    // Retirer de la liste des mots-clés à supprimer
    setKeywordsToDelete((prev) => prev.filter((k) => k.id !== keyword.id));

    // Remettre dans la liste d'affichage principale
    setDisplayedKeywords((prev) => [...prev, keyword]);

    // Retirer l'ID de la liste des suppressions en attente
    const updatedPendingDeletionIds = new Set(pendingDeletionIds);
    updatedPendingDeletionIds.delete(keyword.id);
    setPendingDeletionIds(updatedPendingDeletionIds);
  };

  /**
   * Gère la confirmation de suppression des mots-clés
   * - Gère les suppressions multiples avec la fonction silencieuse si disponible
   * - Affiche un toast unique pour les suppressions multiples
   * - Réinitialise les états après les suppressions
   */
  const handleConfirmDeletion = async (): Promise<void> => {
    setIsDeleting(true);
    let overallSuccess = true;
    const deletedCount = pendingDeletionIds.size;

    // Pour les suppressions multiples, utiliser la fonction silencieuse
    if (deletedCount > 1 && onDeleteKeywordSilent) {
      for (const id of pendingDeletionIds) {
        try {
          const delOk = await onDeleteKeywordSilent(id);
          overallSuccess = overallSuccess && delOk;
        } catch (e) {
          overallSuccess = false;
        }
      }

      if (overallSuccess) {
        setPendingDeletionIds(new Set());
        setKeywordsToDelete([]);
        // Toast unique pour les suppressions multiples
        toast.success(`${deletedCount} mot(s)-clé(s) supprimé(s) avec succès`);
        // Rafraîchir les données une seule fois après toutes les suppressions
        if (onRefreshData) {
          await onRefreshData();
        }
      }
    } else {
      // Pour une suppression unique, utiliser la fonction normale (avec toast)
      for (const id of pendingDeletionIds) {
        try {
          const delOk = await onDeleteKeyword(id);
          overallSuccess = overallSuccess && delOk;
        } catch (e) {
          overallSuccess = false;
        }
      }

      if (overallSuccess) {
        setPendingDeletionIds(new Set());
        setKeywordsToDelete([]);
      }
    }

    setIsDeleting(false);
    setShowConfirmModal(false);
  };

  // Gérer les changements de sélection dans MultiSelect
  const handleKeywordsSelectionChange = (items: SelectableItem[]) => {
    setSelectedNewKeywords(items);
    setValue(
      "newKeywords",
      items.map((item) => Number(item.id))
    );
  };

  return (
    <>
      <EditableSection
        title="Mots-clés / Symptômes"
        icon={Hash}
        onSave={save}
        onCancel={cancel}
        isSaving={isSaving}
        editContent={
          <div className="space-y-4">
            {/* Liste des mots-clés existants */}
            <div className="space-y-2">
              {displayedKeywords.map((keyword) => (
                <div
                  key={keyword.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                >
                  <span className="text-gray-700">{keyword.symptome}</span>
                  <button
                    onClick={() => markKeywordForDeletion(keyword.id)}
                    className="p-1 text-red-600 hover:bg-red-100 rounded"
                    title="Supprimer"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
              {displayedKeywords.length === 0 &&
                keywordsToDelete.length === 0 && (
                  <p className="text-gray-500 text-sm italic">
                    Aucun mot-clé ajouté
                  </p>
                )}
            </div>

            {/* Liste des mots-clés marqués pour suppression */}
            {keywordsToDelete.length > 0 && (
              <div className="space-y-2 bg-red-50 p-4 rounded mb-4">
                <h2 className="text-lg font-semibold mb-2 text-red-700">
                  Mots-clés à supprimer ({keywordsToDelete.length})
                </h2>
                <p className="text-sm text-red-600 mb-2">
                  Ces mots-clés seront supprimés lors de la sauvegarde. Cliquez
                  sur <span className="font-medium">Restaurer</span> pour
                  annuler la suppression.
                </p>
                {keywordsToDelete.map((keyword) => (
                  <div
                    key={keyword.id}
                    className="flex items-center justify-between p-3 bg-white rounded-lg border border-red-200"
                  >
                    <span className="font-medium text-red-700">
                      {keyword.symptome}
                    </span>
                    <button
                      onClick={() => unmarkKeywordForDeletion(keyword)}
                      className="px-2 py-1 text-blue-600 hover:bg-blue-100 rounded transition-colors text-xs font-medium"
                      title="Annuler la suppression"
                    >
                      Restaurer
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Ajout de nouveaux mots-clés via MultiSelect */}
            <div className="border-t pt-4">
              <MultiSelect
                itemsList={availableKeywordsForSelection}
                selectedItems={selectedNewKeywords}
                handleItemsChange={handleKeywordsSelectionChange}
                register={register}
                fieldName="newKeywords"
                displayProperty="symptome"
                dropdownId="keywords-dropdown"
                placeholderText="Sélectionner des mots-clés"
                selectedText={(count) =>
                  `${count} mot(s)-clé(s) sélectionné(s)`
                }
                allSelectedText="Tous les mots-clés sélectionnés"
                selectedItemsTitle="Mots-clés sélectionnés"
                error={errors.newKeywords}
                sortAlphabetically={true}
              />
            </div>
          </div>
        }
      >
        {/* Contenu en mode lecture */}
        <div>
          {keywords.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {keywords.map((keyword) => (
                <span
                  key={keyword.id}
                  className="inline-flex items-center px-3 py-1 bg-meddoc-primary/10 text-meddoc-primary rounded-full text-sm"
                >
                  {keyword.symptome}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-gray-400 italic">
              Aucun mot-clé renseigné. Cliquez sur l'icône d'édition pour
              ajouter des mots-clés.
            </p>
          )}
        </div>
      </EditableSection>

      {/* Modale de confirmation pour les suppressions */}
      <ConfirmationModal
        isOpen={showConfirmModal}
        message="Voulez-vous vraiment supprimer ce mot-clé ?"
        title="Confirmer la suppression"
        confirmButtonText="Supprimer"
        cancelButtonText="Annuler"
        loading={isDeleting}
        onConfirm={handleConfirmDeletion}
        onClose={() => {
          if (!isDeleting) {
            setShowConfirmModal(false);
          }
        }}
      />
    </>
  );
};

export default KeywordsCRUDSection;
