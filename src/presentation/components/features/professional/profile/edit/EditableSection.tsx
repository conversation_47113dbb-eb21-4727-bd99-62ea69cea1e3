import React, { useState, ReactNode } from "react";
import { Edit3, Save, X, LucideIcon } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

/**
 * Interface pour les propriétés du composant EditableSection
 */
interface EditableSectionProps {
  /** Titre de la section */
  title: string;
  /** Icône de la section */
  icon: LucideIcon;
  /** Contenu en mode lecture */
  children: ReactNode;
  /** Contenu en mode édition */
  editContent: ReactNode;
  /** Fonction appelée lors de la sauvegarde */
  onSave: (event: React.FormEvent<HTMLFormElement>) => Promise<void>;
  /** Fonction appelée lors de l'annulation */
  onCancel?: () => void;
  /** Indique si la section est en cours de sauvegarde */
  isSaving?: boolean;
  /** Classe CSS personnalisée */
  className?: string;
}

/**
 * Composant wrapper pour les sections éditables
 *
 * @description Ce composant encapsule la logique d'édition par section,
 * gérant l'état d'édition, les boutons de contrôle et les animations.
 * Il fournit une interface cohérente pour toutes les sections du profil.
 *
 * @example
 * ```tsx
 * <EditableSection
 *   title="Présentation"
 *   icon={FileText}
 *   onSave={handleSavePresentation}
 *   editContent={
 *     <textarea
 *       value={presentation}
 *       onChange={(e) => setPresentation(e.target.value)}
 *     />
 *   }
 * >
 *   <p>{presentation}</p>
 * </EditableSection>
 * ```
 */
const EditableSection: React.FC<EditableSectionProps> = ({
  title,
  icon: Icon,
  children,
  editContent,
  onSave,
  onCancel,
  isSaving = false,
  className = "",
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  /**
   * Active le mode édition
   */
  const handleEdit = () => {
    setIsEditing(true);
    setHasChanges(false);
  };

  /**
   * Sauvegarde les modifications
   */
  const handleSave = async (event: React.FormEvent<HTMLFormElement>) => {
    try {
      event.preventDefault();
      await onSave(event);
      // Seulement passer en mode lecture si la sauvegarde réussit
      setIsEditing(false);
      setHasChanges(false);
    } catch (error) {
      console.error("Erreur lors de la sauvegarde:", error);
      // Ne pas passer en mode lecture si la sauvegarde échoue
      // Cela permet à l'utilisateur de corriger les erreurs de validation
      // L'erreur est gérée par le hook useErrorHandler dans onSave
    }
  };

  /**
   * Annule les modifications
   */
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    setIsEditing(false);
    setHasChanges(false);
  };

  return (
    <section
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 ${className}`}
    >
      {/* En-tête de la section */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-meddoc-primary/10 dark:bg-meddoc-primary/20 rounded-lg">
            <Icon className="h-5 w-5 text-meddoc-primary" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            {title}
          </h2>
        </div>

        {/* Boutons de contrôle */}
        <div className="flex items-center gap-2">
          {!isEditing ? (
            <button
              onClick={handleEdit}
              className="p-2 text-gray-500 dark:text-gray-400 hover:text-meddoc-primary hover:bg-meddoc-primary/10 dark:hover:bg-meddoc-primary/20 rounded-lg transition-colors"
              title={`Modifier ${title.toLowerCase()}`}
            >
              <Edit3 className="h-4 w-4" />
            </button>
          ) : (
            <div className="flex items-center gap-2">
              <button
                onClick={handleCancel}
                disabled={isSaving}
                className="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50"
                title="Annuler"
              >
                <X className="h-4 w-4" />
              </button>
              <button
                type="submit"
                form="contact-form"
                disabled={isSaving}
                className="px-3 py-1.5 text-sm bg-meddoc-primary text-white hover:bg-meddoc-primary/90 rounded-lg transition-colors disabled:opacity-50 flex items-center gap-2"
                title="Sauvegarder"
              >
                <Save className="h-4 w-4" />
                {isSaving ? "Sauvegarde..." : "Sauvegarder"}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Contenu de la section */}
      <div className="p-4">
        <AnimatePresence mode="wait">
          {isEditing ? (
            <motion.div
              key="edit"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <form id="contact-form" onSubmit={handleSave}>
                {editContent}
              </form>
            </motion.div>
          ) : (
            <motion.div
              key="view"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {children}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default EditableSection;
