import React from "react";
import { motion } from "framer-motion";
import { User } from "lucide-react";
import { professionnels_titre_enum } from "@/domain/models/enums";
import FormField from "@/presentation/components/common/ui/FormField.tsx";
import { BaseInfoSectionData } from "@/presentation/hooks/professional/use-professional-base-info-section-handler.ts";
import { UseFormRegister } from "react-hook-form";

/**
 * Props pour le composant BaseInfoEditForm
 */
interface BaseInfoEditFormProps {
  register: UseFormRegister<{
    titre?: professionnels_titre_enum;
    nom?: string;
    prenom?: string;
  }>;
}

/**
 * Composant de formulaire d'édition des informations de base du professionnel
 *
 * @description Ce composant permet l'édition inline des informations personnelles
 * de base d'un professionnel (titre, nom, prénom) avec validation et animations.
 *
 * @features
 * - Édition inline avec validation en temps réel
 * - Animations fluides avec Framer Motion
 * - Gestion des états de chargement
 * - Validation des champs obligatoires
 * - Interface utilisateur cohérente avec le design system
 *
 * @example
 * ```tsx
 * <BaseInfoEditForm
 *   titre={professionnels_titre_enum.DR}
 *   nom="Dupont"
 *   prenom="Marie"
 *   onSave={handleSave}
 *   onCancel={handleCancel}
 *   isLoading={false}
 * />
 * ```
 */
const BaseInfoEditForm: React.FC<BaseInfoEditFormProps> = ({ register }) => {
  return (
    <>
      {/* Titre */}
      <FormField
        id="titre"
        label="Titre"
        type="select"
        placeholder="Sélectionnez un titre"
        icon={User}
        register={register}
        options={[
          { value: professionnels_titre_enum.PR, label: "Professeur" },
          { value: professionnels_titre_enum.DR, label: "Docteur" },
        ]}
      />

      {/* Nom */}
      <FormField
        id="nom"
        label="Nom"
        type="text"
        placeholder="Nom de famille"
        icon={User}
        register={register}
        required
      />

      {/* Prénom */}
      <FormField
        id="prenom"
        label="Prénom"
        type="text"
        placeholder="Prénom"
        icon={User}
        register={register}
        required
      />
    </>
  );
};

export default BaseInfoEditForm;
