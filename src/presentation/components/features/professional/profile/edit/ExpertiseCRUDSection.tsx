import React, { useEffect, useMemo, useState } from "react";
import { Stethoscope, X } from "lucide-react";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import { SpecialiteProfessionnel } from "@/domain/models/SpecialiteProfessionnel.ts";
import { z } from "zod";
import MultiSelect, {
  SelectableItem,
} from "@/presentation/components/common/MultiSelect.tsx";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";
import { ListeExpertises } from "@/domain/models/ListeExpertises";

/**
 * Interface pour les propriétés du composant SpecialtiesCRUDSection
 */
interface ExpertiseCRUDSectionProps {
  /** Liste des expertise du professionnel */
  expertises: SpecialiteProfessionnel[];
  /** Liste des expertise disponibles */
  availableExpertisesLists: ListeExpertises[];
  /** Fonction pour ajouter une expertise*/
  onAddExpertise: (specialityIds: number[]) => Promise<boolean>;
  /** Fonction pour supprimer une expertise*/
  onDeleteExpertise: (id: number) => Promise<boolean>;
}

/**
 * Composant de section expertise avec opérations CRUD complètes
 *
 * @description
 * - Affiche les expertise existantes du professionnel
 * - Permet l’ajout via MultiSelect (IDs de la table liste des expertise)
 * - La suppression est différée: clic sur l’icône = retrait visuel + marquage,
 *   l’appel API effectif est réalisé uniquement au clic sur "Sauvegarder" (avec confirmation)
 * - Le parent rafraîchit les données au succès pour garder l’UI à jour
 */
const ExpertiseCRUDSection: React.FC<ExpertiseCRUDSectionProps> = ({
  expertises,
  availableExpertisesLists: availableSpecialitiesLists,
  onAddExpertise: onAddSpecialty,
  onDeleteExpertise: onDeleteSpecialty,
}) => {
  // Sélection locale des nouvelles expertise à ajouter (provenant de la liste maître)
  const [selectedNewSpecialities, setSelectedNewSpecialities] = useState<
    SelectableItem[]
  >([]);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [specialitiesToDelete, setSpecialitiesToDelete] = useState<
    SpecialiteProfessionnel[]
  >([]);

  // Liste affichée localement et suppressions différées
  const [displayedSpecialties, setDisplayedSpecialties] =
    useState<SpecialiteProfessionnel[]>(expertises);
  const [pendingDeletionIds, setPendingDeletionIds] = useState<Set<number>>(
    new Set()
  );
  const [tempIdCounter, setTempIdCounter] = useState<number>(-1);

  // Synchroniser l'affichage lorsque les props changent (après sauvegarde)
  useEffect(() => {
    setDisplayedSpecialties(expertises);
    setPendingDeletionIds(new Set());
  }, [expertises]);

  // Options d’ajout: exclure celles déjà affichées (doublon par id)
  const availableSpecialities = useMemo(() => {
    const assignedIds = displayedSpecialties.map((s) => s.id)

    return availableSpecialitiesLists.filter(
      (item) => !assignedIds.includes(item.id));
  }, [availableSpecialitiesLists, displayedSpecialties]);

  // Gestion de l'état d'édition avec le hook spécialisé
  const {
    register,
    formState: { errors },
    save,
    isSaving,
    setValue,
  } = useSectionForm({
    schema: z.object({
      newExpertise: z.array(z.number().int().positive()).default([]),
    }),
    defaultValues: {
      newExpertise: [],
    },
    onSave: async (data) => {
      let overallSuccess = true;

      // Ajout des nouvelles expertise sélectionnées
      if (data.newExpertise && data.newExpertise.length > 0) {
        const addOk = await onAddSpecialty(data.newExpertise);
        overallSuccess = overallSuccess && addOk;
        if (addOk) {
          // Mise à jour optimiste de l'affichage en ajoutant les éléments sélectionnés
          setDisplayedSpecialties((prev) => {
            const additions: SpecialiteProfessionnel[] =
              selectedNewSpecialities.map((item, index) => ({
                id: tempIdCounter - index,
                nom_specialite: String(
                  (item as { nom_specialite?: string }).nom_specialite || ""
                ),
                id_professionnel: 0,
              }));
            return [...prev, ...additions];
          });
          setTempIdCounter((c) => c - selectedNewSpecialities.length);
          setValue("newExpertise", []);
          setSelectedNewSpecialities([]);
        }
      }

      // Suppressions différées avec confirmation au moment de sauvegarder
      if (pendingDeletionIds.size > 0) {
        setIsConfirmModalOpen(true);
        // La suppression effective sera gérée par le gestionnaire onConfirm de la modal
        // On retourne true pour indiquer que la sauvegarde est réussie, même si la confirmation est en attente
        return overallSuccess;
      }

      return overallSuccess;
    },
    sectionName: "expertise",
  });

  // Callback de fermeture du modal
  const handleConfirmModalClose = () => {
    setIsConfirmModalOpen(false);
    // Remettre les expertise supprimées dans la liste des expertise existantes
    displayedSpecialties.push(...specialitiesToDelete);
    setSpecialitiesToDelete([]);
    setPendingDeletionIds(new Set());
  };

  // Marquer une expertise pour suppression (sans appel API immédiat)
  const markSpecialtyForDeletion = (specialtyId: number) => {
    const specialtyToDelete = displayedSpecialties.find(
      (s) => s.id === specialtyId
    );
    if (specialtyToDelete) {
      setSpecialitiesToDelete((prev) => [...prev, specialtyToDelete]);
    }
    setDisplayedSpecialties((prev) => prev.filter((s) => s.id !== specialtyId));
    setPendingDeletionIds((prev) => new Set(prev).add(specialtyId));
  };

  // Remettre les expertise supprimées dans la liste des expertise existantes
  const unmarkSpecialtyForDeletion = (specialty: SpecialiteProfessionnel) => {
    setSpecialitiesToDelete((prev) =>
      prev.filter((s) => s.id !== specialty.id)
    );
    const updatedSpecialties = [...displayedSpecialties, specialty];
    setDisplayedSpecialties(updatedSpecialties);

    const updatedPendingDeletionIds = new Set(pendingDeletionIds);
    updatedPendingDeletionIds.delete(specialty.id);
    setPendingDeletionIds(updatedPendingDeletionIds);
  };

  return (
    <>
      <EditableSection
        title="Expertises professionnels"
        icon={Stethoscope}
        onSave={save}
        onCancel={handleConfirmModalClose}
        isSaving={isSaving}
        editContent={
          <div className="space-y-4">
            {/* Liste des expertise existantes */}
            <div className="space-y-2">
              {displayedSpecialties.map((specialty) => (
                <div
                  key={specialty.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                >
                  <>
                    <span className="text-gray-700">
                      {specialty.nom_specialite}
                    </span>
                    <div className="flex items-center gap-1">
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          markSpecialtyForDeletion(specialty.id);
                        }}
                        className="p-1 text-red-600 hover:bg-red-100 rounded"
                        title="Supprimer"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </>
                </div>
              ))}
            </div>

            {/* Ajout d'une nouvelle expertise*/}
            <div className="border-t pt-4">
              <h2 className="text-lg font-semibold mb-2">
                Ajouter une expertise
              </h2>
              <div className="flex flex-col gap-2 w-full">
                <MultiSelect
                  itemsList={availableSpecialities}
                  selectedItems={selectedNewSpecialities}
                  handleItemsChange={(items) => {
                    setSelectedNewSpecialities(items);
                    setValue(
                      "newExpertise",
                      items.map((item) => Number(item.id))
                    );
                  }}
                  register={register}
                  fieldName="newSpecialty"
                  displayProperty="nom_specialite"
                  dropdownId="specialtiesList"
                  placeholderText="Sélectionnez une ou plusieurs expertises"
                  selectedText={(count) =>
                    `${count} expertise(s) sélectionnée(s)`
                  }
                  sortAlphabetically={true}
                  allSelectedText="Toutes les expertises ont été sélectionnées"
                  selectedItemsTitle="Expertises sélectionnées:"
                  error={errors.newExpertise}
                />
              </div>
            </div>

            {specialitiesToDelete.length > 0 && (
              <div className="space-y-2 bg-red-100 p-4 rounded">
                <h2 className="text-lg font-semibold mb-2">
                  Supprimer les expertises suivantes ?
                </h2>
                {specialitiesToDelete.map((specialty) => (
                  <div
                    key={specialty.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                  >
                    <>
                      <span className="text-gray-700">
                        {specialty.nom_specialite}
                      </span>
                      <div className="flex items-center gap-1">
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            unmarkSpecialtyForDeletion(specialty);
                          }}
                          className="p-1 text-red-600 hover:bg-red-100 rounded"
                          title="Supprimer"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    </>
                  </div>
                ))}
              </div>
            )}
          </div>
        }
      >
        {/* Contenu en mode lecture */}
        <div>
          {expertises.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {expertises.map((specialty) => (
                <span
                  key={specialty.id}
                  className="inline-flex items-center px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200"
                >
                  {specialty.nom_specialite}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-gray-400 italic">
              Aucune expertise renseignée. Cliquez sur l'icône d'édition pour
              ajouter des expertises.
            </p>
          )}
        </div>
      </EditableSection>

      <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={async () => {
          let success = true;
          // Exécuter les suppressions après confirmation
          for (const id of pendingDeletionIds) {
            try {
              const delOk = await onDeleteSpecialty(id);
              success = success && delOk;
            } catch (e) {
              success = false;
            }
          }
          // Réinitialiser la liste des suppressions en attente
          setPendingDeletionIds(new Set());
          setIsConfirmModalOpen(false);
          // Finaliser la sauvegarde
          return success;
        }}
        message={`Voulez-vous vraiment supprimer ${pendingDeletionIds.size} expertise(s) ?`}
        title="Supprimer les expertise sélectionnées"
        confirmButtonText="Supprimer"
        cancelButtonText="Annuler"
      />
    </>
  );
};

export default ExpertiseCRUDSection;
