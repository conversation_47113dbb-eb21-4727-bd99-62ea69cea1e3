import React, { useCallback, useState } from "react";
import { FileText, Plus } from "lucide-react";
import { AnimatePresence, motion } from "framer-motion";
import Button from "@/presentation/components/common/Button/Button.tsx";
import ConfirmActionModal from "@/presentation/components/common/Modal/ConfirmActionModal.tsx";
import { useToast } from "@/presentation/hooks/use-toast.ts";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel.ts";
import { PublicationFormData } from "@/shared/schemas/ProfessionalProfileSchemas";
import PublicationsList from "./publications/PublicationsList";
import PublicationAddForm from "./publications/PublicationAddForm";
import useProfessionalProfilePublication from "./publications/use-professional-profile-publication";

interface PublicationsCRUDSectionProps {
  /** Liste des publications du professionnel */
  publications: PublicationProfessionnel[];
  /** Fonction pour ajouter une publication (retourne entités créées) */
  onAddPublication: (publication: PublicationFormData) => Promise<boolean>;
  /** Fonction pour modifier une publication (retourne entité mise à jour) */
  onUpdatePublication: (
    id: number,
    publication: PublicationFormData
  ) => Promise<boolean>;
  /** Fonction pour supprimer une publication */
  onDeletePublication: (id: number) => Promise<boolean>;
}

/**
 * Section CRUD Publications (pattern Expériences)
 * - CRUD immédiat (submit = API) via `useProfessionalProfilePublication`
 * - Modale de confirmation suppression avec verrouillage pendant le loading
 * - Toasts de feedback succès/erreur
 */
const PublicationsCRUDSection: React.FC<PublicationsCRUDSectionProps> = ({
  publications,
  onAddPublication,
  onUpdatePublication,
  onDeletePublication,
}) => {
  const toast = useToast();
  const [selectedDeleteIds, setSelectedDeleteIds] = useState<number[]>([]);

  const {
    isLoading,
    isAdding,
    setIsAdding,
    editingPublicationId,
    setEditingPublicationId,
    register,
    errors,
    handleSubmit,
    setValue,
    reset,
  } = useProfessionalProfilePublication();

  const startUpdatePublication = useCallback(
    (p: PublicationProfessionnel) => {
      setValue("titre", p.titre);
      setValue("lien", p.lien || "");
      setValue("annee", p.annee);
      setValue("description", p.description || "");
    },
    [setValue]
  );

  return (
    <>
      <section
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700`}
      >
        {/* En-tête de la section */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-meddoc-primary/10 dark:bg-meddoc-primary/20 rounded-lg">
              <FileText className="h-5 w-5 text-meddoc-primary" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Publications et recherches
            </h2>
          </div>
        </div>

        {/* Contenu de la section */}
        <div className="p-4">
          <AnimatePresence mode="wait">
            <motion.div
              key="view"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="flex flex-col gap-4"
            >
              {/* Liste */}
              {publications.length > 0 && (
                <PublicationsList
                  publications={publications}
                  editingPublicationId={editingPublicationId}
                  onStartEdit={(p) => {
                    startUpdatePublication(p);
                    setEditingPublicationId(p.id);
                  }}
                  onApplyEdit={(id) =>
                    handleSubmit(async (data) => {
                      const ok = await onUpdatePublication(id, data);
                      if (ok) {
                        setEditingPublicationId(null);
                        toast.success("Publication mise à jour avec succès.");
                      }
                    })
                  }
                  onCancelEdit={() => setEditingPublicationId(null)}
                  onDelete={(id) => {
                    setSelectedDeleteIds((prev) => [...prev, id]);
                  }}
                  register={register}
                  errors={errors}
                />
              )}

              {/* Formulaire d'ajout */}
              {isAdding ? (
                <PublicationAddForm
                  register={register}
                  errors={errors}
                  onSubmit={handleSubmit(async (data) => {
                    const ok = await onAddPublication(data);
                    if (ok) {
                      setIsAdding(false);
                      toast.success("Publication ajoutée avec succès.");
                      // Vider la formulaire
                      reset();
                    }
                  })}
                  onCancel={() => {
                    reset();
                    setIsAdding(false);
                  }}
                  submitting={isLoading}
                />
              ) : (
                editingPublicationId === null && (
                  <Button
                    type="button"
                    className="ml-auto flex gap-1 items-center"
                    onClick={() => setIsAdding(true)}
                  >
                    <Plus />
                    Ajouter une publication
                  </Button>
                )
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </section>

      {/* Modale de confirmation de suppression */}
      <ConfirmActionModal
        isOpen={selectedDeleteIds.length > 0}
        title="Confirmer la suppression"
        message={`Êtes-vous sûr de vouloir supprimer cette publication ? Cette action est irréversible.`}
        confirmLabel="Supprimer"
        cancelLabel="Annuler"
        loading={isLoading}
        disableCloseOnLoading
        onConfirm={async () => {
          if (selectedDeleteIds.length === 0) return;
          for (const id of selectedDeleteIds) {
            try {
              const delOk = await onDeletePublication(id);
              if (delOk) {
                setSelectedDeleteIds((prev) => prev.filter((i) => i !== id));
              }
            } catch (e) {
              console.error("Erreur lors de la suppression:", e);
            }
          }
          setSelectedDeleteIds([]);
        }}
        onClose={() => {
          if (isLoading) return;
          setSelectedDeleteIds([]);
        }}
      />
    </>
  );
};

export default PublicationsCRUDSection;
