import React, { useCallback, useState } from "react";
import { Briefcase, Plus } from "lucide-react";
import { ExperienceProfessionnel } from "@/domain/models/ExperienceProfessionnel.ts";
import Button from "@/presentation/components/common/Button/Button.tsx";
import ConfirmActionModal from "@/presentation/components/common/Modal/ConfirmActionModal.tsx";
import { useToast } from "@/presentation/hooks/use-toast.ts";
import { AnimatePresence, motion } from "framer-motion";
import ExperiencesList from "./experiences/ExperiencesList";
import ExperienceAddForm from "./experiences/ExperienceAddForm";
import useProfessionalProfileExperience from "./experiences/use-professional-profile-experience";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";

/**
 * Interface pour les propriétés du composant ExperiencesCRUDSection
 */
/**
 * Propriétés de `ExperiencesCRUDSection`
 * - Rend la section liste/ajout/édition des expériences avec CRUD immédiat
 * - UI only: pas de logique métier dans le composant (respect du workflow)
 */
interface ExperiencesCRUDSectionProps {
  /** Liste des expériences du professionnel */
  experiences: ExperienceProfessionnel[];
  /** ID du professionnel */
  professionalId: number;
  /** Fonction de création d'une expérience */
  addExperience: (
    data: Omit<ExperienceProfessionnel, "id" | "id_professionnel">
  ) => Promise<boolean>;
  /** Fonction de mise à jour d'une expérience */
  updateExperience: (
    experienceId: number,
    data: Partial<ExperienceProfessionnel>
  ) => Promise<boolean>;
  /** Fonction de suppression d'une expérience */
  deleteExperience: (experienceId: number) => Promise<boolean>;
}

/**
 * Composant de section expériences avec opérations CRUD complètes
 *
 * @description Ce composant permet la gestion complète des expériences professionnelles :
 * ajout, modification, suppression avec une interface intuitive.
 * Il utilise le système d'édition par section pour une UX cohérente.
 */
/**
 * Section CRUD Expériences (pattern Diplômes)
 * - CRUD immédiat (submit = API) via `useProfessionalProfileExperience`
 * - Modale de confirmation suppression avec verrouillage pendant le loading
 * - Toasts de feedback succès/erreur
 * - Handlers de formulaire typés (React.FormEventHandler)
 */
const ExperiencesCRUDSection: React.FC<ExperiencesCRUDSectionProps> = ({
  experiences,
  professionalId,
  addExperience,
  updateExperience,
  deleteExperience,
}) => {
  const [selectedDeleteId, setSelectedDeleteId] = useState<number | null>(null);
  const toast = useToast();
  const {
    isLoading,
    isAdding,
    setIsAdding,
    editingExperienceId,
    setEditingExperienceId,
    control,
    register,
    errors,
    handleSubmit,
    setValue,
  } = useProfessionalProfileExperience(professionalId);

  const startUpdateExperience = useCallback(
    (data: ExperienceProfessionnel) => {
      setValue("poste", data.poste);
      setValue("etablissement", data.etablissement);
      setValue("date_debut", new Date(data.date_debut));
      setValue("date_fin", data.date_fin ? new Date(data.date_fin) : "");
      setValue("description", data.description);
    },
    [setValue]
  );

  const formatPeriod = (dateDebut: string, dateFin?: string | null) => {
    const start = new Date(dateDebut).getFullYear();
    if (dateFin && dateFin.trim() !== "") {
      const end = new Date(dateFin).getFullYear();
      return `${start} - ${end}`;
    }
    return `${start} - En cours`;
  };

  return (
    <>
      <section
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700`}
      >
        {/* En-tête de la section */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-meddoc-primary/10 dark:bg-meddoc-primary/20 rounded-lg">
              <Briefcase className="h-5 w-5 text-meddoc-primary" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Expériences professionnelles
            </h2>
          </div>
        </div>

        {/* Contenu de la section */}
        <div className="p-4">
          <AnimatePresence mode="wait">
            <motion.div
              key="view"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="flex flex-col gap-4"
            >
              {/* Liste des expériences existantes */}
              {experiences.length > 0 && (
                <ExperiencesList
                  experiences={experiences}
                  editingExperienceId={editingExperienceId}
                  onStartEdit={(exp) => {
                    startUpdateExperience(exp);
                    setEditingExperienceId(exp.id);
                  }}
                  onApplyEdit={(id) =>
                    handleSubmit(async (data) => {
                      const formated: Partial<ExperienceProfessionnel> = {
                        poste: data.poste,
                        etablissement: data.etablissement,
                        date_debut:
                          data.date_debut instanceof Date
                            ? data.date_debut.toISOString()
                            : data.date_debut,
                        date_fin:
                          data.date_fin instanceof Date
                            ? data.date_fin.toISOString()
                            : data.date_fin,
                        description: data.description,
                      };
                      const updatedExperience = await updateExperience(
                        id,
                        formated
                      );
                      if (updatedExperience) {
                        setEditingExperienceId(null);
                        toast.success("Expérience mise à jour avec succès.");
                      }
                    })
                  }
                  onCancelEdit={() => setEditingExperienceId(null)}
                  onDelete={(id) => setSelectedDeleteId(id)}
                  control={control}
                  register={register}
                  errors={errors}
                  formatPeriod={formatPeriod}
                />
              )}
              {/* Formulaire d'ajout */}
              {isAdding ? (
                <ExperienceAddForm
                  register={register}
                  errors={errors}
                  control={control}
                  onSubmit={handleSubmit(async (data) => {
                    const formated: Omit<
                      ExperienceProfessionnel,
                      "id" | "id_professionnel"
                    > = {
                      poste: data.poste,
                      etablissement: data.etablissement,
                      date_debut:
                        data.date_debut instanceof Date
                          ? data.date_debut.toISOString()
                          : data.date_debut,
                      date_fin:
                        data.date_fin instanceof Date
                          ? data.date_fin.toISOString()
                          : data.date_fin,
                      description: data.description,
                    };
                    const added = await addExperience(formated);
                    if (added) {
                      setIsAdding(false);
                      toast.success("Expérience ajoutée avec succès.");
                    }
                  })}
                  onCancel={() => setIsAdding(false)}
                  submitting={isLoading}
                />
              ) : (
                editingExperienceId === null && (
                  <Button
                    type="button"
                    className="ml-auto flex gap-1 items-center"
                    onClick={() => setIsAdding(true)}
                  >
                    <Plus />
                    Ajouter une expérience
                  </Button>
                )
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </section>

      {/* Modale de confirmation de suppression */}
      <ConfirmationModal
        isOpen={typeof selectedDeleteId === "number"}
        title="Confirmer la suppression"
        message={`Êtes-vous sûr de vouloir supprimer cette expérience ? Cette action est irréversible.`}
        confirmButtonText="Supprimer"
        cancelButtonText="Annuler"
        loading={isLoading}
        onConfirm={async () => {
          if (selectedDeleteId == null) return;
          const ok = await deleteExperience(selectedDeleteId);
          if (ok) {
            deleteExperience(selectedDeleteId);
            toast.success("Expérience supprimée avec succès.");
          }
          setSelectedDeleteId(null);
        }}
        onClose={() => {
          if (isLoading) return;
          setSelectedDeleteId(null);
        }}
      />
    </>
  );
};

export default ExperiencesCRUDSection;
