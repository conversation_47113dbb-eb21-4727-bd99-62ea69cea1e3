import React, { useEffect, useMemo, useState } from "react";
import { Stethoscope, X } from "lucide-react";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import { SpecialiteProfessionnel } from "@/domain/models/SpecialiteProfessionnel.ts";
import { z } from "zod";
import MultiSelect, {
  SelectableItem,
} from "@/presentation/components/common/MultiSelect.tsx";
import { useSpecialitiesSection } from "@/presentation/hooks/sections/use-specialities-section.ts";
import { ListeSpecialites } from "@/domain/models/ListeSpecialites.ts";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";
import { Badge } from "@mui/material";

/**
 * Interface pour les propriétés du composant SpecialtiesCRUDSection
 */
interface SpecialtiesCRUDSectionProps {
  /** Liste des spécialités du professionnel */
  specialties: SpecialiteProfessionnel[];
  /** Liste des spécialités disponibles */
  availableSpecialitiesLists: ListeSpecialites[];
  /** Fonction pour ajouter une spécialité */
  onAddSpecialty: (specialityIds: number[]) => Promise<boolean>;
  /** Fonction pour supprimer une spécialité */
  onDeleteSpecialty: (id: number) => Promise<boolean>;
}

/**
 * Composant de section spécialités avec opérations CRUD complètes
 *
 * @description
 * - Affiche les spécialités existantes du professionnel
 * - Permet l’ajout via MultiSelect (IDs de la table liste des spécialités)
 * - La suppression est différée: clic sur l’icône = retrait visuel + marquage,
 *   l’appel API effectif est réalisé uniquement au clic sur "Sauvegarder" (avec confirmation)
 * - Le parent rafraîchit les données au succès pour garder l’UI à jour
 */
const SpecialtiesCRUDSection: React.FC<SpecialtiesCRUDSectionProps> = ({
  specialties,
  availableSpecialitiesLists,
  onAddSpecialty,
  onDeleteSpecialty,
}) => {
  // Sélection locale des nouvelles spécialités à ajouter (provenant de la liste maître)
  const [selectedNewSpecialities, setSelectedNewSpecialities] = useState<
    SelectableItem[]
  >([]);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [specialitiesToDelete, setSpecialitiesToDelete] = useState<
    SpecialiteProfessionnel[]
  >([]);

  // Liste affichée localement et suppressions différées
  const [displayedSpecialties, setDisplayedSpecialties] =
    useState<SpecialiteProfessionnel[]>(specialties);
  const [pendingDeletionIds, setPendingDeletionIds] = useState<Set<number>>(
    new Set()
  );
  const [tempIdCounter, setTempIdCounter] = useState<number>(-1);

  // Synchroniser l'affichage lorsque les props changent (après sauvegarde)
  useEffect(() => {
    setDisplayedSpecialties(specialties);
    setPendingDeletionIds(new Set());
  }, [specialties]);

  // Options d’ajout: exclure celles déjà affichées (doublon par nom)
  const availableSpecialities = useMemo(() => {
    const assignedNames = new Set(
      displayedSpecialties.map((s) => (s.nom_specialite || "").toLowerCase())
    );
    return availableSpecialitiesLists.filter(
      (item) => !assignedNames.has((item.nom_specialite || "").toLowerCase())
    );
  }, [availableSpecialitiesLists, displayedSpecialties]);

  // Gestion de l'état d'édition avec le hook spécialisé
  const {
    register,
    formState: { errors },
    save,
    cancel,
    isSaving,
    setValue,
    watch,
  } = useSectionForm({
    schema: z.object({
      newSpecialty: z.array(z.number().int().positive()).default([]),
    }),
    defaultValues: {
      newSpecialty: [],
    },
    onSave: async (data) => {
      let overallSuccess = true;

      // Ajout des nouvelles spécialités sélectionnées
      if (data.newSpecialty && data.newSpecialty.length > 0) {
        const addOk = await onAddSpecialty(data.newSpecialty);
        overallSuccess = overallSuccess && addOk;
        if (addOk) {
          // Mise à jour optimiste de l'affichage en ajoutant les éléments sélectionnés
          setDisplayedSpecialties((prev) => {
            const additions: SpecialiteProfessionnel[] =
              selectedNewSpecialities.map((item, index) => ({
                id: tempIdCounter - index,
                nom_specialite: String(
                  (item as { nom_specialite?: string }).nom_specialite || ""
                ),
                id_professionnel: 0,
              }));
            return [...prev, ...additions];
          });
          setTempIdCounter((c) => c - selectedNewSpecialities.length);
          setValue("newSpecialty", []);
          setSelectedNewSpecialities([]);
        }
      }

      // Suppressions différées avec confirmation au moment de sauvegarder
      if (pendingDeletionIds.size > 0) {
        setIsConfirmModalOpen(true);
        // La suppression effective sera gérée par le gestionnaire onConfirm de la modal
        // On retourne true pour indiquer que la sauvegarde est réussie, même si la confirmation est en attente
        return overallSuccess;
      }

      return overallSuccess;
    },
    sectionName: "spécialités",
  });

  // Callback de fermeture du modal
  const handleConfirmModalClose = () => {
    setIsConfirmModalOpen(false);
    // Remettre les spécialités supprimées dans la liste des spécialités existantes
    displayedSpecialties.push(...specialitiesToDelete);
    setSpecialitiesToDelete([]);
    setPendingDeletionIds(new Set());
  };

  // Marquer une spécialité pour suppression (sans appel API immédiat)
  const markSpecialtyForDeletion = (specialtyId: number) => {
    const specialtyToDelete = displayedSpecialties.find(
      (s) => s.id === specialtyId
    );
    if (specialtyToDelete) {
      setSpecialitiesToDelete((prev) => [...prev, specialtyToDelete]);
    }
    setDisplayedSpecialties((prev) => prev.filter((s) => s.id !== specialtyId));
    setPendingDeletionIds((prev) => new Set(prev).add(specialtyId));
  };

  // Remettre les spécialités supprimées dans la liste des spécialités existantes
  const unmarkSpecialtyForDeletion = (specialty: SpecialiteProfessionnel) => {
    setSpecialitiesToDelete((prev) =>
      prev.filter((s) => s.id !== specialty.id)
    );
    const updatedSpecialties = [...displayedSpecialties, specialty];
    setDisplayedSpecialties(updatedSpecialties);

    const updatedPendingDeletionIds = new Set(pendingDeletionIds);
    updatedPendingDeletionIds.delete(specialty.id);
    setPendingDeletionIds(updatedPendingDeletionIds);
  };

  return (
    <>
      <EditableSection
        title="Spécialités médicales"
        icon={Stethoscope}
        onSave={save}
        onCancel={handleConfirmModalClose}
        isSaving={isSaving}
        editContent={
          <div className="space-y-4">
            {/* Liste des spécialités existantes */}
            <div className="space-y-2">
              {displayedSpecialties.map((specialty) => (
                <div
                  key={specialty.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                >
                  <>
                    <span className="text-gray-700">
                      {specialty.nom_specialite}
                    </span>
                    <div className="flex items-center gap-1">
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          markSpecialtyForDeletion(specialty.id);
                        }}
                        className="p-1 text-red-600 hover:bg-red-100 rounded"
                        title="Supprimer"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </>
                </div>
              ))}
            </div>

            {/* Ajout d'une nouvelle spécialité */}
            <div className="border-t pt-4">
              <h2 className="text-lg font-semibold mb-2">
                Ajouter une spécialité
              </h2>
              <div className="flex flex-col gap-2 w-full">
                <MultiSelect
                  itemsList={availableSpecialities}
                  selectedItems={selectedNewSpecialities}
                  handleItemsChange={(items) => {
                    setSelectedNewSpecialities(items);
                    setValue(
                      "newSpecialty",
                      items.map((item) => Number(item.id))
                    );
                  }}
                  register={register}
                  fieldName="newSpecialty"
                  displayProperty="nom_specialite"
                  dropdownId="specialtiesList"
                  placeholderText="Sélectionnez une ou plusieurs spécialités"
                  selectedText={(count) =>
                    `${count} spécialité(s) sélectionnée(s)`
                  }
                  sortAlphabetically={true}
                  allSelectedText="Toutes les spécialités ont été sélectionnées"
                  selectedItemsTitle="Spécialités sélectionnées:"
                  error={errors.newSpecialty}
                />
              </div>
            </div>

            {specialitiesToDelete.length > 0 && (
              <div className="space-y-2 bg-red-100 p-4 rounded">
                <h2 className="text-lg font-semibold mb-2">
                  Supprimer les spécialités suivantes ?
                </h2>
                {specialitiesToDelete.map((specialty) => (
                  <div
                    key={specialty.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                  >
                    <>
                      <span className="text-gray-700">
                        {specialty.nom_specialite}
                      </span>
                      <div className="flex items-center gap-1">
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            unmarkSpecialtyForDeletion(specialty);
                          }}
                          className="p-1 text-red-600 hover:bg-red-100 rounded"
                          title="Supprimer"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    </>
                  </div>
                ))}
              </div>
            )}
          </div>
        }
      >
        {/* Contenu en mode lecture */}
        <div>
          {specialties.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {specialties.map((specialty) => (
                <span
                  key={specialty.id}
                  className="inline-flex items-center px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200"
                >
                  {specialty.nom_specialite}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-gray-400 italic">
              Aucune spécialité renseignée. Cliquez sur l'icône d'édition pour
              ajouter des spécialités.
            </p>
          )}
        </div>
      </EditableSection>

      <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={async () => {
          let success = true;
          // Exécuter les suppressions après confirmation
          for (const id of pendingDeletionIds) {
            try {
              const delOk = await onDeleteSpecialty(id);
              success = success && delOk;
            } catch (e) {
              success = false;
            }
          }
          // Réinitialiser la liste des suppressions en attente
          setPendingDeletionIds(new Set());
          setIsConfirmModalOpen(false);
          // Finaliser la sauvegarde
          return success;
        }}
        message={`Voulez-vous vraiment supprimer ${pendingDeletionIds.size} spécialité(s) ?`}
        title="Supprimer les spécialités sélectionnées"
        confirmButtonText="Supprimer"
        cancelButtonText="Annuler"
      />
    </>
  );
};

export default SpecialtiesCRUDSection;
