import React, { FormEventHandler } from "react";
import { Mail, Phone, MapPin, Info } from "lucide-react";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import {
  contactSchema,
  ContactFormData,
} from "@/shared/schemas/ProfessionalProfileSchemas";
import Form<PERSON>ield from "@/presentation/components/common/ui/FormField.tsx";
import { Contact } from "@/domain/models/Contact.ts";

/**
 * Interface pour les propriétés du composant ContactCRUDSection
 */
interface ContactCRUDSectionProps {
  /** Adresse email du professionnel */
  email: string;
  /** Numéro de téléphone du professionnel */
  telephone: Contact | null;
  /** Adresse physique du professionnel */
  adresse: string;
  /** Fokontany (localisation) du professionnel */
  fokontany: string;
  /** Informations d'accès au cabinet (optionnel) */
  infoAcces?: string;
  /** Fonction appelée lors de la sauvegarde des informations de contact */
  onSave: (contactData: ContactFormData, contactId: number) => Promise<boolean>;
}

/**
 * Composant d'informations de contact avec CRUD complet
 *
 * Ce composant utilise react-hook-form avec Zod pour la validation
 * et le système d'édition par section pour une UX cohérente.
 * Il gère les informations de contact avec validation complète.
 *
 * @example
 * ```tsx
 * <ContactCRUDSection
 *   email="<EMAIL>"
 *   telephone="+261 34 12 345 67"
 *   adresse="123 Rue de la Paix"
 *   fokontany="Antananarivo"
 *   infoAcces="2ème étage, ascenseur disponible"
 *   onSave={async (data) => await saveContact(data)}
 * />
 * ```
 *
 * @param props - Les propriétés du composant
 * @returns Élément JSX représentant les informations de contact
 */
const ContactCRUDSection: React.FC<ContactCRUDSectionProps> = ({
  telephone,
  email,
  adresse,
  fokontany,
  infoAcces = "",
  onSave,
}) => {
  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    register,
    formState: { errors },
    save,
    cancel,
    isSaving,
    watch,
  } = useSectionForm({
    schema: contactSchema,
    defaultValues: {
      email: email || "",
      telephone: telephone?.numero || "",
      adresse: adresse || "",
      fokontany: fokontany || "",
      infoAcces: infoAcces || "",
    },
    onSave: async (data) => await onSave(data, telephone?.id || 0),
    sectionName: "informations de contact",
  });

  // Surveille les valeurs actuelles
  const currentValues = watch();

  return (
    <EditableSection
      title="Contact"
      icon={Phone}
      onSave={save}
      onCancel={cancel}
      isSaving={isSaving}
      editContent={
        <div className="space-y-4">
          {/* Email */}
          <FormField
            id="email"
            label="Email"
            placeholder="Entrez votre email"
            type="email"
            icon={Mail}
            register={register}
            required
            error={errors.email}
          />

          {/* Téléphone */}
          <FormField
            id="telephone"
            label="Téléphone"
            placeholder="Entrez votre numéro de téléphone"
            icon={Phone}
            register={register}
            required
            error={errors.telephone}
          />

          {/* Adresse */}
          <FormField
            id="adresse"
            label="Adresse"
            placeholder="Entrez votre adresse"
            icon={MapPin}
            register={register}
            required
            error={errors.adresse}
          />

          {/* Fokontany */}
          <FormField
            id="fokontany"
            label="Fokontany"
            placeholder="Entrez votre fokontany"
            icon={MapPin}
            register={register}
            required
            error={errors.fokontany}
          />

          {/* Informations d'accès */}
          <FormField
            id="infoAcces"
            label="Informations d'accès"
            placeholder="Entrez les informations d'accès"
            icon={Info}
            register={register}
            error={errors.infoAcces}
          />
        </div>
      }
    >
      {/* Contenu en mode lecture */}
      <div className="space-y-4">
        {/* Email */}
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Mail className="h-4 w-4 text-meddoc-primary" />
            <h3 className="text-sm font-medium text-gray-600">Email</h3>
          </div>
          <p className="text-gray-900 ml-6">
            <a
              href={`mailto:${email}`}
              className="text-meddoc-primary hover:text-meddoc-primary/80 transition-colors"
            >
              {email || "Non renseigné"}
            </a>
          </p>
        </div>

        {/* Téléphone */}
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Phone className="h-4 w-4 text-meddoc-primary" />
            <h3 className="text-sm font-medium text-gray-600">Téléphone</h3>
          </div>
          <p className="text-gray-900 ml-6">
            <a
              href={`tel:${telephone?.numero}`}
              className="text-meddoc-primary hover:text-meddoc-primary/80 transition-colors"
            >
              {telephone?.numero || "Non renseigné"}
            </a>
          </p>
        </div>

        {/* Adresse */}
        <div>
          <div className="flex items-center gap-2 mb-1">
            <MapPin className="h-4 w-4 text-meddoc-primary" />
            <h3 className="text-sm font-medium text-gray-600">Adresse</h3>
          </div>
          <p className="text-gray-900 ml-6 dark:text-white">
            {adresse || "Non renseignée"}
          </p>
          {fokontany && (
            <p className="text-gray-600 text-sm ml-6 dark:text-white">
              Fokontany : {fokontany}
            </p>
          )}
        </div>

        {/* Informations d'accès */}
        {infoAcces && (
          <div>
            <div className="flex items-center gap-2 mb-1">
              <Info className="h-4 w-4 text-meddoc-primary" />
              <h3 className="text-sm font-medium text-gray-600">
                Informations d'accès
              </h3>
            </div>
            <p className="text-gray-700 ml-6 whitespace-pre-wrap dark:text-white">
              {infoAcces}
            </p>
          </div>
        )}
      </div>
    </EditableSection>
  );
};

export default ContactCRUDSection;
