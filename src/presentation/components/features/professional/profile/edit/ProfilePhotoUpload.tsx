import React, { useRef, useState } from "react";
import { Camera, Upload, X, User } from "lucide-react";
import { useProfilePhotoUpload } from "@/presentation/hooks/professional/use-profile-photo-upload";
import { Photo } from "@/domain/models/Photo";

/**
 * Interface pour les propriétés du composant ProfilePhotoUpload
 */
interface ProfilePhotoUploadProps {
  /** ID de l'utilisateur */
  userId: number;
  /** Photo de profil actuelle (optionnelle) */
  currentPhoto?: Photo | null;
  /** Callback appelé après un upload/mise à jour réussi */
  onPhotoUpdated?: (photo: Photo) => void;
  /** Callback appelé après une suppression réussie */
  onPhotoDeleted?: () => void;
  /** Taille du composant */
  size?: "sm" | "md" | "lg";
  /** Affichage en mode lecture seule */
  readonly?: boolean;
}

/**
 * Composant d'upload de photo de profil moderne
 * 
 * @description
 * Composant réutilisable pour l'upload, la mise à jour et la suppression
 * de photos de profil. Inclut :
 * - Drag & drop
 * - Prévisualisation
 * - Validation des fichiers
 * - États de chargement
 * - Interface responsive
 */
const ProfilePhotoUpload: React.FC<ProfilePhotoUploadProps> = ({
  userId,
  currentPhoto,
  onPhotoUpdated,
  onPhotoDeleted,
  size = "md",
  readonly = false,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const {
    uploadOrUpdatePhoto,
    deletePhoto,
    isUploading,
    isDeleting,
    isLoading,
  } = useProfilePhotoUpload();

  // Configuration des tailles
  const sizeConfig = {
    sm: {
      container: "w-20 h-20",
      icon: "h-6 w-6",
      text: "text-xs",
    },
    md: {
      container: "w-32 h-32",
      icon: "h-8 w-8",
      text: "text-sm",
    },
    lg: {
      container: "w-48 h-48",
      icon: "h-12 w-12",
      text: "text-base",
    },
  };

  const config = sizeConfig[size];

  /**
   * Gère la sélection de fichier
   */
  const handleFileSelect = async (file: File) => {
    if (!file || readonly) return;

    // Créer une prévisualisation
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    try {
      const result = await uploadOrUpdatePhoto(userId, file);
      if (result && onPhotoUpdated) {
        onPhotoUpdated(result);
      }
    } finally {
      // Nettoyer l'URL de prévisualisation
      URL.revokeObjectURL(url);
      setPreviewUrl(null);
    }
  };

  /**
   * Gère le changement d'input file
   */
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  /**
   * Gère le drag & drop
   */
  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);

    if (readonly) return;

    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!readonly) {
      setDragOver(true);
    }
  };

  const handleDragLeave = () => {
    setDragOver(false);
  };

  /**
   * Ouvre le sélecteur de fichier
   */
  const openFileSelector = () => {
    if (!readonly && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  /**
   * Supprime la photo actuelle
   */
  const handleDeletePhoto = async () => {
    if (!currentPhoto || readonly) return;

    if (confirm("Êtes-vous sûr de vouloir supprimer votre photo de profil ?")) {
      const success = await deletePhoto(currentPhoto.id);
      if (success && onPhotoDeleted) {
        onPhotoDeleted();
      }
    }
  };

  // URL de la photo à afficher
  const displayPhotoUrl = previewUrl || currentPhoto?.path;

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Container principal de la photo */}
      <div
        className={`
          relative ${config.container} rounded-full border-2 border-dashed
          ${dragOver ? 'border-meddoc-primary bg-meddoc-primary/10' : 'border-gray-300'}
          ${!readonly ? 'cursor-pointer hover:border-meddoc-primary hover:bg-gray-50' : ''}
          ${isLoading ? 'opacity-50' : ''}
          transition-all duration-200 overflow-hidden
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={openFileSelector}
      >
        {/* Photo actuelle ou prévisualisation */}
        {displayPhotoUrl ? (
          <img
            src={displayPhotoUrl}
            alt="Photo de profil"
            className="w-full h-full object-cover rounded-full"
          />
        ) : (
          /* Placeholder quand pas de photo */
          <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-full">
            <User className={`${config.icon} text-gray-400`} />
          </div>
        )}

        {/* Overlay de chargement */}
        {isLoading && (
          <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
          </div>
        )}

        {/* Overlay d'upload (visible au hover si pas readonly) */}
        {!readonly && !isLoading && (
          <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
            <Camera className="h-6 w-6 text-white" />
          </div>
        )}

        {/* Bouton de suppression */}
        {currentPhoto && !readonly && !isLoading && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleDeletePhoto();
            }}
            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
            title="Supprimer la photo"
          >
            <X className="h-3 w-3" />
          </button>
        )}
      </div>

      {/* Input file caché */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        onChange={handleInputChange}
        className="hidden"
        disabled={readonly || isLoading}
      />

      {/* Texte d'instruction */}
      {!readonly && (
        <div className={`text-center ${config.text} text-gray-600`}>
          <p className="font-medium">
            {currentPhoto ? "Modifier la photo" : "Ajouter une photo"}
          </p>
          <p className="text-gray-500">
            Cliquez ou glissez-déposez
          </p>
          <p className="text-gray-400 text-xs mt-1">
            JPG, PNG, WebP - Max 5MB
          </p>
        </div>
      )}

      {/* Bouton d'upload alternatif pour mobile */}
      {!readonly && size !== "sm" && (
        <button
          onClick={openFileSelector}
          disabled={isLoading}
          className="flex items-center gap-2 px-4 py-2 bg-meddoc-primary text-white rounded-lg hover:bg-meddoc-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <Upload className="h-4 w-4" />
          {currentPhoto ? "Changer la photo" : "Uploader une photo"}
        </button>
      )}
    </div>
  );
};

export default ProfilePhotoUpload;
