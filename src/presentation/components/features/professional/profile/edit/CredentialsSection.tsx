import React from "react";
import {
  GraduationCap,
  Briefcase,
  FileText,
  Languages,
  LucideIcon,
} from "lucide-react";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel.ts";
import { ExperienceProfessionnel } from "@/domain/models/ExperienceProfessionnel.ts";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel.ts";
import { LangueParleeProfessionnel } from "@/domain/models/LangueParleeProfessionnel.ts";

/**
 * Type générique pour les éléments avec un identifiant
 */
type ItemWithId =
  | DiplomeProfessionnel
  | ExperienceProfessionnel
  | PublicationProfessionnel
  | LangueParleeProfessionnel;

/**
 * Interface pour les propriétés du composant CredentialsSection
 */
interface CredentialsSectionProps {
  /** Liste des diplômes */
  diplomes: DiplomeProfessionnel[];
  /** Liste des expériences */
  experiences: ExperienceProfessionnel[];
  /** Liste des publications */
  publications: PublicationProfessionnel[];
  /** Liste des langues parlées */
  langues: LangueParleeProfessionnel[];
  /** Indique si le mode édition est actif */
  isEditing: boolean;
  /** Fonction appelée lors de l'ajout d'un diplôme */
  onAddDiplome?: () => void;
  /** Fonction appelée lors de la suppression d'un diplôme */
  onRemoveDiplome?: (diplomeId: string | number) => void;
  /** Fonction appelée lors de l'ajout d'une expérience */
  onAddExperience?: () => void;
  /** Fonction appelée lors de la suppression d'une expérience */
  onRemoveExperience?: (experienceId: string | number) => void;
  /** Fonction appelée lors de l'ajout d'une publication */
  onAddPublication?: () => void;
  /** Fonction appelée lors de la suppression d'une publication */
  onRemovePublication?: (publicationId: string | number) => void;
  /** Fonction appelée lors de l'ajout d'une langue */
  onAddLangue?: () => void;
  /** Fonction appelée lors de la suppression d'une langue */
  onRemoveLangue?: (langueId: string | number) => void;
}

/**
 * Composant de section qualifications et expériences
 *
 * Ce composant affiche les qualifications professionnelles du médecin :
 * diplômes, expériences, publications et langues parlées.
 * Il permet l'ajout et la suppression d'éléments en mode édition.
 *
 * @example
 * ```tsx
 * <CredentialsSection
 *   diplomes={[{ nom: "Doctorat en Médecine", institution: "Université", annee: "2015" }]}
 *   experiences={[{ poste: "Médecin", etablissement: "Hôpital", periode: "2015-2020", description: "..." }]}
 *   publications={[{ titre: "Article médical", lien: "https://...", annee: "2020" }]}
 *   langues={[{ nom: "Français", niveau: "Natif" }]}
 *   isEditing={true}
 *   onAddDiplome={() => handleAddDiplome()}
 *   onRemoveDiplome={(id) => handleRemoveDiplome(id)}
 * />
 * ```
 *
 * @param props - Les propriétés du composant
 * @returns Élément JSX représentant la section qualifications
 */
const CredentialsSection: React.FC<CredentialsSectionProps> = ({
  diplomes,
  experiences,
  publications,
  langues,
  isEditing,
  onAddDiplome,
  onRemoveDiplome,
  onAddExperience,
  onRemoveExperience,
  onAddPublication,
  onRemovePublication,
  onAddLangue,
  onRemoveLangue,
}) => {
  /**
   * Composant pour afficher une liste d'éléments avec icône
   */
  const ItemsList = ({
    title,
    icon: Icon,
    items,
    onAdd,
    onRemove,
    renderItem,
  }: {
    title: string;
    icon: LucideIcon;
    items: ItemWithId[];
    onAdd?: () => void;
    onRemove?: (id: string | number) => void;
    renderItem: (item: ItemWithId) => React.ReactNode;
  }) => (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Icon size={18} className="text-blue-600" />
          <h3 className="text-lg font-medium text-gray-800">{title}</h3>
        </div>
        {isEditing && onAdd && (
          <button
            onClick={onAdd}
            className="text-blue-600 hover:text-blue-800 text-sm transition-colors"
          >
            + Ajouter
          </button>
        )}
      </div>

      {items.length > 0 ? (
        <div className="space-y-3">
          {items.map((item, index) => (
            <div key={item.id || index} className="p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-start">
                <div className="flex-1">{renderItem(item)}</div>
                {isEditing && onRemove && (
                  <button
                    onClick={() => onRemove(item.id || index)}
                    className="text-red-600 hover:text-red-800 text-sm ml-2 transition-colors"
                  >
                    Supprimer
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500 text-sm italic">Aucun élément ajouté</p>
      )}
    </div>
  );

  return (
    <section className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-meddoc-primary/10 rounded-lg">
          <GraduationCap className="h-5 w-5 text-meddoc-primary" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">
          Qualifications et Expériences
        </h2>
      </div>

      {/* Diplômes */}
      <ItemsList
        title="Diplômes"
        icon={GraduationCap}
        items={diplomes}
        onAdd={onAddDiplome}
        onRemove={onRemoveDiplome}
        renderItem={(diplome: DiplomeProfessionnel) => (
          <div>
            <h4 className="font-medium text-gray-900">{diplome.titre}</h4>
            <p className="text-sm text-gray-600">
              {diplome.etablissement} • {diplome.annee}
            </p>
          </div>
        )}
      />

      {/* Expériences */}
      <ItemsList
        title="Expériences"
        icon={Briefcase}
        items={experiences}
        onAdd={onAddExperience}
        onRemove={onRemoveExperience}
        renderItem={(experience: ExperienceProfessionnel) => (
          <div>
            <h4 className="font-medium text-gray-900">{experience.poste}</h4>
            <p className="text-sm text-gray-600">
              {experience.etablissement} • {experience.date_debut}
            </p>
            {experience.description && (
              <p className="text-sm text-gray-700 mt-1">
                {experience.description}
              </p>
            )}
          </div>
        )}
      />

      {/* Publications */}
      <ItemsList
        title="Publications"
        icon={FileText}
        items={publications}
        onAdd={onAddPublication}
        onRemove={onRemovePublication}
        renderItem={(publication: PublicationProfessionnel) => (
          <div>
            <h4 className="font-medium text-gray-900">{publication.titre}</h4>
            <p className="text-sm text-gray-600">{publication.annee}</p>
            {publication.lien && (
              <a
                href={publication.lien}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Voir la publication
              </a>
            )}
          </div>
        )}
      />

      {/* Langues */}
      <ItemsList
        title="Langues parlées"
        icon={Languages}
        items={langues}
        onAdd={onAddLangue}
        onRemove={onRemoveLangue}
        renderItem={(langue: LangueParleeProfessionnel) => (
          <div>
            <span className="font-medium text-gray-900">
              {langue.nom_langue}
            </span>
          </div>
        )}
      />
    </section>
  );
};

export default CredentialsSection;
