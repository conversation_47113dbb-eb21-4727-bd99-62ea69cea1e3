import React from "react";
import { Stethoscope } from "lucide-react";

/**
 * Interface pour une spécialité
 */
interface Speciality {
  /** Identifiant unique de la spécialité */
  id: string | number;
  /** Nom de la spécialité */
  nom_specialite: string;
}

/**
 * Interface pour les propriétés du composant SpecialitiesEditSection
 */
interface SpecialitiesEditSectionProps {
  /** Liste des spécialités du professionnel */
  specialities: Speciality[];
  /** Indique si le mode édition est actif */
  isEditing: boolean;
  /** Fonction appelée lors de la suppression d'une spécialité */
  onRemoveSpeciality?: (specialityId: string | number) => void;
  /** Fonction appelée lors de l'ajout d'une nouvelle spécialité */
  onAddSpeciality?: () => void;
}

/**
 * Composant de section spécialités éditable
 *
 * Ce composant affiche la liste des spécialités du professionnel
 * avec la possibilité d'ajouter ou supprimer des spécialités
 * en mode édition.
 *
 * @example
 * ```tsx
 * <SpecialitiesEditSection
 *   specialities={[
 *     { id: 1, nom_specialite: "Cardiologie" },
 *     { id: 2, nom_specialite: "Médecine générale" }
 *   ]}
 *   isEditing={true}
 *   onRemoveSpeciality={(id) => handleRemoveSpeciality(id)}
 *   onAddSpeciality={() => handleAddSpeciality()}
 * />
 * ```
 *
 * @param props - Les propriétés du composant
 * @returns Élément JSX représentant la section spécialités
 */
const SpecialitiesEditSection: React.FC<SpecialitiesEditSectionProps> = ({
  specialities,
  isEditing,
  onRemoveSpeciality,
  onAddSpeciality,
}) => {
  return (
    <section className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-meddoc-primary/10 rounded-lg">
          <Stethoscope className="h-5 w-5 text-meddoc-primary" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">Spécialités</h2>
      </div>
      <div className="space-y-4">
        {specialities.map((speciality) => (
          <div
            key={speciality.id}
            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <span className="font-medium">{speciality.nom_specialite}</span>
            {isEditing && onRemoveSpeciality && (
              <button
                onClick={() => onRemoveSpeciality(speciality.id)}
                className="text-red-600 hover:text-red-800 transition-colors"
              >
                Supprimer
              </button>
            )}
          </div>
        ))}
        {isEditing && onAddSpeciality && (
          <button
            onClick={onAddSpeciality}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            + Ajouter une spécialité
          </button>
        )}
      </div>
    </section>
  );
};

export default SpecialitiesEditSection;
