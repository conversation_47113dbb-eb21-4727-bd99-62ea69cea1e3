import React, { useState, useCallback, useEffect, useMemo } from "react";
import {
  CreditCard,
  Shield,
  Plus,
  X,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import { ListeAssurances } from "@/domain/models/AssuranceProfessionnel.ts";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import { paymentAndInsuranceSchema } from "@/shared/schemas/ProfessionalProfileSchemas";
import TextAreaField from "@/presentation/components/common/ui/TextAreaField.tsx";
import MultiSelect from "@/presentation/components/common/MultiSelect.tsx";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";

/**
 * Interface pour les propriétés du composant PaymentAndInsuranceCRUDSection
 * @interface PaymentAndInsuranceCRUDSectionProps
 */
interface PaymentAndInsuranceCRUDSectionProps {
  /** Listes des assurances disponibles */
  availableInsurances: ListeAssurances[];
  /** Liste des modes de paiement acceptés */
  paymentMethods: string;
  /** Liste des assurances acceptées */
  insurances: ListeAssurances[];
  /** Fonction appelée lors de la sauvegarde des modes de paiement */
  onSavePaymentMethods: (paymentMethods: string) => Promise<boolean>;
  /** Fonction appelée lors de l'ajout d'une assurance */
  onAddInsurance: (newInsurances: ListeAssurances) => Promise<boolean>;
  /** Fonction appelée lors de la suppression d'une assurance */
  onRemoveInsurance: (insuranceId: number) => Promise<boolean>;
}

/**
 * Composant de section modes de paiement et assurances avec CRUD complet
 *
 * @description Ce composant permet la gestion complète des modes de paiement
 * et des assurances avec une interface intuitive utilisant react-hook-form.
 * Il suit les patterns d'édition par section avec une gestion d'état cohérente.
 *
 * @example
 * ```tsx
 * <PaymentAndInsuranceCRUDSection
 *   paymentMethods="Espèces, Carte bancaire"
 *   insurances={[{id: 1, nom: "CNAPS"}]}
 *   onSavePaymentMethods={async (methods) => await savePaymentMethods(methods)}
 *   onAddInsurance={async (name) => await addInsurance(name)}
 *   onRemoveInsurance={async (id) => await removeInsurance(id)}
 * />
 * ```
 */
const PaymentAndInsuranceCRUDSection: React.FC<
  PaymentAndInsuranceCRUDSectionProps
> = ({
  availableInsurances: allInsurances,
  paymentMethods,
  insurances,
  onSavePaymentMethods,
  onAddInsurance,
  onRemoveInsurance,
}) => {
  /**
   * États locaux pour la gestion des assurances
   * 
   * Pattern de suppression différée:
   * 1. L'utilisateur clique sur l'icône de suppression d'une assurance
   * 2. L'assurance est retirée visuellement de la liste principale (displayedInsurances)
   * 3. Elle est ajoutée à la liste des suppressions en attente (insurancesToDelete)
   * 4. Son ID est stocké dans pendingDeletionIds pour traitement ultérieur
   * 5. L'utilisateur peut annuler la suppression avant de sauvegarder
   * 6. Au moment de la sauvegarde, une confirmation est demandée
   * 7. Les suppressions sont effectuées uniquement après confirmation
   * 
   * États:
   * - isAddingInsurance: indicateur de chargement pendant l'ajout
   * - insuranceError/Success: messages de feedback utilisateur
   * - selectedInsurances: nouvelles assurances sélectionnées pour ajout
   * - displayedInsurances: liste des assurances affichées (après suppressions visuelles)
   * - pendingDeletionIds: IDs des assurances marquées pour suppression (différée)
   * - insurancesToDelete: objets complets des assurances marquées pour suppression
   * - isConfirmModalOpen: contrôle l'affichage de la modale de confirmation
   */
  const [isAddingInsurance, setIsAddingInsurance] = useState(false);
  const [insuranceError, setInsuranceError] = useState<string | null>(null);
  const [insuranceSuccess, setInsuranceSuccess] = useState<string | null>(null);
  const [selectedInsurances, setSelectedInsurances] = useState<ListeAssurances[]>([]);
  
  const [displayedInsurances, setDisplayedInsurances] = useState<ListeAssurances[]>(insurances);
  const [pendingDeletionIds, setPendingDeletionIds] = useState<Set<number>>(new Set());
  const [insurancesToDelete, setInsurancesToDelete] = useState<ListeAssurances[]>([]);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  /**
   * Synchronise l'affichage lorsque les props changent (après sauvegarde)
   * Réinitialise également les listes de suppressions en attente
   */
  useEffect(() => {
    setDisplayedInsurances(insurances);
    setPendingDeletionIds(new Set());
    setInsurancesToDelete([]);
  }, [insurances]);

  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    register,
    formState: { errors },
    save,
    cancel,
    isSaving,
    resetWithValues,
  } = useSectionForm({
    schema: paymentAndInsuranceSchema,
    defaultValues: { modes_paiement_acceptes: paymentMethods || "" },
    /**
     * Gestionnaire de sauvegarde du formulaire
     * - Sauvegarde d'abord les modes de paiement
     * - Si des suppressions sont en attente, ouvre la modale de confirmation
     * - Les suppressions effectives sont gérées par le gestionnaire onConfirm de la modale
     */
    onSave: async (data) => {
      let overallSuccess = true;

      // Sauvegarde des modes de paiement
      const paymentSuccess = await onSavePaymentMethods(
        data.modes_paiement_acceptes
      );
      overallSuccess = overallSuccess && paymentSuccess;

      // Suppressions différées avec confirmation au moment de sauvegarder
      if (pendingDeletionIds.size > 0) {
        setIsConfirmModalOpen(true);
        // La suppression effective sera gérée par le gestionnaire onConfirm de la modal
        // On retourne true pour indiquer que la sauvegarde est réussie, même si la confirmation est en attente
      }

      return overallSuccess;
    },
    sectionName: "modes de paiement et assurances",
  });

  // Mise à jour du formulaire quand paymentMethods change
  useEffect(() => {
    resetWithValues({ modes_paiement_acceptes: paymentMethods || "" });
  }, [paymentMethods, resetWithValues]);

  // Calcul des assurances disponibles (filtrées) via useMemo pour éviter les recalculs inutiles
  const availableInsurances = useMemo(() => {
    return allInsurances.filter(
      (ins) =>
        !displayedInsurances.find((existingIns) => existingIns.id === ins.id)
    );
  }, [allInsurances, displayedInsurances]);

  /**
   * Réinitialise les messages d'état des assurances
   */
  const resetInsuranceMessages = useCallback(() => {
    setInsuranceError(null);
    setInsuranceSuccess(null);
  }, []);

  /**
   * Gère les changements de sélection d'assurances
   */
  const handleInsurancesChange = useCallback(
    (insurances: ListeAssurances[]) => {
      setSelectedInsurances(insurances);
    },
    []
  );

  /**
   * Marque une assurance pour suppression (sans appel API immédiat)
   * - Retire l'assurance de l'affichage principal
   * - Ajoute son ID à la liste des suppressions en attente
   * - Conserve l'objet complet pour permettre l'annulation ultérieure
   * 
   * @param {number} insuranceId - ID de l'assurance à marquer pour suppression
   */
  const markInsuranceForDeletion = useCallback((insuranceId: number) => {
    const insuranceToDelete = displayedInsurances.find(ins => ins.id === insuranceId);
    if (insuranceToDelete) {
      setInsurancesToDelete(prev => [...prev, insuranceToDelete]);
    }
    
    setDisplayedInsurances(prev => prev.filter(ins => ins.id !== insuranceId));
    setPendingDeletionIds(prev => new Set(prev).add(insuranceId));
  }, [displayedInsurances]);
  
  /**
   * Annule le marquage d'une assurance pour suppression
   * - Remet l'assurance dans la liste d'affichage principale
   * - Retire son ID de la liste des suppressions en attente
   * - Retire l'objet de la liste des assurances à supprimer
   * 
   * @param {ListeAssurances} insurance - Objet assurance complet à restaurer
   */
  const unmarkInsuranceForDeletion = useCallback((insurance: ListeAssurances) => {
    // Retirer de la liste des assurances à supprimer
    setInsurancesToDelete(prev => prev.filter(ins => ins.id !== insurance.id));
    
    // Remettre dans la liste d'affichage principale
    setDisplayedInsurances(prev => [...prev, insurance]);
    
    // Retirer l'ID de la liste des suppressions en attente
    const updatedPendingDeletionIds = new Set(pendingDeletionIds);
    updatedPendingDeletionIds.delete(insurance.id);
    setPendingDeletionIds(updatedPendingDeletionIds);
  }, [pendingDeletionIds]);

  /**
   * Ajoute les assurances sélectionnées avec gestion d'erreur améliorée
   * - Vérifie qu'au moins une assurance est sélectionnée
   * - Gère les états de chargement et les messages de feedback
   * - Traite chaque assurance individuellement et compte les succès/échecs
   */
  const handleAddInsurances = useCallback(async () => {
    if (selectedInsurances.length === 0) {
      setInsuranceError("Veuillez sélectionner au moins une assurance");
      return;
    }

    resetInsuranceMessages();
    setIsAddingInsurance(true);

    try {
      let allSuccess = true;
      let addedCount = 0;

      // Ajouter chaque assurance sélectionnée en passant la liste complète pour éviter les appels API
      for (const insurance of selectedInsurances) {
        const success = await onAddInsurance(insurance);
        if (success) {
          addedCount++;
        } else {
          allSuccess = false;
        }
      }

      if (allSuccess) {
        setSelectedInsurances([]);
        setInsuranceSuccess(
          `${addedCount} assurance(s) ajoutée(s) avec succès`
        );
        // Effacer le message de succès après 3 secondes
        setTimeout(() => setInsuranceSuccess(null), 3000);
      } else {
        setInsuranceError(
          `${addedCount} assurance(s) ajoutée(s), ${selectedInsurances.length - addedCount} échec(s)`
        );
      }
    } catch (error) {
      setInsuranceError("Erreur lors de l'ajout des assurances");
    } finally {
      setIsAddingInsurance(false);
    }
  }, [selectedInsurances, onAddInsurance, resetInsuranceMessages]);

  return (
    <>
      <EditableSection
        title="Paiements et Assurances"
        icon={CreditCard}
        onSave={save}
        onCancel={cancel}
        isSaving={isSaving}
        editContent={
          <div className="space-y-6">
            {/* Modes de paiement */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <CreditCard size={18} className="text-meddoc-primary" />
                <h3 className="text-lg font-medium text-gray-800">
                  Modes de paiement acceptés
                </h3>
              </div>
              <TextAreaField
                id="modes_paiement_acceptes"
                label="Modes de paiement acceptés"
                placeholder="Modes de paiement acceptés"
                icon={CreditCard}
                register={register}
                error={errors.modes_paiement_acceptes}
                showCharCount
              />
            </div>

            {/* Assurances */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Shield size={18} className="text-meddoc-primary" />
                <h3 className="text-lg font-medium text-gray-800">
                  Assurances acceptées
                </h3>
              </div>

              {/* Messages d'état pour les assurances */}
              {insuranceError && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 mb-3">
                  <AlertCircle size={16} />
                  <span className="text-sm">{insuranceError}</span>
                </div>
              )}
              {insuranceSuccess && (
                <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 mb-3">
                  <CheckCircle size={16} />
                  <span className="text-sm">{insuranceSuccess}</span>
                </div>
              )}

              {/* Liste des assurances existantes */}
              <div className="space-y-2 mb-4">
                {displayedInsurances.map((insurance) => (
                  <div
                    key={insurance.id}
                    className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200"
                  >
                    <span className="font-medium text-blue-900">
                      {insurance.nom}
                    </span>
                    <button
                      onClick={() => markInsuranceForDeletion(insurance.id)}
                      className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                      title="Supprimer"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
                {displayedInsurances.length === 0 && insurancesToDelete.length === 0 && (
                  <p className="text-gray-500 text-sm italic">
                    Aucune assurance ajoutée
                  </p>
                )}
              </div>
              
              {/* Liste des assurances marquées pour suppression */}
              {insurancesToDelete.length > 0 && (
                <div className="space-y-2 bg-red-50 p-4 rounded mb-4">
                  <h2 className="text-lg font-semibold mb-2 text-red-700">
                    Assurances à supprimer ({insurancesToDelete.length})
                  </h2>
                  <p className="text-sm text-red-600 mb-2">
                    Ces assurances seront supprimées lors de la sauvegarde. Cliquez sur <CheckCircle className="h-3 w-3 inline" /> pour annuler la suppression.
                  </p>
                  {insurancesToDelete.map((insurance) => (
                    <div
                      key={insurance.id}
                      className="flex items-center justify-between p-3 bg-white rounded-lg border border-red-200"
                    >
                      <span className="font-medium text-red-700">
                        {insurance.nom}
                      </span>
                      <button
                        onClick={() => unmarkInsuranceForDeletion(insurance)}
                        className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors"
                        title="Annuler la suppression"
                      >
                        <CheckCircle className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* Ajout de nouvelles assurances */}
              <div className="space-y-3">
                <div className="flex flex-col gap-2">
                  <div className="flex-1">
                    <MultiSelect
                      itemsList={availableInsurances}
                      selectedItems={selectedInsurances}
                      handleItemsChange={handleInsurancesChange}
                      register={register}
                      fieldName="selectedInsurances"
                      displayProperty="nom"
                      dropdownId="insurances-dropdown"
                      placeholderText="Sélectionner des assurances à ajouter"
                      selectedText={(count) =>
                        `${count} assurance(s) sélectionnée(s)`
                      }
                      allSelectedText="Toutes les assurances disponibles sont déjà ajoutées"
                      selectedItemsTitle="Assurances sélectionnées"
                    />
                  </div>
                  <button
                    onClick={handleAddInsurances}
                    disabled={
                      selectedInsurances.length === 0 || isAddingInsurance
                    }
                    className="px-4 py-2 bg-meddoc-primary text-white rounded-lg ml-auto hover:bg-meddoc-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
                    title="Ajouter les assurances sélectionnées"
                  >
                    <Plus className="h-4 w-4" />
                    {isAddingInsurance ? "Ajout..." : "Ajouter"}
                  </button>
                </div>

                {/* Affichage des assurances sélectionnées */}
                {selectedInsurances.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">
                      Assurances à ajouter :
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {selectedInsurances.map((insurance) => (
                        <span
                          key={insurance.id}
                          className="inline-flex items-center px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200"
                        >
                          {insurance.nom}
                          <button
                            onClick={() => {
                              setSelectedInsurances((prev) =>
                                prev.filter((ins) => ins.id !== insurance.id)
                              );
                            }}
                            className="ml-2 text-blue-500 hover:text-blue-700"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        }
      >
        {/* Contenu en mode lecture */}
        <div className="space-y-6">
          {/* Modes de paiement */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <CreditCard size={18} className="text-meddoc-primary" />
              <h3 className="text-lg font-medium text-gray-800">
                Modes de paiement acceptés
              </h3>
            </div>
            {paymentMethods ? (
              <div className="flex flex-wrap gap-2">
                {paymentMethods.split(",").map((method, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 bg-green-50 text-green-700 rounded-full text-sm border border-green-200"
                  >
                    {method.trim()}
                  </span>
                ))}
              </div>
            ) : (
              <p className="text-gray-400 italic">
                Aucun mode de paiement spécifié
              </p>
            )}
          </div>

          {/* Assurances */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Shield size={18} className="text-meddoc-primary" />
              <h3 className="text-lg font-medium text-gray-800">
                Assurances acceptées
              </h3>
            </div>
            {insurances.length > 0 ? (
              <div className="space-y-2">
                {insurances.map((insurance) => (
                  <div
                    key={insurance.id}
                    className="p-3 bg-blue-50 rounded-lg border border-blue-200"
                  >
                    <span className="font-medium text-blue-900">
                      {insurance.nom}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-400 italic">Aucune assurance spécifiée</p>
            )}
          </div>
        </div>
      </EditableSection>
      {/* Modale de confirmation pour les suppressions différées */}
      <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={async () => {
          let success = true;
          // Exécuter les suppressions après confirmation
          for (const id of pendingDeletionIds) {
            try {
              const delOk = await onRemoveInsurance(id);
              success = success && delOk;
            } catch (e) {
              success = false;
            }
          }
          // Réinitialiser la liste des suppressions en attente
          setPendingDeletionIds(new Set());
          setInsurancesToDelete([]);
          setIsConfirmModalOpen(false);
          // Finaliser la sauvegarde
          return success;
        }}
        message={`Voulez-vous vraiment supprimer ${pendingDeletionIds.size} assurance(s) ?`}
        title="Supprimer les assurances sélectionnées"
        confirmButtonText="Supprimer"
        cancelButtonText="Annuler"
      />
    </>
  );
};

export default PaymentAndInsuranceCRUDSection;
