import React from "react";
import { FileText } from "lucide-react";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import {
  presentationSchema,
  PresentationFormData,
} from "@/shared/schemas/ProfessionalProfileSchemas";
import <PERSON>Field from "@/presentation/components/common/ui/FormField.tsx";
import Text<PERSON>reaField from "@/presentation/components/common/ui/TextAreaField.tsx";

/**
 * Interface pour les propriétés du composant PresentationEditSection
 */
interface PresentationEditSectionProps {
  /** Texte de présentation générale du professionnel */
  presentationGenerale: string;
  /** Fonction appelée lors de la sauvegarde de la présentation */
  onSave: (presentation: string) => Promise<boolean>;
}

/**
 * Composant de section présentation éditable
 *
 * Ce composant utilise le nouveau système d'édition par section pour
 * permettre la modification de la présentation générale du professionnel.
 * Il gère automatiquement l'état d'édition, la validation et la sauvegarde.
 *
 * @example
 * ```tsx
 * <PresentationEditSection
 *   presentationGenerale="Médecin spécialisé en cardiologie..."
 *   onSave={async (presentation) => await updatePresentation(presentation)}
 * />
 * ```
 *
 * @param props - Les propriétés du composant
 * @returns Élément JSX représentant la section présentation
 */
const PresentationEditSection: React.FC<PresentationEditSectionProps> = ({
  presentationGenerale,
  onSave,
}) => {
  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    register,
    formState: { errors },
    save,
    cancel,
    isSaving,
  } = useSectionForm({
    schema: presentationSchema,
    defaultValues: { presentation: presentationGenerale || "" },
    onSave: async (data) => await onSave(data.presentation),
    sectionName: "présentation",
  });

  return (
    <EditableSection
      title="Présentation"
      icon={FileText}
      onSave={save}
      onCancel={cancel}
      isSaving={isSaving}
      editContent={
        <div>
          <TextAreaField
            id="presentation"
            label="Présentation"
            placeholder="Décrivez votre parcours, vos expériences, les langues parlées, les pathologies prises en charge..."
            icon={FileText}
            register={register}
            error={errors.presentation}
            showCharCount
          />
        </div>
      }
    >
      {/* Contenu en mode lecture */}
      <div className="prose prose-gray max-w-none">
        {presentationGenerale ? (
          <p className="text-gray-700 leading-relaxed whitespace-pre-wrap dark:text-white">
            {presentationGenerale}
          </p>
        ) : (
          <p className="text-gray-400 italic dark:text-white">
            Aucune présentation renseignée. Cliquez sur l'icône d'édition pour
            ajouter une présentation.
          </p>
        )}
      </div>
    </EditableSection>
  );
};

export default PresentationEditSection;
