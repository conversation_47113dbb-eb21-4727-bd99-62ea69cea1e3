import React from "react";
import { Settings } from "lucide-react";
import { professionnels_types_consultation_enum } from "@/domain/models/enums";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import { servicesSchema } from "@/shared/schemas/ProfessionalProfileSchemas";
import FormField from "@/presentation/components/common/ui/FormField.tsx";
import { ServicesData } from "@/presentation/hooks/professional/use-professional-service-section.ts";

/**
 * Interface pour les propriétés du composant ServicesEditSection
 */
interface ServicesEditSectionProps {
  /** Type de consultation sélectionné */
  typesConsultation: professionnels_types_consultation_enum;
  /** Indique si le professionnel accepte de nouveaux patients */
  nouveauPatientAcceptes: boolean;
  /** Fonction appelée lors de la sauvegarde des services */
  onSave: (servicesData: Partial<ServicesData>) => Promise<boolean>;
}

/**
 * Composant de section services éditable
 *
 * Ce composant utilise le nouveau système d'édition par section pour
 * permettre la modification des services proposés par le professionnel.
 * Il gère automatiquement l'état d'édition, la validation et la sauvegarde.
 *
 * @example
 * ```tsx
 * <ServicesEditSection
 *   typesConsultation={professionnels_types_consultation_enum.EN_CABINET}
 *   nouveauPatientAcceptes={true}
 *   onSave={async (data) => await updateServices(data)}
 * />
 * ```
 *
 * @param props - Les propriétés du composant
 * @returns Élément JSX représentant la section services
 */
const ServicesEditSection: React.FC<ServicesEditSectionProps> = ({
  typesConsultation,
  nouveauPatientAcceptes,
  onSave,
}) => {
  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    register,
    formState: { errors },
    save,
    cancel,
    isSaving,
  } = useSectionForm({
    schema: servicesSchema,
    defaultValues: {
      types_consultation: typesConsultation,
      nouveau_patient_acceptes: nouveauPatientAcceptes,
    },
    onSave: async (data) => await onSave(data),
    sectionName: "informations de contact",
  });

  /**
   * Obtient le libellé du type de consultation
   */
  const getConsultationTypeLabel = (
    type: professionnels_types_consultation_enum
  ) => {
    switch (type) {
      case professionnels_types_consultation_enum.EN_CABINET:
        return "En cabinet";
      case professionnels_types_consultation_enum.A_DOMICILE:
        return "À domicile";
      case professionnels_types_consultation_enum.URGENTISTE:
        return "Urgentiste";
      default:
        return "Non défini";
    }
  };
  return (
    <EditableSection
      title="Services"
      icon={Settings}
      onSave={save}
      onCancel={cancel}
      isSaving={isSaving}
      editContent={
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            id="types_consultation"
            label="Types de consultation"
            type="select"
            placeholder="Sélectionnez un type de consultation"
            icon={Settings}
            register={register}
            options={[
              {
                value: professionnels_types_consultation_enum.EN_CABINET,
                label: "En cabinet",
              },
              {
                value: professionnels_types_consultation_enum.A_DOMICILE,
                label: "À domicile",
              },
              {
                value: professionnels_types_consultation_enum.URGENTISTE,
                label: "Urgentiste",
              },
            ]}
          />

          <div className="flex items-center">
            <input
              type="checkbox"
              id="nouveauPatientAcceptes"
              {...register("nouveau_patient_acceptes")}
              className="h-4 w-4 text-meddoc-primary focus:ring-meddoc-primary border-gray-300 rounded"
            />
            <label
              htmlFor="nouveauPatientAcceptes"
              className="ml-2 block text-sm text-gray-600"
            >
              Accepte de nouveaux patients
            </label>
          </div>
        </div>
      }
    >
      {/* Contenu en mode lecture */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">
            Type de consultation
          </h3>
          <p className="text-gray-900 dark:text-white">
            {getConsultationTypeLabel(typesConsultation)}
          </p>
        </div>
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">
            Nouveaux patients
          </h3>
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${nouveauPatientAcceptes ? "bg-green-500" : "bg-red-500"}`}
            />
            <span className="text-gray-900 dark:text-white">
              {nouveauPatientAcceptes ? "Acceptés" : "Non acceptés"}
            </span>
          </div>
        </div>
      </div>
    </EditableSection>
  );
};

export default ServicesEditSection;
