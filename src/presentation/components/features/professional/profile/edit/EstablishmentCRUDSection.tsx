import { EtablissementProfessionnel } from "@/domain/models/EtablissementProfessionnel.ts";
import React, { useState } from "react";
import { Building2, User, Users } from "lucide-react";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import {
  establishmentSchema,
  EstablishmentFormData,
} from "@/shared/schemas/ProfessionalProfileSchemas";
import SimpleLeafletMap from "../../../leaflet/SimpleLeafletMap.tsx";
import LocationPickerMap from "../../../leaflet/LocationPickerMap.tsx";

/**
 * Interface pour les propriétés du composant EstablishmentCRUDSection
 */
interface EstablishmentCRUDSectionProps {
  etablishmentData: EtablissementProfessionnel;
  geolocation: string;
  /** Fonction appelée lors de la sauvegarde des informations d'établissement */
  onSave: (
    etablishmentId: number,
    establishmentData: EstablishmentFormData
  ) => Promise<boolean>;
  handleGeolocationChange: (value: string) => void;
}

/**
 * Composant d'informations de l'établissement professionnel avec CRUD
 *
 * Ce composant utilise react-hook-form avec Zod pour la validation
 * et le système d'édition par section pour une UX cohérente.
 * Il gère les informations de l'établissement avec validation complète.
 *
 * @example
 * ```tsx
 * <EstablishmentCRUDSection
 *   etablishmentData={{
 *     nom_etablissement: "Cabinet Médical Central",
 *     nom_responsable: "Dupont",
 *     prenom_responsable: "Marie",
 *     equipe: "Équipe de 5 professionnels spécialisés..."
 *   }}
 *   onSave={async (data) => await saveEstablishment(data)}
 * />
 * ```
 *
 * @param props - Les propriétés du composant
 * @returns Élément JSX représentant les informations de l'établissement
 */
const EstablishmentCRUDSection: React.FC<EstablishmentCRUDSectionProps> = ({
  etablishmentData,
  geolocation,
  handleGeolocationChange,
  onSave,
}) => {
  const [selectedGeolocation, setSelectedGeolocation] = useState(geolocation);

  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    register,
    formState: { errors },
    save,
    cancel,
    isSaving,
    watch,
  } = useSectionForm({
    schema: establishmentSchema,
    defaultValues: {
      nom_etablissement: etablishmentData.nom_etablissement || "",
      nom_responsable: etablishmentData.nom_responsable || "",
      prenom_responsable: etablishmentData.prenom_responsable || "",
      equipe: etablishmentData.equipe || "",
    },
    onSave: async (data) => {
      if (selectedGeolocation !== geolocation) {
        handleGeolocationChange(selectedGeolocation);
      }
      return await onSave(etablishmentData.id || 0, data);
    },
    sectionName: "informations de l'établissement",
  });

  // Surveille les valeurs actuelles
  const currentValues = watch();

  return (
    <EditableSection
      title="Informations de l'établissement"
      icon={Building2}
      onSave={save}
      onCancel={cancel}
      isSaving={isSaving}
      editContent={
        <div className="space-y-4">
          {/* Nom de l'établissement */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-2">
              <Building2 className="h-4 w-4 text-meddoc-primary" />
              Nom de l'établissement
            </label>
            <input
              type="text"
              {...register("nom_etablissement")}
              className={`block w-full rounded-lg border-gray-300 shadow-sm focus:border-meddoc-primary focus:ring-meddoc-primary transition-colors ${
                errors.nom_etablissement ? "border-red-500" : ""
              }`}
              placeholder="Nom de l'établissement professionnel"
            />
            {errors.nom_etablissement && (
              <p className="mt-1 text-sm text-red-600">
                {errors.nom_etablissement.message}
              </p>
            )}
          </div>

          {/* Responsable */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-2">
                <User className="h-4 w-4 text-meddoc-primary" />
                Nom du responsable
              </label>
              <input
                type="text"
                {...register("nom_responsable")}
                className={`block w-full rounded-lg border-gray-300 shadow-sm focus:border-meddoc-primary focus:ring-meddoc-primary transition-colors ${
                  errors.nom_responsable ? "border-red-500" : ""
                }`}
                placeholder="Nom du responsable"
              />
              {errors.nom_responsable && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.nom_responsable.message}
                </p>
              )}
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-2">
                <User className="h-4 w-4 text-meddoc-primary" />
                Prénom du responsable
              </label>
              <input
                type="text"
                {...register("prenom_responsable")}
                className={`block w-full rounded-lg border-gray-300 shadow-sm focus:border-meddoc-primary focus:ring-meddoc-primary transition-colors ${
                  errors.prenom_responsable ? "border-red-500" : ""
                }`}
                placeholder="Prénom du responsable"
              />
              {errors.prenom_responsable && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.prenom_responsable.message}
                </p>
              )}
            </div>
          </div>

          {/* Équipe */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-2">
              <Users className="h-4 w-4 text-meddoc-primary" />
              Description de l'équipe
            </label>
            <textarea
              {...register("equipe")}
              rows={4}
              className={`block w-full rounded-lg border-gray-300 shadow-sm focus:border-meddoc-primary focus:ring-meddoc-primary transition-colors resize-none ${
                errors.equipe ? "border-red-500" : ""
              }`}
              placeholder="Décrivez votre équipe, les spécialités, l'organisation..."
            />
            {errors.equipe && (
              <p className="mt-1 text-sm text-red-600">
                {errors.equipe.message}
              </p>
            )}
            <div className="mt-1 text-sm text-gray-500">
              {(currentValues.equipe || "").length}/1000 caractères
            </div>
          </div>
          <LocationPickerMap
            onChange={setSelectedGeolocation}
            value={geolocation}
            key={geolocation}
          />
        </div>
      }
    >
      {/* Contenu en mode lecture */}
      <div className="space-y-4">
        {/* Nom de l'établissement */}
        <div>
          <h3 className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-1">
            <Building2 className="h-4 w-4 text-meddoc-primary" />
            Nom de l'établissement
          </h3>
          <p className="text-gray-900">
            {etablishmentData.nom_etablissement || "Non renseigné"}
          </p>
        </div>

        {/* Responsable */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-1">
              <User className="h-4 w-4 text-meddoc-primary" />
              Responsable
            </h3>
            <p className="text-gray-900">
              {etablishmentData.nom_responsable &&
              etablishmentData.prenom_responsable
                ? `${etablishmentData.prenom_responsable} ${etablishmentData.nom_responsable}`
                : "Non renseigné"}
            </p>
          </div>
        </div>

        {/* Équipe */}
        <div>
          <h3 className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-1">
            <Users className="h-4 w-4 text-meddoc-primary" />
            Description de l'équipe
          </h3>
          {etablishmentData.equipe ? (
            <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
              {etablishmentData.equipe}
            </p>
          ) : (
            <p className="text-gray-400 italic">
              Aucune description d'équipe renseignée
            </p>
          )}
        </div>
        {/* <LazyLocationPickerMap
          value={geolocation || ""}
          onChange={handleGeolocationChange}
          isVisible={true}
          name="geolocation"
        /> */}
        <SimpleLeafletMap
          geolocation={geolocation}
          etablishment={[etablishmentData]}
          className="h-64"
        />

        {/* Affichage des coordonnées */}
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-500">
            Coordonnées: {selectedGeolocation}
          </p>
        </div>
      </div>
    </EditableSection>
  );
};

export default EstablishmentCRUDSection;
