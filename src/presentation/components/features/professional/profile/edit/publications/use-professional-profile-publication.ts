import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  PublicationFormData,
  publicationSchema,
} from "@/shared/schemas/ProfessionalProfileSchemas";
import { zodResolver } from "@hookform/resolvers/zod";

/**
 * Hook dédié à la gestion des publications professionnelles (UI + RHF)
 * - G<PERSON> l'ajout, l'édition inline et la suppression via des handlers passés en paramètre
 * - Fournit l'API RHF (register, errors, handleSubmit)
 * - Gère les toasts et l'état de chargement
 */
const useProfessionalProfilePublication = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [editingPublicationId, setEditingPublicationId] = useState<
    number | null
  >(null);

  const {
    register,
    formState: { errors },
    handleSubmit,
    reset,
    setValue,
  } = useForm<PublicationFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(publicationSchema),
    defaultValues: {
      titre: "",
      lien: "",
      annee: "",
      description: "",
    },
  });

  return {
    // states
    isLoading,
    isAdding,
    setIsAdding,
    editingPublicationId,
    setEditingPublicationId,
    // RHF
    register,
    errors,
    handleSubmit,
    reset,
    setValue,
  };
};

export default useProfessionalProfilePublication;
