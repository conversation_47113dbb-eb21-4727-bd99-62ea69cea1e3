import React from "react";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { PublicationFormData } from "@/shared/schemas/ProfessionalProfileSchemas";
import PublicationEditRow from "./PublicationEditRow";
import { Calendar, Edit3, ExternalLink, X } from "lucide-react";

interface PublicationsListProps {
  publications: PublicationProfessionnel[];
  editingPublicationId: number | null;
  onStartEdit: (p: PublicationProfessionnel) => void;
  onApplyEdit: (id: number) => React.FormEventHandler<HTMLFormElement>;
  onCancelEdit: () => void;
  onDelete: (id: number) => void;
  register: UseFormRegister<PublicationFormData>;
  errors: FieldErrors<PublicationFormData>;
}

const PublicationsList: React.FC<PublicationsListProps> = ({
  publications,
  editingPublicationId,
  onStartEdit,
  onApplyEdit,
  onCancelEdit,
  onDelete,
  register,
  errors,
}) => {
  if (publications.length === 0) return null;

  return (
    <div>
      <h3 className="text-lg font-medium text-gray-800 mb-3">
        Publications existantes
      </h3>
      <div className="space-y-4">
        {publications.map((publication) => (
          <div
            key={publication.id}
            className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
          >
            {editingPublicationId === publication.id ? (
              <PublicationEditRow
                register={register}
                errors={errors}
                onEdit={onApplyEdit(publication.id)}
                onCancel={onCancelEdit}
              />
            ) : (
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-2">
                    {publication.titre}
                  </h4>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {publication.annee}
                    </div>
                    {publication.lien && (
                      <a
                        href={publication.lien}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 text-meddoc-primary hover:text-meddoc-primary/80"
                      >
                        <ExternalLink className="h-4 w-4" />
                        Voir la publication
                      </a>
                    )}
                  </div>
                  {publication.description && (
                    <p className="text-sm text-gray-700">
                      {publication.description}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-1 ml-4">
                  <button
                    onClick={() => onStartEdit(publication)}
                    className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                    title="Modifier"
                  >
                    <Edit3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => onDelete(publication.id)}
                    className="p-1 text-red-600 hover:bg-red-100 rounded"
                    title="Supprimer"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PublicationsList;
