import React from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { PublicationFormData } from "@/shared/schemas/ProfessionalProfileSchemas";
import Button from "@/presentation/components/common/Button/Button.tsx";
import <PERSON><PERSON>ield from "@/presentation/components/common/ui/FormField";
import <PERSON><PERSON><PERSON><PERSON>ield from "@/presentation/components/common/ui/TextAreaField";
import { FileText, ExternalLink, Calendar } from "lucide-react";

interface PublicationAddFormProps {
  register: UseFormRegister<PublicationFormData>;
  errors: FieldErrors<PublicationFormData>;
  onSubmit: React.FormEventHandler<HTMLFormElement>;
  onCancel: () => void;
  submitting?: boolean;
}

const PublicationAddForm: React.FC<PublicationAddFormProps> = ({
  register,
  errors,
  onSubmit,
  onCancel,
  submitting = false,
}) => {
  return (
    <form className="border-t pt-6" onSubmit={onSubmit}>
      <h3 className="text-lg font-medium text-gray-800 mb-3">
        Ajouter une nouvelle publication
      </h3>
      <div className="space-y-4">
        <FormField
          id="titre"
          label="Titre de la publication"
          placeholder="Ex: Étude sur l'efficacité des traitements..."
          type="text"
          icon={FileText}
          register={register}
          required
          error={errors.titre}
          inputMode="text"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            id="lien"
            label="Lien vers la publication (optionnel)"
            placeholder="https://..."
            type="text"
            icon={ExternalLink}
            register={register}
            error={errors.lien}
            inputMode="url"
          />

          <FormField
            id="annee"
            label="Année de publication"
            placeholder="Ex: 2023"
            type="text"
            icon={Calendar}
            register={register}
            required
            error={errors.annee}
            inputMode="numeric"
            validation={{
              minLength: { value: 4, message: "L'année doit contenir 4 chiffres" },
              maxLength: { value: 4, message: "L'année doit contenir 4 chiffres" },
              validate: (value: string) => {
                if (!/^\d{4}$/.test(value)) return "Format attendu: AAAA";
                const year = Number(value);
                const current = new Date().getFullYear();
                if (year < 1900 || year > current + 1) {
                  return `L'année doit être comprise entre 1900 et ${current + 1}`;
                }
                return true;
              },
            }}
            onChange={(e) => {
              const onlyDigits = e.target.value.replace(/\D/g, "").slice(0, 4);
              // réinjecter la valeur nettoyée
              e.target.value = onlyDigits;
            }}
            helpText="Format AAAA. Seuls les chiffres sont autorisés."
          />
        </div>

        <TextAreaField
          id="description"
          label="Description ou résumé (optionnel)"
          placeholder="Résumé de la publication, contexte, résultats principaux..."
          icon={FileText}
          register={register}
          error={errors.description}
          rows={4}
        />
      </div>
      <div className="flex justify-end gap-2 mt-4">
        <Button type="submit" disabled={submitting}>
          {submitting ? "Enregistrement..." : "Enregistrer"}
        </Button>
        <Button
          type="button"
          className="bg-gray-500 hover:bg-gray-600"
          onClick={onCancel}
          disabled={submitting}
        >
          Annuler
        </Button>
      </div>
    </form>
  );
};

export default PublicationAddForm;
