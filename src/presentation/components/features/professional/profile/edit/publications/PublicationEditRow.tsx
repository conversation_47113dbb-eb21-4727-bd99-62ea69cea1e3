import React from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { PublicationFormData } from "@/shared/schemas/ProfessionalProfileSchemas";
import { Check, X, ExternalLink, Calendar, FileText } from "lucide-react";
import <PERSON><PERSON>ield from "@/presentation/components/common/ui/FormField";
import Text<PERSON><PERSON><PERSON>ield from "@/presentation/components/common/ui/TextAreaField";

interface PublicationEditRowProps {
  register: UseFormRegister<PublicationFormData>;
  errors: FieldErrors<PublicationFormData>;
  onEdit: React.FormEventHandler<HTMLFormElement>;
  onCancel: () => void;
}

const PublicationEditRow: React.FC<PublicationEditRowProps> = ({
  register,
  errors,
  onEdit,
  onCancel,
}) => {
  return (
    <form className="space-y-3" onSubmit={onEdit}>
      <FormField
        id="titre"
        label="Titre de la publication"
        placeholder="Titre de la publication"
        type="text"
        icon={FileText}
        register={register}
        required
        error={errors.titre}
        inputMode="text"
      />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <FormField
          id="lien"
          label="Lien (optionnel)"
          placeholder="Lien (optionnel)"
          type="text"
          icon={ExternalLink}
          register={register}
          error={errors.lien}
          inputMode="url"
        />
        <FormField
          id="annee"
          label="Année (YYYY)"
          placeholder="Année (YYYY)"
          type="text"
          icon={Calendar}
          register={register}
          required
          error={errors.annee}
          inputMode="numeric"
          validation={{
            minLength: { value: 4, message: "L'année doit contenir 4 chiffres" },
            maxLength: { value: 4, message: "L'année doit contenir 4 chiffres" },
            validate: (value: string) => {
              if (!/^\d{4}$/.test(value)) return "Format attendu: AAAA";
              const year = Number(value);
              const current = new Date().getFullYear();
              if (year < 1900 || year > current + 1) {
                return `L'année doit être comprise entre 1900 et ${current + 1}`;
              }
              return true;
            },
          }}
          onChange={(e) => {
            const onlyDigits = e.target.value.replace(/\D/g, "").slice(0, 4);
            e.target.value = onlyDigits;
          }}
          helpText="Format AAAA. Seuls les chiffres sont autorisés."
        />
      </div>
      <TextAreaField
        id="description"
        label="Description (optionnel)"
        placeholder="Description (optionnel)"
        icon={FileText}
        register={register}
        error={errors.description}
        rows={3}
      />
      <div className="flex items-center gap-2">
        <button
          type="submit"
          className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center gap-1"
        >
          <Check className="h-4 w-4" />
          Sauvegarder
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 flex items-center gap-1"
        >
          <X className="h-4 w-4" />
          Annuler
        </button>
      </div>
    </form>
  );
};

export default PublicationEditRow;
