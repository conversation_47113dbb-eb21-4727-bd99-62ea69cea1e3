import React, { useState, useRef } from "react";
import { Camera, Upload, X, User, Check, AlertCircle } from "lucide-react";
import EditableSection from "./EditableSection";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import { z } from "zod";
import ConfirmActionModal from "@/presentation/components/common/Modal/ConfirmActionModal.tsx";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";
import { Photo } from "@/domain/models/Photo.ts";

/**
 * Schéma de validation pour l'upload de photo
 */
const photoSchema = z.object({
  photo: z.any().optional(),
});

type PhotoFormData = z.infer<typeof photoSchema>;

/**
 * Interface pour les propriétés du composant ProfileEditPhotoModern
 */
interface ProfileEditPhotoModernProps {
  /** URL de la photo de profil actuelle */
  photo: Photo | null;
  /** Nom complet du professionnel pour l'alt text */
  fullName: string;
  /** Fonction appelée lors de l'upload d'une nouvelle photo */
  onPhotoUpload: (file: File) => Promise<boolean>;
  /** Fonction appelée lors de la suppression de la photo */
  onPhotoRemove?: (photoId: number) => Promise<boolean>;
  /** Taille maximale du fichier en MB */
  maxSizeMB?: number;
}

/**
 * Composant moderne d'édition de photo de profil
 *
 * Ce composant utilise l'architecture EditableSection pour une cohérence
 * visuelle avec les autres sections CRUD. Il gère l'upload, la prévisualisation
 * et la validation des photos de profil avec une interface intuitive.
 *
 * @example
 * ```tsx
 * <ProfileEditPhotoModern
 *   photoUrl="/path/to/photo.jpg"
 *   fullName="Dr. Jean Dupont"
 *   onPhotoUpload={async (file) => await uploadPhoto(file)}
 *   onPhotoRemove={async () => await removePhoto()}
 *   maxSizeMB={5}
 * />
 * ```
 */
const ProfileEditPhotoModern: React.FC<ProfileEditPhotoModernProps> = ({
  photo,
  fullName,
  onPhotoUpload,
  onPhotoRemove,
  maxSizeMB = 5,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    formState: { errors },
    save,
    cancel,
    isSaving,
  } = useSectionForm({
    schema: photoSchema,
    defaultValues: { photo: null },
    onSave: async () => {
      if (selectedFile) {
        const success = await onPhotoUpload(selectedFile);
        if (success) {
          setSelectedFile(null);
          setPreviewUrl(null);
          setUploadError(null);
        }
        return success;
      }
      return true;
    },
    sectionName: "photo de profil",
  });
  const [isModalOpen, setIsModalOpen] = useState(false);

  /**
   * Valide un fichier image
   */
  const validateFile = (file: File): string | null => {
    // Vérifier le type de fichier
    if (!file.type.startsWith("image/")) {
      return "Type de fichier non supporté. Utilisez JPG, PNG ou WebP.";
    }

    // Vérifier la taille
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return `Taille trop importante (${(file.size / 1024 / 1024).toFixed(1)}MB). Maximum ${maxSizeMB}MB.`;
    }

    return null;
  };

  /**
   * Gère la sélection d'un fichier
   */
  const handleFileSelection = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    const error = validateFile(file);
    if (error) {
      setUploadError(error);
      setSelectedFile(null);
      setPreviewUrl(null);
      return;
    }

    setUploadError(null);
    setSelectedFile(file);

    // Créer une URL de prévisualisation
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    // Nettoyer l'input
    event.target.value = "";
  };

  /**
   * Gère le clic sur le bouton de sélection
   */
  const handleSelectClick = () => {
    fileInputRef.current?.click();
  };

  /**
   * Annule la sélection
   */
  const handleCancelSelection = () => {
    cancel();
    setSelectedFile(null);
    setPreviewUrl(null);
    setUploadError(null);
  };

  /**
   * Supprime la photo actuelle
   */
  const handleRemovePhoto = async (photoId: number) => {
    if (onPhotoRemove) {
      const success = await onPhotoRemove(photoId);
      if (success) {
        setIsModalOpen(false);
      }
      return success;
    }
  };

  return (
    <>
      <EditableSection
        title="Photo de profil"
        icon={Camera}
        onSave={save}
        onCancel={handleCancelSelection}
        isSaving={isSaving}
        editContent={
          <div className="space-y-6">
            {/* Photo actuelle */}
            <div className="flex flex-col items-center">
              <div className="relative">
                <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-100 border-4 border-white shadow-lg">
                  {photo?.path ? (
                    <img
                      src={photo?.path}
                      alt={`Photo de profil de ${fullName}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-200">
                      <User className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>
                {photo?.path && onPhotoRemove && (
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                    title="Supprimer la photo"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-2 text-center">
                Photo actuelle
              </p>
            </div>

            {/* Nouvelle photo sélectionnée */}
            {selectedFile && previewUrl && (
              <div className="flex flex-col items-center">
                <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-100 border-4 border-green-200 shadow-lg">
                  <img
                    src={previewUrl}
                    alt="Aperçu de la nouvelle photo"
                    className="w-full h-full object-cover"
                  />
                </div>
                <p className="text-sm text-green-600 mt-2 text-center font-medium">
                  Nouvelle photo sélectionnée
                </p>
                <p className="text-xs text-gray-500 text-center">
                  {selectedFile.name} (
                  {(selectedFile.size / 1024 / 1024).toFixed(1)}MB)
                </p>
              </div>
            )}

            {/* Zone d'upload */}
            <div className="text-center">
              <div
                onClick={handleSelectClick}
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 cursor-pointer hover:border-meddoc-primary hover:bg-meddoc-primary/5 transition-colors"
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-lg font-medium text-gray-700 mb-2">
                  Cliquez pour sélectionner une photo
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  ou glissez-déposez votre fichier ici
                </p>
                <div className="text-xs text-gray-400">
                  <p>• Formats acceptés : JPG, PNG, WebP</p>
                  <p>• Taille maximale : {maxSizeMB}MB</p>
                  <p>• Recommandé : photo carrée, minimum 200x200px</p>
                </div>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelection}
                className="hidden"
              />
            </div>

            {/* Erreur d'upload */}
            {uploadError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-red-800 mb-1">
                      Erreur de validation
                    </h4>
                    <p className="text-sm text-red-700">{uploadError}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        }
      >
        {/* Contenu en mode lecture */}
        <div className="flex flex-col items-center text-center">
          <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-100 border-2 border-gray-200 shadow-sm mb-4">
            {photo?.path ? (
              <img
                src={photo?.path}
                alt={`Photo de profil de ${fullName}`}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-200">
                <User className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>

          {photo?.path ? (
            <p className="text-sm text-gray-600">Photo de profil définie</p>
          ) : (
            <div>
              <p className="text-sm text-gray-500 mb-2">
                Aucune photo de profil
              </p>
              <p className="text-xs text-gray-400">
                Cliquez sur l'icône d'édition pour ajouter une photo
              </p>
            </div>
          )}
        </div>
      </EditableSection>
      <ConfirmationModal
        isOpen={isModalOpen}
        message="Êtes-vous sûr de vouloir supprimer votre photo de profil ?"
        onClose={() => setIsModalOpen(false)}
        onConfirm={async () => {
          await handleRemovePhoto(photo.id);
          handleCancelSelection();
        }}
        cancelButtonText="Annuler"
        confirmButtonText="Supprimer"
        loading={isSaving}
      />
    </>
  );
};

export default ProfileEditPhotoModern;
