import React, { useState } from "react";
import { ExperienceProfessionnel } from "@/domain/models";
import MultiFieldList from "@/presentation/components/common/MultiFieldList";
import { X } from "lucide-react";

interface ExperiencesListProps {
  experiences: ExperienceProfessionnel[];
  onExperiencesChange: (experiences: ExperienceProfessionnel[]) => void;
}

// Composant de formulaire pour ajouter une expérience
const ExperienceForm: React.FC<{
  onAdd: (experience: ExperienceProfessionnel) => void;
  isFormVisible: boolean;
  setIsFormVisible: (visible: boolean) => void;
}> = ({ onAdd, isFormVisible, setIsFormVisible }) => {
  const [newExperience, setNewExperience] = useState<
    Omit<ExperienceProfessionnel, "id" | "id_professionnel">
  >({
    poste: "",
    etablissement: "",
    date_debut: "",
    date_fin: "",
    description: "",
    est_actuel: false,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onAdd({
      ...newExperience,
      id: Date.now(), // Temporaire, sera remplacé par l'ID de la base de données
      id_professionnel: 0, // Temporaire, sera remplacé par l'ID du professionnel
    });
    setNewExperience({
      poste: "",
      etablissement: "",
      date_debut: "",
      date_fin: "",
      description: "",
      est_actuel: false,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-medium">Ajouter une expérience</h4>
        <button
          type="button"
          onClick={() => setIsFormVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          <X size={18} />
        </button>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Poste *
        </label>
        <input
          type="text"
          value={newExperience.poste}
          onChange={(e) =>
            setNewExperience({ ...newExperience, poste: e.target.value })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Établissement *
        </label>
        <input
          type="text"
          value={newExperience.etablissement}
          onChange={(e) =>
            setNewExperience({
              ...newExperience,
              etablissement: e.target.value,
            })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Date de début *
        </label>
        <input
          type="text"
          value={newExperience.date_debut}
          onChange={(e) =>
            setNewExperience({
              ...newExperience,
              date_debut: e.target.value,
            })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          placeholder="ex: 2020"
          required
        />
      </div>

      <div className="flex items-center mb-3">
        <input
          type="checkbox"
          id="est_actuel"
          checked={newExperience.est_actuel}
          onChange={(e) =>
            setNewExperience({
              ...newExperience,
              est_actuel: e.target.checked,
              date_fin: e.target.checked ? "" : newExperience.date_fin,
            })
          }
          className="mr-2"
        />
        <label
          htmlFor="est_actuel"
          className="text-sm font-medium text-gray-700"
        >
          Poste actuel
        </label>
      </div>

      {!newExperience.est_actuel && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date de fin
          </label>
          <input
            type="text"
            value={newExperience.date_fin}
            onChange={(e) =>
              setNewExperience({
                ...newExperience,
                date_fin: e.target.value,
              })
            }
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="ex: 2023"
          />
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          value={newExperience.description}
          onChange={(e) =>
            setNewExperience({
              ...newExperience,
              description: e.target.value,
            })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          rows={3}
        />
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="bg-meddoc-primary text-white px-4 py-2 rounded-md hover:bg-meddoc-fonce"
        >
          Ajouter
        </button>
      </div>
    </form>
  );
};

const ExperiencesList: React.FC<ExperiencesListProps> = ({
  experiences,
  onExperiencesChange,
}) => {
  // Fonction pour rendre une expérience
  const renderExperience = (experience: ExperienceProfessionnel) => (
    <div>
      <h4 className="font-medium">{experience.poste}</h4>
      <p className="text-sm text-gray-600">
        {experience.etablissement} - {experience.date_debut}
        {experience.est_actuel
          ? " à aujourd'hui"
          : experience.date_fin
            ? ` à ${experience.date_fin}`
            : ""}
      </p>
      {experience.description && (
        <p className="text-sm text-gray-500 mt-1">{experience.description}</p>
      )}
    </div>
  );

  // Fonction pour rendre le formulaire
  const renderExperienceForm = (
    onAdd: (experience: ExperienceProfessionnel) => void,
    isFormVisible: boolean,
    setIsFormVisible: (visible: boolean) => void
  ) => {
    return (
      <ExperienceForm
        onAdd={onAdd}
        isFormVisible={isFormVisible}
        setIsFormVisible={setIsFormVisible}
      />
    );
  };

  return (
    <MultiFieldList<ExperienceProfessionnel>
      items={experiences}
      onItemsChange={onExperiencesChange}
      renderItem={renderExperience}
      renderForm={renderExperienceForm}
      emptyMessage="Aucune expérience ajoutée"
      addButtonLabel="Ajouter une expérience"
    />
  );
};

export default ExperiencesList;
