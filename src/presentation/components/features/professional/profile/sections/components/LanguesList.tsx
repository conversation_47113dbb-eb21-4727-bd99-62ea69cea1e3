import React, { useState } from "react";
import { LangueParleeProfessionnel } from "@/domain/models";
import MultiFieldList from "@/presentation/components/common/MultiFieldList";
import { X } from "lucide-react";

interface LanguesListProps {
  langues: LangueParleeProfessionnel[];
  onLanguesChange: (langues: LangueParleeProfessionnel[]) => void;
}

// Composant de formulaire pour ajouter une langue
const LangueForm: React.FC<{
  onAdd: (langue: LangueParleeProfessionnel) => void;
  isFormVisible: boolean;
  setIsFormVisible: (visible: boolean) => void;
}> = ({ onAdd, isFormVisible, setIsFormVisible }) => {
  const [newLangue, setNewLangue] = useState<
    Omit<LangueParleeProfessionnel, "id" | "id_professionnel">
  >({
    nom_langue: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onAdd({
      ...newLangue,
      id: Date.now(), // Temporaire, sera remplacé par l'ID de la base de données
      id_professionnel: 0, // Temporaire, sera remplacé par l'ID du professionnel
    });
    setNewLangue({
      nom_langue: "",
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-medium">Ajouter une langue</h4>
        <button
          type="button"
          onClick={() => setIsFormVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          <X size={18} />
        </button>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Langue *
        </label>
        <input
          type="text"
          value={newLangue.nom_langue}
          onChange={(e) =>
            setNewLangue({ ...newLangue, nom_langue: e.target.value })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="bg-meddoc-primary text-white px-4 py-2 rounded-md hover:bg-meddoc-fonce"
        >
          Ajouter
        </button>
      </div>
    </form>
  );
};

const LanguesList: React.FC<LanguesListProps> = ({
  langues,
  onLanguesChange,
}) => {
  // Fonction pour rendre une langue
  const renderLangue = (langue: LangueParleeProfessionnel) => (
    <div>
      <p className="font-medium">{langue.nom_langue}</p>
    </div>
  );

  // Fonction pour rendre le formulaire
  const renderLangueForm = (
    onAdd: (langue: LangueParleeProfessionnel) => void,
    isFormVisible: boolean,
    setIsFormVisible: (visible: boolean) => void
  ) => {
    return (
      <LangueForm
        onAdd={onAdd}
        isFormVisible={isFormVisible}
        setIsFormVisible={setIsFormVisible}
      />
    );
  };

  return (
    <MultiFieldList<LangueParleeProfessionnel>
      items={langues}
      onItemsChange={onLanguesChange}
      renderItem={renderLangue}
      renderForm={renderLangueForm}
      emptyMessage="Aucune langue ajoutée"
      addButtonLabel="Ajouter une langue"
    />
  );
};

export default LanguesList;
