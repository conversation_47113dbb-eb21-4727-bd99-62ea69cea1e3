import React, { useState } from "react";
import { PublicationProfessionnel } from "@/domain/models";
import MultiFieldList from "@/presentation/components/common/MultiFieldList";
import { ExternalLink, X } from "lucide-react";

interface PublicationsListProps {
  publications: PublicationProfessionnel[];
  onPublicationsChange: (publications: PublicationProfessionnel[]) => void;
}

// Composant de formulaire pour ajouter une publication
const PublicationForm: React.FC<{
  onAdd: (publication: PublicationProfessionnel) => void;
  isFormVisible: boolean;
  setIsFormVisible: (visible: boolean) => void;
}> = ({ onAdd, isFormVisible, setIsFormVisible }) => {
  const [newPublication, setNewPublication] = useState<
    Omit<PublicationProfessionnel, "id" | "id_professionnel">
  >({
    titre: "",
    annee: "",
    description: "",
    lien: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onAdd({
      ...newPublication,
      id: Date.now(), // Temporaire, sera remplacé par l'ID de la base de données
      id_professionnel: 0, // Temporaire, sera remplacé par l'ID du professionnel
    });
    setNewPublication({
      titre: "",
      annee: "",
      description: "",
      lien: "",
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-medium">Ajouter une publication</h4>
        <button
          type="button"
          onClick={() => setIsFormVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          <X size={18} />
        </button>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Titre *
        </label>
        <input
          type="text"
          value={newPublication.titre}
          onChange={(e) =>
            setNewPublication({ ...newPublication, titre: e.target.value })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Année *
        </label>
        <input
          type="text"
          value={newPublication.annee}
          onChange={(e) =>
            setNewPublication({ ...newPublication, annee: e.target.value })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Lien
        </label>
        <input
          type="url"
          value={newPublication.lien}
          onChange={(e) =>
            setNewPublication({ ...newPublication, lien: e.target.value })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          placeholder="https://..."
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          value={newPublication.description}
          onChange={(e) =>
            setNewPublication({
              ...newPublication,
              description: e.target.value,
            })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          rows={3}
        />
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="bg-meddoc-primary text-white px-4 py-2 rounded-md hover:bg-meddoc-fonce"
        >
          Ajouter
        </button>
      </div>
    </form>
  );
};

const PublicationsList: React.FC<PublicationsListProps> = ({
  publications,
  onPublicationsChange,
}) => {
  // Fonction pour rendre une publication
  const renderPublication = (publication: PublicationProfessionnel) => (
    <div>
      <div className="flex items-start">
        <h4 className="font-medium flex-1">{publication.titre}</h4>
        {publication.lien && (
          <a
            href={publication.lien}
            target="_blank"
            rel="noopener noreferrer"
            className="text-meddoc-primary hover:text-meddoc-fonce ml-2"
            aria-label="Voir la publication"
          >
            <ExternalLink size={18} />
          </a>
        )}
      </div>
      <p className="text-sm text-gray-600">{publication.annee}</p>
      {publication.description && (
        <p className="text-sm text-gray-500 mt-1">{publication.description}</p>
      )}
    </div>
  );

  // Fonction pour rendre le formulaire
  const renderPublicationForm = (
    onAdd: (publication: PublicationProfessionnel) => void,
    isFormVisible: boolean,
    setIsFormVisible: (visible: boolean) => void
  ) => {
    return (
      <PublicationForm
        onAdd={onAdd}
        isFormVisible={isFormVisible}
        setIsFormVisible={setIsFormVisible}
      />
    );
  };

  return (
    <MultiFieldList<PublicationProfessionnel>
      items={publications}
      onItemsChange={onPublicationsChange}
      renderItem={renderPublication}
      renderForm={renderPublicationForm}
      emptyMessage="Aucune publication ajoutée"
      addButtonLabel="Ajouter une publication"
    />
  );
};

export default PublicationsList;
