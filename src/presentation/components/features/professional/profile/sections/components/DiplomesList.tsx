import React, { useState } from "react";
import { DiplomeProfessionnel } from "@/domain/models";
import MultiFieldList from "@/presentation/components/common/MultiFieldList";
import { X } from "lucide-react";

interface DiplomesListProps {
  diplomes: DiplomeProfessionnel[];
  onDiplomesChange: (diplomes: DiplomeProfessionnel[]) => void;
}

// Composant de formulaire pour ajouter un diplôme
const DiplomeForm: React.FC<{
  onAdd: (diplome: DiplomeProfessionnel) => void;
  isFormVisible: boolean;
  setIsFormVisible: (visible: boolean) => void;
}> = ({ onAdd, isFormVisible, setIsFormVisible }) => {
  const [newDiplome, setNewDiplome] = useState<
    Omit<DiplomeProfessionnel, "id" | "id_professionnel">
  >({
    titre: "",
    etablissement: "",
    annee: "",
    description: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onAdd({
      ...newDiplome,
      id: Date.now(), // Temporaire, sera remplacé par l'ID de la base de données
      id_professionnel: 0, // Temporaire, sera remplacé par l'ID du professionnel
    });
    setNewDiplome({
      titre: "",
      etablissement: "",
      annee: "",
      description: "",
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-medium">Ajouter un diplôme</h4>
        <button
          type="button"
          onClick={() => setIsFormVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          <X size={18} />
        </button>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Titre du diplôme *
        </label>
        <input
          type="text"
          value={newDiplome.titre}
          onChange={(e) =>
            setNewDiplome({ ...newDiplome, titre: e.target.value })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Établissement *
        </label>
        <input
          type="text"
          value={newDiplome.etablissement}
          onChange={(e) =>
            setNewDiplome({ ...newDiplome, etablissement: e.target.value })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Année d'obtention *
        </label>
        <input
          type="text"
          value={newDiplome.annee}
          onChange={(e) =>
            setNewDiplome({ ...newDiplome, annee: e.target.value })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          value={newDiplome.description}
          onChange={(e) =>
            setNewDiplome({ ...newDiplome, description: e.target.value })
          }
          className="w-full p-2 border border-gray-300 rounded-md"
          rows={3}
        />
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="bg-meddoc-primary text-white px-4 py-2 rounded-md hover:bg-meddoc-fonce"
        >
          Ajouter
        </button>
      </div>
    </form>
  );
};

const DiplomesList: React.FC<DiplomesListProps> = ({
  diplomes,
  onDiplomesChange,
}) => {
  // Fonction pour rendre un diplôme
  const renderDiplome = (diplome: DiplomeProfessionnel) => (
    <div>
      <h4 className="font-medium">{diplome.titre}</h4>
      <p className="text-sm text-gray-600">
        {diplome.etablissement} - {diplome.annee}
      </p>
      {diplome.description && (
        <p className="text-sm text-gray-500 mt-1">{diplome.description}</p>
      )}
    </div>
  );

  // Fonction pour rendre le formulaire
  const renderDiplomeForm = (
    onAdd: (diplome: DiplomeProfessionnel) => void,
    isFormVisible: boolean,
    setIsFormVisible: (visible: boolean) => void
  ) => {
    return (
      <DiplomeForm
        onAdd={onAdd}
        isFormVisible={isFormVisible}
        setIsFormVisible={setIsFormVisible}
      />
    );
  };

  return (
    <MultiFieldList<DiplomeProfessionnel>
      items={diplomes}
      onItemsChange={onDiplomesChange}
      renderItem={renderDiplome}
      renderForm={renderDiplomeForm}
      emptyMessage="Aucun diplôme ajouté"
      addButtonLabel="Ajouter un diplôme"
    />
  );
};

export default DiplomesList;
