import React, { memo } from "react";
import { CalendarOff, Clock5 } from "lucide-react";
import { motion } from "framer-motion";
import { ConvertLongDay } from "@/shared/utils/convertLongDay";
import { horaire_hebdomadaire } from "@/domain/models";

interface ScheduleSectionProps {
  horaire_hebdomadaire: horaire_hebdomadaire[] | null;
}

const ScheduleSectionComponent: React.FC<ScheduleSectionProps> = ({
  horaire_hebdomadaire,
}) => {
  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
      id="horaires"
      className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 overflow-hidden relative"
    >
      {/* Effet de fond décoratif */}
      <div className="absolute bottom-0 left-0 w-28 h-28 bg-gradient-to-br from-meddoc-secondary/5 to-meddoc-primary/5 rounded-full translate-y-14 -translate-x-14"></div>

      <div className="flex items-start gap-3 relative z-10">
        <div className="bg-gradient-to-br from-meddoc-secondary to-meddoc-primary p-2 rounded-lg">
          <Clock5 className="h-5 w-5 text-white" />
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold mb-6 text-meddoc-fonce">
            Horaires
          </h2>
          <div>
            <div className="space-y-3">
              {horaire_hebdomadaire && horaire_hebdomadaire.length > 0 ? (
                horaire_hebdomadaire.map((horaire, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    {horaire.creneau_horaire.length !== 0 && (
                      <div className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                        <span className="font-medium text-gray-800">
                          {ConvertLongDay(horaire.jour)}
                        </span>
                        <span className="text-meddoc-primary font-medium">
                          {horaire.creneau_horaire[0].heure_debut} -{" "}
                          {
                            horaire.creneau_horaire[
                              horaire.creneau_horaire.length - 1
                            ].heure_fin
                          }
                        </span>
                      </div>
                    )}
                  </motion.div>
                ))
              ) : (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4 }}
                  className="flex gap-3 items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200"
                >
                  <CalendarOff size={20} className="text-gray-400" />
                  <span className="text-gray-600">Pas d'horaire défini</span>
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.section>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const ScheduleSection = memo(ScheduleSectionComponent);
