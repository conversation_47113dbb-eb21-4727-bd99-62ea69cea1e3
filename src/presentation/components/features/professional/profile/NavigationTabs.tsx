import React, { ComponentProps } from "react";
import { twMerge } from "tailwind-merge";

interface NavigationTabsProps extends ComponentProps<"div"> {
  activeTab: string;
  onTabClick: (tab: string, event: React.MouseEvent) => void;
  stickyRef: React.RefObject<HTMLDivElement>;
}

export const NavigationTabs: React.FC<NavigationTabsProps> = ({
  activeTab,
  onTabClick,
  stickyRef,
  className,
  ...props
}) => {
  // Définir les onglets avec leurs IDs correspondants
  const tabs = [
    { name: "Essentiel", id: "essentiel" },
    { name: "<PERSON><PERSON>", id: "carte" },
    { name: "Présentation", id: "presentation" },
    { name: "<PERSON><PERSON><PERSON>", id: "horaires" }
  ];

  return (
    <div
      className={twMerge(
        "sticky top-16 border-b border-gray-200 mb-8 bg-white z-10",
        className
      )}
      {...props}
    >
      <nav
        className="flex space-x-8 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8"
        ref={stickyRef}
      >
        {tabs.map((tab) => (
          <a
            href={`#${tab.id}`}
            key={tab.id}
            onClick={(e) => onTabClick(tab.id, e)}
            className={`py-4 px-1 border-b-2 ${
              activeTab === tab.id
                ? "border-meddoc-primary text-meddoc-primary"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            {tab.name}
          </a>
        ))}
      </nav>
    </div>
  );
};
