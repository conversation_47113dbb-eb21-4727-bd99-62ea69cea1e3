import React, { useState, memo, useCallback } from "react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import { CheckIcon, MapPin, XIcon, Phone, Mail } from "lucide-react";
import { motion } from "framer-motion";
import AvailabilityModal from "@/presentation/components/common/Modal/AvailabilityModal";
import { ExtendedProfessionalProfile } from "@/presentation/hooks/useProfileData";

interface SummaryProps {
  professional: ExtendedProfessionalProfile | null;
  stickyResumeRef: React.RefObject<HTMLDivElement>;
}

const SummaryComponent: React.FC<SummaryProps> = ({
  professional,
  stickyResumeRef,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleModalStateChange = useCallback(async (isOpen: boolean) => {
    setIsModalOpen(isOpen);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.1 }}
      className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 sticky top-[155px] overflow-hidden relative"
      ref={stickyResumeRef}
    >
      {/* Effet de fond décoratif */}
      <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-meddoc-primary/5 to-meddoc-secondary/5 rounded-full -translate-y-12 translate-x-12"></div>

      <div className="relative z-10">
        <h2 className="text-2xl font-bold text-meddoc-fonce mb-6 flex items-center gap-2">
          <div className="w-1 h-6 bg-gradient-to-b from-meddoc-primary to-meddoc-secondary rounded-full"></div>
          En résumé
        </h2>
        <div className="space-y-5">
          {/* Statut d'acceptation des nouveaux patients */}
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.2 }}
            className={`flex items-center p-3 rounded-lg border ${
              professional?.nouveau_patient_acceptes
                ? "bg-green-50 border-green-200 text-green-700"
                : "bg-red-50 border-red-200 text-red-700"
            }`}
          >
            <span className="mr-3">
              {professional?.nouveau_patient_acceptes ? (
                <CheckIcon className="h-5 w-5 text-green-500" />
              ) : (
                <XIcon className="h-5 w-5 text-red-500" />
              )}
            </span>
            <span className="text-sm font-medium">
              {professional?.nouveau_patient_acceptes
                ? "Accepte les nouveaux patients"
                : "N'accepte plus de nouveaux patients"}
            </span>
          </motion.div>
          {/* Informations de l'établissement */}
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.3 }}
            className="p-3 bg-blue-50 rounded-lg border border-blue-200"
          >
            <div className="flex items-start gap-2">
              <MapPin className="h-4 w-4 text-meddoc-primary mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-800 mb-1">
                  {professional?.etablissements_professionnel?.[0]
                    ?.nom_etablissement ? (
                    <>
                      {
                        professional.etablissements_professionnel[0]
                          .nom_etablissement
                      }
                      {professional.etablissements_professionnel[0].equipe &&
                        ` - ${professional.etablissements_professionnel[0].equipe}`}
                    </>
                  ) : (
                    "Établissement non renseigné"
                  )}
                </p>
                {professional?.adresse && (
                  <p className="text-xs text-gray-600">
                    {professional.adresse}
                  </p>
                )}
              </div>
            </div>
          </motion.div>

          {/* Informations de contact */}
          {(professional?.email || professional?.telephone) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.3 }}
              className="bg-gradient-to-r from-meddoc-primary/5 to-meddoc-secondary/5 p-4 rounded-xl border border-meddoc-primary/10"
            >
              <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <Phone className="h-4 w-4 text-meddoc-primary" />
                Contact direct
              </h4>
              <div className="space-y-2">
                {professional?.email && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-3 w-3 text-meddoc-primary flex-shrink-0" />
                    <a
                      href={`mailto:${professional.email}`}
                      className="text-meddoc-primary hover:text-meddoc-secondary transition-colors truncate"
                    >
                      {professional.email}
                    </a>
                  </div>
                )}
                {professional?.telephone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-3 w-3 text-meddoc-primary flex-shrink-0" />
                    <a
                      href={`tel:${professional.telephone}`}
                      className="text-meddoc-primary hover:text-meddoc-secondary transition-colors"
                    >
                      {professional.telephone}
                    </a>
                  </div>
                )}
              </div>
            </motion.div>
          )}

          {/* Bouton de prise de rendez-vous */}
          <motion.button
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.4 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-shadow"
            onClick={() => handleModalStateChange(true)}
          >
            Prendre rendez-vous
          </motion.button>

          <AvailabilityModal
            isOpen={isModalOpen}
            onClose={() => handleModalStateChange(false)}
            professionalInformations={professional}
          />
        </div>
      </div>
    </motion.div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const Summary = memo(SummaryComponent);
