import { Avatar } from "@mui/material";
import { memo } from "react";
import { twMerge } from "tailwind-merge";
import { motion } from "framer-motion";
import { sexe_enum } from "@/domain/models/enums/sexe";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import { PhotoTypeEnum } from "@/domain/models/enums";

interface ProfileHeaderProps {
  professional: ProfessionalCardDTO | null;
  className?: string;
}

const ProfileHeaderComponent = ({
  professional,
  className,
}: ProfileHeaderProps) => {
  const filteredProfilePhoto = professional?.photos.find(
    (photo) => photo.type === PhotoTypeEnum.PROFILE
  );

  /**
   * Fonction utilitaire pour formater le genre de manière appropriée
   */

  const formatGender = (sexe?: sexe_enum): string => {
    if (!sexe) return "";
    return sexe === sexe_enum.homme ? "Dr." : "Dr.";
  };

  /**
   * Fonction utilitaire pour obtenir la spécialité principale
   */
  const getPrimarySpecialty = (): string => {
    return (
      professional?.specialite?.[0]?.nom_specialite ||
      "Spécialité non renseignée"
    );
  };

  /**
   * Fonction utilitaire pour obtenir le nom complet
   */
  const getFullName = (): string => {
    const title = professional?.titre || formatGender(professional?.sexe);
    const firstName = professional?.prenom || "";
    const lastName = professional?.nom || "";

    return `${title} ${firstName} ${lastName}`.trim();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className={twMerge(
        "relative flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 py-12 px-6 md:px-12",
        className
      )}
    >
      {/* Avatar avec animation */}
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="relative"
      >
        {filteredProfilePhoto ? (
          <img
            src={filteredProfilePhoto.path}
            className="border-4 border-white/20 shadow shadow-white/30 w-[120px] h-[120px] rounded-full overflow-hidden"
            alt="Profile"
          />
        ) : (
          <Avatar
            sx={{
              width: { xs: 80, md: 120 },
              height: { xs: 80, md: 120 },
              border: "4px solid rgba(255, 255, 255, 0.2)",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
            }}
          />
        )}

        {/* Badge de statut */}
        <div className="absolute -bottom-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full shadow-lg">
          {professional?.nouveau_patient_acceptes ? "Disponible" : "Complet"}
        </div>
      </motion.div>

      {/* Informations professionnelles */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="flex-1 text-center md:text-left"
      >
        {/* Nom et titre */}
        <h1 className="text-2xl md:text-4xl font-extrabold text-white mb-2 leading-tight">
          {getFullName()}
        </h1>

        {/* Spécialité principale */}
        <p className="text-lg md:text-xl text-white/90 mb-4 font-medium">
          {getPrimarySpecialty()}
        </p>

        {/* Informations supplémentaires */}
        <div className="flex flex-wrap justify-center md:justify-start gap-4 text-sm text-white/80">
          {professional?.sexe && (
            <span className="bg-white/10 backdrop-blur-sm px-3 py-1 rounded-full">
              {professional.sexe === sexe_enum.homme ? "Homme" : "Femme"}
            </span>
          )}

          {professional?.numero_ordre && (
            <span className="bg-white/10 backdrop-blur-sm px-3 py-1 rounded-full">
              N° Ordre: {professional.numero_ordre}
            </span>
          )}

          {professional?.raison_sociale && (
            <span className="bg-white/10 backdrop-blur-sm px-3 py-1 rounded-full">
              {professional.raison_sociale}
            </span>
          )}
        </div>

        {/* Adresse */}
        {professional?.adresse && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-white/70 mt-3 text-sm"
          >
            📍 {professional.adresse}
          </motion.p>
        )}
      </motion.div>

      {/* Effet de lumière décoratif */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-meddoc-primary/50 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-meddoc-secondary/50 to-transparent"></div>
    </motion.div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const ProfileHeader = memo(ProfileHeaderComponent);
