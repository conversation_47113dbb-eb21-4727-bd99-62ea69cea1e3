import { FC, memo } from 'react';
import { Clock } from 'lucide-react';

import { ProfessionalAppointment } from '@/presentation/types/professional.types';

interface DailyScheduleProps {
  /** Date du jour à afficher */
  date: Date;
  /** Liste des rendez-vous du jour */
  appointments: ProfessionalAppointment[];
}

const DailySchedule: FC<DailyScheduleProps> = memo(({ date, appointments }) => {
  const formattedDate = new Intl.DateTimeFormat('fr-FR', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
  }).format(date);

  return (
    <div className="rounded-lg shadow-md">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold capitalize">{formattedDate}</h3>
      </div>
      <div className="p-4">
        {appointments.length === 0 ? (
          <p className="text-gray-500 text-center py-4">
            Pas de rendez-vous aujourd'hui
          </p>
        ) : (
          <div className="space-y-4">
            {appointments.map((appointment) => (
              <div
                key={appointment.id}
                className={`p-4 rounded-lg ${appointment.isConfirmed
                    ? 'bg-green-50 border border-green-100'
                    : 'bg-yellow-50 border border-yellow-100'
                  }`}
              >
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-0">
                  <div className="flex-grow">
                    <p className="font-medium">{appointment.patientName}</p>
                    <div className="flex flex-wrap items-center gap-2 mt-1 text-sm text-gray-600">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 flex-shrink-0" />
                        <span>{appointment.time}</span>
                      </div>
                      <span className="hidden sm:inline">•</span>
                      <span>{appointment.duration} min</span>
                    </div>
                  </div>
                  <div className="self-start sm:self-center">
                    <span
                      className={`inline-block px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap ${appointment.isConfirmed
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                        }`}
                    >
                      {appointment.type}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
});

DailySchedule.displayName = 'DailySchedule';

export default DailySchedule;
