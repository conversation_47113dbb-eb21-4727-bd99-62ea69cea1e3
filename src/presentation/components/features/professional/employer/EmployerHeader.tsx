import {
  <PERSON><PERSON>,
  But<PERSON>,
  <PERSON>,
  Typography,
  Box,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { motion } from "framer-motion";
import { Calendar, User, Briefcase, Building2, BadgeInfo } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ListeProcheModal } from "@/presentation/components/common/Modal/ListeProcheModal";

// Badge pour le sexe
const GenderChip = ({ sexe }: { sexe?: string }) => {
  if (!sexe) return null;
  const isFemme = sexe === "femme";
  return (
    <Chip
      icon={<User size={14} />}
      label={isFemme ? "F" : "H"}
      size="small"
      sx={{
        backgroundColor: isFemme ? "#e91e63" : "#2196f3",
        color: "white",
        fontWeight: 500,
        fontSize: "0.7rem",
        height: "20px",
        minWidth: "24px",
        "& .MuiChip-label": { px: 1 },
      }}
    />
  );
};

// Badge pour le statut administratif
const StatusChip = ({ status }: { status?: string }) => {
  if (!status) return null;
  let color = "#64748b";
  if (status === "permanent") color = "#22c55e";
  if (status === "contractuel") color = "#f59e42";
  if (status === "temporaire") color = "#eab308";
  return (
    <Chip
      icon={<BadgeInfo size={14} />}
      label={status.charAt(0).toUpperCase() + status.slice(1)}
      size="small"
      sx={{
        backgroundColor: color,
        color: "white",
        fontWeight: 500,
        fontSize: "0.7rem",
        height: "20px",
        minWidth: "24px",
        "& .MuiChip-label": { px: 1 },
      }}
    />
  );
};

// Calcul de l'âge à partir de la date de naissance
const calculateAge = (birthDate?: Date | string) => {
  if (!birthDate) return "-";
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  return age;
};

const EmployerHeader = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { id } = useParams();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md"));

  const {
    selectedEmployerSlice,
    get: getEmployerById,
    loading,
  } = useEmployer();

  useEffect(() => {
    if (id) {
      getEmployerById(Number(id));
    }
  }, [id, getEmployerById]);

  // Affichage d'un loader ou d'un placeholder si les données ne sont pas encore chargées
  if (loading || !selectedEmployerSlice) {
    return (
      <div className="flex items-center justify-center h-32">
        <span className="text-gray-400 dark:text-gray-500">
          Chargement des informations...
        </span>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden mb-6"
    >
      {/* Header principal - Nouvelle structure en deux colonnes */}
      <Box
        className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 px-4 py-5 border-b border-gray-200 dark:border-gray-600"
        sx={{
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          gap: isMobile ? 3 : 4,
          alignItems: isMobile ? "center" : "flex-start",
        }}
      >
        {/* Colonne gauche: Avatar de l'employé */}
        <Box
          sx={{
            flex: isMobile ? "none" : "0 0 auto",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 1,
          }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
            className="relative"
          >
            <Avatar
              src={selectedEmployerSlice?.photo}
              sx={{
                width: isMobile ? 80 : 120,
                height: isMobile ? 80 : 120,
                border: "2px solid",
                borderColor: PRIMARY,
                boxShadow: "0 2px 8px rgba(39, 170, 225, 0.3)",
              }}
            />
          </motion.div>

          <Box
            sx={{
              display: "flex",
              gap: 1,
              mt: 1,
              justifyContent: "center",
            }}
          >
            <GenderChip sexe={selectedEmployerSlice?.sexe} />
            <StatusChip status={selectedEmployerSlice?.status_administratif} />
          </Box>
        </Box>

        {/* Colonne droite: Informations principales */}
        <Box
          sx={{
            flex: 1,
            display: "flex",
            flexDirection: "column",
            gap: 2,
            alignItems: isMobile ? "center" : "flex-start",
            textAlign: isMobile ? "center" : "left",
            width: isMobile ? "100%" : "auto",
          }}
        >
          <Typography
            variant="h5"
            className="text-gray-900 dark:text-white font-semibold"
            sx={{ fontSize: { xs: "1.2rem", sm: "1.5rem" }, lineHeight: 1.2 }}
          >
            {selectedEmployerSlice?.nom} {selectedEmployerSlice?.prenom}
          </Typography>

          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: { xs: "1fr", sm: "repeat(2, 1fr)" },
              gap: 2,
              width: "100%",
            }}
          >
            <div className="flex items-center gap-2">
              <Briefcase
                size={16}
                className="text-blue-500 dark:text-blue-400"
              />
              <Typography variant="body2" className="font-medium">
                {selectedEmployerSlice?.fonction || "Non renseigné"}
              </Typography>
            </div>

            <div className="flex items-center gap-2">
              <Building2
                size={16}
                className="text-green-500 dark:text-green-400"
              />
              <Typography variant="body2">
                {selectedEmployerSlice?.direction || "Non renseigné"}
              </Typography>
            </div>

            <div className="flex items-center gap-2">
              <Calendar
                size={16}
                className="text-indigo-500 dark:text-indigo-400"
              />
              <Typography variant="body2">
                {selectedEmployerSlice?.date_de_naissance
                  ? `${new Date(selectedEmployerSlice.date_de_naissance).toLocaleDateString("fr-FR")} (${calculateAge(selectedEmployerSlice.date_de_naissance)} ans)`
                  : "Non renseigné"}
              </Typography>
            </div>

            <div className="flex items-center gap-2">
              <BadgeInfo
                size={16}
                className="text-gray-500 dark:text-gray-400"
              />
              <Typography variant="body2">
                Matricule :{" "}
                {selectedEmployerSlice?.matricule || "Non renseigné"}
              </Typography>
            </div>

            <Button
              variant="contained"
              size="small"
              sx={{
                textTransform: "none",
                backgroundColor: PRIMARY,
                gridColumn: isTablet ? "span 2" : "auto",
                maxWidth: "fit-content",
                mt: { xs: 1, sm: 0 },
              }}
              onClick={() => setIsOpen(true)}
            >
              Voir liste des proches
            </Button>
          </Box>
        </Box>
      </Box>
      {isOpen && (
        <ListeProcheModal
          isOpen={isOpen}
          handleClose={() => setIsOpen(false)}
        />
      )}
    </motion.div>
  );
};

export default EmployerHeader;
