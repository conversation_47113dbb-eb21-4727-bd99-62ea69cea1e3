import { <PERSON><PERSON>, Chip, Typography } from "@mui/material";
import { motion } from "framer-motion";
import { Calendar, User, Briefcase, Building2, BadgeInfo } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";

// Badge pour le sexe
const GenderChip = ({ sexe }: { sexe?: string }) => {
  if (!sexe) return null;
  const isFemme = sexe === "femme";
  return (
    <Chip
      icon={<User size={14} />}
      label={isFemme ? "F" : "H"}
      size="small"
      sx={{
        backgroundColor: isFemme ? "#e91e63" : "#2196f3",
        color: "white",
        fontWeight: 500,
        fontSize: "0.7rem",
        height: "20px",
        minWidth: "24px",
        "& .MuiChip-label": { px: 1 },
      }}
    />
  );
};

// Badge pour le statut administratif
const StatusChip = ({ status }: { status?: string }) => {
  if (!status) return null;
  let color = "#64748b";
  if (status === "permanent") color = "#22c55e";
  if (status === "contractuel") color = "#f59e42";
  if (status === "temporaire") color = "#eab308";
  return (
    <Chip
      icon={<BadgeInfo size={14} />}
      label={status.charAt(0).toUpperCase() + status.slice(1)}
      size="small"
      sx={{
        backgroundColor: color,
        color: "white",
        fontWeight: 500,
        fontSize: "0.7rem",
        height: "20px",
        minWidth: "24px",
        "& .MuiChip-label": { px: 1 },
      }}
    />
  );
};

// Calcul de l'âge à partir de la date de naissance
const calculateAge = (birthDate?: Date | string) => {
  if (!birthDate) return "-";
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  return age;
};

const HeaderStyleEmployer = () => {
  const { selectedEmployerSlice } = useEmployer();
  return (
    <div>
      <div className="flex items-center gap-3">
        {/* Avatar de l'employé */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.2 }}
          className="relative"
        >
          <Avatar
            src={selectedEmployerSlice?.photo}
            sx={{
              width: 48,
              height: 48,
              border: "2px solid",
              borderColor: PRIMARY,
              boxShadow: "0 2px 8px rgba(39, 170, 225, 0.3)",
            }}
          />
        </motion.div>

        {/* Infos principales */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <Typography
              variant="h6"
              className={`text-white font-semibold truncate`}
              sx={{ fontSize: "1.1rem", lineHeight: 1.2 }}
            >
              {selectedEmployerSlice?.nom} {selectedEmployerSlice?.prenom}
            </Typography>
            <GenderChip sexe={selectedEmployerSlice?.sexe} />
            <StatusChip status={selectedEmployerSlice?.status_administratif} />
          </div>
          <div className="flex items-center gap-3 text-xs text-gray-600 dark:text-gray-300 flex-wrap">
            <span className="flex items-center gap-1">
              <Briefcase
                size={12}
                className="text-blue-500 dark:text-blue-400"
              />
              <span className="font-medium text-gray-300">
                {selectedEmployerSlice?.fonction}
              </span>
            </span>
            <span className="flex items-center gap-1">
              <Building2
                size={12}
                className="text-green-500 dark:text-green-400"
              />
              <span className="font-medium text-gray-300">
                {selectedEmployerSlice?.direction}
              </span>
            </span>
            <span className="flex items-center gap-1">
              <Calendar
                size={12}
                className="text-indigo-500 dark:text-indigo-400"
              />
              <span className="font-medium text-gray-300">
                {selectedEmployerSlice?.date_de_naissance &&
                  `${new Date(selectedEmployerSlice.date_de_naissance).toLocaleDateString("fr-FR")}
              (${calculateAge(selectedEmployerSlice.date_de_naissance)} ans)`}
              </span>
            </span>
            <span className="flex items-center gap-1">
              <BadgeInfo
                size={12}
                className="text-gray-500 dark:text-gray-400"
              />
              <span className="font-medium text-gray-300">
                Matricule : {selectedEmployerSlice?.matricule}
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeaderStyleEmployer;
