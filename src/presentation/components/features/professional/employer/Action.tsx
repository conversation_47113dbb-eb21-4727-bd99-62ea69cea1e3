import React, { useState } from "react";
import { EllipsisVertical } from "lucide-react";
import { Menu, MenuItem, IconButton } from "@mui/material";
import { Employer } from "@/domain/models";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer.ts";
import { EditEmployedModal } from "@/presentation/components/common/Modal/EditEmployedModal.tsx";
import { DeleteEmployedModal } from "@/presentation/components/common/Modal/DeleteEmployedModal.tsx";

import { ComponentProps } from "react";

interface ActionProps extends ComponentProps<"div"> {
  employed: Employer;
}

export default function Action({ employed, ...props }: ActionProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isDeleteEmployerModalOpen, setIsDeleteEmployerModalOpen] =
    useState<boolean>(false);

  const [isEditEmployerModalOpen, setIsEditEmployerModalOpen] =
    useState<boolean>(false);
  const open = Boolean(anchorEl);

  const { clearSelected, selectedEmployerSlice } = useEmployer();

  const handleCloseModal = () => {
    setIsDeleteEmployerModalOpen(false);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    event.preventDefault();
    setAnchorEl(event.currentTarget);
  };

  const handleCloseEditStock = () => {
    setIsEditEmployerModalOpen(false);
    clearSelected();
  };

  const handleClose = () => {
    setAnchorEl(null);
    clearSelected();
  };

  return (
    <div {...props}>
      <IconButton onClick={handleClick}>
        <EllipsisVertical size={20} />
      </IconButton>
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        <MenuItem
          onClick={(e) => {
            e.stopPropagation();
            setAnchorEl(null);
            setIsEditEmployerModalOpen(true);
          }}
        >
          Modifier
        </MenuItem>
        <MenuItem
          onClick={(e) => {
            e.stopPropagation();
            setAnchorEl(null);
            setIsDeleteEmployerModalOpen(true);
          }}
        >
          Supprimer
        </MenuItem>
      </Menu>
      {isDeleteEmployerModalOpen && (
        <DeleteEmployedModal
          isOpen={isDeleteEmployerModalOpen}
          employed={employed}
          handleClose={handleCloseModal}
        />
      )}
      {isEditEmployerModalOpen && (
        <EditEmployedModal
          employed={employed}
          isOpen={isEditEmployerModalOpen}
          handleCloseModal={() => setIsEditEmployerModalOpen(false)}
        />
      )}
    </div>
  );
}
