import {
  <PERSON><PERSON>,
  Box,
  Card,
  CardContent,
  styled,
  Typography,
  Chip,
} from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import { Employer } from "@/domain/models";
import { differenceInYears } from "date-fns";
import { Calendar, User, Briefcase, Building2 } from "lucide-react";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import { useNavigate } from "react-router-dom";
import { DashRoutesNavigation } from "@/shared/constants/AppRoutesNavigation.ts";
import Action from "./Action.tsx";
import { sexe_enum } from "@/domain/models/enums/sexe.ts";

const StyledCard = styled(Card)(({ theme }) => ({
  border: "1px solid",
  borderColor: theme.palette.divider,
  borderLeft: `4px solid ${PRIMARY}`,
  boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
  borderRadius: theme.spacing(1),
  marginBottom: theme.spacing(2),
  cursor: "pointer",
  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  position: "relative",
  overflow: "hidden",
  "&:hover": {
    borderColor: PRIMARY,
    borderLeftWidth: "6px",
    boxShadow: "0 8px 25px rgba(39, 170, 225, 0.15)",
    transform: "translateY(-2px)",
    "& .employee-avatar": {
      transform: "scale(1.05)",
    },
    "& .employee-name": {
      color: PRIMARY,
    },
    "& .employee-matricule": {
      fontWeight: 600,
    },
    "&::before": {
      opacity: 1,
    },
  },
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `linear-gradient(135deg, ${PRIMARY}08, transparent)`,
    opacity: 0,
    transition: "opacity 0.3s ease",
    pointerEvents: "none",
  },
}));

// Composant réutilisable pour l'icône + texte
const InfoRow = ({ icon, text }: { icon: React.ReactNode; text: string }) => (
  <div className="flex items-center mb-1.5 text-sm text-gray-600 dark:text-gray-300">
    <div className="mr-2 text-gray-400 dark:text-gray-500">{icon}</div>
    <span>{text}</span>
  </div>
);

// Chip genre dynamique
const GenderChip = ({ sexe }: { sexe: sexe_enum }) => {
  const isFemme = sexe === sexe_enum.femme;
  return (
    <Chip
      icon={<User size={14} />}
      label=""
      size="small"
      sx={{
        backgroundColor: isFemme ? "#e91e63" : "#2196f3",
        color: "white",
        width: 24,
        height: 24,
        boxShadow: `0 2px 8px ${isFemme ? "rgba(233, 30, 99, 0.3)" : "rgba(33, 150, 243, 0.3)"}`,
        border: "2px solid white",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        minWidth: "unset",
        "& .MuiChip-icon": {
          color: "white",
          fontSize: 14,
          margin: 0,
        },
        "& .MuiChip-label": {
          display: "none",
        },
      }}
    />
  );
};

const EmployerCard = ({ employed }: { employed: Employer }) => {
  const { select } = useEmployer();
  const navigate = useNavigate();

  const onSelectedEmployer = () => {
    select(employed);
    navigate(
      `/${DashRoutesNavigation.MANAGE_EMPLOYED_PAGE.split("/:id")[0]}/${employed.id_utilisateur}`
    );
  };

  // Calculer l'âge
  const age = differenceInYears(
    new Date(),
    new Date(employed.date_de_naissance)
  );

  // Générer les initiales
  const getInitials = (nom: string, prenom: string) => {
    const nomInitial = nom.charAt(0).toUpperCase();
    const prenomInitial = prenom ? prenom.charAt(0).toUpperCase() : "";
    return nomInitial + prenomInitial;
  };

  return (
    <StyledCard
      onClick={onSelectedEmployer}
      className="dark:bg-gray-800 dark:border-gray-700"
    >
      <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
        {/* Menu d'action - centré verticalement à droite */}
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            right: 12,
            transform: "translateY(-50%)",
            zIndex: 1000,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <Action employed={employed} />
        </Box>

        {/* Header avec avatar et nom */}
        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
          <Avatar
            className="employee-avatar"
            sx={{
              width: 48,
              height: 48,
              bgcolor: PRIMARY,
              fontSize: "1rem",
              fontWeight: "bold",
              boxShadow: "0 4px 12px rgba(39, 170, 225, 0.2)",
              transition: "transform 0.3s ease",
            }}
            src={employed.photo}
          >
            {getInitials(employed.nom, employed.prenom)}
          </Avatar>

          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography
              className="employee-name dark:text-gray-100"
              variant="h6"
              sx={{
                fontWeight: 600,
                fontSize: "1rem",
                color: "#2c3e50",
                mb: 0.5,
                lineHeight: 1.2,
                transition: "color 0.3s ease",
              }}
            >
              {employed.nom} {employed.prenom}
            </Typography>

            <Typography
              className="employee-matricule"
              variant="body2"
              sx={{
                color: PRIMARY,
                fontSize: "0.8rem",
                fontWeight: 500,
                transition: "font-weight 0.3s ease",
              }}
            >
              #{employed.matricule}
            </Typography>
          </Box>
        </Box>

        {/* Informations détaillées avec icônes */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <InfoRow icon={<Building2 size={16} />} text={employed.direction} />

          {/* Âge avec GenderChip */}
          <div className="flex items-center mb-1.5 text-sm text-gray-600 dark:text-gray-300">
            <div className="mr-2 text-gray-400 dark:text-gray-500">
              <Calendar size={16} />
            </div>
            <span className="mr-2">{`${age} ans`}</span>
            <GenderChip sexe={employed.sexe} />
          </div>

          <InfoRow
            icon={<Briefcase size={16} />}
            text={employed.fonction || "Employé"}
          />
        </Box>
      </CardContent>
    </StyledCard>
  );
};

export default EmployerCard;
