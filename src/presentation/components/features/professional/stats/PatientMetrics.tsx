import { FC, memo } from 'react';
import { <PERSON>, User<PERSON><PERSON>, User<PERSON><PERSON>ck, Clock } from 'lucide-react';

interface MetricProps {
  icon: React.ReactNode;
  label: string;
  value: string | number;
  change?: {
    value: number;
    isPositive: boolean;
  };
}

const Metric: FC<MetricProps> = ({ icon, label, value, change }) => (
  <div className="p-4 rounded-lg shadow-md">
    <div className="flex items-center">
      <div className="text-primary p-2 bg-meddoc-primary/10 rounded-full">
        {icon}
      </div>
      <div className="ml-3">
        <p className="text-sm text-gray-500">{label}</p>
        <p className="text-xl font-semibold">{value}</p>
        {change && (
          <p
            className={`text-sm ${
              change.isPositive ? 'text-green-500' : 'text-red-500'
            }`}
          >
            {change.isPositive ? '+' : '-'}
            {Math.abs(change.value)}%
          </p>
        )}
      </div>
    </div>
  </div>
);

import { PatientMetricsData } from '@/presentation/types/professional.types';

type PatientMetricsProps = PatientMetricsData;

const PatientMetrics: FC<PatientMetricsProps> = memo(
  ({ totalPatients, newPatients, returningPatients, averageVisitTime, trends }) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Metric
          icon={<Users className="h-6 w-6" />}
          label="Patients Total"
          value={totalPatients}
        />
        <Metric
          icon={<UserPlus className="h-6 w-6" />}
          label="Nouveaux Patients"
          value={newPatients}
          change={{ value: trends.newPatients, isPositive: trends.newPatients > 0 }}
        />
        <Metric
          icon={<UserCheck className="h-6 w-6" />}
          label="Patients Réguliers"
          value={returningPatients}
          change={{
            value: trends.returningPatients,
            isPositive: trends.returningPatients > 0,
          }}
        />
        <Metric
          icon={<Clock className="h-6 w-6" />}
          label="Durée Moyenne Visite"
          value={averageVisitTime}
        />
      </div>
    );
  },
);

PatientMetrics.displayName = 'PatientMetrics';

export default PatientMetrics;
