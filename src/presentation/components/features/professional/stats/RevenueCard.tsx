import { FC, memo } from 'react';
import { Euro } from 'lucide-react';
import { CURRENCY_FORMAT_OPTIONS } from '@/presentation/constants/professional.constants';

import { RevenueTrend } from '@/presentation/types/professional.types';

interface RevenueCardProps {
  /** Titre de la carte */
  title: string;
  /** Montant à afficher */
  amount: number;
  /** Période concernée (ex: "Février 2025") */
  period: string;
  /** Tendance par rapport à la période précédente */
  trend?: RevenueTrend;
}

const RevenueCard: FC<RevenueCardProps> = memo(({ title, amount, period, trend }) => {

  const formattedAmount = new Intl.NumberFormat('fr-FR', CURRENCY_FORMAT_OPTIONS).format(amount);

  return (
    <div className="rounded-lg p-6 shadow-md">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-sm text-gray-500 mt-1">{period}</p>
          <p className="text-2xl font-bold mt-2">{formattedAmount}</p>
          {trend && (
            <div className="flex items-center mt-2">
              <span
                className={`text-sm ${trend.isPositive ? 'text-green-500' : 'text-red-500'
                  }`}
              >
                {trend.isPositive ? '+' : '-'}
                {Math.abs(trend.percentage)}%
              </span>
              <span className="text-gray-500 text-sm ml-1">vs mois dernier</span>
            </div>
          )}
        </div>
        <div className="text-primary p-3 bg-meddoc-primary/10 rounded-full">
          <Euro className="h-6 w-6" />
        </div>
      </div>
    </div>
  );
});

RevenueCard.displayName = 'RevenueCard';

export default RevenueCard;
