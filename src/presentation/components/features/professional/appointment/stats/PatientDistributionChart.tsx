/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * PatientDistributionChart - Composant pour afficher la distribution des patients sous forme de graphique circulaire
 *
 * Note: Ce fichier utilise 'any' à plusieurs endroits car les types exacts de Recharts
 * sont complexes et difficiles à typer correctement. C'est une approche pragmatique
 * pour ce composant spécifique.
 */
import { FC } from "react";
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts";

// Types pour les données et les props
interface ChartData {
  name: string;
  value: number;
  color: string;
}

interface PatientDistributionChartProps {
  data: ChartData[];
}

// Types pour les composants personnalisés de Recharts
interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: ChartData;
    value: number;
    name: string;
    color: string;
  }>;
}

const PatientDistributionChart: FC<PatientDistributionChartProps> = ({
  data,
}) => {
  // Calculer le total pour les pourcentages
  const total = data.reduce((sum, entry) => sum + entry.value, 0);

  // Personnaliser le contenu du tooltip
  // Modifiez la fonction customTooltip
  const customTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 shadow-md rounded-md border border-gray-100 dark:border-gray-700">
          <p className="font-medium text-gray-900 dark:text-white">
            {data.name}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {data.value} patients ({((data.value / total) * 100).toFixed(1)}%)
          </p>
        </div>
      );
    }
    return null;
  };

  // Modifiez la fonction renderLegend
  const renderLegend = (props: any) => {
    const { payload } = props;

    if (!payload) return null;

    return (
      <ul className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry: any, index: number) => {
          // Vérifier que les données nécessaires sont disponibles
          if (!entry || !entry.payload) return null;

          return (
            <li key={`legend-${index}`} className="flex items-center">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: entry.color || "#ccc" }}
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {entry.value}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                ({((entry.payload.value / total) * 100).toFixed(0)}%)
              </span>
            </li>
          );
        })}
      </ul>
    );
  };

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          outerRadius={80}
          innerRadius={40}
          paddingAngle={5}
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        {/*
          Nous utilisons `any` ici car les types exacts de Recharts sont complexes
          et difficiles à typer correctement. C'est une approche pragmatique pour
          ce composant spécifique.
        */}
        <Tooltip content={customTooltip as any} />
        <Legend content={renderLegend} verticalAlign="bottom" align="center" />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default PatientDistributionChart;
