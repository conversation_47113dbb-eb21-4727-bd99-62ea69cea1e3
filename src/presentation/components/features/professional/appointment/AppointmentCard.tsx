import {
    Card,
    CardContent,
    Typography,
    Box,
    Chip,
    styled,
    Button,
    CardActions,
    Divider,
} from "@mui/material";
import {
    Palette as MotifIcon,
    CalendarToday as CalendarIcon,
    AccessTime as TimeIcon,
    LocationOn as LocationIcon,
} from "@mui/icons-material";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { PRIMARY } from "@/shared/constants/Color";
import AppointementActionButton from "./AppointementActionButton";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";

// Interface pour les props du composant
interface AppointmentCardProps {
    appointment: AppointmentProfessionalDTO;
}

// Styled Card avec bordure bleue à gauche
const StyledAppointmentCard = styled(Card)(({ theme }) => ({
    border: "1px solid",
    borderColor: theme.palette.divider,
    borderLeft: `4px solid ${PRIMARY}`,
    boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
    borderRadius: theme.spacing(1),
    cursor: "pointer",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    "&:hover": {
        borderLeftWidth: "6px",
        boxShadow: "0 4px 16px rgba(39, 170, 225, 0.15)",
        transform: "translateY(-2px)",
    },
}));

// Fonction pour obtenir la couleur du statut
const getStatusColor = (status: AppointmentProfessionalDTO["statut"]) => {
    switch (status) {
        case "A venir":
            return {
                backgroundColor: "primary.main",
                color: "white",
            };
        case "Manquer":
            return {
                backgroundColor: "#ffebee",
                color: "#d32f2f",
            };
        case "Annuler":
            return {
                backgroundColor: "#ffebee",
                color: "#d32f2f",
            };
        case "Terminer":
            return {
                backgroundColor: "#e3f2fd",
                color: "#1976d2",
            };
        case "Reporter":
            return {
                backgroundColor: "#fff3e0",
                color: "#f57c00",
            };
        default:
            return {
                backgroundColor: "#f5f5f5",
                color: "#757575",
            };
    }
};

const AppointmentCard = ({
    appointment,
}: AppointmentCardProps) => {
    const statusColors = getStatusColor(appointment.statut);

    return (
        <StyledAppointmentCard>
            <CardContent sx={{ padding: 3 }}>
                {/* Titre et statut */}
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Typography
                        variant="h6"
                        component="h3"
                        sx={{
                            fontWeight: 600,
                            fontSize: "1.1rem",
                            color: "text.primary",
                        }}
                    >
                        {appointment.patient.nom} {appointment.patient.prenom}
                    </Typography>
                    <Chip
                        label={appointment.statut}
                        size="small"
                        sx={{
                            ...statusColors,
                            fontWeight: 500,
                            fontSize: "0.75rem",
                            height: 24,
                        }}
                    />
                </Box>

                {/* Informations de la consultation */}
                <Box display="flex" flexDirection="column" gap={1.5}>
                    {/* Patient */}
                    <Box display="flex" alignItems="center">
                        <MotifIcon
                            sx={{
                                fontSize: 18,
                                mr: 1,
                                color: "text.secondary"
                            }}
                        />
                        <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ fontSize: "0.9rem" }}
                        >
                            {appointment.motif}
                        </Typography>
                    </Box>

                    {/* Date */}
                    <Box display="flex" alignItems="center">
                        <CalendarIcon
                            sx={{
                                fontSize: 18,
                                mr: 1,
                                color: "text.secondary"
                            }}
                        />
                        <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ fontSize: "0.9rem" }}
                        >
                            {format(new Date(appointment.date_rendez_vous), "EEEE dd MMMM yyyy", { locale: fr })}
                        </Typography>
                    </Box>

                    {/* Heure et durée */}
                    <Box display="flex" alignItems="center">
                        <TimeIcon
                            sx={{
                                fontSize: 18,
                                mr: 1,
                                color: "text.secondary"
                            }}
                        />
                        <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ fontSize: "0.9rem" }}
                        >
                            {appointment.time}
                        </Typography>
                        <Box
                            component="span"
                            sx={{
                                mx: 1,
                                width: 4,
                                height: 4,
                                borderRadius: "50%",
                                backgroundColor: "text.secondary",
                                display: "inline-block"
                            }}
                        />
                    </Box>
                </Box>
            </CardContent>

            {/* Divider entre le contenu et les actions */}
            <Divider />
            {/* <AppointementActionButton /> */}
            <AppointementActionButton appointment={appointment} />
        </StyledAppointmentCard>
    );
};

export default AppointmentCard;