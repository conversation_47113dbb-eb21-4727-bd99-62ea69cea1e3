import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { Chip } from "@mui/material";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import Action from "./Action";
import { statusColors } from "@/shared/constants/statusColors";

export const AppointmentColumnsProfessional = (): GridColDef[] => {
  return [
    {
      field: "date_rendez_vous",
      headerName: "Date",
      width: 180,
      valueFormatter: (params) => new Date(params).toLocaleString(),
      headerClassName: "font-semibold",
    },
    {
      field: "patient",
      headerName: "Patient",
      width: 200,
      renderCell: (params: GridRenderCellParams<AppointmentProfessionalDTO>) =>
        `${params.row.patient.nom} ${params.row.patient.prenom}`,
      headerClassName: "font-semibold",
    },
    {
      field: "motif",
      headerName: "Motif",
      width: 250,
      headerClassName: "font-semibold",
    },
    {
      field: "statut",
      headerName: "Statut",
      width: 150,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={statusColors[params.value as keyof typeof statusColors]}
          size="small"
        />
      ),
      headerClassName: "font-semibold",
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      renderCell: (params) => <Action appointment={params.row} />,
      headerClassName: "font-semibold",
    },
  ];
};
