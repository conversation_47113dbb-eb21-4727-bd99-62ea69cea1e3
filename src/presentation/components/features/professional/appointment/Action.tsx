import React, { useState } from "react";
import { Ellipsis } from "lucide-react";
import { <PERSON>u, MenuItem, Icon<PERSON>utton } from "@mui/material";
import CancelAppointmentModal from "@/presentation/components/common/Modal/CancelAppointmentModal";
import PostponeAppointmentModal from "@/presentation/components/common/Modal/PostponeAppointmentModal";
import CompleteAppointmentModal from "@/presentation/components/common/Modal/CompleteAppointmentModal";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { useAvailability } from "@/presentation/hooks/use-availability";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import useAppointmentDate from "@/presentation/hooks/use-appointment-date";
import useAppointment from "@/presentation/hooks/appointment/use-appointment";

interface ActionProps {
  appointment: AppointmentProfessionalDTO;
}

export default function Action({ appointment }: ActionProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { handleResetTimeSlot } = useAvailability();
  const [isCancelAppointmentModalOpen, setIsCancelAppointmentModalOpen] =
    useState(false);
  const [isPostponeAppointmentModalOpen, setIsPostponeAppointmentModalOpen] =
    useState(false);
  const [isCompleteAppointmentModalOpen, setIsCompleteAppointmentModalOpen] =
    useState(false);
  const open = Boolean(anchorEl);

  const { handleEndAppointment } = useProfessionnelPatient();
  const { isAppointmentDoneButtonDisabled } = useAppointmentDate();

  const handleCloseCancelAppointmentModal = () => {
    setIsCancelAppointmentModalOpen(false);
  };

  const handleClosePostponeAppointmentModal = () => {
    setIsPostponeAppointmentModalOpen(false);
    handleResetTimeSlot();
  };

  const handleCloseCompleteAppointmentModal = () => {
    setIsCompleteAppointmentModalOpen(false);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const { setSelectedAppointment } = useAppointment();

  return (
    <div>
      {(appointment.statut === rendez_vous_statut_enum.A_VENIR ||
        appointment.statut === rendez_vous_statut_enum.REPORTER) && (
        <IconButton onClick={handleClick}>
          <Ellipsis size={20} />
        </IconButton>
      )}
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        {appointment.statut !== rendez_vous_statut_enum.TERMINER && (
          <MenuItem
            onClick={() => {
              handleEndAppointment(appointment);
              setSelectedAppointment({
                ...appointment,
              });
            }}
            disabled={isAppointmentDoneButtonDisabled(
              appointment.date_rendez_vous,
            )}
          >
            Consulter
          </MenuItem>
        )}
        {appointment.statut !== rendez_vous_statut_enum.REPORTER && (
          <MenuItem
            onClick={() => {
              setIsPostponeAppointmentModalOpen(true);
              setAnchorEl(null);
            }}
          >
            Reporter
          </MenuItem>
        )}
        {appointment.statut !== rendez_vous_statut_enum.ANNULER && (
          <MenuItem
            onClick={() => {
              setIsCancelAppointmentModalOpen(true);
              setAnchorEl(null);
            }}
          >
            Annuler
          </MenuItem>
        )}
      </Menu>
      {isCancelAppointmentModalOpen && (
        <CancelAppointmentModal
          appointment={appointment}
          isCancelAppointmentModalOpen={isCancelAppointmentModalOpen}
          handleCloseCancelAppointmentModal={handleCloseCancelAppointmentModal}
        />
      )}
      {isPostponeAppointmentModalOpen && (
        <PostponeAppointmentModal
          appointment={appointment}
          isPostponeAppointmentModalOpen={isPostponeAppointmentModalOpen}
          handleClosePostponeAppointmentModal={
            handleClosePostponeAppointmentModal
          }
        />
      )}
      {isCompleteAppointmentModalOpen && (
        <CompleteAppointmentModal
          appointment={appointment}
          isCompleteAppointmentModalOpen={isCompleteAppointmentModalOpen}
          handleCloseCompleteAppointmentModal={
            handleCloseCompleteAppointmentModal
          }
        />
      )}
    </div>
  );
}
