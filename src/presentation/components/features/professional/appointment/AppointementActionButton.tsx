import React, { useState } from "react";
import { <PERSON>, CardA<PERSON>, Button } from "@mui/material";
import {
    MedicalServices as ConsultIcon,
    Schedule as RescheduleIcon,
    Cancel as CancelIcon
} from "@mui/icons-material";
import CancelAppointmentModal from "@/presentation/components/common/Modal/CancelAppointmentModal";
import PostponeAppointmentModal from "@/presentation/components/common/Modal/PostponeAppointmentModal";
import CompleteAppointmentModal from "@/presentation/components/common/Modal/CompleteAppointmentModal";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { useAvailability } from "@/presentation/hooks/use-availability";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import useAppointmentDate from "@/presentation/hooks/use-appointment-date";

interface ActionButtonsCardProps {
    appointment: AppointmentProfessionalDTO;
}

export default function ActionButtonsCard({ appointment }: ActionButtonsCardProps) {
    const [isCancelAppointmentModalOpen, setIsCancelAppointmentModalOpen] = useState(false);
    const [isPostponeAppointmentModalOpen, setIsPostponeAppointmentModalOpen] = useState(false);
    const [isCompleteAppointmentModalOpen, setIsCompleteAppointmentModalOpen] = useState(false);

    const { handleResetTimeSlot } = useAvailability();
    const { handleEndAppointment } = useProfessionnelPatient();
    const { isAppointmentDoneButtonDisabled } = useAppointmentDate();

    const handleCloseCancelAppointmentModal = () => {
        setIsCancelAppointmentModalOpen(false);
    };

    const handleClosePostponeAppointmentModal = () => {
        setIsPostponeAppointmentModalOpen(false);
        handleResetTimeSlot();
    };

    const handleCloseCompleteAppointmentModal = () => {
        setIsCompleteAppointmentModalOpen(false);
    };

    return (
        <>
            <Card>
                <CardActions sx={{ padding: 2, justifyContent: "space-between" }}>
                    {appointment.statut !== rendez_vous_statut_enum.MANQUER && appointment.statut !== rendez_vous_statut_enum.TERMINER && (
                        <Button
                            variant="contained"
                            size="small"
                            startIcon={<ConsultIcon />}
                            onClick={() => handleEndAppointment(appointment)}
                            disabled={isAppointmentDoneButtonDisabled(appointment.date_rendez_vous)}
                            sx={{
                                backgroundColor: "#27aae1",
                                "&:hover": { backgroundColor: "#1e88c7" },
                                textTransform: "none",
                                fontWeight: 500,
                            }}
                        >
                            Consulter
                        </Button>
                    )}

                    {appointment.statut !== rendez_vous_statut_enum.TERMINER && appointment.statut !== rendez_vous_statut_enum.MANQUER && appointment.statut !== rendez_vous_statut_enum.REPORTER && (
                        <Button
                            variant="outlined"
                            size="small"
                            startIcon={<RescheduleIcon />}
                            onClick={() => setIsPostponeAppointmentModalOpen(true)}
                            sx={{
                                borderColor: "#f57c00",
                                color: "#f57c00",
                                "&:hover": {
                                    borderColor: "#e65100",
                                    backgroundColor: "rgba(245, 124, 0, 0.04)",
                                },
                                textTransform: "none",
                                fontWeight: 500,
                            }}
                        >
                            Reporter
                        </Button>
                    )}

                    {appointment.statut !== rendez_vous_statut_enum.TERMINER && appointment.statut !== rendez_vous_statut_enum.MANQUER && appointment.statut !== rendez_vous_statut_enum.ANNULER && (
                        <Button
                            variant="outlined"
                            size="small"
                            startIcon={<CancelIcon />}
                            onClick={() => setIsCancelAppointmentModalOpen(true)}
                            sx={{
                                borderColor: "#d32f2f",
                                color: "#d32f2f",
                                "&:hover": {
                                    borderColor: "#b71c1c",
                                    backgroundColor: "rgba(211, 47, 47, 0.04)",
                                },
                                textTransform: "none",
                                fontWeight: 500,
                            }}
                        >
                            Annuler
                        </Button>
                    )}
                </CardActions>
            </Card>

            {isCancelAppointmentModalOpen && (
                <CancelAppointmentModal
                    appointment={appointment}
                    isCancelAppointmentModalOpen={isCancelAppointmentModalOpen}
                    handleCloseCancelAppointmentModal={handleCloseCancelAppointmentModal}
                />
            )}

            {isPostponeAppointmentModalOpen && (
                <PostponeAppointmentModal
                    appointment={appointment}
                    isPostponeAppointmentModalOpen={isPostponeAppointmentModalOpen}
                    handleClosePostponeAppointmentModal={handleClosePostponeAppointmentModal}
                />
            )}

            {isCompleteAppointmentModalOpen && (
                <CompleteAppointmentModal
                    appointment={appointment}
                    isCompleteAppointmentModalOpen={isCompleteAppointmentModalOpen}
                    handleCloseCompleteAppointmentModal={handleCloseCompleteAppointmentModal}
                />
            )}
        </>
    );
}
