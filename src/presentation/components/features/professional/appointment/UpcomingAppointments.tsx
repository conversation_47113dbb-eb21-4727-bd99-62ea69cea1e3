import { FC } from "react";
import { Clock } from "lucide-react";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

interface UpcomingAppointmentsProps {
  appointments: AppointmentProfessionalDTO[];
}

const UpcomingAppointments: FC<UpcomingAppointmentsProps> = ({
  appointments,
}) => {
  if (appointments.length === 0) {
    return (
      <p className="text-gray-500 dark:text-gray-400 text-sm">
        Aucun rendez-vous à venir
      </p>
    );
  }

  return (
    <div className="space-y-3">
      {appointments.map((appointment) => (
        <div
          key={appointment.id}
          className="flex items-center p-3 rounded-lg border border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/50"
        >
          <div className="flex-shrink-0 mr-3">
            <div
              className={`h-10 w-10 rounded-full flex items-center justify-center ${
                appointment.statut === rendez_vous_statut_enum.A_VENIR
                  ? "bg-green-100 dark:bg-green-800/50"
                  : "bg-amber-100 dark:bg-amber-800/50"
              }`}
            >
              <Clock
                className={`h-5 w-5 ${
                  appointment.statut === rendez_vous_statut_enum.A_VENIR
                    ? "text-green-600 dark:text-green-400"
                    : "text-amber-600 dark:text-amber-400"
                }`}
              />
            </div>
          </div>
          <div>
            <p className="font-medium text-gray-900 dark:text-white">
              {appointment.patient.nom}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {appointment.time} • {appointment.statut}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default UpcomingAppointments;
