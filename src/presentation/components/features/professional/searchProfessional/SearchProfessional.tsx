import SearchBar from "@/presentation/components/common/searchBar/SearchBar";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import "leaflet/dist/leaflet.css";
import { useEffect, useMemo, useState } from "react";
import LeafletMap from "../../leaflet/LeafletMap";
import ProfessionalCard from "../professionalCard/ProfessionalCard";
import SelectFilter from "@/presentation/components/common/searchBar/SelectFilter";
import ProfessionalCardSkeleton from "../professionalCard/ProfessionalCardSkeleton";
import SearchResults from "./SearchResults";
import SearchDetails from "./SearchDetails";

const SearchProfessional = () => {
  const {
    professionals,
    loading,
    searchSpecialityKey,
    searchLocationKey,
    searchProfessional,
  } = useSearchProfessional();

  const [hoveredId, setHoveredId] = useState<number | null>(null);

  // Filters state
  const [filters, setFilters] = useState({
    status: "",
    language: "",
    gender: "",
  });

  // Filtered professionals based on SelectFilter
  const filteredProfessionals = useMemo(() => {
    if (!professionals || professionals.length === 0) return [];
    return professionals.filter((professional) => {
      let matches = true;

      if (filters.status) {
        matches =
          matches &&
          professional.nouveau_patient_acceptes ===
            (filters.status === "accepte");
      }

      if (filters.gender) {
        matches =
          matches &&
          professional.sexe?.toLowerCase() === filters.gender.toLowerCase();
      }

      return matches;
    });
  }, [professionals, filters]);

  const handleFilterChange = (type: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [type]: value,
    }));
  };

  useEffect(() => {
    // Rechercher seulement si nous avons des paramètres valides et non vides
    const hasValidSpeciality = searchSpecialityKey && searchSpecialityKey.trim().length > 0;
    const hasValidLocation = searchLocationKey && searchLocationKey.trim().length > 0;

    if (hasValidSpeciality || hasValidLocation) {
      searchProfessional({
        name: searchSpecialityKey?.trim() || "",
        localization: searchLocationKey?.trim() || "",
      });
    }
  }, [searchSpecialityKey, searchLocationKey]);

  return (
    <div className="mb-5">
      <div className="w-full bg-white py-5 pt-8">
        <SearchBar className="w-[96%] md:w-3/4 relative shadow-[0_0_20px_rgb(0,0,0,0.2)]" />
      </div>
      {!loading && (
        <SearchResults
          professionals={filteredProfessionals}
          filters={filters}
        />
      )}
      <SearchDetails />
      <SelectFilter
        onFilterChange={handleFilterChange}
        filters={filters}
        className="sticky top-16 z-20 shadow-[0_3px_3px_rgb(0,0,0,0.1)]"
      />

      <div className="flex flex-col h-full w-full">
        <div className="flex gap-1">
          <div className="flex-1">
            <div className="w-[95%] mx-auto">
              {loading ? (
                // Afficher les skeletons pendant le chargement
                <div className="grid gap-2 mt-5 animate-fade-in">
                  {[1, 2, 3, 4].map((i) => (
                    <ProfessionalCardSkeleton key={i} />
                  ))}
                </div>
              ) : filteredProfessionals.length > 0 ? (
                <>
                  <div className="flex flex-col gap-3 mt-4 animate-fade-in">
                    {filteredProfessionals.map((professional) => (
                      <ProfessionalCard
                        key={professional.id}
                        professionalInformations={professional}
                        setHoveredId={setHoveredId}
                      />
                    ))}
                  </div>
                </>
              ) : (
                <div className="text-center text-gray-600 mt-5">
                  Aucun professionnel trouvé pour votre recherche
                </div>
              )}
            </div>
          </div>

          <div className="hidden md:block sticky top-[137px] h-[calc(500px)] w-[30%] mt-5 mr-5">
            <LeafletMap
              filteredData={filteredProfessionals}
              hoveredId={hoveredId}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchProfessional;
