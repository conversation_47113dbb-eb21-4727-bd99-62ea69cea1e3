import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";

const SearchDetails = () => {
  const { searchType } = useSearchProfessional();
  const [searchParams, setSearchParams] = useSearchParams();

  const [keyword, setKeyword] = useState("");
  const [location, setLocation] = useState("");

  useEffect(() => {
    const keyword = searchParams.get("searchname");
    const location = searchParams.get("searchlocation");

    setKeyword(keyword || "");
    setLocation(location || "");
  }, [searchParams]);

  return (
    <div className="w-full bg-white py-2 px-6 shadow-md animate-fade-in">
      <div className="text-meddoc-fonce font-semibold">
        <p>
          {searchType === "symptome" ? (
            <>
              {keyword ? (
                <>
                  <span className="text-meddoc-secondary font-semibold">{keyword}</span>
                  {location && (
                    <>
                      {" "}
                      à{" "}
                      <span className="text-meddoc-secondary font-semibold">
                        {location}
                      </span>
                    </>
                  )}
                </>
              ) : (
                <>
                  Prenez rendez-vous{location ? " à " : " en ligne aujourd'hui"}
                  {location && (
                    <span className="text-meddoc-secondary font-semibold">
                      {location}
                    </span>
                  )}
                </>
              )}
              {!keyword ? "" : ": prenez rendez-vous en ligne aujourd'hui"}
            </>
          ) : (
            <>
              Prenez rendez-vous en ligne aujourd'hui
              {keyword && (
                <>
                  {" chez un "}
                  <span className="text-meddoc-secondary font-semibold">{keyword}</span>
                </>
              )}
              {location && (
                <>
                  {" à "}
                  <span className="text-meddoc-secondary font-semibold">{location}</span>
                </>
              )}
            </>
          )}
        </p>
      </div>
    </div>
  );
};

export default SearchDetails;
