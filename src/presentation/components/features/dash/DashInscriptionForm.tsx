import { Building, Lock, Mail } from "lucide-react";
import Form<PERSON>ield from "../../common/ui/FormField.tsx";
import useDashForm from "@/presentation/hooks/dash/use-dash-form.ts";
import { DashInvitationFormData } from "@/shared/schemas/DashInvitationSchema.ts";
import Button from "../../common/Button/Button.tsx";
import LoadingSpinner from "../../common/LoadingSpinner";

interface DashInscriptionFormProps {
  invitationId: number;
  defaultValue?: DashInvitationFormData;
  isInvitationUsed: boolean;
}

/**
 * Formulaire d'inscription pour les organismes
 *
 * Ce composant gère l'inscription des organismes via invitation.
 * Il utilise un design moderne et responsive avec validation en temps réel.
 *
 * @component
 * @param {DashInscriptionFormProps} props - Les propriétés du composant
 * @param {number} props.invitationId - ID de l'invitation
 * @param {DashInvitationFormData} props.defaultValue - Valeurs par défaut du formulaire
 * @param {boolean} props.isInvitationUsed - Indique si l'invitation a déjà été utilisée
 *
 * @example
 * ```tsx
 * <DashInscriptionForm
 *   invitationId={123}
 *   defaultValue={invitationData}
 *   isInvitationUsed={false}
 * />
 * ```
 *
 * @returns {JSX.Element} Formulaire d'inscription organisme
 */
const DashInscriptionForm = ({
  invitationId,
  defaultValue,
  isInvitationUsed,
}: DashInscriptionFormProps): JSX.Element => {
  const { register, errors, handleSubmit, getValues, isLoading } = useDashForm(
    invitationId,
    defaultValue
  );

  if (isInvitationUsed) {
    return (
      <div className="text-center py-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
          <svg
            className="w-8 h-8 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-gray-800 mb-2">
          Invitation déjà utilisée
        </h3>
        <p className="text-gray-600">
          Cette invitation a déjà été utilisée pour créer un compte.
        </p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Première ligne - Informations de base */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          id="organizationName"
          placeholder="Entrez votre nom d'organisme"
          type="text"
          required
          icon={Building}
          value={defaultValue?.organizationName || ""}
          label="Nom de l'organisme"
          register={register}
          error={errors.organizationName}
          validation={{
            required: "Le nom de l'organisme est requis",
            minLength: {
              value: 2,
              message:
                "Le nom de l'organisme doit contenir au moins 2 caractères",
            },
            maxLength: {
              value: 100,
              message:
                "Le nom de l'organisme ne peut pas dépasser 100 caractères",
            },
            pattern: {
              value: /^[a-zA-ZÀ-ÿ0-9\s\-_.&()]+$/,
              message:
                "Le nom de l'organisme ne peut contenir que des lettres, chiffres, espaces et caractères spéciaux autorisés (- _ . & ( ))",
            },
          }}
        />
        <FormField
          id="email"
          placeholder="Entrez votre email"
          type="email"
          required
          icon={Mail}
          value={defaultValue?.email || ""}
          label="Email"
          register={register}
          error={errors.email}
          validation={{
            required: "L'email est requis",
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: "Adresse email invalide",
            },
          }}
        />
      </div>

      {/* Deuxième ligne - Mots de passe */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          id="password"
          placeholder="Entrez votre mot de passe"
          type="password"
          required
          icon={Lock}
          label="Mot de passe"
          showPasswordStrength
          register={register}
          error={errors.password}
          validation={{
            required: "Le mot de passe est requis",
            minLength: {
              value: 8,
              message: "Le mot de passe doit contenir au moins 8 caractères",
            },
          }}
        />
        <FormField
          id="confirmPassword"
          placeholder="Confirmez votre mot de passe"
          type="password"
          required
          icon={Lock}
          label="Confirmer le mot de passe"
          register={register}
          error={errors.confirmPassword}
          validation={{
            required: "La confirmation du mot de passe est requise",
            validate: (value) =>
              value === getValues("password") ||
              "Les mots de passe ne correspondent pas",
          }}
        />
      </div>

      {/* Bouton de soumission */}
      <div className="flex justify-center pt-4">
        <Button
          type="submit"
          disabled={isLoading}
          className="px-8 py-3 bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <LoadingSpinner
                size={16}
                color="border-white"
                className="h-auto"
              />
              Création en cours...
            </div>
          ) : (
            "Créer mon compte"
          )}
        </Button>
      </div>
    </form>
  );
};

export default DashInscriptionForm;
