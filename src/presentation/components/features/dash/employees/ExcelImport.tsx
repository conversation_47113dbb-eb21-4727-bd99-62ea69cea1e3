import React, { useRef, useState } from 'react';
import { Upload } from 'lucide-react';
import { Employer } from '@/domain/models';
import { useLogicImportEmployee } from '@/presentation/hooks/employer/use_logic_import_employee';

interface ExcelImportProps {
    onImport: (employees: Employer[]) => Promise<void>;
    existingEmployees: Employer[];
    isLoading?: boolean;
}

export function ExcelImport({ onImport, existingEmployees, isLoading }: ExcelImportProps) {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isImporting, setIsImporting] = useState(false);
    const [progress, setProgress] = useState(0);

    const { handleFileImport } = useLogicImportEmployee({
        onImport,
        existingEmployees,
        options: { onProgress: (percent) => setProgress(percent) },
    });

    const handleFileSelect = () => {
        fileInputRef.current?.click();
    };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        try {
            setIsImporting(true);
            setProgress(0);

            await handleFileImport(file);
        } catch (error) {
            console.error("Erreur lors de l'import :", error);
        } finally {
            setIsImporting(false);
            setTimeout(() => setProgress(0), 1000);

            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    const isButtonDisabled = isLoading || isImporting;

    return (
        <>
            <input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileChange}
                style={{ display: 'none' }}
            />

            <button
                onClick={handleFileSelect}
                disabled={isButtonDisabled}
                className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-6 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3 whitespace-nowrap group disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
                <Upload className={`h-4 w-4 ${isImporting ? 'animate-spin' : ''}`} />
                {isImporting ? `Import en cours...` : 'Importer depuis Excel'}
            </button>

            {/* Modale de progression */}
            {isImporting && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="bg-white rounded-xl p-6 w-96 shadow-xl text-center animate-fadeIn">
                        <h2 className="text-lg font-semibold mb-4 text-gray-800">
                            Importation en cours...
                        </h2>

                        <div className="relative w-full h-5 rounded-full bg-gray-200 overflow-hidden shadow-inner mb-4">
                            <div
                                className="absolute top-0 left-0 h-full bg-gradient-to-r from-meddoc-primary to-meddoc-secondary transition-all duration-300 ease-linear"
                                style={{ width: `${progress}%` }}
                            ></div>
                            <div className="absolute top-0 left-0 h-full w-full bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulseGlow" />
                        </div>

                        <p className="text-sm font-medium text-gray-600">{progress}%</p>
                    </div>
                </div>
            )}
        </>
    );
}
