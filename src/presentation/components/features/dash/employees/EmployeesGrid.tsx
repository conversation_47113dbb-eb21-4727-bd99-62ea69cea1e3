import { useState } from "react";
import { <PERSON>, Typography, Pa<PERSON>ation, Button, Tooltip, IconButton } from "@mui/material";
import EmployerCard from "@/presentation/components/features/professional/employer/EmployerCard";
import { Employer } from "@/domain/models/Employer";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { Apps as AppsIcon, List as ListIcon } from "@mui/icons-material";

/**
 * Props pour le composant EmployeesGrid
 */
interface EmployeesGridProps {
  employees: Employer[];
  searchQuery: string;
  /** Type de employés à afficher (actif, decede, supprimer, all) */
  pageConfig:
  | {
    title: string;
    subtitle: string;
    icon: JSX.Element;
    color: string;
    bgColor: string;
    borderColor: string;
  }
  | {
    title: string;
    subtitle: string;
    icon: JSX.Element;
    color: string;
    bgColor?: undefined;
    borderColor?: undefined;
  };
}

/**
 * Composant qui affiche la grille des employés avec gestion de l'état vide
 *
 * @param props - Les propriétés du composant
 * @returns JSX.Element
 *
 * @example
 * ```tsx
 * <EmployeesGrid
 *   employees={filteredEmployees}
 *   searchQuery={searchQuery}
 * />
 * ```
 */
const ITEMS_PER_PAGE = 6;

const EmployeesGrid = ({
  employees,
  searchQuery,
  pageConfig,
}: EmployeesGridProps) => {
  const [page, setPage] = useState(1);
  // state pour la vue list ou grid
  const [isGridView, setIsGridView] = useState(false);

  // Calcule les employés à afficher sur la page actuelle
  const paginatedEmployees = employees.slice(
    (page - 1) * ITEMS_PER_PAGE,
    page * ITEMS_PER_PAGE
  );

  const pageCount = Math.ceil(employees.length / ITEMS_PER_PAGE);

  const handlePageChange = (
    _event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };
  return (
    <div className="mb-4">
      <div className="mx-2 mb-10">
        <div className="flex items-center justify-between mb-2">
          {/* Partie gauche - Titre et icône */}
          <div className="flex items-center gap-3">
            {pageConfig.icon}
            <Typography
              variant="h1"
              className={`font-bold ${pageConfig.color}`}
              sx={{ fontSize: "1.2rem" }}
            >
              {pageConfig.title} ({employees.length})
            </Typography>
          </div>

          {/* Partie droite - Bouton de vue */}
          <div>
            <Tooltip title="Changer la vue">
              <IconButton
                onClick={() => setIsGridView(!isGridView)}
                size="medium"
                sx={{
                  backgroundColor: 'rgba(39, 170, 225, 0.1)',
                  color: '#27aae1',
                  '&:hover': {
                    backgroundColor: 'rgba(39, 170, 225, 0.2)',
                  },
                  border: '1px solid rgba(39, 170, 225, 0.3)',
                }}
              >
                {!isGridView ? <ListIcon /> : <AppsIcon />}
              </IconButton>
            </Tooltip>
          </div>
        </div>

        <Typography
          variant="body1"
          className="text-gray-600 dark:text-gray-400"
          sx={{ fontSize: "0.95rem" }}
        >
          {pageConfig.subtitle}
        </Typography>
      </div>


      {employees.length === 0 ? (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          py={8}
          sx={{
            backgroundColor: "rgba(0, 0, 0, 0.02)",
            borderRadius: 2,
            border: "1px dashed rgba(0, 0, 0, 0.12)",
          }}
          className="dark:!bg-white/5 dark:!border-white/20"
        >
          <Typography
            variant="h6"
            sx={{ color: "#6b7280" }}
            className="dark:!text-gray-400"
            gutterBottom
          >
            Aucun employé trouvé
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#6b7280" }}
            className="dark:!text-gray-400"
            textAlign="center"
          >
            {searchQuery
              ? "Aucun employé ne correspond à votre recherche"
              : "Vous n'avez aucun employé pour le moment"}
          </Typography>
        </Box>
      ) : (
        <>
          {!isGridView ? <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {paginatedEmployees.map((employee) => (
              <div style={{ cursor: "pointer" }} key={employee.id}>
                <EmployerCard employed={employee} />
              </div>
            ))}
          </div> : <ListDataGrid data={employees} type="employer" />}

          {/* Pagination */}
          {!isGridView && pageCount > 1 && (
            <Box mt={4} display="flex" justifyContent="center">
              <Pagination
                count={pageCount}
                page={page}
                onChange={handlePageChange}
                color="primary"
                shape="rounded"
              />
            </Box>
          )}
        </>
      )}
    </div>
  );
};

export default EmployeesGrid;
