import { FormControl, MenuItem, Select } from "@mui/material";
import { Building2, Filter, Plus } from "lucide-react";

/**
 * Props pour le composant EmployeesMobileControl
 */
interface EmployeesMobileControlProps {
  /** Filtre de genre sélectionné */
  genderFilter: "all" | "homme" | "femme";
  /** Fonction pour mettre à jour le filtre de genre */
  setGenderFilter: (val: "all" | "homme" | "femme") => void;
  /** Filtre de direction sélectionné */
  directionFilter: string;
  /** Fonction pour mettre à jour le filtre de direction */
  setDirectionFilter: (val: string) => void;
  /** Liste des directions disponibles */
  availableDirections: string[];
  /** Fonction appelée lors du clic sur le bouton d'ajout */
  handleAddEmployedOpen: () => void;
}

/**
 * Composant qui affiche les contrôles de filtrage et d'ajout pour mobile
 * 
 * @param props - Les propriétés du composant
 * @returns JSX.Element
 * 
 * @example
 * ```tsx
 * <EmployeesMobileControl
 *   genderFilter={genderFilter}
 *   setGenderFilter={setGenderFilter}
 *   directionFilter={directionFilter}
 *   setDirectionFilter={setDirectionFilter}
 *   availableDirections={availableDirections}
 *   handleAddEmployedOpen={handleAddEmployedOpen}
 * />
 * ```
 */
const EmployeesMobileControl = ({
  genderFilter,
  setGenderFilter,
  directionFilter,
  setDirectionFilter,
  availableDirections,
  handleAddEmployedOpen,
}: EmployeesMobileControlProps) => {
  return (
    <div className="flex gap-3">
      <FormControl size="small" sx={{ flex: 1 }}>
        <Select
          value={genderFilter}
          onChange={(e) =>
            setGenderFilter(e.target.value as "all" | "homme" | "femme")
          }
          displayEmpty
          sx={{
            borderRadius: "12px",
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            color: "#1f2937",
            "& .MuiOutlinedInput-notchedOutline": {
              border: "none",
            },
            "&:hover": {
              backgroundColor: "#f1f5f9",
            },
            "&.Mui-focused": {
              backgroundColor: "#ffffff",
              borderColor: "#27aae1",
              boxShadow: "0 0 0 3px rgba(39, 170, 225, 0.1)",
            },
          }}
          className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
        >
          <MenuItem value="all">
            <div className="flex items-center gap-2">
              <Filter size={16} className="text-gray-500 dark:text-gray-400" />
              <span className="text-gray-700 dark:text-gray-200">Tous</span>
            </div>
          </MenuItem>
          <MenuItem value="homme">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-gray-700 dark:text-gray-200">Hommes</span>
            </div>
          </MenuItem>
          <MenuItem value="femme">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
              <span className="text-gray-700 dark:text-gray-200">Femmes</span>
            </div>
          </MenuItem>
        </Select>
      </FormControl>

      <FormControl size="small" sx={{ flex: 1 }}>
        <Select
          value={directionFilter}
          onChange={(e) => setDirectionFilter(e.target.value)}
          displayEmpty
          sx={{
            borderRadius: "12px",
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            color: "#1f2937",
            "& .MuiOutlinedInput-notchedOutline": {
              border: "none",
            },
            "&:hover": {
              backgroundColor: "#f1f5f9",
            },
            "&.Mui-focused": {
              backgroundColor: "#ffffff",
              borderColor: "#27aae1",
              boxShadow: "0 0 0 3px rgba(39, 170, 225, 0.1)",
            },
          }}
          className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
        >
          <MenuItem value="all">
            <div className="flex items-center gap-2">
              <Building2
                size={16}
                className="text-gray-500 dark:text-gray-400"
              />
              <span className="text-gray-700 dark:text-gray-200">Toutes</span>
            </div>
          </MenuItem>
          {availableDirections.map((direction) => (
            <MenuItem key={direction} value={direction}>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-gray-700 dark:text-gray-200 text-xs">
                  {direction}
                </span>
              </div>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <button
        onClick={handleAddEmployedOpen}
        className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-4 py-2 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center gap-2 whitespace-nowrap"
      >
        <Plus size={18} />
        <span>Ajouter</span>
      </button>
    </div>
  );
};

export default EmployeesMobileControl;
