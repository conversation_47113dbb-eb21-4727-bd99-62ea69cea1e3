import { InputAdornment, TextField } from "@mui/material";
import { Search } from "lucide-react";

/**
 * Props pour le composant EmployeesSearchBar
 */
interface EmployeesSearchBarProps {
  /** Valeur de la recherche */
  searchQuery: string;
  /** Fonction pour mettre à jour la recherche */
  setSearchQuery: (val: string) => void;
}

/**
 * Composant barre de recherche pour les employés (version mobile)
 * 
 * @param props - Les propriétés du composant
 * @returns JSX.Element
 * 
 * @example
 * ```tsx
 * <EmployeesSearchBar
 *   searchQuery={searchQuery}
 *   setSearchQuery={setSearchQuery}
 * />
 * ```
 */
const EmployeesSearchBar = ({
  searchQuery,
  setSearchQuery,
}: EmployeesSearchBarProps) => {
  return (
    <div className="relative">
      <TextField
        fullWidth
        placeholder="Rechercher un employé..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search size={18} className="text-gray-400 dark:text-gray-500" />
            </InputAdornment>
          ),
        }}
        sx={{
          "& .MuiOutlinedInput-root": {
            borderRadius: "12px",
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            transition: "all 0.2s ease",
            color: "#1f2937",
            "& fieldset": {
              border: "none",
            },
            "&:hover": {
              backgroundColor: "#f1f5f9",
              borderColor: "#cbd5e1",
            },
            "&.Mui-focused": {
              backgroundColor: "#ffffff",
              borderColor: "#27aae1",
              boxShadow: "0 0 0 3px rgba(39, 170, 225, 0.1)",
            },
          },
          "& .MuiInputBase-input": {
            "&::placeholder": {
              color: "#9ca3af",
              opacity: 1,
            },
          },
        }}
        className="[&_.MuiOutlinedInput-root]:dark:!bg-gray-700 [&_.MuiOutlinedInput-root]:dark:!border-gray-600 [&_.MuiOutlinedInput-root]:dark:!text-gray-100 [&_.MuiOutlinedInput-root:hover]:dark:!bg-gray-600 [&_.MuiOutlinedInput-root.Mui-focused]:dark:!bg-gray-700 [&_.MuiInputBase-input::placeholder]:dark:!text-gray-400"
      />
    </div>
  );
};

export default EmployeesSearchBar;
