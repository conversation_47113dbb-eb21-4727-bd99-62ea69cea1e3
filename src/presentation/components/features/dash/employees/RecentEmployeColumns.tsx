import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { DashRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useNavigate } from "react-router-dom";
import { Employer } from "@/domain/models";

export const RecentEmployeColumns = (): GridColDef[] => {
    const navigate = useNavigate();

    return [
        {
            field: "fullName",
            headerName: "Nom et Prénom",
            flex: 1,
            renderCell: (params: GridRenderCellParams<Employer>) => {
                const employe = params.row;
                if (!employe) return "";

                const fullName = `${employe.nom || ""} ${employe.prenom || ""}`.trim();

                const employeId = employe.id;

                return (
                    <a
                        className="text-blue-600 hover:underline cursor-pointer"
                        onClick={() => {
                            console.log("Ligne cliquée :", params.row.id_utilisateur);
                            navigate(
                                `/${DashRoutesNavigation.MANAGE_EMPLOYED_PAGE.split("/:id")[0]}/${params.row.id_utilisateur}`
                            );
                        }}
                    >
                        {fullName}
                    </a>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "age",
            headerName: "Âge",
            flex: 1,
            renderCell: (params: GridRenderCellParams<Employer>) => {
                const dob = params.row.date_de_naissance;
                if (!dob) return "";

                const birthDate = new Date(dob);
                const today = new Date();
                let age = today.getFullYear() - birthDate.getFullYear();
                const m = today.getMonth() - birthDate.getMonth();

                if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }

                return age + " ans";
            },
            headerClassName: "font-semibold",
        },
        {
            field: "created_date",
            headerName: "Date création",
            flex: 1,
            renderCell: (params: GridRenderCellParams<Employer>) => {
                if (!params.row.cree_a) return "";

                const date = new Date(params.row.cree_a);

                // Options pour le format de date long en français
                const dateOptions: Intl.DateTimeFormatOptions = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };

                // Options pour l'heure
                const timeOptions: Intl.DateTimeFormatOptions = {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                };

                const dateStr = date.toLocaleDateString('fr-FR', dateOptions);
                const timeStr = date.toLocaleTimeString('fr-FR', timeOptions);

                return `${dateStr} à ${timeStr}`;
            },
            headerClassName: "font-semibold",
        }
    ];
};
