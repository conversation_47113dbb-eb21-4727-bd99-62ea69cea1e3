import { Employer } from "@/domain/models";
import { Users } from "lucide-react";
import { <PERSON>, <PERSON>, Pie<PERSON><PERSON>, ResponsiveContainer, Toolt<PERSON> } from "recharts";
import { generateGenderDistributionData } from "@/shared/utils/generateGenderDistributionData";

interface PatientsActifsProps {
  employers: Employer[];
}

// Couleurs spécifiques pour chaque genre
const GENDER_COLORS = {
  homme: "#5B9BD5",
  femme: "#FF69B4",
};

const PatientsActifs = ({ employers }: PatientsActifsProps) => {
  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
         Répartition des patients actifs par genre
        </h2>
        <Users size={20} className="text-gray-400" />
      </div>
      <div className="h-64 flex items-center">
        <div className="w-1/2 relative">
          <ResponsiveContainer width="100%" height={200}>
            <PieChart>
              <Pie
                data={generateGenderDistributionData(employers)}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={0}
                dataKey="value"
              >
                {generateGenderDistributionData(employers).map(
                  (entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={GENDER_COLORS[entry.name]}
                    />
                  )
                )}
              </Pie>
              <Tooltip
                formatter={(value, name) => [`${value} employés`, name]}
                contentStyle={{
                  background: "rgba(255, 255, 255, 0.9)",
                  borderRadius: "6px",
                  padding: "8px",
                  border: "none",
                  boxShadow: "0 2px 10px rgba(0,0,0,0.15)",
                }}
                itemStyle={{ color: "#333" }}
              />
            </PieChart>
          </ResponsiveContainer>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <span className="text-4xl font-bold text-gray-900 dark:text-white">
              {generateGenderDistributionData(employers).reduce(
                (total, entry) => total + entry.value,
                0
              )}
            </span>
          </div>
        </div>
        <div className="w-1/2 pl-6">
          <div className="mb-4">
            <span className="text-lg font-semibold text-gray-900 dark:text-white">
              Total:{" "}
              {generateGenderDistributionData(employers).reduce(
                (total, entry) => total + entry.value,
                0
              )}
            </span>
          </div>
          <div className="space-y-2">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-[#5B9BD5] mr-2"></div>
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Homme (
                {
                  generateGenderDistributionData(employers).find(
                    (entry) => entry.name === "homme"
                  )?.value
                }
                )
              </span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-[#FF69B4] mr-2"></div>
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Femme (
                {
                  generateGenderDistributionData(employers).find(
                    (entry) => entry.name === "femme"
                  )?.value
                }
                )
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientsActifs;
