import { PieChartIcon } from "lucide-react";
import { Responsive<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Too<PERSON><PERSON>, Pie<PERSON><PERSON> } from "recharts";
import { Employer } from "@/domain/models";
import { generateDirectionDistributionData } from "@/shared/utils/generateDirectionDistributionData";

interface RepartitionParDirectionProps {
  employers: Employer[];
  availableDirections: string[];
  COLORS: string[];
}

const RepartitionParDirection = ({
  employers,
  availableDirections,
  COLORS,
}: RepartitionParDirectionProps) => {
  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          Répartition des consultations par direction
        </h2>
        <PieChartIcon size={20} className="text-gray-400" />
      </div>
      <div className="h-64 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
        {employers && availableDirections && employers.length > 0 ? (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={generateDirectionDistributionData(
                  employers,
                  availableDirections
                )}
                cx="50%"
                cy="50%"
                labelLine={false}
                // Retirer les labels des segments pour éviter la surcharge
                label={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {generateDirectionDistributionData(
                  employers,
                  availableDirections
                ).map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip
                formatter={(value, name) => [`${value} employés`, name]}
                contentStyle={{
                  background: "rgba(255, 255, 255, 0.9)",
                  borderRadius: "6px",
                  padding: "8px",
                  border: "none",
                  boxShadow: "0 2px 10px rgba(0,0,0,0.15)",
                }}
                itemStyle={{ color: "#333" }}
              />
            </PieChart>
          </ResponsiveContainer>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <PieChartIcon size={48} className="text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 dark:text-gray-400">
                Aucune donnée disponible
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RepartitionParDirection;
