import { Heart } from "lucide-react";

interface PrincipalesPathologiesProps {
  pathologiesData: {
    name: any;
    count: any;
    color: string;
  }[];
}

const PrincipalesPathologies = ({
  pathologiesData,
}: PrincipalesPathologiesProps) => {
  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
        Top 5 des pathologies les plus fréquentes
        </h2>
        <Heart size={20} className="text-gray-400" />
      </div>
      <div className="space-y-4">
        {pathologiesData.map((item, index) => (
          <div key={index} className="space-y-2">
            <div className="w-full bg-gray-200 dark:bg-gray-700 h-3 relative overflow-hidden">
              <div
                className="h-full transition-all duration-500 ease-out"
                style={{
                  backgroundColor: item.color,
                  width: `${(item.count / Math.max(...pathologiesData.map((d) => d.count))) * 100}%`,
                }}
              />
            </div>
          </div>
        ))}
        {/* Légende */}
        <div className="flex flex-wrap gap-4 mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          {pathologiesData.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {item.name} ({item.count})
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PrincipalesPathologies;
