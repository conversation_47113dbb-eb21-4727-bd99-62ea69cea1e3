import { Pill } from "lucide-react";

interface PrincipauxMedicamentsProps {
  medicamentsData: {
    name: any;
    count: any;
    color: string;
  }[];
}

const PrincipauxMedicaments = ({
  medicamentsData,
}: PrincipauxMedicamentsProps) => {
  // Fonction pour générer un nom court automatiquement
  const generateShortName = (fullName) => {
    // Nettoyer et extraire les mots clés importants
    const cleaned = fullName
      .replace(/\([^)]*\)/g, "") // Supprimer tout ce qui est entre parenthèses
      .replace(
        /BTE|FL|CP|COMPRIMES|DE|POUR|GP|CLARIS|OTSUKA|PRIVATE|LIMITED|PHARMALAGASY|DIPHAR|PLUS/gi,
        ""
      ) // Supprimer les mots techniques
      .replace(/\d+G\/L|\d+ML|\d+MG/i, (match) => ` ${match.toUpperCase()}`) // Garder les dosages
      .replace(/[^a-zA-Z0-9\s]/g, " ") // Remplacer la ponctuation par des espaces
      .replace(/\s+/g, " ") // Supprimer les espaces multiples
      .trim()
      .split(" ")
      .filter((word) => word.length > 2) // Garder seulement les mots de plus de 2 caractères
      .slice(0, 3) // Prendre les 3 premiers mots importants
      .join(" ")
      .toUpperCase();

    return cleaned || fullName.split(" ").slice(0, 2).join(" ").toUpperCase();
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          Top 5 des médicaments les plus prescrits
        </h2>
        <Pill size={20} className="text-gray-400" />
      </div>
      <div className="space-y-4">
        {medicamentsData.map((item, index) => (
          <div key={index} className="space-y-2">
            <div className="w-full bg-gray-200 dark:bg-gray-700  h-3 relative overflow-hidden">
              <div
                className="h-full  transition-all duration-500 ease-out"
                style={{
                  backgroundColor: item.color,
                  width: `${(item.count / Math.max(...medicamentsData.map((d) => d.count))) * 100}%`,
                }}
              />
            </div>
          </div>
        ))}
        {/* Légende */}
        <div className="flex flex-wrap gap-4 mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          {medicamentsData.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {generateShortName(item.name)} ({item.count})
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PrincipauxMedicaments;
