import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { useNavigate } from "react-router-dom";
import { consultation_medical } from "@/domain/models";

export const HistoriqueConsultationColumns = (
  onViewConsultation?: (consultation: any) => void
): GridColDef[] => {
  const navigate = useNavigate();

  return [
    {
      field: "date_consultation",
      headerName: "Date consultation",
      flex: 1.2,
      renderCell: (params: GridRenderCellParams<consultation_medical>) => {
        const dateField = params.row.date_visite || params.row.date_visite;
        if (!dateField) return "";

        const date = new Date(dateField);

        const dateOptions: Intl.DateTimeFormatOptions = {
          year: "numeric",
          month: "short",
          day: "numeric",
        };

        const timeOptions: Intl.DateTimeFormatOptions = {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        };

        const dateStr = date.toLocaleDateString("fr-FR", dateOptions);
        const timeStr = date.toLocaleTimeString("fr-FR", timeOptions);

        return (
          <div className="text-gray-700">
            <div className="text-sm font-medium">{dateStr}</div>
            <div className="text-xs text-gray-500">{timeStr}</div>
          </div>
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "raison_de_visite",
      headerName: "Raison de visite",
      flex: 1.5,
      renderCell: (params: GridRenderCellParams<consultation_medical>) => {
        if (!params.row.raison_de_visite) return "";

        return (
          <span className="text-gray-800 capitalize">
            {params.row.raison_de_visite}
          </span>
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "plainte_principale",
      headerName: "Plainte principale",
      flex: 2,
      renderCell: (params: GridRenderCellParams<consultation_medical>) => {
        if (!params.row.plainte_principale) return "";

        const plainte = params.row.plainte_principale;
        const displayText =
          plainte.length > 50 ? `${plainte.substring(0, 50)}...` : plainte;

        return (
          <div className="text-gray-800 max-w-xs">
            <span title={plainte} className="cursor-help">
              {displayText}
            </span>
          </div>
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "remarque",
      headerName: "Remarques",
      flex: 2,
      renderCell: (params: GridRenderCellParams<consultation_medical>) => {
        if (!params.row.remarque) return "";

        const remarque = params.row.remarque;
        const displayText =
          remarque.length > 60 ? `${remarque.substring(0, 60)}...` : remarque;

        return (
          <div className="text-gray-600 max-w-xs">
            <span title={remarque} className="cursor-help italic">
              {displayText}
            </span>
          </div>
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 1.2,
      renderCell: (params: GridRenderCellParams<consultation_medical>) => {
        return (
          <div className="flex gap-1 justify-center items-center h-full min-h-[40px]">
            <button
              className="bg-blue-500 text-white px-2 py-1 text-xs rounded hover:bg-blue-600 transition-colors"
              onClick={() => {
                if (onViewConsultation) {
                  onViewConsultation(params.row);
                }
              }}
            >
              Voir
            </button>
          </div>
        );
      },
      headerClassName: "font-semibold",
      sortable: false,
    },
  ];
};
