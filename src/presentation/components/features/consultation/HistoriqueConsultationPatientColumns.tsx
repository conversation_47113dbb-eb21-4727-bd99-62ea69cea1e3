import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { ConsultationDetails } from "@/domain/DTOS/MedicalConsultationDetailsDTO";

export const HistoriqueConsultationPatientColumns = (
): GridColDef[] => {
    return [
        {
            field: "date_consultation",
            headerName: "Date consultation",
            flex: 1.2,
            renderCell: (params: GridRenderCellParams<ConsultationDetails>) => {
                const dateField = params.row.dateConsultation || params.row.dateConsultation;
                if (!dateField) return "";

                const date = new Date(dateField);

                const dateOptions: Intl.DateTimeFormatOptions = {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                };

                const timeOptions: Intl.DateTimeFormatOptions = {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: false,
                };

                const dateStr = date.toLocaleDateString("fr-FR", dateOptions);
                const timeStr = date.toLocaleTimeString("fr-FR", timeOptions);

                return (
                    <div className="text-gray-700">
                        <div className="text-sm font-medium">{dateStr}</div>
                        <div className="text-xs text-gray-500">{timeStr}</div>
                    </div>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "nom professionnel",
            headerName: "Professionel",
            flex: 1.5,
            renderCell: (params: GridRenderCellParams<ConsultationDetails>) => {
                if (!params.row.nomProfessionnel) return "";

                const fullName = `${params.row.nomProfessionnel} ${params.row.prenomProfessionnel}`.trim();
                const url = `/search/${params.row.nomProfessionnel}-${params.row.professionnelId}`;
                return (
                    <a
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline cursor-pointer"
                    >
                        {fullName}
                    </a>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "raison_de_visite",
            headerName: "Raison de visite",
            flex: 1.5,
            renderCell: (params: GridRenderCellParams<ConsultationDetails>) => {
                if (!params.row.raisonDeVisite) return "";

                return (
                    <span className="text-gray-800 capitalize">
                        {params.row.raisonDeVisite}
                    </span>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "plainte_principale",
            headerName: "Plainte principale",
            flex: 2,
            renderCell: (params: GridRenderCellParams<ConsultationDetails>) => {
                if (!params.row.plaintePrincipale) return "";

                const plainte = params.row.plaintePrincipale;
                const displayText =
                    plainte.length > 50 ? `${plainte.substring(0, 50)}...` : plainte;

                return (
                    <div className="text-gray-800 max-w-xs">
                        <span title={plainte} className="cursor-help">
                            {displayText}
                        </span>
                    </div>
                );
            },
            headerClassName: "font-semibold",
        },
        {
            field: "remarque",
            headerName: "Remarques",
            flex: 2,
            renderCell: (params: GridRenderCellParams<ConsultationDetails>) => {
                if (!params.row.remarques) return "";

                const remarque = params.row.remarques;
                const displayText =
                    remarque.length > 60 ? `${remarque.substring(0, 60)}...` : remarque;

                return (
                    <div className="text-gray-600 max-w-xs">
                        <span title={remarque} className="cursor-help italic">
                            {displayText}
                        </span>
                    </div>
                );
            },
            headerClassName: "font-semibold",
        }
    ];
};
