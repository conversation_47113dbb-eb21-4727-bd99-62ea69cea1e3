import { FC, memo, useState } from "react";
import {
  Users,
  MoreVertical,
  Archive,
  Trash2,
  Volume2,
  Volume<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Phone,
  Video,
  Info,
} from "lucide-react";
import { ConversationDTO } from "@/presentation/types/message.types";
import { TEXT_STYLES, ICON_STYLES } from "@/presentation/styles/common";
import {
  USER_ROLE_STYLES,
  ONLINE_STATUS_STYLES,
  ACCESSIBILITY_LABELS,
} from "@/presentation/constants/message.constants";

interface ConversationHeaderProps {
  /** Conversation data */
  conversation: ConversationDTO;
  /** Callback when mark as read is clicked */
  onMarkAsRead?: () => void;
  /** Callback when archive is clicked */
  onArchive?: () => void;
  /** Callback when delete is clicked */
  onDelete?: () => void;
  /** Callback when mute/unmute is clicked */
  onToggleMute?: () => void;
  /** Callback when call is initiated */
  onCall?: () => void;
  /** Callback when video call is initiated */
  onVideoCall?: () => void;
  /** Callback when info is clicked */
  onInfo?: () => void;
  /** Whether to show action buttons */
  showActions?: boolean;
}

/**
 * ConversationHeader component - Header for the active conversation
 * Shows participant info, online status, and conversation actions
 *
 * @param props - Component props
 * @returns {JSX.Element} The conversation header component
 */
const ConversationHeader: FC<ConversationHeaderProps> = memo(
  ({
    conversation,
    onMarkAsRead,
    onArchive,
    onDelete,
    onToggleMute,
    onCall,
    onVideoCall,
    onInfo,
    showActions = true,
  }) => {
    const [showDropdown, setShowDropdown] = useState(false);

    // Get the main participant (for direct conversations)
    const mainParticipant = conversation.participant;
    const roleStyles = mainParticipant
      ? USER_ROLE_STYLES[mainParticipant.role]
      : null;

    /**
     * Format last seen time
     */
    const formatLastSeen = (lastSeen?: Date): string => {
      if (!lastSeen) return "";

      const now = new Date();
      const diffInMinutes = Math.floor(
        (now.getTime() - lastSeen.getTime()) / (1000 * 60)
      );

      if (diffInMinutes < 1) return "À l'instant";
      if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;
      if (diffInMinutes < 1440)
        return `Il y a ${Math.floor(diffInMinutes / 60)} h`;
      return `Il y a ${Math.floor(diffInMinutes / 1440)} j`;
    };

    /**
     * Handle dropdown toggle
     */
    const toggleDropdown = () => {
      setShowDropdown(!showDropdown);
    };

    /**
     * Handle action click and close dropdown
     */
    const handleActionClick = (action: () => void) => {
      action();
      setShowDropdown(false);
    };

    return (
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        {/* Participant Info */}
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {/* Avatar */}
          <div className="relative flex-shrink-0">
            <div
              className={`w-10 h-10 rounded-full ${roleStyles?.bgColor || "bg-gray-200"} flex items-center justify-center`}
            >
              <Users
                className={`h-5 w-5 ${roleStyles?.color || "text-gray-500"}`}
              />
            </div>

            {/* Online Status Indicator */}
            {mainParticipant && (
              <div className="absolute -bottom-1 -right-1">
                <div
                  className={`w-3 h-3 rounded-full border-2 border-white dark:border-gray-800 ${
                    mainParticipant.isOnline
                      ? ONLINE_STATUS_STYLES.online.color
                      : ONLINE_STATUS_STYLES.offline.color
                  }`}
                />
              </div>
            )}
          </div>

          {/* Participant Details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h2 className={`font-semibold ${TEXT_STYLES.primary} truncate`}>
                {conversation.participant.name}
              </h2>
              {roleStyles && (
                <span
                  className={`px-2 py-0.5 rounded-full text-xs font-medium ${roleStyles.bgColor} ${roleStyles.color}`}
                >
                  {roleStyles.label}
                </span>
              )}
            </div>

            {/* Online Status Text */}
            <div className="flex items-center gap-1">
              {mainParticipant?.isOnline ? (
                <span
                  className={`${TEXT_STYLES.small} text-green-600 dark:text-green-400`}
                >
                  En ligne
                </span>
              ) : (
                <span
                  className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}
                >
                  {mainParticipant?.lastSeen
                    ? formatLastSeen(mainParticipant.lastSeen)
                    : "Hors ligne"}
                </span>
              )}

              {/* Muted Indicator */}
              {conversation.isMuted && (
                <VolumeX className="h-3 w-3 text-gray-400 ml-1" />
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        {showActions && (
          <div className="flex items-center gap-2">
            {/* Quick Actions */}
            <div className="hidden sm:flex items-center gap-1">
              {/* Call Button */}
              {onCall && (
                <button
                  onClick={onCall}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  aria-label="Appeler"
                >
                  <Phone className={`h-4 w-4 ${ICON_STYLES.base}`} />
                </button>
              )}

              {/* Video Call Button */}
              {onVideoCall && (
                <button
                  onClick={onVideoCall}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  aria-label="Appel vidéo"
                >
                  <Video className={`h-4 w-4 ${ICON_STYLES.base}`} />
                </button>
              )}

              {/* Mark as Read Button */}
              {onMarkAsRead && conversation.unreadCount > 0 && (
                <button
                  onClick={onMarkAsRead}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  aria-label={ACCESSIBILITY_LABELS.markAsRead}
                >
                  <CheckCheck className={`h-4 w-4 ${ICON_STYLES.base}`} />
                </button>
              )}

              {/* Info Button */}
              {onInfo && (
                <button
                  onClick={onInfo}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  aria-label="Informations"
                >
                  <Info className={`h-4 w-4 ${ICON_STYLES.base}`} />
                </button>
              )}
            </div>

            {/* More Actions Dropdown */}
            <div className="relative">
              <button
                onClick={toggleDropdown}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                aria-label="Plus d'actions"
              >
                <MoreVertical className={`h-4 w-4 ${ICON_STYLES.base}`} />
              </button>

              {/* Dropdown Menu */}
              {showDropdown && (
                <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10">
                  <div className="py-1">
                    {/* Mobile Quick Actions */}
                    <div className="sm:hidden">
                      {onCall && (
                        <button
                          onClick={() => handleActionClick(onCall)}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3"
                        >
                          <Phone className="h-4 w-4" />
                          <span className={TEXT_STYLES.primary}>Appeler</span>
                        </button>
                      )}

                      {onVideoCall && (
                        <button
                          onClick={() => handleActionClick(onVideoCall)}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3"
                        >
                          <Video className="h-4 w-4" />
                          <span className={TEXT_STYLES.primary}>
                            Appel vidéo
                          </span>
                        </button>
                      )}

                      {onInfo && (
                        <button
                          onClick={() => handleActionClick(onInfo)}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3"
                        >
                          <Info className="h-4 w-4" />
                          <span className={TEXT_STYLES.primary}>
                            Informations
                          </span>
                        </button>
                      )}

                      {onMarkAsRead && conversation.unreadCount > 0 && (
                        <button
                          onClick={() => handleActionClick(onMarkAsRead)}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3"
                        >
                          <CheckCheck className="h-4 w-4" />
                          <span className={TEXT_STYLES.primary}>
                            Marquer comme lu
                          </span>
                        </button>
                      )}

                      <hr className="my-1 border-gray-200 dark:border-gray-700" />
                    </div>

                    {/* Mute/Unmute */}
                    {onToggleMute && (
                      <button
                        onClick={() => handleActionClick(onToggleMute)}
                        className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3"
                      >
                        {conversation.isMuted ? (
                          <>
                            <Volume2 className="h-4 w-4" />
                            <span className={TEXT_STYLES.primary}>
                              Réactiver les notifications
                            </span>
                          </>
                        ) : (
                          <>
                            <VolumeX className="h-4 w-4" />
                            <span className={TEXT_STYLES.primary}>
                              Désactiver les notifications
                            </span>
                          </>
                        )}
                      </button>
                    )}

                    {/* Archive */}
                    {onArchive && (
                      <button
                        onClick={() => handleActionClick(onArchive)}
                        className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3"
                        aria-label={ACCESSIBILITY_LABELS.archiveConversation}
                      >
                        <Archive className="h-4 w-4" />
                        <span className={TEXT_STYLES.primary}>
                          {conversation.isArchived ? "Désarchiver" : "Archiver"}
                        </span>
                      </button>
                    )}

                    {/* Delete */}
                    {onDelete && (
                      <button
                        onClick={() => handleActionClick(onDelete)}
                        className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3 text-red-600 dark:text-red-400"
                        aria-label={ACCESSIBILITY_LABELS.deleteConversation}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span>Supprimer la conversation</span>
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Click outside to close dropdown */}
        {showDropdown && (
          <div
            className="fixed inset-0 z-0"
            onClick={() => setShowDropdown(false)}
          />
        )}
      </div>
    );
  }
);

ConversationHeader.displayName = "ConversationHeader";

export default ConversationHeader;
