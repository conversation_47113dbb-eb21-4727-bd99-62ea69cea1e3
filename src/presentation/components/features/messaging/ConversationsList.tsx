import { TEXT_STYLES } from "@/presentation/styles/common";
import { ConversationDTO } from "@/presentation/types/message.types";
import { MessageCircle, Plus, Users } from "lucide-react";
import { FC } from "react";
import { Button } from "../../common/ui/button.tsx";

interface ConversationsListProps {
  conversations: ConversationDTO[];
  activeConversation: ConversationDTO;
  handleNewConversation: () => void;
  setActiveConversation: (conversationId: number) => void;
}

const ConversationsList: FC<ConversationsListProps> = ({
  conversations,
  activeConversation,
  handleNewConversation,
  setActiveConversation,
}) => {
  return (
    <div className="flex-1 overflow-y-auto relative">
      {conversations.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-32 text-center p-4">
          <MessageCircle className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-2" />
          <p className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}>
            Aucune conversation trouvée
          </p>
          <button
            onClick={handleNewConversation}
            className={`mt-2 ${TEXT_STYLES.small} text-meddoc-primary hover:underline`}
          >
            Commencer une nouvelle conversation
          </button>
        </div>
      ) : (
        <div className="space-y-1 p-2 relative">
          {conversations.map((conversation) => (
            <div
              key={conversation.id}
              onClick={() => setActiveConversation(conversation.id)}
              className={`p-3 rounded-lg cursor-pointer transition-colors ${
                activeConversation?.id === conversation.id
                  ? "bg-meddoc-primary/10 border border-meddoc-primary/20"
                  : "hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 rounded-full bg-meddoc-primary/20 flex items-center justify-center flex-shrink-0">
                  <Users className="h-5 w-5 text-meddoc-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3
                      className={`font-medium ${TEXT_STYLES.primary} truncate`}
                    >
                      {conversation.participant.name}
                    </h3>
                    <span
                      className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}
                    >
                      {conversation.lastMessageTime}
                    </span>
                  </div>
                  <p
                    className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary} truncate mt-1`}
                  >
                    {conversation.lastMessage}
                  </p>
                  {conversation.unreadCount > 0 && (
                    <div className="mt-1">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-meddoc-primary text-white">
                        {conversation.unreadCount}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      <div className="sticky bottom-0 p-5 flex justify-end pointer-events-none">
        <Button
          onClick={handleNewConversation}
          className="w-[50px] h-[50px] rounded-full pointer-events-auto"
        >
          <Plus />
        </Button>
      </div>
    </div>
  );
};

export default ConversationsList;
