import { FC, memo } from "react";
import { User, Download, <PERSON> } from "lucide-react";
import { MessageDTO } from "@/presentation/types/message.types";
import { TEXT_STYLES } from "@/presentation/styles/common";
import {
  MESSAGE_BUBBLE_STYLES,
  USER_ROLE_STYLES,
  MESSAGE_TYPE_STYLES,
} from "@/presentation/constants/message.constants";
import { type_message_enum } from "@/domain/models/enums";

interface MessageItemProps {
  /** Message to display */
  message: MessageDTO;
  /** Whether this message is from the current user */
  isOwn: boolean;
  /** Whether to show sender avatar */
  showAvatar?: boolean;
  /** Whether to show message status */
  showStatus?: boolean;
  /** Function to format message time */
  formatTime: (date: Date) => string;
  /** Function to render status icon */
  renderStatusIcon: (status: MessageDTO["status"]) => JSX.Element;
  /** Callback when message is clicked */
  onClick?: (message: MessageDTO) => void;
  /** Callback when status is clicked */
  onStatusClick?: (message: MessageDTO) => void;
}

/**
 * MessageItem component - Renders an individual message in the conversation
 * Supports text messages, attachments, and various message types
 *
 * @param props - Component props
 * @returns {JSX.Element} The message item component
 */
const MessageItem: FC<MessageItemProps> = memo(
  ({
    message,
    isOwn,
    showAvatar = false,
    showStatus = false,
    formatTime,
    renderStatusIcon,
    onClick,
    onStatusClick,
  }) => {
    const bubbleStyles = isOwn
      ? MESSAGE_BUBBLE_STYLES.own
      : MESSAGE_BUBBLE_STYLES.other;
    const roleStyles = USER_ROLE_STYLES[message.senderRole];
    const typeStyles = MESSAGE_TYPE_STYLES[message.type];

    /**
     * Handle message click
     */
    const handleMessageClick = () => {
      onClick?.(message);
    };

    /**
     * Handle status click
     */
    const handleStatusClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      onStatusClick?.(message);
    };

    /**
     * Render message attachment
     */
    const renderAttachment = () => {
      if (!message.attachment) return null;

      const { attachment } = message;
      const isImage = attachment.type.startsWith("image/");

      return (
        <div className="mt-2">
          {isImage ? (
            <div className="relative group">
              <img
                src={attachment.thumbnailUrl || attachment.url}
                alt={attachment.name}
                className="max-w-xs rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                onClick={() => window.open(attachment.url, "_blank")}
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                <Eye className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg max-w-xs">
              <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <Download className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              </div>
              <div className="flex-1 min-w-0">
                <p className={`font-medium ${TEXT_STYLES.primary} truncate`}>
                  {attachment.name}
                </p>
                <p className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}>
                  {formatFileSize(attachment.size)}
                </p>
              </div>
              <button
                onClick={() => window.open(attachment.url, "_blank")}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
                aria-label="Télécharger le fichier"
              >
                <Download className="h-4 w-4 text-gray-600 dark:text-gray-300" />
              </button>
            </div>
          )}
        </div>
      );
    };

    /**
     * Format file size for display
     */
    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return "0 B";

      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
    };

    /**
     * Render system message
     */
    const renderSystemMessage = () => {
      return (
        <div className="flex justify-center my-4">
          <div className="bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">
            <span className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}>
              {message.content}
            </span>
          </div>
        </div>
      );
    };

    // Render system messages differently
    if (message.type === type_message_enum.system) {
      return renderSystemMessage();
    }

    return (
      <div className={`flex gap-3 ${bubbleStyles.container}`}>
        {/* Avatar for other users */}
        {!isOwn && showAvatar && (
          <div className="flex-shrink-0">
            <div
              className={`w-8 h-8 rounded-full ${roleStyles?.bgColor} flex items-center justify-center`}
            >
              <User className={`h-4 w-4 ${roleStyles?.color}`} />
            </div>
          </div>
        )}

        {/* Message content */}
        <div className="flex-1 max-w-xs lg:max-w-md">
          {/* Sender name for other users */}
          {!isOwn && showAvatar && (
            <div className="mb-1">
              <span
                className={`${TEXT_STYLES.small} font-medium ${roleStyles?.color}`}
              >
                {message.senderName}
              </span>
              <span
                className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary} ml-2`}
              >
                {roleStyles?.label}
              </span>
            </div>
          )}

          {/* Message bubble */}
          <div
            className={`${bubbleStyles.bubble} p-3 shadow-sm cursor-pointer hover:shadow-md transition-shadow`}
            onClick={handleMessageClick}
          >
            {/* Message content */}
            {message.content && (
              <p className="whitespace-pre-wrap break-words">
                {message.content}
              </p>
            )}

            {/* Attachment */}
            {renderAttachment()}

            {/* Message time and status */}
            <div
              className={`flex items-center justify-between mt-2 ${
                isOwn ? "text-white/70" : TEXT_STYLES.secondary
              }`}
            >
              <span className="text-xs">{formatTime(message.sentAt)}</span>

              {/* Message status for own messages */}
              {showStatus && isOwn && (
                <button
                  onClick={handleStatusClick}
                  className="ml-2 hover:opacity-80 transition-opacity"
                  aria-label={`Statut du message: ${message.status}`}
                >
                  {/* {renderStatusIcon(message.status)} */}
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Spacer for own messages to maintain alignment */}
        {isOwn && <div className="w-8" />}
      </div>
    );
  }
);

MessageItem.displayName = "MessageItem";

export default MessageItem;
