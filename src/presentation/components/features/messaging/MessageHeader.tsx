import { FC } from "react";
import { MessageCircle, Plus } from "lucide-react";
import { TEXT_STYLES, BUTTON_STYLES } from "@/presentation/styles/common";

interface MessageHeaderProps {
  setShowContactSelector: React.Dispatch<React.SetStateAction<boolean>>;
}

const MessageHeader: FC<MessageHeaderProps> = ({ setShowContactSelector }) => {
  const handleNewConversation = () => {
    setShowContactSelector(true);
  };

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-meddoc-primary/10">
            <MessageCircle className="h-6 w-6 text-meddoc-primary" />
          </div>
          <div>
            <h1 className={`text-2xl font-bold ${TEXT_STYLES.primary}`}>
              Messages
            </h1>
            <p className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}>
              Communiquez avec vos patients et collègues
            </p>
          </div>
        </div>
        <button
          onClick={handleNewConversation}
          className={`${BUTTON_STYLES.base} ${BUTTON_STYLES.primary} flex items-center gap-2`}
        >
          <Plus className="h-4 w-4" />
          Nouveau message
        </button>
      </div>
    </div>
  );
};

export default MessageHeader;
