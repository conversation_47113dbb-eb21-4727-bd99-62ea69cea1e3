import { FC, memo, useState, useMemo } from "react";
import { X, Search, Users, User, Stethoscope, Shield } from "lucide-react";
import { Contact } from "@/presentation/types/message.types";
import { TEXT_STYLES, BUTTON_STYLES } from "@/presentation/styles/common";
import {
  USER_ROLE_STYLES,
  ONLINE_STATUS_STYLES,
  PLACEHOLDER_TEXTS,
  ACCESSIBILITY_LABELS,
} from "@/presentation/constants/message.constants";
import { utilisateurs_role_enum } from "@/domain/models/enums";

interface ContactSelectorProps {
  /** Current user role */
  roleCurrentUser: utilisateurs_role_enum;
  /** List of available contacts */
  contacts: Contact[];
  /** Callback when a contact is selected */
  onSelectContact: (contactId: number) => void;
  /** Loading state */
  loading?: boolean;
  /** Whether to show only online contacts */
  onlineOnly?: boolean;
  /** Filter by contact role */
  roleFilter?: Contact["role"][];
}

/**
 * ContactSelector component - Modal for selecting contacts to start conversations
 * Features search, filtering, and role-based organization
 *
 * @param props - Component props
 * @returns {JSX.Element} The contact selector modal component
 */
const ContactSelector: FC<ContactSelectorProps> = memo(
  ({
    roleCurrentUser,
    contacts,
    onSelectContact,
    loading = false,
    onlineOnly = false,
    roleFilter,
  }) => {
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedRole, setSelectedRole] = useState<Contact["role"] | "all">(
      "all"
    );

    /**
     * Filter and search contacts
     */
    const filteredContacts = useMemo(() => {
      let filtered = contacts;

      // Filter by online status
      if (onlineOnly) {
        filtered = filtered.filter((contact) => contact.isOnline);
      }

      // Filter by role
      if (roleFilter && roleFilter.length > 0) {
        filtered = filtered.filter((contact) =>
          roleFilter.includes(contact.role)
        );
      }

      if (selectedRole !== "all") {
        filtered = filtered.filter((contact) => contact.role === selectedRole);
      }

      // Filter by search query
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.name.toLowerCase().includes(query) ||
            contact.email?.toLowerCase().includes(query) ||
            contact.specialty?.toLowerCase().includes(query) ||
            contact.department?.toLowerCase().includes(query)
        );
      }

      return filtered;
    }, [contacts, onlineOnly, roleFilter, selectedRole, searchQuery]);

    /**
     * Group contacts by role
     */
    const groupedContacts = useMemo(() => {
      const groups: Record<Contact["role"], Contact[]> = {
        patient: [],
        professionnel: [],
        admin: [],
      };

      filteredContacts.forEach((contact) => {
        groups[contact.role].push(contact);
      });

      return groups;
    }, [filteredContacts]);

    /**
     * Get role icon component
     */
    const getRoleIcon = (role: Contact["role"]) => {
      switch (role) {
        case utilisateurs_role_enum.PATIENT:
          return <User className="h-4 w-4" />;
        case utilisateurs_role_enum.PROFESSIONNEL:
          return <Stethoscope className="h-4 w-4" />;
        case utilisateurs_role_enum.ADMIN:
          return <Shield className="h-4 w-4" />;
        default:
          return <Users className="h-4 w-4" />;
      }
    };

    /**
     * Handle contact selection
     */
    const handleContactSelect = (contactId: number) => {
      onSelectContact(contactId);
    };

    /**
     * Render contact item
     */
    const renderContactItem = (contact: Contact) => {
      const roleStyles = USER_ROLE_STYLES[contact.role];

      return (
        <button
          key={contact.id}
          onClick={() => handleContactSelect(contact.id)}
          className="w-full p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors text-left"
        >
          <div className="flex items-center gap-3">
            {/* Avatar */}
            <div className="relative flex-shrink-0">
              <div
                className={`w-10 h-10 rounded-full ${roleStyles.bgColor} flex items-center justify-center`}
              >
                {getRoleIcon(contact.role)}
              </div>

              {/* Online Status */}
              <div className="absolute -bottom-1 -right-1">
                <div
                  className={`w-3 h-3 rounded-full border-2 border-white dark:border-gray-800 ${
                    contact.isOnline
                      ? ONLINE_STATUS_STYLES.online.color
                      : ONLINE_STATUS_STYLES.offline.color
                  }`}
                />
              </div>
            </div>

            {/* Contact Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <h3 className={`font-medium ${TEXT_STYLES.primary} truncate`}>
                  {contact.name}
                </h3>
                <span
                  className={`px-2 py-0.5 rounded-full text-xs font-medium ${roleStyles.bgColor} ${roleStyles.color}`}
                >
                  {roleStyles.label}
                </span>
              </div>

              <div className="space-y-1">
                {contact.email && (
                  <p
                    className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary} truncate`}
                  >
                    {contact.email}
                  </p>
                )}

                {contact.specialty && (
                  <p
                    className={`${TEXT_STYLES.small} text-meddoc-primary truncate`}
                  >
                    {contact.specialty}
                  </p>
                )}

                {contact.department && (
                  <p
                    className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary} truncate`}
                  >
                    {contact.department}
                  </p>
                )}
              </div>
            </div>

            {/* Online Status Text */}
            <div className="flex-shrink-0">
              <span
                className={`${TEXT_STYLES.small} ${
                  contact.isOnline
                    ? "text-green-600 dark:text-green-400"
                    : TEXT_STYLES.secondary
                }`}
              >
                {contact.isOnline ? "En ligne" : "Hors ligne"}
              </span>
            </div>
          </div>
        </button>
      );
    };

    return (
      <div>
        <div className="flex flex-col">
          {/* Search and Filters */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            {/* Search Bar */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder={PLACEHOLDER_TEXTS.searchContacts}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg 
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                       focus:border-meddoc-primary focus:ring-2 focus:ring-meddoc-primary/20 
                       focus:outline-none transition-colors"
              />
            </div>

            {/* Role Filter */}
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => setSelectedRole("all")}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  selectedRole === "all"
                    ? "bg-meddoc-primary text-white"
                    : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                }`}
              >
                Tous
              </button>

              {Object.entries(USER_ROLE_STYLES).map(([role, styles]) =>
                roleCurrentUser !== utilisateurs_role_enum.PATIENT ||
                role !== utilisateurs_role_enum.PATIENT ? (
                  <button
                    key={role}
                    onClick={() => setSelectedRole(role as Contact["role"])}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors flex items-center gap-1 ${
                      selectedRole === role
                        ? "bg-meddoc-primary text-white"
                        : `${styles.bgColor} ${styles.color} hover:opacity-80`
                    }`}
                  >
                    {getRoleIcon(role as Contact["role"])}
                    {styles.label}
                  </button>
                ) : null
              )}
            </div>
          </div>

          {/* Contact List */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-meddoc-primary"></div>
              </div>
            ) : filteredContacts.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                <h3
                  className={`text-lg font-medium ${TEXT_STYLES.primary} mb-2`}
                >
                  {PLACEHOLDER_TEXTS.noContacts}
                </h3>
                <p className={TEXT_STYLES.secondary}>
                  {searchQuery
                    ? "Essayez de modifier votre recherche"
                    : "Aucun contact disponible pour le moment"}
                </p>
              </div>
            ) : selectedRole === "all" ? (
              // Grouped by role
              <div className="space-y-6">
                {Object.entries(groupedContacts).map(([role, roleContacts]) => {
                  if (roleContacts.length === 0) return null;

                  const roleStyles = USER_ROLE_STYLES[role as Contact["role"]];

                  return (
                    <div key={role}>
                      <h3
                        className={`font-medium ${TEXT_STYLES.primary} mb-3 flex items-center gap-2`}
                      >
                        {getRoleIcon(role as Contact["role"])}
                        {roleStyles.label} ({roleContacts.length})
                      </h3>
                      <div className="space-y-1">
                        {roleContacts.map(renderContactItem)}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              // Single role
              <div className="space-y-1">
                {filteredContacts.map(renderContactItem)}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);

ContactSelector.displayName = "ContactSelector";

export default ContactSelector;
