import { Search } from "lucide-react";
import { FC } from "react";

interface MessageSearchBarProps {
  searchQuery: string;
  handleSearchChange: (query: string) => void;
}

const MessageSearchBar: FC<MessageSearchBarProps> = ({
  searchQuery,
  handleSearchChange,
}) => {
  return (
    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          type="text"
          placeholder="Rechercher une conversation..."
          value={searchQuery}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                         focus:border-meddoc-primary focus:ring-2 focus:ring-meddoc-primary/20
                         focus:outline-none transition-colors"
        />
      </div>
    </div>
  );
};

export default MessageSearchBar;
