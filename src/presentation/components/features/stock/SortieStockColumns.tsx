import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { Eye } from "lucide-react";
import { SortieStockRowDTO } from "@/domain/DTOS/StockDTO";
import { IconButton } from "@mui/material";

export const SortieStockColumns = (): GridColDef[] => {
  const getTypeSortieColor = (type: string) => {
    switch (type) {
      case "vente":
        return "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300";
      case "utilisation":
        return "bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300";
      case "perte":
        return "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300";
      default:
        return "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300";
    }
  };

  const getTypeSortieLabel = (type: string) => {
    switch (type) {
      case "vente":
        return "Vente";
      case "utilisation":
        return "Utilisation";
      case "perte":
        return "Perte";
      default:
        return type;
    }
  };

  return [
    {
      field: "produit",
      headerName: "Produit",
      flex: 1,
      renderCell: (params: GridRenderCellParams<SortieStockRowDTO>) => {
        return (
          <div className="flex gap-1 items-center h-full min-h-[40px]">
            <div>
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {params.row.produit || "Produit non reconnu"}
              </div>
            </div>
          </div>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "quantite",
      headerName: "Quantité",
      flex: 0.5,
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "type_sortie",
      headerName: "Type",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<SortieStockRowDTO>) => {
        const type = params.row.type_sortie;
        return (
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeSortieColor(type)}`}
          >
            {getTypeSortieLabel(type)}
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "destinataire",
      headerName: "Destinataire",
      flex: 1,
      renderCell: (params: GridRenderCellParams<SortieStockRowDTO>) => {
        return params.row.destinataire || "Non spécifié";
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "date_sortie",
      headerName: "Date",
      flex: 1,
      renderCell: (params: GridRenderCellParams<SortieStockRowDTO>) => {
        return new Date(params.row.date_sortie).toLocaleDateString("fr-FR");
      },
      headerClassName:
        "font-semibold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "lot",
      headerName: "Lot",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<SortieStockRowDTO>) => {
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {params.row.lot}
          </span>
        );
      },
      headerClassName:
        "font-semibold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.5,
      sortable: false,
      renderCell: (params: GridRenderCellParams<SortieStockRowDTO>) => {
        return (
          <div className="flex gap-1 justify-center items-center h-full min-h-[40px]">
            <IconButton
              size="small"
              className="text-blue-600 hover:text-blue-800"
              onClick={() => {
                // Ajouter la logique pour voir les détails
                console.log("Voir détails:", params.row);
              }}
            >
              <Eye size={16} />
            </IconButton>
          </div>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
  ];
};
