import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { Archive } from "lucide-react";
import { formatDate } from "@/presentation/pages/patient/profilePatients/formatDate";

/**
 * Interface pour un mouvement récent
 */
interface MouvementRecent {
  id?: number;
  name: string;
  type: "Entrée" | "Sortie";
  quantite: number;
  date: string;
}

/**
 * Définition des colonnes pour le tableau des mouvements récents
 * Respecte les principes SOLID avec une responsabilité unique
 */
export const MouvementRecentColumns = (): GridColDef[] => {
  return [
    {
      field: "name",
      headerName: "Produit",
      flex: 1,
      renderCell: (params: GridRenderCellParams<MouvementRecent>) => {
        const isEntree = params.row.type === "Entrée";
        return (
          <div className="flex items-center h-full min-h-[40px] dark:text-white">
            <div
              className={`p-2 rounded-lg mr-3 ${
                isEntree
                  ? "bg-green-100 dark:bg-green-900/20"
                  : "bg-red-100 dark:bg-red-900/20"
              }`}
            >
              <Archive
                size={16}
                className={isEntree ? "text-green-600" : "text-red-600"}
              />
            </div>
            <div
              className="text-sm font-medium text-gray-900 dark:text-white"
              title={params.row.name}
            >
              {params.row.name?.substring(0, 20) +
                (params.row.name?.length > 20 ? "..." : "") || "N/A"}
            </div>
          </div>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "type",
      headerName: "Type",
      flex: 0.6,
      renderCell: (params: GridRenderCellParams<MouvementRecent>) => {
        const isEntree = params.row.type === "Entrée";
        return (
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              isEntree
                ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300"
                : "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300"
            }`}
          >
            {params.row.type}
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "quantite",
      headerName: "Quantité",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<MouvementRecent>) => {
        return `${params.row.quantite} unités`;
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "date",
      headerName: "Date",
      flex: 1,
      renderCell: (params: GridRenderCellParams<MouvementRecent>) => {
        return formatDate(new Date(params.row.date));
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
  ];
};
