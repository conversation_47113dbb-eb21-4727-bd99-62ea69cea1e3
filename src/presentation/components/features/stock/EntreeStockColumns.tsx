import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { Package, Eye } from "lucide-react";
import { EntreeStockRowDTO } from "@/domain/DTOS/StockDTO";
import { IconButton } from "@mui/material";

export const EntreeStockColumns = (): GridColDef[] => {
  return [
    {
      field: "produit",
      headerName: "Produit",
      flex: 1,
      headerClassName:
        "font-bold bg-meddoc-primary dark:bg-gray-600 text-white",
    },
    {
      field: "fournisseur",
      headerName: "Fournisseur",
      flex: 1,
      headerClassName:
        "font-bold bg-meddoc-primary dark:bg-gray-600 text-white",
    },
    {
      field: "quantite",
      headerName: "Quantité",
      flex: 0.5,
      headerClassName:
        "font-bold bg-meddoc-primary dark:bg-gray-600 text-white",
    },
    {
      field: "prix_unitaire",
      headerName: "Prix unitaire",
      flex: 1,
      renderCell: (params: GridRenderCellParams<EntreeStockRowDTO>) => {
        return `${params.row.prix_unitaire?.toFixed(2) || 0} AR`;
      },
      headerClassName:
        "font-bold bg-meddoc-primary dark:bg-gray-600 text-white",
    },
    {
      field: "date_entree",
      headerName: "Date",
      flex: 1,
      renderCell: (params: GridRenderCellParams<EntreeStockRowDTO>) => {
        return new Date(params.row.date_entree).toLocaleDateString("fr-FR");
      },
      headerClassName:
        "font-bold bg-meddoc-primary dark:bg-gray-600 text-white",
    },
    {
      field: "lot",
      headerName: "Lot",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<EntreeStockRowDTO>) => {
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {params.row.lot}
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary dark:bg-gray-600 text-white dark:bg-blue-500 dark:text-white",
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.5,
      sortable: false,
      renderCell: (params: GridRenderCellParams<EntreeStockRowDTO>) => {
        return (
          <div className="flex gap-1 justify-center items-center h-full min-h-[40px]">
            <IconButton
              size="small"
              className="text-meddoc-primary hover:text-blue-800"
              onClick={() => {
                // Ajouter la logique pour voir les détails
                console.log("Voir détails:", params.row);
              }}
            >
              <Eye size={16} />
            </IconButton>
          </div>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary dark:bg-gray-600 text-white",
    },
  ];
};
