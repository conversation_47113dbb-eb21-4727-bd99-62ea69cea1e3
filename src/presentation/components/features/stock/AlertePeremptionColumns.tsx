import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { AlertTriangle } from "lucide-react";
import { LotWithStockData } from "@/domain/DTOS/StockDTO";

/**
 * Définition des colonnes pour le tableau des alertes de péremption
 * Respecte les principes SOLID avec une responsabilité unique
 */
export const AlertePeremptionColumns = (): GridColDef[] => {
  return [
    {
      field: "stocks",
      headerName: "Produit",
      flex: 1,
      renderCell: (params: GridRenderCellParams<LotWithStockData>) => {
        const produitNom = params.row.stocks?.nom || "N/A";
        return (
          <div className="flex items-center h-full min-h-[40px] dark:text-white">
            <div className="p-2 rounded-lg bg-red-100 mr-3">
              <AlertTriangle size={16} className="text-red-600" />
            </div>
            <div
              className="text-sm font-medium text-gray-900 dark:text-white"
              title={produitNom}
            >
              {produitNom}
            </div>
          </div>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "date_expiration",
      headerName: "Date de péremption",
      flex: 1,
      renderCell: (params: GridRenderCellParams<LotWithStockData>) => {
        return new Date(params.row.date_expiration).toLocaleDateString("fr-FR");
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "numero_lot",
      headerName: "Lot",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<LotWithStockData>) => {
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300">
            {params.row.numero_lot}
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "statut",
      headerName: "Statut",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<LotWithStockData>) => {
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300">
            Critique
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
  ];
};
