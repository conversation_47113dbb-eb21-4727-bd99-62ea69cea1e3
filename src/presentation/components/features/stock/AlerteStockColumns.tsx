import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { TrendingUp } from "lucide-react";
import { LowStockCardDTO } from "@/domain/DTOS/StockDTO";

/**
 * Définition des colonnes pour le tableau des alertes de stock bas
 * Respecte les principes SOLID avec une responsabilité unique
 */
export const AlerteStockColumns = (): GridColDef[] => {
  return [
    {
      field: "produit",
      headerName: "Produit",
      flex: 1,
      renderCell: (params: GridRenderCellParams<LowStockCardDTO>) => {
        return (
          <div className="flex items-center h-full min-h-[40px] dark:text-white">
            <div className="p-2 rounded-lg bg-orange-100 mr-3">
              <TrendingUp size={16} className="text-orange-600" />
            </div>
            <div
              className="text-sm font-medium text-gray-900 dark:text-white"
              title={params.row.produit}
            >
              {params.row.produit?.substring(0, 20) +
                (params.row.produit?.length > 20 ? "..." : "") || "N/A"}
            </div>
          </div>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "stock_actuel",
      headerName: "Stock actuel",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<LowStockCardDTO>) => {
        return (
          <span className="text-sm font-medium text-orange-600 dark:text-orange-400">
            {params.row.stock_actuel}
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "seuil_alerte",
      headerName: "Seuil critique",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<LowStockCardDTO>) => {
        return params.row.seuil_alerte;
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "statut",
      headerName: "Statut",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<LowStockCardDTO>) => {
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-300">
            Stock faible
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
  ];
};
