import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { Truck } from "lucide-react";
import { Fournisseurs } from "@/domain/models/Fournisseurs";

/**
 * Définition des colonnes pour le tableau des fournisseurs
 * Respecte les principes SOLID avec une responsabilité unique
 */
export const FournisseurColumns = (): GridColDef[] => {
  return [
    {
      field: "nom",
      headerName: "Fournisseur",
      flex: 1,
      renderCell: (params: GridRenderCellParams<Fournisseurs>) => {
        return (
          <div className="flex items-center h-full min-h-[40px]">
            <div className="p-2 rounded-lg bg-green-100 mr-3">
              <Truck size={16} className="text-green-600" />
            </div>
            <div
              className="text-sm font-medium text-gray-900 dark:text-white"
              title={params.row.nom}
            >
              {params.row.nom?.substring(0, 20) +
                (params.row.nom?.length > 20 ? "..." : "") || "N/A"}
            </div>
          </div>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "telephone",
      headerName: "Téléphone",
      flex: 1,
      renderCell: (params: GridRenderCellParams<Fournisseurs>) => {
        return params.row.telephone || "Non renseigné";
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "adresse",
      headerName: "Adresse",
      flex: 1.2,
      renderCell: (params: GridRenderCellParams<Fournisseurs>) => {
        const adresse = params.row.adresse || "Non renseignée";
        return (
          <span
            className="text-sm text-gray-500 dark:text-white"
            title={adresse}
          >
            {adresse.substring(0, 30) + (adresse.length > 30 ? "..." : "")}
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
  ];
};
