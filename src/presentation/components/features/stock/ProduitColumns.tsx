import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { Package } from "lucide-react";
import { Stocks } from "@/domain/models/Stocks";

/**
 * Définition des colonnes pour le tableau des produits
 * Respecte les principes SOLID avec une responsabilité unique
 */
export const ProduitColumns = (): GridColDef[] => {
  return [
    {
      field: "nom",
      headerName: "Produit",
      flex: 1,
      renderCell: (params: GridRenderCellParams<Stocks>) => {
        return (
          <div className="flex items-center h-full min-h-[40px]">
            <div className="p-2 rounded-lg bg-blue-100 mr-3">
              <Package size={16} className="text-blue-600" />
            </div>
            <div
              className="text-sm font-medium text-gray-900 dark:text-white"
              title={params.row.nom}
            >
              {params.row.nom?.substring(0, 20) +
                (params.row.nom?.length > 20 ? "..." : "") || "N/A"}
            </div>
          </div>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "unite",
      headerName: "Unité",
      flex: 0.5,
      renderCell: (params: GridRenderCellParams<Stocks>) => {
        return params.row.unite || "N/A";
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "seuil_alerte",
      headerName: "Seuil d'alerte",
      flex: 0.8,
      renderCell: (params: GridRenderCellParams<Stocks>) => {
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-300">
            {params.row.seuil_alerte}
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
    {
      field: "description",
      headerName: "Description",
      flex: 1.2,
      renderCell: (params: GridRenderCellParams<Stocks>) => {
        const description = params.row.description || "Aucune description";
        return (
          <span
            className="text-sm text-gray-500 dark:text-white"
            title={description}
          >
            {description.substring(0, 30) +
              (description.length > 30 ? "..." : "")}
          </span>
        );
      },
      headerClassName:
        "font-bold bg-meddoc-primary text-white dark:bg-gray-600",
    },
  ];
};
