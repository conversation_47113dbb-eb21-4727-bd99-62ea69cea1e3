import { FC, memo } from "react";
import { Prescription } from "@/presentation/types/patient.types";
import { Pill, RefreshCw } from "lucide-react";

interface ActivePrescriptionsProps {
  prescriptions: Prescription[];
  onRequestRefill: (id: string) => void;
}

const ActivePrescriptions: FC<ActivePrescriptionsProps> = memo(
  ({ prescriptions, onRequestRefill }) => {
    return (
      <div className="rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Ordonnances actives</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {prescriptions.map((prescription) => (
            <div key={prescription.id} className="p-4">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                <div className="flex items-start gap-3 flex-grow">
                  <div className="p-2 bg-blue-50 rounded-lg flex-shrink-0">
                    <Pill className="h-5 w-5 text-blue-500" />
                  </div>
                  <div className="min-w-0 flex-grow">
                    <div className="flex items-start justify-between gap-2">
                      <p className="font-medium truncate">
                        {prescription.medication}
                      </p>
                      {prescription.refillsLeft > 0 && (
                        <span className="px-2 py-0.5 rounded-full text-xs bg-green-100 text-green-800 whitespace-nowrap flex-shrink-0">
                          {prescription.refillsLeft} renouvellement
                          {prescription.refillsLeft > 1 ? "s" : ""}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {prescription.dosage} • {prescription.frequency}
                    </p>
                    <p className="text-sm text-gray-600 mt-1">
                      Dr. {prescription.doctorName}
                    </p>
                    <p className="text-sm text-gray-600 mt-1">
                      {prescription.startDate} - {prescription.endDate}
                    </p>
                  </div>
                </div>
                {prescription.refillsLeft > 0 && (
                  <button
                    onClick={() => onRequestRefill(prescription.id)}
                    className="flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md transition-colors border border-blue-200 w-full sm:w-auto justify-center sm:justify-start"
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span>Renouveler</span>
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
);

ActivePrescriptions.displayName = "ActivePrescriptions";

export default ActivePrescriptions;
