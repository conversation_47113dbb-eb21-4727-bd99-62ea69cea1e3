import {
    Card,
    CardContent,
    Typography,
    Box,
    Chip,
    styled,
    Button,
    CardActions,
    Divider,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    DialogContentText,
} from "@mui/material";
import {
    Palette as MotifIcon,
    CalendarToday as CalendarIcon,
    AccessTime as TimeIcon,
    LocationOn as LocationIcon,
    EventBusy as CancelIcon,
} from "@mui/icons-material";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { PRIMARY } from "@/shared/constants/Color";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { useState } from "react";

// Interface pour les props du composant
interface AppointmentCardProps {
    appointment: AppointmentPatientDTO;
}

// Styled Card avec bordure bleue à gauche
const StyledAppointmentCard = styled(Card)(({ theme }) => ({
    border: "1px solid",
    borderColor: theme.palette.divider,
    borderLeft: `4px solid ${PRIMARY}`,
    boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
    borderRadius: theme.spacing(1),
    cursor: "pointer",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    "&:hover": {
        borderLeftWidth: "6px",
        boxShadow: "0 4px 16px rgba(39, 170, 225, 0.15)",
        transform: "translateY(-2px)",
    },
}));

// Fonction pour obtenir la couleur du statut
const getStatusColor = (status: AppointmentPatientDTO["statut"]) => {
    switch (status) {
        case "A venir":
            return {
                backgroundColor: "primary.main",
                color: "white",
            };
        case "Manquer":
            return {
                backgroundColor: "#ffebee",
                color: "#d32f2f",
            };
        case "Annuler":
            return {
                backgroundColor: "#ffebee",
                color: "#d32f2f",
            };
        case "Terminer":
            return {
                backgroundColor: "#e3f2fd",
                color: "#1976d2",
            };
        case "Reporter":
            return {
                backgroundColor: "#fff3e0",
                color: "#f57c00",
            };
        default:
            return {
                backgroundColor: "#f5f5f5",
                color: "#757575",
            };
    }
};

const AppointmentCard = ({
    appointment,
}: AppointmentCardProps) => {
    const { handleCancelAppointment } = useConsultationState();
    const statusColors = getStatusColor(appointment.statut);
    const [openCancelDialog, setOpenCancelDialog] = useState(false);

    const canCancel = appointment.statut === rendez_vous_statut_enum.A_VENIR ||
        appointment.statut === rendez_vous_statut_enum.REPORTER;

    // Fonctions pour gérer la modale de confirmation
    const handleOpenCancelDialog = () => {
        setOpenCancelDialog(true);
    };

    const handleCloseCancelDialog = () => {
        setOpenCancelDialog(false);
    };

    const handleConfirmCancel = () => {
        handleCancelAppointment({
            id: appointment.id,
            time: appointment.time,
            date_rendez_vous: new Date(appointment.date_rendez_vous),
            statut: appointment.statut,
            id_professionnel: appointment.professional.id,
            motif: appointment.motif,
            raison: appointment.raison,
            categorie: "",
            patient: {
                id: appointment.patient_id,
                nom: "Patient",
                prenom: "",
                sexe: "HOMME" as any,
                date_naissance: new Date(),
                province: "",
                district: "",
                commune: "",
                region: "",
                situation_matrimonial: "",
                telephone: "",
                unique_id: `patient_${appointment.patient_id}`
            }
        });
        setOpenCancelDialog(false);
    };

    return (
        <>
            <StyledAppointmentCard>
                <CardContent sx={{ padding: 3 }}>
                    {/* Titre et statut */}
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                        <Typography
                            variant="h6"
                            component="h3"
                            sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "text.primary",
                            }}
                        >
                            Dr {appointment.professional.prenom}
                        </Typography>
                        <Chip
                            label={appointment.statut}
                            size="small"
                            sx={{
                                ...statusColors,
                                fontWeight: 500,
                                fontSize: "0.75rem",
                                height: 24,
                            }}
                        />
                    </Box>

                    {/* Informations de la consultation */}
                    <Box display="flex" flexDirection="column" gap={1.5}>
                        {/* Professionnel */}
                        <Box display="flex" alignItems="center">
                            <MotifIcon
                                sx={{
                                    fontSize: 18,
                                    mr: 1,
                                    color: "text.secondary"
                                }}
                            />
                            <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontSize: "0.9rem" }}
                            >
                                {appointment.motif}
                            </Typography>
                        </Box>

                        {/* Date */}
                        <Box display="flex" alignItems="center">
                            <CalendarIcon
                                sx={{
                                    fontSize: 18,
                                    mr: 1,
                                    color: "text.secondary"
                                }}
                            />
                            <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontSize: "0.9rem" }}
                            >
                                {format(new Date(appointment.date_rendez_vous), "EEEE dd MMMM yyyy", { locale: fr })}
                            </Typography>
                        </Box>

                        {/* Heure */}
                        <Box display="flex" alignItems="center">
                            <TimeIcon
                                sx={{
                                    fontSize: 18,
                                    mr: 1,
                                    color: "text.secondary"
                                }}
                            />
                            <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontSize: "0.9rem" }}
                            >
                                {appointment.time}
                            </Typography>
                        </Box>

                        {/* Adresse */}
                        <Box display="flex" alignItems="center">
                            <LocationIcon
                                sx={{
                                    fontSize: 18,
                                    mr: 1,
                                    color: "text.secondary"
                                }}
                            />
                            <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontSize: "0.9rem" }}
                            >
                                {appointment.professional.adresse}, {appointment.professional.commune}
                            </Typography>
                        </Box>
                    </Box>
                </CardContent>

                {/* Actions */}
                {canCancel && (
                    <>
                        <Divider />
                        <CardActions sx={{ padding: 2, justifyContent: "flex-end" }}>
                            <Button
                                variant="outlined"
                                size="small"
                                startIcon={<CancelIcon />}
                                onClick={handleOpenCancelDialog}
                                sx={{
                                    borderColor: "#d32f2f",
                                    color: "#d32f2f",
                                    "&:hover": {
                                        borderColor: "#b71c1c",
                                        backgroundColor: "rgba(211, 47, 47, 0.04)",
                                    },
                                    textTransform: "none",
                                    fontWeight: 500,
                                }}
                            >
                                Annuler
                            </Button>
                        </CardActions>
                    </>
                )}
            </StyledAppointmentCard>

            {/* Modale de confirmation d'annulation */}
            <Dialog
                open={openCancelDialog}
                onClose={handleCloseCancelDialog}
                aria-labelledby="cancel-dialog-title"
                aria-describedby="cancel-dialog-description"
            >
                <DialogTitle id="cancel-dialog-title" className="text-center">
                    Confirmer l'annulation
                </DialogTitle>
                <DialogContent>
                    <DialogContentText id="cancel-dialog-description">
                        Êtes-vous sûr de vouloir annuler ce rendez-vous avec{" "}
                        <strong>
                            Dr. {appointment.professional.nom} {appointment.professional.prenom}
                        </strong>{" "}
                        prévu le{" "}
                        <strong>
                            {format(new Date(appointment.date_rendez_vous), "dd MMMM yyyy", { locale: fr })}
                        </strong>{" "}
                        à{" "}
                        <strong>{appointment.time}</strong> ?
                        <br />
                        <br />
                        Cette action est irréversible.
                    </DialogContentText>
                </DialogContent>
                <DialogActions sx={{ p: 2, display: "flex", justifyContent: "center" }}>
                    <Button
                        onClick={handleCloseCancelDialog}
                        color="primary"
                    >
                        Non
                    </Button>
                    <Button
                        onClick={handleConfirmCancel}
                        color="error"
                        variant="contained"
                        autoFocus
                    >
                        Oui
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default AppointmentCard;
