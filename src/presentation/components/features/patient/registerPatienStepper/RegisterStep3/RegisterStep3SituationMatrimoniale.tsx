import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { TextField, MenuItem, FormHelperText, FormControl } from "@mui/material";

const RegisterStep3SituationMatrimoniale = () => {
  const {
    errors,
    situation_matrimonial,
    handleSituationMatrimonialeChange,
  } = useRegisterPatientState()
  return (
    <FormControl fullWidth error={!!errors.situation_matrimonial}>
      <TextField
        value={situation_matrimonial || "celibataire"}
        onChange={(e) => handleSituationMatrimonialeChange(e.target.value)}
        select
        fullWidth
        label="Situation matrimoniale*"
      >
        <MenuItem value="marie"><PERSON><PERSON>(e)</MenuItem>
        <MenuItem value="celibataire">Célibataire</MenuItem>
      </TextField>
      <FormHelperText>{errors.situation_matrimonial}</FormHelperText>
    </FormControl>
  );
};

export default RegisterStep3SituationMatrimoniale;
