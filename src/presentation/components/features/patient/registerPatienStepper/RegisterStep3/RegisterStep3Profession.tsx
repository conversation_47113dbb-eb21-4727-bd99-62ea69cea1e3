import { TextField } from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const RegisterStep3Profession = () => {
  const {
    errors,
    profession,
    handleProfessionChange
  } = useRegisterPatientState()
  return (
    <TextField
      value={profession}
      onChange={(e) => handleProfessionChange(e.target.value)}
      label="Profession"
      type="text"
      error={!!errors.profession}
      helperText={errors.profession}
      fullWidth
    />
  );
};

export default RegisterStep3Profession;
