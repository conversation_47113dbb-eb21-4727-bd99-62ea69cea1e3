import {
  Control,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";
import { PatientFormData } from "@/shared/schemas/PatientShema";
import { Patient } from "@/domain/models";
import FormField from "@/presentation/components/common/ui/FormField";
import { Mail, Shield } from "lucide-react";
import Title from "../RegisterStep1/Title";
import { PATIENT_STEPS } from "@/shared/constants/PatientSteps";

interface RegisterPatientStep2Props {
  control: Control<PatientFormData>;
  patient?: Patient;
  errors: FieldErrors<PatientFormData>;
  onSubmit: () => Promise<void>;
  register: UseFormRegister<PatientFormData>;
  setValue: UseFormSetValue<PatientFormData>;
}

const RegisterStep2 = ({ errors, register }: RegisterPatientStep2Props) => {
  return (
    <>
      <Title
        title={PATIENT_STEPS[1]}
        subTitle="Renseignez les informations personnelles pour créer le compte"
      />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
        <FormField
          id="email"
          label="Email"
          type="email"
          placeholder="Entrez votre email"
          icon={Mail}
          register={register}
          required
          error={errors.email}
          validation={{
            required: "L'email est requis",
            pattern: {
              value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
              message: "Format d'email invalide",
            },
            maxLength: {
              value: 100,
              message: "L'email ne peut pas dépasser 100 caractères",
            },
          }}
        />
        <div></div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
        <FormField
          id="mot_de_passe"
          type="password"
          label="Mot de passe"
          placeholder="Entrez votre mot de passe"
          icon={Shield}
          register={register}
          required
          error={errors.mot_de_passe}
          showPasswordStrength={true}
          helpText="Le mot de passe doit contenir au moins 8 caractères avec majuscules, minuscules, chiffres et caractères spéciaux"
          validation={{
            required: "Le mot de passe est requis",
            minLength: {
              value: 8,
              message: "Le mot de passe doit contenir au moins 8 caractères",
            },
            maxLength: {
              value: 100,
              message: "Le mot de passe ne peut pas dépasser 100 caractères",
            },
            pattern: {
              value:
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
              message:
                "Le mot de passe doit contenir au moins: 1 minuscule, 1 majuscule, 1 chiffre et 1 caractère spécial",
            },
          }}
        />
        <FormField
          id="confirmation_mot_de_passe"
          type="password"
          label="Confirmer le mot de passe"
          placeholder="Confirmez votre mot de passe"
          icon={Shield}
          register={register}
          required
          error={errors.confirmation_mot_de_passe}
          helpText="Doit être identique au mot de passe"
          validation={{
            required: "La confirmation du mot de passe est requise",
          }}
        />
      </div>
    </>
  );
};

export default RegisterStep2;
