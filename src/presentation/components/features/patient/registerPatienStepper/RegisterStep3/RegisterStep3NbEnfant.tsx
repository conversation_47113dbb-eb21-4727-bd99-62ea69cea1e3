import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { TextField } from "@mui/material";

const RegisterStep3NbEnfant = () => {
  const {
    errors,
    nb_enfant,
    handleNbEnfantChange
  } = useRegisterPatientState()
  return (
    <TextField
      value={nb_enfant}
      onChange={(e) => handleNbEnfantChange(e.target.value === '' ? null : Number(e.target.value))}
      label="Nombre d'enfant"
      type="number"
      fullWidth
      inputProps={{ min: 0 }}
      error={!!errors.nb_enfant}
      helperText={errors.nb_enfant}
    />
  );
};

export default RegisterStep3NbEnfant;
