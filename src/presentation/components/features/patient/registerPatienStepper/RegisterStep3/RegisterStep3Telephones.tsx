import {
  TextField,
  IconButton,
  Button,
} from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { PRIMARY } from "@/shared/constants/Color";
import { PlusIcon, TrashIcon } from "lucide-react";

const RegisterStep3Telephones = () => {
  const {
    errors,
    contacts,
    handleContactChange,
    handleAddContact,
    handleRemoveContact,
  } = useRegisterPatientState()

  return (
    <div>
      <div className="space-y-5">
        {contacts.map((field, index) => (
          <div className="flex items-center gap-2" key={index}>
            <TextField
              value={field.numero}
              onChange={(e) => handleContactChange(index, e.target.value)}
              label={`Téléphone ${index + 1}*`}
              type="tel"
              fullWidth
              // error={!!errors.telephones?.[index]?.numero}
              // helperText={errors.telephones?.[index]?.numero}
            />
            <IconButton
              onClick={() => handleRemoveContact(index)}
              color="error"
              disabled={contacts.length <= 1}
            >
              <TrashIcon />
            </IconButton>
          </div>
        ))}
        <Button
          variant="contained"
          onClick={handleAddContact}
          color="primary"
          sx={{ mt: 1, textTransform: "none", backgroundColor: PRIMARY, borderRadius: 5 }}
          startIcon={<PlusIcon size={16} />}
        >
          Ajouter un numéro
        </Button>
      </div>
    </div>
  );
};

export default RegisterStep3Telephones;
