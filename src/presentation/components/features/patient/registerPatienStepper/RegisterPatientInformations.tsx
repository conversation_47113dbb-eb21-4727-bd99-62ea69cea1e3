import { PATIENT_STEPS } from "@/shared/constants/PatientSteps";
import { motion } from "framer-motion";
import { Heart, Phone, User } from "lucide-react";

/**
 * Props pour le composant RegisterPatientInformations
 */
interface RegisterPatientInformationsProps {
  /** Index de l'étape actuellement active pour afficher le contexte approprié */
  activeStep: number;
}

/**
 * Panneau d'informations latéral pour le formulaire d'inscription patient
 *
 * Ce composant affiche la section gauche du formulaire d'inscription avec :
 * - Un message de bienvenue et une description de MEDDoC
 * - Une liste des avantages et fonctionnalités de la plateforme
 * - Un indicateur de progression contextuel
 * - Des éléments décoratifs et animations pour améliorer l'UX
 *
 * Le design utilise un dégradé de couleurs de la marque et des animations
 * échelonnées pour créer une expérience visuelle engageante.
 *
 * @component
 * @example
 * ```tsx
 * <RegisterPatientInformations activeStep={0} />
 * ```
 *
 * @param {RegisterPatientInformationsProps} props - Les propriétés du composant
 * @param {number} props.activeStep - L'index de l'étape active pour le contexte
 *
 * @returns {JSX.Element} Panneau d'informations avec avantages et progression
 *
 * @features
 * - Animations d'entrée échelonnées pour chaque avantage
 * - Design responsive avec dégradé de marque
 * - Indicateur de progression contextuel
 * - Éléments décoratifs avec formes géométriques
 *
 * @dependencies
 * - PATIENT_STEPS: Configuration des étapes
 * - Lucide React: Icônes pour les avantages
 * - Framer Motion: Animations d'entrée
 *
 */
const RegisterPatientInformations = ({
  activeStep,
}: RegisterPatientInformationsProps): JSX.Element => {
  return (
    <div className="lg:w-2/5 bg-gradient-to-br from-meddoc-primary to-meddoc-secondary p-8 lg:p-12 text-white relative overflow-hidden">
      {/* Éléments décoratifs */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-10 -mt-10" />
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -ml-8 -mb-8" />

      <div className="relative z-10 h-full flex flex-col">
        <div className="mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold mb-4">
            Rejoignez MEDDoC
          </h2>
          <p className="text-white/90 text-lg leading-relaxed">
            Créez votre compte patient et accédez à tous nos services de santé
            digitaux.
          </p>
        </div>

        {/* Avantages */}
        <div className="space-y-6 flex-1">
          <motion.div
            className="flex items-center space-x-4"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
          >
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <User className="h-6 w-6" />
            </div>
            <div>
              <h3 className="font-semibold">Profil personnalisé</h3>
              <p className="text-white/80 text-sm">
                Gérez vos informations médicales
              </p>
            </div>
          </motion.div>

          <motion.div
            className="flex items-center space-x-4"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8 }}
          >
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <Heart className="h-6 w-6" />
            </div>
            <div>
              <h3 className="font-semibold">Suivi médical</h3>
              <p className="text-white/80 text-sm">
                Accès à vos rendez-vous et dossiers
              </p>
            </div>
          </motion.div>

          <motion.div
            className="flex items-center space-x-4"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.0 }}
          >
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <Phone className="h-6 w-6" />
            </div>
            <div>
              <h3 className="font-semibold">Support 24/7</h3>
              <p className="text-white/80 text-sm">
                Assistance médicale disponible
              </p>
            </div>
          </motion.div>
        </div>

        {/* Étape actuelle */}
        <div className="mt-8 pt-6 border-t border-white/20">
          <p className="text-white/80 text-sm">
            Étape {activeStep + 1} sur {PATIENT_STEPS.length}
          </p>
          <p className="text-white font-medium">{PATIENT_STEPS[activeStep]}</p>
        </div>
      </div>
    </div>
  );
};

export default RegisterPatientInformations;
