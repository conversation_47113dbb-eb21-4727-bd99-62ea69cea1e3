import Title from "./Title";
import FormField from "@/presentation/components/common/ui/FormField";
import { User, MapPin, Phone } from "lucide-react";
import { PatientFormData } from "@/shared/schemas/PatientShema";
import {
  Control,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";
import { Commune, District, Patient, Province, Region } from "@/domain/models";
import DateField from "@/presentation/components/common/ui/DateField";
import SelectDistrict from "@/presentation/components/locationSelector/SelectDistrict";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import SelectCommune from "@/presentation/components/locationSelector/SelectCommune";
import SelectRegion from "@/presentation/components/locationSelector/SelectRegion";
import { sexe_enum } from "@/domain/models/enums";
import SelectProvince from "@/presentation/components/locationSelector/SelectProvince.tsx";

interface RegisterPatientStep1Props {
  control: Control<PatientFormData>;
  patient?: Patient;
  errors: FieldErrors<PatientFormData>;
  onSubmit: () => Promise<void>;
  register: UseFormRegister<PatientFormData>;
  setValue: UseFormSetValue<PatientFormData>;
}

const RegisterStep1 = ({
  control,
  errors,
  onSubmit,
  register,
  setValue,
  patient,
}: RegisterPatientStep1Props) => {
  const {
    selectedProvince,
    handleProvinceChange,
    selectedRegion,
    handleRegionChange,
    selectedDistrict,
    handleDistrictChange,
    selectedCommune,
    handleCommuneChange,
  } = useLocationSelector();

  return (
    <div className="grid">
      <div className="flex justify-center">
        <Title
          title="Informations personnelles"
          subTitle="Renseignez les informations personnelles pour créer le compte"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <FormField
          id="nom"
          label="Nom"
          placeholder="Entrez le nom du patient"
          icon={User}
          register={register}
          required
          error={errors.nom}
          validation={{
            required: "Le nom du patient est requis",
            minLength: {
              value: 2,
              message: "Le nom doit contenir au moins 2 caractères",
            },
            maxLength: {
              value: 50,
              message: "Le nom ne peut pas dépasser 50 caractères",
            },
            pattern: {
              value: /^[a-zA-ZÀ-ÿ\s'-]+$/,
              message:
                "Le nom ne peut contenir que des lettres, espaces, apostrophes et tirets",
            },
          }}
        />
        <FormField
          id="prenom"
          label="Prénom"
          placeholder="Entrez le prénom du patient"
          icon={User}
          register={register}
          error={errors.prenom}
          validation={{
            required: "Le prénom du patient est requis",
            minLength: {
              value: 2,
              message: "Le prénom doit contenir au moins 2 caractères",
            },
            maxLength: {
              value: 50,
              message: "Le prénom ne peut pas dépasser 50 caractères",
            },
            pattern: {
              value: /^[a-zA-ZÀ-ÿ\s'-]+$/,
              message:
                "Le prénom ne peut contenir que des lettres, espaces, apostrophes et tirets",
            },
          }}
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
        <FormField
          id="sexe"
          label="Sexe"
          type="select"
          placeholder="Entrez votre sexe"
          icon={User}
          register={register}
          required
          options={[
            { value: sexe_enum.homme, label: "Homme" },
            { value: sexe_enum.femme, label: "Femme" },
          ]}
          error={errors.sexe}
          validation={{
            required: "Le sexe est requis",
          }}
        />
        <DateField
          id="date_naissance"
          label="Date de naissance"
          icon={User}
          control={control}
          error={errors.date_naissance}
          maxDate={new Date()}
          required
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
        <FormField
          id="telephone"
          label="Téléphone"
          type="tel"
          placeholder="Ex: 032XXXXXXX ou +261XXXXXXXXX"
          icon={Phone}
          register={register}
          required
          error={errors.telephone}
          helpText="Format accepté: 032XXXXXXX ou +261XXXXXXXXX (opérateurs: 32, 33, 34, 39)"
          validation={{
            required: "Le numéro de téléphone est requis",
            pattern: {
              value: /^(0(32|33|34|39)\d{7}|\+261(32|33|34|39)\d{7})$/,
              message:
                "Format de téléphone invalide. Utilisez: 032XXXXXXX ou +261XXXXXXXXX",
            },
          }}
        />
        <FormField
          id="adresse"
          label="Adresse"
          placeholder="Entrez l'adresse du patient"
          icon={MapPin}
          register={register}
          required
          error={errors.adresse}
          validation={{
            required: "L'adresse est requise",
            minLength: {
              value: 5,
              message: "L'adresse doit contenir au moins 5 caractères",
            },
            maxLength: {
              value: 200,
              message: "L'adresse ne peut pas dépasser 200 caractères",
            },
          }}
        />
        
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
        
        <SelectProvince
          register={register}
          errors={errors}
          value={selectedProvince}
          onChange={(province: Province) => {
            handleProvinceChange(province);
          }}
          province_id={patient?.province}
        />
         <SelectRegion
          register={register}
          errors={errors}
          value={selectedRegion}
          onChange={(region: Region) => {
            handleRegionChange(region);
          }}
          isDisabled={!selectedProvince}
          region_id={patient?.region}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
       
        <SelectDistrict
          register={register}
          errors={errors}
          value={selectedDistrict}
          onChange={(district: District) => {
            handleDistrictChange(district);
          }}
          isDisabled={!selectedRegion}
          district_id={patient?.district}
        />
        <SelectCommune
          register={register}
          errors={errors}
          value={selectedCommune}
          onChange={(commune: Commune) => {
            handleCommuneChange(commune);
          }}
          isDisabled={!selectedDistrict}
          commune_id={patient?.commune}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
        
        <div></div>
      </div>
    </div>
  );
};

export default RegisterStep1;
