import {
  MenuItem,
  FormControl,
  FormHelperText,
  Grid,
  TextField,
} from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { sexe_enum } from "@/domain/models/enums";
import { motion } from "framer-motion";
import { Users } from "lucide-react";

const RegisterStep1Sexe = () => {
  const {
    errors,
    sexe,
    handleSexeChange,
  } = useRegisterPatientState()
  return (
    <Grid item xs={12} sm={6}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
      >
        <div className="mb-2">
          <div className="flex items-center mb-2">
            <Users size={18} className="text-meddoc-primary mr-2" />
            <span className="text-sm font-medium text-gray-700">Sexe*</span>
          </div>
        </div>
        <FormControl fullWidth error={!!errors.sexe}>
          <TextField
            value={sexe || ''}
            onChange={(e) => handleSexeChange(e.target.value as sexe_enum)}
            select
            fullWidth
            placeholder="Sélectionnez une option"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '12px',
                backgroundColor: '#f8fafc',
                '& fieldset': {
                  borderColor: '#e2e8f0',
                },
                '&:hover fieldset': {
                  borderColor: '#27aae1',
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#27aae1',
                  borderWidth: '2px',
                },
                '& .MuiSelect-select': {
                  padding: '14px 16px',
                },
              },
            }}
          >
            <MenuItem value={sexe_enum.homme}>Homme</MenuItem>
            <MenuItem value={sexe_enum.femme}>Femme</MenuItem>
          </TextField>
          <FormHelperText>{errors.sexe}</FormHelperText>
        </FormControl>
      </motion.div>
    </Grid>
  );
};

export default RegisterStep1Sexe;
