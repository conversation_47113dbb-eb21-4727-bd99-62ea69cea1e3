import {
  Grid,
  TextField,
} from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { motion } from "framer-motion";
import { Lock } from "lucide-react";

const RegisterStep1Password = () => {
  const {
    errors,
    password,
    handlePasswordChange,
  } = useRegisterPatientState()
  return (
    <Grid item xs={12} sm={6}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <div className="mb-2">
          <div className="flex items-center mb-2">
            <Lock size={18} className="text-meddoc-primary mr-2" />
            <span className="text-sm font-medium text-gray-700">Mot de passe*</span>
          </div>
        </div>
        <TextField
          placeholder="••••••••"
          value={password}
          onChange={(e) => handlePasswordChange(e.target.value)}
          type="password"
          error={!!errors.password}
          helperText={errors.password}
          fullWidth
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '12px',
              backgroundColor: '#f8fafc',
              '& fieldset': {
                borderColor: '#e2e8f0',
              },
              '&:hover fieldset': {
                borderColor: '#27aae1',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#27aae1',
                borderWidth: '2px',
              },
              '& input': {
                padding: '14px 16px',
              },
            },
          }}
        />
      </motion.div>
    </Grid>
  );
};

export default RegisterStep1Password;
