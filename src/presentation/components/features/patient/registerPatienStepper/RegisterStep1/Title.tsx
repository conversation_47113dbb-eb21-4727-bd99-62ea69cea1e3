import { Grid, Typography } from "@mui/material";
import { motion } from "framer-motion";

interface TitleProps {
  title: string;
  subTitle?: string;
}

const Title = ({ title, subTitle }: TitleProps) => {
  return (
    <Grid item xs={12}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <div className="h-0.5 w-8 bg-meddoc-primary rounded-full mr-3" />
            <Typography
              variant="h6"
              className="text-meddoc-primary font-semibold"
              sx={{ fontWeight: 600 }}
            >
              {title}
            </Typography>
          </div>
          <p className="text-gray-600 text-sm">{subTitle}</p>
        </div>
      </motion.div>
    </Grid>
  );
};

export default Title;
