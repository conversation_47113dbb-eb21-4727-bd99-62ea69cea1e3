import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { Grid } from "@mui/material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { fr } from 'date-fns/locale';
import { motion } from "framer-motion";
import { Calendar } from "lucide-react";

const RegisterStep1Birthday = () => {
  const {
    errors,
    date_naissance,
    handleDateNaissanceChange,
  } = useRegisterPatientState()
  return (
    <Grid item xs={12} sm={6}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.7 }}
      >
        <div className="mb-2">
          <div className="flex items-center mb-2">
            <Calendar size={18} className="text-meddoc-primary mr-2" />
            <span className="text-sm font-medium text-gray-700">Date de naissance</span>
          </div>
        </div>
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
          <DatePicker
            value={date_naissance}
            onChange={(newValue) => handleDateNaissanceChange(newValue)}
            slotProps={{
              textField: {
                fullWidth: true,
                placeholder: "DD/MM/YYYY",
                sx: {
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    backgroundColor: '#f8fafc',
                    '& fieldset': {
                      borderColor: '#e2e8f0',
                    },
                    '&:hover fieldset': {
                      borderColor: '#27aae1',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#27aae1',
                      borderWidth: '2px',
                    },
                    '& input': {
                      padding: '14px 16px',
                    },
                  },
                },
              },
            }}
          />
        </LocalizationProvider>
      </motion.div>
    </Grid>
  );
};

export default RegisterStep1Birthday;
