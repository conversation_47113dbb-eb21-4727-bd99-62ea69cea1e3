import { motion } from "framer-motion";
import RegisterStep1 from "./RegisterStep1/RegisterStep1";
import RegisterStep2 from "./RegisterStep3/RegisterStep3";
import { NavigationButtons } from "@/presentation/components/common/NavigationButtons/NavigationButtons";
import {
  Control,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";
import { PatientFormData } from "@/shared/schemas/PatientShema";
import { PATIENT_STEPS } from "@/shared/constants/PatientSteps";

/**
 * Props pour le composant RegisterPatientForm
 */
export interface RegisterPatientFormProps {
  /** Fonction de soumission du formulaire appelée lors de la validation finale */
  onSubmit: () => Promise<void>;
  /** Index de l'étape actuellement active (0-based) */
  activeStep: number;
  /** Contrôleur React Hook Form pour la gestion des champs */
  control: Control<PatientFormData>;
  /** Objet contenant les erreurs de validation pour chaque champ */
  errors: FieldErrors<PatientFormData>;
  /** Fonction d'enregistrement des champs React Hook Form */
  register: UseFormRegister<PatientFormData>;
  /** Fonction pour définir programmatiquement la valeur d'un champ */
  setValue: UseFormSetValue<PatientFormData>;
  /** Fonction appelée pour revenir à l'étape précédente */
  onBack: (count: number) => void;
  /** Fonction appelée pour passer à l'étape suivante */
  onNext: (count: number) => void;
  /** Indique si le formulaire est en cours de soumission (désactive les boutons) */
  isDisabled: boolean;
}

/**
 * Composant principal du formulaire d'inscription patient
 *
 * Ce composant gère l'affichage du formulaire multi-étapes d'inscription patient.
 * Il orchestre l'affichage conditionnel des différentes étapes (RegisterStep1 et RegisterStep2)
 * et intègre les boutons de navigation avec validation.
 *
 * Le composant utilise React Hook Form pour la gestion des données et de la validation,
 * et Framer Motion pour les animations d'entrée fluides.
 *
 * @component
 * @example
 * ```tsx
 * <RegisterPatientForm
 *   onSubmit={handleSubmit}
 *   activeStep={0}
 *   control={control}
 *   errors={errors}
 *   register={register}
 *   setValue={setValue}
 *   onBack={handleBack}
 *   onNext={handleNext}
 *   isDisabled={isLoading}
 * />
 * ```
 *
 * @param {RegisterPatientFormProps} props - Les propriétés du composant
 *
 * @returns {JSX.Element} Formulaire multi-étapes avec navigation et validation
 *
 * @features
 * - Affichage conditionnel des étapes basé sur activeStep
 * - Intégration complète avec React Hook Form
 * - Boutons de navigation avec validation automatique
 * - Animations d'entrée avec Framer Motion
 * - Layout responsive avec Flexbox
 *
 * @dependencies
 * - RegisterStep1: Première étape (informations personnelles)
 * - RegisterStep2: Deuxième étape (identifiants et contact)
 * - NavigationButtons: Composant de navigation entre étapes
 * - React Hook Form: Gestion des formulaires
 * - Framer Motion: Animations
 *
 */
const RegisterPatientForm = ({
  onSubmit,
  activeStep,
  control,
  errors,
  register,
  setValue,
  onBack,
  onNext,
  isDisabled,
}: RegisterPatientFormProps): JSX.Element => {
  return (
    <div className="lg:w-3/5 p-8 lg:p-12">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <form className="h-full flex flex-col" onSubmit={onSubmit}>
          <div className="flex-1">
            {activeStep === 0 ? (
              <RegisterStep1
                control={control}
                errors={errors}
                onSubmit={onSubmit}
                register={register}
                setValue={setValue}
                patient={null}
              />
            ) : (
              <RegisterStep2
                control={control}
                errors={errors}
                onSubmit={onSubmit}
                register={register}
                setValue={setValue}
                patient={null}
              />
            )}
          </div>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <NavigationButtons
              activeStep={activeStep}
              onBack={onBack}
              onNext={onNext}
              onSubmit={onSubmit}
              isDisabled={isDisabled}
              totalSteps={PATIENT_STEPS.length}
            />
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default RegisterPatientForm;
