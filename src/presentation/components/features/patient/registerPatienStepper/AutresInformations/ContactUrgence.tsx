import {
  <PERSON><PERSON><PERSON>,
  IconButton,
  Button,
  <PERSON>po<PERSON>,
  Divider,
} from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { PRIMARY } from "@/shared/constants/Color";
import { PlusIcon, TrashIcon } from "lucide-react";

const ContactUrgence = () => {
  const {
    errors,
    urgences,
    handleContactUrgenceChange,
    handleAddContactUrgence,
    handleRemoveContactUrgence,
  } = useRegisterPatientState();

  return (
    <div>
      <Typography variant="h6" gutterBottom>
        Contact d'urgence
      </Typography>
      <div className="space-y-5">
        {urgences.map((field, index) => (
          <>
            <div className="flex items-center gap-2" key={index}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
                <TextField
                  value={field.contact_urgence_nom}
                  onChange={(e) =>
                    handleContactUrgenceChange(
                      index,
                      "contact_urgence_nom",
                      e.target.value
                    )
                  }
                  label={`Nom ${index + 1}*`}
                  type="text"
                  fullWidth
                />
                <TextField
                  value={field.contact_urgence_prenom}
                  onChange={(e) =>
                    handleContactUrgenceChange(
                      index,
                      "contact_urgence_prenom",
                      e.target.value
                    )
                  }
                  label={`Prénom ${index + 1}*`}
                  type="text"
                  fullWidth
                />
                <TextField
                  value={field.contact_urgence_telephone}
                  onChange={(e) =>
                    handleContactUrgenceChange(
                      index,
                      "contact_urgence_telephone",
                      e.target.value
                    )
                  }
                  label={`Téléphone ${index + 1}*`}
                  type="tel"
                  fullWidth
                  // error={!!errors.telephones?.[index]?.contact_urgence_telephone}
                  // helperText={errors.telephones?.[index]?.contact_urgence_telephone}
                />
              </div>
              <IconButton
                onClick={() => handleRemoveContactUrgence(index)}
                color="error"
                disabled={urgences.length <= 1}
              >
                <TrashIcon />
              </IconButton>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <TextField
                value={field.relation}
                onChange={(e) =>
                  handleContactUrgenceChange(index, "relation", e.target.value)
                }
                label={`Relation ${index + 1}*`}
                type="text"
                fullWidth
              />
              <TextField
                value={field.adresse}
                onChange={(e) =>
                  handleContactUrgenceChange(index, "adresse", e.target.value)
                }
                label={`Adresse ${index + 1}*`}
                type="text"
                fullWidth
              />
            </div>
            <Divider />
          </>
        ))}
        <Button
          variant="contained"
          onClick={handleAddContactUrgence}
          color="primary"
          sx={{
            mt: 1,
            textTransform: "none",
            backgroundColor: PRIMARY,
            borderRadius: 5,
          }}
          startIcon={<PlusIcon size={16} />}
        >
          Ajouter un numéro
        </Button>
      </div>
    </div>
  );
};

export default ContactUrgence;
