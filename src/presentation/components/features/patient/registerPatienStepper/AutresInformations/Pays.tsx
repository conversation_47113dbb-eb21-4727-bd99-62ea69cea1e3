import {
  Grid,
  TextField,
} from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const Pays = () => {
  const {
    errors,
    pays,
    handlePaysChange,
  } = useRegisterPatientState()
  return (
    <Grid item xs={12} sm={6}>
      <TextField
        label="Pays*"
        value={pays}
        onChange={(e) => handlePaysChange(e.target.value)}
        error={!!errors.pays}
        helperText={errors?.pays}
        fullWidth
      />
    </Grid>
  );
};

export default Pays;
