import {
  MenuItem,
  FormControl,
  FormHelperText,
  Grid,
  TextField,
} from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { patients_groupe_sanguin_enum, sexe_enum } from "@/domain/models/enums";

const GroupeSanguin = () => {
  const { errors, groupe_sanguin, handleGroupeSanguinChange } =
    useRegisterPatientState();
  return (
    <Grid item xs={12} sm={6}>
      <FormControl fullWidth error={!!errors.groupeSanguin}>
        <TextField
          value={groupe_sanguin || ""}
          onChange={(e) =>
            handleGroupeSanguinChange(
              e.target.value as unknown as patients_groupe_sanguin_enum
            )
          }
          select
          label="Groupe sanguin"
        >
          <MenuItem value={patients_groupe_sanguin_enum["A-"]}>A-</MenuItem>
          <MenuItem value={patients_groupe_sanguin_enum["AB+"]}>AB+</MenuItem>
          <MenuItem value={patients_groupe_sanguin_enum["AB-"]}>AB-</MenuItem>
          <MenuItem value={patients_groupe_sanguin_enum["A+"]}>A+</MenuItem>
          <MenuItem value={patients_groupe_sanguin_enum["B+"]}>B+</MenuItem>
          <MenuItem value={patients_groupe_sanguin_enum["B-"]}>B-</MenuItem>
          <MenuItem value={patients_groupe_sanguin_enum["O+"]}>O+</MenuItem>
          <MenuItem value={patients_groupe_sanguin_enum["O-"]}>O-</MenuItem>
        </TextField>
        <FormHelperText>{errors.groupeSanguin}</FormHelperText>
      </FormControl>
    </Grid>
  );
};

export default GroupeSanguin;
