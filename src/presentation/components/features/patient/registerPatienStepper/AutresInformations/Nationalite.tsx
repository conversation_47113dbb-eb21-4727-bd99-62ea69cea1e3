import {
  Grid,
  TextField,
} from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const Nationalite = () => {
  const {
    errors,
    nationalite,
    handleNationaliteChange,
  } = useRegisterPatientState()
  return (
    <Grid item xs={12} sm={6}>
      <TextField
        label="Nationalite*"
        value={nationalite}
        onChange={(e) => handleNationaliteChange(e.target.value)}
        error={!!errors.nationalite}
        helperText={errors?.nationalite}
        fullWidth
      />
    </Grid>
  );
};

export default Nationalite;
