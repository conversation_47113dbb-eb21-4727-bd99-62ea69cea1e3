import {
  Grid,
  TextField,
} from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const NumeroTelephone = () => {
  const {
    errors,
    telephone,
    handleNumeroTelephoneChange,
  } = useRegisterPatientState()
  return (
    <Grid item xs={12} sm={6}>
      <TextField
        label="Numero téléphone*"
        value={telephone}
        onChange={(e) => handleNumeroTelephoneChange(e.target.value)}
        error={!!errors.telephone}
        helperText={errors?.telephone}
        fullWidth
      />
    </Grid>
  );
};

export default NumeroTelephone;
