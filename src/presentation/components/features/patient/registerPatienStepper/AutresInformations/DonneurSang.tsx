import {
  FormControl,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const DonneurSang = () => {
  const {
    errors,
    donneur_sang,
    handleDonneurSangChange,
  } = useRegisterPatientState()
  return (
    <Grid item xs={12} sm={6} sx={{ display: "flex", gap: 2, alignItems: "center", my: 1 }}>
      <Typography variant="body2" color="textSecondary">
        Êtes-vous donneur du sang ?
      </Typography>
      <FormControl>
        <RadioGroup onChange={(e) => handleDonneurSangChange(e.target.value)} row>
          <FormControlLabel
            value={"oui"}
            checked={donneur_sang === true}
            control={<Radio size="small" />}
            label={<Typography variant="body2">Oui</Typography>}
          />
          <FormControlLabel
            value={"non"}
            checked={donneur_sang === false}
            control={<Radio size="small" />}
            label={<Typography variant="body2">Non</Typography>}
          />
        </RadioGroup>
      </FormControl>
    </Grid>
  );
};

export default DonneurSang;
