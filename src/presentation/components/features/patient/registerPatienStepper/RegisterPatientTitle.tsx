import { motion } from "framer-motion";

/**
 * Composant d'en-tête pour le formulaire d'inscription patient
 *
 * Ce composant affiche le titre principal et la description de la page d'inscription
 * avec une animation d'entrée fluide. Il utilise Framer Motion pour créer un effet
 * de fondu et de glissement depuis le haut.
 *
 * @component
 * @example
 * ```tsx
 * <RegisterPatientTitle />
 * ```
 *
 * @returns {JSX.Element} Titre animé avec description pour l'inscription patient
 *
 */
const RegisterPatientTitle = (): JSX.Element => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="text-center mb-8"
    >
      <h1 className="text-4xl font-bold text-white mb-2">Inscription</h1>
      <p className="text-white/90 text-lg">Créez votre compte patient MEDDoC</p>
    </motion.div>
  );
};

export default RegisterPatientTitle;
