/**
 * Index des composants modulaires pour l'inscription patient
 *
 * Ce fichier centralise les exports de tous les sous-composants
 * utilisés dans le processus d'inscription patient, facilitant
 * les imports et la maintenance.
 *
 */

// Composants principaux de l'inscription patient
export { default as RegisterPatientTitle } from "./RegisterPatientTitle";
export { default as RegisterPatientCurrentStepIndicator } from "./RegisterPatientCurrentStepIndicator";
export { default as RegisterPatientInformations } from "./RegisterPatientInformations";
export { default as RegisterPatientForm } from "./RegisterPatientForm";

// Composants des étapes spécifiques
export { default as RegisterStep1 } from "./RegisterStep1/RegisterStep1";
export { default as RegisterStep2 } from "./RegisterStep3/RegisterStep3";

// Types et interfaces
export type { RegisterPatientFormProps } from "./RegisterPatientForm";
