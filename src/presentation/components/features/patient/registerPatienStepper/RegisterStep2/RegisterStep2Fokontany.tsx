import { Grid, TextField } from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const RegisterStep2Fokontany = () => {
  const {
    errors,
    fokontany,
    handleFokontanyChange,
  } = useRegisterPatientState()

  return (
    <Grid item xs={12}>
      <TextField
        value={fokontany}
        onChange={(e) => handleFokontanyChange(e.target.value)}
        label="Fokontany*"
        error={!!errors.fokontany}
        helperText={errors.fokontany}
        fullWidth
      />
    </Grid>
  );
};

export default RegisterStep2Fokontany;
