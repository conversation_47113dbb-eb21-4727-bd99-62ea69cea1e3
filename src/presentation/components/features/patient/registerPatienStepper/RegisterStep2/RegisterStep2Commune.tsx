import { Grid, Typography } from "@mui/material";
import SelectCommune from "@/presentation/components/locationSelector/SelectCommune";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import { Commune } from "@/domain/models";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const RegisterStep2Commune = () => {
  const {
    selectedDistrict,
    selectedCommune,
    handleCommuneChange,
  } = useLocationSelector();

  const {
    errors,
  } = useRegisterPatientState()

  return (
    <Grid item xs={12}>
      <SelectCommune
        value={selectedCommune}
        onChange={(commune: Commune) => {
          handleCommuneChange(commune);
        }}
        isDisabled={!selectedDistrict}
      />
      {errors.commune && (
        <Typography color="error" variant="caption">
          {errors.commune}
        </Typography>
      )}
    </Grid>
  );
};

export default RegisterStep2Commune;
