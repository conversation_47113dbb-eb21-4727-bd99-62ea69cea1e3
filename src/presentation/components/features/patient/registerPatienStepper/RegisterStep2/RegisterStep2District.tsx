import React from "react";
import { Grid, Typography } from "@mui/material";
import { Controller } from "react-hook-form";
import { RegisterStepProps } from "@/shared/types/RegisterStepProps";
import SelectDistrict from "@/presentation/components/locationSelector/SelectDistrict";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import { District } from "@/domain/models";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const RegisterStep2District = () => {
  const {
    selectedDistrict,
    handleDistrictChange,
  } = useLocationSelector();

  const {
    errors,
  } = useRegisterPatientState()

  return (
    <Grid item xs={12}>
      <SelectDistrict
        value={selectedDistrict}
        onChange={(district: District) => {
          handleDistrictChange(district);
        }}
        allNeeded
      />
      {errors.district && (
        <Typography color="error" variant="caption">
          {errors.district}
        </Typography>
      )}
    </Grid>
  );
};

export default RegisterStep2District;
