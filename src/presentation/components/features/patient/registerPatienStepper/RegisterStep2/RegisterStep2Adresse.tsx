import { Grid, TextField } from "@mui/material";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const RegisterStep2Adresse = () => {
  const {
    errors,
    adresse,
    handleAdresseChange
  } = useRegisterPatientState()
  return (
    <Grid item xs={12}>
      <TextField
        value={adresse}
        onChange={(e) => handleAdresseChange(e.target.value)}
        label="Adresse*"
        error={!!errors.adresse}
        helperText={errors.adresse}
        fullWidth
      />
    </Grid>
  );
};

export default RegisterStep2Adresse;
