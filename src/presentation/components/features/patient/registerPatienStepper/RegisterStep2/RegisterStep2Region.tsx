import React, { useEffect } from "react";
import { Grid, Typography } from "@mui/material";
import { Controller } from "react-hook-form";
import { RegisterStepProps } from "@/shared/types/RegisterStepProps";
import SelectRegion from "@/presentation/components/locationSelector/SelectRegion";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import { Region } from "@/domain/models";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const RegisterStep2Region = () => {
  const {
    selectedRegion,
    handleRegionChange,
  } = useLocationSelector();

  const {
    errors,
  } = useRegisterPatientState()

  return (
    <Grid item xs={12}>
      <SelectRegion
        value={selectedRegion}
        onChange={(region: Region) => {
          handleRegionChange(region);
        }}
        allNeeded
      />
      {errors.district && (
        <Typography color="error" variant="caption">
          {errors.district}
        </Typography>
      )}
    </Grid>
  );
};

export default RegisterStep2Region;
