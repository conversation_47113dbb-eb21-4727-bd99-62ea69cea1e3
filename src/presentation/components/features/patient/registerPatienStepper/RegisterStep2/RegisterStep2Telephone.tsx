import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import { Grid, TextField } from "@mui/material";

const RegisterStep2Telephone = () => {
  const {
    errors,
  } = useRegisterPatientState()
  return (
    <Grid item xs={12}>
      <TextField
        label="Telephone*"
        type="tel"
        error={!!errors.telephone}
        helperText={errors.telephone}
        fullWidth
      />
    </Grid>
  );
};

export default RegisterStep2Telephone;
