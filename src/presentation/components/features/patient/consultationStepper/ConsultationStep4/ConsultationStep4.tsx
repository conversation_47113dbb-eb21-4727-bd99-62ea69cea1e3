import {
  Control,
  FieldErrors,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import { motion } from "framer-motion";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import ConsultationStep4Confirm from "./ConsultationStep4Confirm";
import ConsultationStep4Self from "./ConsultationStep4Self";
import ConsultationStep4Reason from "./ConsultationStep4Reason";
import ConsultationStep4ForOther from "./ConsultationStep4ForOther";
import ConsultationStep4ForWhom from "./ConsultationStep4ForWhom";
import { RendezVous } from "@/domain/models";

interface ConsultationStep4Props {
  control: Control<ConsultationStepFormData>;
  errors: FieldErrors<ConsultationStepFormData>;
  isLoading: boolean;
  register: UseFormRegister<ConsultationStepFormData>;
  setValue: UseFormSetValue<ConsultationStepFormData>;
  onNext: (count: number) => void;
  onSubmit: () => Promise<RendezVous>;
  watch: UseFormWatch<ConsultationStepFormData>;
}

const ConsultationStep4 = ({
  errors,
  control,
  isLoading,
  setValue,
  onNext,
  onSubmit,
  watch,
  register,
}: ConsultationStep4Props) => {
  const forWhom = watch("forWhom");

  return (
    <div>
      {/* Contenu principal */}
      <div>
        <motion.div
          className="lg:col-span-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="space-y-6">
            {/* Étapes du formulaire */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <ConsultationStep4ForWhom
                control={control}
                errors={errors}
                setValue={setValue}
                forWhom={forWhom}
              />
            </motion.div>

            {forWhom === "other" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <ConsultationStep4ForOther
                  control={control}
                  errors={errors}
                  register={register}
                />
              </motion.div>
            )}

            {forWhom && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="space-y-6"
              >
                <ConsultationStep4Self control={control} register={register} />
                <ConsultationStep4Reason />
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <ConsultationStep4Confirm
                forWhom={forWhom}
                isLoading={isLoading}
                onNext={onNext}
                onSubmit={onSubmit}
              />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ConsultationStep4;
