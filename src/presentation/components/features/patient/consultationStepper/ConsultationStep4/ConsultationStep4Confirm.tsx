import { StyledButton } from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { RendezVous } from "@/domain/models";

interface ConsultationStep4ConfirmProps {
  forWhom: string;
  isLoading: boolean;
  onNext: (count: number) => void;
  onSubmit: () => Promise<RendezVous>;
}

const ConsultationStep4Confirm = ({
  forWhom,
  isLoading,
  onNext,
  onSubmit,
}: ConsultationStep4ConfirmProps) => {
  const { handleCreateAppointment } = useConsultationState();
  const handleSubmit = async () => {
    const result = await onSubmit();
    if (result) {
      onNext(1);
    }
  };

  return (
    <StyledButton
      variant="contained"
      className="w-full"
      disabled={!forWhom}
      onClick={handleSubmit}
      loading={isLoading}
    >
      confirmer rendez-vous
    </StyledButton>
  );
};

export default ConsultationStep4Confirm;
