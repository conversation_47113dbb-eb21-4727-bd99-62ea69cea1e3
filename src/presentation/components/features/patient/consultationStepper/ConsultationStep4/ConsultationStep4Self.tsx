import { Typography } from "@mui/material";
import { FormSection } from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { sexe_enum } from "@/domain/models/enums";
import { Control, UseFormRegister } from "react-hook-form";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import DateField from "@/presentation/components/common/ui/DateField";
import { User } from "lucide-react";
import FormField from "@/presentation/components/common/ui/FormField";

interface ConsultationStep4SelfProps {
  control: Control<ConsultationStepFormData>;
  register: UseFormRegister<ConsultationStepFormData>;
}

const ConsultationStep4Self = ({
  control,
  register,
}: ConsultationStep4SelfProps) => {
  const { userInformations } = useConsultationState();

  if (!userInformations) {
    // toast.error("hello");
    return;
  }

  return (
    <FormSection>
      <Typography
        variant="h6"
        className="text-meddoc-primary font-semibold"
        sx={{ fontWeight: 600 }}
      >
        À propos de vous
      </Typography>

      <div className="grid grid-cols-1 gap-3 mt-4">
        <FormField
          id="nom"
          label="Nom de famille*"
          placeholder="Entrez le nom du patient"
          icon={User}
          register={register}
          required
          value={userInformations.nom}
          disabled={true}
        />
        <FormField
          id="prenom"
          label="Prénom"
          placeholder="Entrez le prénom du patient"
          icon={User}
          value={userInformations.prenom}
          register={register}
          disabled={true}
        />
        <FormField
          id="sexe"
          label="Sexe"
          type="select"
          placeholder="Entrez votre sexe"
          icon={User}
          value={userInformations.sexe}
          register={register}
          required
          options={[
            { value: sexe_enum.homme, label: "Homme" },
            { value: sexe_enum.femme, label: "Femme" },
          ]}
          disabled={true}
        />
        <DateField
          id="date_naissance"
          label="Date de naissance"
          icon={User}
          control={control}
          value={userInformations.date_naissance}
          maxDate={new Date()}
          required
          disabled={true}
        />
      </div>
    </FormSection>
  );
};

export default ConsultationStep4Self;
