import { Typography } from "@mui/material";
import { FormSection } from "@/styles/styleMui/ConsultationSteper";
import ProcheForm from "@/presentation/pages/patient/families/ProcheForm";
import { sexe_enum } from "@/domain/models/enums";
import DateField from "@/presentation/components/common/ui/DateField";
import { User } from "lucide-react";
import Form<PERSON>ield from "@/presentation/components/common/ui/FormField";
import { Control, FieldErrors, UseFormRegister } from "react-hook-form";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";

interface ConsultationStep4ForOtherProps {
  errors: FieldErrors<ConsultationStepFormData>;
  control: Control<ConsultationStepFormData>;
  register: UseFormRegister<ConsultationStepFormData>;
}

const ConsultationStep4ForOther = ({
  control,
  errors,
  register,
}: ConsultationStep4ForOtherProps) => {
  return (
    <FormSection>
      <Typography
        variant="h6"
        className="text-meddoc-primary font-semibold"
        sx={{ fontWeight: 600 }}
      >
        Informations sur le patient
      </Typography>
      <div className="grid grid-cols-1 gap-3 mt-4">
        <FormField
          id="procheInfo.nom"
          label="Nom de famille*"
          placeholder="Entrez le nom du patient"
          icon={User}
          error={errors.procheInfo?.nom}
          register={register}
          required
        />
        <FormField
          id="procheInfo.prenom"
          label="Prénom"
          placeholder="Entrez le prénom du patient"
          icon={User}
          error={errors.procheInfo?.prenom}
          register={register}
        />
        <FormField
          id="procheInfo.sexe"
          label="Sexe"
          type="select"
          placeholder="Entrez votre sexe"
          icon={User}
          error={errors.procheInfo?.sexe}
          register={register}
          required
          options={[
            { value: sexe_enum.homme, label: "Homme" },
            { value: sexe_enum.femme, label: "Femme" },
          ]}
        />
        <FormField
          id="procheInfo.lien_parente"
          label="Lien parente"
          placeholder="Entrez votre lien le patient"
          icon={User}
          error={errors.procheInfo?.lien_parente}
          register={register}
        />
        <DateField
          id="procheInfo.date_naissance"
          label="Date de naissance"
          icon={User}
          control={control}
          error={errors.procheInfo?.date_naissance}
          required
        />
      </div>
    </FormSection>
  );
};

export default ConsultationStep4ForOther;
