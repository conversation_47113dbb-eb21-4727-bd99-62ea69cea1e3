import {
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  Paper,
} from "@mui/material";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { FormSection } from "@/styles/styleMui/ConsultationSteper";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import {
  Control,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";

interface ConsultationStep4ForWhomProps {
  control: Control<ConsultationStepFormData>;
  errors: FieldErrors<ConsultationStepFormData>;
  setValue: UseFormSetValue<ConsultationStepFormData>;
  forWhom: string;
}

const ConsultationStep4ForWhom = ({
  control,
  errors,
  setValue,
  forWhom,
}: ConsultationStep4ForWhomProps) => {
  return (
    <FormSection>
      <Typography
        variant="h6"
        className="text-meddoc-primary font-semibold"
        sx={{ fontWeight: 600 }}
      >
        Pour qui prenez-vous rendez-vous?
      </Typography>

      <RadioGroup
        value={forWhom}
        onChange={(e) => setValue("forWhom", e.target.value)}
      >
        <FormControlLabel value="self" control={<Radio />} label="Vous-même" />
        <FormControlLabel
          value="other"
          control={<Radio />}
          label="Un proche (enfant, parent, etc.)"
        />
      </RadioGroup>
    </FormSection>
  );
};

export default ConsultationStep4ForWhom;
