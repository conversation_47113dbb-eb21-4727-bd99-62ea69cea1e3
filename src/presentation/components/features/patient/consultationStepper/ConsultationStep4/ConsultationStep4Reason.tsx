import { Typography, TextField, Paper } from "@mui/material";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { FormSection } from "@/styles/styleMui/ConsultationSteper";
import Title from "../../registerPatienStepper/RegisterStep1/Title";

const ConsultationStep4Reason = () => {
  const { consultationReason, handleConsultationReasonChange } =
    useConsultationState();

  return (
    <FormSection>
      <Typography
        variant="h6"
        className="text-meddoc-primary font-semibold"
        sx={{ fontWeight: 600 }}
      >
        Raison de la visite / Renseignements complémentaires
      </Typography>
      <TextField
        fullWidth
        multiline
        rows={2}
        margin="normal"
        placeholder="Transmettre un message au personnel soignant (optionnel)"
        value={consultationReason}
        onChange={(e) => handleConsultationReasonChange(e.target.value)}
      />
    </FormSection>
  );
};

export default ConsultationStep4Reason;
