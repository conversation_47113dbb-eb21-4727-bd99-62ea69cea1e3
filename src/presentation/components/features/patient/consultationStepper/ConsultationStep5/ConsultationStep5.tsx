import { useNavigate } from "react-router-dom";
import { Typography } from "@mui/material";
import { motion } from "framer-motion";
import {
  Calendar,
  List,
  CheckCircle2,
  Mail,
  MessageSquare,
} from "lucide-react";
import {
  ActionButton,
  ConfirmationBox,
} from "@/styles/styleMui/ConsultationSteper";
import { useLocation } from "react-router-dom";
import { PatientRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useEffect } from "react";
import { removeDataLocalStorage } from "@/shared/utils/removeDataLocalStorage";

const ConsultationStep5 = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleViewAppointments = () => {
    navigate(`/${PatientRoutesNavigation.APPOINTMENTS}`);
  };

  const handleNewAppointment = () => {
    // navigate('/rdv/1')
  };

  useEffect(() => {
    removeDataLocalStorage("selectedTimeSlot");
  }, []);

  return (
    <div>
      {/* Header de succès */}
      <div className="relative">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          className="relative z-10 text-center text-white"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center justify-center w-10 h-10 bg-white/20 rounded-full mb-2"
          >
            <CheckCircle2 size={24} className="text-white" />
          </motion.div>
          <h1 className="text-lg md:text-xl text-meddoc-fonce font-bold mb-4">
            Rendez-vous confirmé !
          </h1>
          <p className="text-md md:text-lg text-meddoc-fonce mb-2">
            Votre consultation est programmée
          </p>
        </motion.div>
      </div>

      <div className="my-4">
        {/* Message de confirmation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <ConfirmationBox>
            <div className="text-center space-y-4">
              <div className="flex justify-center space-x-8 mb-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="flex items-center space-x-3 text-green-600"
                >
                  <div className="p-2 bg-green-100 rounded-full">
                    <Mail size={20} />
                  </div>
                  <span className="font-medium">Email envoyé</span>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="flex items-center space-x-3 text-blue-600"
                >
                  <div className="p-2 bg-blue-100 rounded-full">
                    <MessageSquare size={20} />
                  </div>
                  <span className="font-medium">SMS programmé</span>
                </motion.div>
              </div>

              <Typography
                variant="body1"
                sx={{ color: "#6b7280", fontSize: "1.1rem" }}
              >
                Nous venons de vous envoyer un email de confirmation de
                rendez-vous
              </Typography>
              <Typography
                variant="body1"
                sx={{ color: "#6b7280", fontSize: "1.1rem" }}
              >
                Vous recevrez également un SMS de rappel 24h avant votre
                consultation!
              </Typography>
            </div>
          </ConfirmationBox>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.3 }}
          className="flex flex-col sm:flex-row gap-4"
        >
          <ActionButton
            variant="contained"
            onClick={handleViewAppointments}
            sx={{
              flex: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: 2,
            }}
          >
            <List size={24} />
            VOIR MES RENDEZ-VOUS
          </ActionButton>
          <ActionButton
            variant="outlined"
            onClick={handleNewAppointment}
            sx={{
              flex: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: 2,
            }}
          >
            <Calendar size={24} />
            NOUVEAU RENDEZ-VOUS
          </ActionButton>
        </motion.div>
      </div>
    </div>
  );
};

export default ConsultationStep5;
