import { WarningBox2 } from "@/styles/styleMui/ConsultationSteper";
import { Typography } from "@mui/material";
import { motion } from "framer-motion";
import { AlertTriangle, Clock } from "lucide-react";

const WarningSumUp = () => {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.4 }}
      >
        <div className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl border-l-4 border-amber-400 shadow-sm">
          <div className="flex items-start gap-3">
            <div className="p-1.5 bg-amber-100 rounded-lg">
              <AlertTriangle size={16} className="text-amber-600" />
            </div>
            <div className="flex-1">
              <Typography
                variant="body2"
                sx={{
                  fontWeight: 600,
                  color: '#d97706',
                  mb: 0.5,
                  fontSize: '0.875rem'
                }}
              >
                Confirmation en attente
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#92400e',
                  fontSize: '0.8rem',
                  lineHeight: 1.4
                }}
              >
                Votre RDV n'est pas encore confirmé. Complétez les étapes pour finaliser.
              </Typography>
            </div>
            <div className="p-1 bg-amber-100 rounded-full">
              <Clock size={12} className="text-amber-600" />
            </div>
          </div>
        </div>
      </motion.div>
    );
};

export default WarningSumUp;