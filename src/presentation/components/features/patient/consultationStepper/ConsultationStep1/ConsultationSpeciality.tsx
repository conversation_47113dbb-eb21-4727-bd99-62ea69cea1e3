import { Typography, Box, Select, MenuItem, InputLabel, FormControl } from "@mui/material";
import { motion } from "framer-motion";
import { Award, ChevronDown } from "lucide-react";
import { StyledPaper, SectionTitle } from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import useProfessionals from "@/presentation/hooks/use-professionals";

const ConsultationSpeciality = () => {
  const { selectedProfessionnal } = useProfessionals();
  const { speciality, handleSpecialityChange } = useConsultationState();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
    >
      <StyledPaper>
        {/* Titre avec icône */}
        <SectionTitle>
          <Award className="section-icon" />
          <Typography variant="h6">
            Spécialité médicale
          </Typography>
        </SectionTitle>

        <Typography
          variant="body1"
          sx={{
            color: '#6b7280',
            mb: 3,
            lineHeight: 1.6
          }}
        >
          Choisissez la spécialité pour laquelle vous souhaitez consulter
        </Typography>

        <FormControl fullWidth>
          <Select
            value={speciality}
            onChange={(e) => handleSpecialityChange(e.target.value)}
            displayEmpty
            IconComponent={ChevronDown}
            renderValue={
              speciality !== ""
                ? undefined
                : () => (
                    <span style={{ color: '#9ca3af', fontStyle: 'italic' }}>
                      Sélectionnez une spécialité
                    </span>
                  )
            }
            sx={{
              borderRadius: '12px',
              backgroundColor: '#f8fafc',
              border: '2px solid #e5e7eb',
              '&:hover': {
                borderColor: '#27aae1',
              },
              '&.Mui-focused': {
                borderColor: '#27aae1',
                boxShadow: '0 0 0 3px rgba(39, 170, 225, 0.1)',
              },
              '& .MuiSelect-select': {
                padding: '16px 20px',
                fontSize: '1rem',
                fontWeight: 500,
              },
              '& .MuiOutlinedInput-notchedOutline': {
                border: 'none',
              },
              '& .MuiSvgIcon-root': {
                color: '#27aae1',
                fontSize: '1.5rem',
              }
            }}
          >
            {selectedProfessionnal?.specialites_professionnel &&
              selectedProfessionnal?.specialites_professionnel.length &&
              selectedProfessionnal?.specialites_professionnel.map((spec, index) => (
                <MenuItem
                  value={spec.nom_specialite}
                  key={spec.id}
                  sx={{
                    padding: '12px 20px',
                    fontSize: '1rem',
                    '&:hover': {
                      backgroundColor: 'rgba(39, 170, 225, 0.05)',
                    },
                    '&.Mui-selected': {
                      backgroundColor: 'rgba(39, 170, 225, 0.1)',
                      color: '#27aae1',
                      fontWeight: 600,
                      '&:hover': {
                        backgroundColor: 'rgba(39, 170, 225, 0.15)',
                      }
                    }
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: '#27aae1',
                    }} />
                    {spec.nom_specialite}
                  </div>
                </MenuItem>
              ))}
          </Select>
        </FormControl>

        {/* Affichage de la spécialité sélectionnée */}
        {speciality && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: 'rgba(39, 170, 225, 0.05)',
              borderRadius: '8px',
              border: '1px solid rgba(39, 170, 225, 0.2)',
            }}
          >
            <Typography variant="body2" sx={{ color: '#27aae1', fontWeight: 500 }}>
              ✓ Spécialité sélectionnée : {speciality}
            </Typography>
          </motion.div>
        )}
      </StyledPaper>
    </motion.div>
  );
};

export default ConsultationSpeciality;
