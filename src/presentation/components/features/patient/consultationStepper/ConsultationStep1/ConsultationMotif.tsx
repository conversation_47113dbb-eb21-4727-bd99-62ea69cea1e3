import { Typo<PERSON>, <PERSON>, Select, <PERSON>uItem, FormControl } from "@mui/material";
import { motion } from "framer-motion";
import { FileText, ChevronDown, Clock, AlertTriangle, Calendar } from "lucide-react";
import { StyledPaper, SectionTitle } from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";

const ConsultationMotif = () => {
  const { categorie, consultationMotif, handleConsultationMotifChange } =
    useConsultationState();

  const getMotifIcon = (motif: string) => {
    switch (motif) {
      case "premiere-consultation":
        return <Calendar size={16} style={{ color: '#27aae1' }} />;
      case "suivi":
        return <Clock size={16} style={{ color: '#10b981' }} />;
      case "urgence":
        return <AlertTriangle size={16} style={{ color: '#ef4444' }} />;
      default:
        return <FileText size={16} style={{ color: '#6b7280' }} />;
    }
  };

  const getMotifColor = (motif: string) => {
    switch (motif) {
      case "premiere-consultation":
        return '#27aae1';
      case "suivi":
        return '#10b981';
      case "urgence":
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
    >
      <StyledPaper>
        {/* Titre avec icône */}
        <SectionTitle>
          <FileText className="section-icon" />
          <Typography variant="h6">
            Motif de consultation
          </Typography>
        </SectionTitle>

        <Typography
          variant="body1"
          sx={{
            color: '#6b7280',
            mb: 3,
            lineHeight: 1.6
          }}
        >
          Précisez le motif de votre consultation médicale
        </Typography>

        <FormControl fullWidth>
          <Select
            value={consultationMotif}
            onChange={(e) => handleConsultationMotifChange(e.target.value)}
            displayEmpty
            IconComponent={ChevronDown}
            renderValue={
              consultationMotif !== ""
                ? undefined
                : () => (
                    <span style={{ color: '#9ca3af', fontStyle: 'italic' }}>
                      Sélectionnez un motif de consultation
                    </span>
                  )
            }
            sx={{
              borderRadius: '12px',
              backgroundColor: '#f8fafc',
              border: '2px solid #e5e7eb',
              '&:hover': {
                borderColor: '#27aae1',
              },
              '&.Mui-focused': {
                borderColor: '#27aae1',
                boxShadow: '0 0 0 3px rgba(39, 170, 225, 0.1)',
              },
              '& .MuiSelect-select': {
                padding: '16px 20px',
                fontSize: '1rem',
                fontWeight: 500,
              },
              '& .MuiOutlinedInput-notchedOutline': {
                border: 'none',
              },
              '& .MuiSvgIcon-root': {
                color: '#27aae1',
                fontSize: '1.5rem',
              }
            }}
          >
            {categorie === "Première consultation" ? (
              <MenuItem
                value="premiere-consultation"
                sx={{
                  padding: '12px 20px',
                  fontSize: '1rem',
                  '&:hover': {
                    backgroundColor: 'rgba(39, 170, 225, 0.05)',
                  },
                  '&.Mui-selected': {
                    backgroundColor: 'rgba(39, 170, 225, 0.1)',
                    color: '#27aae1',
                    fontWeight: 600,
                  }
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Calendar size={18} style={{ color: '#27aae1' }} />
                  Première consultation
                </div>
              </MenuItem>
            ) : (
              <MenuItem
                value="suivi"
                sx={{
                  padding: '12px 20px',
                  fontSize: '1rem',
                  '&:hover': {
                    backgroundColor: 'rgba(16, 185, 129, 0.05)',
                  },
                  '&.Mui-selected': {
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    color: '#10b981',
                    fontWeight: 600,
                  }
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Clock size={18} style={{ color: '#10b981' }} />
                  Consultation de suivi
                </div>
              </MenuItem>
            )}
            <MenuItem
              value="urgence"
              sx={{
                padding: '12px 20px',
                fontSize: '1rem',
                '&:hover': {
                  backgroundColor: 'rgba(239, 68, 68, 0.05)',
                },
                '&.Mui-selected': {
                  backgroundColor: 'rgba(239, 68, 68, 0.1)',
                  color: '#ef4444',
                  fontWeight: 600,
                }
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <AlertTriangle size={18} style={{ color: '#ef4444' }} />
                Consultation d'urgence
              </div>
            </MenuItem>
          </Select>
        </FormControl>

        {/* Affichage du motif sélectionné */}
        {consultationMotif && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: `rgba(${getMotifColor(consultationMotif) === '#27aae1' ? '39, 170, 225' :
                                    getMotifColor(consultationMotif) === '#10b981' ? '16, 185, 129' :
                                    '239, 68, 68'}, 0.05)`,
              borderRadius: '8px',
              border: `1px solid rgba(${getMotifColor(consultationMotif) === '#27aae1' ? '39, 170, 225' :
                                        getMotifColor(consultationMotif) === '#10b981' ? '16, 185, 129' :
                                        '239, 68, 68'}, 0.2)`,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            {getMotifIcon(consultationMotif)}
            <Typography variant="body2" sx={{ color: getMotifColor(consultationMotif), fontWeight: 500 }}>
              ✓ Motif sélectionné : {
                consultationMotif === 'premiere-consultation' ? 'Première consultation' :
                consultationMotif === 'suivi' ? 'Consultation de suivi' :
                'Consultation d\'urgence'
              }
            </Typography>
          </motion.div>
        )}
      </StyledPaper>
    </motion.div>
  );
};

export default ConsultationMotif;
