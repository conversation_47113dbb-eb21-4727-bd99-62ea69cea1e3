import {
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
} from "@mui/material";
import { motion } from "framer-motion";
import { User<PERSON><PERSON>, User<PERSON><PERSON><PERSON>, Stethoscope } from "lucide-react";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { categorie_enum } from "@/domain/models/enums";
import useProfessionals from "@/presentation/hooks/use-professionals";
import { StyledPaper, ConsultationTypeButton, SectionTitle } from "@/styles/styleMui/ConsultationSteper";

const ConsultationCategorie = () => {
  const { categorie, handleCategorieChange } = useConsultationState();
  const { selectedProfessionnal } = useProfessionals();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <StyledPaper>
        {/* Titre avec icône */}
        <SectionTitle>
          <Stethoscope className="section-icon" />
          <Typography variant="h6">
            Type de consultation
          </Typography>
        </SectionTitle>

        <Typography
          variant="body1"
          sx={{
            color: '#6b7280',
            mb: 3,
            lineHeight: 1.6
          }}
        >
          Sélectionnez le type de consultation avec le Dr. {selectedProfessionnal?.nom}
        </Typography>

        <RadioGroup
          value={categorie}
          onChange={(e) => handleCategorieChange(e.target.value)}
          sx={{ gap: 2 }}
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <ConsultationTypeButton
              value={categorie_enum["premiere consultation"]}
              control={<Radio />}
              checked={categorie === categorie_enum["premiere consultation"]}
              label={
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  position: 'relative',
                  width: '100%'
                }}>
                  {/* Indicateur de sélection */}
                  {categorie === categorie_enum["premiere consultation"] && (
                    <div style={{
                      position: 'absolute',
                      right: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      width: '24px',
                      height: '24px',
                      backgroundColor: '#27aae1',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: '0 2px 8px rgba(39, 170, 225, 0.3)'
                    }}>
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3">
                        <polyline points="20,6 9,17 4,12"></polyline>
                      </svg>
                    </div>
                  )}

                  <div style={{
                    padding: '8px',
                    backgroundColor: categorie === categorie_enum["premiere consultation"]
                      ? 'rgba(39, 170, 225, 0.2)'
                      : 'rgba(39, 170, 225, 0.1)',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <UserPlus size={20} style={{
                      color: categorie === categorie_enum["premiere consultation"]
                        ? '#1e40af'
                        : '#27aae1'
                    }} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{
                      fontWeight: 600,
                      fontSize: '1rem',
                      marginBottom: '4px',
                      color: categorie === categorie_enum["premiere consultation"]
                        ? '#1e40af'
                        : '#374151'
                    }}>
                      Première consultation
                    </div>
                    <div style={{
                      fontSize: '0.875rem',
                      color: categorie === categorie_enum["premiere consultation"]
                        ? '#4b5563'
                        : '#6b7280'
                    }}>
                      C'est ma première consultation avec ce praticien
                    </div>
                  </div>
                </div>
              }
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <ConsultationTypeButton
              value={categorie_enum["consultation de suivi"]}
              control={<Radio />}
              checked={categorie === categorie_enum["consultation de suivi"]}
              label={
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  position: 'relative',
                  width: '100%'
                }}>
                  {/* Indicateur de sélection */}
                  {categorie === categorie_enum["consultation de suivi"] && (
                    <div style={{
                      position: 'absolute',
                      right: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      width: '24px',
                      height: '24px',
                      backgroundColor: '#10b981',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: '0 2px 8px rgba(16, 185, 129, 0.3)'
                    }}>
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3">
                        <polyline points="20,6 9,17 4,12"></polyline>
                      </svg>
                    </div>
                  )}

                  <div style={{
                    padding: '8px',
                    backgroundColor: categorie === categorie_enum["consultation de suivi"]
                      ? 'rgba(16, 185, 129, 0.2)'
                      : 'rgba(16, 185, 129, 0.1)',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <UserCheck size={20} style={{
                      color: categorie === categorie_enum["consultation de suivi"]
                        ? '#047857'
                        : '#10b981'
                    }} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{
                      fontWeight: 600,
                      fontSize: '1rem',
                      marginBottom: '4px',
                      color: categorie === categorie_enum["consultation de suivi"]
                        ? '#047857'
                        : '#374151'
                    }}>
                      Consultation de suivi
                    </div>
                    <div style={{
                      fontSize: '0.875rem',
                      color: categorie === categorie_enum["consultation de suivi"]
                        ? '#4b5563'
                        : '#6b7280'
                    }}>
                      Je suis déjà suivi(e) par ce praticien
                    </div>
                  </div>
                </div>
              }
            />
          </motion.div>
        </RadioGroup>
      </StyledPaper>
    </motion.div>
  );
};

export default ConsultationCategorie;
