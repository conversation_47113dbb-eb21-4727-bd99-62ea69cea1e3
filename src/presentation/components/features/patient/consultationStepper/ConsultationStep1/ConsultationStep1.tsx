import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import {
  Control,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";
import Title from "../../registerPatienStepper/RegisterStep1/Title";
import FormField from "@/presentation/components/common/ui/FormField";
import { Award, Stethoscope, FileText } from "lucide-react";
import useProfessionals from "@/presentation/hooks/use-professionals";
import { categorie_enum } from "@/domain/models/enums";
import { RendezVous } from "@/domain/models";
import { motion } from "framer-motion";
import { FormSection } from "@/styles/styleMui/ConsultationSteper";

interface ConsultationStep1Props {
  control: Control<ConsultationStepFormData>;
  errors: FieldErrors<ConsultationStepFormData>;
  onSubmit: () => Promise<RendezVous>;
  register: UseFormRegister<ConsultationStepFormData>;
  setValue: UseFormSetValue<ConsultationStepFormData>;
}

const ConsultationStep1 = ({
  control,
  errors,
  onSubmit,
  register,
  setValue,
}: ConsultationStep1Props) => {
  const { selectedProfessionnal } = useProfessionals();
  return (
    <div>
      <FormSection>
        <Title
          title="Type de consultation"
          subTitle={`Sélectionnez le type de consultation avec le ${selectedProfessionnal?.titre}. ${selectedProfessionnal?.nom}`}
        />
        <FormField
          id="categorie"
          label="type de consultation"
          type="select"
          placeholder="Sélectionnez le type de consultation"
          icon={Stethoscope}
          register={register}
          required
          options={[
            {
              value: categorie_enum["premiere consultation"],
              label: categorie_enum["premiere consultation"],
            },
            {
              value: categorie_enum["consultation de suivi"],
              label: categorie_enum["consultation de suivi"],
            },
          ]}
          error={errors.categorie}
          validation={{
            required: "Le type de consultation est requis",
          }}
        />
      </FormSection>

      <FormSection>
        <Title
          title="Spécialité médicale"
          subTitle="Choisissez la spécialité pour laquelle vous souhaitez consulter"
        />
        <FormField
          id="speciality"
          label="Spécialité médicale"
          type="select"
          placeholder="Sélectionnez un spécialité"
          icon={Award}
          register={register}
          required
          options={selectedProfessionnal?.specialites_professionnel.map(
            (spec) => {
              return {
                label: spec.nom_specialite,
                value: spec.nom_specialite,
              };
            }
          )}
          error={errors.speciality}
          validation={{
            required: "Le spécialité médicale est requis",
          }}
        />
      </FormSection>

      <FormSection>
        <Title
          title="Motif de consultation"
          subTitle="Précisez le motif de votre consultation médicale"
        />
        <FormField
          id="consultationMotif"
          label="Motif de consultation"
          type="select"
          placeholder="Sélectionnez un motif de consultation"
          icon={FileText}
          register={register}
          required
          options={[
            {
              label: "Première consultation",
              value: "Première consultation",
            },
            { label: "Consultation de suivi", value: "Consultation de suivi" },
            {
              label: "Consultation d'urgence",
              value: "Consultation d'urgence",
            },
          ]}
          error={errors.consultationMotif}
          validation={{
            required: "Le sexe est requis",
          }}
        />
      </FormSection>
    </div>
  );
};

export default ConsultationStep1;
