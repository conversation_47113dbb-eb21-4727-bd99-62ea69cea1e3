import { FC, memo } from "react";
import { Calendar, Clock, MapPin, User } from "lucide-react";
import { APPOINTMENT_STATUS_STYLES } from "@/presentation/constants/patient.constants";
import {
  CARD_STYLES,
  TEXT_STYLES,
  ICON_STYLES,
  BUTTON_STYLES,
} from "@/presentation/styles/common";
import { useFormatDate } from "@/presentation/hooks/useFormatDate";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

/**
 * Props for the NextAppointment component
 */
interface NextAppointmentProps {
  /** Appointment data object containing all appointment details */
  appointment: AppointmentPatientDTO;
  /** Optional callback function called when user cancels appointment */
  onCancel?: (id: number) => void;
}

/**
 * NextAppointment Component
 *
 * Displays detailed information about a patient's next upcoming appointment
 * including doctor details, date/time, location, and cancellation option.
 * Used in the patient dashboard to highlight the most important upcoming appointment.
 *
 * @component
 * @example
 * ```tsx
 * <NextAppointment
 *   appointment={appointmentData}
 *   onCancel={(id) => handleCancelAppointment(id)}
 * />
 * ```
 *
 * @param props - Component props
 * @param props.appointment - Complete appointment data object
 * @param props.onCancel - Optional callback for appointment cancellation
 *
 * @features
 * - Professional information display (title, name, specialty)
 * - Formatted date and time presentation
 * - Location/address information
 * - Status-based styling and indicators
 * - Conditional cancellation button for upcoming appointments
 *
 * @architecture
 * - Memoized component to prevent unnecessary re-renders
 * - Uses consistent styling from shared constants
 * - Integrates with date formatting hooks
 * - Responsive design with proper spacing
 *
 * @accessibility
 * - Semantic HTML structure with proper landmarks
 * - Clear visual hierarchy with icons and text
 * - Keyboard accessible cancellation button
 * - Screen reader friendly content structure
 *
 * @performance
 * - Memoized to avoid re-renders when parent updates
 * - Lightweight date formatting with custom hook
 * - Conditional rendering for optimal performance
 */
const NextAppointment: FC<NextAppointmentProps> = memo(
  ({ appointment, onCancel }) => {
    const formattedDate = useFormatDate(appointment.date_rendez_vous);

    return (
      <div
        className={`${CARD_STYLES.container} p-6 ${APPOINTMENT_STATUS_STYLES[appointment.statut]?.container}`}
      >
        <div className="flex flex-col space-y-4">
          <div className="flex justify-between items-start">
            <h3 className={CARD_STYLES.title}>Prochain rendez-vous</h3>
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                APPOINTMENT_STATUS_STYLES[appointment.statut]?.badge
              }`}
            >
              {appointment.statut}
            </span>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <User className={ICON_STYLES.base} />
              <div>
                <p className={`font-medium ${TEXT_STYLES.primary}`}>
                  {appointment.professional?.titre}{" "}
                  {appointment.professional?.nom}{" "}
                  {appointment.professional?.prenom}
                </p>
                {/* <p className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}>{appointment.specialty}</p> */}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              <span className="text-gray-900 dark:text-gray-100">
                {formattedDate}
              </span>
            </div>

            {/* <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              <span className="text-gray-900 dark:text-gray-100">{appointment.time} ({appointment.duration} min)</span>
            </div> */}

            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              <span className="text-gray-900 dark:text-gray-100">
                {appointment.professional.adresse}
              </span>
            </div>
          </div>

          {appointment.statut === rendez_vous_statut_enum.A_VENIR &&
            onCancel && (
              <button
                onClick={() => onCancel(appointment.id)}
                className={`mt-4 w-full ${BUTTON_STYLES.danger}`}
              >
                Annuler le rendez-vous
              </button>
            )}
        </div>
      </div>
    );
  }
);

export default NextAppointment;
