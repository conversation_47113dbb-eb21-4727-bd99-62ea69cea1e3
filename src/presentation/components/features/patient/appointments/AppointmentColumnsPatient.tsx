import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import {
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
} from "@mui/material";
import { statusColors } from "@/shared/constants/statusColors";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { EventBusy as EventBusyIcon } from "@mui/icons-material";
import { useState } from "react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";

// Composant pour le bouton d'annulation avec modale
const CancelButton = ({ appointment }: { appointment: AppointmentPatientDTO }) => {
  const { handleCancelAppointment } = useConsultationState();
  const [openCancelDialog, setOpenCancelDialog] = useState(false);

  const canCancel = appointment.statut === rendez_vous_statut_enum.A_VENIR ||
    appointment.statut === rendez_vous_statut_enum.REPORTER;

  const handleOpenCancelDialog = () => {
    setOpenCancelDialog(true);
  };

  const handleCloseCancelDialog = () => {
    setOpenCancelDialog(false);
  };

  const handleConfirmCancel = () => {
    handleCancelAppointment({
      id: appointment.id,
      time: appointment.time,
      date_rendez_vous: new Date(appointment.date_rendez_vous),
      statut: appointment.statut,
      id_professionnel: appointment.professional.id,
      motif: appointment.motif,
      raison: appointment.raison,
      categorie: "",
      patient: {
        id: appointment.patient_id,
        nom: "Patient",
        prenom: "",
        sexe: "HOMME" as any,
        date_naissance: new Date(),
        province: "",
        district: "",
        commune: "",
        region: "",
        situation_matrimonial: "",
        telephone: "",
        unique_id: `patient_${appointment.patient_id}`
      }
    });
    setOpenCancelDialog(false);
  };

  return (
    <>
      <Button
        onClick={handleOpenCancelDialog}
        variant="contained"
        color="error"
        size="small"
        sx={{ textTransform: "none", gap: 1 }}
        disabled={!canCancel}
      >
        <EventBusyIcon sx={{ width: 20, height: 20 }} />
        Annuler
      </Button>

      {/* Modale de confirmation d'annulation */}
      <Dialog
        open={openCancelDialog}
        onClose={handleCloseCancelDialog}
        aria-labelledby="cancel-dialog-title"
        aria-describedby="cancel-dialog-description"
      >
        <DialogTitle id="cancel-dialog-title" className="text-center">
          Confirmer l'annulation
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="cancel-dialog-description">
            Êtes-vous sûr de vouloir annuler ce rendez-vous avec{" "}
            <strong>
              Dr. {appointment.professional.nom} {appointment.professional.prenom}
            </strong>{" "}
            prévu le{" "}
            <strong>
              {format(new Date(appointment.date_rendez_vous), "dd MMMM yyyy", { locale: fr })}
            </strong>{" "}
            à{" "}
            <strong>{appointment.time}</strong> ?
            <br />
            <br />
            Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, display: "flex", justifyContent: "center" }}>
          <Button
            onClick={handleCloseCancelDialog}
            color="primary"
          >
            Non
          </Button>
          <Button
            onClick={handleConfirmCancel}
            color="error"
            variant="contained"
            autoFocus
          >
            Oui
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export const AppointmentColumnsPatient = (): GridColDef[] => {
  return [
    {
      field: "date_rendez_vous",
      headerName: "Date",
      width: 160,
      valueFormatter: (params) => new Date(params).toLocaleString(),
      headerClassName: "font-semibold",
    },
    {
      field: "Docteur",
      headerName: "docteur",
      width: 200,
      renderCell: (params: GridRenderCellParams<AppointmentPatientDTO>) =>
        `${params.row.professional.nom} ${params.row.professional.prenom}`,
      headerClassName: "font-semibold",
    },
    {
      field: "motif",
      headerName: "Motif",
      width: 250,
      headerClassName: "font-semibold",
    },
    {
      field: "statut",
      headerName: "Statut",
      width: 150,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={statusColors[params.value as keyof typeof statusColors]}
          size="small"
        />
      ),
      headerClassName: "font-semibold",
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 120,
      renderCell: (params: GridRenderCellParams<AppointmentPatientDTO>) => (
        <CancelButton appointment={params.row} />
      ),
      headerClassName: "font-semibold",
    },
  ];
};
