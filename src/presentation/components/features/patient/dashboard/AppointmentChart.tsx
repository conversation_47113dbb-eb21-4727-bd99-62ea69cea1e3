import { FC, memo } from 'react';
import { BarChart2 } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Bar,
  BarChart,
} from 'recharts';
import { useChartConfiguration } from '@/presentation/hooks/dashboard/patient/useChartConfiguration';
import { CHART_SECTIONS } from '@/presentation/constants/patient-dashboard.constants';

/**
 * Appointment chart data interface
 */
interface AppointmentChartData {
  /** Month or period name */
  name: string;
  /** Number of appointments in this period */
  appointments: number;
}

/**
 * Props for the AppointmentChart component
 */
interface AppointmentChartProps {
  /** Chart data array with appointment counts by period */
  data: AppointmentChartData[];
  /** Current dark mode state */
  isDarkMode: boolean;
  /** Chart title (optional, uses default if not provided) */
  title?: string;
  /** Chart subtitle (optional, uses default if not provided) */
  subtitle?: string;
  /** Additional CSS classes for customization */
  className?: string;
}

/**
 * AppointmentChart Component
 * 
 * Displays a bar chart showing the patient's appointment history
 * over the last 6 months. Provides visual insight into appointment
 * frequency and patterns over time.
 * 
 * @component
 * @example
 * ```tsx
 * <AppointmentChart
 *   data={appointmentData}
 *   isDarkMode={isDarkMode}
 *   title="Mon Historique"
 *   subtitle="3 derniers mois"
 * />
 * ```
 * 
 * @param props - Component props
 * @param props.data - Array of appointment data by time period
 * @param props.isDarkMode - Current theme mode for styling
 * @param props.title - Custom chart title (optional)
 * @param props.subtitle - Custom chart subtitle (optional)
 * @param props.className - Additional CSS classes (optional)
 * 
 * @features
 * - Interactive bar chart with hover tooltips
 * - Responsive design adapting to container size
 * - Theme-aware styling for light/dark modes
 * - Configurable title and subtitle
 * - Smooth animations and transitions
 * 
 * @architecture
 * - Uses Recharts library for chart rendering
 * - Integrates useChartConfiguration hook for consistent theming
 * - Memoized component for performance optimization
 * - Centralized configuration through constants
 * 
 * @accessibility
 * - Semantic HTML structure with proper headings
 * - High contrast colors for chart elements
 * - Descriptive tooltips for data points
 * - Keyboard navigation support through Recharts
 * 
 * @performance
 * - Memoized to prevent unnecessary re-renders
 * - Efficient chart rendering with ResponsiveContainer
 * - Optimized tooltip configuration
 * - Lightweight data processing
 */
const AppointmentChart: FC<AppointmentChartProps> = memo(({
  data,
  isDarkMode,
  title = CHART_SECTIONS.appointmentHistory.title,
  subtitle = CHART_SECTIONS.appointmentHistory.subtitle,
  className = '',
}) => {
  // Get chart configuration based on theme
  const {
    tooltipContentStyle,
    tooltipItemStyle,
    tooltipLabelStyle,
    gridConfig,
    axisConfig,
    colors,
  } = useChartConfiguration(isDarkMode);

  return (
    <div className={`lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}>
      {/* Chart Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          {title}
        </h2>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {subtitle}
          </span>
          <BarChart2 className="h-5 w-5 text-gray-400 dark:text-gray-500" />
        </div>
      </div>

      {/* Chart Container */}
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
          >
            <CartesianGrid
              strokeDasharray={gridConfig.strokeDasharray}
              stroke={gridConfig.stroke}
            />
            <XAxis
              dataKey="name"
              stroke={axisConfig.stroke}
            />
            <YAxis 
              stroke={axisConfig.stroke}
            />
            <RechartsTooltip
              contentStyle={tooltipContentStyle}
              itemStyle={tooltipItemStyle}
              labelStyle={tooltipLabelStyle}
              wrapperStyle={{ outline: "none" }}
            />
            <Bar
              dataKey="appointments"
              fill={colors.primary}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
});

// Set display name for debugging
AppointmentChart.displayName = 'AppointmentChart';

export default AppointmentChart;
