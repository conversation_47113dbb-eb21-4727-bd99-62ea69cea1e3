import { FC, memo } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Clock, CalendarX, Heart } from "lucide-react";
import {
  PERSONAL_STATS_CONFIG,
  CHART_SECTIONS,
} from "@/presentation/constants/patient-dashboard.constants";

/**
 * Appointment statistics interface
 */
interface AppointmentStats {
  /** Number of completed appointments */
  completed: number;
  /** Number of upcoming appointments */
  upcoming: number;
  /** Number of cancelled appointments */
  cancelled: number;
  /** Number of appointments completed this month */
  completedThisMonth?: number;
}

/**
 * Props for the PersonalStatsCard component
 */
interface PersonalStatsCardProps {
  /** Appointment statistics object */
  appointmentStats: AppointmentStats;
  /** Current weight in kilograms */
  currentWeight?: number;
  /** Card title (optional, uses default if not provided) */
  title?: string;
  /** Additional CSS classes for customization */
  className?: string;
}

/**
 * PersonalStatsCard Component
 *
 * Displays personal statistics in a compact card format including
 * appointment counts (completed, upcoming, cancelled) and current
 * weight. Provides a quick overview of the patient's activity.
 *
 * @component
 * @example
 * ```tsx
 * <PersonalStatsCard
 *   appointmentStats={{
 *     completed: 8,
 *     upcoming: 2,
 *     cancelled: 1
 *   }}
 *   currentWeight={72}
 * />
 * ```
 *
 * @param props - Component props
 * @param props.appointmentStats - Object containing appointment counts
 * @param props.currentWeight - Patient's current weight in kg (optional)
 * @param props.title - Custom card title (optional)
 * @param props.className - Additional CSS classes (optional)
 *
 * @features
 * - Four mini-cards showing key personal metrics
 * - Color-coded icons for visual identification
 * - Responsive 2x2 grid layout
 * - Dark mode support throughout
 * - Graceful handling of missing data
 *
 * @architecture
 * - Compact card design with consistent spacing
 * - Uses centralized configuration for styling
 * - Memoized component for performance
 * - Semantic HTML structure
 *
 * @accessibility
 * - Proper heading hierarchy with h2 title
 * - High contrast icons and text
 * - Descriptive labels for each metric
 * - Screen reader friendly layout
 *
 * @performance
 * - Memoized to prevent unnecessary re-renders
 * - Lightweight component with minimal dependencies
 * - Efficient icon rendering
 */
const PersonalStatsCard: FC<PersonalStatsCardProps> = memo(
  ({
    appointmentStats,
    currentWeight,
    title = CHART_SECTIONS.personalStats.title,
    className = "",
  }) => {
    return (
      <div
        className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden ${className}`}
      >
        {/* Card Header */}
        <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h2>
        </div>

        {/* Stats Grid */}
        <div className="p-6 space-y-6">
          <div className="grid grid-cols-2 gap-4">
            {/* Completed Appointments */}
            <div
              className={`${PERSONAL_STATS_CONFIG.completed.bgColor} rounded-lg p-4 text-center`}
            >
              <CheckCircle
                className={`h-6 w-6 ${PERSONAL_STATS_CONFIG.completed.iconColor} mx-auto mb-2`}
              />
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {appointmentStats.completed}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {PERSONAL_STATS_CONFIG.completed.title}
              </p>
            </div>

            {/* Upcoming Appointments */}
            <div
              className={`${PERSONAL_STATS_CONFIG.upcoming.bgColor} rounded-lg p-4 text-center`}
            >
              <Clock
                className={`h-6 w-6 ${PERSONAL_STATS_CONFIG.upcoming.iconColor} mx-auto mb-2`}
              />
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {appointmentStats.upcoming}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {PERSONAL_STATS_CONFIG.upcoming.title}
              </p>
            </div>

            {/* Cancelled Appointments */}
            <div
              className={`${PERSONAL_STATS_CONFIG.cancelled.bgColor} rounded-lg p-4 text-center`}
            >
              <CalendarX
                className={`h-6 w-6 ${PERSONAL_STATS_CONFIG.cancelled.iconColor} mx-auto mb-2`}
              />
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {appointmentStats.cancelled}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {PERSONAL_STATS_CONFIG.cancelled.title}
              </p>
            </div>

            {/* Current Weight */}
            <div
              className={`${PERSONAL_STATS_CONFIG.currentWeight.bgColor} rounded-lg p-4 text-center`}
            >
              <Heart
                className={`h-6 w-6 ${PERSONAL_STATS_CONFIG.currentWeight.iconColor} mx-auto mb-2`}
              />
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {currentWeight
                  ? `${currentWeight}${PERSONAL_STATS_CONFIG.currentWeight.unit}`
                  : "--"}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {PERSONAL_STATS_CONFIG.currentWeight.title}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  },
);

// Set display name for debugging
PersonalStatsCard.displayName = "PersonalStatsCard";

export default PersonalStatsCard;
