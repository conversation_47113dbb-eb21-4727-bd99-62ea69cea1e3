import { FC, memo } from 'react';
import { Calendar, Clock, Heart } from 'lucide-react';
import StatCard from '@/presentation/components/common/StatCard';
import { PATIENT_STAT_CARDS } from '@/presentation/constants/patient-dashboard.constants';
import { PatientTrends } from '@/presentation/types/patient.types';

/**
 * Props for the StatisticsGrid component
 */
interface StatisticsGridProps {
  /** Total number of patient appointments */
  totalAppointments: number;
  /** Number of upcoming appointments */
  upcomingAppointments: number;
  /** Current health status assessment */
  healthStatus: string;
  /** Trending metrics for comparison */
  trends?: PatientTrends;
  /** Additional CSS classes for customization */
  className?: string;
}

/**
 * StatisticsGrid Component
 * 
 * Displays key patient metrics in a responsive grid layout including
 * total appointments, upcoming appointments, and health status.
 * Provides a quick overview of the patient's current medical situation.
 * 
 * @component
 * @example
 * ```tsx
 * <StatisticsGrid 
 *   totalAppointments={12}
 *   upcomingAppointments={3}
 *   healthStatus="Excellente"
 *   trends={{ newPatients: 15 }}
 * />
 * ```
 * 
 * @param props - Component props
 * @param props.totalAppointments - Total number of patient appointments
 * @param props.upcomingAppointments - Number of upcoming appointments
 * @param props.healthStatus - Current health status assessment
 * @param props.trends - Trending metrics for comparison (optional)
 * @param props.className - Additional CSS classes (optional)
 * 
 * @features
 * - Three main statistics cards with consistent styling
 * - Responsive grid layout (1 col mobile, 2 col tablet, 3 col desktop)
 * - Icon-based visual identification for each metric
 * - Trend indicators for upcoming appointments
 * - Dark mode support throughout
 * 
 * @architecture
 * - Uses StatCard components for consistent styling
 * - Responsive grid layout with Tailwind CSS
 * - Integrates with centralized constants for configuration
 * - Memoized for performance optimization
 * 
 * @accessibility
 * - Semantic section element for proper document structure
 * - Descriptive card titles and values
 * - High contrast icons and text
 * - Screen reader friendly layout
 * 
 * @performance
 * - Memoized to prevent unnecessary re-renders
 * - Lightweight validation for trend calculations
 * - Efficient icon rendering with Lucide React
 */
const StatisticsGrid: FC<StatisticsGridProps> = memo(({
  totalAppointments,
  upcomingAppointments,
  healthStatus,
  trends,
  className = '',
}) => {
  // Calculate trend for upcoming appointments
  const upcomingTrend = trends ? {
    value: trends.newPatients || 0,
    isPositive: true,
    label: "ce mois-ci",
  } : null;

  return (
    <section className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8 ${className}`}>
      {/* Total Appointments Card */}
      <StatCard
        title={PATIENT_STAT_CARDS.totalAppointments.title}
        value={totalAppointments}
        icon={
          <Calendar className={`h-6 w-6 ${PATIENT_STAT_CARDS.totalAppointments.iconColor}`} />
        }
        trend={null}
        bgColor={PATIENT_STAT_CARDS.totalAppointments.bgColor}
      />

      {/* Upcoming Appointments Card */}
      <StatCard
        title={PATIENT_STAT_CARDS.upcomingAppointments.title}
        value={upcomingAppointments}
        icon={
          <Clock className={`h-6 w-6 ${PATIENT_STAT_CARDS.upcomingAppointments.iconColor}`} />
        }
        trend={upcomingTrend}
        bgColor={PATIENT_STAT_CARDS.upcomingAppointments.bgColor}
      />

      {/* Health Status Card */}
      <StatCard
        title={PATIENT_STAT_CARDS.healthStatus.title}
        value={healthStatus}
        icon={
          <Heart className={`h-6 w-6 ${PATIENT_STAT_CARDS.healthStatus.iconColor}`} />
        }
        trend={null}
        bgColor={PATIENT_STAT_CARDS.healthStatus.bgColor}
      />
    </section>
  );
});

// Set display name for debugging
StatisticsGrid.displayName = 'StatisticsGrid';

export default StatisticsGrid;
