/**
 * Patient Dashboard Components
 * 
 * Centralized exports for all patient dashboard sub-components.
 * These components work together to create a comprehensive
 * dashboard experience for patients.
 */

// Main dashboard components
export { default as DashboardHeader } from './DashboardHeader';
export { default as StatisticsGrid } from './StatisticsGrid';
export { default as AppointmentChart } from './AppointmentChart';
export { default as PersonalStatsCard } from './PersonalStatsCard';

// Type exports for component props
export type { default as DashboardHeaderProps } from './DashboardHeader';
export type { default as StatisticsGridProps } from './StatisticsGrid';
export type { default as AppointmentChartProps } from './AppointmentChart';
export type { default as PersonalStatsCardProps } from './PersonalStatsCard';
