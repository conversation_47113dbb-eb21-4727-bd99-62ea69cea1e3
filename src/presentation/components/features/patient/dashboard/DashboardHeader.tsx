import { FC, memo } from 'react';
import { DASHBOARD_HEADER_CONFIG } from '@/presentation/constants/patient-dashboard.constants';

/**
 * Props for the DashboardHeader component
 */
interface DashboardHeaderProps {
  /** <PERSON><PERSON>'s full name for personalized greeting */
  patientName?: string;
  /** Optional custom subtitle text */
  subtitle?: string;
  /** Additional CSS classes for customization */
  className?: string;
}

/**
 * DashboardHeader Component
 * 
 * Displays a personalized welcome header for the patient dashboard
 * with greeting message and descriptive subtitle. Provides a warm,
 * welcoming entry point to the dashboard experience.
 * 
 * @component
 * @example
 * ```tsx
 * // Basic usage with patient name
 * <DashboardHeader patientName="<PERSON>" />
 * 
 * // With custom subtitle
 * <DashboardHeader 
 *   patientName="<PERSON>"
 *   subtitle="Bienvenue sur votre espace personnel"
 * />
 * 
 * // Fallback when no patient name available
 * <DashboardHeader />
 * ```
 * 
 * @param props - Component props
 * @param props.patientName - <PERSON><PERSON>'s full name for greeting
 * @param props.subtitle - Custom subtitle text (optional)
 * @param props.className - Additional CSS classes (optional)
 * 
 * @features
 * - Personalized greeting with patient name
 * - Fallback to generic greeting when name unavailable
 * - Configurable subtitle text
 * - Responsive typography scaling
 * - Dark mode support
 * 
 * @architecture
 * - Memoized component for performance optimization
 * - Uses centralized configuration constants
 * - Semantic HTML structure with proper heading hierarchy
 * - Consistent styling with design system
 * 
 * @accessibility
 * - Proper heading hierarchy (h1 for main title)
 * - High contrast text colors for readability
 * - Semantic HTML structure for screen readers
 * - Responsive text sizing for different devices
 * 
 * @performance
 * - Memoized to prevent unnecessary re-renders
 * - Lightweight component with minimal dependencies
 * - Efficient string concatenation for patient name
 */
const DashboardHeader: FC<DashboardHeaderProps> = memo(({
  patientName,
  subtitle = DASHBOARD_HEADER_CONFIG.subtitle,
  className = '',
}) => {
  // Format patient name or use fallback
  const displayName = patientName || DASHBOARD_HEADER_CONFIG.greeting.fallbackName;
  
  // Construct greeting message
  const greetingMessage = `${DASHBOARD_HEADER_CONFIG.greeting.title}, ${displayName}`;

  return (
    <header className={`${DASHBOARD_HEADER_CONFIG.greeting.titleClasses} ${className}`}>
      <h1 className={DASHBOARD_HEADER_CONFIG.greeting.titleClasses}>
        {greetingMessage}
      </h1>
      <p className={DASHBOARD_HEADER_CONFIG.greeting.subtitleClasses}>
        {subtitle}
      </p>
    </header>
  );
});

// Set display name for debugging
DashboardHeader.displayName = 'DashboardHeader';

export default DashboardHeader;
