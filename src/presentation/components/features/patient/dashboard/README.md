# Patient Dashboard Components

This directory contains the refactored patient dashboard components that provide a comprehensive interface for patients to view their medical information, appointments, and health metrics.

## Components Overview

### DashboardHeader
**Purpose**: Personalized welcome interface for patients
**Location**: `./DashboardHeader.tsx`

```tsx
<DashboardHeader 
  patientName="Marie Dubois"
  subtitle="Bienvenue sur votre espace personnel"
/>
```

**Features**:
- Personalized greeting with patient name
- Fallback to generic greeting when name unavailable
- Configurable subtitle text
- Responsive typography and dark mode support

### StatisticsGrid
**Purpose**: Key metrics overview in responsive grid layout
**Location**: `./StatisticsGrid.tsx`

```tsx
<StatisticsGrid
  totalAppointments={12}
  upcomingAppointments={3}
  healthStatus="Excellente"
  trends={{ newPatients: 15 }}
/>
```

**Features**:
- Three main statistics cards (appointments, health status)
- Responsive grid (1 col mobile, 2 col tablet, 3 col desktop)
- Trend indicators for metrics comparison
- Icon-based visual identification

### AppointmentChart
**Purpose**: Visual representation of appointment history
**Location**: `./AppointmentChart.tsx`

```tsx
<AppointmentChart
  data={appointmentData}
  isDarkMode={isDarkMode}
  title="Mon Historique"
  subtitle="6 derniers mois"
/>
```

**Features**:
- Interactive bar chart with hover tooltips
- Theme-aware styling for light/dark modes
- Responsive design adapting to container size
- Configurable title and subtitle

### PersonalStatsCard
**Purpose**: Detailed personal statistics in compact format
**Location**: `./PersonalStatsCard.tsx`

```tsx
<PersonalStatsCard
  appointmentStats={{
    completed: 8,
    upcoming: 2,
    cancelled: 1
  }}
  currentWeight={72}
/>
```

**Features**:
- Four mini-cards showing key personal metrics
- Color-coded icons for visual identification
- Responsive 2x2 grid layout
- Graceful handling of missing data

## Usage Example

```tsx
import {
  DashboardHeader,
  StatisticsGrid,
  AppointmentChart,
  PersonalStatsCard,
} from '@/presentation/components/features/patient/dashboard';

function PatientDashboard() {
  const { patient, appointmentData, appointmentStats } = useDashboardPatient(patientId);
  const healthScore = useHealthScore(patient?.signe_vitaux, appointments);
  
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <DashboardHeader patientName={`${patient.nom} ${patient.prenom}`} />
      
      <StatisticsGrid
        totalAppointments={12}
        upcomingAppointments={3}
        healthStatus={healthScore.status}
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <AppointmentChart
          data={appointmentData}
          isDarkMode={isDarkMode}
          className="lg:col-span-2"
        />
        
        <PersonalStatsCard
          appointmentStats={appointmentStats}
          currentWeight={patient?.signe_vitaux?.[0]?.poid}
        />
      </div>
    </div>
  );
}
```

## Architecture Benefits

### Separation of Concerns
- **Presentation**: Components handle only UI rendering
- **Business Logic**: Extracted to specialized hooks
- **Configuration**: Centralized in constants files
- **Data Management**: Focused data fetching hooks

### Performance Optimizations
- All components use `React.memo` for efficient re-rendering
- Memoized calculations in hooks prevent unnecessary work
- Lightweight validation without heavy real-time processing
- Efficient prop interfaces with minimal data passing

### Maintainability
- Clear component responsibilities and boundaries
- Comprehensive JSDoc documentation with examples
- Consistent styling through centralized constants
- Easy testing with focused, single-purpose components

### Accessibility
- Semantic HTML structure with proper heading hierarchy
- High contrast colors for both light and dark themes
- Keyboard navigation support
- Screen reader friendly content structure

## Configuration

Components use centralized configuration from:
- `@/presentation/constants/dashboard.constants.ts` - General dashboard config
- `@/presentation/constants/patient-dashboard.constants.ts` - Patient-specific config

## Related Hooks

These components work with specialized hooks:
- `useDashboardPatient` - Core data fetching
- `useHealthScore` - Health calculations
- `useChartConfiguration` - Chart theming
- `useAppointmentActions` - Appointment operations
- `usePrescriptionActions` - Prescription management

## Testing

Each component includes:
- Unit tests for individual functionality
- Integration tests with hooks
- Visual regression tests for UI consistency
- Accessibility compliance tests

## Future Enhancements

- Real-time data updates via WebSocket
- Advanced health analytics and trends
- Customizable dashboard layouts
- Enhanced mobile experience
- Offline support capabilities
