import { Document } from '@/presentation/types/patient.types';
import { DOCUMENT_TYPE_ICONS } from '@/presentation/constants/patient.constants';
import { TEXT_STYLES, BADGE_STYLES } from '@/presentation/styles/common';

interface RecentDocumentsProps {
  documents: Document[];
  onView: (id: string) => void;
}

export function RecentDocuments({ documents, onView }: RecentDocumentsProps) {
  return (
    <div className="rounded-lg border dark:border-gray-700">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Documents récents</h3>
      </div>
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {documents.map((doc) => (
          <div
            key={doc.id}
            className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
            onClick={() => onView(doc.id)}
          >
            <div className="flex items-start gap-3">
              <div className="p-2 bg-blue-50 dark:bg-blue-900 rounded-lg">
                <span className="text-2xl text-blue-600 dark:text-blue-400">{DOCUMENT_TYPE_ICONS[doc.type]}</span>
              </div>
              <div>
                <p className={`font-medium flex items-center gap-2 ${TEXT_STYLES.primary}`}>
                  {doc.title}
                  {doc.isNew && (
                    <span className={`${BADGE_STYLES.base} ${BADGE_STYLES.new}`}>
                      <span className={BADGE_STYLES.dot} />
                      Nouveau
                    </span>
                  )}
                </p>
                <p className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}>
                  Dr. {doc.doctorName} • {doc.date}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
