import { Ava<PERSON>, <PERSON>, Typography } from "@mui/material";
import { motion } from "framer-motion";
import { Calendar, Droplet, Shield } from "lucide-react";
import { DESTRUCTIVE, PRIMARY } from "@/shared/constants/Color";
import { useProfilePatientData } from "@/presentation/hooks/useProfilePatientData";

const HeaderStylePatient = () => {
  const { patientData } = useProfilePatientData(true);

  // Fonction pour calculer l'âge
  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birth.getDate())
    ) {
      age--;
    }
    return age;
  };
  return (
    <div className="flex items-center gap-3">
      {/* Avatar compact */}
      <motion.div
        whileHover={{ scale: 1.05 }}
        transition={{ duration: 0.2 }}
        className="relative"
      >
        <Avatar
          src={patientData?.avatar}
          sx={{
            width: 48,
            height: 48,
            border: "2px solid",
            borderColor: PRIMARY,
            boxShadow: "0 2px 8px rgba(39, 170, 225, 0.3)",
          }}
        />
        {/* Indicateur de statut */}
        {!patientData?.decede && (
          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
        )}
      </motion.div>

      {/* Informations principales compactes */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <Typography
            variant="h6"
            className="text-white font-semibold truncate"
            sx={{ fontSize: "1.1rem", lineHeight: 1.2 }}
          >
            {patientData?.nom} {patientData?.prenom}
          </Typography>

          {/* Badges compacts */}
          <div className="flex gap-1">
            {patientData?.decede && (
              <Chip
                label="Décédé"
                size="small"
                sx={{
                  backgroundColor: DESTRUCTIVE,
                  color: "white",
                  fontWeight: 500,
                  fontSize: "0.7rem",
                  height: "20px",
                  "& .MuiChip-label": { px: 1 },
                }}
              />
            )}

            {patientData?.sexe && (
              <Chip
                label={patientData.sexe === "homme" ? "H" : "F"}
                size="small"
                sx={{
                  backgroundColor:
                    patientData.sexe === "homme" ? "#3b82f6" : "#ec4899",
                  color: "white",
                  fontWeight: 500,
                  fontSize: "0.7rem",
                  height: "20px",
                  minWidth: "24px",
                  "& .MuiChip-label": { px: 1 },
                }}
              />
            )}
          </div>
        </div>

        {/* Métadonnées compactes */}
        <div className="flex items-center gap-3 text-xs text-gray-600 dark:text-gray-300">
          <span className="flex items-center gap-1">
            <Shield size={12} className="text-blue-500 dark:text-blue-400" />
            <span className="font-medium text-gray-300">
              #{patientData?.unique_id}
            </span>
          </span>

          {patientData?.date_naissance && (
            <span className="flex items-center gap-1">
              <Calendar
                size={12}
                className="text-green-500 dark:text-green-400"
              />
              <span className="font-medium text-gray-300">
                {calculateAge(
                  patientData.date_naissance instanceof Date
                    ? patientData.date_naissance.toISOString()
                    : patientData.date_naissance
                )}{" "}
                ans
              </span>
            </span>
          )}

          {patientData?.groupe_sanguin && (
            <span className="flex items-center gap-1">
              <Droplet size={12} className="text-red-500 dark:text-red-400" />
              <span className="font-medium text-red-600 dark:text-red-400">
                {patientData.groupe_sanguin}
              </span>
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default HeaderStylePatient;
