import { useState } from "react";
import {
  Ava<PERSON>,
  Box,
  Button,
  Chip,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { motion } from "framer-motion";
import {
  Phone,
  Mail,
  Calendar,
  Droplet,
  Heart,
  AlertTriangle,
  Shield,
  Activity,
} from "lucide-react";
import { DESTRUCTIVE, PRIMARY } from "@/shared/constants/Color";
import PatientActions from "./PatientActions";
import { DeletePatientModal } from "@/presentation/components/common/Modal/DeletePatientModal";
import { ArchivePatientModal } from "@/presentation/components/common/Modal/ArchivePatientModal";
import { DecedePatientModal } from "@/presentation/components/common/Modal/DecedePatientModal";
import SigneVitauxSection from "./SigneVitauxSection";
import ContactUrgenceSection from "./ContactUrgenceSection";
import { useProfilePatientData } from "@/presentation/hooks/useProfilePatientData";
import { EditPatientModal } from "@/presentation/components/common/Modal/EditPatientModal";
import { ListeProcheModal } from "@/presentation/components/common/Modal/ListeProcheModal";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { useAppSelector } from "@/presentation/hooks/redux";

const PatientHeader = ({
  handleEditToggle,
  editMode,
}: {
  handleEditToggle?: () => void;
  editMode?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isDeletePatientModal, setIsDeletePatientModal] =
    useState<boolean>(false);
  const [isArchivePatientModal, setIsArchivePatientModal] =
    useState<boolean>(false);
  const [isPatientDecedeModal, setIsPatientDecedeModal] =
    useState<boolean>(false);
  const [isEditPatientModal, setIsEditPatientModal] = useState<boolean>(false);

  const handleCloseDeletePatientModal = () => {
    setIsDeletePatientModal(false);
  };

  const handleCloseArchivePatientModal = () => {
    setIsArchivePatientModal(false);
  };
  const handleClosePatientDecedeModal = () => {
    setIsPatientDecedeModal(false);
  };
  const { patientData, professionalPatientId } = useProfilePatientData(true);
  const role = useAppSelector((state) => state.authentification.user?.role);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md"));

  // Fonction pour calculer l'âge
  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birth.getDate())
    ) {
      age--;
    }
    return age;
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 mb-6"
      >
        {/* Header principal - Nouvelle structure en deux colonnes */}
        <Box
          className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 px-4 py-5 border-b border-gray-200 dark:border-gray-600"
          sx={{
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            gap: isMobile ? 3 : 4,
            alignItems: isMobile ? "center" : "flex-start",
          }}
        >
          {/* Colonne gauche: Avatar du patient */}
          <Box
            sx={{
              flex: isMobile ? "none" : "0 0 auto",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 1,
              position: "relative",
            }}
          >
            <motion.div transition={{ duration: 0.2 }} className="relative">
              <Avatar
                src={patientData?.avatar}
                sx={{
                  width: isMobile ? 80 : 120,
                  height: isMobile ? 80 : 120,
                  border: "2px solid",
                  borderColor: PRIMARY,
                }}
              />
              {/* Indicateur de statut */}
              {/* {!patientData?.decede && (
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
              )} */}
            </motion.div>

            {/* Badges */}
            <Box
              sx={{
                display: "flex",
                gap: 1,
                justifyContent: "center",
              }}
            >
              {patientData?.decede && (
                <Chip
                  label="Décédé"
                  size="small"
                  sx={{
                    backgroundColor: DESTRUCTIVE,
                    color: "white",
                    fontWeight: 500,
                    fontSize: "0.7rem",
                    height: "24px",
                    "& .MuiChip-label": { px: 1 },
                  }}
                />
              )}

              {patientData?.sexe && (
                <Chip
                  label={patientData.sexe === "homme" ? "H" : "F"}
                  size="small"
                  sx={{
                    backgroundColor:
                      patientData.sexe === "homme" ? "#3b82f6" : "#ec4899",
                    color: "white",
                    fontWeight: 500,
                    fontSize: "0.7rem",
                    height: "24px",
                    minWidth: "28px",
                  }}
                />
              )}
            </Box>
          </Box>

          {/* Colonne droite: Informations principales */}
          <Box
            sx={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              gap: 2,
              alignItems: isMobile ? "center" : "flex-start",
              textAlign: isMobile ? "center" : "left",
              width: isMobile ? "100%" : "auto",
            }}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                alignItems: isMobile ? "center" : "flex-start",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography
                variant="h5"
                className="text-gray-900 dark:text-white font-semibold"
                sx={{
                  fontSize: { xs: "1.2rem", sm: "1.5rem" },
                  lineHeight: 1.2,
                }}
              >
                {patientData?.nom} {patientData?.prenom}
              </Typography>

              {/* Actions */}
              <Box>
                {role === utilisateurs_role_enum.PATIENT ? (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleEditToggle}
                    sx={{
                      fontSize: "0.9rem",
                      textTransform: "none",
                      px: 2,
                      py: 0.8,
                    }}
                  >
                    {editMode ? "Annuler" : "Modifier"}
                  </Button>
                ) : (
                  <PatientActions
                    setIsEditPatientModal={setIsEditPatientModal}
                    setIsDeletePatientModal={setIsDeletePatientModal}
                    setIsArchivePatientModal={setIsArchivePatientModal}
                    setIsPatientDecedeModal={setIsPatientDecedeModal}
                    isPatientDecede={patientData?.decede}
                  />
                )}
              </Box>
            </Box>

            {/* Métadonnées */}
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: { xs: "1fr", sm: "repeat(2, 1fr)" },
                gap: 1,
                width: "100%",
              }}
            >
              <div className="flex items-center gap-2">
                <Shield
                  size={16}
                  className="text-blue-500 dark:text-blue-400"
                />
                <Typography variant="body2" className="font-medium">
                  ID Patient: #{patientData?.unique_id}
                </Typography>
              </div>

              {patientData?.date_naissance && (
                <div className="flex items-center gap-2">
                  <Calendar
                    size={16}
                    className="text-green-500 dark:text-green-400"
                  />
                  <Typography variant="body2">
                    {new Date(
                      patientData.date_naissance instanceof Date
                        ? patientData.date_naissance
                        : patientData.date_naissance
                    ).toLocaleDateString("fr-FR")}{" "}
                    (
                    {calculateAge(
                      patientData.date_naissance instanceof Date
                        ? patientData.date_naissance.toISOString()
                        : patientData.date_naissance
                    )}{" "}
                    ans)
                  </Typography>
                </div>
              )}

              {patientData?.groupe_sanguin && (
                <div className="flex items-center gap-2">
                  <Droplet
                    size={16}
                    className="text-red-500 dark:text-red-400"
                  />
                  <Typography
                    variant="body2"
                    className="font-medium text-red-600 dark:text-red-400"
                  >
                    Groupe sanguin: {patientData.groupe_sanguin}
                  </Typography>
                </div>
              )}

              {patientData?.email && (
                <div className="flex items-center gap-2">
                  <Mail
                    size={16}
                    className="text-indigo-500 dark:text-indigo-400"
                  />
                  <Typography
                    variant="body2"
                    className="truncate max-w-[200px]"
                  >
                    {patientData.email}
                  </Typography>
                </div>
              )}
            </Box>

            {/* Patient information cards - Integrated from content section */}
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "repeat(2, 1fr)",
                  lg: "repeat(4, 1fr)",
                },
                gap: 2,
                width: "100%",
              }}
            >
              {/* Contact compact */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 space-y-2"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Phone
                    size={14}
                    className="text-blue-500 dark:text-blue-400"
                  />
                  <Typography
                    variant="subtitle2"
                    className="text-gray-700 dark:text-gray-200 font-medium"
                  >
                    Contact
                  </Typography>
                </div>

                <div className="space-y-1.5 text-xs">
                  <div className="flex items-center gap-2">
                    <Phone size={12} className="text-gray-400 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300 truncate">
                      {patientData?.telephone || "Non renseigné"}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail size={12} className="text-gray-400 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300 truncate">
                      {patientData?.email || "Non renseigné"}
                    </span>
                  </div>
                </div>
              </motion.div>

              {/* Informations médicales compactes */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 space-y-2"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Heart size={14} className="text-red-500 dark:text-red-400" />
                  <Typography
                    variant="subtitle2"
                    className="text-gray-700 dark:text-gray-200 font-medium"
                  >
                    Médical
                  </Typography>
                </div>

                <div className="space-y-1.5 text-xs">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-500 dark:text-gray-400">
                      Groupe
                    </span>
                    <span className="font-medium text-red-600 dark:text-red-400">
                      {patientData?.groupe_sanguin || "N/A"}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-500 dark:text-gray-400">
                      Donneur
                    </span>
                    <span
                      className={`font-medium text-xs ${
                        patientData?.donneur_sang
                          ? "text-green-600 dark:text-green-400"
                          : "text-gray-500 dark:text-gray-400"
                      }`}
                    >
                      {patientData?.donneur_sang !== undefined
                        ? patientData.donneur_sang
                          ? "Oui"
                          : "Non"
                        : "N/A"}
                    </span>
                  </div>
                </div>
              </motion.div>

              {/* Signes vitaux compacts */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Activity
                    size={14}
                    className="text-green-500 dark:text-green-400"
                  />
                  <Typography
                    variant="subtitle2"
                    className="text-gray-700 dark:text-gray-200 font-medium"
                  >
                    Signes vitaux
                  </Typography>
                </div>
                <div className="text-xs">
                  <SigneVitauxSection />
                </div>
              </motion.div>

              {/* Contact d'urgence compact */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3"
              >
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle
                    size={14}
                    className="text-orange-500 dark:text-orange-400"
                  />
                  <Typography
                    variant="subtitle2"
                    className="text-gray-700 dark:text-gray-200 font-medium"
                  >
                    Urgence
                  </Typography>
                </div>
                <div className="text-xs">
                  <ContactUrgenceSection urgence={patientData?.urgence} />
                </div>
              </motion.div>
              {role === utilisateurs_role_enum.PROFESSIONNEL && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      textTransform: "none",
                      backgroundColor: PRIMARY,
                    }}
                    onClick={() => setIsOpen(true)}
                  >
                    Voir liste des proches
                  </Button>
                </motion.div>
              )}
            </Box>
          </Box>
        </Box>
      </motion.div>

      {/* Modals */}
      <>
        {isEditPatientModal && patientData && (
          <EditPatientModal
            isOpen={isEditPatientModal}
            handleClose={() => setIsEditPatientModal(false)}
            patient={patientData}
          />
        )}
        {isDeletePatientModal && (
          <DeletePatientModal
            isOpen={isDeletePatientModal}
            patient={{
              id: professionalPatientId,
              nom: patientData.nom,
            }}
            handleClose={handleCloseDeletePatientModal}
          />
        )}
        {isArchivePatientModal && (
          <ArchivePatientModal
            isOpen={isArchivePatientModal}
            patient={{
              id: professionalPatientId,
              nom: patientData.nom,
            }}
            handleClose={handleCloseArchivePatientModal}
          />
        )}
        {isPatientDecedeModal && (
          <DecedePatientModal
            isOpen={isPatientDecedeModal}
            nom={patientData.nom}
            handleClose={handleClosePatientDecedeModal}
          />
        )}
        {isOpen && (
          <ListeProcheModal
            isOpen={isOpen}
            handleClose={() => setIsOpen(false)}
          />
        )}
      </>
    </>
  );
};

export default PatientHeader;
