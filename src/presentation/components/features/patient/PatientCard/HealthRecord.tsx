import { ChevronLeft, X } from "lucide-react";
import { motion } from "framer-motion";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import PatientHeader from "./PatientHeader";
import ActiveTab from "./ActiveTab";
import EmployerHeader from "../../professional/employer/EmployerHeader";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import useAuth from "@/presentation/hooks/use-auth";
import ProcheHeader from "./ProcheHeader";
import { useAppSelector } from "@/presentation/hooks/redux";

const renderHeader = (
  role: utilisateurs_role_enum,
  roleProf?: utilisateurs_role_enum
) => {
  switch (role) {
    case utilisateurs_role_enum.EMPLOYER:
      return <EmployerHeader />;
    case utilisateurs_role_enum.PATIENT:
      return <PatientHeader />;
    case utilisateurs_role_enum.PROCHE:
      return <ProcheHeader role={roleProf} />;
    default:
      return <p>aucun rendue</p>;
  }
};

const HealthRecord = () => {
  const { resetState } = useRegisterPatientState();
  const { roleUserSelected } = useAuth();
  const role = useAppSelector((state) => state.authentification.user?.role);
  return (
    <div className="sticky top-0 bg-white dark:bg-gray-800 z-50 pt-2 px-4">
      <motion.button
        onClick={() => {
          window.history.back();
          resetState();
        }}
        whileHover={{ scale: 1.02, x: -2 }}
        whileTap={{ scale: 0.98 }}
        className="group flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300
                   bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg
                   hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400
                   hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-200
                   shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
      >
        <ChevronLeft
          size={16}
          className="transition-transform duration-200 group-hover:-translate-x-0.5"
        />
        <span>Retour</span>
      </motion.button>
      <div className="max-w-7xl mx-auto pt-4">
        {renderHeader(roleUserSelected, role)}
        <ActiveTab />
      </div>
    </div>
  );
};

export default HealthRecord;
