import React, { useState } from "react";
import { EllipsisVertical, SkullIcon, UserIcon, X } from "lucide-react";
import { Menu, MenuItem, IconButton, Tooltip } from "@mui/material";
import { DeletePatientModal } from "@/presentation/components/common/Modal/DeletePatientModal";
import { DecedePatientModal } from "@/presentation/components/common/Modal/DecedePatientModal";
import { useProfilePatientData } from "@/presentation/hooks/useProfilePatientData";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";
import { EditPatientModal } from "@/presentation/components/common/Modal/EditPatientModal";

export default function Action() {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const [isDeletePatientModal, setIsDeletePatientModal] = useState(false);
  const [isPatientDecedeModal, setIsPatientDecedeModal] = useState(false);
  const [isEditPatientModal, setIsEditPatientModal] = useState(false);
  const { patientData, professionalPatientId } = useProfilePatientData();
  const { setIsProfile } = useCarnetDeSanteState();

  const handleCloseDeletePatientModal = () => {
    setIsDeletePatientModal(false);
  };

  const handleClosePatientDecedeModal = () => {
    setIsPatientDecedeModal(false);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div className="block lg:hidden">
      <Tooltip title="Actions">
        <IconButton onClick={handleClick}>
          <EllipsisVertical size={20} />
        </IconButton>
      </Tooltip>
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        <MenuItem
          onClick={() => {
            setIsEditPatientModal(true);
            setAnchorEl(null);
          }}
          className="flex items-center gap-2"
        >
          <UserIcon size={20} />
          Profile
        </MenuItem>
        <MenuItem
          onClick={() => {
            setIsDeletePatientModal(true);
            setAnchorEl(null);
          }}
          className="flex items-center gap-2"
        >
          <X size={20} />
          Retirer
        </MenuItem>
        {!patientData?.decede && (
          <MenuItem
            onClick={() => {
              setIsPatientDecedeModal(true);
              setAnchorEl(null);
            }}
            className="flex items-center gap-2"
          >
            <SkullIcon size={20} />
            Décede
          </MenuItem>
        )}
      </Menu>
      {isEditPatientModal && (
        <EditPatientModal
          isOpen={isEditPatientModal}
          handleClose={() => setIsEditPatientModal(false)}
          patient={patientData}
        />
      )}
      {isDeletePatientModal && (
        <DeletePatientModal
          isOpen={isDeletePatientModal}
          patient={{
            id: professionalPatientId,
            nom: patientData.nom,
          }}
          handleClose={handleCloseDeletePatientModal}
        />
      )}
      {isPatientDecedeModal && (
        <DecedePatientModal
          isOpen={isPatientDecedeModal}
          nom={patientData.nom}
          handleClose={handleClosePatientDecedeModal}
        />
      )}
    </div>
  );
}
