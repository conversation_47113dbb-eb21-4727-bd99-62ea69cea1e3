import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Typography,
  styled,
} from "@mui/material";
import {
  Person as PersonIcon,
  Cake as CakeIcon,
} from "@mui/icons-material";
import { PRIMARY } from "@/shared/constants/Color";
import { ProfessionnelPatientDTO } from "@/domain/DTOS";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { sexe_enum } from "@/domain/models/enums";

//StyledCard 
const StyledCard = styled(Card)(({ theme }) => ({
  border: "1px solid",
  borderColor: theme.palette.divider,
  borderLeft: `4px solid ${PRIMARY}`,
  boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
  borderRadius: theme.spacing(1),
  marginBottom: theme.spacing(2),
  cursor: "pointer",
  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  position: "relative",
  overflow: "hidden",
  "&:hover": {
    borderColor: PRIMARY,
    borderLeftWidth: "6px",
    boxShadow: "0 8px 25px rgba(39, 170, 225, 0.15)",
    transform: "translateY(-2px)",
    "& .patient-avatar": {
      transform: "scale(1.05)",
    },
    "& .patient-name": {
      color: PRIMARY,
    },
    "& .patient-id": {
      fontWeight: 600,
    },
    "&::before": {
      opacity: 1,
    },
  },
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `linear-gradient(135deg, ${PRIMARY}08, transparent)`,
    opacity: 0,
    transition: "opacity 0.3s ease",
    pointerEvents: "none",
  },
}));

//  Composant réutilisable pour l'icône + texte
const InfoRow = ({
  icon,
  text,
}: {
  icon: React.ReactNode;
  text: string;
}) => (
  <div className="flex items-center mb-1.5 text-sm text-gray-600">
    <div className="mr-2 text-gray-400">{icon}</div>
    <span>{text}</span>
  </div>
);

// Chip genre dynamique
const GenderChip = ({ sexe }: { sexe: sexe_enum }) => {
  const isFemme = sexe === sexe_enum.femme;
  return (
    <Chip
      icon={<PersonIcon />}
      label=""
      size="small"
      sx={{
        backgroundColor: isFemme ? '#e91e63' : '#2196f3',
        color: 'white',
        width: 28,
        height: 28,
        boxShadow: `0 2px 8px ${isFemme ? 'rgba(233, 30, 99, 0.3)' : 'rgba(33, 150, 243, 0.3)'}`,
        border: '2px solid white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        '& .MuiChip-icon': {
          color: 'white',
          fontSize: 16,
          margin: 0,
        },
        '& .MuiChip-label': {
          display: 'none',
        },
        '&:hover': {
          backgroundColor: isFemme ? '#c2185b' : '#1976d2',
          transform: 'scale(1.1)',
          boxShadow: `0 4px 12px ${isFemme ? 'rgba(233, 30, 99, 0.4)' : 'rgba(33, 150, 243, 0.4)'}`,
        },
      }}
    />
  );
};

//  Composant principal PatientCard
const PatientCard = ({ patient }: { patient: ProfessionnelPatientDTO }) => {
  return (
    <StyledCard>
      <CardContent className="p-6">
        <div className="flex items-center">
          <div className="relative mr-4">
            <Avatar
              className="patient-avatar"
              alt={`${patient.patient.nom} ${patient.patient.prenom || ''}`}
              sx={{
                width: 64,
                height: 64,
                transition: "transform 0.3s ease",
                border: "3px solid white",
                boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
              }}
            />
            <div className="absolute -bottom-1 -right-1">
              <GenderChip sexe={patient.patient.sexe} />
            </div>
          </div>

          <div className="flex-1">
            <Typography
              className="patient-name"
              variant="h6"
              sx={{ fontWeight: 600, fontSize: "1.1rem", mb: 0.5 }}
            >
              {patient.patient.nom} {patient.patient.prenom || ''}
            </Typography>

            <Typography
              className="patient-id"
              color="primary"
              variant="body2"
              sx={{ fontWeight: 500, fontSize: "0.9rem", mb: 1 }}
            >
              # {patient.patient.unique_id}
            </Typography>

            <InfoRow
              icon={<PersonIcon fontSize="small" />}
              text={patient.patient.sexe === sexe_enum.femme ? "Féminin" : "Masculin"}
            />

            <InfoRow
              icon={<CakeIcon fontSize="small" />}
              text={format(new Date(patient.patient.date_naissance), "dd/MM/yyyy", {
                locale: fr,
              })}
            />
          </div>
        </div>
      </CardContent>
    </StyledCard>
  );
};

export default PatientCard;
