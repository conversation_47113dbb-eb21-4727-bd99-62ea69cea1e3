import ToggleButton from "@mui/material/ToggleButton";
import ToggleButtonGroup from "@mui/material/ToggleButtonGroup";
import { useMediaQuery, useTheme } from "@mui/material";
import { active_tab_enum, utilisateurs_role_enum } from "@/domain/models/enums";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";
import { useAppSelector } from "@/presentation/hooks/redux";

const navButtonDass = [
  { value: active_tab_enum.carnetDeSante, text: "Carnet de santé" },
  {
    value: active_tab_enum.consultationMedicale,
    text: "Consultation médicale",
  },
  { value: active_tab_enum.signeVitaux, text: "Signe vitaux" },
  { value: active_tab_enum.diagnostic, text: "Laboratoire et diagnostic" },
  { value: active_tab_enum.pharmacie, text: "Pharmacie et médicament" },
];

const navButton = [
  { value: active_tab_enum.carnetDeSante, text: "Carnet de santé" },
  {
    value: active_tab_enum.consultationMedicale,
    text: "Consultation médicale",
  },
  { value: active_tab_enum.signeVitaux, text: "Signe vitaux" },
  { value: active_tab_enum.diagnostic, text: "Laboratoire et diagnostic" },
  { value: active_tab_enum.pharmacie, text: "Pharmacie et médicament" },
  { value: active_tab_enum.facturation, text: "Facturation" },
];

const ActiveTab = () => {
  const {
    activeTab,
    isProfile,
    handleActiveTabChange,
    setIsProfile,
    setIsAddForm,
  } = useCarnetDeSanteState();
  const role = useAppSelector((state) => state.authentification.user?.role);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const buttons =
    role === utilisateurs_role_enum.DASH ? navButtonDass : navButton;

  return (
    <div className="mx-auto pb-1">
      {!isProfile && (
        <ToggleButtonGroup
          color="primary"
          value={activeTab}
          exclusive
          fullWidth
          aria-label="tabs"
          // orientation={isMobile ? "vertical" : "horizontal"} // <-- responsive
          sx={{
            flexWrap: !isMobile ? "nowrap" : "wrap", // sur mobile, une colonne scrollable
            overflowX: !isMobile ? "auto" : "visible",
          }}
        >
          {buttons.map((nav) => (
            <ToggleButton
              key={nav.value}
              value={nav.value}
              sx={{
                textTransform: "none",
                fontSize: isMobile ? "0.75rem" : "0.875rem", // taille réduite mobile
                whiteSpace: "nowrap",
                fontWeight: "bold",
              }}
              onClick={() => {
                handleActiveTabChange(nav.value);
                setIsProfile(false);
                setIsAddForm(false);
              }}
              size={isMobile ? "small" : "medium"}
            >
              {nav.text}
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      )}
    </div>
  );
};

export default ActiveTab;
