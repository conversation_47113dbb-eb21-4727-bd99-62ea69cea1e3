import {
  <PERSON><PERSON>,
  Chip,
  Typography,
  Box,
  useMediaQuery,
  useTheme,
  Button,
} from "@mui/material";
import { motion } from "framer-motion";
import {
  Calendar,
  User,
  Link,
  Activity,
  BadgeInfo,
  Heart,
  Edit2,
} from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useProfilePatientData } from "@/presentation/hooks/useProfilePatientData";
import SigneVitauxSection from "./SigneVitauxSection";
import { useAppSelector } from "@/presentation/hooks/redux";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { DashRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useEffect, useState } from "react";
import ProcheAction from "@/presentation/pages/patient/families/ProcheAction";
import { EditProcheModal } from "@/presentation/components/common/Modal/EditProcheModal";
import { Proche } from "@/domain/models";

// Badge pour le sexe
const GenderChip = ({ sexe }: { sexe?: string }) => {
  if (!sexe) return null;
  const isFemme = sexe === "femme";
  return (
    <Chip
      icon={<User size={14} />}
      label={isFemme ? "F" : "H"}
      size="small"
      sx={{
        backgroundColor: isFemme ? "#e91e63" : "#2196f3",
        color: "white",
        fontWeight: 500,
        fontSize: "0.7rem",
        height: "20px",
        minWidth: "24px",
        "& .MuiChip-label": { px: 1 },
      }}
    />
  );
};

// Badge pour le lien de parenté
const RelationChip = ({ relation }: { relation?: string }) => {
  if (!relation) return null;
  return (
    <Chip
      icon={<Heart size={14} />}
      label={relation}
      size="small"
      sx={{
        backgroundColor: "#f59e42",
        color: "white",
        fontWeight: 500,
        fontSize: "0.7rem",
        height: "20px",
        minWidth: "24px",
        "& .MuiChip-label": { px: 1 },
      }}
    />
  );
};

// Calcul de l'âge à partir de la date de naissance
const calculateAge = (birthDate?: Date | string) => {
  if (!birthDate) return "-";
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  return age;
};

const ProcheHeader = ({ role }: { role: utilisateurs_role_enum }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md"));
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const { selectedProchePatient, selectedProcheEmployer } =
    useProfilePatientData(true);

  // Déterminer les données à afficher selon le rôle
  const [procheData, setProcheData] = useState<Proche | null>(null);

  const relatedPersonData =
    role === utilisateurs_role_enum.PROFESSIONNEL
      ? selectedProchePatient?.patient
      : selectedProcheEmployer?.employees;

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
  };

  useEffect(() => {
    if (selectedProchePatient) {
      const { patient, ...reste } = selectedProchePatient;
      setProcheData(reste);
    } else if (selectedProcheEmployer) {
      const { employees, ...reste } = selectedProcheEmployer;
      setProcheData(reste);
    }
  }, [selectedProchePatient, selectedProcheEmployer]);

  if (!procheData) {
    return (
      <div className="flex items-center justify-center h-32">
        <span className="text-gray-400 dark:text-gray-500">
          Chargement des informations...
        </span>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden mb-6"
    >
      {/* Header principal - Structure en deux colonnes comme EmployerHeader */}
      <Box
        className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 px-4 py-5 border-b border-gray-200 dark:border-gray-600"
        sx={{
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          gap: isMobile ? 3 : 4,
          alignItems: isMobile ? "center" : "flex-start",
        }}
      >
        {/* Colonne gauche: Avatar du proche */}
        <Box
          sx={{
            flex: isMobile ? "none" : "0 0 auto",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 1,
          }}
        >
          <motion.div className="relative">
            <Avatar
              sx={{
                width: isMobile ? 80 : 120,
                height: isMobile ? 80 : 120,
                border: "2px solid",
                borderColor: PRIMARY,
                boxShadow: "0 2px 8px rgba(39, 170, 225, 0.3)",
                backgroundColor: PRIMARY,
                fontSize: isMobile ? "1.5rem" : "2rem",
                fontWeight: 600,
                color: "white",
              }}
            >
              {`${procheData?.nom?.charAt(0)?.toUpperCase() || ""}${procheData?.prenom?.charAt(0)?.toUpperCase() || ""}`}
            </Avatar>
          </motion.div>

          <Box
            sx={{
              display: "flex",
              gap: 1,
              mt: 1,
              justifyContent: "center",
            }}
          >
            <GenderChip sexe={procheData?.sexe} />
          </Box>
        </Box>

        {/* Colonne droite: Informations principales */}
        <Box
          sx={{
            flex: 1,
            display: "flex",
            flexDirection: "column",
            gap: 1,
            alignItems: isMobile ? "center" : "flex-start",
            textAlign: isMobile ? "center" : "left",
            width: isMobile ? "100%" : "auto",
          }}
        >
          <Typography
            variant="h5"
            className="text-gray-900 dark:text-white font-semibold"
            sx={{ fontSize: { xs: "1.2rem", sm: "1.5rem" }, lineHeight: 1.2 }}
          >
            {procheData?.nom} {procheData?.prenom}
          </Typography>

          <div className="grid grid-cols-3 gap-2 w-full">
            <div>
              <div className="flex items-center gap-2">
                <Heart size={16} className="text-pink-500 dark:text-pink-400" />
                <Typography variant="body2" className="font-medium">
                  {role === utilisateurs_role_enum.PROFESSIONNEL ? (
                    <>
                      {procheData?.lien_parente} de {relatedPersonData?.nom}{" "}
                      {relatedPersonData?.prenom}
                    </>
                  ) : (
                    <>
                      {procheData?.lien_parente} de{" "}
                      <a
                        className="text-blue-600 hover:underline cursor-pointer"
                        href={`/commune-urbaine-antananarivo/dass/mes-employer/${selectedProcheEmployer?.id_patient}`}
                      >
                        {relatedPersonData?.nom} {relatedPersonData?.prenom}
                      </a>
                    </>
                  )}
                </Typography>
              </div>

              <div className="flex items-center gap-2">
                <Calendar
                  size={16}
                  className="text-indigo-500 dark:text-indigo-400"
                />
                <Typography variant="body2">
                  {procheData?.date_naissance
                    ? `${new Date(procheData.date_naissance).toLocaleDateString("fr-FR")} (${calculateAge(procheData.date_naissance)} ans)`
                    : "Non renseigné"}
                </Typography>
              </div>
              <div className="flex items-center gap-2">
                <BadgeInfo
                  size={16}
                  className="text-gray-500 dark:text-gray-400"
                />
                <Typography variant="body2" className="text-blue-500">
                  {role === utilisateurs_role_enum.PROFESSIONNEL
                    ? `Patient: ${relatedPersonData?.nom} ${relatedPersonData?.prenom}`
                    : `Matricule: ${(relatedPersonData as any)?.matricule || "Non renseigné"}`}
                </Typography>
              </div>
            </div>

            <div>
              <div className="flex items-center gap-2">
                <Activity
                  size={16}
                  className="text-green-500 dark:text-green-400"
                />
                <Typography variant="body2" className="font-medium">
                  Signes vitaux disponibles
                </Typography>
              </div>
            </div>
            <div>
              <Button
                variant="contained"
                size="small"
                sx={{
                  backgroundColor: PRIMARY,
                  textTransform: "none",
                }}
                // startIcon={<Edit2 />}
                onClick={() => setIsEditModalOpen(true)}
              >
                Modifier le profile
              </Button>
            </div>
          </div>
        </Box>
        {isEditModalOpen && (
          <EditProcheModal
            isOpen={isEditModalOpen}
            handleClose={handleCloseEditModal}
            proche={procheData}
          />
        )}
      </Box>
    </motion.div>
  );
};

export default ProcheHeader;
