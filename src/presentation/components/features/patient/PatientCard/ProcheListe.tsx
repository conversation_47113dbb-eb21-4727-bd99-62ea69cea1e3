import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { AddProcheModal } from "@/presentation/components/common/Modal/AddProchePatientModal";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { useProche } from "@/presentation/hooks/use-proches";
import ProcheFilters from "@/presentation/pages/patient/families/ProcheFilters";
import { PRIMARY } from "@/shared/constants/Color";
import { Button } from "@mui/material";
import { Plus } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";

const ProcheListe = () => {
  const { id: patientlId } = useParams();
  const [selectedProche, setSelectedProche] = useState<{
    id: number;
    nom: string;
    prenom: string;
  }>(null);
  const [isProcheModalOpen, setIsProcheModalOpen] = useState(false);
  const { proches, handleGetProcheByPatientId } = useProche();

  const filteredProches = useMemo(() => {
    return proches?.filter((prs) => {
      const matchesProches =
        selectedProche === null || prs.id === selectedProche.id;

      return matchesProches;
    });
  }, [proches, selectedProche]);

  useEffect(() => {
    if (patientlId) {
      handleGetProcheByPatientId(Number(patientlId));
    }
  }, [patientlId]);

  const handleCloseModal = () => {
    setIsProcheModalOpen(false);
  };

  return (
    <>
      <div className="flex justify-between my-4">
        <ProcheFilters
          selectedProche={selectedProche}
          onSelectedProcheChange={setSelectedProche}
        />
        {/* Boutton d'ajout */}
        <Button
          variant="contained"
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          startIcon={<Plus fontSize={16} />}
          onClick={() => setIsProcheModalOpen(true)}
        >
          Ajouter un proche
        </Button>
      </div>
      <div>
        <ListDataGrid data={filteredProches} type={"proche"} />
      </div>
      {isProcheModalOpen && (
        <AddProcheModal
          isProcheModalOpen={isProcheModalOpen}
          handleCloseModal={handleCloseModal}
        />
      )}
    </>
  );
};

export default ProcheListe;
