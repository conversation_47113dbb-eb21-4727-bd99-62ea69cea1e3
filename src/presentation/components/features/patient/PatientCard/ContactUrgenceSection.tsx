import { Urgence } from "@/domain/models";

interface ContactUrgenceSectionProps {
  urgence: Urgence[];
}

const ContactUrgenceSection = ({ urgence }: ContactUrgenceSectionProps) => {
  return (
    <p>
      <span className="dark:text-white text-black">Contact urgence : </span>
      <span className="dark:text-gray-400 text-gray-600">
        {urgence?.length > 0
          ? urgence
              .map((urgence) => `${urgence.contact_urgence_telephone}`)
              .join(", ")
          : "--"}
      </span>
    </p>
  );
};

export default ContactUrgenceSection;
