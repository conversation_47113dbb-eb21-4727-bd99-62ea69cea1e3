import { useCarnetDeSanteData } from "@/presentation/hooks/carnetDeSante";
import { format } from "date-fns";
import { fr } from "date-fns/locale";

const SigneVitauxSection = () => {
  const { signeVitaux } = useCarnetDeSanteData();

  // Récupération sécurisée du dernier signe vital
  const lastSigneVitaux =
    signeVitaux && signeVitaux.length > 0
      ? signeVitaux[signeVitaux.length - 1]
      : undefined;

  return (
    lastSigneVitaux && (
      <>
        <p>
          <span className="dark:text-white text-black">Taille : </span>
          <span className="dark:text-gray-400 text-gray-600">
            {lastSigneVitaux.taille} cm
          </span>
        </p>
        <p>
          <span className="dark:text-white text-black">Poids : </span>
          <span className="dark:text-gray-400 text-gray-600">
            {lastSigneVitaux.poid} kg
          </span>
        </p>
        <p>
          <span className="dark:text-white text-black">Tension : </span>
          <span className="dark:text-gray-400 text-gray-600">
            {lastSigneVitaux.tension_arterielle} mmHg
          </span>
        </p>
        <p>
          <span className="dark:text-white text-black">Date de visite : </span>
          <span className="dark:text-gray-400 text-gray-600">
            {format(new Date(lastSigneVitaux.date_visite), "EEEE d MMMM", {
              locale: fr,
            })}
          </span>
        </p>
      </>
    )
  );
};

export default SigneVitauxSection;
