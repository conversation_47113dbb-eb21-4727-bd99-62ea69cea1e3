import React, { useState } from 'react';
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, ChevronUp, HelpCircle, Calendar, Shield, Users, Clock, Phone, CreditCard, FileText } from 'lucide-react';

/**
 * Interface pour définir une question FAQ
 */
interface FAQItem {
  question: string;
  answer: string;
  icon: React.ReactNode;
  category: string;
}

/**
 * Composant FAQ - Affiche les questions fréquemment posées
 * Respecte les principes SOLID et utilise une architecture propre
 */
const FAQ = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const toggleQuestion = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const faqItems: FAQItem[] = [
    {
      question: "Comment prendre un rendez-vous ?",
      answer: "Il suffit de rechercher un médecin ou une spécialité dans notre barre de recherche, choisir un créneau disponible qui vous convient et confirmer votre rendez-vous. Vous recevrez une confirmation par email et SMS.",
      icon: <Calendar className="w-5 h-5" />,
      category: "Rendez-vous"
    },
    {
      question: "Est-ce que le service est gratuit ?",
      answer: "Oui, notre service de prise de rendez-vous en ligne est totalement gratuit pour les patients. Aucun frais caché, aucun abonnement requis. Vous ne payez que la consultation chez votre professionnel de santé.",
      icon: <CreditCard className="w-5 h-5" />,
      category: "Tarification"
    },
    {
      question: "Comment annuler ou modifier mon rendez-vous ?",
      answer: "Vous pouvez facilement gérer vos rendez-vous depuis votre espace personnel en vous connectant à votre compte. Vous pouvez annuler jusqu'à 24h avant le rendez-vous sans frais.",
      icon: <Clock className="w-5 h-5" />,
      category: "Gestion"
    },
    {
      question: "Quels types de professionnels de santé sont disponibles ?",
      answer: "Notre plateforme regroupe plus de 50 spécialités : médecins généralistes, dentistes, cardiologues, dermatologues, gynécologues, pédiatres, kinésithérapeutes, psychologues, et bien d'autres.",
      icon: <Users className="w-5 h-5" />,
      category: "Professionnels"
    },
    {
      question: "Mes données personnelles sont-elles sécurisées ?",
      answer: "Absolument ! Nous utilisons un chiffrement de bout en bout et sommes certifiés HDS (Hébergeur de Données de Santé). Vos informations médicales sont protégées selon les plus hauts standards de sécurité.",
      icon: <Shield className="w-5 h-5" />,
      category: "Sécurité"
    },
    {
      question: "Puis-je consulter mes documents médicaux en ligne ?",
      answer: "Oui, avec notre Carnet de Santé Numérique (CSN), vous pouvez stocker et consulter vos ordonnances, résultats d'examens et certificats médicaux en toute sécurité, accessible 24h/24.",
      icon: <FileText className="w-5 h-5" />,
      category: "Documents"
    },
    {
      question: "Comment contacter un professionnel en urgence ?",
      answer: "Pour les urgences médicales, contactez le 15 (SAMU) ou le 112. Notre plateforme propose également des créneaux d'urgence pour les cas non-urgents.",
      icon: <Phone className="w-5 h-5" />,
      category: "Urgences"
    }
  ];

  return (
    <section className="py-16 lg:py-20 bg-gradient-to-br from-meddoc-primary/20 via-meddoc-secondary/15 to-meddoc-fonce/10 relative overflow-hidden">
      {/* Éléments décoratifs de fond */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-1/4 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-meddoc-primary/10 rounded-full blur-3xl"></div>
        
        {/* Motif de points décoratif */}
        <div className="absolute inset-0 opacity-5 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJ3aGl0ZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSIyIiBjeT0iMiIgcj0iMiIvPjwvZz48L3N2Zz4=')]"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* En-tête amélioré */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          {/* Badge FAQ */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-meddoc-primary/10 to-meddoc-secondary/10 rounded-full border border-meddoc-primary/20 mb-6"
          >
            <HelpCircle className="w-5 h-5 text-meddoc-primary" />
            <span className="text-sm font-semibold text-meddoc-fonce">Centre d'aide</span>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl font-bold text-meddoc-fonce mb-6"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Questions fréquentes
          </motion.h2>
          
          <motion.p
            className="text-xl text-gray-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Trouvez rapidement les réponses à vos questions sur Meddoc
          </motion.p>
        </motion.div>

        {/* Grille FAQ améliorée */}
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-4">
            {faqItems.map((item, index) => (
              <motion.div
                key={index}
                className="group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
              >
                <motion.div
                  whileHover={{ scale: 1.01 }}
                  className="bg-white rounded-xl shadow-lg border border-gray-100/50 overflow-hidden hover:shadow-xl transition-all duration-300"
                >
                  {/* Question header */}
                  <div
                    onClick={() => toggleQuestion(index)}
                    className={`flex items-center justify-between p-6 cursor-pointer transition-all duration-300 ${
                      activeIndex === index
                        ? 'bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white'
                        : 'hover:bg-slate-50 text-meddoc-fonce'
                    }`}
                  >
                    <div className="flex items-center gap-4 flex-1">
                      {/* Icône de catégorie */}
                      <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${
                        activeIndex === index 
                          ? 'bg-white/20' 
                          : 'bg-gradient-to-br from-meddoc-primary/10 to-meddoc-secondary/10'
                      }`}>
                        <div className={activeIndex === index ? 'text-white' : 'text-meddoc-primary'}>
                          {item.icon}
                        </div>
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-left">{item.question}</h3>
                        <span className={`text-sm ${
                          activeIndex === index ? 'text-white/80' : 'text-gray-500'
                        }`}>
                          {item.category}
                        </span>
                      </div>
                    </div>
                    
                    {/* Chevron animé */}
                    <motion.div
                      animate={{ rotate: activeIndex === index ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                      className="flex-shrink-0 ml-4"
                    >
                      <ChevronDown className="w-5 h-5" />
                    </motion.div>
                  </div>

                  {/* Réponse animée */}
                  <AnimatePresence>
                    {activeIndex === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.4, ease: "easeInOut" }}
                        className="overflow-hidden"
                      >
                        <div className="p-6 bg-gradient-to-br from-slate-50 to-white border-t border-gray-100">
                          <motion.p
                            initial={{ y: 20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ duration: 0.3, delay: 0.1 }}
                            className="text-gray-700 leading-relaxed"
                          >
                            {item.answer}
                          </motion.p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Section contact supplémentaire */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-meddoc-fonce to-meddoc-primary rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Vous ne trouvez pas votre réponse ?</h3>
            <p className="text-white/90 mb-6">
              Notre équipe support est là pour vous aider 24h/24 et 7j/7
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center gap-2 bg-white text-meddoc-fonce font-semibold px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Phone className="w-4 h-4" />
              Contacter le support
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FAQ;
