import imagedoc from "@/assets/document.png";
import imagecal from "@/assets/calendar.png";
import meddocrdv from "@/assets/MeddocRdvLogo.png";
import { Link } from "react-router-dom";  
import { motion } from "framer-motion";
import { ArrowRight, Calendar, FileText, Shield, Stethoscope } from "lucide-react";

/**
 * Interface pour définir la structure d'une solution
 */
interface SolutionItem {
  imageUrl: string;
  title: string;
  description: string;
  button: string;
  icon: React.ReactNode;
  features: string[];
}

const solutions: SolutionItem[] = [
  {
    imageUrl: meddocrdv,
    title: "Prenez rendez-vous avec vos professionnels de santé",
    description:
      "Besoin d'une consultation ? Prenez rendez-vous avec médecin généraliste, spécialiste (dentiste, dermatologue, gynécologue, cardiologue, pédiatre, ophtalmologue...) ou professionnel paramédical (kinésithérapeute, orthophoniste, orthoptiste, ostéopathe, diététicien, podologue...). En quelques clics, trouvez un professionnel de santé proche de chez vous.",
    button: "Prendre rendez-vous",
    icon: <Stethoscope className="w-6 h-6" />,
    features: ["Recherche géolocalisée", "Disponibilités en temps réel", "Confirmation instantanée"]
  },
  {
    imageUrl: imagedoc,
    title: "Carnet de santé numérique (CSN): vos documents de santé toujours avec vous !",
    description:
      "Conservez vos ordonnances, résultats d'examen et certificats dans un environnement sécurisé. Partagez vos ordonnances, résultats d'examens ou certificats avec vos médecins.",
    button: "Accéder à mes documents",
    icon: <FileText className="w-6 h-6" />,
    features: ["Stockage sécurisé", "Partage facilité", "Accès 24h/24"]
  }
];

/**
 * Composant Solution - Affiche les solutions principales de Meddoc
 * Respecte les principes SOLID et utilise une architecture propre
 */
const Solution = () => {
  return (
    <section className="py-10 lg:py-14 bg-gradient-to-br from-slate-50 via-white to-slate-50 relative overflow-hidden">
      {/* Éléments décoratifs de fond */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-meddoc-primary/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-meddoc-secondary/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* En-tête de section compact */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-10"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-meddoc-fonce mb-3">
            Nos Solutions
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Découvrez comment Meddoc révolutionne votre accès aux soins de santé
          </p>
        </motion.div>

        {/* Liste des solutions */}
        <div className="space-y-14">
          {solutions.map((item, index) => (
            <motion.div 
              key={index}
              initial={{ opacity: 0, y: 60 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              className="relative"
            >
              <div className={`grid grid-cols-1 lg:grid-cols-2 gap-8 xl:gap-10 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}>
                
                {/* Contenu textuel */}
                <div className={`space-y-5 ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                  {/* Titre avec icône intégrée */}
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="flex items-start gap-4"
                  >
                    <div className="flex-shrink-0 w-14 h-14 bg-gradient-to-br from-meddoc-primary to-meddoc-secondary rounded-xl flex items-center justify-center text-white shadow-lg">
                      {item.icon}
                    </div>
                    <h3 className="text-2xl lg:text-3xl font-bold text-meddoc-fonce leading-tight flex-1">
                      {item.title}
                    </h3>
                  </motion.div>

                  {/* Description */}
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className="text-gray-600 leading-relaxed"
                  >
                    {item.description}
                  </motion.p>

                  {/* Liste des fonctionnalités compacte */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3"
                  >
                    {item.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-3 bg-slate-50 rounded-lg px-3 py-2">
                        <div className="w-2 h-2 bg-meddoc-primary rounded-full flex-shrink-0"></div>
                        <span className="text-gray-700 font-medium text-sm">{feature}</span>
                      </div>
                    ))}
                  </motion.div>

                  {/* Bouton d'action compact */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.5 }}
                  >
                    <Link to="/auth/login">
                      <motion.button 
                        whileHover={{ 
                          scale: 1.02,
                          boxShadow: "0 15px 30px -5px rgba(39, 170, 225, 0.4)"
                        }}
                        whileTap={{ scale: 0.98 }}
                        className="group inline-flex items-center gap-2 bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white font-semibold px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                      >
                        <span>{item.button}</span>
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </motion.button>
                    </Link>
                  </motion.div>
                </div>

                {/* Image compacte */}
                <motion.div 
                  className={`relative ${index % 2 === 1 ? 'lg:col-start-1' : ''}`}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ 
                    duration: 0.6, 
                    delay: 0.2,
                    type: "spring",
                    stiffness: 120
                  }}
                >
                  {/* Container de l'image compact */}
                  <div className="relative bg-white rounded-xl shadow-xl p-4 border border-gray-100/50 hover:shadow-2xl transition-shadow duration-300">
                    <img 
                      src={item.imageUrl} 
                      alt={`Illustration ${item.title}`}
                      className="w-full h-auto max-w-sm mx-auto rounded-lg"
                    />
                    
                    {/* Effet de brillance subtil */}
                    <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent rounded-xl pointer-events-none"></div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Solution;
