import { Lock, Shield, Eye, Database, CheckCircle, Zap } from "lucide-react";
import { motion } from "framer-motion";

/**
 * Interface pour définir une fonctionnalité de sécurité
 */
interface SecurityFeature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const securityFeatures: SecurityFeature[] = [
  {
    icon: <Shield className="w-6 h-6" />,
    title: "Chiffrement de bout en bout",
    description: "Toutes vos données sont chiffrées avec les standards les plus élevés"
  },
  {
    icon: <Eye className="w-6 h-6" />,
    title: "Conformité RGPD",
    description: "Respect total de la réglementation européenne sur la protection des données"
  },
  {
    icon: <Database className="w-6 h-6" />,
    title: "Hébergement sécurisé",
    description: "Serveurs certifiés HDS (Hébergeur de Données de Santé) en France"
  }
];

/**
 * Composant Security - Affiche les garanties de sécurité et confidentialité
 * Respecte les principes SOLID et utilise une architecture propre
 */
const Security = () => {
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-meddoc-fonce via-meddoc-fonce/95 to-meddoc-primary relative overflow-hidden">
      {/* Éléments décoratifs de fond */}
      <div className="absolute inset-0 z-0">
        {/* Motif de points */}
        <div className="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJ3aGl0ZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSIyIiBjeT0iMiIgcj0iMiIvPjwvZz48L3N2Zz4=')]"></div>
        
        {/* Cercles lumineux */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-meddoc-secondary/20 rounded-full blur-3xl"></div>
        
        {/* Lignes décoratives */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-meddoc-secondary/50 to-transparent"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 xl:gap-16 items-center">
            
            {/* Section principale avec icône et texte */}
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              {/* Badge de sécurité */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="inline-flex items-center gap-3 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20"
              >
                <Lock className="w-5 h-5 text-white" />
                <span className="text-sm font-semibold text-white">
                  Sécurité Maximale
                </span>
              </motion.div>

              {/* Titre principal */}
              <motion.h2
                className="text-4xl lg:text-5xl font-bold text-white leading-tight"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                Sécurité & confidentialité{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-white to-meddoc-secondary">
                  garanties
                </span>
              </motion.h2>

              {/* Description */}
              <motion.p
                className="text-xl text-white/90 leading-relaxed"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                Vos données sont en sécurité avec Meddoc ! Nous mettons un point d'honneur à garantir 
                la confidentialité de vos informations médicales avec les plus hauts standards de sécurité.
              </motion.p>

              {/* Statistiques de confiance */}
              <motion.div
                className="grid grid-cols-2 gap-6"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">100%</div>
                  <div className="text-white/80 text-sm">Conformité RGPD</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">24/7</div>
                  <div className="text-white/80 text-sm">Surveillance</div>
                </div>
              </motion.div>
            </motion.div>

            {/* Section des fonctionnalités de sécurité */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {/* Icône centrale avec animation */}
              <motion.div
                className="flex justify-center mb-8"
                initial={{ opacity: 0, scale: 0.5, rotate: -180 }}
                whileInView={{ opacity: 1, scale: 1, rotate: 0 }}
                viewport={{ once: true }}
                transition={{ 
                  duration: 1,
                  delay: 0.3,
                  type: "spring",
                  stiffness: 100
                }}
              >
                <div className="relative">
                  {/* Cercles concentriques animés */}
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="absolute inset-0 w-32 h-32 border-2 border-dashed border-white/30 rounded-full"
                  ></motion.div>
                  <motion.div
                    animate={{ rotate: -360 }}
                    transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                    className="absolute inset-2 w-28 h-28 border border-white/20 rounded-full"
                  ></motion.div>
                  
                  {/* Icône centrale */}
                  <div className="relative w-32 h-32 bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                    <motion.div
                      animate={{ 
                        scale: [1, 1.1, 1],
                        rotate: [0, 5, -5, 0]
                      }}
                      transition={{ 
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Shield className="w-16 h-16 text-white" />
                    </motion.div>
                  </div>
                </div>
              </motion.div>

              {/* Liste des fonctionnalités */}
              <div className="space-y-4">
                {securityFeatures.map((feature, index) => (
                  <motion.div
                    key={index}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center text-white">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-white mb-2">
                          {feature.title}
                        </h3>
                        <p className="text-white/80 text-sm leading-relaxed">
                          {feature.description}
                        </p>
                      </div>
                      <CheckCircle className="w-5 h-5 text-meddoc-secondary flex-shrink-0" />
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Badge de certification */}
              <motion.div
                className="flex items-center justify-center gap-3 bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.8 }}
              >
                <Zap className="w-6 h-6 text-meddoc-secondary" />
                <span className="text-white font-semibold">
                  Certifié HDS - Hébergeur de Données de Santé
                </span>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Security;
