import React from "react";
import { Settings2, MessageS<PERSON>reDot, CalendarCheck2, Clock } from "lucide-react";
import {
  Card,
  Typography,
  CardContent,
  Box,
  styled,
} from '@mui/material';
import { motion } from "framer-motion";

// Styles améliorés pour les cartes
const StyledCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  border: 'none',
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.08)',
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease-in-out',
  overflow: 'hidden',
  height: '100%',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 15px 35px rgba(0, 0, 0, 0.12)',
  }
}));

// Style amélioré pour l'en-tête des cartes
const HeaderBox = styled(Box)<{ type: 'upcoming' | 'cancelled' }>(({ theme, type }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(3, 2),
  color: 'white',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '-50%',
    left: '-50%',
    width: '200%',
    height: '200%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    transform: 'rotate(45deg)',
    zIndex: 1,
  }
}));

// Style pour le titre des cartes
const FeatureTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 540,
  fontSize: '1.1rem',
  marginBottom: theme.spacing(1),
  color: '#0b294b', // Couleur meddoc-fonce
}));

const features = [
  {
    icon: <CalendarCheck2 size={48} />,
    title: "Trouvez facilement un médecin, dentiste, pédiatre, gynécologue, etc.",
    description: "",
    gradient: "from-meddoc-primary to-meddoc-secondary"
  },
  {
    icon: <Clock size={48} />,
    title: "Prenez rendez-vous où que vous soyez, 24h/24 et 7j/7",
    description: "",
    gradient: "from-meddoc-secondary to-[#027f3b]"
  },
  {
    icon: <MessageSquareDot size={48} />,
    title: "Recevez des rappels automatiques par SMS",
    description: "",
    gradient: "from-[#027f3b] to-meddoc-primary"
  },
  {
    icon: <Settings2 size={48} />,
    title: "Centraliser toutes vos informations de santé et celles de vos proches",
    description: "",
    gradient: "from-meddoc-fonce to-meddoc-primary"
  },
];

const Features = () => {
  return (
    <div className="pb-12 px-4 mt-8 relative z-9">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
        className="text-center mb-10"
      >
        
      </motion.div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        {features.map((feature, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <StyledCard>
              <HeaderBox
                type={"upcoming"}
                className={`bg-gradient-to-r ${feature.gradient}`}
              >
                {feature.icon}
              </HeaderBox>
              <CardContent className="p-6">
                <FeatureTitle variant="h6">
                  {feature.title}
                </FeatureTitle>
                <Typography color="textSecondary" variant="body2" style={{ fontSize: '16px'  }} sx={{ fontWeight: 'semi-bold' }}>
                  {feature.description}
                </Typography>
              </CardContent>
            </StyledCard>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default Features;
