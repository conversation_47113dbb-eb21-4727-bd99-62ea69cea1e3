import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import {
  Calendar,
  FileText,
  BarChart2,
  Globe,
  Clock,
  Award,
  Settings,
  Lightbulb,
  MessagesSquare,
  Users,
  ShieldCheck,
  PackageSearch,
  HeartPulse
} from "lucide-react";
import doctorImg from "@/assets/formations.jpg"; // Remplacez par le chemin réel de votre image

// Variants pour les animations Framer Motion
const cardVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12,
      delay: 0.1 + i * 0.1,
    }
  }),
  hover: {
    y: -5,
    scale: 1.03,
    boxShadow: "0 20px 30px -10px rgba(0, 0, 0, 0.1)",
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  },
  tap: {
    scale: 0.98,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
};

const iconContainerVariants = {
  initial: {
    background: "linear-gradient(to bottom right, rgba(0, 150, 136, 0.1), rgba(0, 191, 165, 0.1))"
  },
  hover: {
    background: "linear-gradient(to bottom right, rgba(0, 150, 136, 0.2), rgba(0, 191, 165, 0.2))",
    scale: 1.05,
    transition: { duration: 0.3 }
  }
};

const iconVariants = {
  initial: { color: "#009688", rotate: 0 },
  hover: {
    color: "#00bfa5",
    rotate: 5,
    transition: { duration: 0.2, type: "spring", stiffness: 200 }
  }
};

// Composant pour les cartes d'avantages avec animations avancées
const AdvantageCard = ({
  text,
  index,
  icon: Icon
}: {
  text: string;
  index: number;
  icon: React.ElementType;
}) => {
  return (
    <motion.div
      custom={index}
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      whileTap="tap"
      className="group relative bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden"
    >
      {/* Bordure supérieure avec dégradé et animation */}
      <motion.div
        className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-meddoc-primary to-meddoc-secondary"
        initial={{ width: "30%" }}
        animate={{ width: "100%" }}
        transition={{ duration: 0.8, delay: 0.3 + index * 0.1, ease: "easeOut" }}
      />

      <div className="p-5 flex items-start">
        {/* Icône avec cercle et effet de transition */}
        <div className="flex-shrink-0 mr-4">
          <motion.div
            variants={iconContainerVariants}
            className="w-12 h-12 rounded-full bg-gradient-to-br from-meddoc-primary/10 to-meddoc-secondary/10 flex items-center justify-center"
          >
            <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center shadow-sm">
              <motion.div variants={iconVariants}>
                <Icon className="h-5 w-5" strokeWidth={2} />
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Texte avec style amélioré et animation */}
        <div className="flex-1">
          <motion.p
            className="text-gray-700 font-medium leading-snug"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.2 + index * 0.1 }}
          >
            {text}
          </motion.p>
        </div>
      </div>

      {/* Effet de lueur au survol avec animation */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-meddoc-primary/5 to-meddoc-secondary/5 pointer-events-none"
        initial={{ opacity: 0 }}
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  );
};

const AppMeddoc = () => {
  const advantages = [
    "Gérez facilement les rendez-vous (avec rappels automatiques par SMS)",
    "Optimisez vos consultations et la prise en charge des patients",
    "Accédez à un dossier patient numérique sécurisé",
    "Suivez votre trésorerie en temps réel",
    "Améliorez la coordination de votre équipe",
    "Suivez vos stocks de médicaments et consommables",
    "Renforcez votre visibilité en ligne auprès de nouveaux patients",
    "Offrez une expérience moderne à vos patients",
    "Réduisez les absences et les annulations de dernière minute",
    "Gagnez du temps, réduisez le stress, et concentrez-vous sur l'essentiel : vos patients"
  ];

  const icons = [
    Calendar,
    FileText,
    ShieldCheck,
    BarChart2,
    Users,
    PackageSearch,
    Globe,
    Award,
    Clock,
    HeartPulse
  ];

  // Référence pour l'animation de défilement
  const sectionRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  // Observer pour déclencher les animations au défilement
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const currentRef = sectionRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  return (
    <section className="py-10 flex justify-center items-center bg-transparent">
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7, type: "spring" }}
        viewport={{ once: true }}
        className="w-full max-w-7xl bg-[#002c54] rounded-xl flex flex-col md:flex-row items-center p-8 md:p-12 shadow-lg gap-8"
      >
        {/* Image illustrative à gauche */}
        <div className="relative md:w-1/2 flex justify-center items-center">
          {/* Forme décorative bleue */}
          <div className="absolute -left-8 -top-8 w-48 h-48 bg-blue-600/60 rounded-full z-0" style={{clipPath: 'ellipse(60% 40% at 50% 50%)'}} />
          {/* Image */}
          <img
            src={doctorImg}
            alt="Soignant et patient"
            className="relative z-10 w-64 h-64 object-cover rounded-[40%_60%_60%_40%/50%_40%_60%_50%] border-4 border-white shadow-md"
          />
        </div>
        {/* Texte et avantages */}
        <div className="md:w-1/2 flex flex-col justify-center items-start text-white">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Vous êtes professionnel de santé à Madagascar ?</h2>
          <p className="font-semibold mb-4">Simplifiez la gestion de votre activité avec MEDDoC, votre solution complète pour la gestion médico-administrative:</p>
          <ul className="space-y-2 mb-6">
            {advantages.map((adv, i) => {
              const Icon = icons[i] || Award;
              return (
                <li key={i} className="flex items-center gap-2 text-base">
                  {i === advantages.length - 1 ? (
                    <Icon className="w-7 h-7 text-meddoc-primary" />
                  ) : (
                    <Icon className="w-5 h-5 text-meddoc-primary" />
                  )}
                  <span>{adv}</span>
                </li>
              );
            })}
          </ul>
         <motion.a
            href="#solutions"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.97 }}
            className="inline-block bg-meddoc-primary text-white font-bold px-6 py-3 rounded-lg shadow hover:bg-meddoc-secondary transition-colors"
          >
            EN SAVOIR PLUS SUR NOS SOLUTIONS
          </motion.a>
        </div>
      </motion.div>
    </section>
  );
};

export default AppMeddoc;
