import { FC, memo } from 'react';
import { Calendar, Clock, User } from 'lucide-react';

import { Appointment, AppointmentStatus } from '@/presentation/types/admin.types';
import { STATUS_COLORS, STATUS_LABELS } from '@/presentation/constants/admin.constants';

interface AppointmentsListProps {
  appointments: Appointment[];
  onStatusChange?: (id: string, status: AppointmentStatus) => void;
}

const AppointmentsList: FC<AppointmentsListProps> = memo(({ appointments, onStatusChange }) => {
  return (
    <div className="rounded-lg shadow-md">
      <div className="p-6">
        <h3 className="text-lg font-semibold mb-4">Rendez-vous récents</h3>
        <div className="space-y-4">
          {appointments.map((appointment) => (
            <div
              key={appointment.id}
              className="border-b border-gray-200 last:border-0 pb-4 last:pb-0"
            >
              <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-3 sm:space-y-0">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <User className="h-10 w-10 text-gray-400" />
                  </div>
                  <div className="min-w-0">
                    <p className="font-medium truncate">{appointment.patientName}</p>
                    <div className="flex flex-wrap items-center text-sm text-gray-500 mt-1 gap-2">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1 flex-shrink-0" />
                        <span>{appointment.date}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 flex-shrink-0" />
                        <span>{appointment.time}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between sm:justify-end w-full sm:w-auto gap-2">
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap ${STATUS_COLORS[appointment.status]}`}
                  >
                    {STATUS_LABELS[appointment.status]}
                  </span>
                  {onStatusChange && (
                    <select
                      className="text-sm border rounded-md py-1 px-2 max-w-[120px]"
                      value={appointment.status}
                      onChange={(e) =>
                        onStatusChange(
                          appointment.id,
                          e.target.value as Appointment['status']
                        )
                      }
                    >
                      <option value="pending">En attente</option>
                      <option value="confirmed">Confirmé</option>
                      <option value="cancelled">Annulé</option>
                    </select>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

AppointmentsList.displayName = 'AppointmentsList';

export default AppointmentsList;
