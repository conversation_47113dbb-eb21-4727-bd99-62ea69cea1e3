import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  <PERSON>alogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Typography,
  Box,
  IconButton,
  Alert,
  CircularProgress,
} from "@mui/material";
import { X, Mail, Building, Send } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import FormField from "@/presentation/components/common/ui/FormField";
import {
  DashInvitationFormData,
  createDashInvitationShema,
} from "@/shared/schemas/DashInvitationSchema";
import { PRIMARY } from "@/shared/constants/Color";

interface GenerateDashInvitationModalProps {
  open: boolean;
  onClose: () => void;
  onGenerate: (email: string, organizationName: string) => Promise<void>;
}

/**
 * Modal pour générer une nouvelle invitation dash
 * Utilise react-hook-form avec validation Zod
 */
export const GenerateDashInvitationModal: React.FC<
  GenerateDashInvitationModalProps
> = ({ open, onClose, onGenerate }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch,
  } = useForm<DashInvitationFormData>({
    resolver: zodResolver(createDashInvitationShema),
    mode: "onBlur", // Validation au blur pour de meilleures performances
    reValidateMode: "onChange", // Re-validation en temps réel après la première validation
    defaultValues: {
      email: "",
      organizationName: "",
    },
  });

  // Surveiller les valeurs pour vérifier si le formulaire est rempli
  const watchedValues = watch();
  const isFormFilled =
    watchedValues.email.trim() !== "" &&
    watchedValues.organizationName.trim() !== "";

  /**
   * Gestionnaire de soumission du formulaire
   */
  const onSubmit = async (data: DashInvitationFormData) => {
    setIsSubmitting(true);
    setSubmitError(null);
    setSubmitSuccess(false);

    try {
      await onGenerate(data.email, data.organizationName);
      setSubmitSuccess(true);

      // Fermer le modal après un délai pour montrer le message de succès
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (error) {
      setSubmitError(
        error instanceof Error
          ? error.message
          : "Une erreur est survenue lors de la génération de l'invitation"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Gestionnaire de fermeture du modal
   */
  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      setSubmitError(null);
      setSubmitSuccess(false);
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: "12px",
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 2,
          borderBottom: "1px solid #e0e0e0",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Send size={24} color={PRIMARY} />
          <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
            Générer une invitation dash
          </Typography>
        </Box>
        <IconButton onClick={handleClose} size="small" disabled={isSubmitting}>
          <X size={20} />
        </IconButton>
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent sx={{ pt: 3 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Remplissez les informations ci-dessous pour générer et envoyer une
            invitation d'accès au tableau de bord.
          </Typography>

          {/* Messages d'état */}
          {submitError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {submitError}
            </Alert>
          )}

          {submitSuccess && (
            <Alert severity="success" sx={{ mb: 3 }}>
              Invitation générée et envoyée avec succès !
            </Alert>
          )}

          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            {/* Champ Email */}
            <FormField
              id="email"
              label="Email du destinataire *"
              type="email"
              placeholder="<EMAIL>"
              icon={Mail}
              register={register}
              required={false} // Désactiver la validation automatique car on utilise Zod
              error={errors.email}
              helpText="L'invitation sera envoyée à cette adresse email"
            />

            {/* Champ Nom de l'organisme */}
            <FormField
              id="organizationName"
              label="Nom de l'organisme *"
              type="text"
              placeholder="Nom de votre organisme ou entreprise"
              icon={Building}
              register={register}
              required={false} // Désactiver la validation automatique car on utilise Zod
              error={errors.organizationName}
              helpText="Ce nom apparaîtra dans l'email d'invitation"
            />
          </Box>
        </DialogContent>

        <DialogActions
          sx={{ p: 3, pt: 2, gap: 1, borderTop: "1px solid #e0e0e0" }}
        >
          <Button
            onClick={handleClose}
            variant="outlined"
            disabled={isSubmitting}
            sx={{ borderRadius: "8px" }}
          >
            Annuler
          </Button>

          <Button
            type="submit"
            variant="contained"
            disabled={!isFormFilled || isSubmitting || submitSuccess}
            startIcon={
              isSubmitting ? (
                <CircularProgress size={16} color="inherit" />
              ) : (
                <Send size={16} />
              )
            }
            sx={{
              backgroundColor: PRIMARY,
              "&:hover": { backgroundColor: "#1976d2" },
              "&:disabled": { backgroundColor: "#ccc" },
              borderRadius: "8px",
            }}
          >
            {isSubmitting ? "Génération..." : "Générer et envoyer"}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default GenerateDashInvitationModal;
