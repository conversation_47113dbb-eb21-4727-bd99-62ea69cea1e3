import React, { useState } from "react";
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Typography,
  Grid,
  Chip,
  Box,
  IconButton,
} from "@mui/material";
import { X } from "lucide-react";
import { demande_adhesion } from "@/domain/models";
import { demande_adhesion_statut_enum } from "@/domain/models/enums";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import Button from "@/presentation/components/common/Button/Button";
import {
  ADHESION_REQUEST_STATUS_LABELS,
  ADHESION_REQUEST_STATUS_MUI_COLORS,
} from "@/presentation/constants/adhesionRequest.constants";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal";

interface AdhesionRequestDetailModalProps {
  open: boolean;
  onClose: () => void;
  data: demande_adhesion | null;
  onMarkAsRead?: (id: number) => void;
  onApprove?: (id: number) => void;
  onReject?: (id: number) => void;
  onDelete?: (id: number) => void;
}

export const AdhesionRequestDetailModal: React.FC<
  AdhesionRequestDetailModalProps
> = ({ open, onClose, data, onMarkAsRead, onApprove, onReject, onDelete }) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isApproveModalOpen, setIsApproveModalOpen] = useState(false);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  if (!data) return null;

  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    try {
      return format(new Date(dateString), "dd MMMM yyyy", { locale: fr });
    } catch (error) {
      return dateString;
    }
  };

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (onDelete) {
      setIsLoading(true);
      try {
        await onDelete(data.id);
        onClose();
      } catch (error) {
        console.error("Erreur lors de la suppression:", error);
      } finally {
        setIsLoading(false);
        setIsDeleteModalOpen(false);
      }
    }
  };

  const handleMarkAsRead = async () => {
    if (onMarkAsRead) {
      setIsLoading(true);
      try {
        await onMarkAsRead(data.id);

        onClose();
      } catch (error) {
        console.error("Erreur lors du marquage comme lu:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleApproveClick = () => {
    setIsApproveModalOpen(true);
  };

  const handleApproveConfirm = async () => {
    if (onApprove) {
      setIsLoading(true);
      try {
        await onApprove(data.id);

        onClose();
      } catch (error) {
        console.error("Erreur lors de l'approbation:", error);
      } finally {
        setIsLoading(false);
        setIsApproveModalOpen(false);
      }
    }
  };

  const handleRejectClick = () => {
    setIsRejectModalOpen(true);
  };

  const handleRejectConfirm = async () => {
    if (onReject) {
      setIsLoading(true);
      try {
        await onReject(data.id);

        onClose();
      } catch (error) {
        console.error("Erreur lors du rejet:", error);
      } finally {
        setIsLoading(false);
        setIsRejectModalOpen(false);
      }
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: "12px",
              padding: "8px",
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h5" component="div" fontWeight={600}>
            Détails de la demande d'adhésion
          </Typography>
          <IconButton onClick={onClose} size="small">
            <X size={20} />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers>
          <Box sx={{ mb: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Statut
                </Typography>
                <Chip
                  label={
                    ADHESION_REQUEST_STATUS_LABELS[
                      data.status as demande_adhesion_statut_enum
                    ] || "Inconnu"
                  }
                  color={
                    ADHESION_REQUEST_STATUS_MUI_COLORS[
                      data.status as demande_adhesion_statut_enum
                    ] || "default"
                  }
                  size="small"
                  sx={{ mt: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Date de création
                </Typography>
                <Typography variant="body1" sx={{ mt: 1 }}>
                  {formatDate(data.date_creation)}
                </Typography>
              </Grid>
            </Grid>
          </Box>

          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Informations personnelles
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Nom complet
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.prenom} {data.nom}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Email
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.email}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Téléphone
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.telephone}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Type d'établissement
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.type_etablissement}
              </Typography>
            </Grid>
          </Grid>

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Adresse du cabinet
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Adresse
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.adresse}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                District
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.district}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Commune
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.commune}
              </Typography>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ padding: 2, justifyContent: "space-between" }}>
          <Box className="flex text-center justify-between gap-2 mx-auto">
            <Box className="hidden sm:block">
              {data.status === demande_adhesion_statut_enum.EN_ATTENTE &&
                onMarkAsRead && (
                  <Button
                    className="bg-transparent text-meddoc-primary border border-meddoc-primary"
                    onClick={handleMarkAsRead}
                    color="success"
                  >
                    Marquer comme lu
                  </Button>
                )}
            </Box>

            <Box>
              {(data.status === demande_adhesion_statut_enum.EN_ATTENTE ||
                data.status === demande_adhesion_statut_enum.LU) &&
                onApprove && (
                  <Button
                    className="bg-transparent text-green-500 border border-green-500"
                    onClick={handleApproveClick}
                  >
                    Approuver
                  </Button>
                )}
            </Box>
            <Box>
              {(data.status === demande_adhesion_statut_enum.EN_ATTENTE ||
                data.status === demande_adhesion_statut_enum.LU) &&
                onReject && (
                  <Button
                    className="bg-transparent text-orange-500 border border-orange-500"
                    onClick={handleRejectClick}
                  >
                    Rejeter
                  </Button>
                )}
            </Box>
            <Box>
              {onDelete && (
                <Button
                  className="bg-transparent text-red-500 border border-red-500"
                  onClick={handleDeleteClick}
                >
                  Supprimer
                </Button>
              )}
            </Box>
          </Box>
        </DialogActions>
      </Dialog>

      {/* Modals de confirmation */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Confirmation de suppression"
        message="Êtes-vous sûr de vouloir supprimer cette demande d'adhésion ?"
        confirmButtonText="Supprimer"
        loading={isLoading}
      />

      <ConfirmationModal
        isOpen={isApproveModalOpen}
        onClose={() => setIsApproveModalOpen(false)}
        onConfirm={handleApproveConfirm}
        title="Confirmation d'approbation"
        message="Êtes-vous sûr de vouloir approuver cette demande d'adhésion ?"
        confirmButtonText="Approuver"
        loading={isLoading}
      />

      <ConfirmationModal
        isOpen={isRejectModalOpen}
        onClose={() => setIsRejectModalOpen(false)}
        onConfirm={handleRejectConfirm}
        title="Confirmation de rejet"
        message="Êtes-vous sûr de vouloir rejeter cette demande d'adhésion ?"
        confirmButtonText="Rejeter"
        loading={isLoading}
      />
    </>
  );
};

export default AdhesionRequestDetailModal;
