import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { Chip } from "@mui/material";
import { demande_adhesion } from "@/domain/models";
import { demande_adhesion_statut_enum } from "@/domain/models/enums";
import {
  ADHESION_REQUEST_STATUS_LABELS,
  ADHESION_REQUEST_STATUS_MUI_COLORS,
} from "@/presentation/constants/adhesionRequest.constants";
import AdhesionRequestActions from "./AdhesionRequestActions";

/**
 * Fonction qui retourne les colonnes pour le DataGrid des demandes d'adhésion
 * Utilise le hook useAdhesionRequestUI pour gérer les actions
 */
export const AdhesionRequestColumns = (): GridColDef[] => {
  return [
    {
      field: "nomComplet",
      headerName: "Nom complet",
      width: 200,
      renderCell: (params: GridRenderCellParams<demande_adhesion>) => (
        <span>
          {params.row.prenom} {params.row.nom}
        </span>
      ),
      headerClassName: "font-semibold",
    },
    {
      field: "email",
      headerName: "Email",
      width: 250,
      headerClassName: "font-semibold",
    },
    {
      field: "telephone",
      headerName: "Téléphone",
      width: 150,
      headerClassName: "font-semibold",
    },
    {
      field: "type_etablissement",
      headerName: "Type d'établissement",
      width: 200,
      headerClassName: "font-semibold",
    },
    {
      field: "status",
      headerName: "Statut",
      width: 120,
      renderCell: (params) => {
        const status = params.value as demande_adhesion_statut_enum;
        return (
          <Chip
            label={ADHESION_REQUEST_STATUS_LABELS[status] || "Inconnu"}
            color={ADHESION_REQUEST_STATUS_MUI_COLORS[status] || "default"}
            size="small"
          />
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      renderCell: (params: GridRenderCellParams<demande_adhesion>) => (
        <AdhesionRequestActions row={params.row} />
      ),
      headerClassName: "font-semibold",
    },
  ];
};
