import React, { useState } from "react";
import {
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import { demande_adhesion } from "@/domain/models";
import { demande_adhesion_statut_enum } from "@/domain/models/enums";
import {
  Check,
  Eye,
  MoreVertical,
  Trash2,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react";
import { useAdhesionRequestUI } from "@/presentation/hooks/adhesionRequest";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal";

const AdhesionRequestActions = ({ row }: { row: demande_adhesion }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isApproveModalOpen, setIsApproveModalOpen] = useState(false);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const {
    handleViewDetails,
    handleMarkAsRead,
    handleApprove,
    handleReject,
    handleDelete,
  } = useAdhesionRequestUI();

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const onViewDetails = () => {
    handleViewDetails(row);
    handleMenuClose();
  };

  const onMarkAsRead = async () => {
    setIsLoading(true);
    try {
      await handleMarkAsRead(row.id);
    } catch (error) {
      console.error("Erreur lors du marquage comme lu:", error);
    } finally {
      setIsLoading(false);
      handleMenuClose();
    }
  };

  const onApproveClick = () => {
    setIsApproveModalOpen(true);
  };

  const onApproveConfirm = async () => {
    setIsLoading(true);
    try {
      await handleApprove(row.id);
    } catch (error) {
      console.error("Erreur lors de l'approbation:", error);
    } finally {
      setIsLoading(false);
      setIsApproveModalOpen(false);
      handleMenuClose();
    }
  };

  const onRejectClick = () => {
    setIsRejectModalOpen(true);
  };

  const onRejectConfirm = async () => {
    setIsLoading(true);
    try {
      await handleReject(row.id);
    } catch (error) {
      console.error("Erreur lors du rejet:", error);
    } finally {
      setIsLoading(false);
      setIsRejectModalOpen(false);
      handleMenuClose();
    }
  };

  const onDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const onDeleteConfirm = async () => {
    setIsLoading(true);
    try {
      await handleDelete(row.id);
    } catch (error) {
      console.error("Erreur lors de la suppression:", error);
    } finally {
      setIsLoading(false);
      setIsDeleteModalOpen(false);
      handleMenuClose();
    }
  };

  return (
    <>
      <IconButton onClick={handleMenuOpen} size="small">
        <MoreVertical className="h-5 w-5" />
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        slotProps={{
          paper: {
            elevation: 3,
            sx: { minWidth: 200, borderRadius: "8px" },
          },
        }}
      >
        <MenuItem onClick={onViewDetails}>
          <ListItemIcon>
            <Eye size={18} />
          </ListItemIcon>
          <ListItemText>Voir les détails</ListItemText>
        </MenuItem>

        {row.status === demande_adhesion_statut_enum.EN_ATTENTE && (
          <MenuItem onClick={onMarkAsRead}>
            <ListItemIcon>
              <Check size={18} />
            </ListItemIcon>
            <ListItemText>Marquer comme lu</ListItemText>
          </MenuItem>
        )}

        {(row.status === demande_adhesion_statut_enum.EN_ATTENTE ||
          row.status === demande_adhesion_statut_enum.LU) && (
          <MenuItem onClick={onApproveClick}>
            <ListItemIcon>
              <ThumbsUp size={18} color="green" />
            </ListItemIcon>
            <ListItemText sx={{ color: "success.main" }}>
              Approuver
            </ListItemText>
          </MenuItem>
        )}

        {(row.status === demande_adhesion_statut_enum.EN_ATTENTE ||
          row.status === demande_adhesion_statut_enum.LU) && (
          <MenuItem onClick={onRejectClick}>
            <ListItemIcon>
              <ThumbsDown size={18} color="red" />
            </ListItemIcon>
            <ListItemText sx={{ color: "error.main" }}>Rejeter</ListItemText>
          </MenuItem>
        )}

        <MenuItem onClick={onDeleteClick}>
          <ListItemIcon>
            <Trash2 size={18} color="red" />
          </ListItemIcon>
          <ListItemText sx={{ color: "error.main" }}>Supprimer</ListItemText>
        </MenuItem>
      </Menu>

      {/* Modals de confirmation */}
      <ConfirmationModal
        isOpen={isApproveModalOpen}
        onClose={() => setIsApproveModalOpen(false)}
        onConfirm={onApproveConfirm}
        title="Confirmation d'approbation"
        message="Êtes-vous sûr de vouloir approuver cette demande d'adhésion ?"
        confirmButtonText="Approuver"
        loading={isLoading}
      />

      <ConfirmationModal
        isOpen={isRejectModalOpen}
        onClose={() => setIsRejectModalOpen(false)}
        onConfirm={onRejectConfirm}
        title="Confirmation de rejet"
        message="Êtes-vous sûr de vouloir rejeter cette demande d'adhésion ?"
        confirmButtonText="Rejeter"
        loading={isLoading}
      />

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={onDeleteConfirm}
        title="Confirmation de suppression"
        message="Êtes-vous sûr de vouloir supprimer cette demande d'adhésion ?"
        confirmButtonText="Supprimer"
        loading={isLoading}
      />
    </>
  );
};

export default AdhesionRequestActions;
