import { FC, memo } from 'react';
import {
  <PERSON>C<PERSON>,
  Bar,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

import { AppointmentData } from '@/presentation/types/admin.types';
import { CHART_MARGIN } from '@/presentation/constants/admin.constants';

interface AppointmentsChartProps {
  data: AppointmentData[];
}

const AppointmentsChart: FC<AppointmentsChartProps> = memo(({ data }) => {
  return (
    <div className=" p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Aperçu des rendez-vous</h3>
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={CHART_MARGIN}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="date"
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${value}`}
            />
            <Tooltip />
            <Bar
              dataKey="appointments"
              fill="#4F46E5"
              radius={[4, 4, 0, 0]}
              maxBarSize={50}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
});

AppointmentsChart.displayName = 'AppointmentsChart';

export default AppointmentsChart;
