import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { laboratoire_diagnostics } from "@/domain/models";
import { useAppSelector } from "@/presentation/hooks/redux";
import { utilisateurs_role_enum } from "@/domain/models/enums";

export const LaboratoireDiagnosticsColumns = (
  onViewLaboratoire?: (laboratoire: laboratoire_diagnostics) => void
): GridColDef[] => {
  const role = useAppSelector((state) => state.authentification.user?.role);

  const columns: GridColDef[] = [
    {
      field: "date",
      headerName: "Date",
      flex: 1.2,
      renderCell: (params: GridRenderCellParams<laboratoire_diagnostics>) => {
        if (!params.row.date) return "";

        const date = new Date(params.row.date);

        const dateOptions: Intl.DateTimeFormatOptions = {
          year: "numeric",
          month: "short",
          day: "numeric",
        };

        const timeOptions: Intl.DateTimeFormatOptions = {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        };

        const dateStr = date.toLocaleDateString("fr-FR", dateOptions);
        const timeStr = date.toLocaleTimeString("fr-FR", timeOptions);

        return (
          <div className="text-gray-700">
            <div className="text-sm font-medium">{dateStr}</div>
            <div className="text-xs text-gray-500">{timeStr}</div>
          </div>
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "titre",
      headerName: "Titre de l'examen",
      flex: 1.5,
      renderCell: (params: GridRenderCellParams<laboratoire_diagnostics>) => {
        if (!params.row.titre || params.row.titre.trim() === "") return "";

        return (
          <div className="flex items-center gap-2">
            <span className="text-gray-800 font-medium capitalize">
              {params.row.titre}
            </span>
          </div>
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "type_fichier",
      headerName: "Type de fichier",
      flex: 1,
      renderCell: (params: GridRenderCellParams<laboratoire_diagnostics>) => {
        if (!params.row.type_fichier || params.row.type_fichier.trim() === "")
          return "";

        const getFileTypeColor = (type: string) => {
          switch (type.toLowerCase()) {
            case "pdf":
              return "bg-red-100 text-red-800";
            case "jpg":
            case "jpeg":
            case "png":
              return "bg-green-100 text-green-800";
            case "doc":
            case "docx":
              return "bg-blue-100 text-blue-800";
            default:
              return "bg-gray-100 text-gray-800";
          }
        };

        return (
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${getFileTypeColor(
              params.row.type_fichier
            )}`}
          >
            {params.row.type_fichier.toUpperCase()}
          </span>
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "resultat",
      headerName: "Résultat",
      flex: 2,
      renderCell: (params: GridRenderCellParams<laboratoire_diagnostics>) => {
        if (!params.row.resultat || params.row.resultat.trim() === "")
          return "";

        const resultat = params.row.resultat;
        const displayText =
          resultat.length > 60
            ? `${resultat.substring(0, 60)}...`
            : resultat;

        let statusColor = "text-gray-800";
        const lowerResultat = resultat.toLowerCase();

        if (
          lowerResultat.includes("normal") ||
          lowerResultat.includes("négatif")
        ) {
          statusColor = "text-green-600";
        } else if (
          lowerResultat.includes("anormal") ||
          lowerResultat.includes("positif") ||
          lowerResultat.includes("élevé")
        ) {
          statusColor = "text-red-600";
        } else if (
          lowerResultat.includes("limite") ||
          lowerResultat.includes("borderline")
        ) {
          statusColor = "text-orange-600";
        }

        return (
          <div className={`max-w-xs ${statusColor}`}>
            <span title={resultat} className="cursor-help font-medium">
              {displayText}
            </span>
          </div>
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "remarque",
      headerName: "Remarques",
      flex: 2,
      renderCell: (params: GridRenderCellParams<laboratoire_diagnostics>) => {
        if (!params.row.remarque || params.row.remarque.trim() === "") return "";

        const remarque = params.row.remarque;
        const displayText =
          remarque.length > 50
            ? `${remarque.substring(0, 50)}...`
            : remarque;

        return (
          <div className="text-gray-600 max-w-xs">
            <span title={remarque} className="cursor-help italic">
              {displayText}
            </span>
          </div>
        );
      },
      headerClassName: "font-semibold",
    },
  ];

  // ✅ On ajoute la colonne "Actions" uniquement si role = PROFESSIONNEL
  if (role === utilisateurs_role_enum.PROFESSIONNEL) {
    columns.push({
      field: "actions",
      headerName: "Actions",
      flex: 1.3,
      renderCell: (params: GridRenderCellParams<laboratoire_diagnostics>) => {
        return (
          <div className="flex gap-1 justify-center items-center h-full min-h-[40px]">
            <button
              className="bg-blue-500 text-white px-2 py-1 text-xs rounded hover:bg-blue-600 transition-colors flex items-center gap-1"
              onClick={() => {
                if (onViewLaboratoire) {
                  onViewLaboratoire(params.row);
                }
              }}
            >
              Voir
            </button>
          </div>
        );
      },
      headerClassName: "font-semibold",
      sortable: false,
    });
  }

  return columns;
};
