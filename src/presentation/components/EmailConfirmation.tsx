import Button from "./common/Button/Button";
import useLogin from "../hooks/use-login";
import UnathenticatedLayout from "./layouts/UnauthenticatedLayout";
import { useParams } from "react-router-dom";
import useRegister from "../hooks/use-register";

const EmailConfirmation = () => { 
  const { logout } = useLogin();
  const { email } = useParams();
  const { resendValidationEmail } = useRegister();

  const handleResendEmail = () => {
    resendValidationEmail(email);
  };

  return (
    <UnathenticatedLayout>
      <div className="mx-auto max-w-md space-y-6 p-6">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold text-meddoc-fonce">Vérification de votre email</h1>
          <p className="text-gray-500">
            Pour continuer, veuillez vérifier votre adresse email. Nous vous
            avons envoyé un lien de confirmation.
          </p>
        </div>

        <div className="space-y-4">
          <Button onClick={() => handleResendEmail()} className="w-full bg-gradient-to-r from-meddoc-primary to-meddoc-secondary">
            Renvoyer l'email de confirmation
          </Button>
          <Button className="w-full bg-gray-400" onClick={() => logout()}>
            Se déconnecter
          </Button>
        </div>

        <div className="text-center text-sm">
          <p className="text-gray-500">
            Si vous n'avez pas reçu l'email, vérifiez votre dossier spam.
          </p>
        </div>
      </div>
    </UnathenticatedLayout>
  );
};

export default EmailConfirmation;
