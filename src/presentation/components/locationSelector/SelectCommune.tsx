import React, { useEffect, useState } from "react";
import { TextField, MenuItem } from "@mui/material";
import { Commune } from "@/domain/models";
import LoadingSpinner from "../common/LoadingSpinner";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { FormField } from "../professional/signupProfessional/sections/components";
import { MapPin } from "lucide-react";

interface SelectCommuneProps<T> {
  value?: Commune | null;
  allNeeded?: boolean;
  isDisabled?: boolean;
  errors: FieldErrors<T>;
  onChange: (value: Commune) => void;
  register: UseFormRegister<T>;
  commune_id?: string;
}

const SelectCommune = <T,>({
  value,
  allNeeded,
  isDisabled,
  errors,
  onChange,
  register,
  commune_id,
}: SelectCommuneProps<T>) => {
  const [isCommuneLoading, setIsCommuneLoading] = useState<boolean>(true);
  const [currentCommune, setCurrentCommune] = useState<Commune>(value);
  const {
    loading,
    communes,
    selectedCommune,
    getCommuneById,
    getFilteredCommunes,
    handleCommuneChange,
  } = useLocationSelector();

  const handleSelectCommune = (communeId: string) => {
    const selectedCommune = communes.find((c) => c.id === Number(communeId));
    if (selectedCommune) {
      handleCommuneChange(selectedCommune);
      onChange(selectedCommune);
    }
  };

  useEffect(() => {
    if (commune_id && isCommuneLoading) {
      getCommuneById(Number(commune_id));
    }
  }, [commune_id, isCommuneLoading === true]);

  useEffect(() => {
    if (selectedCommune && isCommuneLoading) {
      getFilteredCommunes(Number(selectedCommune.id_district));
      setCurrentCommune(selectedCommune);
      setIsCommuneLoading(false);
    }
  }, [selectedCommune, isCommuneLoading === true]);

  if (loading && isCommuneLoading && commune_id) {
    return;
  }

  return (
    <FormField
      id="commune"
      icon={MapPin}
      label="Commune"
      type="select"
      placeholder="Selectionnez une commune"
      register={register}
      value={String(currentCommune?.id) || ""}
      onChange={(e) => handleSelectCommune(e.target.value)}
      error={(errors as any).commune}
      options={
        communes && communes.length > 0
          ? communes.map((commune) => ({
              value: String(commune.id),
              label: commune.nom,
            }))
          : [
              {
                value: "",
                label: "Veuiller séléctionner une district",
              },
            ]
      }
    />
  );
};

export default SelectCommune;
