import React, { useEffect, useState } from "react";
import { TextField, MenuItem } from "@mui/material";
import { Province } from "@/domain/models";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import LoadingSpinner from "../common/LoadingSpinner";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import Form<PERSON>ield from "../common/ui/FormField";
import { MapPin } from "lucide-react";

interface SelectProvinceProps<T> {
  value?: Province | null;
  onChange: (value: Province) => void;
  register: UseFormRegister<T>;
  errors: FieldErrors<T>;
  province_id: string;
}

const SelectProvince = <T,>({
  onChange,
  value,
  register,
  errors,
  province_id,
}: SelectProvinceProps<T>) => {
  const [isProvinceLoading, setIsProvinceLoading] = useState<boolean>(true);
  const { provinces, loading, getProvinceById } = useLocationSelector();
  const handleSelectProvince = async (provinceId: string) => {
    const selectedProvince = provinces.find((p) => p.id === Number(provinceId));
    if (selectedProvince) {
      await onChange(selectedProvince);
    }
  };

  useEffect(() => {
    if (province_id && isProvinceLoading) {
      getProvinceById(Number(province_id));
      setIsProvinceLoading(false);
    }
  }, [province_id, isProvinceLoading === true]);

  if (loading && isProvinceLoading && province_id) {
    return;
  }

  return (
    <FormField
      id="province"
      icon={MapPin}
      label="Province"
      type="select"
      placeholder="Selectionner une province"
      register={register}
      value={String(value?.id) || ""}
      error={(errors as any).province}
      onChange={(e) => handleSelectProvince(e.target.value)}
      options={
        provinces && provinces.length > 0
          ? provinces.map((province) => ({
              value: String(province.id),
              label: province.nom,
            }))
          : [
              {
                value: "",
                label: "Veiller séléctionner une district",
              },
            ]
      }
    />
  );
};

export default SelectProvince;
