import { useEffect, useState } from "react";
import { District } from "@/domain/models";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { FormField } from "../professional/signupProfessional/sections/components";
import { MapPin } from "lucide-react";

interface SelectDistrictProps<T> {
  value?: District | null;
  onChange: (value: District) => void;
  allNeeded?: boolean;
  isDisabled?: boolean;
  register: UseFormRegister<T>;
  errors: FieldErrors<T>;
  district_id?: string;
}

const SelectDistrict = <T,>({
  value,
  onChange,
  allNeeded,
  isDisabled,
  register,
  errors,
  district_id,
}: SelectDistrictProps<T>) => {
  const [isDistrictLoading, setIsDistrictLoading] = useState<boolean>(true);
  const [currentDistrict, setCurrentDistrict] = useState<District>(value);
  const {
    loading,
    districts,
    selectedDistrict,
    getDistricts,
    getFilteredDistricts,
    getDistrictById,
    handleDistrictChange,
  } = useLocationSelector();

  useEffect(() => {
    if (allNeeded) {
      getDistricts();
    }
  }, [allNeeded]);

  const handleSelectDistrict = (districtId: string) => {
    const selectedDistrict = districts.find((d) => d.id === Number(districtId));
    if (selectedDistrict) {
      handleDistrictChange(selectedDistrict);
      onChange(selectedDistrict);
    }
  };

  useEffect(() => {
    if (district_id && isDistrictLoading) {
      getDistrictById(Number(district_id));
    }
  }, [district_id, isDistrictLoading === true]);

  useEffect(() => {
    if (selectedDistrict && isDistrictLoading) {
      getFilteredDistricts(Number(selectedDistrict.id_region));
      setCurrentDistrict(selectedDistrict);
      setIsDistrictLoading(false);
    }
  }, [selectedDistrict, isDistrictLoading === true]);

  if (loading && isDistrictLoading && district_id) {
    return;
  }

  return (
    <FormField
      id="district"
      icon={MapPin}
      label="District"
      type="select"
      placeholder="Selectionnez une district"
      register={register}
      value={String(currentDistrict?.id) || ""}
      onChange={(e) => handleSelectDistrict(e.target.value)}
      error={(errors as any).district}
      options={
        districts && districts.length > 0
          ? districts.map((district) => ({
              value: String(district.id),
              label: district.libelle,
            }))
          : [
              {
                value: "",
                label: "Veuiller séléctionner une région",
              },
            ]
      }
    />
  );
};

export default SelectDistrict;
