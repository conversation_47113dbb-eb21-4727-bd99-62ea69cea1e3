import { useEffect, useState } from "react";
import { Patient, Region } from "@/domain/models";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import FormField from "../common/ui/FormField";
import { MapPin } from "lucide-react";
import { FieldErrors, UseFormRegister } from "react-hook-form";

interface SelectRegionProps<T> {
  value?: Region | null;
  onChange: (value: Region) => void;
  allNeeded?: boolean;
  isDisabled?: boolean;
  register: UseFormRegister<T>;
  errors: FieldErrors<T>;
  region_id: string;
}

const SelectRegion = <T,>({
  value,
  onChange,
  allNeeded,
  register,
  errors,
  region_id,
}: SelectRegionProps<T>) => {
  const [isRegionLoading, setIsRegionLoading] = useState<boolean>(true);
  const {
    loading,
    regions,
    selectedRegion,
    getRegions,
    handleRegionChange,
    getFilteredRegions,
    getRegionById,
  } = useLocationSelector();

  const handleSelectRegion = (regionId: string) => {
    const selectedRegion = regions.find((r) => r.id === Number(regionId));
    if (selectedRegion) {
      handleRegionChange(selectedRegion);
      onChange(selectedRegion);
    }
  };

  useEffect(() => {
    if (allNeeded) {
      getRegions();
    }
  }, [allNeeded]);

  useEffect(() => {
    if (region_id && isRegionLoading) {
      getRegionById(Number(region_id));
    }
  }, [region_id, isRegionLoading === true]);

  useEffect(() => {
    if (selectedRegion && isRegionLoading) {
      getFilteredRegions(Number(selectedRegion.id_province));
      setIsRegionLoading(false);
    }
  }, [selectedRegion, isRegionLoading === true]);

  if (loading && isRegionLoading && region_id) {
    return;
  }

  return (
    <FormField
      id="region"
      icon={MapPin}
      label="Region"
      type="select"
      placeholder="Selectionner une region"
      register={register}
      value={String(value?.id) || ""}
      error={(errors as any).region}
      onChange={(e) => handleSelectRegion(e.target.value)}
      options={
        regions && regions.length > 0
          ? regions.map((region) => ({
              value: String(region.id),
              label: region.nom,
            }))
          : [
              {
                value: "",
                label: "Veuiller séléctionner une province",
              },
            ]
      }
    />
  );
};

export default SelectRegion;
