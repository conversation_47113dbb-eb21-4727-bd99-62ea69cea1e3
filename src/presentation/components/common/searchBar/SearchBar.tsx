import { Search } from "lucide-react";
import { ComponentProps, useState, useRef, useEffect } from "react";
import { twMerge } from "tailwind-merge";
import { LocationSearchBar } from "./LocationSearchBar";
import { SpecialitiesSearchBar } from "./SpecialitiesSearchBar";
import Button from "../Button/Button";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import useSearchNavigation from "@/presentation/hooks/useSearchNavigation";

import { Box, Paper } from "@mui/material";
import SpecialitiesFilter from "./filters/SpecialitiesFilter";
import DoctorsFilter from "./filters/DoctorsFilter";
import EtablishmentsFilter from "./filters/EtablishmentsFilter";

const SearchBar = ({ className, ...props }: ComponentProps<"div">) => {
  const [isOpen, setIsOpen] = useState(false);
  const searchBarRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchBarRef.current &&
        !searchBarRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const {
    searchSpecialityKey,
    setSearchSpecialityKey,
    searchLocationKey,
    setSearchLocationKey,
  } = useSearchProfessional();

  const { handleSearch, navigateToProfessionalProfile } = useSearchNavigation();

  // Fonction simplifiée qui utilise le hook de navigation
  const handleSearchClick = async () => {
    await handleSearch(searchLocationKey, searchSpecialityKey);
  };

  const handleSelectSearchTerm = (searchTerm: string) => {
    setSearchSpecialityKey(searchTerm);
    setIsOpen(false);
  };

  const handleSubmitForm = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleSearchClick();
  };

  return (
    <div
      ref={searchBarRef}
      className={twMerge(
        "mx-auto space-y-4 border border-meddoc-secondary rounded-md z-999",
        className
      )}
      {...props}
    >
      {/* Search Inputs */}
      <form
        onSubmit={(e) => handleSubmitForm(e)}
        className="flex flex-col md:flex-row items-center gap-4 bg-white relative w-full rounded-md "
      >
        <div className="relative flex-1 w-full md:w-auto max-h-[300px]">
          <SpecialitiesSearchBar
            value={searchSpecialityKey || ""}
            setValue={setSearchSpecialityKey}
            isCompletionOpen={isOpen}
            setIsCompletionOpen={setIsOpen}
          />
        </div>
        {/* <div className="hidden md:flex w-1 h-[50%] bg-meddoc-secondary">

        </div> */}
        <div className="relative flex-1 w-full md:w-auto">
          <LocationSearchBar
            inputValue={searchLocationKey || ""}
            setInputValue={setSearchLocationKey}
          />
        </div>

        <Button
          onClick={handleSearchClick}
          className="w-full md:w-auto md:m-1 bg-gradient-to-r  from-meddoc-primary to-meddoc-secondary hover:from-meddoc-fonce/90 hover:to-meddoc-primary/90 text-white font-semibold p-2  rounded-md shadow-md transition-all duration-200"
        >
          <Search className="m-2 h-5 w-5" /> Rechercher
        </Button>
        {/* Autocomplete */}
        {isOpen && (
          <Paper className="absolute mt-4 rounded-md overflow-hidden w-full top-12 z-50">
            <Paper className="p-3">
              <Box className="flex flex-col md:flex-row gap-2 w-full bg-white">
                {/* Spécialités Section */}
                <SpecialitiesFilter
                  searchTerm={searchSpecialityKey || ""}
                  handleOptionSelect={handleSelectSearchTerm}
                />

                {/* Praticiens Section */}
                <DoctorsFilter
                  isOpen={isOpen}
                  searchTerm={searchSpecialityKey || ""}
                  handleOptionSelect={(
                    id,
                    nom,
                    prenom,
                    titre,
                    adresse,
                    specialite
                  ) =>
                    navigateToProfessionalProfile({
                      id,
                      nom,
                      prenom,
                      titre,
                      adresse,
                      specialite,
                    })
                  }
                />

                {/* Établissements Section */}
                <EtablishmentsFilter
                  isOpen={isOpen}
                  searchTerm={searchSpecialityKey || ""}
                  handleOptionSelect={(
                    id,
                    nom,
                    prenom,
                    titre,
                    adresse,
                    specialite
                  ) =>
                    navigateToProfessionalProfile({
                      id,
                      nom,
                      prenom,
                      titre,
                      adresse,
                      specialite,
                    })
                  }
                />
              </Box>
            </Paper>
          </Paper>
        )}
      </form>
    </div>
  );
};

export default SearchBar;
