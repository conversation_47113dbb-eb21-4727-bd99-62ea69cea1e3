import { TextField, InputAdornment, Box } from "@mui/material";
import { Search } from "lucide-react";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { styled } from "@mui/material/styles";
import { SECONDARY,GREEN_2 ,PRIMARY ,BLEU_FONCE } from "@/shared/constants/Color";

// Style pour l'icône avec dégradé vert
const GradientIconWrapper = styled('div')({
  background: 'linear-gradient(135deg, '+PRIMARY+', '+SECONDARY+')',
  borderRadius: '50%',
  padding: '6px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  boxShadow: '0 2px 8px rgba(76, 175, 80, 0.3)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.1)',
    boxShadow: '0 4px 12px rgba(76, 175, 80, 0.4)',
  }
});

interface SpecialitiesSearchBarProps {
  value: string;
  setValue: (val: string) => void;
  isCompletionOpen: boolean;
  setIsCompletionOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const SpecialitiesSearchBar = ({
  value,
  setValue,
  setIsCompletionOpen,
}: SpecialitiesSearchBarProps) => {
  // État pour suivre si l'utilisateur est en train de taper
  const [isTyping, setIsTyping] = useState(false);

  const handleChange = (selected: string) => {
    setValue(selected);
    // Activer l'animation de rotation quand l'utilisateur tape
    setIsTyping(true);
    // Désactiver l'animation après un délai
    setTimeout(() => setIsTyping(false), 1000);
  };

  const handleInputFocus = () => {
    setIsCompletionOpen(true);
  };

  return (
    <Box className="w-full">
      <TextField
        fullWidth
        placeholder="Nom, spécialité, symptôme, établissement"
        value={value}
        onChange={(e) => handleChange(e.target.value)}
        onFocus={handleInputFocus}
        sx={{
          "& .MuiOutlinedInput-root": {
            "& fieldset": {
              borderColor: "transparent",
            },
            "&:hover fieldset": {
              borderColor: "transparent",
            },
            "&.Mui-focused fieldset": {
              borderColor: "transparent",
            },
          },
          "& .MuiInputBase-input::placeholder": {
            color: "gray",
            fontSize: "15px",
            opacity: 1,
          },
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <GradientIconWrapper>
                <motion.div
                  animate={{
                    rotate: isTyping ? 360 : 0,
                    scale: isTyping ? 1.2 : 1
                  }}
                  transition={{
                    type: "spring",
                    stiffness: 260,
                    damping: 20,
                    duration: 0.8
                  }}
                >
                  <Search className="h-4 w-4 text-white" />
                </motion.div>
              </GradientIconWrapper>
            </InputAdornment>
          ),
        }}
      />
    </Box>
  );
};
