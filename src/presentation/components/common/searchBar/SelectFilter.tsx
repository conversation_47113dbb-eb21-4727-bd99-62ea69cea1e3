import React, { ComponentProps, useState } from 'react';
import { Select, MenuItem, FormControl, SelectChangeEvent, IconButton, Menu, Button, useMediaQuery, useTheme } from '@mui/material';
import { twMerge } from 'tailwind-merge';
import FilterListIcon from '@mui/icons-material/FilterList';

type SelectFilterProps = ComponentProps<'div'> & {
  onFilterChange: (type: string, value: string) => void
  filters: {
    status: string
    language: string
    gender: string
  }
}

type FilterOption = {
  label: string
  value: string
}

const statusOptions: FilterOption[] = [
  { label: 'Nouveaux patients', value: '' },
  { label: 'Accepté', value: 'accepte' },
  { label: 'Refusé', value: 'refuse' }
]

const languageOptions: FilterOption[] = [
  { label: 'Langue parlée', value: '' },
  { label: 'Malagasy', value: 'malagasy' },
  { label: 'Français', value: 'francais' },
  { label: '<PERSON><PERSON><PERSON>', value: 'anglais' }
]

const genderOptions: FilterOption[] = [
  { label: 'Sexe', value: '' },
  { label: 'Homme', value: 'homme' },
  { label: 'Femme', value: 'femme' }
]

const SelectFilter = ({ onFilterChange, filters, className, ...props }: SelectFilterProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleFilterChange = (type: string, value: string) => {
    onFilterChange(type, value)
    if (isMobile) handleClose()
  }

  const renderFilterGroup = (
    type: 'status' | 'language' | 'gender',
    options: FilterOption[],
    value: string,
    showBorder = true
  ) => (
    <div className="flex items-center h-[32px]">
      {showBorder && <div className="hidden sm:block h-4 w-[1px] bg-gray-300" />}
      <div className="px-2">
        <FormControl
          variant="standard"
          sx={{
            minWidth: 120,
            '& .MuiInput-underline:before': { borderBottom: 'none' },
            '& .MuiInput-underline:after': { borderBottom: 'none' },
            '& .MuiInput-underline:hover:not(.Mui-disabled):before': { borderBottom: 'none' }
          }}
        >
          <Select
            value={value}
            onChange={(event: SelectChangeEvent) => handleFilterChange(type, event.target.value)}
            displayEmpty
            sx={{ fontWeight: 'semi-bold' }}
          >
            {options.map((option) => (
              <MenuItem
                key={option.value}
                value={option.value}
                sx={{ fontWeight: 600 }}
              >
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>
    </div>
  )

  const mobileFilters = (
    <>
      <IconButton
        onClick={handleClick}
        size="small"
        sx={{ ml: 2 }}
        aria-controls={anchorEl ? 'filter-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={anchorEl ? 'true' : undefined}
      >
        <FilterListIcon />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        id="filter-menu"
        open={!!anchorEl}
        onClose={handleClose}
        onClick={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <div className="p-2">
          {renderFilterGroup('status', statusOptions, filters.status, false)}
          {renderFilterGroup('language', languageOptions, filters.language, false)}
          {renderFilterGroup('gender', genderOptions, filters.gender, false)}
        </div>
      </Menu>
    </>
  )

  const desktopFilters = (
    <div className="flex flex-wrap items-center mx-5 h-[32px]">
      {renderFilterGroup('status', statusOptions, filters.status, false)}
      {renderFilterGroup('language', languageOptions, filters.language)}
      {renderFilterGroup('gender', genderOptions, filters.gender)}
    </div>
  )

  return (
    <div
      className={twMerge(
        'flex flex-col text-sm font-bold bg-white px-4 py-2 items-start border-b sm:flex-row sm:items-center',
        className
      )}
      {...props}
    >
      <div className="flex items-center h-[32px]">
        <div className="text-sm font-bold text-gray-500 px-2">FILTRER PAR</div>
        <div className="hidden sm:block h-4 w-[1px] bg-gray-300" />
      </div>
      {isMobile ? mobileFilters : desktopFilters}
    </div>
  )
}

export default SelectFilter