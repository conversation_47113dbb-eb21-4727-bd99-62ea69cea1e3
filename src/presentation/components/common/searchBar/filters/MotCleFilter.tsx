import { Box, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import LoadingSpinner from "../../LoadingSpinner";
import { highlightText } from "@/shared/utils/highlightText";
import { mot_cles } from "@/domain/models/MotCles";
import useMotCle from "@/presentation/hooks/use-mot-cle";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";
import { useToast } from "@/presentation/hooks/use-toast";

interface MotCleFilterProps {
  searchTerm: string;
  handleOptionSelect: (option: string) => void;
}
const MotCleFilter = ({
  searchTerm,
  handleOptionSelect,
}: MotCleFilterProps) => {
  const [motclesList, setMotClesList] = useState<mot_cles[]>();
  const [filteredMotcles, setFilteredMotcles] = useState<mot_cles[]>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const toast = useToast();

  const { getMotCles } = useMotCle();

  useEffect(() => {
    setIsLoading(true);

    getMotCles()
      .then((res) => {
        setMotClesList(res);
      })
      .catch((error) =>
        toast.error(error.message || ErrorMessages.UNKNOWN_ERROR),
      );
    setIsLoading(false);
  }, []);

  useEffect(() => {
    if (motclesList && motclesList.length > 0) {
      if (searchTerm && searchTerm.trim() === "") {
        setFilteredMotcles(motclesList);
      } else {
        const filtered = motclesList.filter((option) =>
          option.symptome.toLowerCase().includes(searchTerm.toLowerCase()),
        );
        setFilteredMotcles(filtered);
      }
    } else {
      setFilteredMotcles([]);
    }
  }, [searchTerm, motclesList]);

  return (
    <Box className="flex-1 p-2 w-full min-w-[300px]">
      <Typography className="text-gray-600 font-medium mb-4 text-sm" component={'div'}>
        <p className="font-bold text-meddoc-fonce">Symptômes</p>
      </Typography>
      <Box className="flex flex-wrap align-center space-y-2">
        {isLoading ? (
          <Box className="flex justify-center py-4">
            <LoadingSpinner
              size={32}
              color="border-gray-300"
              className="h-[50px]"
            />
          </Box>
        ) : filteredMotcles && filteredMotcles.length > 0 ? (
          filteredMotcles.map((option, index) => {
            return (
              <Box
                key={index}
                className="p-1 w-1/2 min-w-[300px] hover:bg-gray-100 cursor-pointer rounded-md transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  handleOptionSelect(option.symptome);
                }}
              >
                <Typography component={'div'}>
                  <p className="text-sm">
                    {highlightText(option.symptome, searchTerm)}
                  </p>
                </Typography>
              </Box>
            );
          })
        ) : (
          <p className="text-xs mt-2 text-gray-500">
            {searchTerm
              ? `Aucune spécialité nommée "${searchTerm}" trouvée`
              : "Aucune spécialité disponible"}
          </p>
        )}
      </Box>
    </Box>
  );
};

export default MotCleFilter;
