import { Box, Typography } from "@mui/material"
import LoadingSpinner from "../../LoadingSpinner"
import { Commune } from "@/domain/models"

interface CommuneFilterProps {
  communes: Commune[],
  loading: boolean,
  handleOptionSelect: (option: string) => void,
  searchTerm: string
}

const CommuneFilter = ({ communes, loading, handleOptionSelect, searchTerm }: CommuneFilterProps) => {
  return (
    <Box className="flex-1 border-x border-gray-200 px-6 max-h-[300px] overflow-y-auto" >
      <Typography className="text-gray-600 font-medium mb-4 text-sm">
        Communes
      </Typography>
      <Box className="flex flex-col space-y-2">
        {loading ? (
          <Box className="flex justify-center py-4">
            <LoadingSpinner size={32} color="border-gray-300" />
          </Box>
        ) : communes.length > 0 ? (
          communes.map((option, index) => (
            <Box
              key={index}
              className="p-3 hover:bg-gray-100 cursor-pointer rounded-md transition-colors"
              onClick={() =>
                handleOptionSelect(`${option.nom}`)
              }
            >
              <Typography className="text-sm">
                {option.nom}
              </Typography>
            </Box>
          ))
        ) : (
          <p className="text-xs mt-2 text-gray-500">
            {searchTerm
              ? `Aucune commune nommée "${searchTerm}" trouvé`
              : "Aucune commune disponible"}
          </p>
        )}
      </Box>
    </Box>
  )
}

export default CommuneFilter