import { Box, Typography } from "@mui/material"
import LoadingSpinner from "../../LoadingSpinner"
import { District } from "@/domain/models"

interface RegionFilterProps {
  regions: District[],
  loading: boolean,
  handleOptionSelect: (option: string) => void,
  searchTerm: string
}

const DistrictFilter = ({ regions, loading, handleOptionSelect, searchTerm }: RegionFilterProps) => {
  return (
    <Box className="flex-1 border-x border-gray-200 px-6 max-h-[300px] overflow-y-auto" >
      <Typography className="text-gray-600 font-medium mb-4 text-sm">
        Dístricts
      </Typography>
      <Box className="flex flex-col space-y-2">
        {loading ? (
          <Box className="flex justify-center py-4">
            <LoadingSpinner size={32} color="border-gray-300" />
          </Box>
        ) : regions.length > 0 ? (
          regions.map((option, index) => (
            <Box
              key={index}
              className="p-3 hover:bg-gray-100 cursor-pointer rounded-md transition-colors"
              onClick={() =>
                handleOptionSelect(`${option.libelle}`)
              }
            >
              <Typography className="text-sm">
                {option.libelle}
              </Typography>
            </Box>
          ))
        ) : (
          <p className="text-xs mt-2 text-gray-500">
            {searchTerm
              ? `Aucun district nommé "${searchTerm}" trouvé`
              : "Aucun district disponible"}
          </p>
        )}
      </Box>
    </Box>
  )
}

export default DistrictFilter