import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import useSpecialitiesLists from "@/presentation/hooks/use-specialities-lists";
import { Box, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import MotCleFilter from "./MotCleFilter";
import { highlightText } from "@/shared/utils/highlightText";

interface SpecialitiesFilterProps {
  searchTerm: string;
  handleOptionSelect: (option: string) => void;
}

export const SpecialitiesFilter = ({
  searchTerm,
  handleOptionSelect,
}: SpecialitiesFilterProps) => {
  const { listes, loading, getSpecialitiesList } = useSpecialitiesLists();
  const [filteredListes, setFilteredListes] = useState(listes);

  const { searchType, setSearchType } = useSearchProfessional();

  useEffect(() => {
    if (listes.length === 0) getSpecialitiesList();
  }, []);

  useEffect(() => {
    if (!listes) setFilteredListes([]);

    if (searchTerm && searchTerm.trim() === "") {
      setFilteredListes(listes);
    } else {
      const filtered = listes.filter((option) =>
        option.nom_specialite.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredListes(filtered);
    }
  }, [searchTerm, listes]);

  const handleSearchProfessionnalSpeciality = (key: string) => {
    handleOptionSelect(key);
    setSearchType("name");
  };

  const handleSearchKeyword = (key: string) => {
    handleOptionSelect(key);
    setSearchType("symptome");
  };

  return (
    <Box className="flex-1 max-h-[300px] overflow-y-auto w-full min-w-[300px]">
      {filteredListes.length > 0 && (
        <>
          <Typography className="text-gray-600 font-medium mb-4 text-sm">
            <span className="font-bold text-meddoc-fonce">Spécialités</span>
          </Typography>
          <Box className="flex flex-wrap align-center ">
            {loading ? (
              <Box className="flex justify-center ">
                <LoadingSpinner
                  size={32}
                  color="border-gray-300"
                  className="h-[50px]"
                />
              </Box>
            ) : filteredListes.length > 0 ? (
              filteredListes.map((option, index) => (
                <Box
                  key={index}
                  className="p-2 w-1/2 min-w-[300px] hover:bg-gray-100 cursor-pointer rounded-md transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSearchProfessionnalSpeciality(option.nom_specialite);
                  }}
                >
                  <Typography component={"div"}>
                    <p className="text-sm">
                      {highlightText(option.nom_specialite, searchTerm)}
                    </p>
                  </Typography>
                </Box>
              ))
            ) : (
              <p className="text-xs mt-2 text-gray-500">
                {searchTerm
                  ? `Aucune spécialité nommée "${searchTerm}" trouvée`
                  : "Aucune spécialité disponible"}
              </p>
            )}
          </Box>
        </>
      )}

      <MotCleFilter
        searchTerm={searchTerm}
        handleOptionSelect={handleSearchKeyword}
      />
    </Box>
  );
};

export default SpecialitiesFilter;
