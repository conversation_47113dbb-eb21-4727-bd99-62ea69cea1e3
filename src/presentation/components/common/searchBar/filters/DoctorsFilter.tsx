import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import useProfessionals from "@/presentation/hooks/use-professionals";
import { Box, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { highlightText } from "@/shared/utils/highlightText";

interface DoctorsFilterProps {
  isOpen: boolean;
  searchTerm: string;
  handleOptionSelect: (
    id: number,
    nom: string,
    prenom: string,
    titre: string,
    adresse: string,
    specialite: string
  ) => void;
}

const DoctorsFilter = ({
  isOpen,
  searchTerm,
  handleOptionSelect,
}: DoctorsFilterProps) => {
  const { professionals, getProfessionalsList, loading } = useProfessionals();
  const [filteredProfessionals, setFilteredProfessionals] =
    useState(professionals);

  useEffect(() => {
    if (professionals.length === 0) {
      getProfessionalsList();
    }
  }, [professionals.length, getProfessionalsList]);

  useEffect(() => {
    if (!professionals) setFilteredProfessionals([]);

    if (searchTerm && searchTerm.trim() === "") {
      setFilteredProfessionals(professionals);
    } else {
      const filtered = professionals.filter(
        (option) =>
          option.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
          option.prenom.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredProfessionals(filtered);
    }
  }, [searchTerm, professionals]);

  if (!isOpen || !searchTerm || searchTerm.trim() === "") return null;

  return (
    <Box className="flex-1 border-x border-gray-200 px-6 max-h-[300px] overflow-y-auto">
      {filteredProfessionals && filteredProfessionals.length > 0 && (
        <>
          <Typography className="text-gray-600 font-medium mb-4 text-sm">
            Médecin
          </Typography>
          <Box className="flex flex-col space-y-2">
            {loading ? (
              <Box className="flex justify-center py-4">
                <LoadingSpinner size={32} color="border-gray-300" />
              </Box>
            ) : filteredProfessionals.length > 0 ? (
              filteredProfessionals.map((option, index) => (
                <Box
                  key={index}
                  className="p-3 hover:bg-gray-100 cursor-pointer rounded-md transition-colors"
                  onClick={() =>
                    handleOptionSelect(
                      option.id,
                      option.nom,
                      option.prenom,
                      option.titre,
                      option.adresse,
                      option.specialites_professionnel[0].nom_specialite
                    )
                  }
                >
                  <Typography className="text-sm">
                    {highlightText(
                      `${option.prenom} ${option.nom}`,
                      searchTerm
                    )}
                  </Typography>
                  <Typography className="text-xs text-gray-500">
                    {option.specialites_professionnel
                      .map((sp) => sp.nom_specialite)
                      .join(", ")}
                    {option.commune && ` • ${option.commune}`}
                  </Typography>
                </Box>
              ))
            ) : (
              <p className="text-xs mt-2 text-gray-500">
                {searchTerm
                  ? `Aucun médecin nommé "${searchTerm}" trouvé`
                  : "Aucun médecin disponible"}
              </p>
            )}
          </Box>
        </>
      )}
    </Box>
  );
};

export default DoctorsFilter;
