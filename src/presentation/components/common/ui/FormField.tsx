import { ChangeEvent, memo, useEffect, useState } from "react";
import { LucideIcon, ChevronDown, Eye, EyeOff } from "lucide-react";
import { FieldError, UseFormRegister } from "react-hook-form";
import PasswordStrengthIndicator from "./PasswordStrengthIndicator";

interface FormFieldProps {
  id: string;
  label: string;
  placeholder: string;
  type?: "text" | "number" | "email" | "password" | "select" | "tel" | "radio";
  icon: LucideIcon;
  register: UseFormRegister<any>;
  required?: boolean;
  options?: { value: string; label: string }[];
  inputMode?: "text" | "numeric" | "tel" | "email" | "url";
  pattern?: string;
  className?: string;
  error?: FieldError;
  validation?: {
    required?: string;
    minLength?: { value: number; message: string };
    maxLength?: { value: number; message: string };
    pattern?: { value: RegExp; message: string };
    validate?: (value: any) => boolean | string;
  };
  value?: string;
  onChange?: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  showPasswordStrength?: boolean;
  helpText?: string;
  disabled?: boolean;
}

const FormField = ({
  id,
  label,
  placeholder,
  type = "text",
  icon: Icon,
  register,
  required = false,
  options = [],
  inputMode,
  pattern,
  className = "col-span-1",
  error,
  validation,
  value,
  onChange,
  showPasswordStrength = false,
  helpText,
  disabled,
}: FormFieldProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [currentValue, setCurrentValue] = useState(value);

  // Préparer les règles de validation
  const registerOptions = {
    ...(validation || {}),
    ...(required && !validation?.required
      ? { required: `Le champ ${label.toLowerCase()} est requis` }
      : {}),
  };

  // Déterminer les classes CSS en fonction de l'état d'erreur
  const inputClasses = `w-full py-2 rounded-[12px] bg-slate-50 dark:bg-gray-700 border border-slate-200 dark:border-gray-600 hover:border-[#27aae1] dark:hover:border-blue-400 focus-within:border-[#27aae1] dark:focus-within:border-blue-400 focus-within:border-1 px-4 py-[14px] ${
    error ? "border-red-500 dark:border-red-400" : "border-gray-200 dark:border-gray-600"
  } ${disabled ? "text-gray-500" : "text-gray-700 dark:text-gray-200"} text-gray-700 dark:text-gray-200 focus:outline-none focus:border-meddoc-primary dark:focus:border-blue-400`;

  // Gérer l'enregistrement avec react-hook-form et les événements onChange
  const registerProps = onChange
    ? {
        ...register(id, registerOptions),
        onChange: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
          setCurrentValue(e.target.value);
          onChange(e);
        },
        value: currentValue,
        disabled: disabled,
      }
    : {
        ...register(id, registerOptions),
        onChange: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
          setCurrentValue(e.target.value);
        },
        value: currentValue,
        disabled: disabled,
      };

  const inputType = type === "password" && showPassword ? "text" : type;

  return (
    <div className={className}>
      <div className="flex flex-col w-full">
        <label
          htmlFor={id}
          className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200 mb-2"
        >
          <Icon size={16} className="text-meddoc-primary dark:text-blue-400 mr-2" />
          {label}
          {required && <span className="text-red-500 dark:text-red-400 ml-1">*</span>}
        </label>

        {type === "select" ? (
          <div className="relative">
            <select
              id={id}
              name={id}
              className={inputClasses + " text-gray-500 dark:text-gray-300 appearance-none"}
              aria-invalid={error ? "true" : "false"}
              aria-describedby={error ? `${id}-error` : undefined}
              {...registerProps}
            >
              <option value="" hidden>
                {placeholder}
              </option>
              {options.map((option) => (
                <option key={option.value} value={option.value} className="dark:bg-gray-700 dark:text-gray-200">
                  {option.label}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <ChevronDown size={16} className="text-gray-400 dark:text-gray-500" />
            </div>
          </div>
        ) : type === "radio" ? (
          <div className="flex flex-col gap-2">
            {options.map((option) => (
              <label
                key={option.value}
                className="inline-flex items-center gap-2"
              >
                <input
                  type="radio"
                  value={option.value}
                  className="text-meddoc-primary"
                  {...registerProps}
                  checked={currentValue === option.value}
                />
                <span className="text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        ) : (
          <div className="relative">
            <input
              type={inputType}
              id={id}
              name={id}
              placeholder={placeholder}
              className={inputClasses}
              inputMode={inputMode}
              pattern={pattern}
              aria-invalid={error ? "true" : "false"}
              aria-describedby={error ? `${id}-error` : undefined}
              {...registerProps}
            />
            {type === "password" && (
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center pr-3"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff
                    size={16}
                    className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400"
                  />
                ) : (
                  <Eye
                    size={16}
                    className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400"
                  />
                )}
              </button>
            )}
          </div>
        )}

        {/* Help text */}
        {helpText && !error && (
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">{helpText}</p>
        )}

        {/* Password strength indicator */}
        {type === "password" && showPasswordStrength && currentValue && (
          <PasswordStrengthIndicator password={currentValue} />
        )}

        {/* Affichage du message d'erreur */}
        {error && (
          <p id={`${id}-error`} className="mt-1 text-sm text-red-500 dark:text-red-400">
            {error.message}
          </p>
        )}
      </div>
    </div>
  );
};

export default memo(FormField);
