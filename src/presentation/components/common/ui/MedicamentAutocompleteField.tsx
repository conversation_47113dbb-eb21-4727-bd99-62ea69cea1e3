import { LucideIcon, Search } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { FieldError, UseFormRegister, UseFormSetValue } from "react-hook-form";
import { Medicament } from "@/domain/models/Medicament.ts";
import { useMedicamentAutocomplete } from "@/presentation/hooks/useMedicamentAutocomplete.ts";

interface MedicamentAutocompleteFieldProps {
  id: string;
  label: string;
  placeholder: string;
  icon: LucideIcon;
  required?: boolean;
  error?: FieldError;
  className?: string;
  register: UseFormRegister<Record<string, unknown>>;
  setValue: UseFormSetValue<Record<string, unknown>>;
}

/**
 * Composant d'autocomplétion pour les médicaments
 * Utilise le hook useMedicamentAutocomplete avec throttling
 */
const MedicamentAutocompleteField: React.FC<MedicamentAutocompleteFieldProps> = ({
  id,
  label,
  placeholder,
  icon: Icon,
  required = false,
  error,
  className = "w-full",
  register,
  setValue,
}) => {
  const [query, setQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Hook pour l'autocomplétion des médicaments
  const { medicaments, loading, search, reset } = useMedicamentAutocomplete();

  // Fermer la liste au clic extérieur
  useEffect(() => {
    const handler = (e: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(e.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handler);
    return () => document.removeEventListener("mousedown", handler);
  }, []);

  const inputClasses = `w-full rounded-[12px] bg-slate-50 dark:bg-gray-700 border border-slate-200 dark:border-gray-600 hover:border-[#27aae1] dark:hover:border-blue-400 focus-within:border-[#27aae1] dark:focus-within:border-blue-400 px-4 py-[14px] pr-8 ${
    error
      ? "border-red-500 dark:border-red-400"
      : "border-gray-200 dark:border-gray-600"
  } text-gray-700 dark:text-gray-200 focus:outline-none focus:border-meddoc-primary dark:focus:border-blue-400`;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setIsOpen(true);
    
    // Mettre à jour React Hook Form
    setValue(id, value, { shouldValidate: true });
    
    // Déclencher la recherche avec throttling
    if (value.trim().length >= 2) {
      search(value);
    } else {
      reset();
    }
  };

  const handleSelectMedicament = (medicament: Medicament) => {
    setQuery(medicament.nom);
    setIsOpen(false);
    setValue(id, medicament.nom, { shouldValidate: true });
    reset();
  };

  return (
    <div className={className} ref={containerRef}>
      <label
        htmlFor={id}
        className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200 mb-2"
      >
        <Icon
          size={16}
          className="text-meddoc-primary dark:text-blue-400 mr-2"
        />
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <div className="relative">
        <input
          type="text"
          id={id}
          placeholder={placeholder}
          className={inputClasses}
          {...register(id, { required: required ? `Le champ ${label.toLowerCase()} est requis` : false })}
          value={query}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-meddoc-primary"></div>
          ) : (
            <Search size={16} className="text-gray-400 dark:text-gray-500" />
          )}
        </div>

        {isOpen && medicaments.length > 0 && (
          <ul className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-xl max-h-48 overflow-auto border border-gray-200 dark:border-gray-600">
            {medicaments.map((medicament) => (
              <li
                key={medicament.id}
                className="px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                onClick={() => handleSelectMedicament(medicament)}
              >
                <div className="font-medium">{medicament.nom}</div>
                {medicament.force && (
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Force: {medicament.force}
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}

        {isOpen && query.length >= 2 && medicaments.length === 0 && !loading && (
          <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-xl px-4 py-2 text-sm text-gray-500 dark:text-gray-400 border border-gray-200 dark:border-gray-600">
            Aucun médicament trouvé
          </div>
        )}

        {isOpen && query.length > 0 && query.length < 2 && (
          <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-xl px-4 py-2 text-sm text-gray-500 dark:text-gray-400 border border-gray-200 dark:border-gray-600">
            Tapez au moins 2 caractères pour rechercher
          </div>
        )}
      </div>

      {error && (
        <p className="mt-1 text-sm text-red-500 dark:text-red-400">
          {error.message}
        </p>
      )}
    </div>
  );
};

export default MedicamentAutocompleteField;
