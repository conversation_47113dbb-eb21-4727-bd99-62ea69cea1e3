import { LucideIcon, Search } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { FieldError, UseFormSetValue } from "react-hook-form";

type BaseOption = {
  id: number;
  nom: string;
};

interface AutocompleteSelectProps<T extends BaseOption> {
  id: string;
  label: string;
  placeholder: string;
  icon: LucideIcon;
  required?: boolean;
  options: T[];
  error?: FieldError;
  className?: string;
  value?: number;
  onChange?: (value: number) => void;
  disabled?: boolean;
  setValue: UseFormSetValue<any>; // RHF
}

const AutocompleteSelect = <T extends BaseOption>({
  id,
  label,
  placeholder,
  icon: Icon,
  required = false,
  options,
  error,
  className = "col-span-1",
  value,
  onChange,
  disabled = false,
  setValue,
}: AutocompleteSelectProps<T>) => {
  const [query, setQuery] = useState(() => {
    const opt = options.find((o) => o.id === value);
    return opt ? opt.nom : "";
  });
  const [isOpen, setIsOpen] = useState(false);
  const [filteredOptions, setFilteredOptions] = useState(options);

  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setFilteredOptions(
      options.filter((opt) =>
        opt.nom.toLowerCase().includes(query.toLowerCase())
      )
    );
  }, [query, options]);

  useEffect(() => {
    const handler = (e: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(e.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handler);
    return () => document.removeEventListener("mousedown", handler);
  }, []);

  const inputClasses = `w-full py-2 rounded-[12px] bg-slate-50 dark:bg-gray-700 border border-slate-200 dark:border-gray-600 hover:border-[#27aae1] dark:hover:border-blue-400 focus-within:border-[#27aae1] dark:focus-within:border-blue-400 px-4 ${
    error
      ? "border-red-500 dark:border-red-400"
      : "border-gray-200 dark:border-gray-600"
  } ${disabled ? "text-gray-500" : "text-gray-700 dark:text-gray-200"} focus:outline-none focus:border-meddoc-primary dark:focus:border-blue-400`;

  return (
    <div className={className} ref={containerRef}>
      <label
        htmlFor={id}
        className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200 mb-2"
      >
        <Icon
          size={16}
          className="text-meddoc-primary dark:text-blue-400 mr-2"
        />
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <div className="relative">
        <input
          type="text"
          id={id}
          placeholder={placeholder}
          disabled={disabled}
          value={query}
          className={inputClasses + " pr-8"}
          onChange={(e) => {
            setQuery(e.target.value);
            setIsOpen(true);
          }}
          onFocus={() => setIsOpen(true)}
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <Search size={16} className="text-gray-400 dark:text-gray-500" />
        </div>

        {isOpen && filteredOptions.length > 0 && (
          <ul className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-xl max-h-48 overflow-auto">
            {filteredOptions.map((opt) => (
              <li
                key={opt.id}
                className="px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200"
                onClick={() => {
                  setQuery(opt.nom);
                  setIsOpen(false);
                  setValue(id, opt.id, { shouldValidate: true });
                  onChange?.(opt.id);
                }}
              >
                {opt.nom}
              </li>
            ))}
          </ul>
        )}

        {isOpen && filteredOptions.length === 0 && (
          <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-xl px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
            Aucun résultat
          </div>
        )}
      </div>

      {error && (
        <p
          id={`${id}-error`}
          className="mt-1 text-sm text-red-500 dark:text-red-400"
        >
          {error.message}
        </p>
      )}
    </div>
  );
};

export default AutocompleteSelect;
