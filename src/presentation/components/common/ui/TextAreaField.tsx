import { ChangeEvent, memo, useState } from "react";
import { LucideIcon } from "lucide-react";
import { FieldError, UseFormRegister } from "react-hook-form";

interface TextAreaFieldProps {
  id: string;
  label: string;
  placeholder: string;
  icon: LucideIcon;
  register: UseFormRegister<any>;
  required?: boolean;
  inputMode?: "text" | "numeric" | "tel" | "email" | "url";
  className?: string;
  error?: FieldError;
  validation?: {
    required?: string;
    minLength?: { value: number; message: string };
    maxLength?: { value: number; message: string };
    pattern?: { value: RegExp; message: string };
    validate?: (value: any) => boolean | string;
  };
  value?: string;
  onChange?: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  helpText?: string;
  disabled?: boolean;
  rows?: number;
  showCharCount?: boolean;
}

const TextAreaField = ({
  id,
  label,
  placeholder,
  icon: Icon,
  register,
  required = false,
  inputMode,
  className = "col-span-1",
  error,
  validation,
  value,
  onChange,
  helpText,
  disabled,
  rows = 4,
  showCharCount = false,
}: TextAreaFieldProps) => {
  const [currentValue, setCurrentValue] = useState(value || "");

  const maxLength = validation?.maxLength?.value;

  const registerOptions = {
    ...(validation || {}),
    ...(required && !validation?.required
      ? { required: `Le champ ${label.toLowerCase()} est requis` }
      : {}),
  };

  const inputClasses = `w-full rounded-[12px] bg-slate-50 dark:bg-gray-700 border border-slate-200 dark:border-gray-600 hover:border-[#27aae1] dark:hover:border-blue-400 focus-within:border-[#27aae1] dark:focus-within:border-blue-400 px-4 py-[14px] resize-none ${
    error
      ? "border-red-500 dark:border-red-400"
      : "border-gray-200 dark:border-gray-600"
  } ${disabled ? "text-gray-500" : "text-gray-700 dark:text-gray-200"} text-gray-700 dark:text-gray-200 focus:outline-none focus:border-meddoc-primary dark:focus:border-blue-400`;

  const registerProps = onChange
    ? {
        ...register(id, registerOptions),
        onChange: (e: ChangeEvent<HTMLTextAreaElement>) => {
          setCurrentValue(e.target.value);
          onChange(e);
        },
        value: currentValue,
        disabled,
      }
    : {
        ...register(id, registerOptions),
        onChange: (e: ChangeEvent<HTMLTextAreaElement>) => {
          setCurrentValue(e.target.value);
        },
        value: currentValue,
        disabled,
      };

  return (
    <div className={className}>
      <div className="flex flex-col w-full">
        <label
          htmlFor={id}
          className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200 mb-2"
        >
          <Icon
            size={16}
            className="text-meddoc-primary dark:text-blue-400 mr-2"
          />
          {label}
          {required && (
            <span className="text-red-500 dark:text-red-400 ml-1">*</span>
          )}
        </label>

        <textarea
          id={id}
          name={id}
          rows={rows}
          placeholder={placeholder}
          inputMode={inputMode}
          className={inputClasses}
          aria-invalid={error ? "true" : "false"}
          aria-describedby={error ? `${id}-error` : undefined}
          maxLength={maxLength}
          {...registerProps}
        />

        {showCharCount && (
          <div className="mt-2 text-sm text-gray-500 dark:text-gray-400 text-right">
            {currentValue.length}
            {maxLength ? ` / ${maxLength}` : ""} caractères
          </div>
        )}

        {helpText && !error && (
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {helpText}
          </p>
        )}

        {error && (
          <p
            id={`${id}-error`}
            className="mt-1 text-sm text-red-500 dark:text-red-400"
          >
            {error.message}
          </p>
        )}
      </div>
    </div>
  );
};

export default memo(TextAreaField);
