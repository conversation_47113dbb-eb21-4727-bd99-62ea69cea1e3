import { useState, useEffect } from "react";
import { Control, Controller, FieldError } from "react-hook-form";
import { WorkflowIcon, Search, Check } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import directionsData from "@/assets/cua/direction-cua.json";

interface DirectionCheckListProps {
  /** Contrôle du formulaire React Hook Form */
  control: Control<any>;
  /** Nom du champ dans le formulaire */
  name: string;
  /** Label du champ */
  label: string;
  /** Erreur de validation */
  error?: FieldError;
  /** Indique si le champ est requis */
  required?: boolean;
  /** Valeur par défaut */
  defaultValue?: string;
}

/**
 * Composant de sélection de direction avec recherche et checklist
 */
const DirectionCheckList = ({
  control,
  name,
  label,
  error,
  required = false,
  defaultValue = "",
}: DirectionCheckListProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredDirections, setFilteredDirections] = useState(directionsData);

  // Filtrer les directions selon le terme de recherche
  useEffect(() => {
    const filtered = directionsData.filter((direction) =>
      direction.nom.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredDirections(filtered);
  }, [searchTerm]);

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        <div className="flex items-center gap-2">
          <WorkflowIcon size={16} className="text-meddoc-primary" />
          {label}
          {required && <span className="text-red-500">*</span>}
        </div>
      </label>

      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={required ? { required: `${label} est requis` } : {}}
        render={({ field }) => (
          <div className="relative">
            {/* Champ de sélection */}
            <button
              type="button"
              onClick={() => setIsOpen(!isOpen)}
              className={`
                w-full px-4 py-3 text-left bg-white dark:bg-gray-800 border rounded-lg
                flex items-center justify-between transition-all duration-200
                ${
                  error
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500/20"
                    : "border-gray-300 dark:border-gray-600 focus:border-meddoc-primary focus:ring-meddoc-primary/20"
                }
                hover:border-meddoc-primary/50 focus:outline-none focus:ring-2
              `}
            >
              <span
                className={
                  field.value
                    ? "text-gray-900 dark:text-gray-100"
                    : "text-gray-500 dark:text-gray-400"
                }
              >
                {field.value || "Sélectionnez une direction"}
              </span>
              <motion.div
                animate={{ rotate: isOpen ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <WorkflowIcon size={16} className="text-gray-400" />
              </motion.div>
            </button>

            {/* Dropdown avec recherche et checklist */}
            <AnimatePresence>
              {isOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-80 overflow-hidden"
                >
                  {/* Barre de recherche */}
                  <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                    <div className="relative">
                      <Search
                        size={16}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      />
                      <input
                        type="text"
                        placeholder="Rechercher une direction..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:border-meddoc-primary"
                      />
                    </div>
                  </div>

                  {/* Liste des directions */}
                  <div className="max-h-60 overflow-y-auto">
                    {filteredDirections.length > 0 ? (
                      filteredDirections.map((direction, index) => (
                        <motion.button
                          key={index}
                          type="button"
                          onClick={() => {
                            field.onChange(direction.nom);
                            setIsOpen(false);
                            setSearchTerm("");
                          }}
                          className={`
                            w-full px-4 py-3 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700
                            flex items-center justify-between transition-colors duration-150
                            ${
                              field.value === direction.nom
                                ? "bg-meddoc-primary/10 text-meddoc-primary"
                                : "text-gray-700 dark:text-gray-300"
                            }
                          `}
                          whileHover={{ backgroundColor: "rgba(39, 170, 225, 0.05)" }}
                        >
                          <span className="flex-1 pr-2">{direction.nom}</span>
                          {field.value === direction.nom && (
                            <Check size={16} className="text-meddoc-primary flex-shrink-0" />
                          )}
                        </motion.button>
                      ))
                    ) : (
                      <div className="px-4 py-6 text-center text-gray-500 dark:text-gray-400">
                        Aucune direction trouvée
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}
      />

      {/* Message d'erreur */}
      {error && (
        <motion.p
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-1 text-sm text-red-600 dark:text-red-400"
        >
          {error.message}
        </motion.p>
      )}
    </div>
  );
};

export default DirectionCheckList;
