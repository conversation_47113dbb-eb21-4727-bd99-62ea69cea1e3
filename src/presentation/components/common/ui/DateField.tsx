import { memo } from "react";
import { startOfDay } from "date-fns";
import { Controller, FieldError, Control } from "react-hook-form";
import { DatePicker, DatePickerProps } from "@mui/x-date-pickers/DatePicker";
import { LucideIcon } from "lucide-react";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { fr } from "date-fns/locale";
interface DateFieldProps extends DatePickerProps<Date> {
  id: string;
  label: string;
  icon: LucideIcon;
  control: Control<any>;
  value?: Date;
  error?: FieldError;
  required?: boolean;
  description?: string;
  className?: string;
  disabled?: boolean;
}

const DateField = ({
  id,
  label,
  icon: Icon,
  control,
  value,
  error,
  required = false,
  description,
  disabled,
  className = "col-span-1",
  ...rest
}: DateFieldProps) => {
  return (
    <div className={`flex flex-col ${className}`}>
      <label
        htmlFor={id}
        className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200 mb-2"
      >
        <Icon
          size={16}
          className="text-meddoc-primary dark:text-blue-400 mr-2"
        />
        {label}
        {required && (
          <span className="text-red-500 dark:text-red-400 ml-1">*</span>
        )}
      </label>

      <Controller
        name={id}
        control={control}
        render={({ field }) => (
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
            <DatePicker
              disabled={disabled}
              value={field.value || null}
              onChange={(date) =>
                field.onChange(date ? startOfDay(date) : null)
              }
              format="dd/MM/yyyy"
              slotProps={{
                textField: {
                  fullWidth: true,
                  id,
                  error: !!error,
                  helperText: error?.message || description,
                  FormHelperTextProps: {
                    className: error
                      ? "text-red-500 dark:text-red-400"
                      : "text-gray-500 dark:text-gray-400",
                  },
                  InputProps: {
                    className: `h-12 px-4 py-2 rounded-lg border ${
                      error
                        ? "border-red-500 dark:border-red-400"
                        : "border-gray-200 dark:border-gray-600"
                    } text-gray-700 dark:text-gray-200 focus:outline-none focus:border-meddoc-primary dark:focus:border-blue-400`,
                  },
                  label: null, // Ne pas utiliser le label flottant interne
                  placeholder: "JJ/MM/AAAA",
                  sx: (theme) => ({
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "12px",
                      backgroundColor: "#f8fafc",
                      "& fieldset": {
                        borderColor: "#e2e8f0",
                      },
                      "&:hover fieldset": {
                        borderColor: "#27aae1",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#27aae1",
                        borderWidth: "2px",
                      },
                      "& input": {
                        padding: "14px 16px",
                        color: "#374151",
                      },
                    },
                    // Dark mode styles
                    ...(theme.palette.mode === "dark" && {
                      "& .MuiOutlinedInput-root": {
                        backgroundColor: "#374151 !important",
                        "& fieldset": {
                          borderColor: "#4b5563 !important",
                        },
                        "&:hover fieldset": {
                          borderColor: "#60a5fa !important",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#60a5fa !important",
                        },
                        "& input": {
                          color: "#e5e7eb !important",
                        },
                      },
                    }),
                  }),
                },
              }}
              {...rest}
            />
          </LocalizationProvider>
        )}
      />
    </div>
  );
};

export default memo(DateField);
