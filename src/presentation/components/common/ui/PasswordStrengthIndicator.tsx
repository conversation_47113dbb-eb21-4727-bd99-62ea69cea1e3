import React from "react";
import { 
  validatePasswordStrength, 
  getPasswordStrengthText, 
  getPasswordStrengthColor 
} from "@/shared/utils/passwordValidation";
import { Check, X } from "lucide-react";

interface PasswordStrengthIndicatorProps {
  password: string;
  showDetails?: boolean;
}

const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  showDetails = true,
}) => {
  const strength = validatePasswordStrength(password);

  if (!password) return null;

  const getBarColor = (index: number): string => {
    if (index < strength.score) {
      if (strength.score < 2) return "bg-red-500";
      if (strength.score < 3) return "bg-orange-500";
      if (strength.score < 4) return "bg-yellow-500";
      return "bg-green-500";
    }
    return "bg-gray-200";
  };

  return (
    <div className="mt-2 space-y-2">
      {/* Strength bars */}
      <div className="flex space-x-1">
        {[0, 1, 2, 3].map((index) => (
          <div
            key={index}
            className={`h-2 flex-1 rounded-full transition-colors duration-200 ${getBarColor(index)}`}
          />
        ))}
      </div>

      {/* Strength text */}
      <div className="flex items-center justify-between">
        <span className={`text-sm font-medium ${getPasswordStrengthColor(strength.score)}`}>
          {getPasswordStrengthText(strength.score)}
        </span>
        {strength.isValid && (
          <Check size={16} className="text-green-500" />
        )}
      </div>

      {/* Detailed feedback */}
      {showDetails && strength.feedback.length > 0 && (
        <div className="space-y-1">
          <p className="text-xs text-gray-600">Critères manquants :</p>
          <ul className="space-y-1">
            {strength.feedback.map((item, index) => (
              <li key={index} className="flex items-center text-xs text-gray-600">
                <X size={12} className="text-red-400 mr-1 flex-shrink-0" />
                {item}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Success message */}
      {strength.isValid && (
        <div className="flex items-center text-xs text-green-600">
          <Check size={12} className="mr-1" />
          Mot de passe sécurisé
        </div>
      )}
    </div>
  );
};

export default PasswordStrengthIndicator;
