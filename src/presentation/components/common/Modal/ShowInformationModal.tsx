import {
  Dialog,
  DialogTitle,
  Typography,
  IconButton,
  DialogContent,
} from "@mui/material";
import { X, AlertTriangle } from "lucide-react";

interface ShowInformationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message: string;
  loading?: boolean;
  children?: React.ReactNode;
  disableBackdropClick?: boolean;
  showWarningIcon?: boolean;
}

const ShowInformationModal = ({
  isOpen,
  onClose,
  title,
  message,
  loading,
  children,
  disableBackdropClick,
  showWarningIcon,
}: ShowInformationModalProps) => {
  // Empêche la fermeture de la modale pendant le chargement ou si disableBackdropClick est true
  const handleClose = (
    event: React.SyntheticEvent,
    reason: "backdropClick" | "escapeKeyDown"
  ) => {
    if (loading) return;
    if (disableBackdropClick && reason === "backdropClick") return;
    onClose();
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleClose}
      maxWidth="lg"
      PaperProps={{
        className: "dark:bg-gray-800 dark:text-white w-full",
        sx: {
          "&.dark": {
            backgroundColor: "#1f2937",
            color: "#ffffff",
          },
        },
      }}
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white">
        <Typography variant="h6" component="div" className="text-center w-full">
          {title}
        </Typography>
        <IconButton
          onClick={loading ? undefined : onClose}
          disabled={loading}
          sx={{
            color: "white",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
            "&.Mui-disabled": {
              color: "rgba(255, 255, 255, 0.5)",
            },
          }}
        >
          <X className="h-4 w-4" />
        </IconButton>
      </DialogTitle>
      <DialogContent className="py-6 px-4">
        <div className="flex flex-col items-center gap-4">
          {showWarningIcon && (
            <div className="mb-2">
              <AlertTriangle className="h-12 w-12 text-amber-500" />
            </div>
          )}
          <Typography variant="body1" className="text-center p-3">
            {message}
          </Typography>
          {children}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ShowInformationModal;
