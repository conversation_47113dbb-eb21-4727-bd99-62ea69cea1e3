import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import RepartitionParDirection from "@/presentation/components/features/dash/dashboard/RepartitionParDirection";
import { motion } from "framer-motion";
import PatientsActifs from "@/presentation/components/features/dash/dashboard/PatientsActifs";
import PrincipalesPathologies from "@/presentation/components/features/dash/dashboard/PrincipalesPathologies";
import PrincipauxMedicaments from "@/presentation/components/features/dash/dashboard/PrincipauxMedicaments";
import { Employer } from "@/domain/models";

interface StatistiqueDassModalProps {
  isModalOpen: boolean;
  handleCloseModal: () => void;
  pathologiesData: {
    name: any;
    count: any;
    color: string;
  }[];
  medicamentsData: {
    name: any;
    count: any;
    color: string;
  }[];
  employers: Employer[];
  availableDirections: string[];
  COLORS: string[];
}

export const StatistiqueDassModal = ({
  isModalOpen,
  handleCloseModal,
  pathologiesData,
  medicamentsData,
  employers,
  availableDirections,
  COLORS,
}: StatistiqueDassModalProps) => {
  return (
    <Dialog
      open={isModalOpen}
      onClose={handleCloseModal}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle className="flex justify-between items-center bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <Typography variant="h6" component="div">
          Statistique : Consulter les données détaillées
        </Typography>
        <Tooltip title="Fermer">
          <IconButton onClick={handleCloseModal} size="small">
            <X className="h-4 w-4 text-white" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-6"
        >
          {/* Graphique Employés par Direction */}
          <RepartitionParDirection
            employers={employers}
            availableDirections={availableDirections}
            COLORS={COLORS}
          />

          {/* Graphique Patients actifs */}
          <PatientsActifs employers={employers} />

          {/* Principales pathologies */}
          <PrincipalesPathologies pathologiesData={pathologiesData} />

          {/* Les 5 principaux médicaments */}
          <PrincipauxMedicaments medicamentsData={medicamentsData} />
        </motion.div>
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button
          variant="outlined"
          onClick={handleCloseModal}
          sx={{ textTransform: "none" }}
        >
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
