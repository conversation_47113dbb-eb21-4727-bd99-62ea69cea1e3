import { Employer } from "@/domain/models";
import { useToast } from "../toast/Toast";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer.ts";
import ConfirmationModal from "./ConfirmationModal.tsx";

interface DeleteEmployedModalProps {
  isOpen: boolean;
  handleClose: () => void;
  employed: Employer;
}

export const DeleteEmployedModal = ({
  isOpen,
  employed,
  handleClose,
}: DeleteEmployedModalProps) => {
  const toast = useToast();
  const { loading, remove: handleDeleteEmployedProfessional } = useEmployer();

  const onConfirm = async () => {
    if (!employed) {
      toast.error("Stock non trouvee.");
      return;
    }
    await handleDeleteEmployedProfessional(employed.id);
    handleClose();
  };

  return (
    <ConfirmationModal
      isOpen={isOpen}
      onClose={handleClose}
      onConfirm={onConfirm}
      title="Confirmation de suppression"
      message="Êtes-vous sûr de vouloir supprimer cet employer ?"
      confirmButtonText="Oui"
      cancelButtonText="Non"
      loading={loading}
      confirmButtonColor="danger"
    />
  );
};
