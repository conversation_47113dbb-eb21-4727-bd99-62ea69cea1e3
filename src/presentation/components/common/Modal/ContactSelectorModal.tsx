import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON>ctions,
  IconButton,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { Contact } from "@/presentation/types/message.types";
import ContactSelector from "../../features/messaging/ContactSelector";
import { useAppSelector } from "@/presentation/hooks/redux";

interface ContactSelectorModalProps {
  isOpen: boolean;
  contacts: Contact[];
  loading: boolean;
  onSelectContact: (contactId: number) => void;
  handleClose: () => void;
}

export const ContactSelectorModal = ({
  isOpen,
  contacts,
  loading,
  onSelectContact,
  handleClose,
}: ContactSelectorModalProps) => {
  const roleCurrentUser = useAppSelector(
    (state) => state.authentification?.user?.role
  );
  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="flex justify-end">
        <Tooltip title="Fermer">
          <IconButton onClick={handleClose} size="small">
            <X className="h-4 w-4" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="my-2 min-h-[0] md:min-h-[68.2vh]">
        <ContactSelector
          roleCurrentUser={roleCurrentUser}
          contacts={contacts}
          onSelectContact={onSelectContact}
          loading={loading}
        />
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
      </DialogActions>
    </Dialog>
  );
};
