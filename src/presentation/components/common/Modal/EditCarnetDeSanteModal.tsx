import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  IconButton,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { RenderFormFields } from "@/presentation/components/features/professional/carnetSante/RenderFormFields";
import { getTitle } from "@/shared/utils/getTitle";
import {
  action_carnet_de_sante_enum,
  utilisateurs_role_enum,
} from "@/domain/models/enums";
import { PRIMARY } from "@/shared/constants/Color";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";
import { useUpdateMedicalRecord } from "@/presentation/hooks/carnetDeSante/sousCarnet/useUpdateMedicalRecord";
import { useAppSelector } from "@/presentation/hooks/redux";
import useAuth from "@/presentation/hooks/use-auth";
import HeaderStylePatient from "@/presentation/components/features/patient/PatientCard/HeaderStylePatient";
import HeaderStyleProche from "@/presentation/components/features/proche/HeaderStyleProche";
import HeaderStyleEmployer from "@/presentation/components/features/professional/employer/HeaderStyleEmployer";

interface EditCarnetDeSanteModalProps {
  type: string;
  isOpen: boolean;
  handleClose: () => void;
  item: {
    id: number;
    nom: string;
    remarks?: string;
  };
}

export const EditCarnetDeSanteModal = ({
  type,
  isOpen,
  handleClose,
  item,
}: EditCarnetDeSanteModalProps) => {
  const { handleUpdate } = useUpdateMedicalRecord();
  const { selectedFile } = useCarnetDeSanteState();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { roleUserSelected } = useAuth();
  const handleSave = async () => {
    try {
      await handleUpdate(item.id, type, item.nom, selectedFile);
      handleClose();
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        {role === utilisateurs_role_enum.PROFESSIONNEL ? (
          roleUserSelected === utilisateurs_role_enum.PATIENT ? (
            <HeaderStylePatient />
          ) : (
            <HeaderStyleProche />
          )
        ) : roleUserSelected === utilisateurs_role_enum.EMPLOYER ? (
          <HeaderStyleEmployer />
        ) : (
          <HeaderStyleProche />
        )}
        <Tooltip title="Fermer">
          <IconButton onClick={handleClose} size="small">
            <X className="h-4 w-4 text-white" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="mt-4 min-h-[0] md:min-h-[64.8vh]">
        {getTitle(type, action_carnet_de_sante_enum.modification)}
        <RenderFormFields item={item.nom} type={type} itemId={item.id} />
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            textTransform: "none",
            backgroundColor: PRIMARY,
          }}
          disabled={role === utilisateurs_role_enum.PATIENT}
        >
          Enregistrer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
