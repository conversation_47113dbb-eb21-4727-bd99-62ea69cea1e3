import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  IconButton,
  Button,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Paper,
} from "@mui/material";
import { FileText, Stethoscope, X } from "lucide-react";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { categorie_enum } from "@/domain/models/enums";
import ListePatient from "@/presentation/components/features/professional/agenda/BookAppointment/ListePatient";
import AddProfessionnelPatient from "@/presentation/pages/professional/patients/AddProfessionnelPatient";
import { useRef, useState } from "react";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import Availability from "@/presentation/components/features/professional/professionalCard/Availability";
import { PROFESSIONAL_CARD_CLASSNAMES } from "@/shared/constants/professionalCardConstants";
import { useAvailability } from "@/presentation/hooks/use-availability";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useParams } from "react-router-dom";
import { PRIMARY } from "@/shared/constants/Color";
import { usePatientRegistrationLogic } from "@/presentation/hooks/authentification";
import { useConsultationStepLogic } from "@/presentation/hooks/consultationStep/useConsultationStepLogic";
import FormField from "../ui/FormField";

export const BookAppointmentModal = () => {
  const { id: patientId } = useParams();
  const { currentProfessional } = useSearchProfessional();
  const { selectedTimeSlot } = useConsultationState();
  const { handleResetTimeSlot } = useAvailability();
  const { isAppointmentModalOpen, handleCloseAppointment } = useAgendaState();
  const [selectedPatientId, setSelectedPatientId] = useState<number | null>(
    patientId ? Number(patientId) : null
  );
  const {
    control,
    errors,
    register,
    setValue,
    onSubmit: registerPatient,
  } = usePatientRegistrationLogic();
  const {
    isLoading,
    errors: errorsAppointment,
    setValue: setValueAppointment,
    onSubmit: onSubmitAppointment,
    register: registerAppointment,
    watch,
  } = useConsultationStepLogic();
  const categorie = watch("categorie");
  const horairesRef = useRef<HTMLDivElement>(null);

  const onClose = () => {
    // handleresetState();
    handleCloseAppointment(false);
    if (selectedTimeSlot) {
      handleResetTimeSlot();
    }
  };

  const onSubmit = async () => {
    if (categorie === categorie_enum["premiere consultation"]) {
      const isLoginUser = false;
      const patient = await registerPatient(isLoginUser);
      if (patient?.success) {
        await onSubmitAppointment(patient.userData.id);
      }
    } else if (categorie === categorie_enum["consultation de suivi"]) {
      await onSubmitAppointment(selectedPatientId);
    }
    onClose();
  };

  return (
    <Dialog
      open={isAppointmentModalOpen}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        Reservation de rendez-vous
        <IconButton onClick={onClose}>
          <X className="h-4 w-4 text-white" />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        {selectedTimeSlot ? (
          <div className="my-2">
            <div className="my-2">
              <Typography variant="body1">
                {format(new Date(selectedTimeSlot.date), "EEEE d MMMM", {
                  locale: fr,
                })}
                {" de "} {selectedTimeSlot?.start} à {selectedTimeSlot?.end}
              </Typography>
            </div>
            <div className="mt-2">
              <Button
                variant="outlined"
                onClick={handleResetTimeSlot}
                sx={{ textTransform: "none" }}
              >
                Choisir une autre date
              </Button>
            </div>
          </div>
        ) : (
          <div
            ref={horairesRef}
            className={PROFESSIONAL_CARD_CLASSNAMES.AVAILABILITY_SECTION}
          >
            {!currentProfessional ? null : (
              <Availability
                professionalInformation={currentProfessional}
                className="min-h-[270px]"
                horairesRef={horairesRef}
              />
            )}
          </div>
        )}
        <RadioGroup
          value={categorie}
          onChange={(e) =>
            setValueAppointment("categorie", e.target.value as categorie_enum)
          }
        >
          <FormControlLabel
            value={categorie_enum["consultation de suivi"]}
            control={<Radio />}
            label="Patient existant"
          />
          <FormControlLabel
            value={categorie_enum["premiere consultation"]}
            control={<Radio />}
            label="Nouveau Patient"
          />
        </RadioGroup>
        {categorie === categorie_enum["premiere consultation"] ? (
          <AddProfessionnelPatient
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            register={register}
            setValue={setValue}
          />
        ) : (
          <Paper sx={{ marginBottom: 3, p: 2 }}>
            <Typography variant="h6" color="primary" gutterBottom>
              Rendez-vous avec
            </Typography>
            <ListePatient onPatientSelect={setSelectedPatientId} />
          </Paper>
        )}
        {categorie && (
          <div className="space-y-4">
            <FormField
              id="consultationMotif"
              label="Motif de consultation"
              type="select"
              placeholder="Sélectionnez un motif de consultation"
              icon={FileText}
              register={registerAppointment}
              required
              options={[
                {
                  label: "Première consultation",
                  value: "Première consultation",
                },
                {
                  label: "Consultation de suivi",
                  value: "Consultation de suivi",
                },
                {
                  label: "Consultation d'urgence",
                  value: "Consultation d'urgence",
                },
              ]}
              error={errorsAppointment.consultationMotif}
              validation={{
                required: "Le sexe est requis",
              }}
            />
            <FormField
              id="consultationReason"
              label="Raison de la consultation"
              type="text"
              placeholder="Sélectionnez une raison de la consultation"
              icon={Stethoscope}
              register={registerAppointment}
              required
              error={errorsAppointment.consultationReason}
              validation={{
                required: "La raison de la consultation est requise",
              }}
            />
          </div>
        )}
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          sx={{
            textTransform: "none",
            backgroundColor: PRIMARY,
          }}
          disabled={!categorie}
          onClick={onSubmit}
          loading={isLoading}
        >
          Enregistrer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
