import CarnetDeSante from "@/presentation/pages/professional/patients/CarnetDeSante";
import { Button, Dialog, DialogActions, DialogContent, DialogTitle, IconButton, Tooltip, Typography } from "@mui/material";
import { X } from "lucide-react";

interface CarnetDeSanteModaleProps {
    isModalOpen: boolean;
    handleCloseModal: () => void;
}

export const CarnetDeSanteModale = ({
    isModalOpen,
    handleCloseModal,
}) => {
    return (
        <Dialog
            open={isModalOpen}
            onClose={handleCloseModal}
            maxWidth="lg"
            fullWidth
        >
            <DialogTitle className="flex justify-between items-center bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
                <Typography variant="h6" component="div">
                    Carnet de santé
                </Typography>
                <Tooltip title="Fermer">
                    <IconButton onClick={handleCloseModal} size="small">
                        <X className="h-4 w-4 text-white" />
                    </IconButton>
                </Tooltip>
            </DialogTitle>
            <DialogContent>
                <CarnetDeSante
                    isEdit={false}
                    className="grid grid-cols-1 lg:grid-cols-2 gap-4"
                />
            </DialogContent>
            <DialogActions sx={{ p: 2 }}>
                <Button
                    variant="outlined"
                    onClick={handleCloseModal}
                    sx={{ textTransform: "none" }}
                >
                    Fermer
                </Button>
            </DialogActions>
        </Dialog>

    )
}
