import {
  <PERSON>,
  Chip,
  TextField,
  Autocomplete,
  Typography,
  styled,
  FormControlLabel,
  Checkbox,
  Alert,
} from "@mui/material";
import Tooltip, { TooltipProps, tooltipClasses } from "@mui/material/Tooltip";
import { RenderFormFields } from "@/presentation/components/features/professional/carnetSante/RenderFormFields";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { Info } from "lucide-react";
import {
  useCarnetDeSanteOptions,
  useCarnetDeSanteAutocomplete,
  useSelectedItems,
} from "./hooks";
import { useCarnetDeSanteStockManagement } from "./hooks/useCarnetDeSanteStockManagement";
import { useStockModeContext } from "./context/StockModeContext";

interface CarnetDeSanteFormProps {
  type: string;
}

// Type pour les options (utilisé dans le renderOption)
interface MedicamentOption {
  nom: string;
  dosage?: string;
  forme?: string;
}

const LightTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.white,
    color: "rgba(0, 0, 0, 0.87)",
    boxShadow: theme.shadows[3],
  },
}));

export const CarnetDeSanteForm = ({ type }: CarnetDeSanteFormProps) => {
  // Utiliser le contexte pour l'état du mode stock
  const { useStockMode, setUseStockMode } = useStockModeContext();

  // Utilisation des custom hooks pour séparer les responsabilités
  const { selectedItems, handleItemAdd, handleItemDelete } = useSelectedItems();
  const { inputValue, handleInputChange, createChangeHandler, getOptionLabel } =
    useCarnetDeSanteAutocomplete();
  const { filteredOptions, loading, error, stockMedicaments, isStockMode } =
    useCarnetDeSanteOptions(type, inputValue, useStockMode);

  // Hook pour la gestion du stock (utilisé seulement si useStockMode = true)
  const { checkMedicamentAvailability, getStockStatistics, medicamentsCount } =
    useCarnetDeSanteStockManagement();

  // Créer le gestionnaire de changement spécifique à ce composant
  const handleChange = createChangeHandler(selectedItems, handleItemAdd);

  return (
    <>
      {/* Affichage des erreurs de chargement */}
      {error && (
        <Typography variant="body2" color="error" sx={{ mb: 2 }}>
          Erreur lors du chargement des données : {error}
        </Typography>
      )}

      {/* Checkbox pour activer le mode stock professionnel (seulement pour les médicaments) */}
      {type === TITRES_CARNET_DE_SANTE.medicaments && (
        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={useStockMode}
                onChange={(e) => setUseStockMode(e.target.checked)}
                color="primary"
              />
            }
            label="Prescrire un médicament en stock"
          />
          {useStockMode && (
            <Alert severity="info" sx={{ mt: 1 }}>
              <Typography variant="body2">
                Mode stock activé : {medicamentsCount} médicaments disponibles
                en stock interne. Les sorties de stock seront créées
                automatiquement lors de la soumission.
              </Typography>
            </Alert>
          )}
        </Box>
      )}

      {type !== TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage && (
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          {type === TITRES_CARNET_DE_SANTE.medicaments && useStockMode
            ? "Recherchez parmi les médicaments disponibles en stock interne"
            : 'Si vous ne trouvez pas un élément sur la liste, tapez son nom et appuyez "Entrée" sur le clavier pour le rajouter'}
        </Typography>
      )}
      {type !== TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage &&
        type !== TITRES_CARNET_DE_SANTE.medicaments && (
          <Autocomplete
            freeSolo
            options={filteredOptions}
            getOptionLabel={getOptionLabel}
            inputValue={inputValue}
            onInputChange={handleInputChange}
            onChange={handleChange}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder="Recherche par nom"
                fullWidth
                disabled={loading}
                helperText={loading ? "Chargement des données..." : ""}
              />
            )}
            disableListWrap
            slotProps={{
              listbox: {
                style: { maxHeight: "200px" },
              },
            }}
          />
        )}
      {type === TITRES_CARNET_DE_SANTE.medicaments && (
        <Autocomplete
          options={filteredOptions}
          getOptionLabel={getOptionLabel}
          inputValue={inputValue}
          onInputChange={handleInputChange}
          onChange={handleChange}
          renderOption={(props, option: MedicamentOption) => (
            <Box
              component="li"
              {...props}
              sx={{
                display: "relative",
              }}
            >
              <span>{typeof option === "string" ? option : option.nom}</span>
              {typeof option !== "string" &&
                (option.forme || option.dosage) && (
                  <LightTooltip
                    title={
                      <div>
                        {option.forme && (
                          <h1 className="text-sm">
                            <span className="font-semibold text-orange">
                              Forme:
                            </span>{" "}
                            <span className="text-black capitalize">
                              {option.forme}
                            </span>
                          </h1>
                        )}
                        {option.dosage && (
                          <h1 className="text-sm">
                            <span className="font-semibold text-orange">
                              Dosage:
                            </span>{" "}
                            <span className="text-black capitalize">
                              {option.dosage}
                            </span>
                          </h1>
                        )}
                      </div>
                    }
                  >
                    <Info
                      size={20}
                      className="text-gray-400 absolute right-1"
                    />
                  </LightTooltip>
                )}
            </Box>
          )}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder={
                useStockMode
                  ? "Recherche parmi les médicaments en stock"
                  : "Recherche par nom, dosage ou forme"
              }
              fullWidth
              disabled={loading}
              helperText={
                loading
                  ? useStockMode
                    ? "Chargement du stock..."
                    : "Chargement des médicaments..."
                  : useStockMode
                    ? `${medicamentsCount} médicaments disponibles en stock`
                    : ""
              }
            />
          )}
          disableListWrap
          slotProps={{
            listbox: {
              style: { maxHeight: "200px" },
            },
          }}
        />
      )}
      {selectedItems.length > 0 && (
        <Box sx={{ mt: 2, display: "flex", gap: 1, flexWrap: "wrap" }}>
          {selectedItems.map((item: string) => (
            <Chip
              key={item}
              label={item}
              onDelete={() => handleItemDelete(item)}
              color="primary"
            />
          ))}
        </Box>
      )}

      {selectedItems.map((item: string) => (
        <RenderFormFields
          key={item}
          item={item}
          type={type}
          useStockMode={useStockMode}
        />
      ))}
      {type === TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage && (
        <RenderFormFields type={type} useStockMode={useStockMode} />
      )}
    </>
  );
};
