import {
  Dialog,
  DialogActions,
  Typography,
  DialogContent,
  <PERSON>ton,
  DialogTitle,
  IconButton,
  Toolt<PERSON>,
} from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import {
  useSigneVitaux,
  useSigneVitauxForm,
} from "@/presentation/hooks/signeVitaux";
import { SigneVitauxForm } from "@/presentation/pages/professional/patients/SigneVitauxForm";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";
import { X } from "lucide-react";
import { useEffect } from "react";
import { signe_vitaux } from "@/domain/models";

interface AddSigneVitauxModalProps {
  canPrint?: boolean;
  signeVitaux?: signe_vitaux;
  isAddSigneVitauxModalOpen: boolean;
  isDesableButtonVoir?: boolean;
  handleCloseAddSigneVitauxModal: () => void;
}

const AddSigneVitauxModal = ({
  canPrint,
  signeVitaux,
  isAddSigneVitauxModalOpen,
  isDesableButtonVoir,
  handleCloseAddSigneVitauxModal,
}: AddSigneVitauxModalProps) => {
  const {
    loading,
    create: handleCreateSigneVitaux,
    update: handleUpdateSigneVitaux,
  } = useSigneVitaux();
  const { idCarnetSante } = useCarnetDeSante();
  const {
    formData,
    selectedSignes,
    selectAll,
    handlePrint,
    initialiseState,
    handleSelectAll,
    handleSigneChange,
    handleInputChange,
    getSignesVitauxData,
  } = useSigneVitauxForm(idCarnetSante);

  const handleSubmit = async () => {
    const data = getSignesVitauxData();
    if (signeVitaux) {
      await handleUpdateSigneVitaux(signeVitaux.id, data);
    } else {
      await handleCreateSigneVitaux(data);
    }
    handleCloseAddSigneVitauxModal();
  };

  useEffect(() => {
    if (signeVitaux) {
      initialiseState(signeVitaux);
    }
  }, [signeVitaux]);

  return (
    <Dialog
      open={isAddSigneVitauxModalOpen}
      onClose={handleCloseAddSigneVitauxModal}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <div>
          <Typography variant="h6" className="text-white font-semibold">
            {signeVitaux
              ? "Modifier le signe vitaux"
              : "Ajouter un signe vitaux"}
          </Typography>
        </div>
        <div className="flex items-center gap-4">
          {canPrint && (
            <Button
              variant="outlined"
              sx={{
                textTransform: "none",
                ml: 2,
                borderRadius: 5,
                color: "white",
                borderColor: "white",
              }}
              onClick={handlePrint}
            >
              IMPRIMER
            </Button>
          )}
          <Tooltip title="Fermer">
            <IconButton
              onClick={handleCloseAddSigneVitauxModal}
              sx={{
                color: "white",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                },
              }}
            >
              <X className="h-4 w-4" />
            </IconButton>
          </Tooltip>
        </div>
      </DialogTitle>
      <DialogContent className="mt-4">
        <SigneVitauxForm
          formData={formData}
          selectedSignes={selectedSignes}
          selectAll={selectAll}
          handleSelectAll={handleSelectAll}
          handleSigneChange={handleSigneChange}
          handleInputChange={handleInputChange}
        />
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleCloseAddSigneVitauxModal}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          onClick={handleSubmit}
          loading={loading}
          disabled={isDesableButtonVoir}
        >
          Enregistrer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddSigneVitauxModal;
