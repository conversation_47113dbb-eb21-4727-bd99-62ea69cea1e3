import CarnetDeSante from "@/presentation/pages/professional/patients/CarnetDeSante";
import { Button, Dialog, DialogActions, DialogContent, DialogTitle, IconButton, Tooltip, Typography } from "@mui/material";
import { Activity, Edit, FlaskConical, Stethoscope, UserPlus, Users, X } from "lucide-react";
import { useMemo, useState } from "react";
import ListDataGrid from "../listDataGrid/ListDataGrid";
import { useCarnetDeSanteData } from "@/presentation/hooks/carnetDeSante";
import { ConsultationDetails } from "@/domain/DTOS/MedicalConsultationDetailsDTO";
import { VitalSignsDetails } from "@/domain/DTOS/VitalSignsDetailsDTO";
import { DiagnosticDTO } from "@/domain/DTOS/DiagnosticDTO";

interface HistoriquePatientModaleProps {
    isModalOpen: boolean;
    handleCloseModal: () => void;
    consultationsPatient: ConsultationDetails[];
    signeVitauxPatient: VitalSignsDetails[];
    diagnosticPatient: DiagnosticDTO[];
}

export const HistoriquePatientModale = ({
    isModalOpen,
    handleCloseModal,
    consultationsPatient,
    signeVitauxPatient,
    diagnosticPatient,
}) => {
    const [activePatientTab, setActivePatientTab] = useState("consultations");
    const { consultations, signeVitaux, data } = useCarnetDeSanteData();
    return (
        <Dialog
            open={isModalOpen}
            onClose={handleCloseModal}
            maxWidth="lg"
            fullWidth
        >
            <DialogTitle className="flex justify-between text-center items-center bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
                <Typography variant="h6" component="div">
                    Historique en générale
                </Typography>
                <Tooltip title="Fermer">
                    <IconButton onClick={handleCloseModal} size="small">
                        <X className="h-4 w-4 text-white" />
                    </IconButton>
                </Tooltip>
            </DialogTitle>
            <DialogContent className="mt-4">
                <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-primary/20 dark:border-gray-700 overflow-hidden">
                    {/* Onglets pour filtrer les patients */}
                    <div className="flex border-b border-gray-200 dark:border-gray-700">
                        <button
                            className={`flex-1 py-3 px-4 text-center font-medium text-sm border-b-2 ${activePatientTab === "consultations" ? "border-meddoc-primary text-meddoc-primary" : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`}
                            onClick={() => setActivePatientTab("consultations")}
                        >
                            <div className="flex items-center justify-center">
                                <Stethoscope className="w-4 h-4 mr-2" />
                                Consultations
                            </div>
                        </button>
                        <button
                            className={`flex-1 py-3 px-4 text-center font-medium text-sm border-b-2 ${activePatientTab === "signes-vitaux" ? "border-meddoc-primary text-meddoc-primary" : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`}
                            onClick={() => setActivePatientTab("signes-vitaux")}
                        >
                            <div className="flex items-center justify-center">
                                <Activity className="w-4 h-4 mr-2" />
                                Signes vitaux
                            </div>
                        </button>
                        <button
                            className={`flex-1 py-3 px-4 text-center font-medium text-sm border-b-2 ${activePatientTab === "laboratoire" ? "border-meddoc-primary text-meddoc-primary" : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`}
                            onClick={() => setActivePatientTab("laboratoire")}
                        >
                            <div className="flex items-center justify-center">
                                <FlaskConical className="w-4 h-4 mr-2" />
                                Laboratoire et examens
                            </div>
                        </button>
                    </div>
                    <div className="p-4">
                        {useMemo(() => {
                            if (activePatientTab === "consultations") {
                                return (
                                    <ListDataGrid
                                        data={consultationsPatient}
                                        type="historique_consultation_patient"
                                    />
                                );
                            } else if (activePatientTab === "signes-vitaux") {
                                return (
                                    <ListDataGrid
                                        data={signeVitauxPatient}
                                        type="signe_vitaux_patient"
                                    />
                                );
                            } else if (activePatientTab === "laboratoire") {
                                return (
                                    <ListDataGrid
                                        data={diagnosticPatient}
                                        type="laboratoire_diagnostics"
                                    />
                                );
                            }
                        }, [activePatientTab, consultationsPatient, signeVitauxPatient, data])}

                    </div>
                </div>
            </DialogContent>
            <DialogActions sx={{ p: 2 }}>
                <Button
                    variant="outlined"
                    onClick={handleCloseModal}
                    sx={{ textTransform: "none" }}
                >
                    Fermer
                </Button>
            </DialogActions>
        </Dialog>

    )
}
