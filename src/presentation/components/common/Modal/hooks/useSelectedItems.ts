import { useCallback } from "react";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";

/**
 * Hook personnalisé pour gérer les éléments sélectionnés du carnet de santé
 * Responsabilité : Gestion des éléments sélectionnés (ajout, suppression)
 * Utilise le hook existant useCarnetDeSante pour maintenir la compatibilité
 */
export const useSelectedItems = () => {
  const { selectedSearch, handleSearchChange, handleDeleteSelected } =
    useCarnetDeSante();

  // Gérer l'ajout d'un nouvel élément
  const handleItemAdd = useCallback(
    (item: string) => {
      if (item && !selectedSearch.includes(item)) {
        handleSearchChange(item);
      }
    },
    [selectedSearch, handleSearchChange]
  );

  // Gérer la suppression d'un élément sélectionné
  const handleItemDelete = useCallback(
    (item: string) => {
      handleDeleteSelected(item);
    },
    [handleDeleteSelected]
  );

  return {
    selectedItems: selectedSearch,
    handleItemAdd,
    handleItemDelete,
  };
};
