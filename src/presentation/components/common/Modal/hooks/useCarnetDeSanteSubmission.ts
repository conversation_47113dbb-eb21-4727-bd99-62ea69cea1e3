import { useCallback } from "react";
import { useCarnetDeSanteStockManagement } from "./useCarnetDeSanteStockManagement";

/**
 * Hook personnalisé pour gérer la soumission du formulaire de carnet de santé
 * avec gestion optionnelle des sorties de stock
 * 
 * Responsabilités :
 * - Gérer la soumission normale du formulaire
 * - Créer des sorties de stock si le mode stock est activé
 * - Fournir un feedback sur les opérations
 */
export const useCarnetDeSanteSubmission = () => {
  const {
    createStockSortieForPrescription,
    processMultiplePrescriptions,
  } = useCarnetDeSanteStockManagement();

  /**
   * Soumettre le formulaire avec gestion optionnelle du stock
   */
  const submitFormWithStockManagement = useCallback(async (
    formData: any,
    selectedItems: string[],
    useStockMode: boolean,
    patientInfo?: { nom?: string; id?: number }
  ): Promise<{
    success: boolean;
    message: string;
    stockResults?: Array<{ nom: string; success: boolean; message: string }>;
  }> => {
    try {
      // 1. Soumission normale du formulaire (logique existante)
      // Cette partie devrait appeler la logique de soumission existante
      // Pour l'instant, on simule une soumission réussie
      
      let stockResults: Array<{ nom: string; success: boolean; message: string }> = [];

      // 2. Si le mode stock est activé, créer les sorties de stock
      if (useStockMode && selectedItems.length > 0) {
        // Traiter chaque médicament sélectionné
        const prescriptions = selectedItems.map(item => ({
          nom: item,
          quantite: 1, // Quantité par défaut, pourrait être récupérée du formulaire
        }));

        stockResults = await processMultiplePrescriptions(prescriptions, patientInfo);
      }

      // 3. Analyser les résultats
      const stockFailures = stockResults.filter(result => !result.success);
      const stockSuccesses = stockResults.filter(result => result.success);

      let message = "Formulaire soumis avec succès";
      
      if (useStockMode) {
        if (stockSuccesses.length > 0) {
          message += `. ${stockSuccesses.length} sortie(s) de stock créée(s)`;
        }
        if (stockFailures.length > 0) {
          message += `. ${stockFailures.length} erreur(s) de stock`;
        }
      }

      return {
        success: true,
        message,
        stockResults,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur lors de la soumission';
      return {
        success: false,
        message: errorMessage,
        stockResults: [],
      };
    }
  }, [createStockSortieForPrescription, processMultiplePrescriptions]);

  /**
   * Valider les médicaments avant soumission (mode stock)
   */
  const validateStockBeforeSubmission = useCallback(async (
    selectedItems: string[]
  ): Promise<{
    valid: boolean;
    warnings: string[];
    errors: string[];
  }> => {
    const warnings: string[] = [];
    const errors: string[] = [];

    // Cette validation pourrait être étendue pour vérifier
    // les quantités disponibles, les seuils d'alerte, etc.

    return {
      valid: errors.length === 0,
      warnings,
      errors,
    };
  }, []);

  /**
   * Obtenir un aperçu des sorties de stock qui seront créées
   */
  const getStockPreview = useCallback((
    selectedItems: string[]
  ): Array<{ nom: string; quantite: number; available: boolean }> => {
    return selectedItems.map(item => ({
      nom: item,
      quantite: 1, // Quantité par défaut
      available: true, // Devrait être vérifié avec le stock réel
    }));
  }, []);

  return {
    // Fonctions principales
    submitFormWithStockManagement,
    validateStockBeforeSubmission,
    getStockPreview,
  };
};
