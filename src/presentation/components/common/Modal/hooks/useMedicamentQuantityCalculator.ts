import { useMemo } from "react";

/**
 * Hook pour calculer dynamiquement la quantité de médicament à sortir du stock
 * 
 * Formule : Fréquence par jour × Quantité par dosage × Nombre de jours
 * 
 * @param frequenceParJour - Nombre de prises par jour (ex: "Deux fois par jour" = 2)
 * @param quantiteParDosage - Quantité par prise (ex: "2 comprimés" = 2)
 * @param nombreDeJours - Durée du traitement en jours
 */
export const useMedicamentQuantityCalculator = (
  frequenceParJour: string | null,
  quantiteParDosage: string | null,
  nombreDeJours: number | null
) => {
  
  /**
   * Convertir la fréquence textuelle en nombre
   */
  const parseFrequence = useMemo(() => {
    if (!frequenceParJour) return 0;
    
    const frequenceMap: Record<string, number> = {
      "Une fois par jour": 1,
      "Deux fois par jour": 2,
      "Trois fois par jour": 3,
      "Quatre fois par jour": 4,
      "Toutes les 4 heures": 6, // 24h / 4h = 6 prises
      "Toutes les 6 heures": 4, // 24h / 6h = 4 prises
      "Toutes les 8 heures": 3, // 24h / 8h = 3 prises
      "Toutes les 12 heures": 2, // 24h / 12h = 2 prises
      "Au besoin": 1, // Par défaut 1 prise par jour
    };
    
    return frequenceMap[frequenceParJour] || 0;
  }, [frequenceParJour]);

  /**
   * Extraire le nombre de la quantité par dosage
   */
  const parseQuantiteParDosage = useMemo(() => {
    if (!quantiteParDosage) return 0;
    
    // Extraire le premier nombre trouvé dans la chaîne
    const match = quantiteParDosage.match(/(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : 1; // Par défaut 1 si pas de nombre trouvé
  }, [quantiteParDosage]);

  /**
   * Calculer la quantité totale nécessaire
   */
  const quantiteTotale = useMemo(() => {
    if (!parseFrequence || !parseQuantiteParDosage || !nombreDeJours) {
      return 0;
    }
    
    return parseFrequence * parseQuantiteParDosage * nombreDeJours;
  }, [parseFrequence, parseQuantiteParDosage, nombreDeJours]);

  /**
   * Informations détaillées du calcul
   */
  const calculDetails = useMemo(() => {
    return {
      frequenceParJour: parseFrequence,
      quantiteParDosage: parseQuantiteParDosage,
      nombreDeJours: nombreDeJours || 0,
      quantiteTotale,
      formule: `${parseFrequence} × ${parseQuantiteParDosage} × ${nombreDeJours || 0} = ${quantiteTotale}`,
      isValid: quantiteTotale > 0,
    };
  }, [parseFrequence, parseQuantiteParDosage, nombreDeJours, quantiteTotale]);

  /**
   * Formater la quantité pour l'affichage
   */
  const formatQuantite = useMemo(() => {
    if (quantiteTotale === 0) return "0";
    
    // Si c'est un nombre entier, afficher sans décimales
    if (quantiteTotale % 1 === 0) {
      return quantiteTotale.toString();
    }
    
    // Sinon, afficher avec 2 décimales maximum
    return quantiteTotale.toFixed(2).replace(/\.?0+$/, "");
  }, [quantiteTotale]);

  /**
   * Message d'explication du calcul
   */
  const calculExplanation = useMemo(() => {
    if (!calculDetails.isValid) {
      return "Veuillez remplir tous les champs pour calculer la quantité";
    }
    
    return `Calcul : ${calculDetails.frequenceParJour} prise(s)/jour × ${calculDetails.quantiteParDosage} unité(s)/prise × ${calculDetails.nombreDeJours} jour(s) = ${formatQuantite} unité(s) au total`;
  }, [calculDetails, formatQuantite]);

  return {
    // Valeurs calculées
    quantiteTotale,
    formatQuantite,
    
    // Détails du calcul
    calculDetails,
    calculExplanation,
    
    // Validation
    isValid: calculDetails.isValid,
    
    // Valeurs parsées
    frequenceParsee: parseFrequence,
    quantiteParsee: parseQuantiteParDosage,
  };
};
