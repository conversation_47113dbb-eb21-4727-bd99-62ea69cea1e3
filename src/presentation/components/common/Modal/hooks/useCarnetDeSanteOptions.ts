import { useState, useEffect, useMemo } from "react";
import { useOptionsCarnetDeSante } from "@/presentation/hooks/carnetDeSante/sousCarnet/useOptionsCarnetDeSante";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { useMedicamentDatabase } from "./useMedicamentDatabase";
import { useProfessionalStockMedicaments } from "./useProfessionalStockMedicaments";
import { ListeMedicaments } from "@/domain/models";

// Type pour les options
interface Option {
  nom: string;
  dosage?: string;
  forme?: string;
}

/**
 * Hook personnalisé pour gérer les options du carnet de santé et leur filtrage
 * Responsabilité : Chargement et filtrage des options disponibles selon le type
 * Migration Supabase : Utilise la base de données pour les médicaments, données JSON pour les autres types
 *
 * @param type - Type de carnet de santé
 * @param inputValue - Valeur de recherche
 * @param useStockMode - Si true, utilise le stock professionnel pour les médicaments
 */
export const useCarnetDeSanteOptions = (
  type: string,
  inputValue: string,
  useStockMode: boolean = false
) => {
  const { getOption } = useOptionsCarnetDeSante(type);
  const {
    medicaments,
    loading: medicamentsLoading,
    error: medicamentsError,
  } = useMedicamentDatabase();

  // Hook pour le stock professionnel (utilisé seulement si useStockMode = true)
  const {
    stockMedicaments,
    loading: stockLoading,
    error: stockError,
    filterStockMedicaments,
  } = useProfessionalStockMedicaments();

  const [options, setOptions] = useState<Option[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Limiter le nombre d'options affichées pour améliorer les performances
  const MAX_OPTIONS = 100;

  // Fonction pour convertir ListeMedicaments vers Option
  const convertMedicamentsToOptions = (
    medicamentsList: ListeMedicaments[]
  ): Option[] => {
    return medicamentsList.map((medicament) => ({
      nom: medicament.nom,
      dosage: medicament.dosage,
      forme: medicament.forme,
    }));
  };

  // Fonction pour convertir les médicaments du stock vers Option
  const convertStockMedicamentsToOptions = (
    stockMedicamentsList: any[]
  ): Option[] => {
    return stockMedicamentsList.map((medicament) => ({
      nom: medicament.nom,
      dosage: medicament.dosage || "",
      forme: medicament.forme || "",
    }));
  };

  // Charger les options selon le type
  useEffect(() => {
    setLoading(true);
    setError(null);

    try {
      if (type === TITRES_CARNET_DE_SANTE.medicaments) {
        if (useStockMode) {
          // Mode stock professionnel : utiliser les médicaments en stock
          if (stockError) {
            setError(stockError);
            setOptions([]);
          } else {
            const convertedOptions =
              convertStockMedicamentsToOptions(stockMedicaments);
            setOptions(convertedOptions);
          }
          setLoading(stockLoading);
        } else {
          // Mode normal : utiliser les données de la base de données globale
          if (medicamentsError) {
            setError(medicamentsError);
            setOptions([]);
          } else {
            const convertedOptions = convertMedicamentsToOptions(medicaments);
            setOptions(convertedOptions);
          }
          setLoading(medicamentsLoading);
        }
      } else {
        // Utiliser les données JSON pour les autres types
        const jsonOptions = getOption(type);
        setOptions(jsonOptions);
        setLoading(false);
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Erreur lors du chargement des options";
      setError(errorMessage);
      setOptions([]);
      setLoading(false);
    }
  }, [
    type,
    useStockMode,
    getOption,
    medicaments,
    medicamentsLoading,
    medicamentsError,
    stockMedicaments,
    stockLoading,
    stockError,
  ]);

  // Filtrer les options de manière optimisée avec useMemo
  const filteredOptions = useMemo(() => {
    if (!inputValue || inputValue.length < 2) {
      return options.slice(0, MAX_OPTIONS); // Limiter le nombre initial d'options
    }

    const search = inputValue.toLowerCase();

    // Filtrage spécifique pour les médicaments (nom, dosage, forme)
    if (type === TITRES_CARNET_DE_SANTE.medicaments) {
      return options
        .filter(
          (opt: Option) =>
            opt.nom.toLowerCase().includes(search) ||
            (opt.dosage && opt.dosage.toLowerCase().includes(search)) ||
            (opt.forme && opt.forme.toLowerCase().includes(search))
        )
        .slice(0, MAX_OPTIONS);
    }

    // Filtrage standard par nom pour les autres types
    return options
      .filter((opt: Option) => opt.nom.toLowerCase().includes(search))
      .slice(0, MAX_OPTIONS);
  }, [options, inputValue, type]);

  return {
    options,
    filteredOptions,
    loading,
    error,
    MAX_OPTIONS,
    // Fonctions pour la gestion du stock (disponibles seulement en mode stock)
    stockMedicaments: useStockMode ? stockMedicaments : [],
    isStockMode: useStockMode,
  };
};
