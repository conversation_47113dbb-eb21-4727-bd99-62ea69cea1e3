# Hooks du Composant CarnetDeSanteForm

## Vue d'ensemble

Cette refactorisation du composant `CarnetDeSanteForm` implémente le principe de responsabilité unique (Single Responsibility Principle) en séparant la logique métier en plusieurs custom hooks spécialisés.

## Architecture

### Hooks Créés

#### 1. `useCarnetDeSanteOptions`

**Responsabilité :** Gestion des options disponibles et du filtrage des données

- Chargement des options selon le type de carnet de santé
- **NOUVEAU :** Utilise la base de données pour les médicaments via `useMedicamentDatabase`
- Utilise les données JSON pour les autres types (compatibilité maintenue)
- Filtrage optimisé avec limitation du nombre d'options affichées
- Filtrage spécialisé pour les médicaments (nom, dosage, forme)
- Gestion des états de chargement et d'erreur

#### 2. `useCarnetDeSanteAutocomplete`

**Responsabilité :** Gestion de l'état de l'autocomplete

- Gestion de la valeur d'entrée (`inputValue`)
- Gestionnaires d'événements pour les changements d'entrée
- Factory pour créer des gestionnaires de changement personnalisés
- Fonction utilitaire pour obtenir les labels des options

#### 3. `useSelectedItems`

**Responsabilité :** Gestion des éléments sélectionnés

- Interface avec le hook existant `useCarnetDeSante`
- Ajout et suppression d'éléments sélectionnés
- Maintien de la compatibilité avec l'état global

#### 4. `useMedicamentDatabase` ⭐ **NOUVEAU**

**Responsabilité :** Récupération des médicaments depuis la base de données

- Utilise l'architecture Clean Architecture (Repository/UseCase pattern)
- Gestion des états de chargement, erreur et données
- Interface avec `GetMedicamentListCompleteUsecase`
- Fonction de rechargement manuel des données

#### 5. `useProfessionalStockMedicaments` ⭐ **NOUVEAU**

**Responsabilité :** Gestion des médicaments du stock professionnel

- Récupération des médicaments disponibles en stock
- Filtrage par catégorie "médicament"
- Calcul des quantités disponibles avec lots et entrées
- Création de sorties de stock automatiques
- Validation des quantités demandées

#### 6. `useCarnetDeSanteStockManagement` ⭐ **NOUVEAU**

**Responsabilité :** Interface haut niveau pour la gestion du stock

- Création de sorties de stock pour prescriptions
- Traitement de prescriptions multiples
- Vérification de disponibilité des médicaments
- Statistiques de stock (total, disponibles, stock faible)

#### 7. `useCarnetDeSanteSubmission` ⭐ **NOUVEAU**

**Responsabilité :** Soumission du formulaire avec gestion optionnelle du stock

- Soumission normale du formulaire (mode global)
- Création automatique de sorties de stock (mode stock interne)
- Validation avant soumission
- Aperçu des sorties de stock qui seront créées

## Avantages de la Refactorisation

### 1. **Maintenabilité**

- Chaque hook a une responsabilité claire et unique
- Code plus facile à comprendre et à modifier
- Séparation claire entre logique métier et rendu

### 2. **Réutilisabilité**

- Les hooks peuvent être réutilisés dans d'autres composants
- Logique modulaire et indépendante

### 3. **Testabilité**

- Chaque hook peut être testé indépendamment
- Logique métier isolée du rendu JSX

## 🆕 Nouvelle Fonctionnalité : Gestion du Stock Professionnel

### Vue d'ensemble

Le système supporte maintenant **deux modes de prescription** pour les médicaments :

1. **Mode Global (défaut)** : Utilise la base de données globale des médicaments
2. **Mode Stock Interne** : Utilise uniquement les médicaments disponibles en stock professionnel

### Interface Utilisateur

Une **checkbox** "Prescrire un médicament en stock" permet de basculer entre les modes :

```typescript
{type === TITRES_CARNET_DE_SANTE.medicaments && (
  <FormControlLabel
    control={
      <Checkbox
        checked={useStockMode}
        onChange={(e) => setUseStockMode(e.target.checked)}
      />
    }
    label="Prescrire un médicament en stock"
  />
)}
```

### Flux de Données

#### Mode Global (useStockMode = false)

1. `useMedicamentDatabase` → Base de données globale
2. `useCarnetDeSanteOptions` → Filtre et affiche toutes les options
3. Soumission normale sans impact sur le stock

#### Mode Stock Interne (useStockMode = true)

1. `useProfessionalStockMedicaments` → Stock professionnel via Redux
2. Filtrage par catégorie "médicament"
3. `useCarnetDeSanteOptions` → Affiche uniquement les médicaments en stock
4. `useCarnetDeSanteSubmission` → Crée les sorties de stock automatiquement

### Gestion Automatique des Sorties de Stock

Lors de la soumission en mode stock :

1. **Validation** : Vérification des quantités disponibles
2. **Création** : Sortie de stock avec type "utilisation"
3. **Traçabilité** : Destinataire = informations patient
4. **Feedback** : Messages de succès/erreur détaillés

### Configuration

Le système identifie les médicaments par catégorie :

```typescript
const medicamentCategoryIds = categories
  .filter(
    (category) =>
      category.nom.toLowerCase().includes("medicament") ||
      category.nom.toLowerCase().includes("médicament") ||
      category.nom.toLowerCase().includes("pharmacie")
  )
  .map((category) => category.id);
```

### 4. **Performance**

- Optimisations conservées (useMemo, useCallback)
- Limitation du nombre d'options affichées

## Migration Supabase - État Actuel

### ✅ **Phase 1 Terminée : Médicaments**

La migration vers Supabase est **partiellement terminée** pour les médicaments :

#### 1. `useMedicamentDatabase` - ✅ **IMPLÉMENTÉ**

- **Architecture Clean Architecture** : Repository/UseCase pattern respecté
- **Base de données Supabase** : Table `liste_medicaments` utilisée
- **Gestion d'erreurs** : États de chargement et d'erreur intégrés
- **Performance** : Chargement optimisé avec mise en cache

```typescript
// Architecture actuelle pour les médicaments
const repository = new GetMedicamentListCompleteRepository();
const usecase = new GetMedicamentListCompleteUsecase(repository);
const data = await usecase.execute(); // Appel Supabase
```

#### 2. `useCarnetDeSanteOptions` - ✅ **HYBRIDE**

- **Médicaments** : Utilise la base de données Supabase
- **Autres types** : Utilise encore les données JSON (compatibilité maintenue)

```typescript
if (type === TITRES_CARNET_DE_SANTE.medicaments) {
  // ✅ Base de données Supabase
  const convertedOptions = convertMedicamentsToOptions(medicaments);
  setOptions(convertedOptions);
} else {
  // ⏳ Données JSON (à migrer)
  const jsonOptions = getOption(type);
  setOptions(jsonOptions);
}
```

### 🔄 **Prochaines Phases**

#### Phase 2 : Migration des autres types

- Allergies, dispositifs médicaux, vaccinations, etc.
- Création de repositories et use cases similaires
- Migration progressive type par type

#### Phase 3 : Optimisations avancées

- Mise en cache intelligente
- Synchronisation temps réel
- Pagination et recherche optimisée

## Compatibilité

- ✅ Interface publique identique (props du composant)
- ✅ Fonctionnalités utilisateur inchangées
- ✅ Intégration avec les hooks existants préservée
- ✅ Performance maintenue ou améliorée

## Utilisation

```typescript
import { CarnetDeSanteForm } from './CarnetDeSanteForm';

// Utilisation identique à l'ancienne version
<CarnetDeSanteForm type={TITRES_CARNET_DE_SANTE.medicaments} />
```

## Structure des Fichiers

```
hooks/
├── index.ts                           # Export centralisé
├── useCarnetDeSanteOptions.ts        # Gestion des options (hybride JSON/DB)
├── useCarnetDeSanteAutocomplete.ts   # Gestion de l'autocomplete
├── useSelectedItems.ts               # Gestion des éléments sélectionnés
├── useMedicamentDatabase.ts          # ⭐ Récupération médicaments (Supabase)
└── README.md                         # Documentation
```
