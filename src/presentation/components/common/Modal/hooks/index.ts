/**
 * Index des hooks personnalisés pour le composant CarnetDeSanteForm
 *
 * Ces hooks implémentent le principe de responsabilité unique (SRP) en séparant
 * les différentes préoccupations du composant CarnetDeSanteForm :
 *
 * - useCarnetDeSanteOptions : Gestion des options et du filtrage
 * - useCarnetDeSanteAutocomplete : Gestion de l'état de l'autocomplete
 * - useSelectedItems : Gestion des éléments sélectionnés
 *
 * Cette architecture facilite la maintenance, les tests et la migration future vers Supabase.
 */

export { useCarnetDeSanteOptions } from "./useCarnetDeSanteOptions";
export { useCarnetDeSanteAutocomplete } from "./useCarnetDeSanteAutocomplete";
export { useSelectedItems } from "./useSelectedItems";
export { useProfessionalStockMedicaments } from "./useProfessionalStockMedicaments";
export { useCarnetDeSanteStockManagement } from "./useCarnetDeSanteStockManagement";
export { useCarnetDeSanteSubmission } from "./useCarnetDeSanteSubmission";
export { useMedicamentDatabase } from "./useMedicamentDatabase";
export { useMedicamentQuantityCalculator } from "./useMedicamentQuantityCalculator";
