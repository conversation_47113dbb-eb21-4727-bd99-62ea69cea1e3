import { useState, useCallback } from "react";

/**
 * Hook pour gérer l'état global du mode stock dans le formulaire
 * Permet de partager l'état entre le formulaire et le modal
 */
export const useStockModeState = () => {
  const [useStockMode, setUseStockMode] = useState<boolean>(false);

  const toggleStockMode = useCallback((enabled: boolean) => {
    setUseStockMode(enabled);
  }, []);

  const resetStockMode = useCallback(() => {
    setUseStockMode(false);
  }, []);

  return {
    useStockMode,
    setUseStockMode: toggleStockMode,
    resetStockMode,
  };
};
