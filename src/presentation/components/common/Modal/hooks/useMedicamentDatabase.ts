import { useState, useEffect, useCallback } from "react";
import { ListeMedicaments } from "@/domain/models";
import GetMedicamentListCompleteRepository from "@/infrastructure/repositories/medicament/GetMedicamentListCompleteRepository";
import GetMedicamentListCompleteUsecase from "@/domain/usecases/medicament/GetMedicamentListCompleteUsecase";

/**
 * Hook personnalisé pour récupérer les médicaments depuis la base de données
 * Responsabilité : Gestion de la récupération des données des médicaments via l'architecture Clean Architecture
 * Utilise le pattern Repository/UseCase pour maintenir la séparation des responsabilités
 */
export const useMedicamentDatabase = () => {
  const [medicaments, setMedicaments] = useState<ListeMedicaments[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Initialisation des dépendances selon l'architecture Clean Architecture
  const repository = new GetMedicamentListCompleteRepository();
  const usecase = new GetMedicamentListCompleteUsecase(repository);

  // Fonction pour charger les médicaments depuis la base de données
  const loadMedicaments = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await usecase.execute();
      setMedicaments(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur lors du chargement des médicaments";
      setError(errorMessage);
      console.error("Erreur lors du chargement des médicaments:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Charger les médicaments au montage du composant
  useEffect(() => {
    loadMedicaments();
  }, [loadMedicaments]);

  // Fonction pour recharger les données manuellement
  const refetch = useCallback(() => {
    loadMedicaments();
  }, [loadMedicaments]);

  return {
    medicaments,
    loading,
    error,
    refetch,
  };
};
