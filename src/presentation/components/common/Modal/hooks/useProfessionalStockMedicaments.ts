import { useState, useEffect, useMemo } from "react";
import { useAppSelector, useAppDispatch } from "@/presentation/hooks/redux";
import {
  getProduits,
  getCategories,
  getEntrees,
  getLots,
  createSortieStock,
} from "@/application/slices/professionnal/professionalStockSlice";
import { SortieStockDTO } from "@/domain/DTOS/StockDTO";

// Interface pour les options de médicaments du stock
interface StockMedicamentOption {
  id: number;
  nom: string;
  dosage?: string;
  forme?: string;
  quantiteDisponible: number;
  unite?: string;
  seuil_alerte?: number;
}

/**
 * Hook personnalisé pour gérer les médicaments du stock professionnel
 *
 * Responsabilités :
 * - Récupérer les médicaments disponibles en stock
 * - Filtrer par catégorie "médicament"
 * - Créer des sorties de stock automatiques
 * - Gérer les états de chargement et d'erreur
 */
export const useProfessionalStockMedicaments = () => {
  const dispatch = useAppDispatch();
  const {
    produits,
    categories,
    lots,
    entrees,
    loading: stockLoading,
  } = useAppSelector((state) => state.professionalStock);
  const user = useAppSelector((state) => state?.authentification?.user);

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [stockMedicaments, setStockMedicaments] = useState<
    StockMedicamentOption[]
  >([]);

  // Charger les données initiales
  useEffect(() => {
    const loadStockData = async () => {
      if (!user?.id) return;

      setLoading(true);
      setError(null);

      try {
        // Charger toutes les données de stock en parallèle si pas déjà chargées
        const promises = [];

        if (!produits || produits.length === 0) {
          promises.push(dispatch(getProduits(user.id)).unwrap());
        }
        if (!categories || categories.length === 0) {
          promises.push(dispatch(getCategories()).unwrap());
        }
        if (!entrees || entrees.length === 0) {
          promises.push(dispatch(getEntrees(user.id)).unwrap());
        }
        if (!lots || lots.length === 0) {
          promises.push(dispatch(getLots(user.id)).unwrap());
        }

        if (promises.length > 0) {
          await Promise.all(promises);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Erreur lors du chargement du stock";
        console.error("Erreur lors du chargement des données de stock:", err);
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    loadStockData();
  }, [
    dispatch,
    user?.id,
    produits.length,
    categories.length,
    entrees.length,
    lots.length,
  ]);

  // Calculer les médicaments en stock avec leurs quantités
  const medicamentsEnStock = useMemo(() => {
    if (!produits || !categories || !lots || !entrees) {
      return [];
    }

    try {
      // Filtrer les catégories de médicaments par type
      const medicamentCategoryIds = categories
        .filter(
          (category) =>
            category.nom.toLowerCase().includes("medicament") ||
            category.nom.toLowerCase().includes("médicament")
        )
        .map((category) => category.id);

      // SOLUTION TEMPORAIRE : Si aucune catégorie médicament trouvée, utiliser tous les produits
      let medicamentProduits;
      if (medicamentCategoryIds.length === 0) {
        // Utiliser tous les produits comme solution temporaire
        medicamentProduits = produits;
      } else {
        // Filtrer normalement par catégorie
        medicamentProduits = produits.filter(
          (produit) =>
            produit.categorie_id &&
            medicamentCategoryIds.includes(produit.categorie_id)
        );
      }

      // Calculer les quantités disponibles pour chaque médicament
      const medicamentsAvecQuantites: StockMedicamentOption[] =
        medicamentProduits.map((produit: any) => {
          // Trouver les entrées pour ce produit
          const produitsEntrees = entrees.filter(
            (entree) => entree.stock_id === produit.id
          );

          // Calculer la quantité totale disponible
          const quantiteTotale = lots
            .filter((lot) => {
              const entreeCorrespondante = produitsEntrees.find(
                (entree) => entree.id === lot.entree_id
              );
              return entreeCorrespondante && lot.quantite > 0;
            })
            .reduce((total, lot) => total + lot.quantite, 0);

          // Extraire dosage et forme du nom si possible
          let dosage = "";
          let forme = "";

          // Essayer d'extraire le dosage (ex: "500mg", "250ml")
          const dosageMatch = produit.nom.match(
            /(\d+(?:\.\d+)?(?:mg|ml|g|l|%|ui|iu))/i
          );
          if (dosageMatch) {
            dosage = dosageMatch[1];
          }

          // Essayer d'extraire la forme (ex: "comprimé", "gélule", "sirop")
          const formeKeywords = [
            "comprimé",
            "gélule",
            "sirop",
            "solution",
            "pommade",
            "crème",
            "injection",
          ];
          const formeFound = formeKeywords.find(
            (keyword) =>
              produit.nom.toLowerCase().includes(keyword) ||
              (produit.description &&
                produit.description.toLowerCase().includes(keyword))
          );
          if (formeFound) {
            forme = formeFound;
          }

          return {
            id: produit.id,
            nom: produit.nom,
            dosage,
            forme,
            quantiteDisponible: quantiteTotale,
            unite: produit.unite || "unité",
            seuil_alerte: produit.seuil_alerte || 0,
          };
        });

      // Filtrer pour ne garder que les médicaments avec stock > 0
      return medicamentsAvecQuantites.filter(
        (medicament) => medicament.quantiteDisponible > 0
      );
    } catch (err) {
      console.error("Erreur lors du calcul des médicaments en stock:", err);
      setError("Erreur lors du calcul des quantités en stock");
      return [];
    }
  }, [produits, categories, lots, entrees]);

  // Mettre à jour l'état des médicaments en stock
  useEffect(() => {
    setStockMedicaments(medicamentsEnStock);
  }, [medicamentsEnStock]);

  /**
   * Créer une sortie de stock pour un médicament prescrit
   */
  const createMedicamentSortie = async (
    medicamentId: number,
    quantite: number,
    patientNom?: string,
    patientId?: number
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const sortieData: SortieStockDTO = {
        stock_id: medicamentId,
        quantite: quantite,
        type_sortie: "utilisation", // Type pour les prescriptions
        destinataire:
          patientNom || `Patient ID: ${patientId}` || "Patient non spécifié",
        date_sortie: new Date().toISOString(),
      };

      await dispatch(createSortieStock(sortieData)).unwrap();

      return {
        success: true,
        message: `Sortie de stock créée avec succès pour ${quantite} unités`,
      };
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Erreur lors de la création de la sortie";
      return {
        success: false,
        message: errorMessage,
      };
    }
  };

  /**
   * Vérifier si une quantité est disponible pour un médicament
   */
  const isQuantiteDisponible = (
    medicamentId: number,
    quantiteDemandee: number
  ): boolean => {
    const medicament = stockMedicaments.find((m) => m.id === medicamentId);
    return medicament
      ? medicament.quantiteDisponible >= quantiteDemandee
      : false;
  };

  /**
   * Obtenir les informations d'un médicament en stock
   */
  const getMedicamentInfo = (
    medicamentId: number
  ): StockMedicamentOption | undefined => {
    return stockMedicaments.find((m) => m.id === medicamentId);
  };

  /**
   * Filtrer les médicaments en stock par terme de recherche
   */
  const filterStockMedicaments = (
    searchTerm: string
  ): StockMedicamentOption[] => {
    if (!searchTerm || searchTerm.length < 2) {
      return stockMedicaments.slice(0, 50); // Limiter à 50 résultats par défaut
    }

    const search = searchTerm.toLowerCase();
    return stockMedicaments.filter(
      (medicament) =>
        medicament.nom.toLowerCase().includes(search) ||
        (medicament.dosage &&
          medicament.dosage.toLowerCase().includes(search)) ||
        (medicament.forme && medicament.forme.toLowerCase().includes(search))
    );
  };

  return {
    // Données
    stockMedicaments,
    medicamentsCount: stockMedicaments.length,

    // États
    loading: loading || stockLoading,
    error,

    // Fonctions
    createMedicamentSortie,
    isQuantiteDisponible,
    getMedicamentInfo,
    filterStockMedicaments,

    // Données brutes pour debug
    categories,
    produits,
  };
};
