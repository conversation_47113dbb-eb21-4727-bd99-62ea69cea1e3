import { useState, useCallback } from "react";
import {
  AutocompleteChangeReason,
  AutocompleteInputChangeReason,
} from "@mui/material/Autocomplete";

// Type pour les options
interface Option {
  nom: string;
  dosage?: string;
  forme?: string;
}

/**
 * Hook personnalisé pour gérer l'état de l'autocomplete du carnet de santé
 * Responsabilité : Gestion de l'état de saisie et des interactions utilisateur avec l'autocomplete
 */
export const useCarnetDeSanteAutocomplete = () => {
  // État pour contrôler l'entrée de recherche
  const [inputValue, setInputValue] = useState("");

  // Gérer le changement de l'entrée de recherche
  const handleInputChange = useCallback(
    (
      _: React.SyntheticEvent,
      newInputValue: string,
      reason: AutocompleteInputChangeReason
    ) => {
      setInputValue(newInputValue);
    },
    []
  );

  // Créer un gestionnaire de changement de valeur personnalisé
  const createChangeHandler = useCallback(
    (selectedItems: string[], onItemAdd: (item: string) => void) => {
      return (
        _: React.SyntheticEvent,
        newValue: string | Option | null,
        reason: AutocompleteChangeReason
      ) => {
        if (!newValue || reason === "clear") return;

        const nom = typeof newValue === "string" ? newValue : newValue.nom;
        if (nom && !selectedItems.includes(nom)) {
          onItemAdd(nom);
        }
      };
    },
    []
  );

  // Fonction utilitaire pour obtenir le label d'une option
  const getOptionLabel = useCallback((option: string | Option) => {
    return typeof option === "string" ? option : option.nom;
  }, []);

  return {
    inputValue,
    handleInputChange,
    createChangeHandler,
    getOptionLabel,
  };
};
