import { useCallback } from "react";
import { useProfessionalStockMedicaments } from "./useProfessionalStockMedicaments";

/**
 * Hook personnalisé pour gérer les sorties de stock dans le contexte du carnet de santé
 *
 * Responsabilités :
 * - Créer des sorties de stock lors de la soumission du formulaire
 * - Valider les quantités disponibles
 * - Fournir des informations sur les médicaments en stock
 */
export const useCarnetDeSanteStockManagement = () => {
  const {
    createMedicamentSortie,
    isQuantiteDisponible,
    getMedicamentInfo,
    stockMedicaments,
    loading,
    error,
  } = useProfessionalStockMedicaments();

  /**
   * Créer une sortie de stock pour un médicament prescrit dans le carnet de santé
   */
  const createStockSortieForPrescription = useCallback(
    async (
      medicamentNom: string,
      quantite: number = 1,
      patientInfo?: { nom?: string; id?: number }
    ): Promise<{
      success: boolean;
      message: string;
      medicamentId?: number;
    }> => {
      try {
        // Trouver le médicament en stock par nom
        const medicament = stockMedicaments.find(
          (m) =>
            m.nom.toLowerCase() === medicamentNom.toLowerCase() ||
            m.nom.toLowerCase().includes(medicamentNom.toLowerCase()) ||
            medicamentNom.toLowerCase().includes(m.nom.toLowerCase())
        );

        if (!medicament) {
          return {
            success: false,
            message: `Médicament "${medicamentNom}" non trouvé en stock`,
          };
        }

        // Vérifier la quantité disponible
        if (!isQuantiteDisponible(medicament.id, quantite)) {
          return {
            success: false,
            message: `Quantité insuffisante en stock pour "${medicamentNom}". Disponible: ${medicament.quantiteDisponible}, Demandé: ${quantite}`,
          };
        }

        // Créer la sortie de stock
        const result = await createMedicamentSortie(
          medicament.id,
          quantite,
          patientInfo?.nom,
          patientInfo?.id
        );

        return {
          ...result,
          medicamentId: medicament.id,
        };
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Erreur lors de la création de la sortie de stock";
        return {
          success: false,
          message: errorMessage,
        };
      }
    },
    [stockMedicaments, isQuantiteDisponible, createMedicamentSortie]
  );

  /**
   * Traiter plusieurs prescriptions de médicaments avec sorties de stock
   */
  const processMultiplePrescriptions = useCallback(
    async (
      prescriptions: Array<{ nom: string; quantite?: number }>,
      patientInfo?: { nom?: string; id?: number }
    ): Promise<Array<{ nom: string; success: boolean; message: string }>> => {
      const results = [];

      for (const prescription of prescriptions) {
        const result = await createStockSortieForPrescription(
          prescription.nom,
          prescription.quantite || 1,
          patientInfo
        );

        results.push({
          nom: prescription.nom,
          success: result.success,
          message: result.message,
        });
      }

      return results;
    },
    [createStockSortieForPrescription]
  );

  /**
   * Vérifier si un médicament est disponible en stock
   */
  const checkMedicamentAvailability = useCallback(
    (
      medicamentNom: string,
      quantite: number = 1
    ): { available: boolean; medicament?: any; message: string } => {
      const medicament = stockMedicaments.find(
        (m) =>
          m.nom.toLowerCase() === medicamentNom.toLowerCase() ||
          m.nom.toLowerCase().includes(medicamentNom.toLowerCase()) ||
          medicamentNom.toLowerCase().includes(m.nom.toLowerCase())
      );

      if (!medicament) {
        return {
          available: false,
          message: `Médicament "${medicamentNom}" non trouvé en stock`,
        };
      }

      const hasEnoughQuantity = isQuantiteDisponible(medicament.id, quantite);

      return {
        available: hasEnoughQuantity,
        medicament,
        message: hasEnoughQuantity
          ? `Médicament disponible (${medicament.quantiteDisponible} ${medicament.unite})`
          : `Quantité insuffisante. Disponible: ${medicament.quantiteDisponible}, Demandé: ${quantite}`,
      };
    },
    [stockMedicaments, isQuantiteDisponible]
  );

  /**
   * Obtenir des statistiques sur les médicaments en stock
   */
  const getStockStatistics = useCallback(() => {
    const totalMedicaments = stockMedicaments.length;
    const medicamentsStockFaible = stockMedicaments.filter(
      (m) => m.quantiteDisponible <= m.seuil_alerte
    ).length;
    const medicamentsDisponibles = stockMedicaments.filter(
      (m) => m.quantiteDisponible > 0
    ).length;

    return {
      totalMedicaments,
      medicamentsDisponibles,
      medicamentsStockFaible,
      pourcentageDisponible:
        totalMedicaments > 0
          ? Math.round((medicamentsDisponibles / totalMedicaments) * 100)
          : 0,
    };
  }, [stockMedicaments]);

  return {
    // Fonctions principales
    createStockSortieForPrescription,
    processMultiplePrescriptions,
    checkMedicamentAvailability,

    // Utilitaires
    getStockStatistics,
    getMedicamentInfo,

    // Données
    stockMedicaments,
    medicamentsCount: stockMedicaments.length,

    // États
    loading,
    error,
  };
};
