import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
  Box,
  Divider,
} from "@mui/material";
import { X } from "lucide-react";
import { useEffect } from "react";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";
import { getTableNameByTitle } from "@/shared/utils/getTableNameByTitle";
import LoadingSpinner from "../LoadingSpinner";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useHistoriqueCarnetSante } from "@/presentation/hooks/carnetDeSante/sousCarnet/useHistoriqueCarnetSante";
import useAuth from "@/presentation/hooks/use-auth";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import HeaderStylePatient from "../../features/patient/PatientCard/HeaderStylePatient";
import HeaderStyleProche from "../../features/proche/HeaderStyleProche";
import HeaderStyleEmployer from "../../features/professional/employer/HeaderStyleEmployer";

interface HistoriqueCarnetDeSanteModalProps {
  type: string;
  isHistoriqueCarnetDeSanteModalOpen: boolean;
  handleCloseModal: () => void;
}

export const HistoriqueCarnetDeSanteModal = ({
  type,
  isHistoriqueCarnetDeSanteModalOpen,
  handleCloseModal,
}: HistoriqueCarnetDeSanteModalProps) => {
  const {
    historiqueCarnetSantes,
    loading,
    get: fetchHistoriqueTable,
  } = useHistoriqueCarnetSante();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { roleUserSelected } = useAuth();
  const { idCarnetSante } = useCarnetDeSante();
  const tableName = getTableNameByTitle(type);

  useEffect(() => {
    if (type) {
      fetchHistoriqueTable(idCarnetSante, tableName);
    }
  }, [type, idCarnetSante, fetchHistoriqueTable]);

  return (
    <Dialog
      open={isHistoriqueCarnetDeSanteModalOpen}
      onClose={handleCloseModal}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <Typography variant="h6" component="div">
          {role === utilisateurs_role_enum.PROFESSIONNEL ? (
            roleUserSelected === utilisateurs_role_enum.PATIENT ? (
              <HeaderStylePatient />
            ) : (
              <HeaderStyleProche />
            )
          ) : roleUserSelected === utilisateurs_role_enum.EMPLOYER ? (
            <HeaderStyleEmployer />
          ) : (
            <HeaderStyleProche />
          )}
        </Typography>
        <Tooltip title="Fermer">
          <IconButton onClick={handleCloseModal} size="small">
            <X className="h-4 w-4 text-white" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="mt-4 min-h-[0] md:min-h-[64.8vh]">
        {loading ? (
          <LoadingSpinner />
        ) : (
          <Box className="space-y-4">
            {historiqueCarnetSantes.length === 0 ? (
              <Typography
                variant="body1"
                className="text-center text-gray-500 py-4"
              >
                Aucun historique disponible
              </Typography>
            ) : (
              historiqueCarnetSantes.map((historique) => (
                <Box
                  key={historique.id}
                  className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                  sx={{
                    backgroundColor: "background.paper",
                    borderColor: "divider",
                  }}
                >
                  <Box className="flex justify-between items-start mb-2">
                    <Box>
                      <Typography variant="subtitle1" className="font-medium">
                        {historique.type_action}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(historique.date_action).toLocaleDateString(
                          "fr-FR",
                          {
                            day: "numeric",
                            month: "long",
                            year: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          }
                        )}
                      </Typography>
                    </Box>
                    {historique.professionnel && (
                      <Box
                        className="px-2 py-1 rounded-full text-xs"
                        sx={{
                          backgroundColor: "primary.light",
                          color: "primary.contrastText",
                        }}
                      >
                        {historique.professionnel.id === professionalId
                          ? "Vous"
                          : historique.professionnel.nom}
                      </Box>
                    )}
                  </Box>
                  <Divider className="my-2" />
                  <Typography variant="body1" className="whitespace-pre-wrap">
                    {historique.details}
                  </Typography>
                </Box>
              ))
            )}
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button
          variant="outlined"
          onClick={handleCloseModal}
          sx={{ textTransform: "none" }}
        >
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
