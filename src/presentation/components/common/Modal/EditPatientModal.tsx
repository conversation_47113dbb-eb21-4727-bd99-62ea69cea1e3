import {
    Dialog,
    DialogTitle,
    DialogContent,
    IconButton,
    Typography,
    Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { Patient } from "@/domain/models";
import ProfilePatient from "@/presentation/pages/shared/patients/ProfilePatient";

interface EditPatientModalProps {
    isOpen: boolean;
    handleClose: () => void;
    patient: Patient;
}

export const EditPatientModal = ({
    isOpen,
    handleClose,
    patient,
}: EditPatientModalProps) => {
    return (
        <Dialog open={isOpen} onClose={handleClose} maxWidth="md" fullWidth>
            <DialogTitle className="flex justify-between items-center bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white">
                <Typography variant="h6" component="div" className="text-center w-full">
                    Modifier le patient
                </Typography>
                <Tooltip title="Fermer">
                    <IconButton onClick={handleClose} size="small">
                        <X className="h-4 w-4" />
                    </IconButton>
                </Tooltip>
            </DialogTitle>
            <DialogContent sx={{ p: 2 }}>
                <ProfilePatient
                    handleBack={handleClose}
                    patient={patient}
                />
            </DialogContent>
        </Dialog>
    );
}



