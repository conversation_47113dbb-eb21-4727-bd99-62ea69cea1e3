import {
  Dialog,
  DialogContent,
  DialogActions,
  <PERSON><PERSON>ield,
  <PERSON><PERSON><PERSON>,
  Button,
  DialogTitle,
} from "@mui/material";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { PRIMARY } from "@/shared/constants/Color";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { useState } from "react";
import { AnnulerRendezVous } from "@/domain/models/AnnulerRendezVous";

interface CancelAppointmentModalProps {
  appointment: AppointmentProfessionalDTO;
  isCancelAppointmentModalOpen: boolean;
  handleCloseCancelAppointmentModal: () => void;
}

const CancelAppointmentModal = ({
  appointment,
  isCancelAppointmentModalOpen,
  handleCloseCancelAppointmentModal,
}: CancelAppointmentModalProps) => {
  const {
    loading,
    handleCancelAppointment,
    handleCancelAppointmentByProfessional,
    fetchAppointmentListByProfessionalId,
  } = useConsultationState();
  const [cancelReason, setCancelReason] = useState("");

  const onCancelAppointment = async () => {
    try {
      const appointmentData: Omit<AnnulerRendezVous, "id"> = {
        id_rendez_vous: appointment.id,
        motif: cancelReason,
      };
      await handleCancelAppointment({ ...appointment });
      handleCancelAppointmentByProfessional(appointmentData);
      // Rafraîchir la liste des rendez-vous après l'annulation
      await fetchAppointmentListByProfessionalId(appointment.id_professionnel);
      handleCloseCancelAppointmentModal();
    } catch (error) {
      console.error("Erreur lors de l'annulation du rendez-vous:", error);
    }
  };

  return (
    <Dialog
      open={isCancelAppointmentModalOpen}
      onClose={handleCloseCancelAppointmentModal}
      maxWidth="sm"
    >
      <DialogTitle className="flex justify-between items-center bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <Typography variant="h6">
          Annuler le rendez-vous
        </Typography>
      </DialogTitle>
      <DialogContent className="my-2">
        <Typography>
          Voulez-vous annuler le rendez-vous avec {appointment?.patient.nom}{" "}
          {appointment?.patient.prenom}?
        </Typography>
        <TextField
          value={cancelReason}
          onChange={(e) => setCancelReason(e.target.value)}
          label="Raison de l'annulation"
          fullWidth
          sx={{ mt: 2 }}
        />
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleCloseCancelAppointmentModal}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={onCancelAppointment}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          disabled={loading || !cancelReason}
        >
          {loading ? "Annulation en cours..." : "Confirmer"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CancelAppointmentModal;
