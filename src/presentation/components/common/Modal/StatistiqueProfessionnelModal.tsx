import {
    Dialog,
    DialogTitle,
    DialogContent,
    <PERSON>alogA<PERSON>,
    IconButton,
    Typography,
    Button,
    Tooltip,
} from "@mui/material";
import { Activity, AlertCircle, Calendar, CheckCircle, Clock1, Heart, Pie<PERSON><PERSON>, X } from "lucide-react";
import { motion } from "framer-motion";
import PrincipalesPathologies from "@/presentation/components/features/dash/dashboard/PrincipalesPathologies";
import PrincipauxMedicaments from "@/presentation/components/features/dash/dashboard/PrincipauxMedicaments";
import PatientDistributionChart from "../../features/professional/appointment/stats/PatientDistributionChart";
import { Clock } from "@mui/x-date-pickers/TimeClock/Clock";

interface StatistiqueProfessionnelModalProps {
    isModalOpen: boolean;
    handleCloseModal: () => void;
    pathologiesData: {
        name: any;
        count: any;
        color: string;
    }[];
    medicamentsData: {
        name: any;
        count: any;
        color: string;
    }[];
    data: {
        name: string;
        value: number;
        color: string;
    }[];
    appointmentStats: {
        completed: number;
        upcoming: number;
        cancelled: number;
        totalThisMonth: number;
        completedThisMonth: number;
    };
}

export const StatistiqueProfessionnelModal = ({
    isModalOpen,
    handleCloseModal,
    pathologiesData,
    medicamentsData,
    data,
    appointmentStats,
}: StatistiqueProfessionnelModalProps) => {
    return (
        <Dialog
            open={isModalOpen}
            onClose={handleCloseModal}
            maxWidth="lg"
            fullWidth
        >
            <DialogTitle className="flex justify-between items-center bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
                <Typography variant="h6" component="div">
                    Statistique : Consulter les données détaillées
                </Typography>
                <Tooltip title="Fermer">
                    <IconButton onClick={handleCloseModal} size="small">
                        <X className="h-4 w-4 text-white" />
                    </IconButton>
                </Tooltip>
            </DialogTitle>
            <DialogContent>
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-6"
                >
                    {/* Statistiques des rendez-vous */}
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-fonce/20 dark:border-gray-700 overflow-hidden">
                        <div className="px-4 py-3 bg-gradient-to-r from-meddoc-fonce/10 to-meddoc-fonce/5 dark:from-meddoc-fonce/20 dark:to-meddoc-fonce/10 border-b border-meddoc-fonce/20 dark:border-gray-600">
                            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                                <Activity className="w-4 h-4 mr-2 text-meddoc-fonce" />
                                Statistiques des Rendez-vous
                            </h2>
                        </div>
                        <div className="p-4">
                            <div className="grid grid-cols-2 gap-3">
                                <div className="bg-gradient-to-br from-meddoc-secondary/10 to-meddoc-secondary/5 rounded-lg p-3 text-center border border-meddoc-secondary/20">
                                    <CheckCircle className="h-6 w-6 text-meddoc-secondary mx-auto mb-1" />
                                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                                        {appointmentStats.completed}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Complétés
                                    </p>
                                </div>
                                <div className="bg-gradient-to-br from-meddoc-primary/10 to-meddoc-primary/5 rounded-lg p-3 text-center border border-meddoc-primary/20">
                                    <Clock1 className="h-6 w-6 text-meddoc-primary mx-auto mb-1" />
                                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                                        {appointmentStats.upcoming}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        À venir
                                    </p>
                                </div>
                                <div className="bg-gradient-to-br from-meddoc-orange/10 to-meddoc-orange/5 rounded-lg p-3 text-center border border-meddoc-orange/20">
                                    <AlertCircle className="h-6 w-6 text-meddoc-orange mx-auto mb-1" />
                                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                                        {appointmentStats.cancelled}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Annulés
                                    </p>
                                </div>
                                <div className="bg-gradient-to-br from-meddoc-fonce/10 to-meddoc-fonce/5 rounded-lg p-3 text-center border border-meddoc-fonce/20">
                                    <Calendar className="h-6 w-6 text-meddoc-fonce mx-auto mb-1" />
                                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                                        {appointmentStats.totalThisMonth}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Total du mois
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-meddoc-secondary/20 dark:border-gray-700 overflow-hidden">
                        <div className="px-4 py-3 bg-gradient-to-r from-meddoc-secondary/10 to-meddoc-secondary/5 dark:from-meddoc-secondary/20 dark:to-meddoc-secondary/10 border-b border-meddoc-secondary/20 dark:border-gray-600">
                            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                                <PieChart className="w-4 h-4 mr-2 text-meddoc-secondary" />
                                Distribution Patients
                            </h2>
                        </div>
                        <div className="p-4">
                            <div className="h-64 flex items-center justify-center">
                                <PatientDistributionChart data={data} />
                            </div>
                        </div>
                    </div>
                    {/* Principales pathologies */}
                    <PrincipalesPathologies pathologiesData={pathologiesData} />

                    {/* Les 5 principaux médicaments */}
                    <PrincipauxMedicaments medicamentsData={medicamentsData} />

                </motion.div>
            </DialogContent>
            <DialogActions sx={{ p: 2 }}>
                <Button
                    variant="outlined"
                    onClick={handleCloseModal}
                    sx={{ textTransform: "none" }}
                >
                    Fermer
                </Button>
            </DialogActions>
        </Dialog>
    );
};