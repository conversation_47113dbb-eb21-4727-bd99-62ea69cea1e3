import {
  Dialog,
  DialogTitle,
  DialogActions,
  Typography,
  DialogContent,
  <PERSON><PERSON>,
  Box,
} from "@mui/material";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { PRIMARY } from "@/shared/constants/Color";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { PROFESSIONAL_CARD_CLASSNAMES } from "@/shared/constants/professionalCardConstants";
import Availability from "@/presentation/components/features/professional/professionalCard/Availability";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useAvailability } from "@/presentation/hooks/use-availability";
import { useRef } from "react";

interface PostponeAppointmentModalProps {
  appointment: AppointmentProfessionalDTO;
  isPostponeAppointmentModalOpen: boolean;
  handleClosePostponeAppointmentModal: () => void;
}

const PostponeAppointmentModal = ({
  appointment,
  isPostponeAppointmentModalOpen,
  handleClosePostponeAppointmentModal,
}: PostponeAppointmentModalProps) => {
  const horairesRef = useRef<HTMLDivElement>(null);
  const { currentProfessional } = useSearchProfessional();
  const {
    loading,
    selectedTimeSlot,
    handlePostponeAppointment,
    fetchAppointmentListByProfessionalId,
  } = useConsultationState();
  const { handleResetTimeSlot } = useAvailability();

  const onPostponeAppointment = async () => {
    // Logique pour Reporter le rendez-vous
    const date_rendez_vous = new Date(selectedTimeSlot.date);
    date_rendez_vous.setHours(
      Number.parseInt(selectedTimeSlot.start.split(":")[0]),
      Number.parseInt(selectedTimeSlot.start.split(":")[1]),
      0,
      0
    );
    await handlePostponeAppointment(appointment.id, date_rendez_vous);
    await fetchAppointmentListByProfessionalId(appointment.id_professionnel);
    handleClosePostponeAppointmentModal();
  };

  return (
    <Dialog
      open={isPostponeAppointmentModalOpen}
      onClose={handleClosePostponeAppointmentModal}
      maxWidth="sm"
    >
      <DialogTitle className="flex justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <Typography variant="h6">
          {format(new Date(appointment?.date_rendez_vous), "EEEE d MMMM yyyy", {
            locale: fr,
          })}
          {" à "}
          {format(new Date(appointment?.date_rendez_vous), "HH:mm")}
        </Typography>
      </DialogTitle>
      <DialogContent className="my-2">
        <Typography>
          Voulez-vous reporter le rendez-vous avec {appointment?.patient.nom}{" "}
          {appointment?.patient.prenom} ?
        </Typography>
        <Typography variant="body2" color="textSecondary">
          choisissez le nouveau date
        </Typography>
        <div ref={horairesRef} className="mt-2">
          {selectedTimeSlot ? (
            <div>
              <div className="mt-4">
                {selectedTimeSlot.date} {selectedTimeSlot?.start} -{" "}
                {selectedTimeSlot?.end}
              </div>
              <div className="mt-4">
                <Button
                  variant="outlined"
                  onClick={handleResetTimeSlot}
                  sx={{ textTransform: "none" }}
                >
                  Choisir une autre date
                </Button>
              </div>
            </div>
          ) : (
            <div className={PROFESSIONAL_CARD_CLASSNAMES.AVAILABILITY_SECTION}>
              <Availability
                professionalInformation={currentProfessional}
                className="min-h-[270px]"
                horairesRef={horairesRef}
              />
            </div>
          )}
        </div>
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleClosePostponeAppointmentModal}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={onPostponeAppointment}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          disabled={selectedTimeSlot === null}
          loading={loading}
        >
          Confirmer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PostponeAppointmentModal;
