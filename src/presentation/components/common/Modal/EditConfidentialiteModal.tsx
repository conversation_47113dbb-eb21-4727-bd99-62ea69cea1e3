import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
  FormControlLabel,
  Switch,
} from "@mui/material";
import { X } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";

interface EditConfidentialiteModalProps {
  type: string;
  isCarnetDeSanteModalOpen: boolean;
  handleCloseModal: () => void;
}

export const EditConfidentialiteModal = ({
  type,
  isCarnetDeSanteModalOpen,
  handleCloseModal,
}: EditConfidentialiteModalProps) => {
  const { confidentialite, setConfidentialite } = useCarnetDeSanteState();
  const handleSave = async () => {
    try {
      handleCloseModal();
    } catch (error) {
      console.error("Error saving data:", error);
    }
  };

  return (
    <Dialog
      open={isCarnetDeSanteModalOpen}
      onClose={handleCloseModal}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle className="flex items-center justify-between">
        <Typography variant="h6" component="div">
          Confidentialité {type}
        </Typography>
        <Tooltip title="Fermer">
          <IconButton onClick={handleCloseModal} size="small">
            <X className="h-4 w-4" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent>
        <FormControlLabel
          control={
            <Switch
              checked={confidentialite}
              onChange={(e) => setConfidentialite(e.target.checked)}
              name="confidentialite"
            />
          }
          label="Render confidentielle"
        />
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button
          variant="outlined"
          onClick={handleCloseModal}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            textTransform: "none",
            backgroundColor: PRIMARY,
          }}
        >
          Enregistrer et quitter
        </Button>
      </DialogActions>
    </Dialog>
  );
};
