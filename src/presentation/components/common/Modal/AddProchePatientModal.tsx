import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import ProcheForm from "@/presentation/pages/patient/families/ProcheForm";
import { PRIMARY } from "@/shared/constants/Color";
import { useProche } from "@/presentation/hooks/use-proches";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { useAppSelector } from "@/presentation/hooks/redux";
import ListePatient from "../../features/professional/agenda/BookAppointment/ListePatient";
import { useEffect, useState } from "react";
import { Patient, Employer } from "@/domain/models";
import ListeEmployer from "@/presentation/pages/patient/families/ListeEmployer";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { useParams } from "react-router-dom";

interface ProcheModalProps {
  patient?: Patient[];
  employer?: Employer[];
  isProcheModalOpen: boolean;
  handleCloseModal: () => void;
}

export const AddProcheModal = ({
  patient,
  employer,
  isProcheModalOpen,
  handleCloseModal,
}: ProcheModalProps) => {
  const { id } = useParams();
  const [currentPatient, setCurrentPatient] = useState<Patient | null>(null);
  const [currentEmployer, setCurrentEmployer] = useState<Employer | null>(null);
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const {
    loading,
    handleCreateProcheByProfessional,
    handleCreateProcheByDass,
    handleCreateProche,
  } = useProche();
  const { procheInfo, handleresetState } = useConsultationState();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const patientId = useAppSelector((state) => state.authentification.user?.id);
  const handleSave = async () => {
    try {
      if (role === utilisateurs_role_enum.PATIENT) {
        await handleCreateProche({
          ...procheInfo,
          id_patient: patientId,
        });
      } else if (patient) {
        await handleCreateProcheByProfessional(
          {
            ...procheInfo,
            id_patient: currentPatient.utilisateur_id,
          },
          currentPatient
        );
      } else if (employer) {
        await handleCreateProcheByDass(
          {
            ...procheInfo,
            id_patient: currentEmployer.id_utilisateur,
          },
          currentEmployer
        );
      } else if (id) {
        await handleCreateProche({
          ...procheInfo,
          id_patient: Number(id),
        });
      }
      handleCloseModal();
    } catch (error) {
      console.error("Error saving data:", error);
    }
  };

  useEffect(() => {
    if (patient && selectedId) {
      setCurrentPatient(patient.find((p) => p.id === selectedId));
    } else if (employer && selectedId) {
      setCurrentEmployer(employer.find((e) => e.id === selectedId));
    } else {
      handleresetState();
    }
  }, [patient, employer, selectedId]);

  return (
    <Dialog
      open={isProcheModalOpen}
      onClose={handleCloseModal}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <Typography variant="h6" component="div">
          Ajouter un proche
        </Typography>
        <Tooltip title="Fermer">
          <IconButton onClick={handleCloseModal} size="small">
            <X className="h-4 w-4 text-white" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent>
        {patient && <ListePatient onPatientSelect={setSelectedId} />}
        {employer && <ListeEmployer onEmployerSelect={setSelectedId} />}
        <ProcheForm />
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button
          variant="outlined"
          onClick={handleCloseModal}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            textTransform: "none",
            backgroundColor: PRIMARY,
          }}
          loading={loading}
        >
          Enregistrer et quitter
        </Button>
      </DialogActions>
    </Dialog>
  );
};
