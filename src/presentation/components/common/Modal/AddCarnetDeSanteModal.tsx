import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { getTitle } from "@/shared/utils/getTitle";
import { CarnetDeSanteForm } from "./CarnetDeSanteForm";
import { PRIMARY } from "@/shared/constants/Color";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";
import { useCreateMedicalRecord } from "@/presentation/hooks/carnetDeSante/sousCarnet/useCreateMedicalRecord";
import {
  action_carnet_de_sante_enum,
  utilisateurs_role_enum,
} from "@/domain/models/enums";
import { useAppSelector } from "@/presentation/hooks/redux";
import useAuth from "@/presentation/hooks/use-auth";
import HeaderStylePatient from "../../features/patient/PatientCard/HeaderStylePatient";
import HeaderStyleProche from "../../features/proche/HeaderStyleProche";
import HeaderStyleEmployer from "../../features/professional/employer/HeaderStyleEmployer";
import {
  StockModeProvider,
  useStockModeContext,
} from "./context/StockModeContext";

interface CarnetDeSanteModalProps {
  type: string;
  isCarnetDeSanteModalOpen: boolean;
  handleCloseModal: () => void;
}

// Composant interne qui utilise le contexte
const ModalContent = ({
  type,
  isCarnetDeSanteModalOpen,
  handleCloseModal,
}: CarnetDeSanteModalProps) => {
  const { handleCreate } = useCreateMedicalRecord();
  const { selectedFile } = useCarnetDeSanteState();
  const { useStockMode, resetStockMode } = useStockModeContext();

  const handleSave = async () => {
    try {
      await handleCreate(type, selectedFile, useStockMode);
      resetStockMode(); // Réinitialiser le mode stock après la sauvegarde
      handleCloseModal();
    } catch (error) {
      console.error("Error saving data:", error);
    }
  };

  const role = useAppSelector((state) => state.authentification.user?.role);
  const { roleUserSelected } = useAuth();

  return (
    <Dialog
      open={isCarnetDeSanteModalOpen}
      onClose={handleCloseModal}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        {role === utilisateurs_role_enum.PROFESSIONNEL ? (
          roleUserSelected === utilisateurs_role_enum.PATIENT ? (
            <HeaderStylePatient />
          ) : (
            <HeaderStyleProche />
          )
        ) : roleUserSelected === utilisateurs_role_enum.EMPLOYER ? (
          <HeaderStyleEmployer />
        ) : (
          <HeaderStyleProche />
        )}
        <Tooltip title="Fermer">
          <IconButton onClick={handleCloseModal} size="small">
            <X className="h-4 w-4 text-white" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="my-2 min-h-[0] md:min-h-[64.8vh]">
        {getTitle(type, action_carnet_de_sante_enum.ajout)}
        <CarnetDeSanteForm type={type} />
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button
          variant="outlined"
          onClick={handleCloseModal}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            textTransform: "none",
            backgroundColor: PRIMARY,
          }}
        >
          Enregistrer et quitter
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Composant principal avec le provider
export const AddCarnetDeSanteModal = ({
  type,
  isCarnetDeSanteModalOpen,
  handleCloseModal,
}: CarnetDeSanteModalProps) => {
  return (
    <StockModeProvider>
      <ModalContent
        type={type}
        isCarnetDeSanteModalOpen={isCarnetDeSanteModalOpen}
        handleCloseModal={handleCloseModal}
      />
    </StockModeProvider>
  );
};
