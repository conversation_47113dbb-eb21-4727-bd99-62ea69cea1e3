import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";
import { useParams } from "react-router-dom";
import { PRIMARY } from "@/shared/constants/Color";

interface DecedePatientModalProps {
  isOpen: boolean;
  handleClose: () => void;
  nom: string;
}

export const DecedePatientModal = ({
  isOpen,
  handleClose,
  nom,
}: DecedePatientModalProps) => {
  const { id: patientId } = useParams();
  const { handleDecedePatient } = useCarnetDeSante();
  const onConfirme = async () => {
    await handleDecedePatient(Number(patientId));
    handleClose();
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="flex justify-end">
        <Tooltip title="Fermer">
          <IconButton onClick={handleClose} size="small">
            <X className="h-4 w-4" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="flex justify-center">
        <Typography variant="body1">
          Êtes-vous sûr que {nom} est décede ?
        </Typography>
      </DialogContent>
      <DialogActions sx={{ p: 2, display: "flex", justifyContent: "center" }}>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Non
        </Button>
        <Button
          variant="contained"
          onClick={onConfirme}
          sx={{
            textTransform: "none",
            backgroundColor: PRIMARY,
          }}
          // loading={loading}
        >
          Oui
        </Button>
      </DialogActions>
    </Dialog>
  );
};
