import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  <PERSON>alog<PERSON>ontent,
  IconButton,
  Typography,
  DialogA<PERSON>,
  Button,
} from "@mui/material";
import { X } from "lucide-react";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import { EmployeeForm } from "@/presentation/components/features/dash/employees";
import {
  EmployerFormData,
  employerSchema,
} from "@/shared/schemas/EmployerSchema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";

interface AddEmployerModal {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const AddEmployedModal = ({ isOpen, setIsOpen }: AddEmployerModal) => {
  const { createEmploye, loading } = useEmployer();
  const [profilePhoto, setProfilePhoto] = useState<File | null>(null);

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<EmployerFormData>({
    resolver: zodResolver(employerSchema),
  });

  const handleAddEmployedClose = () => {
    setIsOpen(false);
  };

  const onSubmit = async (data: EmployerFormData) => {
    try {
      const result = await createEmploye(data, profilePhoto);

      if (result) {
        handleAddEmployedClose();
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleAddEmployedClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        className: "dark:bg-gray-800 dark:text-white",
        sx: {
          "&.dark": {
            backgroundColor: "#1f2937",
            color: "#ffffff",
          },
        },
      }}
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <Typography
          variant="h6"
          component="div"
          className="text-center w-full text-white font-semibold"
          sx={{
            color: "white !important",
            fontWeight: 600,
          }}
        >
          Ajouter un nouvel employé
        </Typography>
        <IconButton
          onClick={handleAddEmployedClose}
          sx={{
            color: "white",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
        >
          <X className="h-4 w-4" />
        </IconButton>
      </DialogTitle>
      <DialogContent className="dark:bg-gray-800 dark:text-white">
        <EmployeeForm
          control={control}
          register={register}
          errors={errors}
          onSubmit={handleSubmit(onSubmit)}
          setValue={setValue}
          profilePhotoFile={profilePhoto}
          setProfilePhotoFile={setProfilePhoto}
        />
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          className="w-full font-medium bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white dark:text-white"
          onClick={handleSubmit(onSubmit)}
          disabled={loading}
        >
          {loading ? "Chargement..." : "Créer le compte"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddEmployedModal;
