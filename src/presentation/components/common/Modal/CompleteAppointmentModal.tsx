import {
  Dialog,
  DialogActions,
  Typography,
  DialogContent,
  Button,
} from "@mui/material";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { PRIMARY } from "@/shared/constants/Color";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";

interface CompleteAppointmentModalProps {
  appointment: AppointmentProfessionalDTO;
  isCompleteAppointmentModalOpen: boolean;
  handleCloseCompleteAppointmentModal: () => void;
}

const CompleteAppointmentModal = ({
  appointment,
  isCompleteAppointmentModalOpen,
  handleCloseCompleteAppointmentModal,
}: CompleteAppointmentModalProps) => {
  const {
    loading,
    handleCompleteAppointment,
    fetchAppointmentListByProfessionalId,
  } = useConsultationState();
  const onCompleteAppointment = async () => {
    // Logique pour terminer le rendez-vous
    await handleCompleteAppointment(appointment.id);
    await fetchAppointmentListByProfessionalId(appointment.id_professionnel);
    handleCloseCompleteAppointmentModal();
  };

  return (
    <Dialog
      open={isCompleteAppointmentModalOpen}
      onClose={handleCloseCompleteAppointmentModal}
      maxWidth="sm"
    >
      <DialogContent>
        <Typography>
          Terminer le rendez-vous avec {appointment?.patient.nom}{" "}
          {appointment?.patient.prenom}
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleCloseCompleteAppointmentModal}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={onCompleteAppointment}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          disabled={loading}
        >
          {loading ? "En cours..." : "Terminer"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CompleteAppointmentModal;
