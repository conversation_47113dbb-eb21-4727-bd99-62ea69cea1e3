import {
  Dialog,
  DialogActions,
  Typography,
  <PERSON>alog<PERSON><PERSON>nt,
  <PERSON><PERSON>,
  DialogTitle,
  IconButton,
  Tooltip,
} from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";
import { X } from "lucide-react";
import { laboratoire_diagnostics } from "@/domain/models";
import { useDiagnostic } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDiagnostic";
import { useDiagnosticForm } from "@/presentation/hooks/diagnostic/useDiagnosticForm";
import TestsMedicauxDiagnosticsEtDepistage from "../historiqueCarnetSante/testsMedicauxDiagnosticsEtDepistage/TestsMedicauxDiagnosticsEtDepistage";
import { useEffect } from "react";
import HeaderStylePatient from "../../features/patient/PatientCard/HeaderStylePatient";
import HeaderStyleEmployer from "../../features/professional/employer/HeaderStyleEmployer";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { useAppSelector } from "@/presentation/hooks/redux";
import useAuth from "@/presentation/hooks/use-auth";
import HeaderStyleProche from "../../features/proche/HeaderStyleProche";

interface AddDiagnosticModalProps {
  diagnostic?: laboratoire_diagnostics;
  isAddDiagnosticModalOpen: boolean;
  isDesableButtonVoir?: boolean;
  handleClose: () => void;
}

const AddDiagnosticModal = ({
  diagnostic,
  isAddDiagnosticModalOpen,
  isDesableButtonVoir,
  handleClose,
}: AddDiagnosticModalProps) => {
  const { idCarnetSante } = useCarnetDeSante();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { roleUserSelected } = useAuth();
  const {
    loading,
    create: handleCreateDiagnostic,
    update: handleUpdateDiagnostic,
    diagnosticState: { selectedFile },
  } = useDiagnostic();
  const { isFormValid, initialiseState, getDiagnosticData } =
    useDiagnosticForm(idCarnetSante);

  useEffect(() => {
    if (diagnostic) {
      initialiseState(diagnostic);
    }
  }, [diagnostic]);

  const handleSubmit = async () => {
    const diagnosticData = getDiagnosticData();
    if (!diagnosticData) {
      return;
    }
    if (diagnostic) {
      await handleUpdateDiagnostic(diagnostic.id, diagnosticData, selectedFile);
    } else {
      await handleCreateDiagnostic(diagnosticData, selectedFile);
    }
    handleClose();
  };
  return (
    <Dialog
      open={isAddDiagnosticModalOpen}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle className="flex justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <div className="flex items-center">
          {role === utilisateurs_role_enum.PROFESSIONNEL ? (
            roleUserSelected === utilisateurs_role_enum.PATIENT ? (
              <HeaderStylePatient />
            ) : (
              <HeaderStyleProche />
            )
          ) : roleUserSelected === utilisateurs_role_enum.EMPLOYER ? (
            <HeaderStyleEmployer />
          ) : (
            <HeaderStyleProche />
          )}
        </div>
        <div className="flex items-center gap-4">
          <Tooltip title="Fermer">
            <IconButton
              onClick={handleClose}
              size="small"
              sx={{
                color: "white",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                },
              }}
            >
              <X className="h-4 w-4" />
            </IconButton>
          </Tooltip>
        </div>
      </DialogTitle>
      <DialogContent sx={{ mt: 2 }}>
        <Typography variant="h6" className="text-gray-700 dark:text-gray-300">
          Tests médicaux, diagnostics et dépistages
        </Typography>
        <TestsMedicauxDiagnosticsEtDepistage />
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          onClick={handleSubmit}
          loading={loading}
          disabled={!isFormValid || isDesableButtonVoir}
        >
          Enregistrer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddDiagnosticModal;
