import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import ContactUrgence from "@/presentation/components/features/patient/registerPatienStepper/AutresInformations/ContactUrgence";
import { useUrgence } from "@/presentation/hooks/useUrgence";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

interface AddContactUrgenceModalProps {
  isOpen: boolean;
  patientId: number;
  handleClose: () => void;
}

export const AddContactUrgenceModal = ({
  isOpen,
  patientId,
  handleClose,
}: AddContactUrgenceModalProps) => {
  const { loading, create: createUrgence } = useUrgence();
  const { getContactUrgence } = useRegisterPatientState();
  const handleSubmit = async () => {
    const contactUrgence = getContactUrgence(patientId);
    if (contactUrgence.length > 0) {
      await createUrgence(contactUrgence);
    }
    handleClose();
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="flex items-center justify-between">
        <Typography variant="body1">Ajouter un contact d'urgence</Typography>
        <Tooltip title="Fermer">
          <IconButton onClick={handleClose} size="small">
            <X className="h-4 w-4" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="flex justify-center">
        <ContactUrgence />
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          loading={loading}
        >
          Enregistrer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
