import AddAvailability from "@/presentation/components/features/professional/agenda/AddAvailability/AddAvailability";
import {
  useAvailabilitySettingState,
  useAvailabilitySettings,
} from "@/presentation/hooks/agenda/settings";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Button,
  Typography,
} from "@mui/material";
import { Save, X } from "lucide-react";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { PRIMARY } from "@/shared/constants/Color";

export const AddSettingModal = () => {
  const { settings, settingsLocal, errors, handleSettingSave } =
    useAvailabilitySettingState();
  const { isSettingsModalOpen, handleSaveSettings, handleCloseSettings } =
    useAgendaState();
  const { loading } = useAvailabilitySettings();

  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );

  const handleSave = () => {
    console.log(settings);

    const AvailabilitySettings = handleSettingSave(professionalId);

    const settingLocal = settingsLocal;

    // Save to localStorage
    localStorage.setItem("settingLocal", JSON.stringify(settingLocal));
    console.log(AvailabilitySettings);
    // Call the onSave prop with the new settings
    handleSaveSettings(AvailabilitySettings);
  };

  return (
    <Dialog
      open={isSettingsModalOpen}
      onClose={() => handleCloseSettings(false)}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <Typography variant="h6" className="text-white font-semibold">
          Paramètres de disponibilité
        </Typography>
        <IconButton onClick={() => handleCloseSettings(false)}>
          <X className="h-4 w-4 text-white" />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <AddAvailability />
      </DialogContent>
      <DialogActions>
        <Button
          disabled={Object.keys(errors).length > 0}
          variant="contained"
          onClick={handleSave}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          startIcon={<Save className="h-5 w-5" />}
          loading={loading}
        >
          Enregistrer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
