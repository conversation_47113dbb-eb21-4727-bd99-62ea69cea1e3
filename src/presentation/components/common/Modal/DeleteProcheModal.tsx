import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useProche } from "@/presentation/hooks/use-proches";
import { Proche } from "@/domain/models";

interface DeleteProcheModalProps {
  isOpen: boolean;
  handleClose: () => void;
  proche: Proche;
}

export const DeleteProcheModal = ({
  isOpen,
  handleClose,
  proche,
}: DeleteProcheModalProps) => {
  const { loading, handleDeleteProcheByPatientId } = useProche();

  const onDelete = async () => {
    await handleDeleteProcheByPatientId(proche.utilisateur_id);
    handleClose();
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="flex justify-end">
        <Tooltip title="Fermer">
          <IconButton onClick={handleClose} size="small">
            <X className="h-4 w-4" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="flex justify-center">
        <Typography variant="body1">
          Êtes-vous sûr de vouloir supprimer {proche.nom} {proche.prenom} ?
        </Typography>
      </DialogContent>
      <DialogActions sx={{ p: 2, display: "flex", justifyContent: "center" }}>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={onDelete}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          loading={loading}
        >
          Supprimer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
