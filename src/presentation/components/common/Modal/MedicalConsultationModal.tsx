import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Button,
  Tooltip,
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import { X, ChevronDown, Edit2 } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { consultation_medical, signe_vitaux } from "@/domain/models";
import { ConsultationForm } from "@/presentation/pages/professional/patients/ConsultationForm";
import CarnetDeSanteCard from "../historiqueCarnetSante/component/CarnetDeSanteCard";
import { AddCarnetDeSanteModal } from "./AddCarnetDeSanteModal";
import { BookAppointmentModal } from "./BookAppointmentModal";
import AddSigneVitauxModal from "@/presentation/components/common/Modal/AddSigneVitauxModal";
import { AFFECTATION_MEDICAL_TABLE_NAME } from "@/infrastructure/repositories/affectationMedicale/Constant";
import { MEDICAMENT_TABLE_NAME } from "@/infrastructure/repositories/medicament/Constant";
import { ALLERGIE_TABLE_NAME } from "@/infrastructure/repositories/allergie/Constant";
import { active_tab_enum, utilisateurs_role_enum } from "@/domain/models/enums";
import {
  ConsultationFormProps,
  useMedicalConsultation,
} from "@/presentation/hooks/consultationMedicale";
import { getDataCard } from "@/shared/constants/cardData";
import useAppointment from "@/presentation/hooks/appointment/use-appointment";
import { useState, useEffect } from "react";
import { useAgendaState } from "@/presentation/hooks/agenda";
import {
  useCarnetDeSante,
  useCarnetDeSanteData,
} from "@/presentation/hooks/carnetDeSante";
import { CarnetSanteDTO } from "@/domain/DTOS";
import { useAppSelector } from "@/presentation/hooks/redux";
import { HistoriqueCarnetDeSanteModal } from "./HistoriqueCarnetDeSanteModal";
import { useHistoriqueCarnetSante } from "@/presentation/hooks/carnetDeSante/sousCarnet/useHistoriqueCarnetSante";
import HeaderStylePatient from "@/presentation/components/features/patient/PatientCard/HeaderStylePatient";
import HeaderStyleEmployer from "@/presentation/components/features/professional/employer/HeaderStyleEmployer";
import useAuth from "@/presentation/hooks/use-auth";
import HeaderStyleProche from "../../features/proche/HeaderStyleProche";
import { DISPOSITIF_MEDICAUX_TABLE_NAME } from "@/infrastructure/repositories/dispositifMedicaux/Constant";
import { ANTECEDANT_CHIRURGICAUX_TABLE_NAME } from "@/infrastructure/repositories/antecedantChirurgicaux/Constant";
import { ANTECEDANT_FAMILIAUX_TABLE_NAME } from "@/infrastructure/repositories/antecedantFamiliaux/Constant";
import { ANTECEDENT_SOCIAUX_TABLE_NAME } from "@/infrastructure/repositories/antecedentSociaux/Constant";
import { VACCINATION_TABLE_NAME } from "@/infrastructure/repositories/vaccination/Constant";
import { CONDITION_GYNECOLOGIQUE_TABLE_NAME } from "@/infrastructure/repositories/conditionGynecologique/Constant";
import { DIAGNOSTIC_TABLE_NAME } from "@/infrastructure/repositories/diagnostics/Constant";

interface MedicalConsultationModalProps {
  isOpen: boolean;
  formData: ConsultationFormProps;
  isFormValid: boolean;
  data: Partial<CarnetSanteDTO>;
  isDesableButtonVoir?: boolean;
  handleClose: () => void;
  handlePrint: () => void;
  consultationDetail: { id: number; date_visite: Date } | null;
  getConsultationData: () => Omit<consultation_medical, "id">;
}

const MedicalConsultationModal = ({
  isOpen,
  formData,
  isFormValid,
  data,
  isDesableButtonVoir,
  handleClose,
  handlePrint,
  consultationDetail,
  getConsultationData,
}: MedicalConsultationModalProps) => {
  const [isCarnetDeSanteModalOpen, setIsCarnetDeSanteModalOpen] =
    useState(false);
  const [isHistoriqueCarnetDeSanteModal, setIsHistoriqueCarnetDeSanteModal] =
    useState(false);
  const { isAppointmentModalOpen, handleIsAppointmentModalOpen } =
    useAgendaState();
  const [type, setType] = useState("");
  const [expandedCards, setExpandedCards] = useState<string[]>([]);
  const { terminateAppointment, selectedAppointment } = useAppointment();
  const {
    loading,
    handleCreateMedicalConsultation,
    handleUpdateMedicalConsultation,
  } = useMedicalConsultation();
  const { resetSearch } = useCarnetDeSante();
  const { clearSelected } = useHistoriqueCarnetSante();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { roleUserSelected } = useAuth();

  // États pour les signes vitaux
  const { signeVitaux } = useCarnetDeSanteData();
  const [signeVitauxFilter, setSigneVitauxFilter] =
    useState<signe_vitaux | null>(null);
  const [isAddSigneVitauxModalOpen, setIsAddSigneVitauxModalOpen] =
    useState(false);
  const [isEditSigneVitauxModalOpen, setIsEditSigneVitauxModalOpen] =
    useState(false);

  // Effect pour filtrer les signes vitaux selon la date de consultation
  useEffect(() => {
    const date_consultation = consultationDetail
      ? new Date(consultationDetail.date_visite)
      : new Date();

    if (!date_consultation || !signeVitaux) return;

    const formattedConsultationDate = date_consultation
      .toISOString()
      .split("T")[0];

    const signe = signeVitaux.find((sv) => {
      if (!sv.date_visite) return false;
      const formattedSigneDate = new Date(sv.date_visite)
        .toISOString()
        .split("T")[0];
      return formattedSigneDate === formattedConsultationDate;
    });

    setSigneVitauxFilter(signe || null);
  }, [consultationDetail, signeVitaux]);

  const handleSubmit = async () => {
    const data = getConsultationData();
    if (consultationDetail) {
      await handleUpdateMedicalConsultation(consultationDetail.id, data);
    } else {
      await handleCreateMedicalConsultation(data);
    }

    if (selectedAppointment && selectedAppointment.id) {
      await terminateAppointment(selectedAppointment.id);
    }
    handleClose();
  };

  const handleIsCarnetDeSanteModalOpen = (type: string) => {
    setIsCarnetDeSanteModalOpen(true);
    setType(type);
  };

  const handleCloseCarnetDeSanteModal = () => {
    setIsCarnetDeSanteModalOpen(false);
    resetSearch();
  };

  const handleIsHistoriqueCarnetDeSanteModalOpen = (type: string) => {
    setIsHistoriqueCarnetDeSanteModal(true);
    setType(type);
  };

  const handleCloseHistoriqueCarnetDeSanteModal = () => {
    setIsHistoriqueCarnetDeSanteModal(false);
    clearSelected();
  };

  const handleCardExpand =
    (cardTitle: string) =>
    (event: React.SyntheticEvent, isExpanded: boolean) => {
      if (isExpanded) {
        setExpandedCards((prev) => [...prev, cardTitle]);
      } else {
        setExpandedCards((prev) => prev.filter((title) => title !== cardTitle));
      }
    };

  const handleCloseSigneVitauxModal = () => {
    if (isEditSigneVitauxModalOpen) {
      setIsEditSigneVitauxModalOpen(false);
    } else if (isAddSigneVitauxModalOpen) {
      setIsAddSigneVitauxModalOpen(false);
    }
  };

  const DATA_CARD = getDataCard(data, active_tab_enum.consultationMedicale, [
    ALLERGIE_TABLE_NAME,
    MEDICAMENT_TABLE_NAME,
    AFFECTATION_MEDICAL_TABLE_NAME,
    DISPOSITIF_MEDICAUX_TABLE_NAME,
    ANTECEDANT_CHIRURGICAUX_TABLE_NAME,
    ANTECEDANT_FAMILIAUX_TABLE_NAME,
    ANTECEDENT_SOCIAUX_TABLE_NAME,
    VACCINATION_TABLE_NAME,
    CONDITION_GYNECOLOGIQUE_TABLE_NAME,
    DIAGNOSTIC_TABLE_NAME,
  ]);

  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle className="flex justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <div className="flex items-center">
          {role === utilisateurs_role_enum.PROFESSIONNEL ? (
            roleUserSelected === utilisateurs_role_enum.PATIENT ? (
              <HeaderStylePatient />
            ) : (
              <HeaderStyleProche />
            )
          ) : roleUserSelected === utilisateurs_role_enum.EMPLOYER ? (
            <HeaderStyleEmployer />
          ) : (
            <HeaderStyleProche />
          )}
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outlined"
            sx={{
              textTransform: "none",
              ml: 2,
              borderRadius: 5,
              color: "white",
              borderColor: "white",
            }}
            onClick={handlePrint}
          >
            IMPRIMER
          </Button>
          <Tooltip title="Fermer">
            <IconButton
              onClick={handleClose}
              size="small"
              sx={{
                color: "white",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                },
              }}
            >
              <X className="h-4 w-4" />
            </IconButton>
          </Tooltip>
        </div>
      </DialogTitle>

      <DialogContent sx={{ mt: 2 }}>
        <Typography variant="h6" className="text-gray-700 dark:text-white">
          Consultation médicale
        </Typography>
        <div className="my-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 my-4">
            <div className="lg:col-span-2 space-y-6">
              <ConsultationForm
                {...formData}
                date_consultation={
                  consultationDetail
                    ? new Date(consultationDetail.date_visite)
                    : new Date()
                }
                hideSigneVitaux={true}
              />
            </div>

            <div className="lg:col-span-1 h-full flex flex-col">
              {/* Section Signes Vitaux déplacée en haut */}
              <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <Typography
                  variant="h6"
                  className="mb-3 text-gray-700 dark:text-gray-300"
                >
                  Signes Vitaux
                </Typography>

                {signeVitauxFilter ? (
                  <>
                    <Box className="grid grid-cols-1 gap-2 mb-4 text-sm">
                      <div className="grid grid-cols-2 gap-4">
                        <p className="dark:text-gray-400 text-gray-600">
                          <span className="dark:text-white text-black font-medium">
                            Taille :{" "}
                          </span>
                          <span>{signeVitauxFilter.taille || "--"}</span>
                        </p>
                        <p className="dark:text-gray-400 text-gray-600">
                          <span className="dark:text-white text-black font-medium">
                            Poids :{" "}
                          </span>
                          <span>{signeVitauxFilter.poid || "--"}</span>
                        </p>
                        <p className="dark:text-gray-400 text-gray-600">
                          <span className="dark:text-white text-black font-medium">
                            Température :{" "}
                          </span>
                          <span>{signeVitauxFilter.temperature || "--"}</span>
                        </p>
                        <p className="dark:text-gray-400 text-gray-600">
                          <span className="dark:text-white text-black font-medium">
                            IMC :{" "}
                          </span>
                          <span>
                            {signeVitauxFilter.indice_masse_corporel || "--"}
                          </span>
                        </p>
                        <p className="dark:text-gray-400 text-gray-600 col-span-2">
                          <span className="dark:text-white text-black font-medium">
                            Circonférence tête :{" "}
                          </span>
                          <span>
                            {signeVitauxFilter.circonference_tete || "--"}
                          </span>
                        </p>
                        <p className="dark:text-gray-400 text-gray-600">
                          <span className="dark:text-white text-black font-medium">
                            FC :{" "}
                          </span>
                          <span>
                            {signeVitauxFilter.frequence_cardiaque || "--"}
                          </span>
                        </p>
                        <p className="dark:text-gray-400 text-gray-600">
                          <span className="dark:text-white text-black font-medium">
                            FR :{" "}
                          </span>
                          <span>
                            {signeVitauxFilter.frequence_respiratoire || "--"}
                          </span>
                        </p>
                        <p className="dark:text-gray-400 text-gray-600">
                          <span className="dark:text-white text-black font-medium">
                            SA02 :{" "}
                          </span>
                          <span>{signeVitauxFilter.sa02 || "--"}</span>
                        </p>
                        <p className="dark:text-gray-400 text-gray-600">
                          <span className="dark:text-white text-black font-medium">
                            Glucose :{" "}
                          </span>
                          <span>
                            {signeVitauxFilter.niveau_glucose || "--"}
                          </span>
                        </p>
                        <p className="dark:text-gray-400 text-gray-600 col-span-2">
                          <span className="dark:text-white text-black font-medium">
                            Tension artérielle :{" "}
                          </span>
                          <span>
                            {signeVitauxFilter.tension_arterielle || "--"}
                          </span>
                        </p>
                      </div>
                    </Box>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{
                        textTransform: "none",
                        borderRadius: 3,
                        backgroundColor: PRIMARY,
                      }}
                      onClick={() => setIsEditSigneVitauxModalOpen(true)}
                      startIcon={<Edit2 className="h-4 w-4" />}
                    >
                      Modifier les signes vitaux
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      textTransform: "none",
                      borderRadius: 3,
                      backgroundColor: PRIMARY,
                    }}
                    onClick={() => setIsAddSigneVitauxModalOpen(true)}
                  >
                    Ajouter des signes vitaux
                  </Button>
                )}
              </div>

              <div className="flex-1 max-h-[600px] lg:max-h-[calc(100vh-300px)] overflow-y-auto space-y-2 pr-2">
                {DATA_CARD.map((cardData, index) => (
                  <Accordion
                    key={index}
                    expanded={expandedCards.includes(cardData.title)}
                    onChange={handleCardExpand(cardData.title)}
                    sx={{
                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                      borderRadius: "8px !important",
                      "&:before": {
                        display: "none",
                      },
                      // Ajout de ces propriétés pour éviter les conflits de hauteur
                      "&.Mui-expanded": {
                        margin: "8px 0",
                      },
                      margin: "8px 0",
                      "& .MuiAccordionSummary-root": {
                        backgroundColor: "rgba(25, 118, 210, 0.04)",
                        borderRadius: "8px 8px 0 0",
                        "&:hover": {
                          backgroundColor: "rgba(25, 118, 210, 0.08)",
                        },
                        "&.Mui-expanded": {
                          borderRadius: "8px 8px 0 0",
                        },
                        minHeight: "48px",
                        "& .MuiAccordionSummary-content": {
                          margin: "8px 0",
                        },
                      },
                      "& .MuiAccordionDetails-root": {
                        padding: "8px 16px 16px",
                        backgroundColor: "#fafafa",
                        borderRadius: "0 0 8px 8px",
                        // S'assurer que le contenu peut aussi scroller si nécessaire
                        maxHeight: "300px",
                        overflowY: "auto",
                      },
                    }}
                  >
                    <AccordionSummary
                      expandIcon={
                        <ChevronDown
                          className={`h-4 w-4 transition-transform duration-200 ${
                            expandedCards.includes(cardData.title)
                              ? "rotate-180"
                              : ""
                          }`}
                        />
                      }
                      aria-controls={`${cardData.title}-content`}
                      id={`${cardData.title}-header`}
                    >
                      <Box className="flex items-center justify-between w-full pr-4">
                        <Typography
                          variant="subtitle1"
                          className="font-medium text-gray-700 dark:text-gray-300"
                        >
                          {cardData.title}
                        </Typography>
                      </Box>
                    </AccordionSummary>

                    <AccordionDetails>
                      <CarnetDeSanteCard
                        data={cardData}
                        setIsCarnetDeSanteModalOpen={() =>
                          handleIsCarnetDeSanteModalOpen(cardData.title)
                        }
                        setIsHistoriqueCarnetDeSanteModalOpen={() =>
                          handleIsHistoriqueCarnetDeSanteModalOpen(
                            cardData.title
                          )
                        }
                        className="shadow-none border-none bg-transparent"
                      />
                    </AccordionDetails>
                  </Accordion>
                ))}
              </div>

              {/* Modales */}
              {isCarnetDeSanteModalOpen && (
                <AddCarnetDeSanteModal
                  type={type}
                  isCarnetDeSanteModalOpen={isCarnetDeSanteModalOpen}
                  handleCloseModal={handleCloseCarnetDeSanteModal}
                />
              )}

              {isHistoriqueCarnetDeSanteModal && (
                <HistoriqueCarnetDeSanteModal
                  type={type}
                  isHistoriqueCarnetDeSanteModalOpen={
                    isHistoriqueCarnetDeSanteModal
                  }
                  handleCloseModal={handleCloseHistoriqueCarnetDeSanteModal}
                />
              )}

              {(isAddSigneVitauxModalOpen || isEditSigneVitauxModalOpen) && (
                <AddSigneVitauxModal
                  isAddSigneVitauxModalOpen={
                    isAddSigneVitauxModalOpen || isEditSigneVitauxModalOpen
                  }
                  handleCloseAddSigneVitauxModal={handleCloseSigneVitauxModal}
                  signeVitaux={
                    isEditSigneVitauxModalOpen ? signeVitauxFilter : undefined
                  }
                />
              )}
            </div>
          </div>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mt: 4,
            }}
          ></Box>

          {isAppointmentModalOpen && <BookAppointmentModal />}
        </div>
      </DialogContent>

      <DialogActions
        sx={{
          p: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {role === utilisateurs_role_enum.PROFESSIONNEL ? (
          <Button
            variant="contained"
            color="primary"
            sx={{
              textTransform: "none",
              ml: 2,
              borderRadius: 5,
              backgroundColor: PRIMARY,
            }}
            onClick={() => handleIsAppointmentModalOpen(true)}
          >
            Prochain rendez-vous
          </Button>
        ) : (
          <p></p>
        )}

        <Box>
          <Button
            variant="outlined"
            color="primary"
            sx={{ textTransform: "none", ml: 2, borderRadius: 5 }}
            onClick={handleClose}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            color="primary"
            sx={{
              textTransform: "none",
              ml: 2,
              borderRadius: 5,
              backgroundColor: PRIMARY,
            }}
            onClick={handleSubmit}
            loading={loading}
            disabled={!isFormValid || isDesableButtonVoir}
          >
            Enregistrer
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default MedicalConsultationModal;
