import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  DialogActions,
} from "@mui/material";
import { X } from "lucide-react";
import useRegister from "@/presentation/hooks/use-register";
import StyledButton from "@/styles/styleMui/ConsultationSteper";
import { useAppSelector } from "@/presentation/hooks/redux";
import AddProfessionnelPatient from "@/presentation/pages/professional/patients/AddProfessionnelPatient";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { useState } from "react";
import { ProfessionnelPatient } from "@/domain/models";
import { usePatientRegistrationLogic } from "@/presentation/hooks/authentification";
import { GetLocaleDate } from "@/shared/utils/getLocaleDate";

const AddPatientModal = () => {
  const { isOpen, handleToggleModal } = useRegister();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const { loading, handleCreateProfessionnelPatient } =
    useProfessionnelPatient();
  const {
    control,
    errors,
    register,
    setValue,
    handleSubmit,
    onSubmit: registerPatient,
  } = usePatientRegistrationLogic();

  const handleAddPatientClose = () => {
    handleToggleModal(false);
  };

  const onSubmit = async () => {
    // Valider tous les champs avant la soumission
    const isLoginUser = false;
    const patient = await registerPatient(isLoginUser);
    if (patient?.success) {
      const data: Omit<ProfessionnelPatient, "id"> = {
        id_patient: patient?.userData.id,
        id_professionnel: professionalId,
        created_date: GetLocaleDate(),
        updated_date: GetLocaleDate(),
        is_delete: false,
      };
      await handleCreateProfessionnelPatient(
        data,
        patient.userData,
        patient.user
      );
      handleAddPatientClose();
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleAddPatientClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        className: "dark:bg-gray-800 dark:text-white",
        sx: {
          '&.dark': {
            backgroundColor: '#1f2937',
            color: '#ffffff',
          }
        }
      }}
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white">
        <Typography variant="h6" component="div" className="text-center w-full">
          Ajouter un nouveaux patient
        </Typography>
        <IconButton onClick={handleAddPatientClose}
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)'
            }
          }}
        >
          <X className="h-4 w-4" />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <AddProfessionnelPatient
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            register={register}
            setValue={setValue}
          />
        </form>
      </DialogContent>
      <DialogActions>
        <StyledButton
          variant="contained"
          className="w-full"
          onClick={onSubmit}
          loading={loading}
        >
          Creer le compte
        </StyledButton>
      </DialogActions>
    </Dialog>
  );
};

export default AddPatientModal;
