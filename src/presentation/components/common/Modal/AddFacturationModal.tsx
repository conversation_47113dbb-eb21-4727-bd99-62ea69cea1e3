import {
  Dialog,
  DialogActions,
  Typography,
  <PERSON>alog<PERSON>ontent,
  But<PERSON>,
  DialogTitle,
  IconButton,
  Tooltip,
} from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import {
  useFacturation,
  useFacturationForm,
} from "@/presentation/hooks/facturation";
import { FacturationForm } from "@/presentation/pages/professional/patients/FacturationForm";
import { X } from "lucide-react";
import { useParams } from "react-router-dom";
import { useAppSelector } from "@/presentation/hooks/redux";
import HeaderStylePatient from "../../features/patient/PatientCard/HeaderStylePatient";
import HeaderStyleEmployer from "../../features/professional/employer/HeaderStyleEmployer";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import useAuth from "@/presentation/hooks/use-auth";
import HeaderStyleProche from "../../features/proche/HeaderStyleProche";

interface AddFacturationModalProps {
  isAddFacturationModalOpen: boolean;
  handleClose: () => void;
}

const AddFacturationModal = ({
  isAddFacturationModalOpen,
  handleClose,
}: AddFacturationModalProps) => {
  const { id: patientId } = useParams();
  const { currentProfessional } = useSearchProfessional();
  const { selectedDataProfessionalPatient } = useProfessionnelPatient();
  const { roleUserSelected } = useAuth();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.utilisateur_id
  );
  const { loading, handleCreateFacturation } = useFacturation();
  const { formData, isFormValid, getFacturationData } = useFacturationForm(
    professionalId,
    Number(patientId)
  );

  const handleSubmit = async () => {
    const data = getFacturationData();
    const patient = {
      id: Number(patientId),
      nom: selectedDataProfessionalPatient?.patient.nom,
      prenom: selectedDataProfessionalPatient?.patient.prenom,
    };
    const professionnel = {
      id: currentProfessional?.id,
      nom: currentProfessional?.nom,
      prenom: currentProfessional?.prenom,
    };
    if (data && patient && professionnel) {
      await handleCreateFacturation(data, patient, professionnel);
    }
    handleClose();
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <Dialog
      open={isAddFacturationModalOpen}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle className="flex justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <div className="flex items-center">
          {roleUserSelected === utilisateurs_role_enum.PATIENT ? (
            <HeaderStylePatient />
          ) : (
            <HeaderStyleProche />
          )}
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outlined"
            sx={{
              textTransform: "none",
              ml: 2,
              borderRadius: 5,
              color: "white",
              borderColor: "white",
            }}
            onClick={handlePrint}
          >
            IMPRIMER
          </Button>
          <Tooltip title="Fermer">
            <IconButton
              onClick={handleClose}
              size="small"
              sx={{
                color: "white",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                },
              }}
            >
              <X className="h-4 w-4" />
            </IconButton>
          </Tooltip>
        </div>
      </DialogTitle>
      <DialogContent sx={{ marginTop: 2 }}>
        <Typography variant="h6" className="text-gray-700 dark:text-gray-300">
          Facturation
        </Typography>
        <FacturationForm {...formData} dateFacturationt={new Date()} />
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          onClick={handleSubmit}
          loading={loading}
          disabled={!isFormValid}
        >
          Enregistrer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddFacturationModal;
