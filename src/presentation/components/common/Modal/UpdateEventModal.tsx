import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
} from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";
import {
  useEvenement,
  useEventState,
} from "@/presentation/hooks/agenda/events";
import { useState } from "react";
import { Evenement } from "@/domain/models";
import { PRIMARY } from "@/shared/constants/Color";

interface UpdateEventModalProps {
  open: boolean;
  onClose: () => void;
  idCurrentEvent?: number;
}

const UpdateEventModal = ({
  open,
  onClose,
  idCurrentEvent,
}: UpdateEventModalProps) => {
  const [typeDeSuppression, setTypeDeSuppression] = useState<"this" | "all">(
    "this"
  );
  const { updateExistingEvenement, updateEvenementByProfessional } =
    useEvenement();

  const {
    title,
    est_toute_la_journee,
    description,
    est_reporte,
    date_debut,
    date_fin,
    repetition,
  } = useEventState();

  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );

  const handleUpdate = (
    id: number,
    callback: (id: number, evenement: Evenement) => void
  ) => {
    const dateDebut = new Date(date_debut);
    const dateFin = new Date(date_fin);

    console.log(dateDebut, dateFin);
    if (est_toute_la_journee) {
      // Pour une journée complète, on définit le début à 00:00:00
      dateDebut.setUTCHours(0, 0, 0, 0);
      // Et la fin à 23:59:59
      dateFin.setUTCHours(0, 0, 0, 0);
    }

    const evenement: Evenement = {
      id_professionnel: professionalId,
      titre: title,
      est_toute_la_journee: est_toute_la_journee,
      description: description,
      date_debut: dateDebut,
      date_fin: dateFin,
      repetition: repetition,
      est_reportee: est_reporte,
    };
    console.log(evenement);
    callback(id, evenement);
    onClose();
  };

  const handleUpdateEvenement = () => {
    if (typeDeSuppression === "this") {
      handleUpdate(idCurrentEvent, updateExistingEvenement);
    } else {
      handleUpdate(professionalId, updateEvenementByProfessional);
    }
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm">
      <DialogTitle className="flex items-center justify-between">
        Modifier un événement
      </DialogTitle>
      <DialogContent>
        <FormControl component="fieldset" sx={{ mb: 2 }}>
          <RadioGroup
            value={typeDeSuppression}
            onChange={(e) =>
              setTypeDeSuppression(e.target.value as "this" | "all")
            }
          >
            <FormControlLabel
              value="this"
              checked={typeDeSuppression == "this"}
              control={<Radio size="small" />}
              label="Cet événement et tous les suivants"
            />
            <FormControlLabel
              value="all"
              checked={typeDeSuppression == "all"}
              control={<Radio size="small" />}
              label="Tous les événements"
            />
          </RadioGroup>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={onClose}
          sx={{ textTransform: "none" }}
          variant="outlined"
        >
          Annuler
        </Button>
        <Button
          onClick={handleUpdateEvenement}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          variant="contained"
        >
          Ok
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UpdateEventModal;
