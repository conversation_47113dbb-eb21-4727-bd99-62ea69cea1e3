import React, { createContext, useContext, ReactNode } from "react";
import { useStockModeState } from "../hooks/useStockModeState";

interface StockModeContextType {
  useStockMode: boolean;
  setUseStockMode: (enabled: boolean) => void;
  resetStockMode: () => void;
}

const StockModeContext = createContext<StockModeContextType | undefined>(undefined);

interface StockModeProviderProps {
  children: ReactNode;
}

export const StockModeProvider: React.FC<StockModeProviderProps> = ({ children }) => {
  const stockModeState = useStockModeState();

  return (
    <StockModeContext.Provider value={stockModeState}>
      {children}
    </StockModeContext.Provider>
  );
};

export const useStockModeContext = (): StockModeContextType => {
  const context = useContext(StockModeContext);
  if (context === undefined) {
    throw new Error("useStockModeContext must be used within a StockModeProvider");
  }
  return context;
};
