import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import { X, AlertTriangle } from "lucide-react";
import Button from "../Button/Button.tsx";
import { twMerge } from "tailwind-merge";

/**
 * Interface pour les propriétés du composant ConfirmationModal
 */
interface ConfirmationModalProps {
  /** État d'ouverture de la modale */
  isOpen: boolean;
  /** Fonction appelée à la fermeture de la modale */
  onClose: () => void;
  /** Fonction appelée à la confirmation de l'action */
  onConfirm: () => void;
  /** Titre de la modale */
  title?: string;
  /** Message principal de la modale */
  message: string;
  /** Texte du bouton de confirmation */
  confirmButtonText?: string;
  /** Texte du bouton d'annulation */
  cancelButtonText?: string;
  /** État de chargement (désactive les boutons et affiche un indicateur) */
  loading?: boolean;
  /** Couleur du bouton de confirmation (danger = rouge) */
  confirmButtonColor?: "primary" | "danger" | "secondary" | "gradient";
  /** Contenu supplémentaire à afficher dans la modale */
  children?: React.ReactNode;
  /** Désactive la fermeture de la modale en cliquant à l'extérieur */
  disableBackdropClick?: boolean;
  /** Affiche une icône d'avertissement */
  showWarningIcon?: boolean;
}

/**
 * Composant de modale de confirmation générique
 *
 * @description Ce composant permet d'afficher une modale de confirmation avec un titre,
 * un message, et des boutons de confirmation et d'annulation. Il gère également l'état
 * de chargement pendant l'exécution de l'action confirmée.
 */
const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirmation",
  message,
  confirmButtonText = "Confirmer",
  cancelButtonText = "Annuler",
  loading = false,
  children,
  disableBackdropClick = false,
  showWarningIcon = false,
  confirmButtonColor = "gradient",
}: ConfirmationModalProps) => {
  // Empêche la fermeture de la modale pendant le chargement ou si disableBackdropClick est true
  const handleClose = (
    event: React.SyntheticEvent,
    reason: "backdropClick" | "escapeKeyDown"
  ) => {
    if (loading) return;
    if (disableBackdropClick && reason === "backdropClick") return;
    onClose();
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        className: "dark:bg-gray-800 dark:text-white",
        sx: {
          "&.dark": {
            backgroundColor: "#1f2937",
            color: "#ffffff",
          },
          boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
          display: "flex",
          flexDirection: "column",
          gap: "16px",
        },
      }}
    >
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white">
        <Typography variant="h6" component="div" className="text-center w-full">
          {title}
        </Typography>
        <IconButton
          onClick={loading ? undefined : onClose}
          disabled={loading}
          sx={{
            color: "white",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
            "&.Mui-disabled": {
              color: "rgba(255, 255, 255, 0.5)",
            },
          }}
        >
          <X className="h-4 w-4" />
        </IconButton>
      </DialogTitle>
      <DialogContent className="py-6">
        <div className="flex flex-col items-center gap-4">
          {showWarningIcon && (
            <div className="mb-2">
              <AlertTriangle className="h-12 w-12 text-amber-500" />
            </div>
          )}
          <Typography variant="body1" className="text-center">
            {message}
          </Typography>
          {children}
        </div>
      </DialogContent>
      <DialogActions sx={{ display: "flex", justifyContent: "right", gap: 2 }}>
        <Button
          variant="secondary"
          className="px-5 font-bold disabled:opacity-50 disabled:cursor-wait"
          onClick={onClose}
          disabled={loading}
        >
          {cancelButtonText}
        </Button>
        <Button
          onClick={onConfirm}
          disabled={loading}
          variant={confirmButtonColor}
          className="flex gap-2 px-5 font-bold disabled:opacity-50 disabled:cursor-wait"
        >
          {loading ? (
            <>
              <CircularProgress size={16} color="inherit" />
              <span>{confirmButtonText}</span>
            </>
          ) : (
            confirmButtonText
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationModal;
