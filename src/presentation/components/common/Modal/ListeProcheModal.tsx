import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import ProcheListe from "@/presentation/components/features/patient/PatientCard/ProcheListe";
import { useAppSelector } from "@/presentation/hooks/redux";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import useAuth from "@/presentation/hooks/use-auth";
import HeaderStylePatient from "../../features/patient/PatientCard/HeaderStylePatient";
import HeaderStyleProche from "../../features/proche/HeaderStyleProche";
import HeaderStyleEmployer from "../../features/professional/employer/HeaderStyleEmployer";

interface ListeProcheModalProps {
  isOpen: boolean;
  handleClose: () => void;
}

export const ListeProcheModal = ({
  isOpen,
  handleClose,
}: ListeProcheModalProps) => {
  const role = useAppSelector((state) => state.authentification.user?.role);
  const { roleUserSelected } = useAuth();
  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <div className="flex items-center">
          {role === utilisateurs_role_enum.PROFESSIONNEL ? (
            roleUserSelected === utilisateurs_role_enum.PATIENT ? (
              <HeaderStylePatient />
            ) : (
              <HeaderStyleProche />
            )
          ) : roleUserSelected === utilisateurs_role_enum.EMPLOYER ? (
            <HeaderStyleEmployer />
          ) : (
            <HeaderStyleProche />
          )}
        </div>
        <Tooltip title="Fermer">
          <IconButton onClick={handleClose} size="small">
            <X className="h-4 w-4" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent>
        <ProcheListe />
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
