import { ProfessionalCardDTO } from "@/domain/DTOS";
import Availability from "../../features/professional/professionalCard/Availability";
import { ComponentProps, useRef } from "react";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Tooltip,
  Typography,
} from "@mui/material";
import { PROFESSIONAL_CARD_CLASSNAMES } from "@/shared/constants/professionalCardConstants";
import { X } from "lucide-react";

interface AvailabilityModalProps {
  professionalInformations: ProfessionalCardDTO;
  isOpen: boolean;
  onClose: (val: boolean) => void;
}
const AvailabilityModal = ({
  isOpen,
  onClose,
  professionalInformations,
}: AvailabilityModalProps) => {
  const handleClose = () => {
    onClose(false);
  };
  const horairesRef = useRef<HTMLDivElement>(null);
  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="flex justify-between">
        <Typography variant="h6">Disponibilité</Typography>
        <Tooltip title="Fermer">
          <IconButton onClick={handleClose} size="small">
            <X className="h-4 w-4" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent>
        <div
          ref={horairesRef}
          className={PROFESSIONAL_CARD_CLASSNAMES.AVAILABILITY_SECTION}
        >
          <Availability
            professionalInformation={professionalInformations}
            className="min-h-[270px]"
            horairesRef={horairesRef}
          />
        </div>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={handleClose}
          variant="outlined"
          sx={{ textTransform: "none" }}
        >
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AvailabilityModal;
