import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Button,
} from "@mui/material";
import { useEffect, useState } from "react";
import AddEvents from "@/presentation/components/features/professional/agenda/AddEvents/AddEvents";
import { Evenement } from "@/domain/models";
import {
  useEvenement,
  useEventState,
} from "@/presentation/hooks/agenda/events";
import { useAppSelector } from "@/presentation/hooks/redux";
import { Save, X } from "lucide-react";
import DeleteEventModal from "./DeleteEventModal";
import UpdateEventModal from "./UpdateEventModal";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { PRIMARY } from "@/shared/constants/Color";

const AddEventModal = () => {
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

  const { evenements, loading, createNewEvenement } = useEvenement();

  const {
    isAddEventModalOpen,
    timeEvent,
    idCurrentEvent,
    handleCloseAddEventModal,
  } = useAgendaState();

  const {
    title,
    est_toute_la_journee,
    description,
    est_reporte,
    date_debut,
    date_fin,
    repetition,
    initializeState,
  } = useEventState();

  const userId = useAppSelector((state) => state.authentification.user?.id);
  const currentEvenement = evenements?.find(
    (event) => event.id === idCurrentEvent
  );

  const handleSave = async () => {
    if (!userId) {
      return;
    }

    const dateDebut = new Date(date_debut);
    const dateFin = new Date(date_fin);

    if (est_toute_la_journee) {
      // Pour une journée complète, on définit le début à 00:00:00
      dateDebut.setUTCHours(0, 0, 0, 0);
      // Et la fin à 23:59:59
      dateFin.setUTCHours(0, 0, 0, 0);
    }

    const evenement: Evenement = {
      id_professionnel: userId,
      est_toute_la_journee: est_toute_la_journee,
      titre: title,
      description: description,
      date_debut: dateDebut,
      date_fin: dateFin,
      est_reportee: est_reporte,
      repetition: repetition,
    };
    console.log(evenement);
    await createNewEvenement(evenement);
    handleCloseAddEventModal();
  };

  useEffect(() => {
    console.log(idCurrentEvent);

    if (currentEvenement) {
      console.log(currentEvenement);
      initializeState(currentEvenement);
    } else if (timeEvent) {
      initializeState({
        id_professionnel: userId,
        titre: "",
        est_toute_la_journee: false,
        description: "",
        est_reportee: false,
        date_debut: timeEvent.start,
        date_fin: timeEvent.end,
        repetition: "once",
      });
    }
  }, [currentEvenement]);

  return (
    <Dialog
      open={isAddEventModalOpen}
      onClose={handleCloseAddEventModal}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle className="flex justify-between items-center bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        {currentEvenement ? "Modifier l'évènement" : "Ajouter une évènement"}
        <IconButton onClick={handleCloseAddEventModal}>
          <X className="h-4 w-4 text-white" />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <div className="py-6">
          <AddEvents />
          <DeleteEventModal />
          <UpdateEventModal
            open={isUpdateModalOpen}
            onClose={() => setIsUpdateModalOpen(false)}
            idCurrentEvent={idCurrentEvent}
          />
        </div>
      </DialogContent>
      <DialogActions>
        {currentEvenement ? (
          <Button
            variant="contained"
            onClick={() => setIsUpdateModalOpen(true)}
            sx={{ textTransform: "none", backgroundColor: PRIMARY }}
            startIcon={<Save className="h-5 w-5" />}
          >
            Enregistrer
          </Button>
        ) : (
          <Button
            variant="contained"
            onClick={handleSave}
            sx={{ textTransform: "none", backgroundColor: PRIMARY }}
            startIcon={<Save className="h-5 w-5" />}
            loading={loading}
          >
            Enregistrer
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AddEventModal;
