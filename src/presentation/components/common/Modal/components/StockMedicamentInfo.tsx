import React from "react";
import {
  Box,
  Typography,
  Chip,
  Alert,
  Card,
  CardContent,
} from "@mui/material";
import { Inventory as Package, Warning } from "@mui/icons-material";

interface StockMedicamentInfoProps {
  medicamentNom: string;
  stockInfo?: {
    id: number;
    nom: string;
    quantiteDisponible: number;
    unite?: string;
    seuil_alerte?: number;
  };
  isStockMode: boolean;
  quantiteDemandee?: number;
}

/**
 * Composant pour afficher les informations de stock d'un médicament
 * 
 * Fonctionnalités :
 * - Affichage des quantités disponibles
 * - Alertes pour stock faible
 * - Indicateurs visuels selon la disponibilité
 */
export const StockMedicamentInfo: React.FC<StockMedicamentInfoProps> = ({
  medicamentNom,
  stockInfo,
  isStockMode,
  quantiteDemandee = 1,
}) => {
  if (!isStockMode) {
    return null;
  }

  const isStockFaible = stockInfo && stockInfo.quantiteDisponible <= (stockInfo.seuil_alerte || 0);
  const isQuantiteSuffisante = stockInfo && stockInfo.quantiteDisponible >= quantiteDemandee;

  return (
    <Card variant="outlined" sx={{ mt: 1, mb: 1 }}>
      <CardContent sx={{ py: 1.5 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Package sx={{ width: 16, height: 16, color: 'primary.main' }} />
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
            Informations de stock
          </Typography>
        </Box>

        {stockInfo ? (
          <Box>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
              <Chip
                size="small"
                label={`Stock: ${stockInfo.quantiteDisponible} ${stockInfo.unite || 'unité'}`}
                color={isQuantiteSuffisante ? 'success' : 'error'}
                variant="outlined"
              />
              
              {isStockFaible && (
                <Chip
                  size="small"
                  label="Stock faible"
                  color="warning"
                  variant="filled"
                  icon={<Warning sx={{ width: 12, height: 12 }} />}
                />
              )}
            </Box>

            {!isQuantiteSuffisante && (
              <Alert severity="warning" sx={{ mt: 1 }}>
                <Typography variant="body2">
                  Quantité insuffisante en stock. Disponible: {stockInfo.quantiteDisponible}, 
                  Demandé: {quantiteDemandee}
                </Typography>
              </Alert>
            )}

            {isQuantiteSuffisante && (
              <Alert severity="success" sx={{ mt: 1 }}>
                <Typography variant="body2">
                  Médicament disponible en stock. Une sortie de stock sera créée automatiquement.
                </Typography>
              </Alert>
            )}
          </Box>
        ) : (
          <Alert severity="error" sx={{ mt: 1 }}>
            <Typography variant="body2">
              Médicament "{medicamentNom}" non trouvé en stock interne.
            </Typography>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};
