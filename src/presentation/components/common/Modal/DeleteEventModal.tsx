import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
} from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useEvenement } from "@/presentation/hooks/agenda/events";
import { useState } from "react";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { PRIMARY } from "@/shared/constants/Color";

const DeleteEventModal = () => {
  const [typeDeSuppression, setTypeDeSuppression] = useState<"this" | "all">(
    "this"
  );
  const { deleteExistingEvenement, deleteEvenementByProfessional } =
    useEvenement();

  const {
    isDeleteEventModalOpen,
    idCurrentEvent,
    handleCloseDeleteEvenementModal,
  } = useAgendaState();

  const professionalId = useAppSelector(
    (state) => state.authentification.user?.id
  );

  const handleDeleteEvenement = () => {
    if (typeDeSuppression === "this") {
      deleteExistingEvenement(idCurrentEvent);
    } else {
      deleteEvenementByProfessional(professionalId);
    }
    handleCloseDeleteEvenementModal();
  };

  return (
    <Dialog
      open={isDeleteEventModalOpen}
      onClose={handleCloseDeleteEvenementModal}
      maxWidth="sm"
    >
      <DialogTitle className="flex items-center justify-between">
        Supprimer l'événement périodique
      </DialogTitle>
      <DialogContent>
        <FormControl component="fieldset" sx={{ mb: 2 }}>
          <RadioGroup
            value={typeDeSuppression}
            onChange={(e) =>
              setTypeDeSuppression(e.target.value as "this" | "all")
            }
          >
            <FormControlLabel
              value="this"
              checked={typeDeSuppression == "this"}
              control={<Radio size="small" />}
              label="Ce événement"
            />
            <FormControlLabel
              value="all"
              checked={typeDeSuppression == "all"}
              control={<Radio size="small" />}
              label="Tous les événements"
            />
          </RadioGroup>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleCloseDeleteEvenementModal}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={handleDeleteEvenement}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
        >
          Ok
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteEventModal;
