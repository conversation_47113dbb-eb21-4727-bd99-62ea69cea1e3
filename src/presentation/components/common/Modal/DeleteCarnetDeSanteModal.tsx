import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useDeleteMedicalRecord } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDeleteMedicalRecord";

interface DeleteCarnetDeSanteModalProps {
  type: string;
  isOpen: boolean;
  handleClose: () => void;
  item: {
    id: number;
    nom: string;
  };
}

export const DeleteCarnetDeSanteModal = ({
  type,
  isOpen,
  handleClose,
  item,
}: DeleteCarnetDeSanteModalProps) => {
  const { handleDelete } = useDeleteMedicalRecord();
  const onDelete = async (type: string) => {
    await handleDelete(type, item);
    handleClose();
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="flex justify-end">
        <Tooltip title="Fermer">
          <IconButton onClick={handleClose} size="small">
            <X className="h-4 w-4" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="flex justify-center">
        <Typography variant="body1">
          Êtes-vous sûr de vouloir supprimer {item.nom} ?
        </Typography>
      </DialogContent>
      <DialogActions sx={{ p: 2, display: "flex", justifyContent: "center" }}>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={() => onDelete(type)}
          sx={{
            textTransform: "none",
            backgroundColor: PRIMARY,
          }}
        >
          Supprimer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
