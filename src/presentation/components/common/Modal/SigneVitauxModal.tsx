import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  TextField,
  Grid,
  Switch,
  FormControlLabel,
} from "@mui/material";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import { signe_vitaux } from "@/domain/models/SigneVitaux";

interface SigneVitauxModalProps {
  isOpen: boolean;
  handleClose: () => void;
  onSubmit: (data: Omit<signe_vitaux, "id">) => Promise<void>;
  initialData?: signe_vitaux;
  patientId: number;
}

export const SigneVitauxModal = ({
  isOpen,
  handleClose,
  onSubmit,
  initialData,
  patientId,
}: SigneVitauxModalProps) => {
  const [formData, setFormData] = useState<Omit<signe_vitaux, "id">>({
    id_patient: patientId,
    taille: 0,
    poid: 0,
    indice_masse_corporel: 0,
    temperature: 0,
    circonference_tete: 0,
    frequence_cardiaque: 0,
    frequence_respiratoire: 0,
    sa02: 0,
    niveau_glucose: 0,
    confidentielite: false,
  });

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleChange = (field: keyof signe_vitaux) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = field === "confidentielite" ? event.target.checked : Number(event.target.value);
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const calculateIMC = () => {
    if (formData.taille > 0 && formData.poid > 0) {
      const tailleEnMetres = formData.taille / 100;
      const imc = formData.poid / (tailleEnMetres * tailleEnMetres);
      setFormData((prev) => ({
        ...prev,
        indice_masse_corporel: Number(imc.toFixed(2)),
      }));
    }
  };

  useEffect(() => {
    calculateIMC();
  }, [formData.taille, formData.poid]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit(formData);
    handleClose();
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle className="flex items-center justify-between">
        <Typography variant="h6">
          {initialData ? "Modifier les signes vitaux" : "Ajouter des signes vitaux"}
        </Typography>
        <IconButton onClick={handleClose} size="small">
          <X className="h-4 w-4" />
        </IconButton>
      </DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                label="Taille (cm)"
                type="number"
                fullWidth
                value={formData.taille}
                onChange={handleChange("taille")}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Poids (kg)"
                type="number"
                fullWidth
                value={formData.poid}
                onChange={handleChange("poid")}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="IMC"
                type="number"
                fullWidth
                value={formData.indice_masse_corporel}
                InputProps={{ readOnly: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Température (°C)"
                type="number"
                fullWidth
                value={formData.temperature}
                onChange={handleChange("temperature")}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Circonférence tête (cm)"
                type="number"
                fullWidth
                value={formData.circonference_tete}
                onChange={handleChange("circonference_tete")}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Fréquence cardiaque (bpm)"
                type="number"
                fullWidth
                value={formData.frequence_cardiaque}
                onChange={handleChange("frequence_cardiaque")}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Fréquence respiratoire"
                type="number"
                fullWidth
                value={formData.frequence_respiratoire}
                onChange={handleChange("frequence_respiratoire")}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="SA02 (%)"
                type="number"
                fullWidth
                value={formData.sa02}
                onChange={handleChange("sa02")}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Niveau de glucose"
                type="number"
                fullWidth
                value={formData.niveau_glucose}
                onChange={handleChange("niveau_glucose")}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.confidentielite}
                    onChange={handleChange("confidentielite")}
                  />
                }
                label="Confidentiel"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button variant="outlined" onClick={handleClose}>
            Annuler
          </Button>
          <Button variant="contained" type="submit">
            {initialData ? "Modifier" : "Ajouter"}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
