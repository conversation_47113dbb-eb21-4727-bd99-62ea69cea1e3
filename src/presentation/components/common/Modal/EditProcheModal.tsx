import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useProche } from "@/presentation/hooks/use-proches";
import { Proche } from "@/domain/models";
import { useEffect } from "react";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import ProcheForm from "@/presentation/pages/patient/families/ProcheForm";
import HeaderStyleProche from "../../features/proche/HeaderStyleProche";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { useAppSelector } from "@/presentation/hooks/redux";

interface EditProcheModalProps {
  isOpen: boolean;
  handleClose: () => void;
  proche: Proche;
}

export const EditProcheModal = ({
  isOpen,
  handleClose,
  proche,
}: EditProcheModalProps) => {
  const { loading, handleEditProche } = useProche();
  const { procheInfo, initProcheData } = useConsultationState();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const handleSave = async () => {
    await handleEditProche(proche.id, procheInfo);
    handleClose();
  };

  useEffect(() => {
    if (proche) {
      initProcheData(proche);
    }
  }, [proche]);

  return (
    <Dialog open={isOpen} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="flex items-center justify-between bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        {role === utilisateurs_role_enum.PATIENT ? (
          <Typography variant="body1">
            {proche.nom} {proche.prenom}
          </Typography>
        ) : (
          <HeaderStyleProche />
        )}
        <Tooltip title="Fermer">
          <IconButton onClick={handleClose} size="small">
            <X className="h-4 w-4 text-white" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="mt-2">
        <Typography variant="h6" component="div">
          Modifier un proche
        </Typography>
        <ProcheForm />
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          loading={loading}
        >
          Enregistrer
        </Button>
      </DialogActions>
    </Dialog>
  );
};
