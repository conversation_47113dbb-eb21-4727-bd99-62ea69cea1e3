import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Typography,
  Box,
  Button,
} from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useState } from "react";
import {
  useAvailabilitySettings,
  useAvailabilitySettingState,
} from "@/presentation/hooks/agenda/settings";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { creneau_horaire } from "@/domain/models";
import { PRIMARY } from "@/shared/constants/Color";

const DeleteSettingsModal = () => {
  const [typeDeSuppression, setTypeDeSuppression] = useState<
    "this" | "weekly" | "all"
  >("this");
  const { deleteSettings, deleteThis, deleteWeeklySettings } =
    useAvailabilitySettings();

  const { settings, handleAddDateException, handleDeleteTimeSlotAt } =
    useAvailabilitySettingState();

  const {
    isDeleteSettingsModalOpen,
    timeEvent,
    handleCloseDeleteSettingsModal,
  } = useAgendaState();

  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );

  const handleDelete = () => {
    if (typeDeSuppression === "this") {
      // supprimer (jeudi, 13 mars 9:00am à 5:00pm)
      const start = format(new Date(timeEvent.start), "HH:mm");
      const end = format(new Date(timeEvent.end), "HH:mm");

      const creneau_horaire: creneau_horaire = {
        heure_debut: start,
        heure_fin: end,
      };
      const dateException = handleAddDateException(
        new Date(timeEvent.start),
        creneau_horaire
      );
      console.log(settings);

      deleteThis(dateException, settings.id);
    } else if (typeDeSuppression === "weekly") {
      // supprimer (jeudi, 9:00am à 5:00pm)
      const jour = format(new Date(timeEvent.start), "EEEE", {
        locale: fr,
      });
      const newSchedule = handleDeleteTimeSlotAt(jour); // supprimer le crenaux du jour selectionnée
      deleteWeeklySettings(newSchedule, settings.id);
    } else {
      // supprimer definitivement plainning de rdv complet
      deleteSettings(professionalId);
    }
    handleCloseDeleteSettingsModal();
  };

  return (
    <Dialog
      open={isDeleteSettingsModalOpen}
      onClose={handleCloseDeleteSettingsModal}
      maxWidth="sm"
    >
      <DialogTitle className="flex items-center justify-between">
        Supprimer la disponibilité des rendez-vous
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" sx={{ mb: 2 }}>
          Les rendez-vous déjat réservés ne seront pas supprimés.
        </Typography>
        <FormControl component="fieldset" sx={{ mb: 2 }}>
          <RadioGroup
            value={typeDeSuppression}
            onChange={(e) =>
              setTypeDeSuppression(e.target.value as "this" | "weekly" | "all")
            }
          >
            <FormControlLabel
              value="this"
              checked={typeDeSuppression == "this"}
              control={<Radio size="small" />}
              label={
                <>
                  <p>Cette semaine uniquement</p>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                      mb: 1,
                    }}
                  >
                    <Typography variant="body2">
                      {format(new Date(timeEvent.start), "EEEE d MMMM", {
                        locale: fr,
                      })}
                    </Typography>
                    <Typography variant="body2">
                      {format(new Date(timeEvent.start), "HH:mm")} à{" "}
                      {format(new Date(timeEvent.end), "HH:mm")}
                    </Typography>
                  </Box>
                </>
              }
            />
            {timeEvent.type != "exception" && (
              <FormControlLabel
                value="weekly"
                checked={typeDeSuppression == "weekly"}
                control={<Radio size="small" />}
                label={
                  <>
                    <p>Toutes les semaines</p>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                        mb: 1,
                      }}
                    >
                      <Typography variant="body2">
                        {format(new Date(timeEvent.start), "EEEE", {
                          locale: fr,
                        })}
                        ,
                      </Typography>
                      <Typography variant="body2">
                        {format(new Date(timeEvent.start), "HH:mm")} à{" "}
                        {format(new Date(timeEvent.end), "HH:mm")}
                      </Typography>
                    </Box>
                  </>
                }
              />
            )}
            <FormControlLabel
              value="all"
              checked={typeDeSuppression == "all"}
              control={<Radio size="small" />}
              label={
                <>
                  <p>Toutes les disponibilités</p>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                      mb: 1,
                    }}
                  >
                    <Typography variant="body2">
                      supprimer definitivement le plainning de rendez-vous
                      complet
                    </Typography>
                  </Box>
                </>
              }
            />
          </RadioGroup>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={handleCloseDeleteSettingsModal}
          sx={{ textTransform: "none" }}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={handleDelete}
          sx={{ textTransform: "none", backgroundColor: PRIMARY }}
          // loading={loading}
        >
          Ok
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteSettingsModal;
