import React from 'react';
import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material';
import { rendez_vous_statut_enum } from '@/domain/models/enums';
import { DESTRUCTIVE, EVENT_COLOR, GRE<PERSON>, PRIMARY } from '@/shared/constants/Color';

const statusColors = {
  [rendez_vous_statut_enum.A_VENIR]: PRIMARY, // primary blue
  [rendez_vous_statut_enum.TERMINER]: GREEN, // success green
  [rendez_vous_statut_enum.ANNULER]: DESTRUCTIVE, // error red
  [rendez_vous_statut_enum.REPORTER]: EVENT_COLOR, // warning orange
  [rendez_vous_statut_enum.MANQUER]: DESTRUCTIVE, // error red
} as const;

interface AppointmentFiltersProps {
  searchQuery: string;
  selectedStatus: string;
  onSearchChange: (value: string) => void;
  onStatusChange: (value: string) => void;
}

const AppointmentFilters: React.FC<AppointmentFiltersProps> = ({
  searchQuery,
  selectedStatus,
  onSearchChange,
  onStatusChange,
}) => {
  return (
    <div className="flex gap-4 mb-6">
      <TextField
        label="Rechercher"
        variant="outlined"
        size="medium"
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        className="flex-1"
        placeholder="Rechercher par nom, prénom ou motif..."
      />
      <FormControl sx={{ minWidth: 130 }}>
        <InputLabel>Filtres</InputLabel>
        <Select
          value={selectedStatus}
          label="Filtres"
          onChange={(e) => onStatusChange(e.target.value)}
        >
          <MenuItem value="all">
            <em>Tous</em>
          </MenuItem>
          <MenuItem value="today">
            Aujourd'hui
          </MenuItem>
          {Object.values(rendez_vous_statut_enum).map((statusValue) => (
            <MenuItem key={statusValue} value={statusValue}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div
                  style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: statusColors[statusValue as keyof typeof statusColors],
                  }}
                />
                {statusValue}
              </div>
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </div>
  );
}

export default AppointmentFilters;