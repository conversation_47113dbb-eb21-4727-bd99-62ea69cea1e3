import { Box, Typography, IconButton, Tooltip } from "@mui/material";
import { Edit2, Trash2, X } from "lucide-react";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

const EventPopoverHeader = () => {
  const {
    selectedEvent,
    handleClosePopover,
    handleEditEvent,
    handleDeleteEvent,
  } = useAgendaState();

  const { appointmentProfessional } = useConsultationState();

  const currentAppointment =
    selectedEvent.type === rendez_vous_statut_enum.A_VENIR ||
    selectedEvent.type === rendez_vous_statut_enum.ANNULER
      ? appointmentProfessional?.find(
          (appointment) => appointment.id === selectedEvent.id
        )
      : null;

  return (
    <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
      <Typography sx={{ fontWeight: "bold" }}>
        {currentAppointment
          ? `Rendez-vous avec ${currentAppointment.patient.nom} ${currentAppointment.patient.prenom}`
          : selectedEvent.title}
      </Typography>
      <Box>
        {selectedEvent.type !== rendez_vous_statut_enum.A_VENIR &&
          selectedEvent.type !== rendez_vous_statut_enum.ANNULER &&
          selectedEvent.type !== rendez_vous_statut_enum.REPORTER && (
            <>
              <Tooltip title="Modifier" enterDelay={500}>
                <IconButton size="small" onClick={handleEditEvent}>
                  <Edit2 size={18} />
                </IconButton>
              </Tooltip>
              <Tooltip title="Supprimer" enterDelay={500}>
                <IconButton size="small" onClick={handleDeleteEvent}>
                  <Trash2 size={18} />
                </IconButton>
              </Tooltip>
            </>
          )}
        <Tooltip title="Fermer" enterDelay={500}>
          <IconButton size="small" onClick={handleClosePopover}>
            <X size={18} />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );
};

export default EventPopoverHeader;
