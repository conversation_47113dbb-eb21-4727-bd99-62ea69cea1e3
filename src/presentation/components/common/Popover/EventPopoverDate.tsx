import { Box, Typography } from "@mui/material";
import { CalendarDays, CalendarX2 } from "lucide-react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

const EventPopoverDate = () => {
  const { selectedEvent } = useAgendaState();
  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
      {selectedEvent.type != rendez_vous_statut_enum.ANNULER ? (
        <CalendarDays size={18} />
      ) : (
        <CalendarX2 size={18} />
      )}
      <Typography variant="body2">
        {format(new Date(selectedEvent.start), "EEEE d MMMM yyyy", {
          locale: fr,
        })}
      </Typography>
    </Box>
  );
};

export default EventPopoverDate;
