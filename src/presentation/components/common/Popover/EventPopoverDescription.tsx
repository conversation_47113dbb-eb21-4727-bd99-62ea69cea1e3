import { Box, Typography, Divider } from "@mui/material";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { Check, X } from "lucide-react";
import { DESTRUCTIVE, GREEN } from "@/shared/constants/Color";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

const EventPopoverDescription = () => {
  const { selectedEvent } = useAgendaState();

  return (
    <>
      <Divider sx={{ my: 1 }} />
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <Typography
          variant="body2"
          color="text.secondary"
          className="flex items-center gap-1"
        >
          {selectedEvent.type === rendez_vous_statut_enum.ANNULER && (
            <X size={18} color={DESTRUCTIVE} />
          )}
          {selectedEvent.type}
        </Typography>
      </Box>
    </>
  );
};

export default EventPopoverDescription;
