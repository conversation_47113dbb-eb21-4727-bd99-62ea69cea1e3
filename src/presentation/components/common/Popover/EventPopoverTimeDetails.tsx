import { Box, Typography } from "@mui/material";
import { Clock } from "lucide-react";
import { format } from "date-fns";
import { useAgendaState } from "@/presentation/hooks/agenda";

const EventPopoverTimeDetails = () => {
  const { selectedEvent } = useAgendaState();

  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
      <Clock size={18} />
      <Typography variant="body2">
        {selectedEvent.allDay
          ? "Toute la journée"
          : `${format(new Date(selectedEvent.start), "HH:mm")} - ${format(
              new Date(selectedEvent.end),
              "HH:mm"
            )}`}
      </Typography>
    </Box>
  );
};

export default EventPopoverTimeDetails;
