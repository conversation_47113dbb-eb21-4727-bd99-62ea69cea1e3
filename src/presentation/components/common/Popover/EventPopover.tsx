import { Popover, Box, Divider } from "@mui/material";
import { useAgendaState } from "@/presentation/hooks/agenda";
import EventPopoverHeader from "./EventPopoverHeader";
import EventPopoverTimeDetails from "./EventPopoverTimeDetails";
import EventPopoverDate from "./EventPopoverDate";
import EventPopoverDescription from "./EventPopoverDescription";

const EventPopover = () => {
  const { selectedEvent, anchorEl, handleClosePopover } = useAgendaState();
  const open = Boolean(anchorEl);

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={handleClosePopover}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      PaperProps={{
        sx: {
          width: "300px",
          p: 2,
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
          borderRadius: "8px",
        },
      }}
    >
      <Box>
        {/* Header */}
        <EventPopoverHeader />

        <Divider sx={{ my: 1 }} />

        {/* Time Details */}
        <EventPopoverTimeDetails />

        {/* Date */}
        <EventPopoverDate />

        {/* Description */}
        {selectedEvent.type && <EventPopoverDescription />}
      </Box>
    </Popover>
  );
};

export default EventPopover;
