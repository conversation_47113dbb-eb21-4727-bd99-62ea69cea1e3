import React from "react";
import { DataGrid } from "@mui/x-data-grid";
import { Paper } from "@mui/material";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { localeText } from "@/shared/constants/localeText";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { active_tab_enum, utilisateurs_role_enum } from "@/domain/models/enums";
import { AppointmentColumnsProfessional } from "@/presentation/components/features/professional/appointment/AppointmentColumnsProfessional";
import { AppointmentColumnsPatient } from "@/presentation/components/features/patient/appointments/AppointmentColumnsPatient";
import { FacturationDTO, ProfessionnelPatientDTO } from "@/domain/DTOS";
import { FacturationColumns } from "../historiqueCarnetSante/component/FacturationColumns";
import { FacturationProfessionalColumns } from "../../features/facturation/FacturationProfessionalColumns";
import {
  demande_adhesion,
  Proche,
  InvitationDash,
  Employer,
  consultation_medical,
  signe_vitaux,
  laboratoire_diagnostics,
  EntreesStocks,
  SortiesStocks,
  Stocks,
  Fournisseurs,
} from "@/domain/models";
import { LotWithStockData, LowStockCardDTO } from "@/domain/DTOS/StockDTO";
import { AdhesionRequestColumns } from "../../features/admin/adhesionRequests/AdhesionRequestColumns";
import { twMerge } from "tailwind-merge";
import { ProcheColumns } from "@/presentation/pages/patient/families/ProcheColumns";

import { DashInvitationColumns } from "../../features/admin/dashInvitations/DashInvitationColumns";
import { RecentPatientColumns } from "@/presentation/pages/professional/patients/RecentPatientColumns.tsx";
import { RecentEmployeColumns } from "../../features/dash/employees/RecentEmployeColumns.tsx";
import { UpdatedEmployeColumns } from "../../features/dash/employees/UpdatedEmployeColumns.tsx";
import { RecentUpdatedPatientColumns } from "@/presentation/pages/professional/patients/RecentUpdatedPatientColumns.tsx";
import { EmployerColumns } from "@/presentation/pages/dash/employer/employer_columns.tsx";
import { HistoriqueConsultationColumns } from "../../features/consultation/HistoriqueConsultationColumns.tsx";
import { SigneVitauxColumns } from "../../features/signeVitaux/SigneVItauxColumns.tsx";
import { LaboratoireDiagnosticsColumns } from "../../features/LaboratoireDiagnostic/LaboratoireDiagnostiqueColumns.tsx";
import { EntreeStockColumns } from "../../features/stock/EntreeStockColumns.tsx";
import { SortieStockColumns } from "../../features/stock/SortieStockColumns.tsx";
import { ProduitColumns } from "../../features/stock/ProduitColumns.tsx";
import { FournisseurColumns } from "../../features/stock/FournisseurColumns.tsx";
import { AlertePeremptionColumns } from "../../features/stock/AlertePeremptionColumns.tsx";
import { AlerteStockColumns } from "../../features/stock/AlerteStockColumns.tsx";
import { MouvementRecentColumns } from "../../features/stock/MouvementRecentColumns.tsx";
import { HistoriqueConsultationPatientColumns } from "../../features/consultation/HistoriqueConsultationPatientColumns.tsx";
import { ConsultationDetails } from "@/domain/DTOS/MedicalConsultationDetailsDTO.ts";
import { SigneVitauxPatientColumns } from "../../features/signeVitaux/SigneVitauxPatientColumns.tsx";

interface MouvementRecent {
  id?: number;
  name: string;
  type: "Entrée" | "Sortie";
  quantite: number;
  date: string;
}

interface ListDataGridProps extends React.ComponentProps<"div"> {
  data:
  | AppointmentProfessionalDTO[]
  | laboratoire_diagnostics[]
  | signe_vitaux[]
  | AppointmentPatientDTO[]
  | FacturationDTO[]
  | demande_adhesion[]
  | Employer[]
  | Proche[]
  | ProfessionnelPatientDTO[]
  | consultation_medical[]
  | InvitationDash[]
  | EntreesStocks[]
  | SortiesStocks[]
  | Stocks[]
  | Fournisseurs[]
  | LotWithStockData[]
  | LowStockCardDTO[]
  | MouvementRecent[]
  | ConsultationDetails[];

  type: string;
  height?: string;
  width?: string;
  onViewConsultation?: (consultation: any) => void;
  onViewSigneVitaux?: (signeVitaux: any) => void;
  onViewLaboratoireDiagnostics?: (laboratoireDiagnostics: any) => void;
}

const getColumns = (
  type: string,
  onViewConsultation?: (consultation: any) => void,
  onViewSigneVitaux?: (signeVitaux: any) => void,
  onViewLaboratoireDiagnostics?: (laboratoireDiagnostics: any) => void
) => {
  switch (type) {
    case utilisateurs_role_enum.PROFESSIONNEL:
      return AppointmentColumnsProfessional();
    case "employer":
      return EmployerColumns();
    case utilisateurs_role_enum.PATIENT:
      return AppointmentColumnsPatient();
    case active_tab_enum.facturation:
      return FacturationColumns();
    case "facturation_professional":
      return FacturationProfessionalColumns();
    case "adhesion_requests":
      return AdhesionRequestColumns();
    case "proche":
      return ProcheColumns();
    case "dash_invitations":
      return DashInvitationColumns();
    case "recent_patient":
      return RecentPatientColumns();
    case "recent_employe":
      return RecentEmployeColumns();
    case "updated_employe":
      return UpdatedEmployeColumns();
    case "recent_updated_patient":
      return RecentUpdatedPatientColumns();
    case "historique_consultation":
      return HistoriqueConsultationColumns(onViewConsultation);
    case "historique_consultation_patient":
      return HistoriqueConsultationPatientColumns();
    case "signe_vitaux":
      return SigneVitauxColumns(onViewSigneVitaux);
    case "signe_vitaux_patient":
      return SigneVitauxPatientColumns();
    case "laboratoire_diagnostics":
      return LaboratoireDiagnosticsColumns(onViewLaboratoireDiagnostics);
    case "entree_stock":
      return EntreeStockColumns();
    case "sortie_stock":
      return SortieStockColumns();
    case "produit":
      return ProduitColumns();
    case "fournisseur":
      return FournisseurColumns();
    case "alerte_peremption":
      return AlertePeremptionColumns();
    case "alerte_stock":
      return AlerteStockColumns();
    case "mouvement_recent":
      return MouvementRecentColumns();
    default:
      return [];
  }
};

const ListDataGrid = ({
  data,
  type,
  className,
  height = "h-[371px]",
  width = "w-full",
  onViewConsultation,
  onViewSigneVitaux,
  onViewLaboratoireDiagnostics,
}: ListDataGridProps) => {
  const columns = getColumns(
    type,
    onViewConsultation,
    onViewSigneVitaux,
    onViewLaboratoireDiagnostics
  );

  return (
    <Paper
      component="div"
      className={twMerge(`${height} ${width} shadow-lg`, className)}
    >
      <DataGrid
        rows={data}
        columns={columns}
        initialState={{
          pagination: {
            paginationModel: { page: 0, pageSize: 5 },
          },
        }}
        pageSizeOptions={[5, 10, 20]}
        localeText={localeText}
        sx={{
          "& .font-semibold": {
            fontWeight: "bold",
          },
        }}
        disableRowSelectionOnClick
        disableVirtualization
      />
    </Paper>
  );
};

export default ListDataGrid;
