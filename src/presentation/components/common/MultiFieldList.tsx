import React, { useState } from "react";
import { Plus, Trash2 } from "lucide-react";

export interface MultiFieldListProps<T> {
  items: T[];
  onItemsChange: (items: T[]) => void;
  renderItem: (item: T, index: number) => React.ReactNode;
  renderForm: (
    onAdd: (item: T) => void,
    isFormVisible: boolean,
    setIsFormVisible: (visible: boolean) => void
  ) => React.ReactNode;
  emptyMessage: string;
  addButtonLabel: string;
}

function MultiFieldList<T>({
  items,
  onItemsChange,
  renderItem,
  renderForm,
  emptyMessage,
  addButtonLabel,
}: MultiFieldListProps<T>) {
  const [isFormVisible, setIsFormVisible] = useState(false);

  const handleAdd = (item: T) => {
    onItemsChange([...items, item]);
    setIsFormVisible(false);
  };

  const handleRemove = (index: number) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    onItemsChange(newItems);
  };

  return (
    <div className="space-y-4">
      {items.length === 0 && !isFormVisible ? (
        <p className="text-gray-500 italic">{emptyMessage}</p>
      ) : (
        <div className="space-y-3">
          {items.map((item, index) => (
            <div
              key={index}
              className="flex items-start justify-between p-3 border border-gray-200 rounded-lg"
            >
              <div className="flex-1">{renderItem(item, index)}</div>
              <button
                type="button"
                onClick={() => handleRemove(index)}
                className="text-red-500 hover:text-red-700 p-1"
                aria-label="Supprimer"
              >
                <Trash2 size={18} />
              </button>
            </div>
          ))}
        </div>
      )}

      {isFormVisible ? (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          {renderForm(handleAdd, isFormVisible, setIsFormVisible)}
        </div>
      ) : (
        <button
          type="button"
          onClick={() => setIsFormVisible(true)}
          className="flex items-center text-meddoc-primary hover:text-meddoc-fonce"
        >
          <Plus size={18} className="mr-1" />
          {addButtonLabel}
        </button>
      )}
    </div>
  );
}

export default MultiFieldList;
