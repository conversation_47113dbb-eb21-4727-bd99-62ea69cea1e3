import { twMerge } from "tailwind-merge";
import { ComponentProps } from "react";

// Ajouter des variantes: primary et secondary (par défaut primary)
type ButtonProps = ComponentProps<"button"> & {
  children: React.ReactNode;
  variant?: "primary" | "secondary" | "danger" | "gradient";
  className?: string;
};

const Button = ({
  className,
  children,
  variant = "primary",
  ...props
}: ButtonProps) => {
  if (variant === "gradient") {
    return (
      <button
        className={twMerge(
          "flex items-center justify-center rounded-md bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white p-2",
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }

  if (variant === "primary") {
    return (
      <button
        className={twMerge(
          "flex items-center justify-center rounded-md  bg-meddoc-primary text-white p-2",
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }

  if (variant === "secondary") {
    return (
      <button
        className={twMerge(
          "flex items-center justify-center rounded-md bg-gray-500 hover:bg-gray-600 text-white p-2",
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }
  if (variant === "danger") {
    return (
      <button
        className={twMerge(
          "flex items-center justify-center rounded-md bg-red-500 hover:bg-red-600 text-white p-2",
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }
};
export default Button;
