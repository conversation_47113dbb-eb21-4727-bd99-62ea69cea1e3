import { utilisateurs_role_enum } from "@/domain/models/enums";
import LogoDass from "../features/dash/dashboard/LogoDass";


interface PageTitleProps {
  titre: string;
  description: string;
  logo?: string;
  boutton?: React.ReactNode;
  role?: string;
}

const PageTitle = ({
  titre,
  description,
  logo,
  boutton,
  role,
}: PageTitleProps) => {

  return (
    <div className="max-w-7xl mx-auto bg-gradient-to-r from-meddoc-primary to-meddoc-fonce text-white p-6 shadow-lg mb-4">
      <div className="flex items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">
            {titre}
          </h1>
          <p className="text-white/90 text-sm sm:text-base">
            {description}

          </p>
        </div>
        {
          logo && role === utilisateurs_role_enum.DASH && (
            <div className="flex items-center gap-3">
              {boutton}

              <div className="w-20 h-20 relative">
                <img
                  src={logo}
                  alt="Logo"
                  className="w-full h-full absolute"
                />
              </div>

            </div>
          )
        }
      </div>
    </div>
  );
};

export default PageTitle;
