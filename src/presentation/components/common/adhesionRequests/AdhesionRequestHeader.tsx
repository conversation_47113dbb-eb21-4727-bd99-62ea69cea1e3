import React from "react";
import {
  Typo<PERSON>,
  Box,
  TextField,
  InputAdornment,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
} from "@mui/material";
import { Search } from "lucide-react";
import {
  GRE<PERSON>,
  EVENT_COLOR,
  PRIMARY,
  DESTRUCTIVE,
} from "@/shared/constants/Color";
import { demande_adhesion_statut_enum } from "@/domain/models/enums";
import {
  ADHESION_REQUEST_STATUS_LABELS,
  ADHESION_REQUEST_STATUS_COLORS,
} from "@/presentation/constants/adhesionRequest.constants";

interface AdhesionRequestHeaderProps {
  title?: string;
  description?: string;
  searchQuery?: string;
  statusFilter?: string;
  onSearchChange?: (query: string) => void;
  onStatusFilterChange?: (status: string) => void;
}

export const AdhesionRequestHeader: React.FC<AdhesionRequestHeaderProps> = ({
  title = "Demandes d'adhésion",
  description = "<PERSON><PERSON><PERSON> les demandes d'adhésion des professionnels de santé",
  searchQuery = "",
  statusFilter = "all",
  onSearchChange,
  onStatusFilterChange,
}) => {
  return (
    <Box className="mb-6">
      <Typography
        variant="h4"
        component="h1"
        sx={{
          mb: { xs: 2, sm: 3 },
          fontWeight: 600,
          fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2rem" },
          color: "#07294A",
          letterSpacing: "-0.01em",
        }}
      >
        {title}
      </Typography>

      <Typography
        variant="body1"
        sx={{
          mb: { xs: 3, sm: 4 },
          fontSize: { xs: "0.875rem", sm: "1rem" },
          lineHeight: 1.6,
          color: "#07294A",
          fontWeight: 400,
        }}
      >
        {description}
      </Typography>

      {(onSearchChange || onStatusFilterChange) && (
        <Box
          sx={{
            mb: 3,
            display: "flex",
            gap: 2,
            flexWrap: { xs: "wrap", sm: "nowrap" },
            width: "100%",
          }}
        >
          {onSearchChange && (
            <TextField
              fullWidth
              placeholder="Rechercher par nom, prénom, email..."
              variant="outlined"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              // Utilisation de l'icône de recherche
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search size={20} />
                  </InputAdornment>
                ),
              }}
              sx={{
                flex: "1 1 auto",
                "& .MuiOutlinedInput-root": {
                  borderRadius: "8px",
                },
                "& .MuiInputBase-root": {
                  paddingLeft: "12px",
                },
              }}
            />
          )}

          {onStatusFilterChange && (
            <FormControl
              sx={{ width: { xs: "100%", sm: "250px" }, flex: "0 0 auto" }}
            >
              <Select
                value={statusFilter}
                onChange={(e: SelectChangeEvent<string>) =>
                  onStatusFilterChange(e.target.value)
                }
                displayEmpty
                fullWidth
                variant="outlined"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                  },
                  height: "56px", // Match the height of the search field
                }}
              >
                <MenuItem value="all">
                  <Box
                    sx={{ display: "flex", alignItems: "center", gap: "8px" }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        bgcolor: "#CCCCCC",
                      }}
                    />
                    Tous les statuts
                  </Box>
                </MenuItem>
                <MenuItem value={demande_adhesion_statut_enum.EN_ATTENTE}>
                  <Box
                    sx={{ display: "flex", alignItems: "center", gap: "8px" }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        bgcolor: EVENT_COLOR,
                      }}
                    />
                    {
                      ADHESION_REQUEST_STATUS_LABELS[
                        demande_adhesion_statut_enum.EN_ATTENTE
                      ]
                    }
                  </Box>
                </MenuItem>
                <MenuItem value={demande_adhesion_statut_enum.LU}>
                  <Box
                    sx={{ display: "flex", alignItems: "center", gap: "8px" }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        bgcolor: PRIMARY,
                      }}
                    />
                    {
                      ADHESION_REQUEST_STATUS_LABELS[
                        demande_adhesion_statut_enum.LU
                      ]
                    }
                  </Box>
                </MenuItem>
                <MenuItem value={demande_adhesion_statut_enum.APPROUVEE}>
                  <Box
                    sx={{ display: "flex", alignItems: "center", gap: "8px" }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        bgcolor: GREEN,
                      }}
                    />
                    {
                      ADHESION_REQUEST_STATUS_LABELS[
                        demande_adhesion_statut_enum.APPROUVEE
                      ]
                    }
                  </Box>
                </MenuItem>
                <MenuItem value={demande_adhesion_statut_enum.REJETEE}>
                  <Box
                    sx={{ display: "flex", alignItems: "center", gap: "8px" }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        bgcolor: DESTRUCTIVE,
                      }}
                    />
                    {
                      ADHESION_REQUEST_STATUS_LABELS[
                        demande_adhesion_statut_enum.REJETEE
                      ]
                    }
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>
          )}
        </Box>
      )}
    </Box>
  );
};

export default AdhesionRequestHeader;
