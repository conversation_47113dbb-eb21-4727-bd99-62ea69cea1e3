import React, { useState } from "react";
import { DataGrid, GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import {
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import { localeText } from "@/shared/constants/localeText";
import { demande_adhesion } from "@/domain/models";
import { Check, Eye, MoreVertical, Trash2 } from "lucide-react";
import { AdhesionRequestDetailModal } from "./AdhesionRequestDetailModal";
import ConfirmationModal from "../Modal/ConfirmationModal";

interface AdhesionRequestListProps {
  data: demande_adhesion[];
  onMarkAsRead?: (id: number) => void;
  onDelete?: (id: number) => void;
}

export const AdhesionRequestList: React.FC<AdhesionRequestListProps> = ({
  data,
  onMarkAsRead,
  onDelete,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRequest, setSelectedRequest] =
    useState<demande_adhesion | null>(null);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleMenuOpen = (
    event: React.MouseEvent<HTMLElement>,
    request: demande_adhesion
  ) => {
    setAnchorEl(event.currentTarget);
    setSelectedRequest(request);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleViewDetails = () => {
    setDetailModalOpen(true);
    handleMenuClose();
  };

  const handleMarkAsRead = async () => {
    if (onMarkAsRead && selectedRequest) {
      setIsLoading(true);
      try {
        await onMarkAsRead(selectedRequest.id);
        // Fermer le modal de détails après l'action
        setDetailModalOpen(false);
      } catch (error) {
        console.error("Erreur lors du marquage comme lu:", error);
      } finally {
        setIsLoading(false);
        handleMenuClose();
      }
    }
  };

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (onDelete && selectedRequest) {
      setIsLoading(true);
      try {
        await onDelete(selectedRequest.id);
        // Fermer le modal de détails après l'action
        setDetailModalOpen(false);
      } catch (error) {
        console.error("Erreur lors de la suppression:", error);
      } finally {
        setIsLoading(false);
        setIsDeleteModalOpen(false);
        handleMenuClose();
      }
    }
  };

  const columns: GridColDef[] = [
    {
      field: "nomComplet",
      headerName: "Nom complet",
      width: 200,
      renderCell: (params: GridRenderCellParams<demande_adhesion>) => (
        <span>
          {params.row.prenom} {params.row.nom}
        </span>
      ),
      headerClassName: "font-semibold",
    },
    {
      field: "email",
      headerName: "Email",
      width: 250,
      headerClassName: "font-semibold",
    },
    {
      field: "telephone",
      headerName: "Téléphone",
      width: 150,
      headerClassName: "font-semibold",
    },
    {
      field: "type_etablissement",
      headerName: "Type d'établissement",
      width: 200,
      headerClassName: "font-semibold",
    },
    {
      field: "status",
      headerName: "Statut",
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value === "lu" ? "Lu" : "Non lu"}
          color={params.value === "lu" ? "success" : "warning"}
          size="small"
        />
      ),
      headerClassName: "font-semibold",
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      renderCell: (params: GridRenderCellParams<demande_adhesion>) => (
        <div>
          <Tooltip title="Actions">
            <IconButton
              onClick={(e) => handleMenuOpen(e, params.row)}
              size="small"
            >
              <MoreVertical className="h-5 w-5" />
            </IconButton>
          </Tooltip>
        </div>
      ),
      headerClassName: "font-semibold",
    },
  ];

  return (
    <>
      <Paper className="w-full shadow-lg">
        <DataGrid
          rows={data}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          pageSizeOptions={[5, 10, 25, 50]}
          localeText={localeText}
          sx={{
            "& .font-semibold": {
              fontWeight: "bold",
            },
            minHeight: "500px",
          }}
          disableRowSelectionOnClick
          disableVirtualization
        />
      </Paper>

      {/* Menu d'actions */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        slotProps={{
          paper: {
            elevation: 3,
            sx: { minWidth: 200, borderRadius: "8px" },
          },
        }}
      >
        <MenuItem onClick={handleViewDetails}>
          <ListItemIcon>
            <Eye size={18} />
          </ListItemIcon>
          <ListItemText>Voir les détails</ListItemText>
        </MenuItem>

        {selectedRequest && selectedRequest.status !== "lu" && onMarkAsRead && (
          <MenuItem onClick={handleMarkAsRead}>
            <ListItemIcon>
              <Check size={18} />
            </ListItemIcon>
            <ListItemText>Marquer comme lu</ListItemText>
          </MenuItem>
        )}

        {onDelete && (
          <MenuItem onClick={handleDeleteClick}>
            <ListItemIcon>
              <Trash2 size={18} color="red" />
            </ListItemIcon>
            <ListItemText sx={{ color: "error.main" }}>Supprimer</ListItemText>
          </MenuItem>
        )}
      </Menu>

      {/* Modal de détails */}
      <AdhesionRequestDetailModal
        open={detailModalOpen}
        onClose={() => setDetailModalOpen(false)}
        data={selectedRequest}
        onMarkAsRead={onMarkAsRead}
        onDelete={onDelete}
      />

      {/* Modal de confirmation pour la suppression */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Confirmation de suppression"
        message="Êtes-vous sûr de vouloir supprimer cette demande d'adhésion ?"
        confirmButtonText="Supprimer"
        loading={isLoading}
      />
    </>
  );
};

export default AdhesionRequestList;
