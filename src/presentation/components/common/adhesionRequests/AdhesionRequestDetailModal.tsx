import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Grid,
  Chip,
  Box,
  IconButton,
} from "@mui/material";
import { X } from "lucide-react";
import { demande_adhesion } from "@/domain/models";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { PRIMARY } from "@/shared/constants/Color";
import ConfirmationModal from "../Modal/ConfirmationModal";

interface AdhesionRequestDetailModalProps {
  open: boolean;
  onClose: () => void;
  data: demande_adhesion | null;
  onMarkAsRead?: (id: number) => void;
  onDelete?: (id: number) => void;
}

export const AdhesionRequestDetailModal: React.FC<
  AdhesionRequestDetailModalProps
> = ({ open, onClose, data, onMarkAsRead, onDelete }) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  if (!data) return null;

  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    try {
      return format(new Date(dateString), "dd MMMM yyyy", { locale: fr });
    } catch (error) {
      return dateString;
    }
  };

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (onDelete) {
      setIsLoading(true);
      try {
        await onDelete(data.id);
        onClose();
      } catch (error) {
        console.error("Erreur lors de la suppression:", error);
      } finally {
        setIsLoading(false);
        setIsDeleteModalOpen(false);
      }
    }
  };

  const handleMarkAsRead = async () => {
    if (onMarkAsRead) {
      setIsLoading(true);
      try {
        await onMarkAsRead(data.id);

        onClose();
      } catch (error) {
        console.error("Erreur lors du marquage comme lu:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: "12px",
              padding: "8px",
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h5" component="div" fontWeight={600}>
            Détails de la demande d'adhésion
          </Typography>
          <IconButton onClick={onClose} size="small">
            <X size={20} />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers>
          <Box sx={{ mb: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Statut
                </Typography>
                <Chip
                  label={data.status === "lu" ? "Lu" : "Non lu"}
                  color={data.status === "lu" ? "success" : "warning"}
                  size="small"
                  sx={{ mt: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Date de création
                </Typography>
                <Typography variant="body1" sx={{ mt: 1 }}>
                  {formatDate(data.date_creation)}
                </Typography>
              </Grid>
            </Grid>
          </Box>

          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Informations personnelles
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Nom complet
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.prenom} {data.nom}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Email
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.email}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Téléphone
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.telephone}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Type d'établissement
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.type_etablissement}
              </Typography>
            </Grid>
          </Grid>

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Adresse du cabinet
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Adresse
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.adresse}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                District
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.district}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Commune
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {data.commune}
              </Typography>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ padding: 2, justifyContent: "space-between" }}>
          <Box>
            {data.status !== "lu" && onMarkAsRead && (
              <button
                className="btn-outline bg-white text-primary p-2 border border-primary rounded-md pointer"
                onClick={handleMarkAsRead}
                type="button"
              >
                Marquer comme lu
              </button>
            )}
          </Box>
          <Box>
            {onDelete && (
              <Button
                onClick={handleDeleteClick}
                variant="outlined"
                color="error"
                sx={{ mr: 1 }}
              >
                Supprimer
              </Button>
            )}
          </Box>
        </DialogActions>
      </Dialog>

      {/* Modal de confirmation pour la suppression */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Confirmation de suppression"
        message="Êtes-vous sûr de vouloir supprimer cette demande d'adhésion ?"
        confirmButtonText="Supprimer"
        loading={isLoading}
      />
    </>
  );
};

export default AdhesionRequestDetailModal;
