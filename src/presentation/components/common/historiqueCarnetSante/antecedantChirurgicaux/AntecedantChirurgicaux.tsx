import { Box, Button, TextField, Typography } from "@mui/material";
import { useState } from "react";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";
import { useAntecedantChirurgicaux } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAntecedantChirurgicaux";

interface AntecedantChirurgicauxProps {
  item: string;
}

const AntecedantChirurgicaux = ({ item }: AntecedantChirurgicauxProps) => {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState<boolean>(false);
  const handleDateChange = (value: Date | null) => {
    if (!value) return;
    handleDateDeChirurgieChange(item, value);
  };
  const {
    antecedantChirurgicauxState,
    handleDateDeChirurgieChange,
    handleDescriptionsChange,
    handleRemarksChange,
  } = useAntecedantChirurgicaux();
  return (
    <>
      <Typography variant="subtitle1" sx={{ mt: 3, mb: 1, fontWeight: "bold" }}>
        {item}
      </Typography>
      <Typography variant="subtitle2" color="textSecondary">
        Date de chirurgie
      </Typography>
      <Box className="flex items-center gap-2 my-2 h-10">
        <Button
          variant="outlined"
          onClick={() => setIsDatePickerOpen(true)}
          sx={{ textTransform: "none" }}
        >
          {antecedantChirurgicauxState.dateDeChirurgie[item]
            ? new Date(
                antecedantChirurgicauxState.dateDeChirurgie[item]
              ).toLocaleDateString()
            : "Sélectionner une date"}
        </Button>
      </Box>
      <Typography variant="subtitle2" color="textSecondary">
        Description
      </Typography>
      <TextField
        placeholder="Description"
        multiline
        rows={2}
        fullWidth
        sx={{ my: 2 }}
        value={antecedantChirurgicauxState.descriptions[item] || ""}
        onChange={(e) => handleDescriptionsChange(item, e.target.value)}
      />
      <Typography variant="subtitle2" color="textSecondary">
        Remarques
      </Typography>
      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ my: 2 }}
        value={antecedantChirurgicauxState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
      <DatePickerModal
        open={isDatePickerOpen}
        onClose={() => setIsDatePickerOpen(false)}
        onDateSelect={handleDateChange}
      />
    </>
  );
};

export default AntecedantChirurgicaux;
