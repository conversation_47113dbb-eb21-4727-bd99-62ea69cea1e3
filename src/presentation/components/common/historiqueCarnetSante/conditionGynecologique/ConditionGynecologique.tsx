import {
  Box,
  Button,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import { useState } from "react";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";
import { useConditionGynecologique } from "@/presentation/hooks/carnetDeSante/sousCarnet/useConditionGynecologique";

interface ConditionGynecologiqueProps {
  item: string;
}

const ConditionGynecologique = ({ item }: ConditionGynecologiqueProps) => {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState<boolean>(false);
  const handleDateChange = (value: Date | null) => {
    if (!value) return;
    handleDateConditionGynecologiqueChange(item, value);
  };
  const {
    conditionGynecologiqueState,
    handleDateChange: handleDateConditionGynecologiqueChange,
    handleRemarksChange,
  } = useConditionGynecologique();
  return (
    <>
      <Typography variant="subtitle1" sx={{ mt: 3, mb: 1, fontWeight: "bold" }}>
        {item}
      </Typography>
      <Typography variant="subtitle2" color="textSecondary">
        Date de grossesse
      </Typography>
      <Box className="flex items-center gap-2 my-2 h-10">
        <Button
          variant="outlined"
          onClick={() => setIsDatePickerOpen(true)}
          sx={{ textTransform: "none" }}
        >
          {conditionGynecologiqueState.date[item]
            ? new Date(
                conditionGynecologiqueState.date[item]
              ).toLocaleDateString()
            : "Sélectionner une date"}
        </Button>
      </Box>
      <Typography variant="subtitle2" color="textSecondary">
        Remarques
      </Typography>
      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ my: 2 }}
        value={conditionGynecologiqueState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
      <DatePickerModal
        open={isDatePickerOpen}
        onClose={() => setIsDatePickerOpen(false)}
        onDateSelect={handleDateChange}
      />
    </>
  );
};

export default ConditionGynecologique;
