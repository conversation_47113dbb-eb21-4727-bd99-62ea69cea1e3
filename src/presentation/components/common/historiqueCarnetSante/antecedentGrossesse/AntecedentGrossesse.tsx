import {
  Box,
  Button,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import { useState } from "react";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";
import { useAntecedentGrossesse } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAntecedentGrossesse";

interface AntecedentGrossesseProps {
  item: string;
}

const AntecedentGrossesse = ({ item }: AntecedentGrossesseProps) => {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState<boolean>(false);
  const handleDateChange = (value: Date | null) => {
    if (!value) return;
    handleDateGrossesseChange(item, value);
  };
  const {
    antecedentGrossesseState,
    handlePariteChange,
    handleEstEnceinteChange,
    handleNombreEnfantsChange,
    handleDateChange: handleDateGrossesseChange,
    handleRemarksChange,
  } = useAntecedentGrossesse();
  return (
    <>
      <Typography variant="subtitle1" sx={{ mt: 3, mb: 1, fontWeight: "bold" }}>
        {item}
      </Typography>
      <Box sx={{ display: "flex", gap: 2, alignItems: "center", my: 1 }}>
        <Typography variant="body2" color="textSecondary">
          Êtes-vous enceinte ?
        </Typography>
        <FormControl>
          <RadioGroup
            onChange={(e) => handleEstEnceinteChange(item, e.target.value)}
            row
          >
            <FormControlLabel
              value="oui"
              checked={antecedentGrossesseState.estEnceinte[item] === true}
              control={<Radio size="small" />}
              label={<Typography variant="body2">Oui</Typography>}
            />
            <FormControlLabel
              value="non"
              checked={antecedentGrossesseState.estEnceinte[item] === false}
              control={<Radio size="small" />}
              label={<Typography variant="body2">Non</Typography>}
            />
          </RadioGroup>
        </FormControl>
      </Box>
      <Typography variant="subtitle2" color="textSecondary">
        Date de grossesse
      </Typography>
      <Box className="flex items-center gap-2 my-2 h-10">
        <Button
          variant="outlined"
          onClick={() => setIsDatePickerOpen(true)}
          sx={{ textTransform: "none" }}
          disabled={antecedentGrossesseState.estEnceinte[item] != true}
        >
          {antecedentGrossesseState.date[item]
            ? new Date(antecedentGrossesseState.date[item]).toLocaleDateString()
            : "Sélectionner une date"}
        </Button>
      </Box>
      <Typography variant="subtitle2" color="textSecondary">
        Parite
      </Typography>
      <TextField
        placeholder="Parite"
        multiline
        rows={2}
        fullWidth
        sx={{ my: 2 }}
        value={antecedentGrossesseState.parite[item] || ""}
        onChange={(e) => handlePariteChange(item, e.target.value)}
      />
      <Typography variant="subtitle2" color="textSecondary">
        Nombres d'enfants
      </Typography>
      <TextField
        placeholder="Nombres d'enfants"
        type="number"
        fullWidth
        sx={{ my: 2 }}
        value={antecedentGrossesseState.nombreEnfants[item] || ""}
        onChange={(e) =>
          handleNombreEnfantsChange(item, Number(e.target.value))
        }
      />
      <Typography variant="subtitle2" color="textSecondary">
        Remarques
      </Typography>
      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ my: 2 }}
        value={antecedentGrossesseState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
      <DatePickerModal
        open={isDatePickerOpen}
        onClose={() => setIsDatePickerOpen(false)}
        onDateSelect={handleDateChange}
      />
    </>
  );
};

export default AntecedentGrossesse;
