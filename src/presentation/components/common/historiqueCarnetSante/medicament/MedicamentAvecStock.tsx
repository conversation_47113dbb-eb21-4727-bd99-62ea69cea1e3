import React, { useMemo } from "react";
import { useMedicament } from "@/presentation/hooks/carnetDeSante/sousCarnet/useMedicament";
import { useCarnetDeSanteStockManagement } from "@/presentation/components/common/Modal/hooks/useCarnetDeSanteStockManagement";
import { useMedicamentQuantityCalculator } from "@/presentation/components/common/Modal/hooks/useMedicamentQuantityCalculator";
import { PRIMARY } from "@/shared/constants/Color";
import {
  Autocomplete,
  Box,
  Button,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography,
  Alert,
  Chip,
  Card,
  CardContent,
} from "@mui/material";
import { Inventory as Package } from "@mui/icons-material";
import { Calculator } from "lucide-react";

interface MedicamentAvecStockProps {
  item: string;
  useStockMode?: boolean;
}

const typeConsommationOptions = [
  "Oral",
  "Parenteral",
  "Intramusculaire",
  "Sous-cutané",
  "Topique",
];

const frequenceOptions = [
  "Une fois par jour",
  "Deux fois par jour",
  "Trois fois par jour",
  "Quatre fois par jour",
  "Toutes les 4 heures",
  "Toutes les 6 heures",
  "Toutes les 8 heures",
  "Toutes les 12 heures",
  "Au besoin",
];

const calendrierOptions = [
  "Matin",
  "Midi",
  "Soir",
  "Au coucher",
  "Avant les repas",
  "Pendant les repas",
  "Après les repas",
];

const MedicamentAvecStock = ({
  item,
  useStockMode = false,
}: MedicamentAvecStockProps) => {
  const {
    medicamentState,
    handleForceChange,
    handlePosologieChange,
    handleDureeChange,
    handleTypeConsommationChange,
    handleFrequenceChange,
    handleQuantiteChange,
    handleCalendrierChange,
    handleRemarksChange,
  } = useMedicament();

  // Gestion du stock si activée
  const { checkMedicamentAvailability } = useCarnetDeSanteStockManagement();

  // Calcul automatique de la quantité
  const {
    quantiteTotale,
    formatQuantite,
    calculExplanation,
    isValid: isCalculValid,
  } = useMedicamentQuantityCalculator(
    medicamentState.frequence[item] || null,
    medicamentState.quantite[item] || null,
    medicamentState.duree[item] || null
  );

  // Vérification de disponibilité en stock
  const stockAvailability = useMemo(() => {
    if (!useStockMode || !item || quantiteTotale === 0) {
      return null;
    }

    return checkMedicamentAvailability(item, quantiteTotale);
  }, [useStockMode, item, quantiteTotale, checkMedicamentAvailability]);

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="subtitle1" sx={{ mb: 3, fontWeight: "bold" }}>
        {item}
      </Typography>

      {/* Indicateur de mode stock */}
      {useStockMode && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Package sx={{ width: 16, height: 16 }} />
            <Typography variant="body2">
              Mode stock activé - Les quantités seront décomptées
              automatiquement
            </Typography>
          </Box>
        </Alert>
      )}

      <Box sx={{ mb: 3 }}>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          FORCE
        </Typography>
        <TextField
          placeholder="Entrez la force"
          fullWidth
          size="small"
          value={medicamentState.force[item] || ""}
          onChange={(e) => handleForceChange(item, e.target.value)}
        />
      </Box>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-3">
        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            TYPE
          </Typography>
          <Autocomplete
            size="small"
            options={typeConsommationOptions}
            renderInput={(params) => (
              <TextField {...params} placeholder="Sélectionnez le type" />
            )}
            value={medicamentState.typeConsommation[item] || ""}
            onChange={(event, newValue) =>
              handleTypeConsommationChange(item, newValue || "")
            }
          />
        </Box>

        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            FRÉQUENCE PAR JOUR
          </Typography>
          <Autocomplete
            size="small"
            options={frequenceOptions}
            renderInput={(params) => (
              <TextField {...params} placeholder="Sélectionnez la fréquence" />
            )}
            value={medicamentState.frequence[item] || ""}
            onChange={(event, newValue) =>
              handleFrequenceChange(item, newValue || "")
            }
          />
        </Box>

        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            QUANTITÉ PAR DOSAGE
          </Typography>
          <TextField
            placeholder="Ex: 2 comprimés"
            fullWidth
            size="small"
            value={medicamentState.quantite[item] || ""}
            onChange={(e) => handleQuantiteChange(item, e.target.value)}
          />
        </Box>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-3">
        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            NOMBRE DE JOURS
          </Typography>
          <TextField
            placeholder="Durée du traitement"
            fullWidth
            size="small"
            type="number"
            value={medicamentState.duree[item] || ""}
            onChange={(e) => handleDureeChange(item, e.target.value)}
          />
        </Box>

        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            POSOLOGIE
          </Typography>
          <TextField
            placeholder="Instructions de prise"
            fullWidth
            size="small"
            value={medicamentState.posologie[item] || ""}
            onChange={(e) => handlePosologieChange(item, e.target.value)}
          />
        </Box>
      </div>

      {/* Calcul automatique de la quantité */}

      {isCalculValid && (
        <Card variant="outlined" sx={{ mb: 3, bgcolor: "background.default" }}>
          <CardContent sx={{ py: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
              <Calculator className="w-4 h-4" />
              <Typography variant="subtitle2" sx={{ fontWeight: "bold" }}>
                Calcul automatique de quantité
              </Typography>
            </Box>
            <Typography variant="body2" color="textSecondary">
              {calculExplanation}
            </Typography>
            <Box sx={{ mt: 1 }}>
              <Chip
                label={`Quantité totale : ${formatQuantite} unité(s)`}
                color="primary"
                variant="outlined"
                size="small"
              />
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Vérification de stock */}
      {useStockMode && stockAvailability && (
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent sx={{ py: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
              <Package sx={{ width: 16, height: 16, color: "primary.main" }} />
              <Typography variant="subtitle2" sx={{ fontWeight: "bold" }}>
                Vérification de stock
              </Typography>
            </Box>

            {stockAvailability.available ? (
              <Alert severity="success" sx={{ mt: 1 }}>
                <Typography variant="body2">
                  ✅ {stockAvailability.message}
                </Typography>
              </Alert>
            ) : (
              <Alert severity="warning" sx={{ mt: 1 }}>
                <Typography variant="body2">
                  ⚠️ {stockAvailability.message}
                </Typography>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      <Box sx={{ mb: 3 }}>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          CALENDRIER
        </Typography>
        <Autocomplete
          size="small"
          options={calendrierOptions}
          renderInput={(params) => (
            <TextField {...params} placeholder="Moment de prise" />
          )}
          value={medicamentState.calendrier[item] || ""}
          onChange={(event, newValue) =>
            handleCalendrierChange(item, newValue || "")
          }
        />
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          REMARQUES
        </Typography>
        <TextField
          placeholder="Remarques supplémentaires"
          fullWidth
          multiline
          rows={3}
          size="small"
          value={medicamentState.remarks[item] || ""}
          onChange={(e) => handleRemarksChange(item, e.target.value)}
        />
      </Box>
    </Box>
  );
};

export default MedicamentAvecStock;
