import { useMedicament } from "@/presentation/hooks/carnetDeSante/sousCarnet/useMedicament";
import { PRIMARY } from "@/shared/constants/Color";
import {
  Autocomplete,
  Box,
  Button,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";

interface MedicamentProps {
  item: string;
}

const typeConsommationOptions = [
  "Oral",
  "Parenteral",
  "Intramusculaire",
  "Sous-cutané",
  "Topique",
];

const frequenceOptions = [
  "Une fois par jour",
  "Deux fois par jour",
  "Trois fois par jour",
  "Quatre fois par jour",
  "Toutes les 4 heures",
  "Toutes les 6 heures",
  "Toutes les 8 heures",
  "Toutes les 12 heures",
  "Au besoin",
];

const calendrierOptions = [
  "Matin",
  "Midi",
  "Soir",
  "Au coucher",
  "Avant les repas",
  "Pendant les repas",
  "Après les repas",
];

const Medicaments = ({ item }: MedicamentProps) => {
  const {
    medicamentState,
    handleForceChange,
    handlePosologieChange,
    handleDureeChange,
    handleTypeConsommationChange,
    handleFrequenceChange,
    handleQuantiteChange,
    handleCalendrierChange,
    handleRemarksChange,
  } = useMedicament();
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="subtitle1" sx={{ mb: 3, fontWeight: "bold" }}>
        {item}
      </Typography>

      <Box sx={{ mb: 3 }}>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          FORCE
        </Typography>
        <TextField
          placeholder="Entrez la force"
          fullWidth
          size="small"
          value={medicamentState.force[item] || ""}
          onChange={(e) => handleForceChange(item, e.target.value)}
        />
      </Box>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-3">
        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            TYPE
          </Typography>
          <Autocomplete
            size="small"
            options={typeConsommationOptions}
            renderInput={(params) => (
              <TextField {...params} placeholder="Sélectionnez le type" />
            )}
            value={medicamentState.typeConsommation[item] || ""}
            onChange={(event, newValue) =>
              handleTypeConsommationChange(item, newValue || "")
            }
          />
        </Box>

        <Box sx={{ gridColumn: "span 2" }}>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            DURÉE
          </Typography>
          <Box sx={{ display: "flex", gap: 2, alignItems: "flex-start" }}>
            <TextField
              type="number"
              size="small"
              placeholder="Durée"
              sx={{ width: "150px" }}
              value={medicamentState.duree[item] || ""}
              onChange={(e) => handleDureeChange(item, e.target.value)}
            />
            <FormControl>
              <RadioGroup row>
                <FormControlLabel
                  value="jours"
                  control={<Radio size="small" />}
                  label={<Typography variant="body2">Jours</Typography>}
                />
                <FormControlLabel
                  value="mois"
                  control={<Radio size="small" />}
                  label={<Typography variant="body2">Mois</Typography>}
                />
              </RadioGroup>
            </FormControl>
          </Box>
        </Box>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-3">
        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            QUANTITÉ PAR DOSAGE
          </Typography>
          <TextField
            placeholder="Entrez la quantité"
            size="small"
            fullWidth
            value={medicamentState.quantite[item] || ""}
            onChange={(e) => handleQuantiteChange(item, e.target.value)}
          />
        </Box>

        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            FRÉQUENCE DE LA DOSE
          </Typography>
          <Autocomplete
            size="small"
            options={frequenceOptions}
            renderInput={(params) => (
              <TextField {...params} placeholder="Sélectionnez la fréquence" />
            )}
            value={medicamentState.frequence[item] || ""}
            onChange={(event, newValue) =>
              handleFrequenceChange(item, newValue || "")
            }
          />
        </Box>
      </div>

      <Box sx={{ mb: 3 }}>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          POSOLOGIE
        </Typography>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            variant={
              medicamentState.posologie[item] === "Comme requis"
                ? "contained"
                : "outlined"
            }
            sx={{
              flex: 1,
              textTransform: "none",
              backgroundColor:
                medicamentState.posologie[item] === "Comme requis"
                  ? PRIMARY
                  : "",
              borderRadius: "4px 0 0 4px",
            }}
            onClick={() => handlePosologieChange(item, "Comme requis")}
          >
            Comme requis
          </Button>
          <Button
            variant={
              medicamentState.posologie[item] === "Dose planifiée"
                ? "contained"
                : "outlined"
            }
            sx={{
              flex: 1,
              textTransform: "none",
              backgroundColor:
                medicamentState.posologie[item] === "Dose planifiée"
                  ? PRIMARY
                  : "",
              borderRadius: "0 4px 4px 0",
            }}
            onClick={() => handlePosologieChange(item, "Dose planifiée")}
          >
            Dose planifiée
          </Button>
        </Box>
      </Box>

      <Box>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          CALENDRIER DE LA DOSE
        </Typography>
        <Autocomplete
          size="small"
          options={calendrierOptions}
          disabled={medicamentState.posologie[item] === "Comme requis"}
          renderInput={(params) => (
            <TextField {...params} placeholder="Sélectionnez le calendrier" />
          )}
          value={medicamentState.calendrier[item] || ""}
          onChange={(event, newValue) =>
            handleCalendrierChange(item, newValue || "")
          }
        />
      </Box>

      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ mt: 2 }}
        value={medicamentState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
    </Box>
  );
};

export default Medicaments;
