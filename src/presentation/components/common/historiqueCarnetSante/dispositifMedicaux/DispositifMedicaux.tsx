import { <PERSON>, Button, TextField, Typography } from "@mui/material";
import { useState } from "react";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";
import { useDispositifMedicaux } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDispositifMedicaux";

interface DispositifMedicauxProps {
  item: string;
}

const DispositifMedicaux = ({ item }: DispositifMedicauxProps) => {
  const [datePickerType, setDatePickerType] = useState<
    "acquisition" | "prochaine" | null
  >(null);
  const onDateAcquisitionChange = (value: Date | null) => {
    if (!value) return;
    handleDateAcquisitionChange(item, value);
  };
  const onProchaineDateChange = (value: Date | null) => {
    if (!value) return;
    handleProchaineDateChange(item, value);
  };
  const {
    dispositifMedicauxState,
    handleMarqueChange,
    handleModeleChange,
    handleReferenceChange,
    handleDateAcquisition<PERSON>hange,
    handleProchaineDateChange,
    handleRemarksChange,
  } = useDispositifMedicaux();
  return (
    <>
      <Typography variant="subtitle1" sx={{ mt: 3, mb: 1, fontWeight: "bold" }}>
        {item}
      </Typography>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-3">
        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            MARQUE
          </Typography>
          <TextField
            value={dispositifMedicauxState.marque[item] || ""}
            onChange={(e) => handleMarqueChange(item, e.target.value)}
            type="text"
            size="small"
            placeholder="Veuiller entrer la marque"
          />
        </Box>
        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            MODELE
          </Typography>
          <TextField
            value={dispositifMedicauxState.modele[item] || ""}
            onChange={(e) => handleModeleChange(item, e.target.value)}
            type="text"
            size="small"
            placeholder="Veuiller entrer la modele"
          />
        </Box>
        <Box>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            REFERENCE
          </Typography>
          <TextField
            value={dispositifMedicauxState.reference[item] || ""}
            onChange={(e) => handleReferenceChange(item, e.target.value)}
            type="text"
            size="small"
            placeholder="Veuiller entrer la reference de l'appareil"
          />
        </Box>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-3">
        <div>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            Date d'acquisition
          </Typography>
          <Box className="flex items-center gap-2 my-2 h-10">
            <Button
              variant="outlined"
              onClick={() => setDatePickerType("acquisition")}
              sx={{ textTransform: "none" }}
            >
              {dispositifMedicauxState.dateAcquisition[item]
                ? new Date(
                    dispositifMedicauxState.dateAcquisition[item]
                  ).toLocaleDateString()
                : "Sélectionner une date"}
            </Button>
          </Box>
        </div>
        <div>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            Prochaine mise à jour
          </Typography>
          <Box className="flex items-center gap-2 my-2 h-10">
            <Button
              variant="outlined"
              onClick={() => setDatePickerType("prochaine")}
              sx={{ textTransform: "none" }}
            >
              {dispositifMedicauxState.prochaineDate[item]
                ? new Date(
                    dispositifMedicauxState.prochaineDate[item]
                  ).toLocaleDateString()
                : "Sélectionner une date"}
            </Button>
          </Box>
        </div>
      </div>
      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ mt: 2 }}
        value={dispositifMedicauxState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
      {datePickerType && (
        <DatePickerModal
          open={!!datePickerType}
          onClose={() => setDatePickerType(null)}
          onDateSelect={
            datePickerType === "acquisition"
              ? onDateAcquisitionChange
              : onProchaineDateChange
          }
        />
      )}
    </>
  );
};

export default DispositifMedicaux;
