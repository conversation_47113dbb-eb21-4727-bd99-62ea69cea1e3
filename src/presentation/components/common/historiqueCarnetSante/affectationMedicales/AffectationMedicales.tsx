import { Box, Button, TextField, Typography } from "@mui/material";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";
import { useState } from "react";
import { useAffectationMedicale } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAffectationMedicale";

interface AffectationMedicalesProps {
  item: string;
}

const AffectationMedicales = ({ item }: AffectationMedicalesProps) => {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState<boolean>(false);
  const handleDateChange = (value: Date | null) => {
    if (!value) return;
    handleDateAcquisitionChange(item, value);
  };
  const {
    affectationMedicaleState,
    handleDateAcquisitionChange,
    handleRemarksChange,
  } = useAffectationMedicale();
  return (
    <>
      <Typography variant="subtitle1" sx={{ mt: 3, mb: 1, fontWeight: "bold" }}>
        {item}
      </Typography>
      <Typography
        variant="caption"
        color="textSecondary"
        sx={{ mb: 1, display: "block" }}
      >
        Date d'acquisition
      </Typography>
      <Box className="flex items-center gap-2 my-2 h-10">
        <Button
          variant="outlined"
          onClick={() => setIsDatePickerOpen(true)}
          sx={{ textTransform: "none" }}
        >
          {affectationMedicaleState.dateAcquisition[item]
            ? new Date(
                affectationMedicaleState.dateAcquisition[item]
              ).toLocaleDateString()
            : "Sélectionner une date"}
        </Button>
      </Box>
      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ mt: 2 }}
        value={affectationMedicaleState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
      <DatePickerModal
        open={isDatePickerOpen}
        onClose={() => setIsDatePickerOpen(false)}
        onDateSelect={handleDateChange}
      />
    </>
  );
};

export default AffectationMedicales;
