import { useEffect, useState } from "react";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { Typography } from "@mui/material";
import { useFacturation } from "@/presentation/hooks/facturation";
import { active_tab_enum } from "@/domain/models/enums";
import { useParams } from "react-router-dom";

const FacturationDataGrid = () => {
  const { listeFacturationPatient, getFacturationsByPatientId } =
    useFacturation();

  const { id: patientId } = useParams();

  useEffect(() => {
    if (patientId) {
      getFacturationsByPatientId(Number(patientId));
    }
  }, [patientId]);

  return (
    <div>
      <div className="mb-2">
        <Typography variant="subtitle1" color="textSecondary">
          Solde total:{" "}
          {listeFacturationPatient.reduce(
            (acc, fact) => acc + (fact.montant - fact.total_paye),
            0
          )}{" "}
          MGA
        </Typography>
      </div>
      <div>
        <ListDataGrid
          data={listeFacturationPatient}
          type={active_tab_enum.facturation}
        />
      </div>
    </div>
  );
};

export default FacturationDataGrid;
