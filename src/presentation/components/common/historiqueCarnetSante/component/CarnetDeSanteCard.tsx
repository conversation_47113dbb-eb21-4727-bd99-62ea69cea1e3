import { HeaderBox } from "@/presentation/components/common/historiqueCarnetSante/component/HeaderBox";
import { StyledCard } from "@/presentation/components/common/historiqueCarnetSante/component/StyledCard";
import {
  Box,
  CardContent,
  Divider,
  IconButton,
  Tooltip,
  Typography,
  Avatar,
} from "@mui/material";
import { Eye, LockKeyhole, Plus, Trash2, Info } from "lucide-react";
import { useState } from "react";
import { EditCarnetDeSanteModal } from "@/presentation/components/common/Modal/EditCarnetDeSanteModal";
import { PRIMARY } from "@/shared/constants/Color";
import { DeleteCarnetDeSanteModal } from "@/presentation/components/common/Modal/DeleteCarnetDeSanteModal";
import { ComponentProps } from "react";
import { twMerge } from "tailwind-merge";
import { useDiagnostic } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDiagnostic";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";
import { useAppSelector } from "@/presentation/hooks/redux";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { getCarnetVisualConfig } from "@/shared/constants/CarnetDeSanteIcons";
import { parseTitleWithTooltip } from "@/shared/utils/titleParser";
import TooltipComponent from "@/shared/utils/tooltipe";

type CarnetDeSanteProps = ComponentProps<"div"> & {
  className?: string;
  data: {
    title: string;
    content: ContentItem[];
  };
  isEdit?: boolean;
  setIsCarnetDeSanteModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setIsHistoriqueCarnetDeSanteModalOpen?: React.Dispatch<
    React.SetStateAction<boolean>
  >;
};

interface ContentItem {
  id: number;
  nom: string;
  remarks?: string;
}

const CarnetDeSanteCard = ({
  className,
  data,
  isEdit,
  setIsCarnetDeSanteModalOpen,
  setIsHistoriqueCarnetDeSanteModalOpen,
  ...props
}: CarnetDeSanteProps) => {
  const [editItem, setEditItem] = useState<ContentItem | null>(null);
  const [deleteItem, setDeleteItem] = useState<ContentItem | null>(null);
  const { resetState } = useDiagnostic();
  const { resetSelectedFileState } = useCarnetDeSanteState();
  const role = useAppSelector((state) => state.authentification.user?.role);
  
  // Récupération de la configuration visuelle pour ce type de carnet
  const visualConfig = getCarnetVisualConfig(data.title);
  const IconComponent = visualConfig.icon;
  
  // Parse du titre pour séparer le titre principal du contenu du tooltip
  const parsedTitle = parseTitleWithTooltip(data.title);

  const handleEdit = (item: ContentItem) => {
    setEditItem(item);
  };

  const handleDelete = (item: ContentItem) => {
    setDeleteItem(item);
  };

  const handleCloseEdit = () => {
    setEditItem(null);
    resetState();
    resetSelectedFileState();
  };

  const handleCloseDelete = () => {
    setDeleteItem(null);
    resetState();
    resetSelectedFileState();
  };

  return (
    <div className={twMerge(className)} {...props}>
      <StyledCard 
        className={`
          bg-white dark:bg-gray-800
          border border-gray-200 dark:border-gray-700
          rounded-xl shadow-sm
          transition-all duration-300 ease-out
          hover:shadow-lg hover:-translate-y-1
          overflow-hidden
        `}
      >
        <HeaderBox className="
          bg-gray-50 dark:bg-gray-700/50
          border-b border-gray-200 dark:border-gray-600
          px-4 py-3
        ">
          <div className="flex items-center gap-3">
            <div className={`
              ${visualConfig.avatarClass}
              w-10 h-10 rounded-lg flex items-center justify-center
              shadow-sm
            `}>
              <IconComponent className="w-5 h-5 text-white" />
            </div>
            <div className="flex items-center gap-2 flex-1">
              <h3 className="font-bold text-black dark:text-white text-base">
                {parsedTitle.mainTitle}
              </h3>
              {parsedTitle.tooltipContent && (
                <TooltipComponent
                  content={parsedTitle.tooltipContent}
                  position="top"
                  size="medium"
                  maxWidth="max-w-sm"
                >
                  <Info className="w-4 h-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 cursor-help transition-colors duration-200" />
                </TooltipComponent>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Tooltip title="Voir l'historique" enterDelay={100}>
              <button
                onClick={() => setIsHistoriqueCarnetDeSanteModalOpen(true)}
                className={`
                  ${visualConfig.colorClass}
                  hover:bg-gray-100 dark:hover:bg-gray-600
                  p-2 rounded-lg transition-colors duration-200
                `}
              >
                <Eye className="h-4 w-4" />
              </button>
            </Tooltip>
            {(role === utilisateurs_role_enum.PROFESSIONNEL ||
              role === utilisateurs_role_enum.DASH) && (
              <Tooltip title="Ajouter" enterDelay={100}>
                <button
                  onClick={() => setIsCarnetDeSanteModalOpen(true)}
                  className={`
                    ${visualConfig.colorClass}
                    hover:bg-gray-100 dark:hover:bg-gray-600
                    p-2 rounded-lg transition-colors duration-200
                  `}
                >
                  <Plus className="h-4 w-4" />
                </button>
              </Tooltip>
            )}
            {isEdit && (
              <Tooltip title="Modifier" enterDelay={100}>
                <button
                  onClick={() => setIsCarnetDeSanteModalOpen(true)}
                  className={`
                    ${visualConfig.colorClass}
                    hover:bg-gray-100 dark:hover:bg-gray-600
                    p-2 rounded-lg transition-colors duration-200
                  `}
                >
                  <LockKeyhole className="h-4 w-4" />
                </button>
              </Tooltip>
            )}
          </div>
        </HeaderBox>
        <div className="p-4 min-h-[240px] max-h-[240px] overflow-hidden relative">
          {data.content.length === 0 ? (
            <div className={`
              flex flex-col items-center justify-center h-full
              ${visualConfig.colorClass} opacity-60
            `}>
              <IconComponent size={32} className="mb-3" />
              <p className="text-sm italic text-center">
                Aucune donnée disponible
              </p>
            </div>
          ) : (
            <div className={`
              max-h-44 space-y-2
              ${data.content.length > 2 ? 'overflow-y-auto pr-2' : 'overflow-visible'}
              scrollbar-thin scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-500
              scrollbar-track-gray-100 dark:scrollbar-track-gray-700
              scrollbar-thumb-rounded-full scrollbar-track-rounded-full
            `}>
              {data.content.map((item) => (
                <div
                  key={item.id}
                  className={`
                    flex items-center justify-between px-3 py-2.5 rounded-lg
                    bg-gray-50 dark:bg-gray-700/50
                    border border-gray-200 dark:border-gray-600
                    hover:bg-gray-100 dark:hover:bg-gray-600/50
                    transition-colors duration-200
                  `}
                >
                  <p
                    onClick={() => handleEdit(item)}
                    className={`
                      cursor-pointer flex-1 text-sm font-medium
                      text-gray-800 dark:text-gray-200
                      hover:${visualConfig.colorClass.split(' ')[0]}
                      transition-colors duration-200
                    `}
                  >
                    {item.nom}
                  </p>
                  <div>
                    {(role === utilisateurs_role_enum.PROFESSIONNEL ||
                      role === utilisateurs_role_enum.DASH) && (
                      <Tooltip title="Supprimer" enterDelay={100}>
                        <button
                          onClick={() => handleDelete(item)}
                          className="
                            text-red-500 dark:text-red-400
                            hover:bg-red-50 dark:hover:bg-red-900/30
                            p-1.5 rounded transition-colors duration-200
                          "
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </Tooltip>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </StyledCard>

      {editItem && (
        <EditCarnetDeSanteModal
          type={data.title}
          isOpen={!!editItem}
          item={editItem}
          handleClose={handleCloseEdit}
        />
      )}
      {deleteItem && (
        <DeleteCarnetDeSanteModal
          type={data.title}
          isOpen={!!deleteItem}
          item={deleteItem}
          handleClose={handleCloseDelete}
        />
      )}
    </div>
  );
};

export default CarnetDeSanteCard;
