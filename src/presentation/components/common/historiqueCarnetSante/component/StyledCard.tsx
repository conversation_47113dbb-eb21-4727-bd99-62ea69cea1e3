import { PRIMARY } from "@/shared/constants/Color";
import { Card, styled } from "@mui/material";

export const StyledCard = styled(Card)<{ isConsultation?: boolean }>(
  ({ theme, isConsultation }) => ({
    border: "1px solid",
    borderColor: theme.palette.divider,
    boxShadow: "none",
    borderRadius: theme.spacing(0.5),
    marginTop: 4,
    ...(isConsultation && {
      ":hover": {
        borderColor: PRIMARY,
      },
    }),
  })
);
