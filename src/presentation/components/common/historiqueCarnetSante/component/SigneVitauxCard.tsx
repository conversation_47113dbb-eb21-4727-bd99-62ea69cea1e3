import { signe_vitaux } from "@/domain/models";
import { HeaderBox } from "@/presentation/components/common/historiqueCarnetSante/component/HeaderBox";
import { StyledCard } from "@/presentation/components/common/historiqueCarnetSante/component/StyledCard";
import { Typography } from "@mui/material";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { ComponentProps } from "react";
import { twMerge } from "tailwind-merge";

type SigneVitauxCardProps = ComponentProps<"div"> & {
  className?: string;
  signeVitaux: signe_vitaux;
  onClick: () => void;
};

const SigneVitauxCard = ({
  className,
  signeVitaux,
  onClick,
  ...props
}: SigneVitauxCardProps) => {
  return (
    <div className={twMerge(className)}>
      <StyledCard isConsultation onClick={onClick}>
        <HeaderBox>
          <Typography variant="h6">
            {format(new Date(signeVitaux.date_visite), "d MMMM", {
              locale: fr,
            })}
          </Typography>
        </HeaderBox>
      </StyledCard>
    </div>
  );
};

export default SigneVitauxCard;
