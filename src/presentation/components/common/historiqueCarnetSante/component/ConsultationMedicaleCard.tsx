import { consultation_medical } from "@/domain/models";
import { HeaderBox } from "@/presentation/components/common/historiqueCarnetSante/component/HeaderBox";
import { StyledCard } from "@/presentation/components/common/historiqueCarnetSante/component/StyledCard";
import { CardContent, Divider, Typography } from "@mui/material";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { ComponentProps } from "react";
import { twMerge } from "tailwind-merge";

type ConsultationMedicaleCardProps = ComponentProps<"div"> & {
  className?: string;
  consultation: consultation_medical;
  onClick: () => void;
};

const ConsultationMedicaleCard = ({
  className,
  consultation,
  onClick,
  ...props
}: ConsultationMedicaleCardProps) => {
  return (
    <div className={twMerge(className)} {...props}>
      <StyledCard isConsultation onClick={onClick}>
        <HeaderBox>
          <Typography variant="h6">
            {format(new Date(consultation.date_visite), "d MMMM", {
              locale: fr,
            })}
          </Typography>
        </HeaderBox>
        <Divider />
        <CardContent className="overflow-y-auto">
          <Typography variant="subtitle1" fontWeight="bold">
            Raison pour la visite:{" "}
          </Typography>
          <Typography>{consultation.raison_de_visite}</Typography>
          <Typography variant="subtitle1" fontWeight="bold">
            Diagnostic{" "}
          </Typography>
          <Typography>{consultation.diagnostique}</Typography>
          <Typography variant="subtitle1" fontWeight="bold">
            Plan de soins{" "}
          </Typography>
          <Typography>{consultation.plan_de_soin}</Typography>
        </CardContent>
      </StyledCard>
    </div>
  );
};

export default ConsultationMedicaleCard;
