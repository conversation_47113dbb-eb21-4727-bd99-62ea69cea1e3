import { laboratoire_diagnostics, signe_vitaux } from "@/domain/models";
import { HeaderBox } from "@/presentation/components/common/historiqueCarnetSante/component/HeaderBox";
import { StyledCard } from "@/presentation/components/common/historiqueCarnetSante/component/StyledCard";
import { Typography } from "@mui/material";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { ComponentProps } from "react";
import { twMerge } from "tailwind-merge";

type DiagnosticCardProps = ComponentProps<"div"> & {
  className?: string;
  diagnostic: laboratoire_diagnostics;
  onClick: () => void;
};

const DiagnosticCard = ({
  className,
  diagnostic,
  onClick,
  ...props
}: DiagnosticCardProps) => {
  return (
    <div className={twMerge(className)}>
      <StyledCard isConsultation onClick={onClick}>
        <HeaderBox>
          <Typography variant="h6">
            {format(new Date(diagnostic.date), "d MMMM", {
              locale: fr,
            })}
          </Typography>
        </HeaderBox>
      </StyledCard>
    </div>
  );
};

export default DiagnosticCard;
