import React from "react";
import {
  Autocomplete,
  Avatar,
  Box,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import { Search } from "lucide-react";
import { PatientOption } from "@/presentation/hooks/facturation/useFacturationFilters";

/**
 * Props pour le composant FacturationFilters
 */
interface FacturationFiltersProps {
  /** Patient actuellement sélectionné pour le filtrage */
  selectedPatient: PatientOption | null;
  /** Liste des patients disponibles pour le filtrage */
  availablePatients: PatientOption[];
  /** Callback appelé lors du changement de sélection de patient */
  onSelectedPatientChange: (patient: PatientOption | null) => void;
  /** Indique si le composant est en cours de chargement */
  loading?: boolean;
}

/**
 * Composant de filtrage des facturations par patient
 *
 * Ce composant présente une interface utilisateur permettant de sélectionner
 * un patient pour filtrer les facturations. Il utilise un Autocomplete avec
 * recherche intégrée et affichage d'avatars.
 *
 * Fonctionnalités :
 * - Sélection d'un patient dans une liste déroulante
 * - Recherche par nom/prénom avec autocomplétion
 * - Affichage d'avatars pour chaque patient
 * - Possibilité de vider la sélection
 * - Gestion des états de chargement
 *
 * @param props - Les propriétés du composant
 * @returns Composant React de filtrage
 *
 * @example
 * ```tsx
 * <FacturationFilters
 *   selectedPatient={selectedPatient}
 *   availablePatients={patients}
 *   onSelectedPatientChange={handleChange}
 *   loading={false}
 * />
 * ```
 */
const FacturationFilters: React.FC<FacturationFiltersProps> = ({
  selectedPatient,
  availablePatients,
  onSelectedPatientChange,
  loading = false,
}) => {
  /**
   * Gère le changement de sélection de patient
   * @param newValue - Nouveau patient sélectionné ou null pour désélectionner
   */
  const handlePatientChange = (newValue: PatientOption | null) => {
    onSelectedPatientChange(newValue);
  };
  return (
    <Autocomplete
      size="small"
      value={selectedPatient}
      onChange={(_, newValue) => handlePatientChange(newValue)}
      options={availablePatients}
      className="w-full md:w-[250px]"
      getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
      disabled={loading}
      clearOnEscape
      clearText="Effacer la sélection"
      noOptionsText="Aucun patient trouvé"
      renderOption={(props, option) => (
        <Box
          component="li"
          sx={{ display: "flex", alignItems: "center", gap: 2 }}
          {...props}
          key={option.id}
        >
          <Avatar sx={{ width: 24, height: 24 }} />
          <Typography>
            {option.nom} {option.prenom}
          </Typography>
        </Box>
      )}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder="Chercher par le nom du patient..."
          slotProps={{
            input: {
              ...params.InputProps,
              startAdornment: (
                <InputAdornment position="start">
                  <Search size={18} />
                </InputAdornment>
              ),
            },
          }}
        />
      )}
    />
  );
};

export default FacturationFilters;

// Ré-export de l'interface PatientOption pour la compatibilité
export type { PatientOption } from "@/presentation/hooks/facturation/useFacturationFilters";
