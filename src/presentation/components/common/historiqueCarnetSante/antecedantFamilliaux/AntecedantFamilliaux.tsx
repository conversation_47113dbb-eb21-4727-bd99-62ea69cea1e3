import {
  Autocomplete,
  Box,
  But<PERSON>,
  TextField,
  Typography,
} from "@mui/material";
import { AffectationMedicalesOptions } from "@/presentation/hooks/carnetDeSante/sousCarnet/constants";
import { useAntecedantFamiliaux } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAntecedantFamiliaux";

interface AntecedantFamilliauxProps {
  item: string;
}

const AntecedantFamilliaux = ({ item }: AntecedantFamilliauxProps) => {
  const {
    antecedantFamiliauxState,
    handleAffectionChange,
    handleDecedeChange,
    handleRemarksChange,
  } = useAntecedantFamiliaux();
  return (
    <>
      <Typography variant="subtitle1" sx={{ mt: 3, mb: 1, fontWeight: "bold" }}>
        {item}
      </Typography>
      <Box sx={{ mb: 3 }}>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          Ce membre de la famille est
        </Typography>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            variant={
              antecedantFamiliauxState.decede[item] === true
                ? "contained"
                : "outlined"
            }
            sx={{
              flex: 1,
              textTransform: "none",
              borderRadius: "4px 0 0 4px",
            }}
            onClick={() => handleDecedeChange(item, true)}
          >
            Vivant
          </Button>
          <Button
            variant={
              antecedantFamiliauxState.decede[item] === false
                ? "contained"
                : "outlined"
            }
            sx={{
              flex: 1,
              textTransform: "none",
              borderRadius: "0 4px 4px 0",
            }}
            onClick={() => handleDecedeChange(item, false)}
          >
            Décédé
          </Button>
        </Box>
      </Box>
      <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: "bold" }}>
        Entrez les affections médicales
      </Typography>
      <Box>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          Si vous ne trouvez pas un élément sur la liste, tapez son nom et
          appuyez "Entrée" sur le clavier pour le rajouter
        </Typography>
        <Autocomplete
          size="small"
          options={AffectationMedicalesOptions}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Sélectionnez depuis la liste ou taper ici"
            />
          )}
          value={antecedantFamiliauxState.affection[item] || ""}
          onChange={(e, newValue) =>
            handleAffectionChange(item, newValue || "")
          }
        />
      </Box>
      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ mt: 2 }}
        value={antecedantFamiliauxState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
    </>
  );
};

export default AntecedantFamilliaux;
