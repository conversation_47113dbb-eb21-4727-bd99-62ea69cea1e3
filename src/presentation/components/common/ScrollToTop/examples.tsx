import React, { useState } from 'react';
import {
  useScrollToTop,
  useStepperScrollToTop,
  ScrollToTopWrapper,
  StepperScrollToTop,
  FormStepperWrapper,
  withScrollToTop
} from './index';

/**
 * Exemples d'utilisation des composants ScrollToTop
 * Démonstration des principes SOLID appliqués
 */

// 1. Utilisation basique avec le hook
export const BasicScrollExample: React.FC = () => {
  const [counter, setCounter] = useState(0);
  
  // Scroll automatique à chaque changement de counter
  useScrollToTop(counter, {
    behavior: 'smooth',
    delay: 100
  });

  return (
    <div>
      <button onClick={() => setCounter(c => c + 1)}>
        Incrémenter ({counter}) - Scroll automatique
      </button>
    </div>
  );
};

// 2. Utilisation avec le wrapper
export const WrapperScrollExample: React.FC = () => {
  const [page, setPage] = useState(1);

  return (
    <ScrollToTopWrapper 
      trigger={page}
      config={{ behavior: 'smooth', delay: 200 }}
    >
      <div>
        <h2>Page {page}</h2>
        <button onClick={() => setPage(p => p + 1)}>
          Page suivante
        </button>
      </div>
    </ScrollToTopWrapper>
  );
};

// 3. Utilisation spécialisée pour stepper
export const StepperExample: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const steps = ['Étape 1', 'Étape 2', 'Étape 3'];

  return (
    <StepperScrollToTop 
      activeStep={activeStep}
      config={{ 
        behavior: 'smooth',
        delay: 150,
        top: 100 // Scroll un peu en dessous du haut
      }}
      disabledSteps={[0]} // Pas de scroll pour la première étape
    >
      <div>
        <h2>{steps[activeStep]}</h2>
        <div>
          <button 
            onClick={() => setActiveStep(s => Math.max(0, s - 1))}
            disabled={activeStep === 0}
          >
            Précédent
          </button>
          <button 
            onClick={() => setActiveStep(s => Math.min(steps.length - 1, s + 1))}
            disabled={activeStep === steps.length - 1}
          >
            Suivant
          </button>
        </div>
      </div>
    </StepperScrollToTop>
  );
};

// 4. Utilisation avancée avec FormStepperWrapper
export const AdvancedFormExample: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({});

  const handleStepChange = (step: number) => {
    console.log(`Changement vers l'étape ${step}`);
    // Ici vous pourriez sauvegarder les données, valider, etc.
  };

  const handleBeforeScroll = (step: number) => {
    console.log(`Préparation du scroll vers l'étape ${step}`);
    // Ici vous pourriez afficher un loader, etc.
  };

  const handleAfterScroll = (step: number) => {
    console.log(`Scroll terminé vers l'étape ${step}`);
    // Ici vous pourriez masquer un loader, focus un champ, etc.
  };

  return (
    <FormStepperWrapper
      activeStep={currentStep}
      config={{
        behavior: 'smooth',
        delay: 200,
        top: 50
      }}
      onStepChange={handleStepChange}
      onBeforeScroll={handleBeforeScroll}
      onAfterScroll={handleAfterScroll}
    >
      <div>
        <h2>Formulaire - Étape {currentStep + 1}</h2>
        <div>
          <button 
            onClick={() => setCurrentStep(s => Math.max(0, s - 1))}
            disabled={currentStep === 0}
          >
            Précédent
          </button>
          <button 
            onClick={() => setCurrentStep(s => s + 1)}
          >
            Suivant
          </button>
        </div>
      </div>
    </FormStepperWrapper>
  );
};

// 5. Utilisation avec HOC
const SimpleComponent: React.FC<{ title: string }> = ({ title }) => (
  <div>
    <h1>{title}</h1>
    <p>Contenu du composant</p>
  </div>
);

const ComponentWithScrollToTop = withScrollToTop(SimpleComponent, {
  behavior: 'smooth',
  delay: 100
});

export const HOCExample: React.FC = () => {
  const [title, setTitle] = useState('Titre initial');

  return (
    <div>
      <ComponentWithScrollToTop 
        title={title}
        scrollTrigger={title} // Le scroll se déclenche quand le titre change
      />
      <button onClick={() => setTitle(`Nouveau titre ${Date.now()}`)}>
        Changer le titre
      </button>
    </div>
  );
};

// 6. Utilisation conditionnelle
export const ConditionalScrollExample: React.FC = () => {
  const [data, setData] = useState<any[]>([]);
  const [shouldScroll, setShouldScroll] = useState(true);

  useScrollToTop(data.length, {
    enabled: shouldScroll && data.length > 0,
    behavior: 'smooth'
  });

  return (
    <div>
      <label>
        <input 
          type="checkbox" 
          checked={shouldScroll}
          onChange={(e) => setShouldScroll(e.target.checked)}
        />
        Activer le scroll automatique
      </label>
      <button onClick={() => setData(d => [...d, `Item ${d.length + 1}`])}>
        Ajouter un élément
      </button>
      <ul>
        {data.map((item, index) => (
          <li key={index}>{item}</li>
        ))}
      </ul>
    </div>
  );
};
