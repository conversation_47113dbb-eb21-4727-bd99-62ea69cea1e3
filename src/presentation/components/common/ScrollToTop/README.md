# ScrollToTop Components

Composants réutilisables pour gérer le scroll automatique vers le haut, conçus selon les principes SOLID.

## Principes SOLID Appliqués

### 1. Single Responsibility Principle (SRP)
- `useScrollToTop`: Responsable uniquement du scroll
- `ScrollToTopWrapper`: Responsable uniquement de l'encapsulation
- `StepperScrollToTop`: Responsable uniquement du scroll dans les steppers

### 2. Open/Closed Principle (OCP)
- Les composants sont ouverts à l'extension via la configuration
- Fermés à la modification grâce aux interfaces bien définies
- Possibilité d'étendre avec de nouveaux comportements sans modifier le code existant

### 3. Liskov Substitution Principle (LSP)
- `useStepperScrollToTop` peut remplacer `useScrollToTop`
- `FormStepperWrapper` peut remplacer `StepperScrollToTop`
- Tous respectent les mêmes contrats d'interface

### 4. Interface Segregation Principle (ISP)
- Interfaces spécifiques pour chaque cas d'usage
- Pas de dépendances sur des méthodes non utilisées
- Configuration optionnelle et modulaire

### 5. Dependency Inversion Principle (DIP)
- Dépendance sur des abstractions (interfaces) plutôt que sur des implémentations
- Injection de configuration via props
- Callbacks personnalisables

## Utilisation

### Hook basique
```tsx
import { useScrollToTop } from '@/presentation/components/common/ScrollToTop';

const MyComponent = () => {
  const [page, setPage] = useState(1);
  
  useScrollToTop(page, {
    behavior: 'smooth',
    delay: 200
  });
  
  return <div>...</div>;
};
```

### Wrapper simple
```tsx
import { ScrollToTopWrapper } from '@/presentation/components/common/ScrollToTop';

const MyComponent = () => {
  const [trigger, setTrigger] = useState(0);
  
  return (
    <ScrollToTopWrapper trigger={trigger}>
      <div>Contenu qui déclenche le scroll</div>
    </ScrollToTopWrapper>
  );
};
```

### Stepper spécialisé
```tsx
import { StepperScrollToTop } from '@/presentation/components/common/ScrollToTop';

const MyStepper = () => {
  const [activeStep, setActiveStep] = useState(0);
  
  return (
    <StepperScrollToTop 
      activeStep={activeStep}
      disabledSteps={[0]} // Pas de scroll pour la première étape
    >
      <div>Contenu du stepper</div>
    </StepperScrollToTop>
  );
};
```

### Formulaire avancé
```tsx
import { FormStepperWrapper } from '@/presentation/components/common/ScrollToTop';

const MyForm = () => {
  const [step, setStep] = useState(0);
  
  return (
    <FormStepperWrapper
      activeStep={step}
      onStepChange={(newStep) => console.log('Étape:', newStep)}
      onBeforeScroll={(step) => console.log('Avant scroll:', step)}
      onAfterScroll={(step) => console.log('Après scroll:', step)}
    >
      <form>...</form>
    </FormStepperWrapper>
  );
};
```

## Configuration

```tsx
interface ScrollToTopConfig {
  top?: number;           // Position Y cible (défaut: 0)
  left?: number;          // Position X cible (défaut: 0)
  behavior?: ScrollBehavior; // 'smooth' | 'instant' | 'auto'
  delay?: number;         // Délai en ms (défaut: 0)
  enabled?: boolean;      // Activer/désactiver (défaut: true)
}
```

## Avantages

1. **Réutilisabilité**: Composants modulaires utilisables partout
2. **Flexibilité**: Configuration complète du comportement
3. **Performance**: Optimisé avec useEffect et cleanup
4. **Maintenabilité**: Code organisé selon SOLID
5. **Extensibilité**: Facile d'ajouter de nouveaux comportements
6. **Type Safety**: Interfaces TypeScript complètes

## Tests

Les composants peuvent être testés facilement grâce à leur séparation des responsabilités :

```tsx
// Test du hook
const { result } = renderHook(() => useScrollToTop(trigger, config));

// Test du composant
render(
  <ScrollToTopWrapper trigger={1}>
    <div>Test content</div>
  </ScrollToTopWrapper>
);
```
