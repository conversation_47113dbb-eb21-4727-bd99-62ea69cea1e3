import React from 'react';
import { useScrollToTop, ScrollToTopConfig } from '@/presentation/hooks/useScrollToTop';

/**
 * Interface pour le composant ScrollToTopWrapper
 * Respecte le principe de ségrégation des interfaces (ISP)
 */
interface ScrollToTopWrapperProps {
  /** Valeur qui déclenche le scroll */
  trigger: any;
  /** Configuration du scroll */
  config?: ScrollToTopConfig;
  /** Enfants à rendre */
  children: React.ReactNode;
  /** Classe CSS optionnelle */
  className?: string;
}

/**
 * Composant wrapper qui gère automatiquement le scroll to top
 * Respecte le principe de responsabilité unique (SRP)
 * Respecte le principe d'inversion de dépendance (DIP)
 */
export const ScrollToTopWrapper: React.FC<ScrollToTopWrapperProps> = ({
  trigger,
  config,
  children,
  className
}) => {
  // Utilise le hook pour gérer le scroll
  useScrollToTop(trigger, config);

  return (
    <div className={className}>
      {children}
    </div>
  );
};

/**
 * HOC (Higher Order Component) pour ajouter le scroll to top
 * Respecte le principe ouvert/fermé (OCP)
 */
export const withScrollToTop = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  scrollConfig?: ScrollToTopConfig
) => {
  return React.forwardRef<any, P & { scrollTrigger?: any }>((props, ref) => {
    const { scrollTrigger, ...restProps } = props;
    
    useScrollToTop(scrollTrigger, scrollConfig);
    
    return <WrappedComponent {...(restProps as P)} ref={ref} />;
  });
};

export default ScrollToTopWrapper;
