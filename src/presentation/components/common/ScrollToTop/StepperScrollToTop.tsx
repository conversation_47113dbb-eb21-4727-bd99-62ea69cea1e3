import React from 'react';
import { useStepperScrollToTop, ScrollToTopConfig } from '@/presentation/hooks/useScrollToTop';

/**
 * Interface pour le composant StepperScrollToTop
 * Respecte le principe de ségrégation des interfaces (ISP)
 */
interface StepperScrollToTopProps {
  /** Étape active du stepper */
  activeStep: number;
  /** Configuration du scroll */
  config?: ScrollToTopConfig;
  /** Enfants à rendre */
  children: React.ReactNode;
  /** Classe CSS optionnelle */
  className?: string;
  /** Désactiver le scroll pour certaines étapes */
  disabledSteps?: number[];
}

/**
 * Composant spécialisé pour gérer le scroll dans les steppers
 * Respecte le principe de responsabilité unique (SRP)
 * Respecte le principe ouvert/fermé (OCP) - peut être étendu
 */
export const StepperScrollToTop: React.FC<StepperScrollToTopProps> = ({
  activeStep,
  config,
  children,
  className,
  disabledSteps = []
}) => {
  // Configuration par défaut pour les steppers
  const defaultConfig: ScrollToTopConfig = {
    behavior: 'smooth',
    delay: 150,
    top: 0,
    enabled: !disabledSteps.includes(activeStep),
    ...config
  };

  // Utilise le hook spécialisé pour les steppers
  useStepperScrollToTop(activeStep, defaultConfig);

  return (
    <div className={className}>
      {children}
    </div>
  );
};

/**
 * Interface pour les props du FormStepperWrapper
 */
interface FormStepperWrapperProps extends StepperScrollToTopProps {
  /** Fonction appelée lors du changement d'étape */
  onStepChange?: (step: number) => void;
  /** Fonction appelée avant le scroll */
  onBeforeScroll?: (step: number) => void;
  /** Fonction appelée après le scroll */
  onAfterScroll?: (step: number) => void;
}

/**
 * Composant wrapper avancé pour les formulaires multi-étapes
 * Respecte le principe d'inversion de dépendance (DIP)
 */
export const FormStepperWrapper: React.FC<FormStepperWrapperProps> = ({
  activeStep,
  config,
  children,
  className,
  disabledSteps = [],
  onStepChange,
  onBeforeScroll,
  onAfterScroll
}) => {
  const [previousStep, setPreviousStep] = React.useState(activeStep);

  React.useEffect(() => {
    if (activeStep !== previousStep) {
      onStepChange?.(activeStep);
      onBeforeScroll?.(activeStep);
      
      // Délai pour permettre l'exécution des callbacks
      setTimeout(() => {
        onAfterScroll?.(activeStep);
      }, config?.delay || 150);
      
      setPreviousStep(activeStep);
    }
  }, [activeStep, previousStep, onStepChange, onBeforeScroll, onAfterScroll, config?.delay]);

  return (
    <StepperScrollToTop
      activeStep={activeStep}
      config={config}
      className={className}
      disabledSteps={disabledSteps}
    >
      {children}
    </StepperScrollToTop>
  );
};

export default StepperScrollToTop;
