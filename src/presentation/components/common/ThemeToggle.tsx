import React from 'react';
import { <PERSON>, Moon } from 'lucide-react';
import { motion } from 'framer-motion';
import { useThemeToggle } from '@/presentation/hooks/use-theme-toggle';

/**
 * Interface pour les propriétés du composant ThemeToggle
 */
interface ThemeToggleProps {
  /** Variante du bouton */
  variant?: 'default' | 'floating' | 'minimal';
  /** Taille du bouton */
  size?: 'sm' | 'md' | 'lg';
  /** Afficher le texte à côté de l'icône */
  showText?: boolean;
  /** Classes CSS supplémentaires */
  className?: string;
}

/**
 * Composant de basculement entre mode clair et sombre
 * 
 * @description Ce composant fournit une interface utilisateur élégante
 * pour basculer entre les modes clair et sombre avec des animations fluides.
 * Il utilise le hook useThemeToggle pour la logique de gestion des thèmes.
 * 
 * @features
 * - Animations fluides avec Framer Motion
 * - Plusieurs variantes de style (default, floating, minimal)
 * - Différentes tailles disponibles
 * - Option d'affichage du texte
 * - Icônes animées (soleil/lune)
 * - Accessible avec support clavier
 * 
 * @example
 * ```tsx
 * // Bouton par défaut
 * <ThemeToggle />
 * 
 * // Bouton flottant avec texte
 * <ThemeToggle variant="floating" showText />
 * 
 * // Bouton minimal petit
 * <ThemeToggle variant="minimal" size="sm" />
 * ```
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'default',
  size = 'md',
  showText = false,
  className = '',
}) => {
  const { isDarkMode, toggleTheme } = useThemeToggle();

  // Configuration des tailles
  const sizeConfig = {
    sm: {
      button: 'h-8 w-8 p-1',
      icon: 'h-4 w-4',
      text: 'text-sm',
      gap: 'gap-1',
    },
    md: {
      button: 'h-10 w-10 p-2',
      icon: 'h-5 w-5',
      text: 'text-base',
      gap: 'gap-2',
    },
    lg: {
      button: 'h-12 w-12 p-3',
      icon: 'h-6 w-6',
      text: 'text-lg',
      gap: 'gap-3',
    },
  };

  // Configuration des variantes
  const variantConfig = {
    default: `
      bg-white dark:bg-gray-800 
      border border-gray-200 dark:border-gray-700 
      text-gray-700 dark:text-gray-300
      hover:bg-gray-50 dark:hover:bg-gray-700
      shadow-sm hover:shadow-md
    `,
    floating: `
      bg-white dark:bg-gray-800 
      text-gray-700 dark:text-gray-300
      shadow-lg hover:shadow-xl
      border border-gray-200 dark:border-gray-700
    `,
    minimal: `
      bg-transparent 
      text-gray-600 dark:text-gray-400
      hover:bg-gray-100 dark:hover:bg-gray-800
      hover:text-gray-900 dark:hover:text-gray-100
    `,
  };

  const currentSize = sizeConfig[size];
  const currentVariant = variantConfig[variant];

  // Classes pour le bouton avec ou sans texte
  const buttonClasses = showText
    ? `flex items-center ${currentSize.gap} px-3 py-2 rounded-lg transition-all duration-200 ${currentVariant} ${className}`
    : `${currentSize.button} rounded-lg transition-all duration-200 ${currentVariant} ${className}`;

  return (
    <motion.button
      onClick={toggleTheme}
      className={buttonClasses}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      title={isDarkMode ? 'Passer en mode clair' : 'Passer en mode sombre'}
      aria-label={isDarkMode ? 'Passer en mode clair' : 'Passer en mode sombre'}
    >
      {/* Icône animée */}
      <motion.div
        key={isDarkMode ? 'moon' : 'sun'}
        initial={{ rotate: -180, opacity: 0 }}
        animate={{ rotate: 0, opacity: 1 }}
        exit={{ rotate: 180, opacity: 0 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className="flex items-center justify-center"
      >
        {isDarkMode ? (
          <Moon className={`${currentSize.icon} text-blue-400`} />
        ) : (
          <Sun className={`${currentSize.icon} text-yellow-500`} />
        )}
      </motion.div>

      {/* Texte optionnel */}
      {showText && (
        <motion.span
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.2, delay: 0.1 }}
          className={`${currentSize.text} font-medium`}
        >
          {isDarkMode ? 'Mode clair' : 'Mode sombre'}
        </motion.span>
      )}
    </motion.button>
  );
};

export default ThemeToggle;
