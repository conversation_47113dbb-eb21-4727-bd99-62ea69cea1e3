import { memo, useState } from "react";
import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight, Send, Loader2, Check, AlertCircle } from "lucide-react";
import useRegister from "@/presentation/hooks/use-register";

export interface NavigationButtonsProps {
  activeStep: number;
  onBack: (count: number) => void;
  onNext: (count: number) => void;
  onSubmit: () => void;
  isDisabled: boolean;
  totalSteps: number;
}

export const NavigationButtons = memo(
  ({
    activeStep,
    onBack,
    onNext,
    onSubmit,
    isDisabled,
    totalSteps,
  }: NavigationButtonsProps) => {
    const { loading } = useRegister();
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [hasError, setHasError] = useState(false);

    const handleNext = () => {
      onNext(1);
    };

    const handleBack = () => {
      onBack(1);
    };

    const handleSubmit = () => {
      try {
        setHasError(false);
        onSubmit();
        // Marquer comme soumis après un délai pour laisser le temps au loading
        setTimeout(() => {
          if (!hasError) {
            setIsSubmitted(true);
            // Réinitialiser après 3 secondes
            setTimeout(() => setIsSubmitted(false), 3000);
          }
        }, 1000);
      } catch (error) {
        console.error("Erreur lors de la soumission:", error);
        setHasError(true);
        setTimeout(() => setHasError(false), 3000);
      }
    };

    const isLastStep = activeStep === totalSteps - 1;
    const isSubmitDisabled = isDisabled || loading || isSubmitted;

    return (
      <div className="flex justify-between items-center gap-4 mt-8 px-4">
        {/* Bouton Retour */}
        <motion.button
          onClick={handleBack}
          disabled={activeStep === 0}
          className={`
            flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium text-sm
            transition-all duration-300 ease-out
            ${
              activeStep === 0
                ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-md border border-gray-200 hover:-translate-y-0.5"
            }
          `}
          whileHover={activeStep !== 0 ? { scale: 1.02 } : {}}
          whileTap={activeStep !== 0 ? { scale: 0.98 } : {}}
        >
          <ChevronLeft size={16} />
          <span>Retour</span>
        </motion.button>

        {/* Bouton Suivant/Envoyer */}
        <motion.button
          onClick={isLastStep ? handleSubmit : handleNext}
          disabled={isSubmitDisabled}
          className={`
            flex items-center gap-2 px-6 py-2.5 rounded-lg font-medium text-sm text-white
            transition-all duration-300 ease-out
            ${
              isSubmitDisabled
                ? "bg-gray-300 cursor-not-allowed"
                : hasError
                ? "bg-red-500 hover:bg-red-600"
                : isSubmitted
                ? "bg-green-500 hover:bg-green-600"
                : "bg-gradient-to-r from-meddoc-primary to-meddoc-secondary hover:from-meddoc-primary/90 hover:to-meddoc-secondary/90 hover:shadow-lg hover:-translate-y-0.5"
            }
          `}
          whileHover={!isSubmitDisabled ? { scale: 1.02 } : {}}
          whileTap={!isSubmitDisabled ? { scale: 0.98 } : {}}
        >
          {loading ? (
            <>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Loader2 size={16} />
              </motion.div>
              <span>
                {isLastStep ? "Envoi en cours..." : "Chargement..."}
              </span>
            </>
          ) : hasError ? (
            <>
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <AlertCircle size={16} />
              </motion.div>
              <span>Erreur</span>
            </>
          ) : isSubmitted ? (
            <>
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Check size={16} />
              </motion.div>
              <span>Envoyé !</span>
            </>
          ) : (
            <>
              <span>
                {isLastStep ? "Envoyer" : "Suivant"}
              </span>
              {isLastStep ? (
                <Send size={16} />
              ) : (
                <ChevronRight size={16} />
              )}
            </>
          )}
        </motion.button>
      </div>
    );
  }
);

NavigationButtons.displayName = "NavigationButtons";
