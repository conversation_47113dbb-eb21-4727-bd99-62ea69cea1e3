import { Camera, User, AlertCircle } from "lucide-react";
import { useProfilePhotoUploader } from "@/presentation/hooks/common";
import { PRIMARY } from "@/shared/constants/Color";
import { useFormContext } from "react-hook-form";
import { UPLOAD_CONFIG } from "@/shared/constants/cabinetMedicalConfig";

const ProfilePhotoUploader = () => {
  const { previewUrl, fileInputRef, triggerFileInput, handleImageUpload, error } =
    useProfilePhotoUploader();
  const { formState: { errors } } = useFormContext();

  return (
    <div className="flex flex-col items-center mb-12">
      <p className="mb-4 text-center text-[#07294A] font-medium text-sm sm:text-base">
        Photo de profil (optionnelle)
      </p>
      <p className="mb-4 text-center text-gray-500 text-xs sm:text-sm">
        Taille maximale: {UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB
      </p>
      <div className="relative">
        <div className="w-[100px] h-[100px] sm:w-[120px] sm:h-[120px] rounded-full border-2 border-[#e0e0e0] overflow-hidden bg-gray-200 flex flex-col justify-center items-center align-center">
          {previewUrl ? (
            <img
              src={previewUrl}
              alt="Profile"
              className="w-full h-full object-cover"
            />
          ) : (
            <User size={60} className="text-gray-400" />
          )}
        </div>
        <button
          onClick={triggerFileInput}
          className="absolute bottom-0 right-0 bg-meddoc-primary hover:bg-[#0056b3] text-white rounded-full w-9 h-9 flex items-center justify-center"
          type="button"
        >
          <Camera size={18} />
        </button>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleImageUpload}
          accept="image/*"
          className="hidden"
        />
      </div>

      {/* Affichage des erreurs */}
      {(error || errors.profileImageFile) && (
        <div className="mt-4 flex items-start text-red-500 text-sm">
          <AlertCircle size={16} className="mr-1 mt-0.5 flex-shrink-0" />
          <span>
            {error || errors.profileImageFile?.message?.toString()}
          </span>
        </div>
      )}
    </div>
  );
};

export default ProfilePhotoUploader;
