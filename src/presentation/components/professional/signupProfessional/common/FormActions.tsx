import React from "react";
import { Box, Button } from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";

interface FormActionsProps {
  isFirstStep: boolean;
  isLastStep: boolean;
  isSubmitting: boolean;
  handleBack: () => void;
  handleNext: () => void;
  handlePreview: () => void;
  showPreviewButton?: boolean;
}

const FormActions: React.FC<FormActionsProps> = ({
  isFirstStep,
  isLastStep,
  isSubmitting,
  handleBack,
  handleNext,
  handlePreview,
  showPreviewButton = true,
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "space-between",
        mt: 4,
        flexWrap: { xs: "wrap", sm: "nowrap" },
        gap: 2,
      }}
    >
      <Box sx={{ order: { xs: 2, sm: 1 } }}>
        {!isFirstStep && (
          <Button
            variant="outlined"
            onClick={handleBack}
            sx={{
              borderColor: PRIMARY,
              color: PRIMARY,
              "&:hover": {
                borderColor: PRIMARY,
                backgroundColor: "rgba(0, 123, 255, 0.04)",
              },
              width: { xs: "100%", sm: "auto" },
            }}
          >
            Précédent
          </Button>
        )}
      </Box>

      <Box
        sx={{
          display: "flex",
          gap: 2,
          order: { xs: 1, sm: 2 },
          width: { xs: "100%", sm: "auto" },
          justifyContent: { xs: "space-between", sm: "flex-end" },
        }}
      >
        {showPreviewButton && (
          <Button
            variant="outlined"
            onClick={handlePreview}
            sx={{
              borderColor: PRIMARY,
              color: PRIMARY,
              "&:hover": {
                borderColor: PRIMARY,
                backgroundColor: "rgba(0, 123, 255, 0.04)",
              },
              width: { xs: "48%", sm: "auto" },
            }}
          >
            Aperçu
          </Button>
        )}

        <Button
          variant="contained"
          onClick={handleNext}
          disabled={isSubmitting}
          sx={{
            backgroundColor: PRIMARY,
            "&:hover": {
              backgroundColor: "#0056b3",
            },
            width: { xs: showPreviewButton ? "48%" : "100%", sm: "auto" },
          }}
        >
          {isLastStep ? "Soumettre" : "Suivant"}
        </Button>
      </Box>
    </Box>
  );
};

export default FormActions;
