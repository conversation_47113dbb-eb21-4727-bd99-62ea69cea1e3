import { Box, Typography } from "@mui/material";
import { motion } from "framer-motion";
import { PRIMARY } from "@/shared/constants/Color";
import useCabinetMedicalForm from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";

const FormHeader = () => {
  const { overallProgress, hasSavedData, clearSavedData } =
    useCabinetMedicalForm();

  return (
    <>
      <Typography
        variant="h4"
        component="h1"
        sx={{
          mb: { xs: 2, sm: 3, md: 4 },
          fontWeight: 600,
          fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2.125rem" },
          color: "#07294A",
          letterSpacing: "-0.01em",
          textAlign: { xs: "center", sm: "left" },
        }}
      >
        Inscription Cabinet Médical
      </Typography>

      <Typography
        variant="body1"
        sx={{
          mb: { xs: 2, sm: 3 },
          fontSize: { xs: "0.875rem", sm: "1rem", md: "1.125rem" },
          lineHeight: 1.6,
          color: "#07294A",
          fontWeight: 400,
          textAlign: { xs: "center", sm: "left" },
        }}
      >
        Veuillez remplir ce formulaire pour inscrire votre cabinet médical sur
        MEDDoC Pro. Tous les champs marqués d'un astérisque (*) sont
        obligatoires.
      </Typography>

      {/* Barre de progression */}
      <Box sx={{ mb: 4, mt: 2 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Typography variant="body2" color="textSecondary">
              Progression
            </Typography>
            {hasSavedData && (
              <button
                onClick={clearSavedData}
                style={{
                  marginLeft: "0.5rem",
                  padding: "2px 8px",
                  backgroundColor: "transparent",
                  border: "none",
                  color: "#f44336",
                  cursor: "pointer",
                  fontSize: "0.75rem",
                  textDecoration: "underline",
                }}
              >
                Effacer les données sauvegardées
              </button>
            )}
          </Box>
          <Typography variant="body2" color="textSecondary">
            {overallProgress}%
          </Typography>
        </Box>
        <Box
          sx={{
            width: "100%",
            height: 8,
            bgcolor: "#e0e0e0",
            borderRadius: 4,
            overflow: "hidden",
            position: "relative",
          }}
        >
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${overallProgress}%` }}
            transition={{ duration: 0.5 }}
            style={{
              height: "100%",
              backgroundColor: PRIMARY,
              borderRadius: 4,
            }}
          />
        </Box>
      </Box>
    </>
  );
};

export default FormHeader;
