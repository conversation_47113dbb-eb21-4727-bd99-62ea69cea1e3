import { Box, Typography } from "@mui/material";
import { useFormFooter } from "@/presentation/hooks/common";

const FormFooter = () => {
  const { isLoading, handlePreview } = useFormFooter();
  return (
    <Box sx={{ width: "100%", mt: 3 }}>
      <Typography
        variant="body2"
        color="textSecondary"
        sx={{
          fontSize: { xs: "0.75rem", sm: "0.875rem" },
          textAlign: { xs: "center", sm: "left" },
          mb: 3,
        }}
      >
        MEDDoC a besoin de vos informations professionnelles pour valider votre
        compte et vous offrir les services adaptés à votre cabinet médical.
        Consultez notre politique de confidentialité pour en savoir plus sur nos
        pratiques en matière de protection des données.
      </Typography>

      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          gap: 2,
        }}
      >
        <button
          className="btn-outline p-3 border rounded text-meddoc-primary border-meddoc-primary w-full"
          style={{
            cursor: "pointer",
          }}
          onClick={handlePreview}
          type="button"
        >
          Prévisualiser
        </button>
        <button
          className={`w-full p-3 text-white border-none rounded bg-meddoc-primary ${isLoading ? "opacity-70" : ""}`}
          style={{
            cursor: isLoading ? "wait" : "pointer",
          }}
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? "Enregistrement en cours..." : "Soumettre"}
        </button>
      </Box>
    </Box>
  );
};

export default FormFooter;
