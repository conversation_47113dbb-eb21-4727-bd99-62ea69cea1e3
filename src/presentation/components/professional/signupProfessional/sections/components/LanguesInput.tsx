import React, { useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  Langue,
} from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";
import { Plus, X } from "lucide-react";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const LanguesInput: React.FC = () => {
  const { watch, setValue } = useFormContext<CabinetMedicalFormDTO>();
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [newLangue, setNewLangue] = useState<Omit<Langue, "id">>({
    nom_langue: "",
  });

  const langues = watch("langues") || [];

  // Cette fonction n'est plus utilisée car nous utilisons directement le gestionnaire d'événements onClick

  const handleRemoveLangue = (index: number) => {
    const updatedLangues = [...langues];
    updatedLangues.splice(index, 1);
    setValue("langues", updatedLangues);
  };

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium">Langues parlées*</h4>

      {langues.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {langues.map((langue, index) => (
            <div
              key={langue.id}
              className="flex items-center bg-meddoc-primary text-white px-3 py-1 rounded-full text-sm"
            >
              <span>{langue.nom_langue}</span>
              <button
                type="button"
                className="ml-2 focus:outline-none"
                onClick={() => handleRemoveLangue(index)}
              >
                <X size={16} />
              </button>
            </div>
          ))}
        </div>
      )}

      {isFormVisible ? (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <div className="space-y-3">
            <div className="flex justify-between items-center mb-2">
              <h5 className="font-medium">Ajouter une langue</h5>
              <button
                type="button"
                onClick={() => setIsFormVisible(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={18} />
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Langue *
              </label>
              <input
                type="text"
                value={newLangue.nom_langue}
                onChange={(e) =>
                  setNewLangue({ ...newLangue, nom_langue: e.target.value })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  if (!newLangue.nom_langue) {
                    return; // Ne pas ajouter si le champ est vide
                  }
                  const updatedLangues = [
                    ...langues,
                    {
                      ...newLangue,
                      id: Date.now(),
                    },
                  ];
                  setValue("langues", updatedLangues);
                  setNewLangue({
                    nom_langue: "",
                  });
                  setIsFormVisible(false);
                }}
                className="bg-meddoc-primary text-white px-4 py-2 rounded-md hover:bg-meddoc-fonce"
              >
                Ajouter
              </button>
            </div>
          </div>
        </div>
      ) : (
        <button
          type="button"
          onClick={() => setIsFormVisible(true)}
          className="flex items-center text-meddoc-primary hover:text-meddoc-fonce"
        >
          <Plus size={18} className="mr-1" />
          Ajouter une langue
        </button>
      )}
    </div>
  );
};

export default LanguesInput;
