import { ListeAssurances } from "@/domain/models";
import { formData } from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";
import { UseFormRegister } from "react-hook-form";
import { memo } from "react";
import MultiSelect, {
  ValidationError,
  SelectableItem,
} from "@/presentation/components/common/MultiSelect";

interface InsuranceSelectProps {
  insurancesList: ListeAssurances[];
  selectedInsurances: ListeAssurances[];
  handleInsurancesChange: (insurances: ListeAssurances[]) => void;
  register: UseFormRegister<formData>;
  error?: ValidationError;
}

const InsuranceSelect = ({
  insurancesList,
  selectedInsurances,
  handleInsurancesChange,
  register,
  error,
}: InsuranceSelectProps) => {
  // Fonction pour adapter le type
  const handleItemsChange = (items: SelectableItem[]) => {
    handleInsurancesChange(items as ListeAssurances[]);
  };

  return (
    <MultiSelect
      itemsList={insurancesList as SelectableItem[]}
      selectedItems={selectedInsurances as SelectableItem[]}
      handleItemsChange={handleItemsChange}
      register={register}
      fieldName="insurances"
      displayProperty="nom"
      dropdownId="insurancesList"
      placeholderText="Sélectionnez les assurances (optionnel)"
      selectedText={(count) => `${count} assurance(s) sélectionnée(s)`}
      allSelectedText="Toutes les assurances ont été sélectionnées"
      selectedItemsTitle="Assurances acceptées sélectionnées:"
      error={error}
      sortAlphabetically={true}
    />
  );
};

export default memo(InsuranceSelect);
