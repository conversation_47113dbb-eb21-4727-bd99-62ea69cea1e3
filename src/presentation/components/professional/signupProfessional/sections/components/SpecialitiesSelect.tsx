import { ListeSpecialites } from "@/domain/models";
import { formData } from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";
import { UseFormRegister } from "react-hook-form";
import { memo } from "react";
import MultiSelect, {
  ValidationError,
  SelectableItem,
} from "@/presentation/components/common/MultiSelect";

interface SpecialitiesSelectProps {
  specialitiesList: ListeSpecialites[];
  selectedSpecialities: ListeSpecialites[];
  handleSpecialitiesChange: (specialities: ListeSpecialites[]) => void;
  register: UseFormRegister<formData>;
  error?: ValidationError;
}

const SpecialitiesSelect = ({
  specialitiesList,
  selectedSpecialities,
  handleSpecialitiesChange,
  register,
  error,
}: SpecialitiesSelectProps) => {
  // Fonction pour adapter le type
  const handleItemsChange = (items: SelectableItem[]) => {
    handleSpecialitiesChange(items as ListeSpecialites[]);
  };

  return (
    <MultiSelect
      itemsList={specialitiesList as SelectableItem[]}
      selectedItems={selectedSpecialities as SelectableItem[]}
      handleItemsChange={handleItemsChange}
      register={register}
      fieldName="specialities"
      displayProperty="nom_specialite"
      dropdownId="specialitiesList"
      placeholderText="Sélectionnez une ou plusieurs spécialités"
      allSelectedText="Toutes les spécialités ont été sélectionnées"
      selectedItemsTitle="Spécialités sélectionnées:"
      error={error}
    />
  );
};

export default memo(SpecialitiesSelect);
