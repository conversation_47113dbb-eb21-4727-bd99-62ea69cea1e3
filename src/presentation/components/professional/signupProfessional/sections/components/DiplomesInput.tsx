import React, { useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  Diplome,
} from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";
import { Plus, Trash2, X } from "lucide-react";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const DiplomesInput: React.FC = () => {
  const { watch, setValue } = useFormContext<CabinetMedicalFormDTO>();
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [newDiplome, setNewDiplome] = useState<Omit<Diplome, "id">>({
    titre: "",
    etablissement: "",
    annee: "",
    description: "",
  });

  const diplomes = watch("diplomes") || [];

  const handleRemoveDiplome = (index: number) => {
    const updatedDiplomes = [...diplomes];
    updatedDiplomes.splice(index, 1);
    setValue("diplomes", updatedDiplomes);
  };

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium">
        Diplômes nationaux et universitaires (formations)
      </h4>

      {diplomes.length > 0 && (
        <div className="space-y-3">
          {diplomes.map((diplome, index) => (
            <div
              key={index}
              className="flex items-start justify-between p-3 border border-gray-200 rounded-lg"
            >
              <div>
                <h5 className="font-medium">{diplome.titre}</h5>
                <p className="text-sm text-gray-600">
                  {diplome.etablissement} - {diplome.annee}
                </p>
                {diplome.description && (
                  <p className="text-sm text-gray-500 mt-1">
                    {diplome.description}
                  </p>
                )}
              </div>
              <button
                type="button"
                onClick={() => handleRemoveDiplome(index)}
                className="text-red-500 hover:text-red-700 p-1"
                aria-label="Supprimer"
              >
                <Trash2 size={18} />
              </button>
            </div>
          ))}
        </div>
      )}

      {isFormVisible ? (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <div className="space-y-3">
            <div className="flex justify-between items-center mb-2">
              <h5 className="font-medium">Ajouter un diplôme</h5>
              <button
                type="button"
                onClick={() => setIsFormVisible(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={18} />
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Titre du diplôme *
              </label>
              <input
                type="text"
                value={newDiplome.titre}
                onChange={(e) =>
                  setNewDiplome({ ...newDiplome, titre: e.target.value })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Établissement *
              </label>
              <input
                type="text"
                value={newDiplome.etablissement}
                onChange={(e) =>
                  setNewDiplome({
                    ...newDiplome,
                    etablissement: e.target.value,
                  })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Année d'obtention *
              </label>
              <input
                type="text"
                value={newDiplome.annee}
                onChange={(e) =>
                  setNewDiplome({ ...newDiplome, annee: e.target.value })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={newDiplome.description}
                onChange={(e) =>
                  setNewDiplome({ ...newDiplome, description: e.target.value })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
              />
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  if (
                    !newDiplome.titre ||
                    !newDiplome.etablissement ||
                    !newDiplome.annee
                  ) {
                    return; // Ne pas ajouter si les champs obligatoires sont vides
                  }
                  const updatedDiplomes = [
                    ...diplomes,
                    {
                      ...newDiplome,
                      id: Date.now(),
                    },
                  ];
                  setValue("diplomes", updatedDiplomes);
                  setNewDiplome({
                    titre: "",
                    etablissement: "",
                    annee: "",
                    description: "",
                  });
                  setIsFormVisible(false);
                }}
                className="bg-meddoc-primary text-white px-4 py-2 rounded-md hover:bg-meddoc-fonce"
              >
                Ajouter
              </button>
            </div>
          </div>
        </div>
      ) : (
        <button
          type="button"
          onClick={() => setIsFormVisible(true)}
          className="flex items-center text-meddoc-primary hover:text-meddoc-fonce"
        >
          <Plus size={18} className="mr-1" />
          Ajouter un diplôme
        </button>
      )}
    </div>
  );
};

export default DiplomesInput;
