import React, { useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  Experience,
} from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";
import { Plus, Trash2, X } from "lucide-react";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const ExperiencesInput: React.FC = () => {
  const { watch, setValue } = useFormContext<CabinetMedicalFormDTO>();
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [newExperience, setNewExperience] = useState<Omit<Experience, "id">>({
    poste: "",
    etablissement: "",
    date_debut: "",
    date_fin: "",
    description: "",
    est_actuel: false,
  });

  const experiences = watch("experiences") || [];

  const handleRemoveExperience = (index: number) => {
    const updatedExperiences = [...experiences];
    updatedExperiences.splice(index, 1);
    setValue("experiences", updatedExperiences);
  };

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium">Expériences</h4>

      {experiences.length > 0 && (
        <div className="space-y-3">
          {experiences.map((experience, index) => (
            <div
              key={experience.id}
              className="flex items-start justify-between p-3 border border-gray-200 rounded-lg"
            >
              <div>
                <h5 className="font-medium">{experience.poste}</h5>
                <p className="text-sm text-gray-600">
                  {experience.etablissement} - {experience.date_debut}
                  {experience.est_actuel
                    ? " à aujourd'hui"
                    : experience.date_fin
                      ? ` à ${experience.date_fin}`
                      : ""}
                </p>
                {experience.description && (
                  <p className="text-sm text-gray-500 mt-1">
                    {experience.description}
                  </p>
                )}
              </div>
              <button
                type="button"
                onClick={() => handleRemoveExperience(index)}
                className="text-red-500 hover:text-red-700 p-1"
                aria-label="Supprimer"
              >
                <Trash2 size={18} />
              </button>
            </div>
          ))}
        </div>
      )}

      {isFormVisible ? (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <div className="space-y-3">
            <div className="flex justify-between items-center mb-2">
              <h5 className="font-medium">Ajouter une expérience</h5>
              <button
                type="button"
                onClick={() => setIsFormVisible(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={18} />
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Poste *
              </label>
              <input
                type="text"
                value={newExperience.poste}
                onChange={(e) =>
                  setNewExperience({ ...newExperience, poste: e.target.value })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Établissement *
              </label>
              <input
                type="text"
                value={newExperience.etablissement}
                onChange={(e) =>
                  setNewExperience({
                    ...newExperience,
                    etablissement: e.target.value,
                  })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date de début *
              </label>
              <input
                type="text"
                value={newExperience.date_debut}
                onChange={(e) =>
                  setNewExperience({
                    ...newExperience,
                    date_debut: e.target.value,
                  })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="ex: 2020"
              />
            </div>

            <div className="flex items-center mb-3">
              <input
                type="checkbox"
                id="est_actuel"
                checked={newExperience.est_actuel}
                onChange={(e) =>
                  setNewExperience({
                    ...newExperience,
                    est_actuel: e.target.checked,
                    date_fin: e.target.checked ? "" : newExperience.date_fin,
                  })
                }
                className="mr-2"
              />
              <label
                htmlFor="est_actuel"
                className="text-sm font-medium text-gray-700"
              >
                Poste actuel
              </label>
            </div>

            {!newExperience.est_actuel && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date de fin
                </label>
                <input
                  type="text"
                  value={newExperience.date_fin}
                  onChange={(e) =>
                    setNewExperience({
                      ...newExperience,
                      date_fin: e.target.value,
                    })
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="ex: 2023"
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={newExperience.description}
                onChange={(e) =>
                  setNewExperience({
                    ...newExperience,
                    description: e.target.value,
                  })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
              />
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  if (
                    !newExperience.poste ||
                    !newExperience.etablissement ||
                    !newExperience.date_debut
                  ) {
                    return; // Ne pas ajouter si les champs obligatoires sont vides
                  }
                  const updatedExperiences = [
                    ...experiences,
                    {
                      ...newExperience,
                      id: Date.now(),
                    },
                  ];
                  setValue("experiences", updatedExperiences);
                  setNewExperience({
                    poste: "",
                    etablissement: "",
                    date_debut: "",
                    date_fin: "",
                    description: "",
                    est_actuel: false,
                  });
                  setIsFormVisible(false);
                }}
                className="bg-meddoc-primary text-white px-4 py-2 rounded-md hover:bg-meddoc-fonce"
              >
                Ajouter
              </button>
            </div>
          </div>
        </div>
      ) : (
        <button
          type="button"
          onClick={() => setIsFormVisible(true)}
          className="flex items-center text-meddoc-primary hover:text-meddoc-fonce"
        >
          <Plus size={18} className="mr-1" />
          Ajouter une expérience
        </button>
      )}
    </div>
  );
};

export default ExperiencesInput;
