import { ordre_appartenance } from "@/domain/models";
import { UseFormRegister } from "react-hook-form";
import { memo } from "react";
import MultiSelect, {
  ValidationError,
  SelectableItem,
} from "@/presentation/components/common/MultiSelect";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO";

interface OrdreAppartenanceSelectProps {
  ordresList: ordre_appartenance[];
  selectedOrdres: ordre_appartenance[];
  handleOrdreAppartenanceChange: (ordres: ordre_appartenance[]) => void;
  register: UseFormRegister<CabinetMedicalFormDTO>;
  error?: ValidationError;
}

const OrdreAppartenanceSelect = ({
  ordresList,
  selectedOrdres,
  handleOrdreAppartenanceChange,
  register,
  error,
}: OrdreAppartenanceSelectProps) => {
  // Fonction pour adapter le type
  const handleItemsChange = (items: SelectableItem[]) => {
    handleOrdreAppartenanceChange(items as ordre_appartenance[]);
  };

  return (
    <MultiSelect
      itemsList={ordresList as SelectableItem[]}
      selectedItems={selectedOrdres as SelectableItem[]}
      handleItemsChange={handleItemsChange}
      register={register}
      fieldName="ordre_appartenance"
      displayProperty="nom"
      dropdownId="ordreAppartenancesList"
      placeholderText="Sélectionnez un ou plusieurs ordres d'appartenance"
      allSelectedText="Tous les ordres ont été sélectionnés"
      selectedItemsTitle="Ordres d'appartenance sélectionnés:"
      error={error}
    />
  );
};

export default memo(OrdreAppartenanceSelect);
