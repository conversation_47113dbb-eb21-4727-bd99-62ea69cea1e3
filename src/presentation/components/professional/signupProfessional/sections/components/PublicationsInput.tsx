import React, { useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  Publication,
} from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form";
import { ExternalLink, Plus, Trash2, X } from "lucide-react";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const PublicationsInput: React.FC = () => {
  const { watch, setValue } = useFormContext<CabinetMedicalFormDTO>();
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [newPublication, setNewPublication] = useState<Omit<Publication, "id">>(
    {
      titre: "",
      annee: "",
      description: "",
      lien: "",
    }
  );

  const publications = watch("publications") || [];

  // Cette fonction n'est plus utilisée car nous utilisons directement le gestionnaire d'événements onClick

  const handleRemovePublication = (index: number) => {
    const updatedPublications = [...publications];
    updatedPublications.splice(index, 1);
    setValue("publications", updatedPublications);
  };

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium">
        Travaux et publications (avec lien)
      </h4>

      {publications.length > 0 && (
        <div className="space-y-3">
          {publications.map((publication, index) => (
            <div
              key={publication.id}
              className="flex items-start justify-between p-3 border border-gray-200 rounded-lg"
            >
              <div>
                <div className="flex items-start">
                  <h5 className="font-medium">{publication.titre}</h5>
                  {publication.lien && (
                    <a
                      href={publication.lien}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-meddoc-primary hover:text-meddoc-fonce ml-2"
                      aria-label="Voir la publication"
                    >
                      <ExternalLink size={18} />
                    </a>
                  )}
                </div>
                <p className="text-sm text-gray-600">{publication.annee}</p>
                {publication.description && (
                  <p className="text-sm text-gray-500 mt-1">
                    {publication.description}
                  </p>
                )}
              </div>
              <button
                type="button"
                onClick={() => handleRemovePublication(index)}
                className="text-red-500 hover:text-red-700 p-1"
                aria-label="Supprimer"
              >
                <Trash2 size={18} />
              </button>
            </div>
          ))}
        </div>
      )}

      {isFormVisible ? (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <div className="space-y-3">
            <div className="flex justify-between items-center mb-2">
              <h5 className="font-medium">Ajouter une publication</h5>
              <button
                type="button"
                onClick={() => setIsFormVisible(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={18} />
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Titre *
              </label>
              <input
                type="text"
                value={newPublication.titre}
                onChange={(e) =>
                  setNewPublication({
                    ...newPublication,
                    titre: e.target.value,
                  })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Année *
              </label>
              <input
                type="text"
                value={newPublication.annee}
                onChange={(e) =>
                  setNewPublication({
                    ...newPublication,
                    annee: e.target.value,
                  })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Lien
              </label>
              <input
                type="url"
                value={newPublication.lien}
                onChange={(e) =>
                  setNewPublication({ ...newPublication, lien: e.target.value })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="https://..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={newPublication.description}
                onChange={(e) =>
                  setNewPublication({
                    ...newPublication,
                    description: e.target.value,
                  })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
              />
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  if (!newPublication.titre || !newPublication.annee) {
                    return; // Ne pas ajouter si les champs obligatoires sont vides
                  }
                  const updatedPublications = [
                    ...publications,
                    {
                      ...newPublication,
                      id: Date.now(),
                    },
                  ];
                  setValue("publications", updatedPublications);
                  setNewPublication({
                    titre: "",
                    annee: "",
                    description: "",
                    lien: "",
                  });
                  setIsFormVisible(false);
                }}
                className="bg-meddoc-primary text-white px-4 py-2 rounded-md hover:bg-meddoc-fonce"
              >
                Ajouter
              </button>
            </div>
          </div>
        </div>
      ) : (
        <button
          type="button"
          onClick={() => setIsFormVisible(true)}
          className="flex items-center text-meddoc-primary hover:text-meddoc-fonce"
        >
          <Plus size={18} className="mr-1" />
          Ajouter une publication
        </button>
      )}
    </div>
  );
};

export default PublicationsInput;
