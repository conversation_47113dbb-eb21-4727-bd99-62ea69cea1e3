import { mot_cles } from "@/domain/models/MotCles";
import { UseFormRegister } from "react-hook-form";
import { memo } from "react";
import MultiSelect, {
  ValidationError,
  SelectableItem,
} from "@/presentation/components/common/MultiSelect";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO";

interface MotCleSelectProps {
  motClesList: mot_cles[];
  selectedMotCles: mot_cles[];
  handleMotClesChange: (motCles: mot_cles[]) => void;
  register: UseFormRegister<CabinetMedicalFormDTO>;
  error?: ValidationError;
}

const MotCleSelect = ({
  motClesList,
  selectedMotCles,
  handleMotClesChange,
  register,
  error,
}: MotCleSelectProps) => {
  // Fonction pour adapter le type
  const handleItemsChange = (items: SelectableItem[]) => {
    handleMotClesChange(items as mot_cles[]);
  };

  return (
    <MultiSelect
      itemsList={motClesList as SelectableItem[]}
      selectedItems={selectedMotCles as SelectableItem[]}
      handleItemsChange={handleItemsChange}
      register={register}
      fieldName="motCles"
      displayProperty="symptome"
      dropdownId="motClesList"
      placeholderText="Sélectionnez les mots-clés pour la recherche"
      selectedText={(count) => `${count} mot(s)-clé(s) sélectionné(s)`}
      allSelectedText="Tous les mots-clés ont été sélectionnés"
      selectedItemsTitle="Mots-clés sélectionnés:"
      error={error}
      sortAlphabetically={true}
    />
  );
};

export default memo(MotCleSelect);
