import { memo } from "react";
import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import { Phone, CheckCircle, Circle, ChevronDown, Mail } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useContactSection } from "@/presentation/hooks/sections";
import { useFormContext } from "react-hook-form";
import { FormField } from "./components";
import { useSectionProgress } from "@/presentation/hooks/sections/use-section-progress";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const ContactSection = () => {
  const {
    register,
    formState: { errors },
    watch,
  } = useFormContext<CabinetMedicalFormDTO>();

  const { isExpanded, handleAccordionChange } = useContactSection();

  // Calculer la progression de la section
  const sectionComplete = !!watch("email") && !!watch("telephone");

  // Utiliser le hook pour gérer la progression de la section
  const { isComplete } = useSectionProgress("panel3", sectionComplete);

  return (
    <Accordion
      expanded={isExpanded}
      onChange={handleAccordionChange}
      sx={{ width: "100%", mb: 1 }}
    >
      <AccordionSummary
        expandIcon={<ChevronDown />}
        aria-controls="panel3-content"
        id="panel3-header"
        sx={{
          backgroundColor: PRIMARY,
          color: "white",
          "& .MuiAccordionSummary-expandIconWrapper": {
            color: "white",
          },
        }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Phone size={20} className="mr-2" />
            <h3 className="text-base font-medium">Coordonnées</h3>
          </div>
          <div className="flex items-center">
            {isComplete ? (
              <CheckCircle size={20} className="mr-2" />
            ) : (
              <Circle size={20} className="mr-2 opacity-50" />
            )}
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <div className="p-2">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 md:gap-4 w-full">
            <FormField
              id="email"
              label="Email"
              placeholder="Entrez votre email"
              type="email"
              icon={Mail}
              register={register}
              required
              error={errors.email}
              validation={{
                required: "L'email est requis",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Adresse email invalide",
                },
              }}
              inputMode="email"
            />
            <FormField
              id="telephone"
              label="Téléphone"
              placeholder="Entrez votre numéro de téléphone"
              icon={Phone}
              register={register}
              required
              error={errors.telephone}
              validation={{
                required: "Le numéro de téléphone est requis",
                pattern: {
                  value: /\d{3}[\s.-]?\d{2}[\s.-]?\d{3}[\s.-]?\d{2}/,
                  message: "Format de téléphone invalide (ex: 034 12 345 67)",
                },
              }}
              inputMode="tel"
            />
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default memo(ContactSection);
