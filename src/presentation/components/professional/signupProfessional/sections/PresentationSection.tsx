import { memo, useState } from "react";
import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import {
  Info,
  CheckCircle,
  Circle,
  ChevronDown,
  X,
  Camera,
  GraduationCap,
  Briefcase,
  FileText,
  Languages,
  AlertCircle,
} from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { usePresentationSection } from "@/presentation/hooks/sections";
import { useFormContext } from "react-hook-form";
import { useRef } from "react";
import { useSectionProgress } from "@/presentation/hooks/sections/use-section-progress";
import DiplomesInput from "./components/DiplomesInput";
import ExperiencesInput from "./components/ExperiencesInput";
import PublicationsInput from "./components/PublicationsInput";
import LanguesInput from "./components/LanguesInput";
import MotCleSelection from "./MotCleSection";
import { UPLOAD_CONFIG } from "@/shared/constants/cabinetMedicalConfig";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const PresentationSection = () => {
  const { isExpanded, handleAccordionChange } = usePresentationSection();
  const {
    register,
    watch,
    setValue,
    formState: { errors, isSubmitted },
  } = useFormContext<CabinetMedicalFormDTO>();

  const cabinetImagesInputRef = useRef<HTMLInputElement>(null);

  // Tout les champs de cette section sont optionnels
  const sectionComplete = true;

  // Utiliser le hook pour gérer la progression de la section
  const { isComplete } = useSectionProgress("panel4", sectionComplete);

  // Fonction pour déclencher le clic sur l'input file caché
  const triggerCabinetImagesInput = () => {
    if (cabinetImagesInputRef.current) {
      cabinetImagesInputRef.current.click();
    }
  };

  // État local pour les erreurs d'images
  const [imageErrors, setImageErrors] = useState<string[]>([]);

  // Fonction pour gérer l'upload d'images
  const handleCabinetImagesUpload = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      const currentImages = watch("cabinetImages") || [];
      const currentPreviewUrls = watch("cabinetImagesPreviewUrls") || [];
      const newErrors: string[] = [];

      // Limiter le nombre total d'images au maximum défini
      const availableSlots =
        UPLOAD_CONFIG.MAX_CABINET_IMAGES - currentImages.length;
      const filesToProcess = files.slice(0, availableSlots);

      // Stocker les fichiers et créer des URLs pour la prévisualisation
      const newFiles = [...currentImages];
      const newPreviewUrls = [...currentPreviewUrls];

      // Vérifier la taille de chaque fichier
      const maxSizeBytes = UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB * 1024 * 1024;

      filesToProcess.forEach((file) => {
        // Vérifier la taille du fichier
        if (file.size > maxSizeBytes) {
          const errorMessage = `L'image "${file.name}" (${(file.size / 1024 / 1024).toFixed(2)}MB) dépasse la limite de ${UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB`;
          newErrors.push(errorMessage);
          return;
        }

        // Ajouter le fichier à la liste
        newFiles.push(file);

        // Créer une URL pour la prévisualisation
        const objectUrl = URL.createObjectURL(file);
        newPreviewUrls.push(objectUrl);
      });

      // Mettre à jour les erreurs
      setImageErrors(newErrors);

      // Si des erreurs sont présentes, les ajouter au formulaire
      if (newErrors.length > 0) {
        setValue("cabinetImagesErrors", newErrors, {
          shouldValidate: true,
        });
      } else {
        // Effacer les erreurs précédentes
        setValue("cabinetImagesErrors", [], {
          shouldValidate: true,
        });
      }

      // Mettre à jour les valeurs dans le formulaire
      setValue("cabinetImages", newFiles);
      setValue("cabinetImagesPreviewUrls", newPreviewUrls);
    }
  };

  // Fonction pour supprimer une image
  const removeCabinetImage = (index: number) => {
    const currentImages = [...watch("cabinetImages")];
    const currentPreviewUrls = [...(watch("cabinetImagesPreviewUrls") || [])];

    // Révoquer l'URL de prévisualisation pour libérer la mémoire
    if (currentPreviewUrls[index]) {
      URL.revokeObjectURL(currentPreviewUrls[index]);
    }

    // Supprimer l'image et son URL de prévisualisation
    currentImages.splice(index, 1);
    currentPreviewUrls.splice(index, 1);

    setValue("cabinetImages", currentImages);
    setValue("cabinetImagesPreviewUrls", currentPreviewUrls);
  };

  return (
    <Accordion
      expanded={isExpanded}
      onChange={handleAccordionChange}
      sx={{ width: "100%", mb: 1 }}
    >
      <AccordionSummary
        expandIcon={<ChevronDown />}
        aria-controls="panel4-content"
        id="panel4-header"
        sx={{
          backgroundColor: PRIMARY,
          color: "white",
          "& .MuiAccordionSummary-expandIconWrapper": {
            color: "white",
          },
        }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Info size={20} className="mr-2" />
            <h3 className="text-base font-medium">Présentation et Images</h3>
          </div>
          <div className="flex items-center">
            {isComplete ? (
              <CheckCircle size={20} className="mr-2" />
            ) : (
              <Circle size={20} className="mr-2 opacity-50" />
            )}
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <div className="p-2">
          <div className="flex flex-col gap-4 w-full">
            <h4 className="text-sm font-medium mb-1">
              Présentez votre cabinet médical et ajoutez jusqu'à{" "}
              {UPLOAD_CONFIG.MAX_CABINET_IMAGES} images
            </h4>

            {/* Champ de présentation */}
            <div className="w-full">
              <textarea
                id="presentation"
                name="presentation"
                rows={4}
                className={`w-full p-3 border resize-none rounded-lg focus:outline-none focus:border-meddoc-primary ${
                  errors.presentation ? "border-red-500" : "border-gray-200"
                }`}
                placeholder="Décrivez votre cabinet, vos services, votre équipe, etc."
                aria-invalid={errors.presentation ? "true" : "false"}
                aria-describedby={
                  errors.presentation ? "presentation-error" : undefined
                }
                {...register("presentation", {
                  required: "La présentation est requise",
                  minLength: {
                    value: 20,
                    message:
                      "La présentation doit contenir au moins 20 caractères",
                  },
                })}
              ></textarea>
              {errors.presentation && (
                <p
                  id="presentation-error"
                  className="mt-1 text-sm text-red-500"
                >
                  {errors.presentation.message}
                </p>
              )}
            </div>

            {/* Diplômes */}
            <div className="mt-6">
              <div className="flex items-center gap-2 mb-2">
                <GraduationCap size={18} className="text-meddoc-primary" />
                <h4 className="text-sm font-medium">
                  Diplômes nationaux et universitaires (formations)
                </h4>
              </div>
              <DiplomesInput />
            </div>

            {/* Expériences */}
            <div className="mt-6">
              <div className="flex items-center gap-2 mb-2">
                <Briefcase size={18} className="text-meddoc-primary" />
                <h4 className="text-sm font-medium">Expériences</h4>
              </div>
              <ExperiencesInput />
            </div>

            {/* Publications */}
            <div className="mt-6">
              <div className="flex items-center gap-2 mb-2">
                <FileText size={18} className="text-meddoc-primary" />
                <h4 className="text-sm font-medium">
                  Travaux et publications (avec lien)
                </h4>
              </div>
              <PublicationsInput />
            </div>

            {/* Langues parlées */}
            <div className="mt-6">
              <div className="flex items-center gap-2 mb-2">
                <Languages size={18} className="text-meddoc-primary" />
                <h4 className="text-sm font-medium">Langues parlées*</h4>
              </div>
              <LanguesInput />
            </div>

            {/* Mots-clés */}
            <MotCleSelection />

            {/* Upload d'images */}
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">
                Images du cabinet ({watch("cabinetImages")?.length || 0}/
                {UPLOAD_CONFIG.MAX_CABINET_IMAGES})
              </h4>

              <div className="flex flex-col gap-2">
                <p className="text-xs text-gray-500">
                  Taille maximale par image: {UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB
                </p>
                <div className="flex flex-wrap gap-4">
                  {/* Affichage des images existantes */}
                  {watch("cabinetImagesPreviewUrls")?.map(
                    (previewUrl, index) => (
                      <div
                        key={index}
                        className="relative w-[150px] h-[150px] border border-gray-200 rounded-lg overflow-hidden"
                      >
                        <img
                          src={previewUrl}
                          alt={`Cabinet image ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                        <button
                          type="button"
                          onClick={() => removeCabinetImage(index)}
                          className="absolute top-1 right-1 bg-white bg-opacity-70 hover:bg-opacity-90 text-red-500 p-1 rounded-full"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    )
                  )}

                  {/* Bouton d'ajout d'image (si moins que le maximum d'images) */}
                  {(!watch("cabinetImages") ||
                    watch("cabinetImages").length <
                      UPLOAD_CONFIG.MAX_CABINET_IMAGES) && (
                    <div
                      onClick={triggerCabinetImagesInput}
                      className="w-[150px] h-[150px] border border-dashed border-gray-400 rounded-lg flex flex-col justify-center items-center cursor-pointer bg-gray-50 hover:bg-gray-100"
                    >
                      <Camera size={24} className="text-gray-600" />
                      <p className="mt-2 text-sm text-gray-600">
                        Ajouter une image
                      </p>
                    </div>
                  )}

                  <input
                    type="file"
                    ref={cabinetImagesInputRef}
                    onChange={handleCabinetImagesUpload}
                    accept={UPLOAD_CONFIG.ACCEPTED_IMAGE_TYPES}
                    className="hidden"
                    multiple={
                      !watch("cabinetImages") ||
                      watch("cabinetImages").length <
                        UPLOAD_CONFIG.MAX_CABINET_IMAGES
                    }
                  />
                </div>
              </div>

              {/* Affichage des erreurs pour les images */}
              {imageErrors.length > 0 && (
                <div className="mt-2">
                  {imageErrors.map((error, index) => (
                    <div
                      key={index}
                      className="flex items-start text-red-500 text-sm mt-1"
                    >
                      <AlertCircle
                        size={16}
                        className="mr-1 mt-0.5 flex-shrink-0"
                      />
                      <span>{error}</span>
                    </div>
                  ))}
                </div>
              )}

              {errors.cabinetImages?.message &&
                Object.values(errors.cabinetImages.message).some(
                  (error) => error !== ""
                ) &&
                isSubmitted && (
                  <p className="mt-2 text-sm text-red-500">
                    Veuillez corriger les erreurs dans le formulaire avant de
                    continuer.
                  </p>
                )}
            </div>
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default memo(PresentationSection);
