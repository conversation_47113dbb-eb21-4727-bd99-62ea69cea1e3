import { memo } from "react";
import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import {
  MapPin,
  CheckCircle,
  Circle,
  ChevronDown,
  MapPinned,
  Building,
  Home,
} from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import SelectRegion from "@/presentation/components/locationSelector/SelectRegion";
import SelectDistrict from "@/presentation/components/locationSelector/SelectDistrict";
import SelectCommune from "@/presentation/components/locationSelector/SelectCommune";
import LazyLocationPickerMap from "@/presentation/components/features/leaflet/LazyLocationPickerMap";
import {
  useLocationSection,
  useSectionProgress,
} from "@/presentation/hooks/sections";
import { useFormContext } from "react-hook-form";
import { FormField } from "./components";
import { Commune, District, Region } from "@/domain/models";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const LocationSection = () => {
  const { isExpanded, handleAccordionChange } = useLocationSection();

  const {
    formState: { errors },
    watch,
    register,
    setValue,
  } = useFormContext<CabinetMedicalFormDTO>();

  const handleRegionChange = (value: Region) => {
    setValue("region", value);
    setValue("district", null);
    setValue("commune", null);
  };

  const handleDistrictChange = (value: District) => {
    setValue("district", value);
    setValue("commune", null);
  };

  const handleCommuneChange = (value: Commune) => {
    setValue("commune", value);
  };

  const handleGeolocationChange = (value: string) => {
    setValue("geolocation", value);
  };

  const selectedRegion = watch("region");
  const selectedDistrict = watch("district");
  const selectedCommune = watch("commune");
  const geolocation = watch("geolocation");
  const adresse = watch("adresse");
  const fokotany = watch("fokotany");
  const infoAcces = watch("infoAcces");

  // Vérifier si tous les champs obligatoires sont remplis
  const sectionComplete =
    !!adresse &&
    !!selectedRegion &&
    !!selectedDistrict &&
    !!selectedCommune &&
    !!fokotany &&
    !!infoAcces &&
    !!geolocation;

  // Utiliser le hook pour gérer la progression de la section
  const { isComplete } = useSectionProgress("panel2", sectionComplete);

  return (
    <Accordion
      expanded={isExpanded}
      onChange={handleAccordionChange}
      sx={{ width: "100%", mb: 1 }}
    >
      <AccordionSummary
        expandIcon={<ChevronDown />}
        aria-controls="panel2-content"
        id="panel2-header"
        sx={{
          backgroundColor: PRIMARY,
          color: "white",
          "& .MuiAccordionSummary-expandIconWrapper": {
            color: "white",
          },
        }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <MapPin size={20} className="mr-2" />
            <h3 className="text-base font-medium">Localisation</h3>
          </div>
          <div className="flex items-center">
            {isComplete ? (
              <CheckCircle size={20} className="mr-2" />
            ) : (
              <Circle size={20} className="mr-2 opacity-50" />
            )}
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <div className="p-2">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 md:gap-4 w-full">
            {/* Adresse */}
            <div className="col-span-1 sm:col-span-2">
              <FormField
                id="adresse"
                label="Adresse"
                placeholder="Entrez votre adresse"
                icon={MapPinned}
                register={register}
                required
                error={errors.adresse}
                validation={{
                  required: "L'adresse est requise",
                  minLength: {
                    value: 5,
                    message: "L'adresse doit contenir au moins 5 caractères",
                  },
                }}
              />
            </div>

            {/* Région */}
            <div className="col-span-1">
              <SelectRegion
                value={selectedRegion}
                onChange={handleRegionChange}
                allNeeded={true}
                errors={errors}
                register={register}
              />
            </div>

            {/* District */}
            <div className="col-span-1 sm:col-span-1">
              <SelectDistrict
                value={selectedDistrict}
                onChange={handleDistrictChange}
                isDisabled={!selectedRegion}
                errors={errors}
                register={register}
              />
            </div>

            {/* Commune */}
            <div className="col-span-1">
              <SelectCommune
                value={selectedCommune}
                onChange={handleCommuneChange}
                isDisabled={!selectedDistrict}
                errors={errors}
                register={register}
              />
            </div>

            {/* Fokotany */}
            <div className="col-span-1 sm:col-span-1">
              <FormField
                id="fokotany"
                label="Fokotany"
                placeholder="Entrez votre fokotany"
                icon={Home}
                register={register}
                required
                error={errors.fokotany}
                validation={{
                  required: "Le fokotany est requis",
                }}
              />
            </div>

            {/* Informations d'accès */}
            <div className="col-span-1 sm:col-span-2">
              <FormField
                id="infoAcces"
                label="Informations d'accès"
                placeholder="Entrez les informations d'accès"
                icon={MapPin}
                register={register}
                required
                error={errors.infoAcces}
                validation={{
                  required: "Les informations d'accès sont requises",
                }}
              />
            </div>

            {/* Carte de localisation */}
            <div className="col-span-1 sm:col-span-2">
              <LazyLocationPickerMap
                value={geolocation || ""}
                onChange={handleGeolocationChange}
                isVisible={isExpanded}
                name="geolocation"
              />
            </div>
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default memo(LocationSection);
