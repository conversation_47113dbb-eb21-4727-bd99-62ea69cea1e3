import { memo } from "react";
import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import { Lock, CheckCircle, Circle, ChevronDown } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import {
  useAuthenticationSection,
  useSectionProgress,
} from "@/presentation/hooks/sections";
import { useFormContext } from "react-hook-form";
import { FormField } from "./components";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const AuthenticationSection = () => {
  const { isExpanded, handleAccordionChange } = useAuthenticationSection();

  const {
    formState: { errors },
    watch,
    register,
  } = useFormContext<CabinetMedicalFormDTO>();

  const sectionComplete =
    !!watch("motDePasse") &&
    !!watch("confirmMotDePasse") &&
    watch("motDePasse") === watch("confirmMotDePasse");

  const { isComplete } = useSectionProgress("panel5", sectionComplete);

  return (
    <Accordion
      expanded={isExpanded}
      onChange={handleAccordionChange}
      sx={{ width: "100%", mb: 1 }}
    >
      <AccordionSummary
        expandIcon={<ChevronDown />}
        aria-controls="panel5-content"
        id="panel5-header"
        sx={{
          backgroundColor: PRIMARY,
          color: "white",
          "& .MuiAccordionSummary-expandIconWrapper": {
            color: "white",
          },
        }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Lock size={20} className="mr-2" />
            <h3 className="text-base font-medium">Authentification</h3>
          </div>
          <div className="flex items-center">
            {isComplete ? (
              <CheckCircle size={20} className="mr-2" />
            ) : (
              <Circle size={20} className="mr-2 opacity-50" />
            )}
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <div className="p-2">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 md:gap-4 w-full">
            <FormField
              id="motDePasse"
              label="Mot de passe"
              placeholder="Entrez votre mot de passe"
              type="password"
              showPasswordStrength
              icon={Lock}
              register={register}
              required
              error={errors.motDePasse}
              validation={{
                required: "Le mot de passe est requis",
                pattern: {
                  value: /^(?=.*[A-Z])(?=.*\d).{8,}$/,
                  message: "Minimum 8 caractères, 1 majuscule, 1 chiffre",
                },
              }}
            />
            <FormField
              id="confirmMotDePasse"
              label="Confirmer le mot de passe"
              placeholder="Confirmez votre mot de passe"
              type="password"
              icon={Lock}
              register={register}
              required
              error={errors.confirmMotDePasse}
              validation={{
                required: "La confirmation du mot de passe est requise",
                validate: (value) =>
                  value === watch("motDePasse") ||
                  "Les mots de passe ne correspondent pas",
              }}
            />
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default memo(AuthenticationSection);
