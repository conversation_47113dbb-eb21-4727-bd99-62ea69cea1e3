import React, { useEffect } from "react";
import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import { Stethoscope, CheckCircle, Circle, ChevronDown } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import {
  useSectionProgress,
  useSpecialitiesSection,
} from "@/presentation/hooks/sections";
import { useFormContext } from "react-hook-form";
import useSpecialitiesLists from "@/presentation/hooks/use-specialities-lists";
import { ListeSpecialites } from "@/domain/models";
import { SpecialitiesSelect } from "./components";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const SpecialitiesSection = () => {
  const { isExpanded, handleAccordionChange } = useSpecialitiesSection();

  const {
    watch,
    formState: { errors },
    register,
    setValue,
  } = useFormContext<CabinetMedicalFormDTO>();

  const {
    getSpecialitiesList,
    listes: rawSpecialities,
    loading,
  } = useSpecialitiesLists();

  // Trier les spécialités par ordre alphabétique
  const specialities = [...rawSpecialities].sort((a, b) =>
    a.nom_specialite.localeCompare(b.nom_specialite)
  );

  const [selectedSpecialities, setSelectedSpecialities] = React.useState<
    ListeSpecialites[]
  >([]);

  // Charger les spécialités au chargement du composant
  useEffect(() => {
    getSpecialitiesList();
  }, [getSpecialitiesList]);

  const handleSpecialitiesChange = (newValue: ListeSpecialites[]) => {
    setSelectedSpecialities(newValue);
    setValue("specialities", newValue);
  };

  const sectionComplete = selectedSpecialities.length > 0;

  const { isComplete } = useSectionProgress("panel6", sectionComplete);

  return (
    <Accordion
      expanded={isExpanded}
      onChange={handleAccordionChange}
      sx={{ width: "100%", mb: 1 }}
    >
      <AccordionSummary
        expandIcon={<ChevronDown />}
        aria-controls="panel6-content"
        id="panel6-header"
        sx={{
          backgroundColor: PRIMARY,
          color: "white",
          "& .MuiAccordionSummary-expandIconWrapper": {
            color: "white",
          },
        }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Stethoscope size={20} className="mr-2" />
            <h3 className="text-base font-medium">Spécialités</h3>
          </div>
          <div className="flex items-center">
            {isComplete ? (
              <CheckCircle size={20} className="mr-2" />
            ) : (
              <Circle size={20} className="mr-2 opacity-50" />
            )}
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <div className="p-2">
          <div className="flex flex-col gap-2 w-full">
            <p className="text-sm text-gray-600">
              Sélectionnez vos spécialités médicales. Vous devez sélectionner au
              moins une spécialité.
            </p>

            <SpecialitiesSelect
              specialitiesList={specialities}
              selectedSpecialities={selectedSpecialities}
              handleSpecialitiesChange={handleSpecialitiesChange}
              register={register}
              error={errors.specialities}
            />
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default SpecialitiesSection;
