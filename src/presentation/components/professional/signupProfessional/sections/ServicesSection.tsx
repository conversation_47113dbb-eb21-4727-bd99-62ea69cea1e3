import { memo, useState } from "react";
import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import {
  Stethoscope,
  CheckCircle,
  Circle,
  ChevronDown,
  Plus,
  X,
} from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { professionnels_types_consultation_enum } from "@/domain/models/enums";
import {
  useServicesSection,
  PaymentMethod,
  useSectionProgress,
} from "@/presentation/hooks/sections";
import { useFormContext } from "react-hook-form";
import { ListeAssurances } from "@/domain/models";
import { PAYMENT_METHODS } from "@/shared/constants/cabinetMedicalConfig";
import { InsuranceSelect } from "./components";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const ServicesSection = () => {
  const { isExpanded, handleAccordionChange, insurances } =
    useServicesSection();

  const {
    formState: { errors },
    watch,
    register,
    setValue,
  } = useFormContext<CabinetMedicalFormDTO>();

  // État local pour les dropdowns
  const [paymentDropdownOpen, setPaymentDropdownOpen] = useState(false);

  // Fonctions pour gérer les changements
  const handlePaymentMethodsChange = (methods: PaymentMethod[]) => {
    setValue("paymentMethods", methods);
  };

  const handleInsurancesChange = (insurances: ListeAssurances[]) => {
    setValue("insurances", insurances);
  };

  const sectionComplete =
    !!watch("typeConsultation") &&
    !!watch("nouveauPatientAcceptes") &&
    !!watch("paymentMethods");

  const { isComplete } = useSectionProgress("panel7", sectionComplete);

  return (
    <Accordion
      expanded={isExpanded}
      onChange={handleAccordionChange}
      sx={{ width: "100%", mb: 1 }}
    >
      <AccordionSummary
        expandIcon={<ChevronDown />}
        aria-controls="panel7-content"
        id="panel7-header"
        sx={{
          backgroundColor: PRIMARY,
          color: "white",
          "& .MuiAccordionSummary-expandIconWrapper": {
            color: "white",
          },
        }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Stethoscope size={20} className="mr-2" />
            <h3 className="text-base font-medium">Services proposés</h3>
          </div>
          <div className="flex items-center">
            {isComplete ? (
              <CheckCircle size={20} className="mr-2" />
            ) : (
              <Circle size={20} className="mr-2 opacity-50" />
            )}
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <div className="flex flex-col gap-2 w-full p-2">
          <p className="text-sm text-gray-600">
            Indiquez les types de consultations que vous proposez et si vous
            acceptez de nouveaux patients.
          </p>

          {/* Type de consultation */}
          <div className="mt-4">
            <fieldset
              className={`border-0 p-0 ${errors.typeConsultation ? "text-red-500" : ""}`}
            >
              <legend className="text-sm font-medium mb-2">
                Type de consultation*
              </legend>
              <div className="space-y-2">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="typeConsultation"
                    value={professionnels_types_consultation_enum.EN_CABINET}
                    {...register("typeConsultation")}
                    className="h-4 w-4 text-meddoc-primary focus:ring-meddoc-primary"
                  />
                  <span className="text-sm">En cabinet uniquement</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="typeConsultation"
                    value={professionnels_types_consultation_enum.A_DOMICILE}
                    {...register("typeConsultation")}
                    className="h-4 w-4 text-meddoc-primary focus:ring-meddoc-primary"
                  />
                  <span className="text-sm">À domicile uniquement</span>
                </label>
              </div>
              {errors.typeConsultation && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.typeConsultation.message}
                </p>
              )}
            </fieldset>
          </div>

          {/* Nouveaux patients */}
          <div className="mt-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                {...register("nouveauPatientAcceptes")}
                className="h-4 w-4 text-meddoc-primary focus:ring-meddoc-primary rounded"
              />
              <span className="text-sm">J'accepte de nouveaux patients</span>
            </label>
          </div>

          {/* Modes de paiement */}
          <div className="mt-4">
            <fieldset
              className={`border-0 p-0 ${errors.paymentMethods ? "text-red-500" : ""}`}
            >
              <legend className="text-sm font-medium mb-2">
                Modes de paiement acceptés*
              </legend>
              <div className="relative">
                <div
                  className={`w-full h-12 px-4 py-2 rounded-lg border ${
                    errors.paymentMethods ? "border-red-500" : "border-gray-200"
                  } text-gray-700 flex items-center justify-between cursor-pointer`}
                  onClick={() => setPaymentDropdownOpen(!paymentDropdownOpen)}
                >
                  <span className="text-sm">
                    {watch("paymentMethods")?.length > 0
                      ? `${watch("paymentMethods")?.length} mode(s) sélectionné(s)`
                      : "Sélectionnez les modes de paiement"}
                  </span>
                  <ChevronDown size={16} className="text-gray-400" />
                </div>

                {paymentDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {PAYMENT_METHODS.map((method) => (
                      <div
                        key={method.id}
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                        onClick={() => {
                          const paymentMethods = watch("paymentMethods") || [];
                          const isSelected = paymentMethods.some(
                            (m) => m.id === method.id
                          );
                          if (isSelected) {
                            handlePaymentMethodsChange(
                              paymentMethods.filter((m) => m.id !== method.id)
                            );
                          } else {
                            handlePaymentMethodsChange([
                              ...(paymentMethods as PaymentMethod[]),
                              method,
                            ]);
                          }
                        }}
                      >
                        <Plus size={16} className="text-meddoc-primary mr-2" />
                        {method.name}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              {errors.paymentMethods && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.paymentMethods.message}
                </p>
              )}
            </fieldset>

            {/* Affichage des modes de paiement sélectionnés */}
            {watch("paymentMethods")?.length > 0 && (
              <div className="mt-3">
                <p className="text-sm font-medium mb-2">
                  Modes de paiement sélectionnés:
                </p>
                <div className="flex flex-wrap gap-2">
                  {(watch("paymentMethods") as PaymentMethod[]).map(
                    (method) => (
                      <div
                        key={method.id}
                        className="flex items-center bg-meddoc-primary text-white px-3 py-1 rounded-full text-sm"
                      >
                        <span>{method.name}</span>
                        <button
                          type="button"
                          className="ml-2 focus:outline-none"
                          onClick={() => {
                            handlePaymentMethodsChange(
                              (
                                watch("paymentMethods") as PaymentMethod[]
                              ).filter((m) => m.id !== method.id)
                            );
                          }}
                        >
                          <X size={14} />
                        </button>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Assurances */}
          <div className="mt-4">
            <fieldset
              className={`border-0 p-0 ${errors.insurances ? "text-red-500" : ""}`}
            >
              <legend className="text-sm font-medium mb-2">
                Assurances acceptées (optionnel)
              </legend>
              <InsuranceSelect
                insurancesList={insurances}
                selectedInsurances={watch("insurances") || []}
                handleInsurancesChange={handleInsurancesChange}
                register={register}
                error={errors.insurances}
              />
            </fieldset>
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default memo(ServicesSection);
