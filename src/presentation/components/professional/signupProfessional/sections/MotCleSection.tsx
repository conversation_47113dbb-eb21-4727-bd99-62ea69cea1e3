import { useFormContext } from "react-hook-form";
import MotCleSelect from "./components/MotCleSelect";
import useMotCleSelection from "@/presentation/hooks/cabinetMedical/use-mot-cle-selection";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const MotCleSelection = () => {
  const {
    register,
    formState: { errors },
  } = useFormContext<CabinetMedicalFormDTO>();
  const { motClesList, selectedMotCles, handleMotClesChange, isLoading } =
    useMotCleSelection();

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Mots-clés pour la recherche</h3>
      <p className="text-sm text-gray-500">
        Sélectionnez les mots-clés qui permettront aux patients de vous trouver
        plus facilement lors de leurs recherches.
      </p>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Mots-clés
        </label>
        <MotCleSelect
          motClesList={motClesList}
          selectedMotCles={selectedMotCles}
          handleMotClesChange={handleMotClesChange}
          register={register}
          error={errors.motCles}
        />
      </div>
    </div>
  );
};

export default MotCleSelection;
