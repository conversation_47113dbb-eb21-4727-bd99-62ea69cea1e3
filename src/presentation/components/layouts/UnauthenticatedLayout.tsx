import { ReactNode, useLayoutEffect, useState } from "react";
import { Link } from "react-router-dom";
import Footer from "./Footer";
import LogoMEDDoC from "@/presentation/components/layouts/LogoMEDDoC";
import { restoreData } from "@/application/slices/auth/authSlice";
import { useAppSelector } from "@/presentation/hooks/redux";
import { User } from "./User";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { CircleHelpIcon, Menu as MenuIcon } from "lucide-react";
import {
  IconButton,
  Menu,
  MenuItem,
  useTheme,
  useMediaQuery,
} from "@mui/material";

interface LayoutProps {
  children: ReactNode;
}

const UnathenticatedLayout = ({ children }: LayoutProps) => {
  const user = useAppSelector((state) => state.authentification.user);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const theme = useTheme();
  const isMobile = useMediaQuery("(max-width: 650px)");

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  useLayoutEffect(() => {
    restoreData();
  }, []);

  return (
    <div className="min-h-screen ">
      <nav className="sticky w-full top-0 border-b z-50 bg-white backdrop-blur-md shadow-lg">
        <div className="container mx-auto flex h-16 items-center justify-between px-4">
          <Link
            to="/"
            className="text-xl font-semibold text-primary flex items-center"
          >
            <LogoMEDDoC />
          </Link>
          {user?.id ? (
            <User />
          ) : isMobile ? (
            <>
              <IconButton
                onClick={handleMenuOpen}
                size="large"
                edge="end"
                color="inherit"
                aria-label="menu"
              >
                <MenuIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "right",
                }}
                transformOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
              >
                <MenuItem
                  onClick={handleMenuClose}
                  component={Link}
                  to={`/${PublicRoutesNavigation.PROFESSIONAL_ADHESION_REQUEST}`}
                >
                  Je suis professionnel
                </MenuItem>
                <MenuItem
                  onClick={handleMenuClose}
                  component={Link}
                  to={`/${PublicRoutesNavigation.LOGIN_PAGE}`}
                >
                  Connexion
                </MenuItem>
                <MenuItem
                  onClick={handleMenuClose}
                  component={Link}
                  to={`/${PublicRoutesNavigation.REGISTER_PATIENT_PAGE}`}
                >
                  Créer un compte
                </MenuItem>
                <MenuItem
                  onClick={handleMenuClose}
                  component={Link}
                  to={`/${PublicRoutesNavigation.HELP}`}
                >
                  <div className="flex items-center gap-2">
                    <CircleHelpIcon width={18} height={18} />
                    Centre d'aide
                  </div>
                </MenuItem>
              </Menu>
            </>
          ) : (
            <div className="flex items-center gap-4">
              <Link
                to={`/${PublicRoutesNavigation.PROFESSIONAL_ADHESION_REQUEST}`}
                className="text-md bg-transparent text-sm font-semibold text-meddoc-fonce border border-black px-4 py-2 rounded-md hover:bg-meddoc-primary hover:text-white hover:border-transparent"
              >
                Je suis professionnel
              </Link>
              <Link
                to={`/${PublicRoutesNavigation.LOGIN_PAGE}`}
                className="bg-transparent text-sm font-semibold text-meddoc-fonce border border-black px-4 py-2 rounded-md hover:bg-meddoc-primary hover:text-white hover:border-transparent"
              >
                Connexion
              </Link>
              <Link
                to={`/${PublicRoutesNavigation.REGISTER_PATIENT_PAGE}`}
                className="bg-transparent text-sm font-semibold text-meddoc-fonce border border-black px-4 py-2 rounded-md hover:bg-meddoc-primary hover:text-white hover:border-transparent"
              >
                Créer un compte
              </Link>
              <Link
                to={`/${PublicRoutesNavigation.HELP}`}
                className="flex gap-1 items-center text-sm bg-meddoc-primary text-white font-semibold px-4 py-2 rounded-md border-meddoc-primary border hover:bg-transparent hover:text-meddoc-fonce hover:border-black"
              >
                <CircleHelpIcon width={18} height={18} />
                Centre d'aide
              </Link>
            </div>
          )}
        </div>
      </nav>
      <main>{children}</main>
      <Footer />
    </div>
  );
};
export default UnathenticatedLayout;
