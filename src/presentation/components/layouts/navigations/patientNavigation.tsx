import {
  Calendar,
  Handshake,
  Search,
  SquareActivity,
  Users,
  MessageCircle,
  LayoutDashboard,
  LogOutIcon,
} from "lucide-react";

import { Navigation } from "@toolpad/core/AppProvider";
import { PatientRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";

export const PATIENT_NAVIGATION: Navigation = [
  {
    kind: "header",
    title: "Principale",
  },
  {
    segment: PatientRoutesNavigation.DASHBOARD,
    title: "Tableau de bord",
    icon: <LayoutDashboard />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Medecin",
  },
  {
    segment: PatientRoutesNavigation.FIND_DOCTOR,
    title: "Trouver un medecin",
    icon: <Search />,
  },
  {
    segment: PatientRoutesNavigation.APPOINTMENTS,
    title: "Mes rendez-vous",
    icon: <Calendar />,
  },
  {
    segment: PatientRoutesNavigation.MEDICAL_ACTIVITY,
    title: "<PERSON>ivi médical",
    icon: <SquareActivity />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Personnel",
  },
  {
    segment: PatientRoutesNavigation.FAMILIES,
    title: "Mes proches",
    icon: <Users />,
  },
  {
    segment: PatientRoutesNavigation.MESSAGES,
    title: "Messages",
    icon: <MessageCircle />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Services",
  },
  {
    segment: PatientRoutesNavigation.HELP,
    title: "Assistance",
    icon: <Handshake />,
  },
  {
    kind: "header",
    title: "Profil",
  },
  {
    segment: "logout-action",
    title: "Déconnecter",
    icon: <LogOutIcon />,
  },
];
