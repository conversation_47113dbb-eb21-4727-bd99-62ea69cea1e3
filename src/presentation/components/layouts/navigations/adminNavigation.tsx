import { AdminRoutesNavigations } from "@/shared/constants/AppRoutesNavigation";
import { Navigation } from "@toolpad/core/AppProvider";
import {
  ContactIcon,
  Handshake,
  LayoutDashboard,
  Loader,
  Stethoscope,
} from "lucide-react";

export const ADMIN_NAVIGATION: Navigation = [
  {
    kind: "header",
    title: "Principale",
  },
  {
    segment: AdminRoutesNavigations.DASHBOARD,
    title: "Tableau de bord",
    icon: <LayoutDashboard />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Gestion professionnels",
  },
  {
    segment: AdminRoutesNavigations.MANAGE_PROFESSIONALS,
    title: "Comptes professionnels",
    icon: <Stethoscope />,
  },
  {
    segment: AdminRoutesNavigations.MANAGE_ADHESION_REQUESTS,
    title: "Demande d'adhesion",
    icon: <Loader />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Gestion patients",
  },
  {
    segment: AdminRoutesNavigations.MANAGE_PATIENTS,
    title: "Liste des comptes patients",
    icon: <ContactIcon />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Gestion dash",
  },
  {
    segment: AdminRoutesNavigations.DASH,
    title: "Liste des comptes dash",
    icon: <Stethoscope />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Services",
  },
  {
    segment: AdminRoutesNavigations.HELP,
    title: "Assistance",
    icon: <Handshake />,
  },
];
