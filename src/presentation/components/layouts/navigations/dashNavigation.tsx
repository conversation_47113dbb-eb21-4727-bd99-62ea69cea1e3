import { DashRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { Navigation } from "@toolpad/core/AppProvider";
import {
  Calendar,
  Database,
  Handshake,
  LayoutDashboard,
  LogOutIcon,
  Trash2,
  UserCheck,
  Users,
} from "lucide-react";

export const DASH_NAVIGATION: Navigation = [
  {
    kind: "header",
    title: "Menu",
  },
  {
    segment: DashRoutesNavigation.DASHBOARD,
    title: "Tableau de bord",
    icon: <LayoutDashboard />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Agenda",
  },

  {
    segment: DashRoutesNavigation.AGENDA,
    title: "Agenda",
    icon: <Calendar />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Gestion des personnel",
  },
  {
    segment: DashRoutesNavigation.EMPLOYER,
    title: "Employés",
    icon: <Users />,
    children: [
      {
        segment: "actif",
        title: "Actifs",
        icon: <UserCheck style={{ color: "#10b981" }} />, // Vert pour actifs
      },
      {
        segment: "supprimer",
        title: "Supprimés",
        icon: <Trash2 style={{ color: "#f59e0b" }} />, // Orange pour supprimés
      },
    ],
  },
  {
    segment: DashRoutesNavigation.PROCHE_EMPLOYER,
    title: "Liste des proches",
    icon: <Users />, // Vert pour actifs
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Gestion des stocks médicaux",
  },
  {
    segment: DashRoutesNavigation.STOCK,
    title: "Stocks médicaux",
    icon: <Database />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Services",
  },
  {
    segment: DashRoutesNavigation.HELP,
    title: "Assistance",
    icon: <Handshake />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Profil",
  },
  {
    segment: "logout-action",
    title: "Déconnecter",
    icon: <LogOutIcon />,
  },
];
