import * as React from "react";
import { Typography } from "@mui/material";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Stack from "@mui/material/Stack";
import Tooltip from "@mui/material/Tooltip";
import { useNavigate } from "react-router-dom";

// Composant pour l'icône utilisateur
export const VousEtes = ({ userType }) => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNavigate = (to: string) => {
    handleClose();
    navigate(to);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Stack direction="row" alignItems="center">
      <Tooltip title="Vous etes" enterDelay={1000}>
        <button onClick={handleClick} className="button">
          {userType}
        </button>
      </Tooltip>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem
          onClick={() => handleNavigate("/auth/inscription/patient")}
          className="flex items-center gap-2"
        >
          <Typography>Patient</Typography>
        </MenuItem>
        <MenuItem
          onClick={() => handleNavigate("/auth/inscription/professionnel")}
          className="flex items-center gap-2"
        >
          <Typography>Professionel</Typography>
        </MenuItem>
      </Menu>
    </Stack>
  );
};
