import { Link } from "react-router-dom";
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Mail,
  Phone,
  TwitterIcon,
} from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-meddoc-fonce text-white border-t">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 py-12">
          {/* About Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-200">À propos</h3>
            <p className="text-sm text-gray-400">
            MEDDoC, la solution santé à Madagascar pour gérer les rendez-vous, dossiers patients et la visibilité en ligne des professionnels de santé
            </p>
            <div className="flex space-x-4">
              <a href="https://www.facebook.com/MEDDOCHC" className="text-gray-200 hover:text-primary">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="https://x.com/MEDDoCMG" className="text-gray-200 hover:text-primary">
                
                <TwitterIcon className="h-5 w-5" />
              </a>
              <a href="https://www.instagram.com/meddoc.healthcare/" className="text-gray-200 hover:text-primary">
                <Instagram className="h-5 w-5" />
              </a>
              <a  href="https://www.linkedin.com/company/meddochealthcare/" className="text-gray-200 hover:text-primary">
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-200">
              Liens rapides
            </h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/" className="text-gray-400 hover:text-primary">
                  Accueil
                </Link>
              </li>
              <li>
                <Link to="/auth/inscription/patient" className="text-gray-400 hover:text-primary">
                Créer un compte
                </Link>
              </li>
              <li>
                <Link
                  to="/auth/login"
                  className="text-gray-400 hover:text-primary"
                >
                  Accéder à mon espace
                </Link>
              </li>
              <li>
                <Link
                  to="/auth/inscription/adhesion-professionnel"
                  className="text-gray-400 hover:text-primary"
                >
                 Vous êtes professionnel de santé ? 
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-200">Découvrir nos services</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  to="https://www.meddoc.mg/"
                  className="text-gray-400 hover:text-primary"
                >
                 MEDDoC
                </Link>
              </li>
              <li>
                <Link
                  to="https://www.meddoc.mg/services/solutions-digitales-sante"
                  className="text-gray-400 hover:text-primary"
                >
                  Solutions digitales santé
                </Link>
              </li>
              <li>
                <Link to="https://www.meddoc.mg/services/community-management-medical" className="text-gray-400 hover:text-primary">
                Community management médical
                </Link>
              </li>
              <li>
                <Link to="https://www.meddoc.mg/services/formations-sante" className="text-gray-400 hover:text-primary">
                Formations santé
                </Link>
              
              </li>
              <li>
                <Link to="https://www.meddoc.mg/services/consulting-sante-strategie" className="text-gray-400 hover:text-primary">
                Consulting santé et stratégie
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-200">Contact</h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center space-x-2 text-gray-400">
                <Mail className="h-5 w-5 text-gray-200" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-center space-x-2 text-gray-400">
                <Phone className="h-5 w-5 text-gray-200" />
                <span>+(261) 38 96 555 44</span>
              </li>
              <li className="flex items-center space-x-2 text-gray-400">
                <Phone className="h-5 w-5 text-gray-200 opacity-0" />
                <span>+(261) 32 65 031 58</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-200 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-gray-400">
              © {currentYear} PrisedeRDV. Tous droits réservés.
            </p>
            <div className="flex space-x-6 text-sm">
              <Link to="/privacy" className="text-gray-400 hover:text-primary">
                Politique de confidentialité
              </Link>
              <Link to="/terms" className="text-gray-400 hover:text-primary">
                Conditions d'utilisation
              </Link>
              <Link to="/legal" className="text-gray-400 hover:text-primary">
                Mentions légales
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
