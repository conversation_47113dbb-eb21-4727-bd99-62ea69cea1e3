/**
 * Configuration moderne du Dashboard Patient
 * 
 * Configuration centralisée et typée pour tous les aspects
 * du dashboard patient avec une approche moderne et maintenable.
 */

import { 
  ThemeConfig, 
  ResponsiveConfig, 
  AccessibilityConfig, 
  PerformanceConfig 
} from '@/presentation/types/dashboard.types';

/**
 * Configuration du thème par défaut
 */
export const DEFAULT_THEME_CONFIG: ThemeConfig = {
  isDarkMode: false,
  colors: {
    primary: '#4F46E5',
    secondary: '#6B7280',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  fontSizes: {
    small: '0.875rem',
    medium: '1rem',
    large: '1.125rem',
    xlarge: '1.25rem',
  },
  spacing: {
    small: '0.5rem',
    medium: '1rem',
    large: '1.5rem',
    xlarge: '2rem',
  },
} as const;

/**
 * Configuration responsive moderne
 */
export const RESPONSIVE_CONFIG: ResponsiveConfig = {
  mobile: {
    columns: 1,
    spacing: '1rem',
    fontSize: '0.875rem',
  },
  tablet: {
    columns: 2,
    spacing: '1.5rem',
    fontSize: '1rem',
  },
  desktop: {
    columns: 3,
    spacing: '2rem',
    fontSize: '1rem',
  },
} as const;

/**
 * Configuration d'accessibilité
 */
export const ACCESSIBILITY_CONFIG: AccessibilityConfig = {
  screenReaderSupport: true,
  keyboardNavigation: true,
  highContrast: false,
  largeFonts: false,
  reducedMotion: false,
} as const;

/**
 * Configuration de performance
 */
export const PERFORMANCE_CONFIG: PerformanceConfig = {
  autoRefreshInterval: 300000, // 5 minutes
  maxItems: 100,
  enablePagination: true,
  enableCaching: true,
  cacheDuration: 600000, // 10 minutes
} as const;

/**
 * Points de rupture pour le design responsive
 */
export const BREAKPOINTS = {
  mobile: '640px',
  tablet: '768px',
  desktop: '1024px',
  wide: '1280px',
} as const;

/**
 * Configuration des animations
 */
export const ANIMATION_CONFIG = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  easing: {
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
  scale: {
    enter: 'scale(0.95)',
    exit: 'scale(1.05)',
  },
} as const;

/**
 * Configuration des notifications
 */
export const NOTIFICATION_CONFIG = {
  duration: {
    success: 4000,
    error: 6000,
    warning: 5000,
    info: 4000,
  },
  position: 'top-right',
  maxVisible: 3,
  showProgress: true,
} as const;

/**
 * Configuration des graphiques
 */
export const CHART_CONFIG = {
  defaultHeight: 320,
  margins: {
    top: 20,
    right: 30,
    bottom: 20,
    left: 20,
  },
  colors: {
    primary: DEFAULT_THEME_CONFIG.colors.primary,
    secondary: DEFAULT_THEME_CONFIG.colors.secondary,
    success: DEFAULT_THEME_CONFIG.colors.success,
    warning: DEFAULT_THEME_CONFIG.colors.warning,
    error: DEFAULT_THEME_CONFIG.colors.error,
  },
  animation: {
    duration: 300,
    easing: 'ease-out',
  },
} as const;

/**
 * Configuration des métriques de santé
 */
export const HEALTH_METRICS_CONFIG = {
  scoreRanges: {
    excellent: { min: 90, max: 100, color: DEFAULT_THEME_CONFIG.colors.success },
    good: { min: 75, max: 89, color: DEFAULT_THEME_CONFIG.colors.info },
    average: { min: 60, max: 74, color: DEFAULT_THEME_CONFIG.colors.warning },
    concerning: { min: 40, max: 59, color: '#F97316' },
    poor: { min: 0, max: 39, color: DEFAULT_THEME_CONFIG.colors.error },
  },
  updateInterval: 30000, // 30 secondes
  trendPeriods: ['7d', '30d', '90d'],
} as const;

/**
 * Configuration des rendez-vous
 */
export const APPOINTMENT_CONFIG = {
  statusColors: {
    'A_VENIR': DEFAULT_THEME_CONFIG.colors.info,
    'TERMINE': DEFAULT_THEME_CONFIG.colors.success,
    'ANNULE': DEFAULT_THEME_CONFIG.colors.error,
    'REPORTE': DEFAULT_THEME_CONFIG.colors.warning,
  },
  reminderTimes: [
    { value: 15, label: '15 minutes avant' },
    { value: 30, label: '30 minutes avant' },
    { value: 60, label: '1 heure avant' },
    { value: 1440, label: '1 jour avant' },
  ],
  cancellationDeadline: 24, // heures
} as const;

/**
 * Configuration des prescriptions
 */
export const PRESCRIPTION_CONFIG = {
  statusColors: {
    active: DEFAULT_THEME_CONFIG.colors.success,
    expired: DEFAULT_THEME_CONFIG.colors.error,
    completed: DEFAULT_THEME_CONFIG.colors.secondary,
  },
  refillThreshold: 2, // Nombre de renouvellements restants pour alerter
  reminderIntervals: [
    { value: 8, label: 'Toutes les 8 heures' },
    { value: 12, label: 'Toutes les 12 heures' },
    { value: 24, label: 'Une fois par jour' },
  ],
} as const;

/**
 * Configuration des erreurs
 */
export const ERROR_CONFIG = {
  retryAttempts: 3,
  retryDelay: 1000, // ms
  timeoutDuration: 30000, // 30 secondes
  errorCodes: {
    NETWORK_ERROR: 'Problème de connexion réseau',
    TIMEOUT_ERROR: 'Délai d\'attente dépassé',
    VALIDATION_ERROR: 'Données invalides',
    PERMISSION_ERROR: 'Accès non autorisé',
    SERVER_ERROR: 'Erreur serveur',
    UNKNOWN_ERROR: 'Erreur inconnue',
  },
} as const;

/**
 * Configuration de développement
 */
export const DEV_CONFIG = {
  enableDebugMode: process.env.NODE_ENV === 'development',
  showPerformanceMetrics: process.env.NODE_ENV === 'development',
  enableErrorBoundaryDetails: process.env.NODE_ENV === 'development',
  logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'error',
} as const;

/**
 * Configuration complète du dashboard
 */
export const DASHBOARD_CONFIG = {
  theme: DEFAULT_THEME_CONFIG,
  responsive: RESPONSIVE_CONFIG,
  accessibility: ACCESSIBILITY_CONFIG,
  performance: PERFORMANCE_CONFIG,
  breakpoints: BREAKPOINTS,
  animation: ANIMATION_CONFIG,
  notifications: NOTIFICATION_CONFIG,
  charts: CHART_CONFIG,
  healthMetrics: HEALTH_METRICS_CONFIG,
  appointments: APPOINTMENT_CONFIG,
  prescriptions: PRESCRIPTION_CONFIG,
  errors: ERROR_CONFIG,
  development: DEV_CONFIG,
} as const;

/**
 * Type pour la configuration complète
 */
export type DashboardConfig = typeof DASHBOARD_CONFIG;

/**
 * Hook pour accéder à la configuration
 */
export const useDashboardConfig = () => {
  return DASHBOARD_CONFIG;
};

/**
 * Fonction utilitaire pour merger des configurations personnalisées
 */
export const mergeDashboardConfig = (
  customConfig: Partial<DashboardConfig>
): DashboardConfig => {
  return {
    ...DASHBOARD_CONFIG,
    ...customConfig,
  };
};

export default DASHBOARD_CONFIG;
