import { supabase } from "@/infrastructure/supabase/supabase";
import { useEffect, useState } from "react";

const TestDebug = () => {
  const [data, setData] = useState<string>("");

  useEffect(() => {
    const testData = async () => {
      try {
        const professionalId = 16; // ID du professionnel de l'exemple

        // Test 1: Vérifier les mots-clés
        console.log("=== Test mots-clés ===");
        const { data: motCles, error: motClesError } = await supabase
          .from("mot_cles_professionnel")
          .select("*")
          .eq("id_professionnel", professionalId);
        
        console.log("Mots-clés:", motCles);
        console.log("Erreur mots-clés:", motClesError);

        // Test 2: Vérifier les paramètres de disponibilité
        console.log("=== Test disponibilité ===");
        const { data: disponibilite, error: disponibiliteError } = await supabase
          .from("parametre_disponibilite")
          .select("*")
          .eq("id_professionnel", professionalId);
        
        console.log("Disponibilité:", disponibilite);
        console.log("Erreur disponibilité:", disponibiliteError);

        // Test 3: Vérifier les contacts via utilisateur
        console.log("=== Test contacts ===");
        const { data: contacts, error: contactsError } = await supabase
          .from("contact")
          .select("*")
          .eq("utilisateur_id", 77); // utilisateur_id du professionnel
        
        console.log("Contacts:", contacts);
        console.log("Erreur contacts:", contactsError);

        // Test 4: Vérifier les horaires hebdomadaires
        console.log("=== Test horaires hebdomadaires ===");
        const { data: horaires, error: horairesError } = await supabase
          .from("horaire_hebdomadaire")
          .select(`
            *,
            creneau_horaire(*)
          `);
        
        console.log("Horaires hebdomadaires:", horaires);
        console.log("Erreur horaires:", horairesError);

        // Test 5: Requête complète simplifiée
        console.log("=== Test requête simplifiée ===");
        const { data: professionnel, error: professionnelError } = await supabase
          .from("professionnels")
          .select(`
            *,
            mot_cles_professionnel:mot_cles_professionnel(
              id,
              symptome,
              id_professionnel
            )
          `)
          .eq("id", professionalId);
        
        console.log("Professionnel avec mots-clés:", professionnel);
        console.log("Erreur professionnel:", professionnelError);

        setData(JSON.stringify({
          motCles: motCles?.length || 0,
          disponibilite: disponibilite?.length || 0,
          contacts: contacts?.length || 0,
          horaires: horaires?.length || 0,
          professionnel: professionnel?.[0] || null
        }, null, 2));

      } catch (error) {
        console.error("Erreur générale:", error);
        setData("Erreur: " + error.message);
      }
    };

    testData();
  }, []);

  return (
    <div style={{ padding: "20px", fontFamily: "monospace" }}>
      <h2>Debug - Données manquantes</h2>
      <pre style={{ background: "#f5f5f5", padding: "10px", overflow: "auto" }}>
        {data}
      </pre>
    </div>
  );
};

export default TestDebug;
