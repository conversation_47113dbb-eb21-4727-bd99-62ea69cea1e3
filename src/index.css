@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --primary: 200 98% 39%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --primary: 200 98% 39%;
    --primary-foreground: 0 0% 100%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
  }

  /* Amélioration du scroll global */
  html {
    scroll-behavior: smooth;
  }

  /* Styles pour les éléments avec overflow */
  .overflow-auto-custom {
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(203, 213, 225, 0.8) transparent;
  }

  .overflow-hidden-custom {
    overflow: hidden;
  }
}

@layer components {
  .card {
    @apply rounded-xl bg-white shadow-sm transition-all duration-200 hover:shadow-md;
  }

  .input {
    @apply rounded-lg border border-gray-200 px-4 py-2 focus:border-meddoc-primary focus:outline-none focus:ring-2 focus:ring-meddoc-primary/20;
  }

  .button {
    @apply rounded-lg bg-meddoc-primary px-6 py-2 text-white shadow-sm transition-all duration-100 hover:bg-meddoc-primary/90 hover:shadow-md active:scale-95;
  }
}

@layer utilities {
  .animation-delay-500 {
    animation-delay: 500ms;
  }
  .animation-delay-1000 {
    animation-delay: 1000ms;
  }
  .animation-delay-2000 {
    animation-delay: 2000ms;
  }
}
