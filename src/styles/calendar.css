.custom-calendar .rbc-time-content::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.custom-calendar .rbc-time-content::-webkit-scrollbar-track {
  border-radius: 10px;
}

.custom-calendar .rbc-time-content::-webkit-scrollbar-thumb {
  background: #eee;
  border-radius: 10px;
}

.calendar-container {
  padding: 0;
  height: 100%;
  min-width: 100px;
}

.custom-calendar .rbc-calendar {
  font-family: 'Google Sans', Arial, sans-serif;
  height: 100%;
}

.rbc-button-link div {
  background-color: transparent !important;
  border-bottom: none !important;
}

.rbc-header div {
  background-color: transparent !important;
  border-bottom: none !important;
}

.custom-calendar .rbc-toolbar {
  margin-bottom: 0;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e0e0e0;
}

.custom-calendar .rbc-toolbar-label {
  font-size: 22px;
  font-weight: 400;
  color: #3c4043;
}

.custom-calendar .rbc-btn-group {
  display: flex;
  gap: 4px;
}

.custom-calendar .rbc-btn-group button {
  background: none;
  border: 1px solid transparent;
  padding: 8px 16px;
  border-radius: 4px;
  color: #3c4043;
  font-family: 'Google Sans', Arial, sans-serif;
  font-size: 14px;
  cursor: pointer;
}

.custom-calendar .rbc-btn-group button:hover {
  background-color: #f6f6f6;
}

.custom-calendar .rbc-btn-group button.rbc-active {
  background-color: #e8f0fe;
  color: #0EA5E9;
}

.custom-calendar .rbc-header {
  padding: 8px 6px;
  font-weight: 500;
  font-size: 11px;
  color: #70757a;
  text-transform: uppercase;
  border-bottom: 1px solid #e0e0e0;
}

.custom-calendar .rbc-month-view {
  border: none;
}

.custom-calendar .rbc-month-row {
  border-bottom: 1px solid #e0e0e0;
}

.custom-calendar .rbc-day-bg {
  border-left: 1px solid #e0e0e0;
}

.custom-calendar .rbc-date-cell {
  padding: 8px;
  text-align: center;
  font-size: 12px;
  color: #3c4043;
}

.custom-calendar .rbc-off-range-bg {
  background-color: #f8f9fa;
}

.custom-calendar .rbc-off-range {
  color: #70757a;
}

.custom-calendar .rbc-today {
  background-color: #e8f0fe;
}

.custom-calendar .rbc-time-view {
  border: none;
}

.custom-calendar .rbc-time-header {
  border-bottom: 1px solid #e0e0e0;
}

.custom-calendar .rbc-time-content {
  border-top: none;
}

.custom-calendar .rbc-timeslot-group {
  border-bottom: 1px solid #e0e0e0;
}

.custom-calendar .rbc-time-gutter {
  font-size: 10px;
  color: #70757a;
}

.custom-calendar .rbc-current-time-indicator {
  background-color: #ea4335;
  height: 2px;
}

.custom-calendar .rbc-event {
  font-size: 0.70rem;
  transition: all 0.2s ease;
}

.custom-calendar .rbc-allday-cell {
  display: none;
}

.custom-calendar .rbc-button-link {
  font-size: small;
}

.custom-calendar .rbc-header+.rbc-header {
  border-left: 1px solid #fff;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(2px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}