import { PRIMARY } from "@/shared/constants/Color";
import {
  Box,
  Button,
  FormControl,
  RadioGroup,
  FormControlLabel,
  styled,
  Alert,
  Paper,
} from "@mui/material";

// ConsultationStep1 - Design professionnel inspiré du profil //
export const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  marginBottom: theme.spacing(3),
  borderRadius: "16px",
  boxShadow:
    "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
  border: "1px solid rgba(229, 231, 235, 0.8)",
  background: "linear-gradient(135deg, #ffffff 0%, #fafafa 100%)",
  position: "relative",
  overflow: "hidden",
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: "4px",
    background: "linear-gradient(90deg, #27aae1 0%, #00bfa5 100%)",
  },
  "&::after": {
    content: '""',
    position: "absolute",
    top: "-50px",
    right: "-50px",
    width: "100px",
    height: "100px",
    borderRadius: "50%",
    background:
      "linear-gradient(135deg, rgba(39, 170, 225, 0.05) 0%, rgba(0, 191, 165, 0.05) 100%)",
    pointerEvents: "none",
  },
}));

export const StyledFormControl = styled(FormControl)(({ theme }) => ({
  marginTop: theme.spacing(3),
  width: "100%",
  "& .MuiOutlinedInput-root": {
    borderRadius: "12px",
    backgroundColor: "#f8fafc",
    border: "1px solid #e2e8f0",
    "&:hover": {
      borderColor: "#27aae1",
    },
    "&.Mui-focused": {
      borderColor: "#27aae1",
      boxShadow: "0 0 0 3px rgba(39, 170, 225, 0.1)",
    },
  },
}));

export const StyledRadioGroup = styled(RadioGroup)(({ theme }) => ({
  marginTop: theme.spacing(3),
  gap: theme.spacing(2),
  "& .MuiFormControlLabel-root": {
    margin: 0,
    padding: 0,
  },
}));

export const ConsultationTypeButton = styled(FormControlLabel)(({ theme }) => ({
  width: "100%",
  margin: 0,
  "& .MuiRadio-root": {
    display: "none",
  },
  "& .MuiTypography-root": {
    width: "100%",
    padding: "16px 20px",
    border: "2px solid #e5e7eb",
    borderRadius: "12px",
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-start",
    gap: "12px",
    backgroundColor: "#ffffff",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    cursor: "pointer",
    position: "relative",
    overflow: "hidden",
    "&:hover": {
      backgroundColor: "#f8fafc",
      borderColor: "#27aae1",
      transform: "translateY(-2px)",
      boxShadow: "0 10px 25px -5px rgba(39, 170, 225, 0.1)",
    },
    "&::before": {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0,
      width: "4px",
      height: "100%",
      backgroundColor: "transparent",
      transition: "background-color 0.3s ease",
    },
  },
  "&.Mui-checked .MuiTypography-root": {
    backgroundColor: "rgba(39, 170, 225, 0.08)",
    borderColor: "#27aae1",
    borderWidth: "3px",
    color: "#1e40af",
    fontWeight: 600,
    boxShadow:
      "0 0 0 1px rgba(39, 170, 225, 0.2), 0 4px 12px rgba(39, 170, 225, 0.15)",
    transform: "translateY(-1px)",
    "&::before": {
      backgroundColor: "#27aae1",
      width: "6px",
    },
  },
}));

export const StyledBox = styled(Box)(({ theme }) => ({
  maxWidth: 900,
  margin: "0 auto",
  padding: theme.spacing(4),
  backgroundColor: "#ffffff",
  borderRadius: "20px",
  boxShadow:
    "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
  border: "1px solid rgba(229, 231, 235, 0.6)",
  position: "relative",
  overflow: "hidden",
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: "3px",
    background: "linear-gradient(90deg, #27aae1 0%, #00bfa5 100%)",
  },
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  background: "linear-gradient(135deg, #27aae1 0%, #00bfa5 100%)",
  "& .MuiSvgIcon-root": {
    fontSize: "1.3rem",
    marginRight: theme.spacing(1),
  },
  textTransform: "none",
  color: "white",
  padding: "12px 24px",
  borderRadius: "12px",
  fontSize: "1rem",
  fontWeight: 600,
  boxShadow: "0 4px 14px 0 rgba(39, 170, 225, 0.3)",
  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  "&:hover": {
    background: "linear-gradient(135deg, #1e88e5 0%, #00acc1 100%)",
    transform: "translateY(-2px)",
    boxShadow: "0 8px 25px 0 rgba(39, 170, 225, 0.4)",
  },
  "&:active": {
    transform: "translateY(0)",
  },
}));

export default StyledButton;

// ConsultationStep2 - Design professionnel //
export const WarningBox2 = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: "rgba(255, 243, 205, 0.8)",
  border: "2px solid rgba(255, 193, 7, 0.3)",
  borderRadius: "16px",
  marginBottom: theme.spacing(4),
  position: "relative",
  overflow: "hidden",
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    width: "4px",
    height: "100%",
    backgroundColor: "#ffc107",
  },
  "& .MuiTypography-root": {
    color: "#856404",
    fontWeight: 500,
    lineHeight: 1.6,
  },
}));

// ConsultationStep3 - Design moderne //
export const StepContainer = styled(Box)(({ theme }) => ({
  background: "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
  borderRadius: "20px",
  padding: theme.spacing(4),
  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1)",
  border: "1px solid rgba(229, 231, 235, 0.6)",
  position: "relative",
  overflow: "hidden",
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: "3px",
    background: "linear-gradient(90deg, #27aae1 0%, #00bfa5 100%)",
  },
}));

// ConsultationStep4 - Design élégant //
export const WarningBox = styled(Alert)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  borderRadius: "16px",
  padding: theme.spacing(3),
  border: "1px solid rgba(229, 231, 235, 0.6)",
  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
  "& .MuiAlert-message": {
    width: "100%",
    fontSize: "1rem",
    lineHeight: 1.6,
  },
  "& .MuiAlert-icon": {
    fontSize: "1.5rem",
  },
}));

export const FormSection = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(4),
  padding: theme.spacing(3),
  backgroundColor: "#ffffff",
  borderRadius: "16px",
  border: "1px solid rgba(229, 231, 235, 0.6)",
  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.05)",
}));

export const EditButton = styled(Button)(({ theme }) => ({
  minWidth: "auto",
  padding: theme.spacing(1, 2),
  marginLeft: theme.spacing(2),
  color: "#27aae1",
  backgroundColor: "rgba(39, 170, 225, 0.1)",
  borderRadius: "10px",
  border: "1px solid rgba(39, 170, 225, 0.2)",
  transition: "all 0.3s ease",
  "& .MuiSvgIcon-root": {
    fontSize: "1.2rem",
  },
  "&:hover": {
    backgroundColor: "rgba(39, 170, 225, 0.15)",
    borderColor: "#27aae1",
    transform: "translateY(-1px)",
  },
}));

// ConsultationStep5 - Design premium //
export const StyledBox5 = styled(Box)(({ theme }) => ({
  maxWidth: 1200,
  margin: "0 auto",
  padding: theme.spacing(4),
  background: "linear-gradient(135deg, #f8fafc 0%, #ffffff 100%)",
  minHeight: "60vh",
}));

export const ConfirmationBox = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  marginBottom: theme.spacing(4),
  textAlign: "center",
  borderRadius: "20px",
  border: "1px solid rgba(229, 231, 235, 0.6)",
  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1)",
  background: "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
  position: "relative",
  overflow: "hidden",
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: "4px",
    background: "linear-gradient(90deg, #10b981 0%, #059669 100%)",
  },
  "&::after": {
    content: '""',
    position: "absolute",
    top: "-50px",
    right: "-50px",
    width: "100px",
    height: "100px",
    borderRadius: "50%",
    background:
      "linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%)",
    pointerEvents: "none",
  },
}));

export const ActionButton = styled(Button)(({ theme }) => ({
  textTransform: "none",
  borderRadius: "12px",
  padding: theme.spacing(1),
  background: "linear-gradient(135deg, #27aae1 0%, #00bfa5 100%)",
  color: "white",
  fontWeight: 600,
  boxShadow: "0 4px 14px 0 rgba(39, 170, 225, 0.3)",
  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  "&:hover": {
    background: "linear-gradient(135deg, #1e88e5 0%, #00acc1 100%)",
    transform: "translateY(-2px)",
    boxShadow: "0 8px 25px 0 rgba(39, 170, 225, 0.4)",
  },
  "&.MuiButton-outlined": {
    background: "transparent",
    color: "#27aae1",
    border: "2px solid #27aae1",
    "&:hover": {
      background: "rgba(39, 170, 225, 0.05)",
      borderColor: "#1e88e5",
    },
  },
}));

export const InfoItem = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  marginBottom: theme.spacing(3),
  padding: theme.spacing(2),
  backgroundColor: "rgba(248, 250, 252, 0.8)",
  borderRadius: "12px",
  border: "1px solid rgba(229, 231, 235, 0.6)",
  transition: "all 0.3s ease",
  "&:hover": {
    backgroundColor: "rgba(39, 170, 225, 0.05)",
    borderColor: "rgba(39, 170, 225, 0.2)",
    transform: "translateY(-1px)",
  },
  "& .MuiSvgIcon-root": {
    marginRight: theme.spacing(2),
    color: "#27aae1",
    fontSize: "1.5rem",
    padding: theme.spacing(0.5),
    backgroundColor: "rgba(39, 170, 225, 0.1)",
    borderRadius: "8px",
  },
}));

// Nouveau style pour les titres de section
export const SectionTitle = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: theme.spacing(2),
  marginBottom: theme.spacing(3),
  "& .MuiTypography-root": {
    fontSize: "1.5rem",
    fontWeight: 700,
    color: "#1f2937",
    background: "linear-gradient(135deg, #27aae1 0%, #00bfa5 100%)",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    backgroundClip: "text",
  },
  "& .section-icon": {
    padding: theme.spacing(1.5),
    backgroundColor: "rgba(39, 170, 225, 0.1)",
    borderRadius: "16px",
    color: "#27aae1",
    width: "48px",
    height: "48px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    border: "2px solid rgba(39, 170, 225, 0.2)",
    boxShadow: "0 4px 12px rgba(39, 170, 225, 0.15)",
    "& svg": {
      width: "28px",
      height: "28px",
    },
  },
}));
