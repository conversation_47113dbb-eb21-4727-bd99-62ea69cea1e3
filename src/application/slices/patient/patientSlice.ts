import { Patient, Urgence } from "@/domain/models";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import CreatePatientRepository from "@/infrastructure/repositories/patients/CreatePatientRepository";
import GetPatientsRepository from "@/infrastructure/repositories/patients/GetPatientsRepository";
import EditPatientRepository from "@/infrastructure/repositories/patients/EditPatientRepository";
import DeletePatientRepository from "@/infrastructure/repositories/patients/DeletePatientRepository";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";
import { GetPatientByIdRepository } from "@/infrastructure/repositories/patients";
import { GetPatientByIdUsecase } from "@/domain/usecases/patients";
import { PatientDTO } from "@/domain/DTOS";
import EditPatientUsecase from "@/domain/usecases/patients/EditPatientUsecase";
import DeletePatientUsecase from "@/domain/usecases/patients/DeletePatientUsecase";
import { GetLastSigneVitauxRepository } from "@/infrastructure/repositories/signesVitaux";
import GetPatientsUsecase from "@/domain/usecases/patients/GetPatientsUsecase";
import { GetUrgenceRepository } from "@/infrastructure/repositories/urgence";

interface PatientState {
  patients: Patient[] | null;
  patient: PatientDTO | null;
  loading: boolean;
  error: string | null;
  selectedPatient: Patient | null;
  isAddPatientOpen: boolean;
  isEditPatientOpen: boolean;
  isRemovePatientOpen: boolean;
}

const initialState: PatientState = {
  patients: [],
  patient: null,
  loading: false,
  error: null,
  selectedPatient: null,
  isAddPatientOpen: false,
  isEditPatientOpen: false,
  isRemovePatientOpen: false,
};

export const getPatient = createAsyncThunk(
  "patient/getPatients",
  async (_, { rejectWithValue }) => {
    try {
      const getPatientsRepository = new GetPatientsRepository();
      const getPatientUsecase = new GetPatientsUsecase(getPatientsRepository);
      const patients = await getPatientUsecase.execute();
      return patients;
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const getPatientById = createAsyncThunk(
  "patient/getPatientById",
  async ({ id }: { id: number }, { rejectWithValue }) => {
    try {
      const getPatientRepository = new GetPatientByIdRepository();
      const getPatientUsecase = new GetPatientByIdUsecase(getPatientRepository);
      const result = await getPatientUsecase.execute(id);
      return result;
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

/**
 * @returns Le patient ajouté
 */
export const addPatients = createAsyncThunk(
  "patient/addpatient",
  async (patientInfo: Omit<Patient, "id">, { rejectWithValue }) => {
    try {
      const createPatientRepository = new CreatePatientRepository();
      const response = await createPatientRepository.execute(patientInfo);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

/**
 * @param id - L'ID du patient à mettre à jour
 * @param data - Les données à mettre à jour avec les nouvelles valeurs
 * @returns L'patient mis à jour
 */
export const updatePatient = createAsyncThunk(
  "patient/updatePatient",
  async (
    { id, data }: { id: number; data: Partial<Patient> },
    { rejectWithValue }
  ) => {
    try {
      const editPatientRepository = new EditPatientRepository();
      const editPatientUsecase = new EditPatientUsecase(editPatientRepository);
      return await editPatientUsecase.execute(id, data);
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

/*
 * @param id - L'ID du patientà supprimer
 * @returns L'ID du patient supprimé
 */
export const deletePatient = createAsyncThunk(
  "patient/deletePatient",
  async (id: number, { rejectWithValue }) => {
    try {
      if (!id) {
        throw new Error("aucun patient sélectionné pour suppression.");
      }
      const deletePatientRepository = new DeletePatientRepository();
      const deletePatientUsecase = new DeletePatientUsecase(
        deletePatientRepository
      );
      await deletePatientUsecase.execute(id);
      return id; // Retourne l'ID supprimé
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

const patientSlice = createSlice({
  name: "patient",
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    setSelectedPatient: (state, action: PayloadAction<Patient>) => {
      state.selectedPatient = action.payload;
    },
    getPatientyId: (state, action: PayloadAction<number>) => {
      const matchingPatient = state.patients.find(
        (patient) => patient.id === action.payload
      )[0];
      return matchingPatient;
    },
    setIsAddPatientOpen: (state, action: PayloadAction<boolean>) => {
      state.isAddPatientOpen = action.payload;
    },
    setIsEditPatientOpen: (state, action: PayloadAction<boolean>) => {
      state.isEditPatientOpen = action.payload;
    },

    setIsRemovePatientOpen: (state, action: PayloadAction<boolean>) => {
      state.isRemovePatientOpen = action.payload;
    },

    // Contrôler l'état des modales
    setModalState: (
      state,
      action: PayloadAction<{
        modalType: "add" | "edit" | "remove";
        isOpen: boolean;
      }>
    ) => {
      const { modalType, isOpen } = action.payload;
      if (modalType == "add") {
        state.isAddPatientOpen = isOpen;
      }
      if (modalType == "edit") {
        state.isEditPatientOpen = isOpen;
      }
      if (modalType == "remove") {
        state.isRemovePatientOpen = isOpen;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getPatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getPatient.fulfilled, (state, action) => {
        state.loading = false;
        state.patients = action.payload;
      })
      .addCase(getPatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // get patientById
      .addCase(getPatientById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getPatientById.fulfilled, (state, action) => {
        state.loading = false;
        state.patient = action.payload;
      })
      .addCase(getPatientById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Ajout du patient
      .addCase(addPatients.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addPatients.fulfilled, (state, action) => {
        state.patients.push(action.payload);
        state.loading = false;
      })
      .addCase(addPatients.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Mise à jour du patient
      .addCase(updatePatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePatient.fulfilled, (state, action) => {
        const index = state.patients.findIndex(
          (patient) => patient.id === action.payload.id
        );
        if (index !== -1) state.patients[index] = action.payload;
        state.loading = false;
        state.selectedPatient = null;
      })
      .addCase(updatePatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Suppression du patient
      .addCase(deletePatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deletePatient.fulfilled, (state, action) => {
        state.patients = state.patients.filter(
          (patient) => patient.id !== action.payload
        );
        state.selectedPatient = null;
        state.loading = false;
      })
      .addCase(deletePatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setError,
  setLoading,
  setSelectedPatient,
  getPatientyId,
  setIsAddPatientOpen,
  setIsEditPatientOpen,
  setIsRemovePatientOpen,
  setModalState,
} = patientSlice.actions;
export default patientSlice.reducer;
