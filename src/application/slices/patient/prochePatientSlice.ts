import { Employer, Patient, Proche } from "@/domain/models";
import {
  CreateProchePatientUsecase,
  GetProcheByIdUsecase,
  GetProcheEmployerUsecase,
  GetProchePatientByIdUsecase,
  UpdateProchePatientUsecase,
} from "@/domain/usecases/prochePatient";
import { GetProchePatientUsecase } from "@/domain/usecases/prochePatient/GetProchePatientUsecase";
import {
  CreateProchePatientRepository,
  GetProcheByIdRepository,
  GetProcheEmployerRepository,
  GetProcheRepository,
  UpdateProcheRepository,
} from "@/infrastructure/repositories/prochePatient";
import { GetProchePatientRepository } from "@/infrastructure/repositories/prochePatient/GetProchePatientRepository";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { addNotification } from "../notification/notificationSlice";
import { ProcheSuccessMessage } from "@/domain/usecases/prochePatient/procheMessages/ProcheSuccessMessage";
import { ProcheErrorMessages } from "@/domain/usecases/prochePatient/procheMessages/ProcheErrorMessages";
import CreateUserRepository from "@/infrastructure/repositories/user/CreateUserRepository";
import { CreateUserUsecase, DeleteUserUsecase } from "@/domain/usecases/user";
import { PasswordService } from "@/domain/services/PasswordService";
import DeleteUserRepository from "@/infrastructure/repositories/user/DeleteUserRepository";
import { ProchePatientDTO, ProcheEmployerDTO } from "@/domain/DTOS";
import { GetProcheUsecase } from "@/domain/usecases/prochePatient/GetProcheUsecase";
import { GetEmployerByUserIdRepository } from "@/infrastructure/repositories/employer";
import { GetProcheEmployerByIdUsecase } from "@/domain/usecases/prochePatient/GetProcheEmployerByIdUsecase";
import GetPatientByUserIdRepository from "@/infrastructure/repositories/patients/GetPatientByUserIdRepository";
import { utilisateurs_role_enum } from "@/domain/models/enums";

const createUserRepository = new CreateUserRepository();
const deleteUserRepository = new DeleteUserRepository();
const passwordService = new PasswordService();
const createUserUsecase = new CreateUserUsecase(
  createUserRepository,
  passwordService
);
const createProchePatientRepository = new CreateProchePatientRepository();
const createProchePatientUsecase = new CreateProchePatientUsecase(
  createProchePatientRepository,
  createUserUsecase,
  deleteUserRepository
);

interface prochePatientSlice {
  loading: boolean;
  error: string | null;
  selectedProche: Proche | null;
  selectedProchePatient: ProchePatientDTO | null;
  selectedProcheEmployer: ProcheEmployerDTO | null;
  prochePatient: ProchePatientDTO[] | null;
  procheEmployer: ProcheEmployerDTO[] | null;
  proches: Proche[] | null;
  isModalOpen: boolean;
}

const initialState: prochePatientSlice = {
  error: "",
  selectedProche: null,
  selectedProchePatient: null,
  selectedProcheEmployer: null,
  loading: false,
  prochePatient: null,
  procheEmployer: null,
  proches: null,
  isModalOpen: false,
};

export const createProche = createAsyncThunk(
  "prochePatient/create",
  async (procheData: Omit<Proche, "id">, { rejectWithValue, dispatch }) => {
    try {
      const data = await createProchePatientUsecase.execute(procheData);
      dispatch(
        addNotification({
          message: ProcheSuccessMessage.PROCHE_CREATED,
          type: "success",
        })
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_CREATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const createProcheByProfessional = createAsyncThunk(
  "prochePatient/createByProfessional",
  async (
    {
      procheData,
      patient,
    }: {
      procheData: Omit<Proche, "id">;
      patient?: Patient | null;
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      const data = await createProchePatientUsecase.execute(procheData);
      dispatch(
        addNotification({
          message: ProcheSuccessMessage.PROCHE_CREATED,
          type: "success",
        })
      );
      return { ...data, patient };
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_CREATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const createProcheByDass = createAsyncThunk(
  "prochePatient/createByDass",
  async (
    {
      procheData,
      employees,
    }: {
      procheData: Omit<Proche, "id">;
      employees?: Employer | null;
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      const data = await createProchePatientUsecase.execute(procheData);
      dispatch(
        addNotification({
          message: ProcheSuccessMessage.PROCHE_CREATED,
          type: "success",
        })
      );
      return { ...data, employees };
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_CREATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const updateProche = createAsyncThunk(
  "prochePatient/update",
  async (
    { id, procheData }: { id: number; procheData: Partial<Proche> },
    { rejectWithValue, dispatch }
  ) => {
    try {
      const updateProchePatientRepository = new UpdateProcheRepository();
      const updateProchePatientUsecase = new UpdateProchePatientUsecase(
        updateProchePatientRepository
      );
      const data = await updateProchePatientUsecase.execute(id, procheData);
      dispatch(
        addNotification({
          message: ProcheSuccessMessage.PROCHE_UPDATED,
          type: "success",
        })
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_UPDATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const getProchePatient = createAsyncThunk(
  "prochePatient/getProchePatient",
  async (patientId: number[], { rejectWithValue, dispatch }) => {
    try {
      const getProchePatientRepository = new GetProchePatientRepository();
      const getProchePatientUsecase = new GetProchePatientUsecase(
        getProchePatientRepository
      );
      return await getProchePatientUsecase.execute(patientId);
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_CREATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const getProcheEmployer = createAsyncThunk(
  "prochePatient/getProcheEmployer",
  async (patientId: number[], { rejectWithValue, dispatch }) => {
    try {
      const getProcheEmployerRepository = new GetProcheEmployerRepository();
      const getProcheEmployerUsecase = new GetProcheEmployerUsecase(
        getProcheEmployerRepository
      );
      return await getProcheEmployerUsecase.execute(patientId);
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_CREATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const getProcheById = createAsyncThunk(
  "prochePatient/getProcheById",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const getProcheByIdRepository = new GetProcheByIdRepository();
      const getProcheByIdUsecase = new GetProcheByIdUsecase(
        getProcheByIdRepository
      );
      return await getProcheByIdUsecase.execute(id);
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_CREATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const getProchePatientById = createAsyncThunk(
  "prochePatient/getProchePatientById",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const getProcheByIdRepository = new GetProcheByIdRepository();
      const getPatientByUserIdRepository = new GetPatientByUserIdRepository();
      const getProchePatientByIdUsecase = new GetProchePatientByIdUsecase(
        getProcheByIdRepository,
        getPatientByUserIdRepository
      );
      return await getProchePatientByIdUsecase.execute(id);
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_FETCHED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const getProcheEmployerById = createAsyncThunk(
  "prochePatient/getProcheEmployerById",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const getProcheByIdRepository = new GetProcheByIdRepository();
      const getEmployerByUserIdRepository = new GetEmployerByUserIdRepository();
      const getProcheEmployerByIdUsecase = new GetProcheEmployerByIdUsecase(
        getProcheByIdRepository,
        getEmployerByUserIdRepository
      );
      return await getProcheEmployerByIdUsecase.execute(id);
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_CREATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const getProcheByPatientId = createAsyncThunk(
  "prochePatient/getProcheByPatientId",
  async (patientId: number, { rejectWithValue, dispatch }) => {
    try {
      const getProcheRepository = new GetProcheRepository();
      const getProchePatientUsecase = new GetProcheUsecase(getProcheRepository);
      return await getProchePatientUsecase.execute(patientId);
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_CREATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

export const deleteProcheByPatientId = createAsyncThunk(
  "prochePatient/delete",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const deleteUserRepository = new DeleteUserRepository();
      const deleteUserUsecase = new DeleteUserUsecase(deleteUserRepository);
      const data = await deleteUserUsecase.execute(id);
      dispatch(
        addNotification({
          message: ProcheSuccessMessage.PROCHE_DELETED,
          type: "success",
        })
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: ProcheErrorMessages.PROCHE_DELETED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  }
);

const prochePatientSlice = createSlice({
  name: "prochePatient",
  initialState,
  reducers: {
    setIsPageValid: (state, action: PayloadAction<boolean>) => {
      state.isModalOpen = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createProche.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createProche.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        if (!state.proches) {
          state.proches = [];
        }
        state.proches.push(action.payload);
      })
      .addCase(createProche.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(createProcheByDass.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createProcheByDass.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        if (!state.proches) {
          state.proches = [];
        }
        state.procheEmployer.push(action.payload);
      })
      .addCase(createProcheByDass.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(createProcheByProfessional.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createProcheByProfessional.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        if (!state.proches) {
          state.proches = [];
        }
        state.prochePatient.push(action.payload);
      })
      .addCase(createProcheByProfessional.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getProchePatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProchePatient.fulfilled, (state, action) => {
        state.loading = false;
        state.prochePatient = action.payload;
      })
      .addCase(getProchePatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getProcheEmployer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProcheEmployer.fulfilled, (state, action) => {
        state.loading = false;
        state.procheEmployer = action.payload;
      })
      .addCase(getProcheEmployer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getProcheById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProcheById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedProche = action.payload;
      })
      .addCase(getProcheById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getProchePatientById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProchePatientById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedProchePatient = action.payload;
      })
      .addCase(getProchePatientById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getProcheEmployerById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProcheEmployerById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedProcheEmployer = action.payload;
      })
      .addCase(getProcheEmployerById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getProcheByPatientId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProcheByPatientId.fulfilled, (state, action) => {
        state.loading = false;
        state.proches = action.payload;
      })
      .addCase(getProcheByPatientId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateProche.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProche.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.proches = state.proches?.map((proche) =>
          proche.id === action.payload.id
            ? { ...proche, ...action.payload }
            : proche
        );
        if (state.selectedProcheEmployer?.employees) {
          const { employees, ...reste } = state.selectedProcheEmployer;
          state.selectedProcheEmployer = { ...action.payload, employees };
        } else if (state.selectedProchePatient?.patient) {
          const { patient, ...reste } = state.selectedProchePatient;
          state.selectedProchePatient = { ...action.payload, patient };
        }
      })
      .addCase(updateProche.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(deleteProcheByPatientId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteProcheByPatientId.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.proches = state.proches?.filter(
          (proche) => proche.utilisateur_id !== action.meta.arg
        );
        state.procheEmployer = state.procheEmployer?.filter(
          (proche) => proche.utilisateur_id !== action.meta.arg
        );
        state.prochePatient = state.prochePatient?.filter(
          (proche) => proche.utilisateur_id !== action.meta.arg
        );
      })
      .addCase(deleteProcheByPatientId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setIsPageValid } = prochePatientSlice.actions;

export default prochePatientSlice.reducer;
