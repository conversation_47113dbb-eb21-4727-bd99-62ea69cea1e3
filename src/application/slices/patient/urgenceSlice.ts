import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { Urgence } from "@/domain/models";
import {
  CreateUrgenceRepository,
  DeleteUrgenceRepository,
  GetUrgenceRepository,
  UpdateUrgenceRepository,
} from "@/infrastructure/repositories/urgence";
import { CreateUrgencesUsecase } from "@/domain/usecases/urgence/CreateUrgencesUsecase";
import { GetUrgencesUsecase } from "@/domain/usecases/urgence/GetUrgencesUsecase";
import { UpdateUrgencesUsecase } from "@/domain/usecases/urgence/UpdateUrgenceUsecase";
import { DeleteUrgencesUsecase } from "@/domain/usecases/urgence/DeleteUrgenceUsecase";
import { setContactUrgence } from "@/application/slices/professionnal/professionnelPatientSlice";

interface UrgenceSliceState {
  urgence: Urgence[];
  selectedUrgenceSlice: Urgence | null;
  loading: boolean;
  error: string | null;
}

const initialState: UrgenceSliceState = {
  urgence: [],
  selectedUrgenceSlice: null,
  loading: false,
  error: null,
};

export const createUrgenceSlice = createAsyncThunk(
  "urgence/create",
  async (data: Omit<Urgence, "id">[], { rejectWithValue, dispatch }) => {
    try {
      const deleteUrgenceRepository = new DeleteUrgenceRepository();
      const createUrgenceRepository = new CreateUrgenceRepository();
      const createUrgenceUseCase = new CreateUrgencesUsecase(
        createUrgenceRepository,
        deleteUrgenceRepository
      );
      const result = await createUrgenceUseCase.execute(data);
      dispatch(setContactUrgence(result));
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getAllUrgenceSlices = createAsyncThunk(
  "urgence/getAll",
  async (patientId: number, { rejectWithValue }) => {
    try {
      const getUrgenceRepository = new GetUrgenceRepository();
      const getUrgenceUseCase = new GetUrgencesUsecase(getUrgenceRepository);
      const result = await getUrgenceUseCase.execute(patientId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateUrgenceSlice = createAsyncThunk(
  "urgence/update",
  async (
    { id, data }: { id: number; data: Partial<Urgence> },
    { rejectWithValue }
  ) => {
    try {
      const updateUrgenceRepository = new UpdateUrgenceRepository();
      const updateUrgenceUseCase = new UpdateUrgencesUsecase(
        updateUrgenceRepository
      );
      const result = await updateUrgenceUseCase.execute(id, data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteUrgenceSlice = createAsyncThunk(
  "urgence/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteUrgenceRepository = new DeleteUrgenceRepository();
      const deleteUrgenceUseCase = new DeleteUrgencesUsecase(
        deleteUrgenceRepository
      );
      await deleteUrgenceUseCase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const urgenceSlice = createSlice({
  name: "urgence",
  initialState,
  reducers: {
    setSelectedUrgenceSlice: (state, action) => {
      state.selectedUrgenceSlice = action.payload;
    },
    clearSelectedUrgenceSlice: (state) => {
      state.selectedUrgenceSlice = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createUrgenceSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createUrgenceSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.urgence.push(...action.payload);
      })
      .addCase(createUrgenceSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get All
      .addCase(getAllUrgenceSlices.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllUrgenceSlices.fulfilled, (state, action) => {
        state.loading = false;
        state.urgence = action.payload;
      })
      .addCase(getAllUrgenceSlices.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateUrgenceSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUrgenceSlice.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.urgence.findIndex(
          (am) => am.id === action.payload.id
        );
        if (index !== -1) {
          state.urgence[index] = action.payload;
        }
      })
      .addCase(updateUrgenceSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteUrgenceSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteUrgenceSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.urgence = state.urgence.filter(
          (am) => am.id !== Number(action.payload)
        );
      })
      .addCase(deleteUrgenceSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedUrgenceSlice, clearSelectedUrgenceSlice } =
  urgenceSlice.actions;
export default urgenceSlice.reducer;
