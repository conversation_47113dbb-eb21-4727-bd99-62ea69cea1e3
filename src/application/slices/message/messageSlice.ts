import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { message } from "@/domain/models";
import {
  CreateMessageRepository,
  GetMessageByConversationIdRepository,
  UpdateMessageRepository,
  DeleteMessageRepository,
} from "@/infrastructure/repositories/message";
import {
  CreateMessageUseCase,
  GetMessageByConversationIdUsecase,
  UpdateMessageUseCase,
  DeleteMessageUseCase,
  GetContactMessageUsecase,
} from "@/domain/usecases/message";
import { Contact, MessageDTO } from "@/presentation/types/message.types";
import GetUserByIdRepository from "@/infrastructure/repositories/user/GetUserByIdRepository";
import GetUsersCanMessagingRepository from "@/infrastructure/repositories/user/GetUsersCanMessagingRepository";
import GetUsersCanMessagingByPatientRepository from "@/infrastructure/repositories/user/GetUsersCanMessagingByPatientRepository";
import { utilisateurs_role_enum } from "@/domain/models/enums";

const getContactMessageUsecase = new GetContactMessageUsecase(
  new GetUsersCanMessagingRepository(),
  new GetUsersCanMessagingByPatientRepository()
);

const createMessageRepository = new CreateMessageRepository();
const createMessageUseCase = new CreateMessageUseCase(createMessageRepository);

const getMessageByConversationIdRepository =
  new GetMessageByConversationIdRepository();
const getUserByIdRepository = new GetUserByIdRepository();
const getMessageByConversationIdUsecase = new GetMessageByConversationIdUsecase(
  getMessageByConversationIdRepository,
  getUserByIdRepository
);

const updateMessageRepository = new UpdateMessageRepository();
const updateMessageUseCase = new UpdateMessageUseCase(updateMessageRepository);

const deleteMessageRepository = new DeleteMessageRepository();
const deleteMessageUseCase = new DeleteMessageUseCase(deleteMessageRepository);

interface messageSliceState {
  messages: message[];
  messagesDTO: MessageDTO[];
  contacts: Contact[];
  selectedMessageSlice: message | null;
  loading: boolean;
  error: string | null;
}

const initialState: messageSliceState = {
  messages: [],
  messagesDTO: [],
  contacts: [],
  selectedMessageSlice: null,
  loading: false,
  error: null,
};

export const createMessageSlice = createAsyncThunk(
  "message/create",
  async (data: Omit<message, "id">, { rejectWithValue }) => {
    try {
      const result = await createMessageUseCase.execute(data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getContactMessageSlice = createAsyncThunk(
  "message/getContactMessage",
  async (role: utilisateurs_role_enum, { rejectWithValue }) => {
    try {
      const result = await getContactMessageUsecase.execute(role);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getMessageByConversationIdSlices = createAsyncThunk(
  "message/getByConversationId",
  async (
    { conversationId, userId }: { conversationId: number; userId: number },
    { rejectWithValue }
  ) => {
    try {
      const result = await getMessageByConversationIdUsecase.execute(
        conversationId,
        userId
      );
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateMessageSlice = createAsyncThunk(
  "message/update",
  async (
    { id, data }: { id: number; data: Partial<message> },
    { rejectWithValue }
  ) => {
    try {
      const result = await updateMessageUseCase.execute(id, data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteMessageSlice = createAsyncThunk(
  "message/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      await deleteMessageUseCase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const messageSlice = createSlice({
  name: "message",
  initialState,
  reducers: {
    setSelectedMessageSlice: (state, action) => {
      state.selectedMessageSlice = action.payload;
    },
    clearSelectedMessageSlice: (state) => {
      state.selectedMessageSlice = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createMessageSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createMessageSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.messages.push(action.payload);
      })
      .addCase(createMessageSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get ContactMessage
      .addCase(getContactMessageSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getContactMessageSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.contacts = action.payload;
      })
      .addCase(getContactMessageSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get All
      .addCase(getMessageByConversationIdSlices.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMessageByConversationIdSlices.fulfilled, (state, action) => {
        state.loading = false;
        state.messagesDTO = action.payload;
      })
      .addCase(getMessageByConversationIdSlices.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateMessageSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateMessageSlice.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.messages.findIndex(
          (am) => am.id === action.payload.id
        );
        if (index !== -1) {
          state.messages[index] = action.payload;
        }
      })
      .addCase(updateMessageSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteMessageSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteMessageSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.messages = state.messages.filter(
          (am) => am.id !== Number(action.payload)
        );
      })
      .addCase(deleteMessageSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedMessageSlice, clearSelectedMessageSlice } =
  messageSlice.actions;
export default messageSlice.reducer;
