import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Evenement } from "@/domain/models";
import { CreateEventsUsecase } from "@/domain/usecases/professional/evenement/CreateEvents";
import { DeleteEventUsecase } from "@/domain/usecases/professional/evenement/DeleteEvent";
import { GetEventsByProfessionalIdUsecase } from "@/domain/usecases/professional/evenement/GetEventsByProfessionalId";
import { UpdateEventUsecase } from "@/domain/usecases/professional/evenement/UpdateEvent";
import { addNotification } from "@/application/slices/notification/notificationSlice";
import { EvenementSuccessMessages } from "@/domain/usecases/professional/evenement/evenementMessages/EvenementSuccessMessage";
import { EvenementErrorMessages } from "@/domain/usecases/professional/evenement/evenementMessages/EvenementErrorMessages";
import { DeleteEventByProfessionalIdUsecase } from "@/domain/usecases/professional/evenement/deleteEventByProfessionalId";
import { UpdateEventByProfessionalIdUsecase } from "@/domain/usecases/professional/evenement/UpdateEventByProfessionalId";
import { GenerateRecurringEvents } from "@/domain/services/GenerateRecurringEvents";
import {
  CreateEventRepository,
  DeleteEventByProfessionalIdRepository,
  DeleteEventRepository,
  GetEventsByProfessionalIdRepository,
  GetDistinctEventsByProfessionalIdRepository,
  UpdateEventRepository,
} from "@/infrastructure/repositories/evenement";
import { GetDistinctEventsByProfessionalIdUsecase } from "@/domain/usecases/professional/evenement/GetDistinctEventsByProfessionalIdUsecase";

interface evenementSlice {
  evenement: Evenement[];
  currentEvenement: Evenement | null;
  loading: boolean;
  error: string | null;
  isPopoverOpen: boolean;
}

const initialState: evenementSlice = {
  evenement: [],
  currentEvenement: null,
  loading: false,
  error: null,
  isPopoverOpen: false,
};

const createEventRepository = new CreateEventRepository();
const generateRecurringEvents = new GenerateRecurringEvents();
const createEventsUsecase = new CreateEventsUsecase(
  createEventRepository,
  generateRecurringEvents
);
const deleteEventRepository = new DeleteEventRepository();
const deleteEventUsecase = new DeleteEventUsecase(deleteEventRepository);
const deleteEventByProfessionalIdRepository =
  new DeleteEventByProfessionalIdRepository();
const deleteEventByProfessionalIdUsecase =
  new DeleteEventByProfessionalIdUsecase(deleteEventByProfessionalIdRepository);
const getEventsByProfessionalIdRepository =
  new GetEventsByProfessionalIdRepository();
const getEventsByProfessionalIdUsecase = new GetEventsByProfessionalIdUsecase(
  getEventsByProfessionalIdRepository
);
const getDistinctEventsByProfessionalIdRepository =
  new GetDistinctEventsByProfessionalIdRepository();
const getDistinctEventsByProfessionalIdUsecase =
  new GetDistinctEventsByProfessionalIdUsecase(
    getDistinctEventsByProfessionalIdRepository
  );
const updateEventRepository = new UpdateEventRepository();
const updateEventUsecase = new UpdateEventUsecase(updateEventRepository);
const updateEventByProfessionalIdUsecase =
  new UpdateEventByProfessionalIdUsecase(
    deleteEventByProfessionalIdRepository,
    createEventRepository,
    generateRecurringEvents
  );

export const getEvenement = createAsyncThunk(
  "evenement/getEvenement",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const data = await getEventsByProfessionalIdUsecase.execute(id);
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: EvenementErrorMessages.EVENTS_FETCHED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || "Failed to fetch events");
    }
  }
);

export const getDistinctEvenement = createAsyncThunk(
  "evenement/getDistinctEvenement",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const data = await getDistinctEventsByProfessionalIdUsecase.execute(id);
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: EvenementErrorMessages.EVENTS_FETCHED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || "Failed to fetch events");
    }
  }
);

export const createEvenement = createAsyncThunk(
  "evenement/createEvenement",
  async (evenement: Omit<Evenement, "id">, { rejectWithValue, dispatch }) => {
    try {
      const data = await createEventsUsecase.execute(evenement);
      dispatch(
        addNotification({
          message: EvenementSuccessMessages.EVENT_CREATED,
          type: "success",
        })
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: EvenementErrorMessages.EVENT_CREATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || "Failed to create event");
    }
  }
);

export const updateEvenement = createAsyncThunk(
  "evenement/updateEvenement",
  async (
    { id, evenement }: { id: number; evenement: Partial<Evenement> },
    { rejectWithValue, dispatch }
  ) => {
    try {
      const data = await updateEventUsecase.execute(id, evenement);
      dispatch(
        addNotification({
          message: EvenementSuccessMessages.EVENT_UPDATED,
          type: "success",
        })
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: EvenementErrorMessages.EVENT_UPDATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || "Failed to update event");
    }
  }
);

export const updateEvenementByProfessionalId = createAsyncThunk(
  "evenement/updateEventByProfessionalIdUsecase",
  async (
    { id, evenement }: { id: number; evenement: Omit<Evenement, "id"> },
    { rejectWithValue, dispatch }
  ) => {
    try {
      const data = await updateEventByProfessionalIdUsecase.execute(
        id,
        evenement
      );
      dispatch(
        addNotification({
          message: EvenementSuccessMessages.EVENT_UPDATED,
          type: "success",
        })
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: EvenementErrorMessages.EVENT_UPDATED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || "Failed to update event");
    }
  }
);

export const deleteEvenement = createAsyncThunk(
  "evenement/deleteEvenement",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      await deleteEventUsecase.execute(id);
      dispatch(
        addNotification({
          message: EvenementSuccessMessages.EVENT_DELETED,
          type: "success",
        })
      );
      return id;
    } catch (error) {
      dispatch(
        addNotification({
          message: EvenementErrorMessages.EVENT_DELETED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || "Failed to delete event");
    }
  }
);

export const deleteEvenementByProfessionalId = createAsyncThunk(
  "evenement/deleteEvenementByProfessionalId",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      await deleteEventByProfessionalIdUsecase.execute(id);
      dispatch(
        addNotification({
          message: EvenementSuccessMessages.EVENT_DELETED,
          type: "success",
        })
      );
      return id;
    } catch (error) {
      dispatch(
        addNotification({
          message: EvenementErrorMessages.EVENT_DELETED,
          type: "error",
        })
      );
      return rejectWithValue(error.message || "Failed to delete event");
    }
  }
);

const evenementSlice = createSlice({
  name: "evenement",
  initialState,
  reducers: {
    setIsPopOverOpen(state, action: PayloadAction<boolean>) {
      state.isPopoverOpen = action.payload;
    },
    reset(state) {
      state.currentEvenement = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get Events
      .addCase(getEvenement.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getEvenement.fulfilled, (state, action) => {
        state.loading = false;
        state.evenement = action.payload;
      })
      .addCase(getEvenement.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get Distinct Events
      .addCase(getDistinctEvenement.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDistinctEvenement.fulfilled, (state, action) => {
        state.loading = false;
        state.evenement = action.payload;
      })
      .addCase(getDistinctEvenement.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Create Event
      .addCase(createEvenement.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createEvenement.fulfilled, (state, action) => {
        state.loading = false;
        state.evenement.push(...action.payload);
      })
      .addCase(createEvenement.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update Event
      .addCase(updateEvenement.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateEvenement.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.evenement.findIndex(
          (e) => e.id === action.payload.id
        );
        if (index !== -1) {
          state.evenement[index] = action.payload;
        }
      })
      .addCase(updateEvenement.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update Event By Professional Id
      .addCase(updateEvenementByProfessionalId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateEvenementByProfessionalId.fulfilled, (state, action) => {
        state.loading = false;
        state.evenement = [];
        state.evenement.push(...action.payload);
      })
      .addCase(updateEvenementByProfessionalId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete Event
      .addCase(deleteEvenement.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteEvenement.fulfilled, (state, action) => {
        state.loading = false;
        state.evenement = state.evenement.filter(
          (e) => e.id !== action.payload
        );
      })
      .addCase(deleteEvenement.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete Event By Professional Id
      .addCase(deleteEvenementByProfessionalId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteEvenementByProfessionalId.fulfilled, (state, action) => {
        state.loading = false;
        state.evenement = state.evenement.filter(
          (e) => e.id_professionnel !== action.payload
        );
      })
      .addCase(deleteEvenementByProfessionalId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setIsPopOverOpen, reset } = evenementSlice.actions;

export default evenementSlice.reducer;
