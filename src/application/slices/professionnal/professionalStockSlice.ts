import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import { Stocks } from "@/domain/models/Stocks.ts";
import CreateFournisseurUsecase from "@/domain/usecases/fournisseurs/CreateFournisseurUsecase.ts";
import DeleteFournisseurUsecase from "@/domain/usecases/fournisseurs/DeleteFournisseurUsecase.ts";
import GetFournisseursByUserIdUsecase from "@/domain/usecases/fournisseurs/GetFournisseursByUserIdUsecase.ts";
import UpdateFournisseurUsecase from "@/domain/usecases/fournisseurs/UpdateFournisseurUsecase.ts";
import CreateStocksUsecase from "@/domain/usecases/stocks/CreateStocksUsecase.ts";
import DeleteStocksUsecase from "@/domain/usecases/stocks/DeleteStocksUsecase.ts";
import GetStocksUsecase from "@/domain/usecases/stocks/GetStocksUsecase.ts";
import UpdateStocksUsecase from "@/domain/usecases/stocks/UpdateStocksUsecase.ts";
import CreateFournisseurRepository from "@/infrastructure/repositories/fournisseurs/CreateFournisseurRepository.ts";
import DeleteFournisseurRepository from "@/infrastructure/repositories/fournisseurs/DeleteFournisseurRepository.ts";
import GetFournisseursByUserIdRepository from "@/infrastructure/repositories/fournisseurs/GetFournisseursByUserIdRepository.ts";
import UpdateFournisseurRepository from "@/infrastructure/repositories/fournisseurs/UpdateFournisseurRepository.ts";
import CreateStocksRepository from "@/infrastructure/repositories/stocks/CreateStocksRepository.ts";
import DeleteStocksRepository from "@/infrastructure/repositories/stocks/DeleteStocksRepository.ts";
import GetStocksRepository from "@/infrastructure/repositories/stocks/GetStocksRepository.ts";
import GetStockByIdRepository from "@/infrastructure/repositories/stocks/GetStockByIdRepository.ts";
import UpdateStocksRepository from "@/infrastructure/repositories/stocks/UpdateStocksRepository.ts";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import GetStockCategoriesRepository from "@/infrastructure/repositories/categories/GetStockCategoriesRepository.ts";
import GetStockCategoriesUsecase from "@/domain/usecases/categories/GetStockCategoriesUsecase.ts";
import { Categories } from "@/domain/models/Categories.ts";
import {
  CreateStockDTO,
  EntreeStockRowDTO,
  LotWithStockData,
  LowStockCardDTO,
  SortieStockDTO,
  SortieStockRowDTO,
  StockDTO,
  StockNearPeremptionDateCardDTO,
  StockStatistics,
} from "@/domain/DTOS/StockDTO.ts";
import GetEntreesStocksRepository from "@/infrastructure/repositories/entreesStocks/GetEntreesStocksRepository.ts";
import GetEntreesStocksUsecase from "@/domain/usecases/entreesStocks/GetEntreesStocksUsecase.ts";
import CreateEntreesStocksRepository from "@/infrastructure/repositories/entreesStocks/CreateEntreesStocksRepository.ts";
import CreateEntreesStocksUsecase from "@/domain/usecases/entreesStocks/CreateEntreesStocksUsecase.ts";
import { EntreesStocks } from "@/domain/models/EntreesStocks.ts";
import CreateLotRepository from "@/infrastructure/repositories/lots/CreateLotRepository.ts";
import CreateLotUsecase from "@/domain/usecases/lots/CreateLotUsecase.ts";
import { RootState } from "@/store/index.ts";
import CreateSortiesStocksRepository from "@/infrastructure/repositories/sortiesStocks/CreateSortiesStocksRepository.ts";
import CreateSortiesStocksUsecase from "@/domain/usecases/sortiesStocks/CreateSortiesStocksUsecase.ts";
import GetLotsByEntreeIdRepository from "@/infrastructure/repositories/lots/GetLotsByEntreeIdRepository.ts";
import GetLotsByEntreeIdUsecase from "@/domain/usecases/lots/GetLotsByEntreeIdUsecase.ts";
import GetEntreeStockByStockIdRepository from "@/infrastructure/repositories/entreesStocks/GetEntreesStocksByStockIdRepository.ts";
import GetEntreeStocksByStockIdUsecase from "@/domain/usecases/entreesStocks/GetEntreeStocksByStockIdUsecase.ts";
import UpdateLotUsecase from "@/domain/usecases/lots/UpdateLotUsecase.ts";
import UpdateLotRepository from "@/infrastructure/repositories/lots/UpdateLotRepository.ts";
import GetSortiesStocksRepository from "@/infrastructure/repositories/sortiesStocks/GetSortiesStocksRepository.ts";
import GetSortiesStocksUsecase from "@/domain/usecases/sortiesStocks/GetSortiesStocksUsecase.ts";
import GetLotsByIdsUsecase from "@/domain/usecases/lots/GetLotsByIdsUsecase.ts";
import GetLotsByIdsRepository from "@/infrastructure/repositories/lots/GetLotsByIdsRepository.ts";
import GetLotByIdRepository from "@/infrastructure/repositories/lots/GetLotByIdRepository.ts";
import GetLotByIdUsecase from "@/domain/usecases/lots/GetLotByIdUsecase.ts";
import { Lots } from "@/domain/models/Lots.ts";
import GetLotsWithStockDataRepository from "@/infrastructure/repositories/lots/GetLotsWithStockDataRepository.ts";
import GetLotsWithStockDataUsecase from "@/domain/usecases/lots/GetLotsWithStockDataUsecase.ts";

interface ProfessionalStockSlice {
  fournisseurs: Fournisseurs[];
  produits: Stocks[];
  categories: Categories[];
  entrees: EntreeStockRowDTO[];
  sorties: SortieStockRowDTO[];
  lots: LotWithStockData[];
  stocksNearPeremption?: StockNearPeremptionDateCardDTO[];
  lowStockAlert?: LowStockCardDTO[];
  expiredStocks?: LotWithStockData[];
  dashboardData: StockStatistics | null;
  loading: boolean;
  error: string | null;
}

const initialState: ProfessionalStockSlice = {
  fournisseurs: [],
  produits: [],
  categories: [],
  entrees: [],
  sorties: [],
  lots: [],
  stocksNearPeremption: [],
  lowStockAlert: [],
  dashboardData: null,
  loading: false,
  error: null,
};

// Recuperation de la liste des categories
export const getCategories = createAsyncThunk(
  "professionalStock/getCategories",
  async (_, { rejectWithValue }) => {
    try {
      const getStockCategoriesRepository = new GetStockCategoriesRepository();
      const getStockCategoriesUsecase = new GetStockCategoriesUsecase(
        getStockCategoriesRepository
      );
      return await getStockCategoriesUsecase.execute();
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const createFournisseur = createAsyncThunk(
  "professionalStock/createFournisseur",
  async (fournisseur: Omit<Fournisseurs, "id">, { rejectWithValue }) => {
    try {
      const createFournisseurRepository = new CreateFournisseurRepository();
      const createFournisseurUsecase = new CreateFournisseurUsecase(
        createFournisseurRepository
      );
      return await createFournisseurUsecase.execute(fournisseur);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateFournisseur = createAsyncThunk(
  "professionalStock/updateFournisseur",
  async (
    { id, fournisseur }: { id: number; fournisseur: Omit<Fournisseurs, "id"> },
    { rejectWithValue }
  ) => {
    try {
      const updateFournisseurRepository = new UpdateFournisseurRepository();
      const updateFournisseurUsecase = new UpdateFournisseurUsecase(
        updateFournisseurRepository
      );
      return await updateFournisseurUsecase.execute(id, fournisseur);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteFournisseur = createAsyncThunk(
  "professionalStock/deleteFournisseur",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteFournisseurRepository = new DeleteFournisseurRepository();
      const deleteFournisseurUsecase = new DeleteFournisseurUsecase(
        deleteFournisseurRepository
      );
      await deleteFournisseurUsecase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

// Recuperation des fournisseurs assiciee a un professionnel
export const getFournisseurs = createAsyncThunk(
  "professionalStock/getFournisseurs",
  async (userId: number, { rejectWithValue }) => {
    try {
      const getFournisseursRepository = new GetFournisseursByUserIdRepository();
      const getFournisseursUsecase = new GetFournisseursByUserIdUsecase(
        getFournisseursRepository
      );
      return await getFournisseursUsecase.execute(userId);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

// Thunks pour les produits
export const createProduit = createAsyncThunk(
  "professionalStock/createProduit",
  async (produit: Omit<Stocks, "id">, { rejectWithValue }) => {
    try {
      const createStocksRepository = new CreateStocksRepository();
      const createStocksUsecase = new CreateStocksUsecase(
        createStocksRepository
      );
      const result = await createStocksUsecase.execute([produit]);
      return result[0]; // Retourner le premier élément créé
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateProduit = createAsyncThunk(
  "professionalStock/updateProduit",
  async (
    { id, produit }: { id: number; produit: Omit<Stocks, "id"> },
    { rejectWithValue }
  ) => {
    try {
      const updateStocksRepository = new UpdateStocksRepository();
      const getStockByIdRepository = new GetStockByIdRepository();
      const updateStocksUsecase = new UpdateStocksUsecase(
        updateStocksRepository,
        getStockByIdRepository
      );
      return await updateStocksUsecase.execute(id, produit);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteProduit = createAsyncThunk(
  "professionalStock/deleteProduit",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteStocksRepository = new DeleteStocksRepository();
      const getStockByIdRepository = new GetStockByIdRepository();
      const deleteStocksUsecase = new DeleteStocksUsecase(
        deleteStocksRepository,
        getStockByIdRepository
      );
      await deleteStocksUsecase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

// Recuperation des produits associes a un professionnel
export const getProduits = createAsyncThunk(
  "professionalStock/getProduits",
  async (userId: number, { rejectWithValue }) => {
    try {
      const getStocksRepository = new GetStocksRepository();
      const getStocksUsecase = new GetStocksUsecase(getStocksRepository);
      return await getStocksUsecase.execute(userId);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getEntrees = createAsyncThunk(
  "professionalStock/getEntrees",
  async (userId: number, { rejectWithValue }) => {
    try {
      const getLotsByEntreeIdRepository = new GetLotsByEntreeIdRepository();
      const getLotsByEntreeIdUsecase = new GetLotsByEntreeIdUsecase(
        getLotsByEntreeIdRepository
      );
      const getEntreesStocksRepository = new GetEntreesStocksRepository();
      const getEntreesStocksUsecase = new GetEntreesStocksUsecase(
        getEntreesStocksRepository,
        getLotsByEntreeIdUsecase
      );
      return await getEntreesStocksUsecase.execute(userId);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const createEntreeStock = createAsyncThunk(
  "professionalStock/createEntreeStock",
  async (entree: CreateStockDTO, { rejectWithValue }) => {
    try {
      const createLotRepository = new CreateLotRepository();
      const createLotUsecase = new CreateLotUsecase(createLotRepository);
      const createEntreesStocksRepository = new CreateEntreesStocksRepository();
      const createEntreesStocksUsecase = new CreateEntreesStocksUsecase(
        createEntreesStocksRepository,
        createLotUsecase
      );

      const result = await createEntreesStocksUsecase.execute([entree]);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const createSortieStock = createAsyncThunk(
  "professionalStock/createSortieStock",
  async (sortie: SortieStockDTO, { rejectWithValue }) => {
    try {
      const getLotsByEntreeLotRepository = new GetLotsByEntreeIdRepository();
      const getLotsByEntreeLotUsecase = new GetLotsByEntreeIdUsecase(
        getLotsByEntreeLotRepository
      );
      const getEntreeStockByStockIdRepository =
        new GetEntreeStockByStockIdRepository();
      const getEntreeStockByStockIdUsecase =
        new GetEntreeStocksByStockIdUsecase(getEntreeStockByStockIdRepository);
      const updateLotRepository = new UpdateLotRepository();
      const updateLotUsecase = new UpdateLotUsecase(updateLotRepository);

      const createSortiesStocksRepository = new CreateSortiesStocksRepository();
      const createSortiesStocksUsecase = new CreateSortiesStocksUsecase(
        createSortiesStocksRepository,
        getEntreeStockByStockIdUsecase,
        getLotsByEntreeLotUsecase,
        updateLotUsecase
      );
      const out = await createSortiesStocksUsecase.execute(
        sortie.stock_id,
        sortie.quantite,
        sortie.type_sortie,
        sortie.destinataire
      );

      const lotsIds: number[] = out.map((sortie) => sortie.lot_id);
      const getLotsByIdsRepository = new GetLotsByIdsRepository();
      const getLotsByIdsUsecase = new GetLotsByIdsUsecase(
        getLotsByIdsRepository
      );
      const lots = await getLotsByIdsUsecase.execute(lotsIds);

      return { sorties: out, lots: lots };
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getSorties = createAsyncThunk(
  "professionalStock/getSorties",
  async (userId: number, { rejectWithValue }) => {
    try {
      const getSortiesStocksRepository = new GetSortiesStocksRepository();
      const getSortiesStocksUsecase = new GetSortiesStocksUsecase(
        getSortiesStocksRepository
      );
      const data = await getSortiesStocksUsecase.execute(userId);

      // Recuperation du lot correspondant a chaque sortie
      const lotIds: number[] = data.map((sortie) => sortie.lot_id);
      const getLotsByIdsRepository = new GetLotsByIdsRepository();
      const getLotsByIdsUsecase = new GetLotsByIdsUsecase(
        getLotsByIdsRepository
      );
      const lots = await getLotsByIdsUsecase.execute(lotIds);

      return { sorties: data, lots: lots };
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getLots = createAsyncThunk(
  "professionalStock/getLots",
  async (userId: number, { rejectWithValue }) => {
    try {
      const getLotsWithStockDataRepository =
        new GetLotsWithStockDataRepository();
      const getLotsWithStockDataUsecase = new GetLotsWithStockDataUsecase(
        getLotsWithStockDataRepository
      );
      return await getLotsWithStockDataUsecase.execute(userId);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const professionalStockSlice = createSlice({
  name: "professionalStock",
  initialState,
  reducers: {
    setError: (state, action) => {
      state.error = action.payload;
    },
    setDashboardData: (state, action) => {
      state.dashboardData = action.payload;
    },
    setStocksNearPeremption: (state, action) => {
      state.stocksNearPeremption = action.payload;
    },
    setLowStockAlert: (state, action) => {
      state.lowStockAlert = action.payload;
    },
    setExpiredLots: (state, action) => {
      state.expiredStocks = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // NOTE: Pas besoin de gestion de chargement pour les sous fonctionnalités. Elle est geree dans les hooks respectifs
      .addCase(createFournisseur.fulfilled, (state, action) => {
        state.fournisseurs?.push(action.payload);
      })
      .addCase(createFournisseur.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(updateFournisseur.fulfilled, (state, action) => {
        const index = state.fournisseurs.findIndex(
          (f) => f.id === action.payload.id
        );
        if (index !== -1) {
          state.fournisseurs[index] = action.payload;
        }
      })
      .addCase(updateFournisseur.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(deleteFournisseur.fulfilled, (state, action) => {
        state.fournisseurs = state.fournisseurs.filter(
          (f) => f.id !== action.payload
        );
      })
      .addCase(deleteFournisseur.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(getFournisseurs.fulfilled, (state, action) => {
        state.fournisseurs = action.payload;
      })
      .addCase(getFournisseurs.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      // Reducers pour les produits
      .addCase(createProduit.fulfilled, (state, action) => {
        state.produits?.push(action.payload);
      })
      .addCase(createProduit.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(updateProduit.fulfilled, (state, action) => {
        const index = state.produits.findIndex(
          (p) => p.id === action.payload.id
        );
        if (index !== -1) {
          state.produits[index] = action.payload;
        }
      })
      .addCase(updateProduit.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(deleteProduit.fulfilled, (state, action) => {
        state.produits = state.produits.filter((p) => p.id !== action.payload);
      })
      .addCase(deleteProduit.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(getProduits.fulfilled, (state, action) => {
        state.produits = action.payload;
      })
      .addCase(getProduits.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(getCategories.fulfilled, (state, action) => {
        state.categories = action.payload;
      })
      .addCase(getCategories.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(
        getEntrees.fulfilled,
        (
          state,
          action: PayloadAction<{ entree: EntreesStocks; lots: Lots }[]>
        ) => {
          const data: EntreeStockRowDTO[] = action.payload.map(
            (entreeRow: { entree: EntreesStocks; lots: Lots }) => {
              const matchingStocks = state.produits.find(
                (stockRow) => stockRow.id === entreeRow.entree.stock_id
              );
              const matchingFournisseur = state.fournisseurs.find(
                (fournisseurRow) =>
                  fournisseurRow.id === entreeRow.entree.fournisseur_id
              );

              return {
                id: entreeRow.entree.id,
                stock_id: entreeRow.entree.stock_id,
                fournisseur_id: entreeRow.entree.fournisseur_id,
                prix_unitaire: entreeRow.entree.prix_unitaire,
                quantite: entreeRow.entree.quantite,
                produit: matchingStocks?.nom,
                fournisseur: matchingFournisseur?.nom,
                lot: entreeRow.lots?.numero_lot,
                date_entree:
                  new Date(entreeRow.entree.date_entree).toLocaleDateString(
                    "fr-FR"
                  ) || "Non specifie",
              };
            }
          );
          state.entrees = data;
        }
      )
      .addCase(createEntreeStock.fulfilled, (state, action) => {
        const data: EntreeStockRowDTO[] = action.payload.entrees.map(
          (entreeRow) => {
            const matchingStocks = state.produits.find(
              (stockRow) => stockRow.id === entreeRow.stock_id
            );
            const matchingFournisseur = state.fournisseurs.find(
              (fournisseurRow) => fournisseurRow.id === entreeRow.fournisseur_id
            );

            return {
              ...entreeRow,
              produit: matchingStocks?.nom,
              fournisseur: matchingFournisseur?.nom,
              lot:
                action.payload.lots.find(
                  (lotRow) => lotRow.entree_id === entreeRow.id
                )?.numero_lot || "Non specifie",
            };
          }
        );

        // Ajout du lots au state
        // Transformation de Lot en LotWithStockData
        const lotsWithStockData: LotWithStockData[] = action.payload.lots.map(
          (lotRow) => {
            const matchingEntree = data.find(
              (entreeRow) => entreeRow.id === lotRow.entree_id
            );
            const matchingStock = state.produits.find(
              (stockRow) => stockRow.id === matchingEntree.stock_id
            );
            return {
              ...lotRow,
              stocks: matchingStock,
            };
          }
        );

        state.lots = [...state.lots, ...lotsWithStockData];
        state.entrees = [...state.entrees, ...data];
      })
      .addCase(createEntreeStock.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(getEntrees.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(createSortieStock.fulfilled, (state, action) => {
        // Transformer une donnee de type SortiesStocks en SortieStockRowDTO
        const data: SortieStockRowDTO[] = action.payload.sorties.map(
          (sortieRow) => {
            const matchingLot = action.payload.lots.find(
              (lotRow) => lotRow.id === sortieRow.lot_id
            );
            const matchingEntree = state.entrees.find(
              (entreeRow) => entreeRow.id === matchingLot?.entree_id
            );

            return {
              ...sortieRow,
              produit: matchingEntree?.produit,
              lot: matchingLot?.numero_lot,
              date_sortie:
                new Date(sortieRow.date_sortie).toLocaleDateString("fr-FR") ||
                "Non specifie",
              destinataire: sortieRow?.destinataire || "Non specifie",
            };
          }
        );
        state.sorties = [...state.sorties, ...data];
      })
      .addCase(createSortieStock.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(getSorties.fulfilled, (state, action) => {
        // Creer une type de donne compatible a SortieStockRowDTO
        const matchingEntree: EntreeStockRowDTO[] = state.entrees.filter(
          (entree) => {
            return action.payload.lots.find(
              (lot) => lot.entree_id === entree.id
            );
          }
        );

        const data: SortieStockRowDTO[] = action.payload.sorties.map(
          (sortieRow) => {
            const matchingLot = action.payload.lots.find(
              (lot) => lot.id === sortieRow.lot_id
            );
            const out: SortieStockRowDTO = {
              ...sortieRow,
              lot: matchingLot?.numero_lot,
              produit: matchingEntree.find(
                (entree) => entree.id === matchingLot?.entree_id
              )?.produit,
            };

            return out;
          }
        );

        state.sorties = data;
      })
      .addCase(getSorties.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(getLots.fulfilled, (state, action) => {
        state.lots = action.payload;
      })
      .addCase(getLots.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      });
  },
});

export const { setError } = professionalStockSlice.actions;
export default professionalStockSlice.reducer;
