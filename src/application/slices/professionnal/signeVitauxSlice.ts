import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { signe_vitaux } from "@/domain/models/SigneVitaux";
import {
  CreateSigneVitauxUseCase,
  GetAllSigneVitauxUseCase,
  UpdateSigneVitauxUseCase,
  DeleteSigneVitauxUseCase,
} from "@/domain/usecases/professional/signeVitaux";
import {
  CreateSigneVitauxRepository,
  DeleteSigneVitauxRepository,
  GetAllSigneVitauxRepository,
  GetByIdSigneVitauxRepository,
  UpdateSigneVitauxRepository,
} from "@/infrastructure/repositories/signesVitaux";
import { GetByIdSigneVitauxUseCase } from "@/domain/usecases/professional/signeVitaux/GetByIdSigneVitauxUseCase";
import GetVitalSignsByPatientRepository from "@/infrastructure/repositories/signesVitaux/GetVitalSignsByPatientRepository";
import GetVitalSignsByPatientUsecase from "@/domain/usecases/professional/signeVitaux/GetVitalSignsByPatientUsecase";
import { VitalSignsDetails } from "@/domain/DTOS/VitalSignsDetailsDTO";

interface SigneVitauxState {
  vitalSigns: VitalSignsDetails[];
  signeVitaux: signe_vitaux[];
  loading: boolean;
  error: string | null;
}

const initialState: SigneVitauxState = {
  vitalSigns: [],
  signeVitaux: [],
  loading: false,
  error: null,
};

export const getAllSigneVitaux = createAsyncThunk(
  "signeVitaux/getAll",
  async (carnetId: number) => {
    const getAllSigneVitauxRepository = new GetAllSigneVitauxRepository();
    const getAllSigneVitauxUseCase = new GetAllSigneVitauxUseCase(
      getAllSigneVitauxRepository
    );
    return await getAllSigneVitauxUseCase.execute(carnetId);
  }
);

export const getByIdSigneVitaux = createAsyncThunk(
  "signeVitaux/getById",
  async (carnetId: number) => {
    const getByIdSigneVitauxRepository = new GetByIdSigneVitauxRepository();
    const getByIdSigneVitauxUseCase = new GetByIdSigneVitauxUseCase(
      getByIdSigneVitauxRepository
    );
    return await getByIdSigneVitauxUseCase.execute(carnetId);
  }
);

// fetch by patient
export const getVitalSignsByPatient = createAsyncThunk(
  "signeVitaux/getByPatient",
  async (patientId: number) => {
    const getVitalSignsByPatientRepository = new GetVitalSignsByPatientRepository();
    const getVitalSignsByPatientUseCase = new GetVitalSignsByPatientUsecase(
      getVitalSignsByPatientRepository
    );
    return await getVitalSignsByPatientUseCase.execute(patientId);
  }
);

export const createSigneVitaux = createAsyncThunk(
  "signeVitaux/create",
  async (data: Omit<signe_vitaux, "id">) => {
    const createSigneVitauxRepository = new CreateSigneVitauxRepository();
    const createSigneVitauxUseCase = new CreateSigneVitauxUseCase(
      createSigneVitauxRepository
    );
    return await createSigneVitauxUseCase.execute(data);
  }
);

export const updateSigneVitaux = createAsyncThunk(
  "signeVitaux/update",
  async ({ id, data }: { id: number; data: Partial<signe_vitaux> }) => {
    const updateSigneVitauxRepository = new UpdateSigneVitauxRepository();
    const useCase = new UpdateSigneVitauxUseCase(updateSigneVitauxRepository);
    return await useCase.execute(id, data);
  }
);

export const deleteSigneVitaux = createAsyncThunk(
  "signeVitaux/delete",
  async (id: number) => {
    const deleteSigneVitauxRepository = new DeleteSigneVitauxRepository();
    const useCase = new DeleteSigneVitauxUseCase(deleteSigneVitauxRepository);
    await useCase.execute(id);
    return id;
  }
);

const signeVitauxSlice = createSlice({
  name: "signeVitaux",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get
      .addCase(getAllSigneVitaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllSigneVitaux.fulfilled, (state, action) => {
        state.loading = false;
        state.signeVitaux = action.payload;
      })
      .addCase(getAllSigneVitaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      })
      // Get by patient id
      .addCase(getVitalSignsByPatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getVitalSignsByPatient.fulfilled, (state, action) => {
        state.loading = false;
        state.vitalSigns = action.payload;
      })
      .addCase(getVitalSignsByPatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      })

      // Create
      .addCase(createSigneVitaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSigneVitaux.fulfilled, (state, action) => {
        state.loading = false;
        state.signeVitaux.unshift(action.payload);
      })
      .addCase(createSigneVitaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      })
      // Update
      .addCase(updateSigneVitaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSigneVitaux.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.signeVitaux.findIndex(
          (sv) => sv.id === action.payload.id
        );
        if (index !== -1) {
          state.signeVitaux[index] = action.payload;
        }
      })
      .addCase(updateSigneVitaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      })
      // Delete
      .addCase(deleteSigneVitaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSigneVitaux.fulfilled, (state, action) => {
        state.loading = false;
        state.signeVitaux = state.signeVitaux.filter(
          (sv) => sv.id !== action.payload
        );
      })
      .addCase(deleteSigneVitaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      });
  },
});

export default signeVitauxSlice.reducer;
