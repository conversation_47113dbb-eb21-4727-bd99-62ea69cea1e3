import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Antecedant_sociaux } from "@/domain/models";
import {
  CreateAntecedentSociauxUseCase,
  GetAntecedentSociauxUseCase,
  UpdateAntecedentSociauxUseCase,
  DeleteAntecedentSociauxUseCase,
} from "@/domain/usecases/professional/antecedentSociaux";
import {
  CreateAntecedentSociauxAlcooliqueRepository,
  CreateAntecedentSociauxFummeurRepository,
  GetAntecedentSociauxAlcooliqueRepository,
  GetAntecedentSociauxFummeurRepository,
  DeleteAntecedentSociauxFummeurRepository,
  DeleteAntecedentSociauxAlcooliqueRepository,
  UpdateAntecedentSociauxFummeurRepository,
  UpdateAntecedentSociauxAlcooliqueRepository,
} from "@/infrastructure/repositories/antecedentSociaux";

interface AntecedentSociauxSliceState {
  antecedentSociaux: Antecedant_sociaux[];
  selectedAntecedentSociaux: Antecedant_sociaux | null;
  antecedentSociauxState: {
    estActive: { [key: string]: string };
    quantite: { [key: string]: string };
    qualite: { [key: string]: string };
    remarks: { [key: string]: string };
  };
  loading: boolean;
  error: string | null;
}

const DEFAULT_ANTECEDENT_SOCIAUX_STATE = {
  estActive: {},
  quantite: {},
  qualite: {},
  remarks: {},
};

const initialState: AntecedentSociauxSliceState = {
  antecedentSociaux: [],
  selectedAntecedentSociaux: null,
  antecedentSociauxState: DEFAULT_ANTECEDENT_SOCIAUX_STATE,
  loading: false,
  error: null,
};

export const createAntecedentSociaux = createAsyncThunk(
  "antecedentSociaux/create",
  async (data: Omit<Antecedant_sociaux, "id">[], { rejectWithValue }) => {
    try {
      const createAntecedentSociauxFummeurRepository =
        new CreateAntecedentSociauxFummeurRepository();
      const createAntecedentSociauxAlcooliqueRepository =
        new CreateAntecedentSociauxAlcooliqueRepository();
      const createAntecedentSociauxUseCase = new CreateAntecedentSociauxUseCase(
        createAntecedentSociauxFummeurRepository,
        createAntecedentSociauxAlcooliqueRepository
      );
      const result = await createAntecedentSociauxUseCase.execute(data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getAntecedentSociaux = createAsyncThunk(
  "antecedentSociaux/getAll",
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getAntecedentSociauxFummeurRepository =
        new GetAntecedentSociauxFummeurRepository();
      const getAntecedentSociauxAlcooliqueRepository =
        new GetAntecedentSociauxAlcooliqueRepository();
      const getAntecedentSociauxUseCase = new GetAntecedentSociauxUseCase(
        getAntecedentSociauxFummeurRepository,
        getAntecedentSociauxAlcooliqueRepository
      );
      const result = await getAntecedentSociauxUseCase.execute(carnetId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateAntecedentSociaux = createAsyncThunk(
  "antecedentSociaux/update",
  async (
    { id, data }: { id: number; data: Partial<Antecedant_sociaux> },
    { rejectWithValue }
  ) => {
    try {
      const updateAntecedentSociauxFummeurRepository =
        new UpdateAntecedentSociauxFummeurRepository();
      const updateAntecedentSociauxAlcooliqueRepository =
        new UpdateAntecedentSociauxAlcooliqueRepository();
      const updateAntecedentSociauxUseCase = new UpdateAntecedentSociauxUseCase(
        updateAntecedentSociauxFummeurRepository,
        updateAntecedentSociauxAlcooliqueRepository
      );
      const result = await updateAntecedentSociauxUseCase.execute(id, data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteAntecedentSociaux = createAsyncThunk(
  "antecedentSociaux/delete",
  async ({ id, nom }: { id: number; nom: string }, { rejectWithValue }) => {
    try {
      const deleteAntecedentSociauxFummeurRepository =
        new DeleteAntecedentSociauxFummeurRepository();
      const deleteAntecedentSociauxAlcooliqueRepository =
        new DeleteAntecedentSociauxAlcooliqueRepository();
      const deleteAntecedentSociauxUseCase = new DeleteAntecedentSociauxUseCase(
        deleteAntecedentSociauxFummeurRepository,
        deleteAntecedentSociauxAlcooliqueRepository
      );
      await deleteAntecedentSociauxUseCase.execute(id, nom);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const antecedentSociauxSlice = createSlice({
  name: "antecedentSociaux",
  initialState,
  reducers: {
    setSelectedAntecedentSociaux: (state, action) => {
      state.selectedAntecedentSociaux = action.payload;
    },
    setEstActive: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.antecedentSociauxState.estActive = {
        ...state.antecedentSociauxState.estActive,
        [action.payload.item]: action.payload.value,
      };
    },
    setQualite: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.antecedentSociauxState.qualite = {
        ...state.antecedentSociauxState.qualite,
        [action.payload.item]: action.payload.value,
      };
    },
    setQuantite: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.antecedentSociauxState.quantite = {
        ...state.antecedentSociauxState.quantite,
        [action.payload.item]: action.payload.value,
      };
    },
    setRemarks: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.antecedentSociauxState.remarks = {
        ...state.antecedentSociauxState.remarks,
        [action.payload.item]: action.payload.value,
      };
    },
    resetAntecedentSociauxState: (state) => {
      state.antecedentSociauxState = DEFAULT_ANTECEDENT_SOCIAUX_STATE;
    },
    clearSelectedAntecedentSociaux: (state) => {
      state.selectedAntecedentSociaux = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createAntecedentSociaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAntecedentSociaux.fulfilled, (state, action) => {
        state.loading = false;
        state.antecedentSociaux.push(...action.payload);
      })
      .addCase(createAntecedentSociaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get All
      .addCase(getAntecedentSociaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAntecedentSociaux.fulfilled, (state, action) => {
        state.loading = false;
        state.antecedentSociaux = action.payload;
      })
      .addCase(getAntecedentSociaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateAntecedentSociaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAntecedentSociaux.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.antecedentSociaux.findIndex(
          (as) => as.id === action.payload.id
        );
        if (index !== -1) {
          state.antecedentSociaux[index] = action.payload;
        }
      })
      .addCase(updateAntecedentSociaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteAntecedentSociaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAntecedentSociaux.fulfilled, (state, action) => {
        state.loading = false;
        state.antecedentSociaux = state.antecedentSociaux.filter(
          (as) => as.id !== Number(action.payload)
        );
      })
      .addCase(deleteAntecedentSociaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setSelectedAntecedentSociaux,
  setEstActive,
  setQualite,
  setQuantite,
  setRemarks,
  resetAntecedentSociauxState,
  clearSelectedAntecedentSociaux,
} = antecedentSociauxSlice.actions;
export default antecedentSociauxSlice.reducer;
