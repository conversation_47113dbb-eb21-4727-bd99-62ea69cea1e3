import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ProfessionalProfileDTO } from "@/domain/DTOS/CabinetMedicalFormDTO";
import { GetProfessionalProfileCompleteRepository } from "@/infrastructure/repositories/professionals/GetProfessionalProfileCompleteRepository";
import { GetProfessionalProfileCompleteUsecase } from "@/domain/usecases/professional/GetProfessionals/GetProfessionalProfileCompleteUsecase";
import { UpdateProfessionalProfessionalInfoRepository } from "@/infrastructure/repositories/professionals/UpdateProfessionalProfessionalInfoRepository";
import { UpdateProfessionalProfessionalInfoUsecase } from "@/domain/usecases/professional/UpdateProfessionalProfessionalInfoUsecase";
import {
  AssuranceProfessionnel,
  Contact,
  EtablissementProfessionnel,
  LangueParleeProfessionnel,
  MotClesProfessionnel,
  Professionnel,
  SpecialiteProfessionnel,
  Utilisateur,
} from "@/domain/models";
import { professionnels_types_consultation_enum } from "@/domain/models/enums";
import { Photo } from "@/domain/models/Photo";
import { PhotoTypeEnum } from "@/domain/models/enums/photo_type_enum";
import { DiplomeProfessionnel, ExperienceProfessionnel } from "@/domain/models";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";
import UpdateProfessionalRepository from "@/infrastructure/repositories/professionals/UpdateProfessionalRepository.ts";
import UpdateProfessionalUsecase from "@/domain/usecases/professional/UpdateProfessionalUsecase.ts";
import { CreateProfessionalDiplomaRepository } from "@/infrastructure/repositories/professionalDiploma/CreateProfessionalDiplomaRepository.ts";
import CreateProfessionalDiplomaUsecase from "@/domain/usecases/professionalDiploma/CreateProfessionalDiplomaUsecase.ts";
import { UpdateProfessionalDiplomaRepository } from "@/infrastructure/repositories/professionalDiploma/UpdateProfessionalDiplomaRepository.ts";
import UpdateProfessionalDiplomaUsecase from "@/domain/usecases/professionalDiploma/UpdateProfessionalDiplomaUsecase.ts";
import { DeleteProfessionalDiplomaRepository } from "@/infrastructure/repositories/professionalDiploma/DeleteProfessionalDiplomaRepository.ts";
import DeleteProfessionalDiplomaUsecase from "@/domain/usecases/professionalDiploma/DeleteProfessionalDiplomaUsecase.ts";
import { CreateProfessionalExperienceRepository } from "@/infrastructure/repositories/professionalExperience/CreateProfessionalExperienceRepository.ts";
import CreateProfessionalExperienceUsecase from "@/domain/usecases/professionalExperience/CreateProfessionalExperienceUsecase.ts";
import { UpdateProfessionalExperienceRepository } from "@/infrastructure/repositories/professionalExperience/UpdateProfessionalExperienceRepository.ts";
import UpdateProfessionalExperienceUsecase from "@/domain/usecases/professionalExperience/UpdateProfessionalExperienceUsecase.ts";
import { DeleteProfessionalExperienceRepository } from "@/infrastructure/repositories/professionalExperience/DeleteProfessionalExperienceRepository.ts";
import DeleteProfessionalExperienceUsecase from "@/domain/usecases/professionalExperience/DeleteProfessionalExperienceUsecase.ts";
import { CreateProfessionalPublicationRepository } from "@/infrastructure/repositories/professionalPublication/CreateProfessionalPublicationRepository.ts";
import CreateProfessionalPublicationUsecase from "@/domain/usecases/professionalPublication/CreateProfessionalPublicationUsecase.ts";
import { UpdateProfessionalPublicationRepository } from "@/infrastructure/repositories/professionalPublication/UpdateProfessionalPublicationRepository.ts";
import UpdateProfessionalPublicationUsecase from "@/domain/usecases/professionalPublication/UpdateProfessionalPublicationUsecase.ts";
import { DeleteProfessionalPublicationRepository } from "@/infrastructure/repositories/professionalPublication/DeleteProfessionalPublicationRepository.ts";
import DeleteProfessionalPublicationUsecase from "@/domain/usecases/professionalPublication/DeleteProfessionalPublicationUsecase.ts";
import UpdateProfessionalEtablishmentRepository from "@/infrastructure/repositories/EtablissementProfessionnel/UpdateProfessionalEtablishmentRepository.ts";
import UpdateProfessionalEtablishmentUsecase from "@/domain/usecases/professional/etablissementProfessionnel/UpdateProfessionalEtablishmentUsecase.ts";
import CreateProfessionalEtablishmentRepository from "@/infrastructure/repositories/EtablissementProfessionnel/CreateProfessionalEtablishmentRepository.ts";
import CreateProfessionalEtablishmentUsecase from "@/domain/usecases/professional/etablissementProfessionnel/CreateProfessionalEtablishmentUsecase.ts";
import { CreateContactRepository } from "@/infrastructure/repositories/contact/CreateContactRepository.ts";
import { CreateContactUsecase } from "@/domain/usecases/contact/CreateContactUsecase.ts";
import { DeleteContactRepository } from "@/infrastructure/repositories/contact/DeleteTimeSlotRepository.ts";
import { DeleteContactUsecase } from "@/domain/usecases/contact/DeleteContactUsecase.ts";
import { UpdateContactRepository } from "@/infrastructure/repositories/contact/UpdateContactRepository.ts";
import { UpdateContactUsecase } from "@/domain/usecases/contact/UpdateContactUsecase.ts";
import UpdateUserRepository from "@/infrastructure/repositories/user/UpdateUserRepository.ts";
import UpdateUserUsecase from "@/domain/usecases/user/UpdateUserUsecase.ts";
import GetAuthenticatedUserRepository from "@/infrastructure/repositories/user/GetAuthenticatedUserRepository.ts";
import { GetAuthenticatedUserUsecase } from "@/domain/usecases/user/index.ts";
import UpdateAuthentificationUserRepository from "@/infrastructure/repositories/user/UpdateAuthentificationUserRepository.ts";
import UpdateAuthentificationUserUsecase from "@/domain/usecases/user/userUsecase/UpdateAuthentificationUserUsecase.ts";
import { UserAttributes } from "@supabase/supabase-js";
import { UploadProfilePhotoUsecase } from "@/domain/usecases/professional/UploadProfilePhotoUsecase.ts";
import { SupabaseUploader } from "@/domain/services/SupabaseUploader.ts";
import { FilenameGenerator } from "@/domain/services/FilenameGenerator.ts";
import AddPhotoRepository from "@/infrastructure/repositories/photos/AddPhotosRepository.ts";
import AddPhotosUsecase from "@/domain/usecases/photos/AddPhotosUsecase";
import DeletePhotoRepository from "@/infrastructure/repositories/photos/DeletePhotoRepository.ts";
import DeletePhotoUsecase from "@/domain/usecases/photos/DeletePhotoUsecase.ts";
import GetPhotosByUserIdRepository from "@/infrastructure/repositories/photos/GetPhotosByUserIdRepository.ts";
import GetPhotosByUserIdUsecase from "@/domain/usecases/photos/GetPhotosByUserIdUsecase.ts";
import AddProfessionalSpecilitiesRepository from "@/infrastructure/repositories/professionalSpecialities/AddProfessionalSpecilitiesRepository.ts";
import AddProfessionalSpecilitiesUsecase from "@/domain/usecases/professionalSpecialities/AddProfessionalSpecialitiesUsecase.ts";
import DeleteSpecialiteRepository from "@/infrastructure/repositories/professionalSpecialities/DeleteSpecialiteRepository.ts";
import DeleteSpecialiteUsecase from "@/domain/usecases/professionalSpecialities/DeleteSpecialiteUsecase.ts";
import { AddLanguageRepository } from "@/infrastructure/repositories/professionalLanguage/AddLanguageRepository.ts";
import AddLanguageUsecase from "@/domain/usecases/professionalLanguage/AddLanguageUsecase.ts";
import DeleteLanguageUsecase from "@/domain/usecases/professionalLanguage/DeleteLanguageUsecase.ts";
import { DeleteLanguageRepository } from "@/infrastructure/repositories/professionalLanguage/DeleteLanguageRepository.ts";
import exp from "constants";
import { CreateProfessionalInsuranceRepository } from "@/infrastructure/repositories/insurance/CreateProfessionalInsuranceRepository.ts";
import CreateProfessionalInsuranceUsecase from "@/domain/usecases/insurance/CreateProfessionalInsuranceUsecase.ts";
import { DeleteProfessionalInsuranceRepository } from "@/infrastructure/repositories/insurance/DeleteProfessionalInsuranceRepository.ts";
import DeleteProfessionalInsuranceUsecase from "@/domain/usecases/insurance/DeleteProfessionalInsuranceUsecase.ts";
import GetInsuranceByIdRepository from "@/infrastructure/repositories/insurance/GetInsuranceByIdRepository.ts";
import GetInsuranceByIdUsecase from "@/domain/usecases/insurance/GetInsuranceByIdUsecase.ts";
import CreateProfessionalMotClesRepository from "@/infrastructure/repositories/motCles/CreateProfessionalMotClesRepository.ts";
import CreateProfessionalMotClesUsecase from "@/domain/usecases/motCles/CreateProfessionalMotClesUsecase.ts";
import { DeleteProfessionalMotClesRepository } from "@/infrastructure/repositories/motCles/DeleteProfessionalMotClesRepository.ts";
import DeleteProfessionalMotClesUsecase from "@/domain/usecases/professional/motcles/DeleteProfessionalMotClesUsecase.ts";
import { UploadCabinetImageUsecase } from "@/domain/usecases/professional/UploadCabinetImagesUsecase.ts";

// Définition de l'interface d'état
interface ProfessionalProfileState {
  profileData: ProfessionalProfileDTO | null;
  isLoading: boolean;
  error: string | null;
  lastFetchedId: number | null;
}

// État initial
const initialState: ProfessionalProfileState = {
  profileData: null,
  isLoading: false,
  error: null,
  lastFetchedId: null,
};

const getProfessionalProfileRepository =
  new GetProfessionalProfileCompleteRepository();
const getProfessionalProfileUsecase = new GetProfessionalProfileCompleteUsecase(
  getProfessionalProfileRepository
);

const updateProfessionalRepository = new UpdateProfessionalRepository();
const updateProfessionalUsecase = new UpdateProfessionalUsecase(
  updateProfessionalRepository
);

const updateContactRepository = new UpdateContactRepository();
const updateContactUsecase = new UpdateContactUsecase(updateContactRepository);

const createContactRepository = new CreateContactRepository();
const createContactUsecase = new CreateContactUsecase(createContactRepository);

const deleteContactRepository = new DeleteContactRepository();
const deleteContactUsecase = new DeleteContactUsecase(deleteContactRepository);

// wip
const filenameGenerator = new FilenameGenerator();
const supabaseUploader = new SupabaseUploader(filenameGenerator);

const updateUserRepository = new UpdateUserRepository();
const updateUserUsecase = new UpdateUserUsecase(updateUserRepository);

const getAuthenticatedUserRepository = new GetAuthenticatedUserRepository();
const getAuthenticatedUserUsecase = new GetAuthenticatedUserUsecase(
  getAuthenticatedUserRepository
);
const updateAuthentificationUserRepository =
  new UpdateAuthentificationUserRepository();
const updateAuthentificationUserUsecase = new UpdateAuthentificationUserUsecase(
  getAuthenticatedUserUsecase,
  updateAuthentificationUserRepository
);

const getProfessionalProfileCompleteRepository =
  new GetProfessionalProfileCompleteRepository();
const getProfessionalProfileCompleteUsecase =
  new GetProfessionalProfileCompleteUsecase(
    getProfessionalProfileCompleteRepository
  );

const deletePhotoRepository = new DeletePhotoRepository();
const deletePhotoUsecase = new DeletePhotoUsecase(
  deletePhotoRepository,
  supabaseUploader
);

const addPhotoRepository = new AddPhotoRepository();
const addPhotosUsecase = new AddPhotosUsecase(
  addPhotoRepository,
  supabaseUploader
);

const getPhotosByUserRepository = new GetPhotosByUserIdRepository();
const getPhotosByUserUsecase = new GetPhotosByUserIdUsecase(
  getPhotosByUserRepository
);

const uploadProfilePhotoUsecase = new UploadProfilePhotoUsecase(
  supabaseUploader,
  addPhotosUsecase
);

const uploadCabinetImagesUsecase = new UploadCabinetImageUsecase(
  supabaseUploader,
  addPhotosUsecase
);

const addProfessionalSpecilitiesRepository =
  new AddProfessionalSpecilitiesRepository();
const addProfessionalSpecilitiesUsecase = new AddProfessionalSpecilitiesUsecase(
  addProfessionalSpecilitiesRepository
);

const addLanguageRepository = new AddLanguageRepository();
const addLanguageUsecase = new AddLanguageUsecase(addLanguageRepository);

const deleteLanguageRepository = new DeleteLanguageRepository();
const deleteLanguageUsecase = new DeleteLanguageUsecase(
  deleteLanguageRepository
);

const deleteSpecialiteRepository = new DeleteSpecialiteRepository();
const deleteSpecialiteUsecase = new DeleteSpecialiteUsecase(
  deleteSpecialiteRepository
);

const createProfessionalInsuranceRepository =
  new CreateProfessionalInsuranceRepository();
const createProfessionalInsuranceUsecase =
  new CreateProfessionalInsuranceUsecase(createProfessionalInsuranceRepository);

const getInsuranceByIdRepository = new GetInsuranceByIdRepository();
const getInsuranceByIdUsecase = new GetInsuranceByIdUsecase(
  getInsuranceByIdRepository
);

const deleteProfessionalInsuranceRepository =
  new DeleteProfessionalInsuranceRepository();
const deleteProfessionalInsuranceUsecase =
  new DeleteProfessionalInsuranceUsecase(deleteProfessionalInsuranceRepository);

const getInsuranceById = async (id: number) => {
  return await getInsuranceByIdUsecase.execute(id);
};

// Thunk pour récupérer les données du profil professionnel
export const fetchProfessionalProfile = createAsyncThunk(
  "professionalProfile/fetch",
  async (userId: number, { rejectWithValue }) => {
    try {
      const data = await getProfessionalProfileUsecase.execute(userId);
      return data;
    } catch (error) {
      console.error("Erreur lors de la récupération du profil:", error);
      return rejectWithValue("Erreur lors de la récupération du profil");
    }
  }
);

// Thunk pour mettre à jour les informations de base
export const updateProfessional = createAsyncThunk(
  "professionalProfile/updateBaseInfo",
  async (
    {
      professionalId,
      baseInfo,
    }: {
      professionalId: number;
      baseInfo: Partial<Professionnel>;
    },
    { rejectWithValue }
  ) => {
    try {
      const updatedProfessional = await updateProfessionalUsecase.execute(
        professionalId,
        baseInfo
      );
      return updatedProfessional;
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour des informations de base:",
        error
      );
      return rejectWithValue(
        "Erreur lors de la mise à jour des informations de base"
      );
    }
  }
);

/**
 * Authentification
 */

export const updateAuthentificationUser = createAsyncThunk(
  "professionalProfile/updateAuthentificationUser",
  async (newUserData: UserAttributes, { rejectWithValue }) => {
    try {
      const updatedUser =
        await updateAuthentificationUserUsecase.execute(newUserData);
      return updatedUser;
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour des informations d'authentification:",
        error
      );
      return rejectWithValue(
        "Erreur lors de la mise à jour des informations d'authentification"
      );
    }
  }
);

/** Mise a jour table utilisateur */
export const updateUser = createAsyncThunk(
  "professionalProfile/updateUser",
  async (
    {
      userId,
      userData,
    }: {
      userId: number;
      userData: Partial<Utilisateur>;
    },
    { rejectWithValue }
  ) => {
    try {
      const updatedUser = await updateUserUsecase.execute(userId, userData);
      return updatedUser;
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour des informations de l'utilisateur:",
        error
      );
      return rejectWithValue(
        "Erreur lors de la mise à jour des informations de l'utilisateur"
      );
    }
  }
);

/**
 * Contact
 */
export const createProfessionalContact = createAsyncThunk(
  "professionalProfile/createContact",
  async (
    {
      contact,
    }: {
      contact: Omit<Contact, "id">;
    },
    { rejectWithValue }
  ) => {
    try {
      const createdContact = await createContactUsecase.execute([contact]);
      return createdContact;
    } catch (error) {
      console.error("Erreur lors de la création du contact:", error);
      return rejectWithValue("Erreur lors de la création du contact");
    }
  }
);

export const updateProfessionalContact = createAsyncThunk(
  "professionalProfile/updateContact",
  async (
    {
      contactId,
      contactData,
    }: {
      contactId: number;
      contactData: Partial<Contact>;
    },
    { rejectWithValue }
  ) => {
    try {
      const updatedProfessional = await updateContactUsecase.execute(
        contactId,
        contactData
      );
      return updatedProfessional;
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour des informations de contact:",
        error
      );
      return rejectWithValue(
        "Erreur lors de la mise à jour des informations de contact"
      );
    }
  }
);

export const deleteProfessionalContact = createAsyncThunk(
  "professionalProfile/deleteContact",
  async (contactId: number, { rejectWithValue }) => {
    try {
      await deleteContactUsecase.execute(contactId);
      return contactId;
    } catch (error) {
      console.error("Erreur lors de la suppression du contact:", error);
      return rejectWithValue("Erreur lors de la suppression du contact");
    }
  }
);

// Thunk pour mettre à jour la présentation
export const updatePresentationSection = createAsyncThunk(
  "professionalProfile/updatePresentation",
  async (
    {
      professionalId,
      presentation,
    }: {
      professionalId: number;
      presentation: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const updatedProfessional = await updateProfessionalUsecase.execute(
        professionalId,
        { presentation_generale: presentation }
      );
      return updatedProfessional;
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la présentation:", error);
      return rejectWithValue(
        "Erreur lors de la mise à jour de la présentation"
      );
    }
  }
);

// Thunk pour mettre à jour les services
export const updateServices = createAsyncThunk(
  "professionalProfile/updateServices",
  async (
    {
      professionalId,
      servicesData,
    }: {
      professionalId: number;
      servicesData: {
        types_consultation?: professionnels_types_consultation_enum;
        nouveau_patient_acceptes?: boolean;
      };
    },
    { rejectWithValue }
  ) => {
    try {
      const updatedProfessional = await updateProfessionalUsecase.execute(
        professionalId,
        servicesData
      );

      return updatedProfessional;
    } catch (error) {
      console.error("Erreur lors de la mise à jour des services:", error);
      return rejectWithValue("Erreur lors de la mise à jour des services");
    }
  }
);

/**
 * Diplomes
 */
export const createProfessionalDiploma = createAsyncThunk(
  "professionalProfile/createDiploma",
  async (
    {
      diploma,
    }: {
      diploma: Omit<DiplomeProfessionnel, "id">;
    },
    { rejectWithValue }
  ) => {
    try {
      const createRepository = new CreateProfessionalDiplomaRepository();
      const createUsecase = new CreateProfessionalDiplomaUsecase(
        createRepository
      );

      const createdDiploma = await createUsecase.execute([diploma]);
      return createdDiploma;
    } catch (error) {
      console.error("Erreur lors de la création du diplôme:", error);
      return rejectWithValue("Erreur lors de la création du diplôme");
    }
  }
);

export const updateProfessionalDiploma = createAsyncThunk(
  "professionalProfile/updateDiploma",
  async (
    {
      diplomaId,
      diploma,
    }: {
      diplomaId: number;
      diploma: Partial<DiplomeProfessionnel>;
    },
    { rejectWithValue }
  ) => {
    try {
      const updateRepository = new UpdateProfessionalDiplomaRepository();
      const updateUsecase = new UpdateProfessionalDiplomaUsecase(
        updateRepository
      );

      const updatedDiploma = await updateUsecase.execute(diplomaId, diploma);
      return updatedDiploma;
    } catch (error) {
      console.error("Erreur lors de la mise à jour du diplôme:", error);
      return rejectWithValue("Erreur lors de la mise à jour du diplôme");
    }
  }
);

export const deleteProfessionalDiploma = createAsyncThunk(
  "professionalProfile/deleteDiploma",
  async (diplomaId: number, { rejectWithValue }) => {
    try {
      const deleteRepository = new DeleteProfessionalDiplomaRepository();
      const deleteUsecase = new DeleteProfessionalDiplomaUsecase(
        deleteRepository
      );

      await deleteUsecase.execute(diplomaId);
      return diplomaId;
    } catch (error) {
      console.error("Erreur lors de la suppression du diplôme:", error);
      return rejectWithValue("Erreur lors de la suppression du diplôme");
    }
  }
);

/**
 * Specialite
 */
export const createProfessionalSpeciality = createAsyncThunk(
  "professionalProfile/createSpeciality",
  async (
    {
      speciality,
    }: {
      speciality: Omit<SpecialiteProfessionnel, "id">[];
    },
    { rejectWithValue }
  ) => {
    try {
      const createdSpeciality =
        await addProfessionalSpecilitiesUsecase.execute(speciality);
      return createdSpeciality;
    } catch (error) {
      console.error("Erreur lors de la création de la spécialité:", error);
      return rejectWithValue("Erreur lors de la création de la spécialité");
    }
  }
);

export const deleteProfessionalSpeciality = createAsyncThunk(
  "professionalProfile/deleteSpeciality",
  async (specialityId: number, { rejectWithValue }) => {
    try {
      await deleteSpecialiteUsecase.execute(specialityId);
      return specialityId;
    } catch (error) {
      console.error("Erreur lors de la suppression de la spécialité:", error);
      return rejectWithValue("Erreur lors de la suppression de la spécialité");
    }
  }
);

/**
 * Experiences
 */
export const createProfessionalExperience = createAsyncThunk(
  "professionalProfile/createExperience",
  async (
    {
      professionalId,
      experience,
    }: {
      professionalId: number;
      experience: Omit<ExperienceProfessionnel, "id">;
    },
    { rejectWithValue }
  ) => {
    try {
      const createRepository = new CreateProfessionalExperienceRepository();
      const createUsecase = new CreateProfessionalExperienceUsecase(
        createRepository
      );

      const createdExperience = await createUsecase.execute([experience]);
      return createdExperience;
    } catch (error) {
      console.error("Erreur lors de la création de l'expérience:", error);
      return rejectWithValue("Erreur lors de la création de l'expérience");
    }
  }
);

export const updateProfessionalExperience = createAsyncThunk(
  "professionalProfile/updateExperience",
  async (
    {
      experienceId,
      experience,
    }: {
      experienceId: number;
      experience: Partial<ExperienceProfessionnel>;
    },
    { rejectWithValue }
  ) => {
    try {
      const updateRepository = new UpdateProfessionalExperienceRepository();
      const updateUsecase = new UpdateProfessionalExperienceUsecase(
        updateRepository
      );

      const updatedExperience = await updateUsecase.execute(
        experienceId,
        experience
      );
      return updatedExperience;
    } catch (error) {
      console.error("Erreur lors de la mise à jour de l'expérience:", error);
      return rejectWithValue("Erreur lors de la mise à jour de l'expérience");
    }
  }
);

export const deleteProfessionalExperience = createAsyncThunk(
  "professionalProfile/deleteExperience",
  async (experienceId: number, { rejectWithValue }) => {
    try {
      const deleteRepository = new DeleteProfessionalExperienceRepository();
      const deleteUsecase = new DeleteProfessionalExperienceUsecase(
        deleteRepository
      );

      await deleteUsecase.execute(experienceId);
      return experienceId;
    } catch (error) {
      console.error("Erreur lors de la suppression de l'expérience:", error);
      return rejectWithValue("Erreur lors de la suppression de l'expérience");
    }
  }
);

/**
 * Mot cles
 */
export const addProfessionalMotCles = createAsyncThunk(
  "professionalProfile/addMotCles",
  async (
    {
      professionalId,
      motCles,
    }: {
      professionalId: number;
      motCles: Omit<MotClesProfessionnel, "id">[];
    },
    { rejectWithValue }
  ) => {
    try {
      const createRepository = new CreateProfessionalMotClesRepository();
      const createUsecase = new CreateProfessionalMotClesUsecase(
        createRepository
      );

      const createdMotCles = await createUsecase.execute(motCles);
      return createdMotCles;
    } catch (error) {
      console.error("Erreur lors de la création des mots-clés:", error);
      return rejectWithValue("Erreur lors de la création des mots-clés");
    }
  }
);

export const removeProfessionalMotCles = createAsyncThunk(
  "professionalProfile/removeMotCles",
  async (motCleId: number, { rejectWithValue }) => {
    try {
      const deleteRepository = new DeleteProfessionalMotClesRepository();
      const deleteUsecase = new DeleteProfessionalMotClesUsecase(
        deleteRepository
      );

      await deleteUsecase.execute(motCleId);
      return motCleId;
    } catch (error) {
      console.error("Erreur lors de la suppression du mot-clé:", error);
      return rejectWithValue("Erreur lors de la suppression du mot-clé");
    }
  }
);

/**
 * Publications
 */
export const createProfessionalPublication = createAsyncThunk(
  "professionalProfile/createPublication",
  async (
    {
      professionalId,
      publication,
    }: {
      professionalId: number;
      publication: Omit<PublicationProfessionnel, "id">;
    },
    { rejectWithValue }
  ) => {
    try {
      const createRepository = new CreateProfessionalPublicationRepository();
      const createUsecase = new CreateProfessionalPublicationUsecase(
        createRepository
      );

      const createdPublication = await createUsecase.execute([publication]);
      return createdPublication;
    } catch (error) {
      console.error("Erreur lors de la création de la publication:", error);
      return rejectWithValue("Erreur lors de la création de la publication");
    }
  }
);

export const updateProfessionalPublication = createAsyncThunk(
  "professionalProfile/updatePublication",
  async (
    {
      publicationId,
      publication,
    }: {
      publicationId: number;
      publication: Partial<PublicationProfessionnel>;
    },
    { rejectWithValue }
  ) => {
    try {
      const updateRepository = new UpdateProfessionalPublicationRepository();
      const updateUsecase = new UpdateProfessionalPublicationUsecase(
        updateRepository
      );

      const updatedPublication = await updateUsecase.execute(
        publicationId,
        publication
      );
      return updatedPublication;
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la publication:", error);
      return rejectWithValue("Erreur lors de la mise à jour de la publication");
    }
  }
);

export const deleteProfessionalPublication = createAsyncThunk(
  "professionalProfile/deletePublication",
  async (publicationId: number, { rejectWithValue }) => {
    try {
      const deleteRepository = new DeleteProfessionalPublicationRepository();
      const deleteUsecase = new DeleteProfessionalPublicationUsecase(
        deleteRepository
      );

      await deleteUsecase.execute(publicationId);
      return publicationId;
    } catch (error) {
      console.error("Erreur lors de la suppression de la publication:", error);
      return rejectWithValue("Erreur lors de la suppression de la publication");
    }
  }
);

/**
 * Etablissements
 */
export const createProfessionalEtablishment = createAsyncThunk(
  "professionalProfile/createEtablishment",
  async (
    {
      professionalId,
      etablishment,
    }: {
      professionalId: number;
      etablishment: Omit<EtablissementProfessionnel, "id">;
    },
    { rejectWithValue }
  ) => {
    try {
      const createRepository = new CreateProfessionalEtablishmentRepository();
      const createUsecase = new CreateProfessionalEtablishmentUsecase(
        createRepository
      );

      const createdEtablishment = await createUsecase.execute(etablishment);
      return createdEtablishment;
    } catch (error) {
      console.error("Erreur lors de la création de l'établissement:", error);
      return rejectWithValue("Erreur lors de la création de l'établissement");
    }
  }
);

export const updateProfessionalEtablishment = createAsyncThunk(
  "professionalProfile/updateEtablishment",
  async (
    {
      etablishmentId,
      etablishment,
    }: {
      etablishmentId: number;
      etablishment: Partial<EtablissementProfessionnel>;
    },
    { rejectWithValue }
  ) => {
    try {
      const updateRepository = new UpdateProfessionalEtablishmentRepository();
      const updateUsecase = new UpdateProfessionalEtablishmentUsecase(
        updateRepository
      );

      const updatedEtablishment = await updateUsecase.execute(
        etablishmentId,
        etablishment
      );
      return updatedEtablishment;
    } catch (error) {
      console.error("Erreur lors de la mise à jour de l'établissement:", error);
      return rejectWithValue(
        "Erreur lors de la mise à jour de l'établissement"
      );
    }
  }
);

// Thunk pour mettre à jour les informations professionnelles
export const updateProfessionalInformationSection = createAsyncThunk(
  "professionalProfile/updateProfessionalInfo",
  async (
    {
      professionalId,
      professionalData,
    }: {
      professionalId: number;
      professionalData: {
        numero_ordre?: string;
        raison_sociale?: string;
        nif?: string;
        stat?: string;
      };
    },
    { rejectWithValue }
  ) => {
    try {
      const updateRepository =
        new UpdateProfessionalProfessionalInfoRepository();
      const updateUsecase = new UpdateProfessionalProfessionalInfoUsecase(
        updateRepository
      );

      const updatedProfessional = await updateUsecase.execute(
        professionalId,
        professionalData
      );
      return updatedProfessional;
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour des informations professionnelles:",
        error
      );
      return rejectWithValue(
        "Erreur lors de la mise à jour des informations professionnelles"
      );
    }
  }
);

/**
 * Photo de profile
 */

export const getProfilePhotoByUserId = createAsyncThunk(
  "professionalProfile/getProfilePhotoByUserId",
  async (userId: number, { rejectWithValue }) => {
    try {
      const photos = await getPhotosByUserUsecase.execute(userId);
      return photos;
    } catch (error) {
      console.error(
        "Erreur lors de la récupération de la photo de profil:",
        error
      );
      return rejectWithValue((error as Error).message);
    }
  }
);

export const uploadProfilePhoto = createAsyncThunk(
  "professionalProfile/uploadProfilePhoto",
  async (
    { userId, file }: { userId: number; file: File },
    { rejectWithValue }
  ) => {
    try {
      const result = await uploadProfilePhotoUsecase.execute(userId, file);
      if (result.error) {
        return rejectWithValue(result.error);
      }
      return result.photo;
    } catch (error) {
      console.error("Erreur lors de l'upload de la photo de profil:", error);
      return rejectWithValue("Erreur lors de l'upload de la photo de profil");
    }
  }
);

export const deleteProfilePhoto = createAsyncThunk(
  "professionalProfile/deleteProfilePhoto",
  async (photoId: number, { rejectWithValue }) => {
    try {
      const result = await deletePhotoUsecase.execute(photoId);

      return result.id;
    } catch (error) {
      console.error(
        "Erreur lors de la suppression de la photo de profil:",
        error
      );
      return rejectWithValue(
        "Erreur lors de la suppression de la photo de profil"
      );
    }
  }
);

/**
 * Photo de presentation
 */
export const uploadPresentationPhoto = createAsyncThunk(
  "professionalProfile/uploadPresentationPhoto",
  async (
    { userId, file }: { userId: number; file: File },
    { rejectWithValue }
  ) => {
    try {
      const result = await uploadCabinetImagesUsecase.execute(userId, file);
      if (result.error) {
        return rejectWithValue(result.error);
      }
      if (result.photo) {
        return result.photo;
      }
    } catch (error) {
      console.error(
        "Erreur lors de l'upload de la photo de presentation:",
        error
      );
      return rejectWithValue(
        "Erreur lors de l'upload de la photo de presentation"
      );
    }
  }
);

export const deletePresentationPhoto = createAsyncThunk(
  "professionalProfile/deletePresentationPhoto",
  async (photoId: number, { rejectWithValue }) => {
    try {
      await deletePhotoUsecase.execute(photoId);
      return photoId;
    } catch (error) {
      console.error(
        "Erreur lors de la suppression de la photo de presentation:",
        error
      );
      return rejectWithValue(
        "Erreur lors de la suppression de la photo de presentation"
      );
    }
  }
);

/**
 * Langues
 */
export const createProfessionalLanguage = createAsyncThunk(
  "professionalProfile/createLanguage",
  async (
    {
      languages,
    }: {
      languages: Omit<LangueParleeProfessionnel, "id">[];
    },
    { rejectWithValue }
  ) => {
    try {
      const createdLanguages = await addLanguageUsecase.execute(languages);
      return createdLanguages;
    } catch (error) {
      console.error("Erreur lors de la création des langues:", error);
      return rejectWithValue("Erreur lors de la création des langues");
    }
  }
);

export const deleteProfessionalLanguage = createAsyncThunk(
  "professionalProfile/deleteLanguage",
  async (languageId: number, { rejectWithValue }) => {
    try {
      await deleteLanguageUsecase.execute(languageId);
      return languageId;
    } catch (error) {
      console.error("Erreur lors de la suppression de la langue:", error);
      return rejectWithValue("Erreur lors de la suppression de la langue");
    }
  }
);

// Modes de paiement
export const updatePaymentMethods = createAsyncThunk(
  "professionalProfile/updatePaymentMethods",
  async (
    {
      professionalId,
      paymentMethods,
    }: {
      professionalId: number;
      paymentMethods: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const updatedProfessional = await updateProfessionalUsecase.execute(
        professionalId,
        { modes_paiement_acceptes: paymentMethods }
      );
      return updatedProfessional;
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour des modes de paiement:",
        error
      );
      return rejectWithValue(
        "Erreur lors de la mise à jour des modes de paiement"
      );
    }
  }
);

// Assurances
export const addProfessionalInsurance = createAsyncThunk(
  "professionalProfile/addAssurance",
  async (
    {
      assurance,
    }: {
      assurance: Omit<AssuranceProfessionnel, "id">;
    },
    { rejectWithValue }
  ) => {
    try {
      const createdAssurance = await createProfessionalInsuranceUsecase.execute(
        [assurance]
      );
      const insurance = await getInsuranceById(
        createdAssurance[0].id_assurance
      );
      return insurance;
    } catch (error) {
      console.error("Erreur lors de la création de l'assurance:", error);
      return rejectWithValue("Erreur lors de la création de l'assurance");
    }
  }
);

export const removeProfessionalInsurance = createAsyncThunk(
  "professionalProfile/removeAssurance",
  async (assuranceId: number, { rejectWithValue }) => {
    try {
      await deleteProfessionalInsuranceUsecase.execute(assuranceId);
      return assuranceId;
    } catch (error) {
      console.error("Erreur lors de la suppression de l'assurance:", error);
      return rejectWithValue("Erreur lors de la suppression de l'assurance");
    }
  }
);

// Création du slice
const professionalProfileSlice = createSlice({
  name: "professionalProfile",
  initialState,
  reducers: {
    // Action pour mettre à jour la photo de profil
    updateProfilePhoto: (state, action: PayloadAction<Photo>) => {
      if (state.profileData) {
        const updatedPhotos = state.profileData.photos.filter(
          (photo) => photo.type !== PhotoTypeEnum.PROFILE
        );
        updatedPhotos.push(action.payload);
        state.profileData = {
          ...state.profileData,
          photos: updatedPhotos,
        };
      }
    },

    // Action pour supprimer la photo de profil
    removeProfilePhoto: (state) => {
      if (state.profileData) {
        state.profileData = {
          ...state.profileData,
          photos: state.profileData.photos.filter(
            (photo) => photo.type !== PhotoTypeEnum.PROFILE
          ),
        };
      }
    },

    // Action pour mettre à jour les diplômes
    updateDiplomas: (state, action: PayloadAction<DiplomeProfessionnel[]>) => {
      if (state.profileData) {
        state.profileData = {
          ...state.profileData,
          diplomas: action.payload,
        };
      }
    },

    // Action pour mettre à jour les expériences
    updateExperiences: (
      state,
      action: PayloadAction<ExperienceProfessionnel[]>
    ) => {
      if (state.profileData) {
        state.profileData = {
          ...state.profileData,
          experiences: action.payload,
        };
      }
    },

    // Action pour mettre à jour les publications
    updatePublications: (
      state,
      action: PayloadAction<PublicationProfessionnel[]>
    ) => {
      if (state.profileData) {
        state.profileData = {
          ...state.profileData,
          publications: action.payload,
        };
      }
    },

    // Action pour mettre à jour les données complètes du profil
    setProfileData: (state, action: PayloadAction<ProfessionalProfileDTO>) => {
      state.profileData = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Gestion de fetchProfessionalProfile
      .addCase(fetchProfessionalProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProfessionalProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profileData = action.payload;
        state.lastFetchedId = action.payload.baseInfo.id;
      })
      .addCase(fetchProfessionalProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Gestion de updateBaseInfoSection
      .addCase(updateProfessional.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            baseInfo: {
              ...state.profileData.baseInfo,
              titre: action.payload.titre,
              nom: action.payload.nom,
              prenom: action.payload.prenom,
            },
          };
        }
      })

      // Gestion de updatePresentationSection
      .addCase(updatePresentationSection.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            baseInfo: {
              ...state.profileData.baseInfo,
              presentation_generale: action.payload.presentation_generale || "",
            },
          };
        }
      })
      .addCase(updatePresentationSection.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      // Gestion de updateServices
      .addCase(updateServices.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            baseInfo: {
              ...state.profileData.baseInfo,
              types_consultation: action.payload.types_consultation,
              nouveau_patient_acceptes: action.payload.nouveau_patient_acceptes,
            },
          };
        }
      })
      // Gestion de updateProfessionalContact
      .addCase(updateProfessionalContact.fulfilled, (state, action) => {
        const updatedContacts = state.profileData.contact.map((contact) => {
          if (contact.id === action.payload.id) {
            return action.payload;
          }
          return contact;
        });

        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            contact: updatedContacts,
          };
        }
      })

      // Gestion de updateProfessionalInformationSection
      .addCase(
        updateProfessionalInformationSection.fulfilled,
        (state, action) => {
          if (state.profileData && action.payload) {
            state.profileData = {
              ...state.profileData,
              baseInfo: {
                ...state.profileData.baseInfo,
                numero_ordre: action.payload.numero_ordre,
                raison_sociale: action.payload.raison_sociale,
                nif: action.payload.nif,
                stat: action.payload.stat,
              },
            };
          }
        }
      )
      // Diplomes
      .addCase(createProfessionalDiploma.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            diplomas: [...state.profileData.diplomas, ...action.payload],
          };
        }
      })
      .addCase(updateProfessionalDiploma.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            diplomas: state.profileData.diplomas.map((diploma) =>
              diploma.id === action.payload.id ? action.payload : diploma
            ),
          };
        }
      })
      .addCase(deleteProfessionalDiploma.fulfilled, (state, action) => {
        if (state.profileData) {
          state.profileData = {
            ...state.profileData,
            diplomas: state.profileData.diplomas.filter(
              (diploma) => diploma.id !== action.payload
            ),
          };
        }
      })
      // Experiences
      .addCase(createProfessionalExperience.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            experiences: [...state.profileData.experiences, ...action.payload],
          };
        }
      })
      .addCase(updateProfessionalExperience.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            experiences: state.profileData.experiences.map((experience) =>
              experience.id === action.payload.id ? action.payload : experience
            ),
          };
        }
      })
      .addCase(deleteProfessionalExperience.fulfilled, (state, action) => {
        if (state.profileData) {
          state.profileData = {
            ...state.profileData,
            experiences: state.profileData.experiences.filter(
              (experience) => experience.id !== action.payload
            ),
          };
        }
      })
      // Publications
      .addCase(createProfessionalPublication.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            publications: [
              ...state.profileData.publications,
              ...action.payload,
            ],
          };
        }
      })
      .addCase(updateProfessionalPublication.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            publications: state.profileData.publications.map((publication) =>
              publication.id === action.payload.id
                ? action.payload
                : publication
            ),
          };
        }
      })
      .addCase(deleteProfessionalPublication.fulfilled, (state, action) => {
        if (state.profileData) {
          state.profileData = {
            ...state.profileData,
            publications: state.profileData.publications.filter(
              (publication) => publication.id !== action.payload
            ),
          };
        }
      })
      // Photo de profile
      .addCase(uploadProfilePhoto.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            photos: [...state.profileData.photos, action.payload],
          };
        }
      })
      .addCase(deleteProfilePhoto.fulfilled, (state, action) => {
        if (state.profileData) {
          state.profileData = {
            ...state.profileData,
            photos: state.profileData.photos.filter(
              (photo) => photo.id !== action.payload
            ),
          };
        }
      })
      // Photo de presentation
      .addCase(uploadPresentationPhoto.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            photos: [...state.profileData.photos, action.payload],
          };
        }
      })
      .addCase(deletePresentationPhoto.fulfilled, (state, action) => {
        if (state.profileData) {
          state.profileData = {
            ...state.profileData,
            photos: state.profileData.photos.filter(
              (photo) => photo.id !== action.payload
            ),
          };
        }
      })
      // Langues
      .addCase(createProfessionalLanguage.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            languages: [...state.profileData.languages, ...action.payload],
          };
        }
      })
      .addCase(deleteProfessionalLanguage.fulfilled, (state, action) => {
        if (state.profileData) {
          state.profileData = {
            ...state.profileData,
            languages: state.profileData.languages.filter(
              (language) => language.id !== action.payload
            ),
          };
        }
      })
      // Specialite
      .addCase(deleteProfessionalSpeciality.fulfilled, (state, action) => {
        if (state.profileData) {
          state.profileData = {
            ...state.profileData,
            specialities: state.profileData.specialities.filter(
              (speciality) => speciality.id !== action.payload
            ),
          };
        }
      })
      .addCase(createProfessionalSpeciality.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            specialities: [
              ...state.profileData.specialities,
              ...action.payload,
            ],
          };
        }
      })
      // Modes de paiement
      .addCase(updatePaymentMethods.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            baseInfo: {
              ...state.profileData.baseInfo,
              modes_paiement_acceptes: action.payload.modes_paiement_acceptes,
            },
            paymentMethods: action.payload.modes_paiement_acceptes,
          };
        }
      })
      // Assurance
      .addCase(addProfessionalInsurance.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            insurances: [...state.profileData.insurances, action.payload],
          };
        }
      })
      .addCase(removeProfessionalInsurance.fulfilled, (state, action) => {
        if (state.profileData) {
          state.profileData = {
            ...state.profileData,
            insurances: state.profileData.insurances.filter(
              (insurance) => insurance.id !== action.payload
            ),
          };
        }
      })
      // mot cles
      .addCase(addProfessionalMotCles.fulfilled, (state, action) => {
        if (state.profileData && action.payload) {
          state.profileData = {
            ...state.profileData,
            keywords: [...state.profileData.keywords, ...action.payload],
          };
        }
      })
      .addCase(removeProfessionalMotCles.fulfilled, (state, action) => {
        if (state.profileData) {
          state.profileData = {
            ...state.profileData,
            keywords: state.profileData.keywords.filter(
              (keyword) => keyword.id !== action.payload
            ),
          };
        }
      });
  },
});

// Export des actions
export const {
  updateProfilePhoto,
  removeProfilePhoto,
  updateDiplomas,
  updateExperiences,
  updatePublications,
  setProfileData,
} = professionalProfileSlice.actions;

// Export du reducer
export default professionalProfileSlice.reducer;
