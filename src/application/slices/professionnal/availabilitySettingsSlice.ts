import { AvailabilitySettingsDTO } from "@/domain/DTOS";
import {
  horaire_date_specifique,
  horaire_hebdomadaire,
  parametre_disponibilite,
} from "@/domain/models";
import { CreateSettingsUsecase } from "@/domain/usecases/professional/availabilitySettings/CreateSettingsUseCase";
import { GetSettingsByProfessionalIdUsecase } from "@/domain/usecases/professional/availabilitySettings/GetSettingsByProfessionalIdUsecase";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { addNotification } from "@/application/slices/notification/notificationSlice";
import { AvailabilitySettingsSuccesMessage } from "@/domain/usecases/professional/availabilitySettings/availabilitySettingsMessage/AvailabilitySettingsSuccesMessage";
import { AvailabilitySettingsErrorMessage } from "@/domain/usecases/professional/availabilitySettings/availabilitySettingsMessage/AvailabilitySettingsErrorMessage";
import { DeleteSettingsUsecase } from "@/domain/usecases/professional/availabilitySettings/DeleteSettingsUsecase";
import { DeleteThisSettingsUsecase } from "@/domain/usecases/professional/availabilitySettings/DeleteThisSettingsUsecase";
import { DeleteWeeklySettingsUsecase } from "@/domain/usecases/professional/availabilitySettings/DeleteWeeklySettingsUsecase";
import { HandleWeeklyScheduleService } from "@/domain/services/HandleWeeklyScheduleService";
import { HandleSpecificScheduleService } from "@/domain/services/HandleSpecificScheduleService";
import { HandleExceptionsService } from "@/domain/services/HandleExceptionsService";
import { UpdateSettingsUsecase } from "@/domain/usecases/professional/availabilitySettings/UpdateSettingsUsecase";
import { DEFAULT_SETTINGS } from "@/shared/constants/DefaultSettings";
import {
  DeleteSettingsByProfessionalIdRepository,
  CreateSettingsRepository,
  UpdateSettingsRepository,
  GetSettingsByProfessionalIdRepository,
} from "@/infrastructure/repositories/availabilitySettings";
import {
  AddWeeklyScheduleRepository,
  DeleteWeeklyScheduleRepository,
} from "@/infrastructure/repositories/WeeklyScheduleRepository";
import {
  AddSpecificDateScheduleRepository,
  DeleteSpecificDateScheduleRepository,
} from "@/infrastructure/repositories/SpecificDataScheduleRepository";
import { CreateTimeSlotRepository } from "@/infrastructure/repositories/TimelotRepository";
interface AvailabilitySettingsState {
  settings: AvailabilitySettingsDTO | null;
  loading: boolean;
  error: string | null;
}

const initialState: AvailabilitySettingsState = {
  settings: null,
  loading: false,
  error: null,
};

const addSpecificDateScheduleRepository =
  new AddSpecificDateScheduleRepository();

const createTimeSlotRepository = new CreateTimeSlotRepository();

const handleExceptionsService = new HandleExceptionsService(
  addSpecificDateScheduleRepository,
  createTimeSlotRepository
);

const addWeeklyScheduleRepository = new AddWeeklyScheduleRepository();

const handleWeeklyScheduleService = new HandleWeeklyScheduleService(
  addWeeklyScheduleRepository,
  createTimeSlotRepository,
  handleExceptionsService
);

const handleSpecificScheduleService = new HandleSpecificScheduleService(
  addSpecificDateScheduleRepository,
  createTimeSlotRepository
);

const createSettingsRepository = new CreateSettingsRepository();
const deleteSettingsByProfessionalIdRepository =
  new DeleteSettingsByProfessionalIdRepository();

const createSettingsUsecase = new CreateSettingsUsecase(
  createSettingsRepository,
  deleteSettingsByProfessionalIdRepository,
  handleWeeklyScheduleService,
  handleSpecificScheduleService
);

const getSettingsByProfessionalIdRepository =
  new GetSettingsByProfessionalIdRepository();
const updateSettingsRepository = new UpdateSettingsRepository();

const updateSettingsUsecase = new UpdateSettingsUsecase(
  updateSettingsRepository
);

const getSettingsByProfessionalIdUsecase =
  new GetSettingsByProfessionalIdUsecase(getSettingsByProfessionalIdRepository);

const deleteSettingsUsecase = new DeleteSettingsUsecase(
  deleteSettingsByProfessionalIdRepository
);

const deleteSpecificDateScheduleRepository =
  new DeleteSpecificDateScheduleRepository();

const deleteThisSettingsUsecase = new DeleteThisSettingsUsecase(
  deleteSpecificDateScheduleRepository,
  handleExceptionsService
);

const deleteWeeklyScheduleRepository = new DeleteWeeklyScheduleRepository();

const deleteWeeklySettingsUsecase = new DeleteWeeklySettingsUsecase(
  deleteWeeklyScheduleRepository,
  handleWeeklyScheduleService
);

export const saveSettings = createAsyncThunk(
  "availabilitySettings/save",
  async (settings: Omit<AvailabilitySettingsDTO, "id">, { dispatch }) => {
    try {
      const settingData = await createSettingsUsecase.execute(settings);
      // changer le format de la date en string
      const data = {
        ...settingData,
        date_debut:
          settingData.date_debut instanceof Date
            ? new Date(settingData.date_debut).toISOString()
            : settingData.date_debut,
        date_fin:
          settingData.date_fin instanceof Date
            ? new Date(settingData.date_fin).toISOString()
            : settingData.date_fin,
      };
      console.log("data : ", data);

      dispatch(
        addNotification({
          message: AvailabilitySettingsSuccesMessage.SETTINGS_CREATED,
          type: "success",
        })
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: AvailabilitySettingsErrorMessage.SETTINGS_CREATED,
          type: "error",
        })
      );
      throw error;
    }
  }
);

export const updateSettings = createAsyncThunk(
  "availabilitySettings/update",
  async (
    {
      id,
      settings,
    }: {
      id: number;
      settings: Partial<parametre_disponibilite>;
    },
    { dispatch }
  ) => {
    try {
      const updatedSettings = await updateSettingsUsecase.execute(id, settings);
      dispatch(
        addNotification({
          message: AvailabilitySettingsSuccesMessage.SETTINGS_UPDATED,
          type: "success",
        })
      );
      return updatedSettings;
    } catch (error) {
      dispatch(
        addNotification({
          message: AvailabilitySettingsErrorMessage.SETTINGS_UPDATED,
          type: "error",
        })
      );
      throw error;
    }
  }
);

export const fetchSettings = createAsyncThunk(
  "availabilitySettings/fetch",
  async (professionalId: number, { rejectWithValue, dispatch }) => {
    try {
      const settings =
        await getSettingsByProfessionalIdUsecase.execute(professionalId);
      return settings;
    } catch (error) {
      dispatch(
        addNotification({
          message: AvailabilitySettingsErrorMessage.SETTINGS_FETCHED,
          type: "error",
        })
      );
      return rejectWithValue(error);
    }
  }
);

export const deleteSettings = createAsyncThunk(
  "availabilitySettings/deleteAll",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const setting = await deleteSettingsUsecase.execute(id);
      dispatch(
        addNotification({
          message: AvailabilitySettingsSuccesMessage.SETTINGS_DELETED,
          type: "success",
        })
      );
      return setting;
    } catch (error) {
      dispatch(
        addNotification({
          message: AvailabilitySettingsErrorMessage.SETTINGS_DELETED,
          type: "error",
        })
      );
      return rejectWithValue(error);
    }
  }
);

export const deleteThisSettings = createAsyncThunk(
  "availabilitySettings/deleteThis",
  async (
    {
      exceptions,
      id_parametre_disponibilite,
    }: {
      exceptions: horaire_date_specifique[];
      id_parametre_disponibilite: number;
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      const exception = await deleteThisSettingsUsecase.execute(
        id_parametre_disponibilite,
        exceptions
      );
      dispatch(
        addNotification({
          message: AvailabilitySettingsSuccesMessage.SETTINGS_DELETED,
          type: "success",
        })
      );
      return exception;
    } catch (error) {
      dispatch(
        addNotification({
          message: AvailabilitySettingsErrorMessage.SETTINGS_DELETED,
          type: "error",
        })
      );
      return rejectWithValue(error);
    }
  }
);

export const deleteWeeklySettings = createAsyncThunk(
  "availabilitySettings/deleteWeekly",
  async (
    {
      data,
      id_parametre_disponibilite,
    }: {
      data: horaire_hebdomadaire[];
      id_parametre_disponibilite: number;
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      const weeklySettings = await deleteWeeklySettingsUsecase.execute(
        id_parametre_disponibilite,
        data
      );
      dispatch(
        addNotification({
          message: AvailabilitySettingsSuccesMessage.SETTINGS_DELETED,
          type: "success",
        })
      );
      return weeklySettings;
    } catch (error) {
      dispatch(
        addNotification({
          message: AvailabilitySettingsErrorMessage.SETTINGS_DELETED,
          type: "error",
        })
      );
      return rejectWithValue(error);
    }
  }
);

const availabilitySettingsSlice = createSlice({
  name: "availabilitySettings",
  initialState,
  reducers: {
    resetSettings: (state) => {
      state.settings = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(saveSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(saveSettings.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.settings = action.payload;
      })
      .addCase(saveSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      })
      .addCase(updateSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSettings.fulfilled, (state, action) => {
        state.loading = false;
        state.settings = action.payload;
      })
      .addCase(updateSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      })
      .addCase(fetchSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSettings.fulfilled, (state, action) => {
        state.loading = false;
        state.settings = action.payload;
      })
      .addCase(fetchSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      })
      // delete all settings
      .addCase(deleteSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSettings.fulfilled, (state, action) => {
        state.loading = false;
        state.settings = DEFAULT_SETTINGS;
      })
      .addCase(deleteSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      })
      // delete this settings
      .addCase(deleteThisSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteThisSettings.fulfilled, (state, action) => {
        state.loading = false;
        state.settings.horaire_date_specifique = action.payload;
      })
      .addCase(deleteThisSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      })
      // delete weekly settings
      .addCase(deleteWeeklySettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteWeeklySettings.fulfilled, (state, action) => {
        state.loading = false;
        state.settings.horaire_hebdomadaire = action.payload;
      })
      .addCase(deleteWeeklySettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Une erreur est survenue";
      });
  },
});

export const { resetSettings } = availabilitySettingsSlice.actions;
export default availabilitySettingsSlice.reducer;
