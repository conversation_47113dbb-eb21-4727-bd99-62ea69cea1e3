import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { ConditionGynecologique } from "@/domain/models";
import {
  CreateConditionGynecologiqueUseCase,
  GetAllConditionGynecologiqueUseCase,
  GetByIdConditionGynecologiqueUseCase,
  UpdateConditionGynecologiqueUseCase,
  DeleteConditionGynecologiqueUseCase,
} from "@/domain/usecases/professional/conditionGynecologique";
import {
  CreateConditionGynecologiqueRepository,
  DeleteConditionGynecologiqueRepository,
  GetAllConditionGynecologiqueRepository,
  GetByIdConditionGynecologiqueRepository,
  UpdateConditionGynecologiqueRepository,
} from "@/infrastructure/repositories/conditionGynecologique";

interface ConditionGynecologiqueSliceState {
  conditionGynecologique: ConditionGynecologique[];
  selectedConditionGynecologique: ConditionGynecologique | null;
  conditionGynecologiqueState: {
    maladie: { [key: string]: string };
    date: { [key: string]: string | null };
    remarks: { [key: string]: string };
  };
  loading: boolean;
  error: string | null;
}

const DEFAULT_ANTECEDANT_GROSSESSE_STATE = {
  maladie: {},
  date: {},
  remarks: {},
};

const initialState: ConditionGynecologiqueSliceState = {
  conditionGynecologique: [],
  selectedConditionGynecologique: null,
  conditionGynecologiqueState: DEFAULT_ANTECEDANT_GROSSESSE_STATE,
  loading: false,
  error: null,
};

export const createConditionGynecologique = createAsyncThunk(
  "conditionGynecologique/create",
  async (data: Omit<ConditionGynecologique, "id">[], { rejectWithValue }) => {
    try {
      const createConditionGynecologiqueRepository =
        new CreateConditionGynecologiqueRepository();
      const createConditionGynecologiqueUseCase =
        new CreateConditionGynecologiqueUseCase(
          createConditionGynecologiqueRepository
        );
      const result = await createConditionGynecologiqueUseCase.execute(data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getAllConditionGynecologique = createAsyncThunk(
  "conditionGynecologique/getAll",
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getAllConditionGynecologiqueRepository =
        new GetAllConditionGynecologiqueRepository();
      const getAllConditionGynecologiqueUseCase =
        new GetAllConditionGynecologiqueUseCase(
          getAllConditionGynecologiqueRepository
        );
      const result =
        await getAllConditionGynecologiqueUseCase.execute(carnetId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getByIdConditionGynecologique = createAsyncThunk(
  "conditionGynecologique/getById",
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getByIdConditionGynecologiqueRepository =
        new GetByIdConditionGynecologiqueRepository();
      const getByIdConditionGynecologiqueUseCase =
        new GetByIdConditionGynecologiqueUseCase(
          getByIdConditionGynecologiqueRepository
        );
      const result =
        await getByIdConditionGynecologiqueUseCase.execute(carnetId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateConditionGynecologique = createAsyncThunk(
  "conditionGynecologique/update",
  async (
    { id, data }: { id: number; data: Partial<ConditionGynecologique> },
    { rejectWithValue }
  ) => {
    try {
      const updateConditionGynecologiqueRepository =
        new UpdateConditionGynecologiqueRepository();
      const updateConditionGynecologiqueUseCase =
        new UpdateConditionGynecologiqueUseCase(
          updateConditionGynecologiqueRepository
        );
      const result = await updateConditionGynecologiqueUseCase.execute(
        id,
        data
      );
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteConditionGynecologique = createAsyncThunk(
  "conditionGynecologique/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteConditionGynecologiqueRepository =
        new DeleteConditionGynecologiqueRepository();
      const deleteConditionGynecologiqueUseCase =
        new DeleteConditionGynecologiqueUseCase(
          deleteConditionGynecologiqueRepository
        );
      await deleteConditionGynecologiqueUseCase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const conditionGynecologiqueSlice = createSlice({
  name: "conditionGynecologique",
  initialState,
  reducers: {
    setSelectedConditionGynecologique: (state, action) => {
      state.selectedConditionGynecologique = action.payload;
    },
    setMaladie: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.conditionGynecologiqueState.maladie = {
        ...state.conditionGynecologiqueState.maladie,
        [action.payload.item]: action.payload.value,
      };
    },
    setDate: (
      state,
      action: PayloadAction<{ item: string; value: string | null }>
    ) => {
      state.conditionGynecologiqueState.date = {
        ...state.conditionGynecologiqueState.date,
        [action.payload.item]: action.payload.value,
      };
    },
    setRemarks: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.conditionGynecologiqueState.remarks = {
        ...state.conditionGynecologiqueState.remarks,
        [action.payload.item]: action.payload.value,
      };
    },
    resetConditionGynecologiqueState: (state) => {
      state.conditionGynecologiqueState = DEFAULT_ANTECEDANT_GROSSESSE_STATE;
    },
    clearSelectedConditionGynecologique: (state) => {
      state.selectedConditionGynecologique = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createConditionGynecologique.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createConditionGynecologique.fulfilled, (state, action) => {
        state.loading = false;
        state.conditionGynecologique.push(...action.payload);
      })
      .addCase(createConditionGynecologique.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get All
      .addCase(getAllConditionGynecologique.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllConditionGynecologique.fulfilled, (state, action) => {
        state.loading = false;
        state.conditionGynecologique = action.payload;
      })
      .addCase(getAllConditionGynecologique.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get ById
      .addCase(getByIdConditionGynecologique.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getByIdConditionGynecologique.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedConditionGynecologique = action.payload;
      })
      .addCase(getByIdConditionGynecologique.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateConditionGynecologique.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateConditionGynecologique.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.conditionGynecologique.findIndex(
          (ac) => ac.id === action.payload.id
        );
        if (index !== -1) {
          state.conditionGynecologique[index] = action.payload;
        }
      })
      .addCase(updateConditionGynecologique.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteConditionGynecologique.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteConditionGynecologique.fulfilled, (state, action) => {
        state.loading = false;
        state.conditionGynecologique = state.conditionGynecologique.filter(
          (ac) => ac.id !== Number(action.payload)
        );
      })
      .addCase(deleteConditionGynecologique.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setSelectedConditionGynecologique,
  setMaladie,
  setDate,
  setRemarks,
  resetConditionGynecologiqueState,
  clearSelectedConditionGynecologique,
} = conditionGynecologiqueSlice.actions;
export default conditionGynecologiqueSlice.reducer;
