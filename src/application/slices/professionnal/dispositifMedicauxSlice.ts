import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { DispositifMedicaux } from "@/domain/models";
import {
  CreateDispositifMedicauxUseCase,
  GetDispositifMedicauxUseCase,
  UpdateDispositifMedicauxUseCase,
  DeleteDispositifMedicauxUseCase,
} from "@/domain/usecases/professional/dispositifMedicaux";
import {
  CreateDispositifMedicauxRepository,
  DeleteDispositifMedicauxRepository,
  GetDispositifMedicauxRepository,
  UpdateDispositifMedicauxRepository,
} from "@/infrastructure/repositories/dispositifMedicaux";

interface DispositifMedicauxSliceState {
  dispositifMedicaux: DispositifMedicaux[];
  selectedDispositifMedicaux: DispositifMedicaux | null;
  dispositifMedicauxState: {
    marque: { [key: string]: string };
    modele: { [key: string]: string };
    reference: { [key: string]: string };
    dateAcquisition: { [key: string]: string | null };
    prochaineDate: { [key: string]: string | null };
    remarks: { [key: string]: string };
  };
  loading: boolean;
  error: string | null;
}

const DEFAULT_DISPOSITIF_MEDICAUX_STATE = {
  marque: {},
  modele: {},
  reference: {},
  dateAcquisition: {},
  prochaineDate: {},
  remarks: {},
};

const initialState: DispositifMedicauxSliceState = {
  dispositifMedicaux: [],
  selectedDispositifMedicaux: null,
  dispositifMedicauxState: DEFAULT_DISPOSITIF_MEDICAUX_STATE,
  loading: false,
  error: null,
};

export const createDispositifMedicaux = createAsyncThunk(
  "dispositifMedicaux/create",
  async (data: Omit<DispositifMedicaux, "id">[], { rejectWithValue }) => {
    try {
      const repository = new CreateDispositifMedicauxRepository();
      const useCase = new CreateDispositifMedicauxUseCase(repository);
      const result = await useCase.execute(data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getDispositifMedicaux = createAsyncThunk(
  "dispositifMedicaux/getAll",
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const repository = new GetDispositifMedicauxRepository();
      const useCase = new GetDispositifMedicauxUseCase(repository);
      const result = await useCase.getAll(carnetId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateDispositifMedicaux = createAsyncThunk(
  "dispositifMedicaux/update",
  async (
    { id, data }: { id: number; data: Partial<DispositifMedicaux> },
    { rejectWithValue }
  ) => {
    try {
      const repository = new UpdateDispositifMedicauxRepository();
      const useCase = new UpdateDispositifMedicauxUseCase(repository);
      const result = await useCase.execute(id, data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteDispositifMedicaux = createAsyncThunk(
  "dispositifMedicaux/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      const repository = new DeleteDispositifMedicauxRepository();
      const useCase = new DeleteDispositifMedicauxUseCase(repository);
      await useCase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const dispositifMedicauxSlice = createSlice({
  name: "dispositifMedicaux",
  initialState,
  reducers: {
    setSelectedDispositifMedicaux: (state, action) => {
      state.selectedDispositifMedicaux = action.payload;
    },
    setMarque: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.dispositifMedicauxState.marque = {
        ...state.dispositifMedicauxState.marque,
        [action.payload.item]: action.payload.value,
      };
    },
    setModele: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.dispositifMedicauxState.modele = {
        ...state.dispositifMedicauxState.modele,
        [action.payload.item]: action.payload.value,
      };
    },
    setReference: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.dispositifMedicauxState.reference = {
        ...state.dispositifMedicauxState.reference,
        [action.payload.item]: action.payload.value,
      };
    },
    setDateAcquisition: (
      state,
      action: PayloadAction<{ item: string; value: string | null }>
    ) => {
      state.dispositifMedicauxState.dateAcquisition = {
        ...state.dispositifMedicauxState.dateAcquisition,
        [action.payload.item]: action.payload.value,
      };
    },
    setProchaineDate: (
      state,
      action: PayloadAction<{ item: string; value: string | null }>
    ) => {
      state.dispositifMedicauxState.prochaineDate = {
        ...state.dispositifMedicauxState.prochaineDate,
        [action.payload.item]: action.payload.value,
      };
    },
    setRemarks: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.dispositifMedicauxState.remarks = {
        ...state.dispositifMedicauxState.remarks,
        [action.payload.item]: action.payload.value,
      };
    },
    resetDispositifMedicauxState: (state) => {
      state.dispositifMedicauxState = DEFAULT_DISPOSITIF_MEDICAUX_STATE;
    },
    clearSelectedDispositifMedicaux: (state) => {
      state.selectedDispositifMedicaux = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createDispositifMedicaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createDispositifMedicaux.fulfilled, (state, action) => {
        state.loading = false;
        state.dispositifMedicaux.push(...action.payload);
      })
      .addCase(createDispositifMedicaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get All
      .addCase(getDispositifMedicaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDispositifMedicaux.fulfilled, (state, action) => {
        state.loading = false;
        state.dispositifMedicaux = action.payload;
      })
      .addCase(getDispositifMedicaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateDispositifMedicaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateDispositifMedicaux.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.dispositifMedicaux.findIndex(
          (dm) => dm.id === action.payload.id
        );
        if (index !== -1) {
          state.dispositifMedicaux[index] = action.payload;
        }
      })
      .addCase(updateDispositifMedicaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteDispositifMedicaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteDispositifMedicaux.fulfilled, (state, action) => {
        state.loading = false;
        state.dispositifMedicaux = state.dispositifMedicaux.filter(
          (dm) => dm.id !== Number(action.payload)
        );
      })
      .addCase(deleteDispositifMedicaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setSelectedDispositifMedicaux,
  setMarque,
  setModele,
  setReference,
  setDateAcquisition,
  setProchaineDate,
  setRemarks,
  resetDispositifMedicauxState,
  clearSelectedDispositifMedicaux,
} = dispositifMedicauxSlice.actions;
export default dispositifMedicauxSlice.reducer;
