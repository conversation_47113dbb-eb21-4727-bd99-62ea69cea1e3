import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { AntecedantFamilliaux } from "@/domain/models";
import {
  CreateAntecedantFamiliauxUseCase,
  GetAntecedantFamiliauxUseCase,
  UpdateAntecedantFamiliauxUseCase,
  DeleteAntecedantFamiliauxUseCase,
} from "@/domain/usecases/professional/antecedantFamiliaux";
import {
  CreateAntecedantFamiliauxRepository,
  DeleteAntecedantFamiliauxRepository,
  GetAntecedantFamiliauxRepository,
  UpdateAntecedantFamiliauxRepository,
} from "@/infrastructure/repositories/antecedantFamiliaux";

interface AntecedantFamiliauxSliceState {
  antecedantFamiliaux: AntecedantFamilliaux[];
  selectedAntecedantFamiliaux: AntecedantFamilliaux | null;
  antecedantFamiliauxState: {
    decede: { [key: string]: boolean };
    affection: { [key: string]: string };
    remarks: { [key: string]: string };
  };
  loading: boolean;
  error: string | null;
}

const DEFAULT_ANTECEDANT_FAMILIAUX_STATE = {
  decede: {},
  affection: {},
  remarks: {},
};

const initialState: AntecedantFamiliauxSliceState = {
  antecedantFamiliaux: [],
  selectedAntecedantFamiliaux: null,
  antecedantFamiliauxState: DEFAULT_ANTECEDANT_FAMILIAUX_STATE,
  loading: false,
  error: null,
};

export const createAntecedantFamiliaux = createAsyncThunk(
  "antecedantFamiliaux/create",
  async (data: Omit<AntecedantFamilliaux, "id">[], { rejectWithValue }) => {
    try {
      const createAntecedantFamiliauxRepository =
        new CreateAntecedantFamiliauxRepository();
      const createAntecedantFamiliauxUseCase =
        new CreateAntecedantFamiliauxUseCase(
          createAntecedantFamiliauxRepository
        );
      const result = await createAntecedantFamiliauxUseCase.execute(data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getAntecedantFamiliaux = createAsyncThunk(
  "antecedantFamiliaux/getAll",
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getAntecedantFamiliauxRepository =
        new GetAntecedantFamiliauxRepository();
      const getAntecedantFamiliauxUseCase = new GetAntecedantFamiliauxUseCase(
        getAntecedantFamiliauxRepository
      );
      const result = await getAntecedantFamiliauxUseCase.getAll(carnetId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateAntecedantFamiliaux = createAsyncThunk(
  "antecedantFamiliaux/update",
  async (
    { id, data }: { id: number; data: Partial<AntecedantFamilliaux> },
    { rejectWithValue }
  ) => {
    try {
      const updateAntecedantFamiliauxRepository =
        new UpdateAntecedantFamiliauxRepository();
      const updateAntecedantFamiliauxUseCase =
        new UpdateAntecedantFamiliauxUseCase(
          updateAntecedantFamiliauxRepository
        );
      const result = await updateAntecedantFamiliauxUseCase.execute(id, data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteAntecedantFamiliaux = createAsyncThunk(
  "antecedantFamiliaux/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteAntecedantFamiliauxRepository =
        new DeleteAntecedantFamiliauxRepository();
      const deleteAntecedantFamiliauxUseCase =
        new DeleteAntecedantFamiliauxUseCase(
          deleteAntecedantFamiliauxRepository
        );
      await deleteAntecedantFamiliauxUseCase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const antecedantFamiliauxSlice = createSlice({
  name: "antecedantFamiliaux",
  initialState,
  reducers: {
    setSelectedAntecedantFamiliaux: (state, action) => {
      state.selectedAntecedantFamiliaux = action.payload;
    },
    setDecede: (
      state,
      action: PayloadAction<{ item: string; value: boolean }>
    ) => {
      state.antecedantFamiliauxState.decede = {
        ...state.antecedantFamiliauxState.decede,
        [action.payload.item]: action.payload.value,
      };
    },
    setAffection: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.antecedantFamiliauxState.affection = {
        ...state.antecedantFamiliauxState.affection,
        [action.payload.item]: action.payload.value,
      };
    },
    setRemarks: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.antecedantFamiliauxState.remarks = {
        ...state.antecedantFamiliauxState.remarks,
        [action.payload.item]: action.payload.value,
      };
    },
    resetAntecedantFamiliauxState: (state) => {
      state.antecedantFamiliauxState = DEFAULT_ANTECEDANT_FAMILIAUX_STATE;
    },
    clearSelectedAntecedantFamiliaux: (state) => {
      state.selectedAntecedantFamiliaux = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createAntecedantFamiliaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAntecedantFamiliaux.fulfilled, (state, action) => {
        state.loading = false;
        state.antecedantFamiliaux.push(...action.payload);
      })
      .addCase(createAntecedantFamiliaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get All
      .addCase(getAntecedantFamiliaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAntecedantFamiliaux.fulfilled, (state, action) => {
        state.loading = false;
        state.antecedantFamiliaux = action.payload;
      })
      .addCase(getAntecedantFamiliaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateAntecedantFamiliaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAntecedantFamiliaux.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.antecedantFamiliaux.findIndex(
          (af) => af.id === action.payload.id
        );
        if (index !== -1) {
          state.antecedantFamiliaux[index] = action.payload;
        }
      })
      .addCase(updateAntecedantFamiliaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteAntecedantFamiliaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAntecedantFamiliaux.fulfilled, (state, action) => {
        state.loading = false;
        state.antecedantFamiliaux = state.antecedantFamiliaux.filter(
          (af) => af.id !== Number(action.payload)
        );
      })
      .addCase(deleteAntecedantFamiliaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setSelectedAntecedantFamiliaux,
  setAffection,
  setDecede,
  setRemarks,
  resetAntecedantFamiliauxState,
  clearSelectedAntecedantFamiliaux,
} = antecedantFamiliauxSlice.actions;
export default antecedantFamiliauxSlice.reducer;
