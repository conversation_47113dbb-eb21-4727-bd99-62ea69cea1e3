import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { AntecedantChirurgicaux } from "@/domain/models";
import {
  CreateAntecedantChirurgicauxUseCase,
  GetAntecedantChirurgicauxUseCase,
  UpdateAntecedantChirurgicauxUseCase,
  DeleteAntecedantChirurgicauxUseCase,
} from "@/domain/usecases/professional/antecedantChirurgicaux";
import {
  CreateAntecedantChirurgicauxRepository,
  DeleteAntecedantChirurgicauxRepository,
  GetAntecedantChirurgicauxRepository,
  UpdateAntecedantChirurgicauxRepository,
} from "@/infrastructure/repositories/antecedantChirurgicaux";

interface AntecedantChirurgicauxSliceState {
  antecedantChirurgicaux: AntecedantChirurgicaux[];
  selectedAntecedantChirurgicaux: AntecedantChirurgicaux | null;
  antecedantChirurgicauxState: {
    descriptions: { [key: string]: string };
    dateDeChirurgie: { [key: string]: string | null };
    remarks: { [key: string]: string };
  };
  loading: boolean;
  error: string | null;
}

const DEFAULT_ANTECEDANT_CHIRURGICAUX_STATE = {
  descriptions: {},
  dateDeChirurgie: {},
  remarks: {},
};

const initialState: AntecedantChirurgicauxSliceState = {
  antecedantChirurgicaux: [],
  selectedAntecedantChirurgicaux: null,
  antecedantChirurgicauxState: DEFAULT_ANTECEDANT_CHIRURGICAUX_STATE,
  loading: false,
  error: null,
};

export const createAntecedantChirurgicaux = createAsyncThunk(
  "antecedantChirurgicaux/create",
  async (data: Omit<AntecedantChirurgicaux, "id">[], { rejectWithValue }) => {
    try {
      const createAntecedantChirurgicauxRepository =
        new CreateAntecedantChirurgicauxRepository();
      const createAntecedantChirurgicauxUseCase =
        new CreateAntecedantChirurgicauxUseCase(
          createAntecedantChirurgicauxRepository
        );
      const result = await createAntecedantChirurgicauxUseCase.execute(data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getAntecedantChirurgicaux = createAsyncThunk(
  "antecedantChirurgicaux/getAll",
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getAntecedantChirurgicauxRepository =
        new GetAntecedantChirurgicauxRepository();
      const getAntecedantChirurgicauxUseCase =
        new GetAntecedantChirurgicauxUseCase(
          getAntecedantChirurgicauxRepository
        );
      const result = await getAntecedantChirurgicauxUseCase.getAll(carnetId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateAntecedantChirurgicaux = createAsyncThunk(
  "antecedantChirurgicaux/update",
  async (
    { id, data }: { id: number; data: Partial<AntecedantChirurgicaux> },
    { rejectWithValue }
  ) => {
    try {
      const updateAntecedantChirurgicauxRepository =
        new UpdateAntecedantChirurgicauxRepository();
      const updateAntecedantChirurgicauxUseCase =
        new UpdateAntecedantChirurgicauxUseCase(
          updateAntecedantChirurgicauxRepository
        );
      const result = await updateAntecedantChirurgicauxUseCase.execute(
        id,
        data
      );
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteAntecedantChirurgicaux = createAsyncThunk(
  "antecedantChirurgicaux/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteAntecedantChirurgicauxRepository =
        new DeleteAntecedantChirurgicauxRepository();
      const deleteAntecedantChirurgicauxUseCase =
        new DeleteAntecedantChirurgicauxUseCase(
          deleteAntecedantChirurgicauxRepository
        );
      await deleteAntecedantChirurgicauxUseCase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const antecedantChirurgicauxSlice = createSlice({
  name: "antecedantChirurgicaux",
  initialState,
  reducers: {
    setSelectedAntecedantChirurgicaux: (state, action) => {
      state.selectedAntecedantChirurgicaux = action.payload;
    },
    setDescriptions: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.antecedantChirurgicauxState.descriptions = {
        ...state.antecedantChirurgicauxState.descriptions,
        [action.payload.item]: action.payload.value,
      };
    },
    setDateDeChirurgie: (
      state,
      action: PayloadAction<{ item: string; value: string | null }>
    ) => {
      state.antecedantChirurgicauxState.dateDeChirurgie = {
        ...state.antecedantChirurgicauxState.dateDeChirurgie,
        [action.payload.item]: action.payload.value,
      };
    },
    setRemarks: (
      state,
      action: PayloadAction<{ item: string; value: string }>
    ) => {
      state.antecedantChirurgicauxState.remarks = {
        ...state.antecedantChirurgicauxState.remarks,
        [action.payload.item]: action.payload.value,
      };
    },
    resetAntecedantChirurgicauxState: (state) => {
      state.antecedantChirurgicauxState = DEFAULT_ANTECEDANT_CHIRURGICAUX_STATE;
    },
    clearSelectedAntecedantChirurgicaux: (state) => {
      state.selectedAntecedantChirurgicaux = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createAntecedantChirurgicaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAntecedantChirurgicaux.fulfilled, (state, action) => {
        state.loading = false;
        state.antecedantChirurgicaux.push(...action.payload);
      })
      .addCase(createAntecedantChirurgicaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get All
      .addCase(getAntecedantChirurgicaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAntecedantChirurgicaux.fulfilled, (state, action) => {
        state.loading = false;
        state.antecedantChirurgicaux = action.payload;
      })
      .addCase(getAntecedantChirurgicaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateAntecedantChirurgicaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAntecedantChirurgicaux.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.antecedantChirurgicaux.findIndex(
          (ac) => ac.id === action.payload.id
        );
        if (index !== -1) {
          state.antecedantChirurgicaux[index] = action.payload;
        }
      })
      .addCase(updateAntecedantChirurgicaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteAntecedantChirurgicaux.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAntecedantChirurgicaux.fulfilled, (state, action) => {
        state.loading = false;
        state.antecedantChirurgicaux = state.antecedantChirurgicaux.filter(
          (ac) => ac.id !== Number(action.payload)
        );
      })
      .addCase(deleteAntecedantChirurgicaux.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setSelectedAntecedantChirurgicaux,
  setDescriptions,
  setRemarks,
  setDateDeChirurgie,
  resetAntecedantChirurgicauxState,
  clearSelectedAntecedantChirurgicaux,
} = antecedantChirurgicauxSlice.actions;
export default antecedantChirurgicauxSlice.reducer;
