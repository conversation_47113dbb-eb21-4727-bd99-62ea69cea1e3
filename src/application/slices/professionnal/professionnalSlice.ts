import {
  ProfessionalCompleteDTO,
  ProfessionalProfileData,
} from "@/domain/DTOS";
import GetProfessionalsUsecase from "@/domain/usecases/professional/GetProfessionals/GetProfessionalsUsecase";
import { GetProfessionalByIdUsecase } from "@/domain/usecases/professional/GetProfessionals/GetProfessionalByIdUsecase";
import GetProfessionalsRepository from "@/infrastructure/repositories/professionals/GetProfessionalsRepository";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { GetProfessionalByIdRepository } from "@/infrastructure/repositories/professionals/GetProfessionalByIdRepository";
import GetProfessionalSpecialitiesRepository from "@/infrastructure/repositories/professionalSpecialities/GetProfessionalSpecialitiesRepository";
import GetProfessionalAvailabilityByProfessionalIdUsecase from "@/domain/usecases/professionalAvailability/GetProfessionalAvailabilityByProfessionalIdUsecase";
import GetProfessionalAvailabilityByProfessionalIdRepository from "@/infrastructure/repositories/professionalAvailability/GetProfessionalAvailabilityByProfessionalIdRepository";
import GetProfessionalSpecialitiesUsecase from "@/domain/usecases/professionalSpecialities/GetProfessionalSpecialitiesUsecase";
import GetProfessionalEtablishmentsUsecase from "@/domain/usecases/professional/etablissementProfessionnel/GetProfessionalEtablishmentsUsecase";
import GetProfessionalEtablishmentsRepository from "@/infrastructure/repositories/EtablissementProfessionnel/GetProfessionalEtablishmentsRepository";

interface ProfessionalState {
  professionals: ProfessionalCompleteDTO[];
  selectedProfessionnal: ProfessionalProfileData | null;
  loading: boolean;
  error: string | null;
}

const initialState: ProfessionalState = {
  professionals: [],
  selectedProfessionnal: null,
  loading: false,
  error: null,
};

export const getProfessionals = createAsyncThunk(
  "professionals/getAll",
  async () => {
    const getProfessionalsRepository = new GetProfessionalsRepository();
    const getProfessionalsUsecase = new GetProfessionalsUsecase(
      getProfessionalsRepository,
    );
    return getProfessionalsUsecase.execute();
  },
);

export const getProfessionalById = createAsyncThunk(
  "professionals/getById",
  async (id: number) => {
    const getProfessionalSpecialitiesByProfessionalIdRepository =
      new GetProfessionalSpecialitiesRepository();

    const getProfessionalSpecialitiesByProfessionalIdUsecase =
      new GetProfessionalSpecialitiesUsecase(
        getProfessionalSpecialitiesByProfessionalIdRepository,
      );

    const getProfessionalAvailabilityByProfessionalIdRepository =
      new GetProfessionalAvailabilityByProfessionalIdRepository();
    const getProfessionalByIdRepository = new GetProfessionalByIdRepository();

    const getProfessionalAvailabilityByProfessionalIdUsecase =
      new GetProfessionalAvailabilityByProfessionalIdUsecase(
        getProfessionalAvailabilityByProfessionalIdRepository,
      );

    const getProfessionalEtablishmentsRepository =
      new GetProfessionalEtablishmentsRepository();
    const getProfessionalEtablishmentsUsecase =
      new GetProfessionalEtablishmentsUsecase(
        getProfessionalEtablishmentsRepository,
      );

    const getProfessionalByIdUsecase = new GetProfessionalByIdUsecase(
      getProfessionalByIdRepository,
      getProfessionalSpecialitiesByProfessionalIdUsecase,
      getProfessionalAvailabilityByProfessionalIdUsecase,
      getProfessionalEtablishmentsUsecase,
    );
    return getProfessionalByIdUsecase.execute(id);
  },
);

const professionalSlice = createSlice({
  name: "professionals",
  initialState,
  reducers: {
    setSelectedProfessional: (
      state,
      action: PayloadAction<ProfessionalProfileData | null>,
    ) => {
      state.selectedProfessionnal = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getProfessionals.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProfessionals.fulfilled, (state, action) => {
        state.loading = false;
        state.professionals = action.payload;
      })
      .addCase(getProfessionals.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getProfessionalById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProfessionalById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedProfessionnal = action.payload;
      })
      .addCase(getProfessionalById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedProfessional } = professionalSlice.actions;
export default professionalSlice.reducer;
