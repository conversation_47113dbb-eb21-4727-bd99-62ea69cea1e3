import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { conversation } from "@/domain/models";
import {
  CreateConversationRepository,
  GetConversationRepository,
  UpdateConversationRepository,
  DeleteConversationRepository,
  MarkConversationAsReadRepository,
} from "@/infrastructure/repositories/conversation";
import {
  CreateConversationUseCase,
  GetConversationUsecase,
  UpdateConversationUseCase,
  DeleteConversationUseCase,
  MarkConversationAsReadUseCase,
} from "@/domain/usecases/conversation";
import { ConversationDTO } from "@/presentation/types/message.types";
import GetUserByIdRepository from "@/infrastructure/repositories/user/GetUserByIdRepository";

interface ConversationSliceState {
  conversations: conversation[];
  conversationsDTO: ConversationDTO[];
  selectedConversationSlice: conversation | null;
  loading: boolean;
  error: string | null;
}

const initialState: ConversationSliceState = {
  conversations: [],
  conversationsDTO: [],
  selectedConversationSlice: null,
  loading: false,
  error: null,
};

export const createConversationSlice = createAsyncThunk(
  "conversation/create",
  async (data: Omit<conversation, "id">, { rejectWithValue }) => {
    try {
      const createConversationRepository = new CreateConversationRepository();
      const createConversationUseCase = new CreateConversationUseCase(
        createConversationRepository
      );
      const result = await createConversationUseCase.execute(data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getConversationSlice = createAsyncThunk(
  "conversation/getConversation",
  async (userId: number, { rejectWithValue }) => {
    try {
      const getConversationRepository = new GetConversationRepository();
      const getUserByIdRepository = new GetUserByIdRepository();
      const getConversationUsecase = new GetConversationUsecase(
        getConversationRepository,
        getUserByIdRepository
      );
      const result = await getConversationUsecase.execute(userId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateConversationSlice = createAsyncThunk(
  "conversation/update",
  async (
    { id, data }: { id: number; data: Partial<conversation> },
    { rejectWithValue }
  ) => {
    try {
      const updateConversationRepository = new UpdateConversationRepository();
      const updateConversationUseCase = new UpdateConversationUseCase(
        updateConversationRepository
      );
      const result = await updateConversationUseCase.execute(id, data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const markConversationAsReadSlice = createAsyncThunk(
  "conversation/markConversationAsRead",
  async (id: number, { rejectWithValue }) => {
    try {
      const markConversationAsReadRepository =
        new MarkConversationAsReadRepository();
      const markConversationAsReadUseCase = new MarkConversationAsReadUseCase(
        markConversationAsReadRepository
      );
      const result = await markConversationAsReadUseCase.execute(id);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteConversationSlice = createAsyncThunk(
  "conversation/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteConversationRepository = new DeleteConversationRepository();
      const deleteConversationUseCase = new DeleteConversationUseCase(
        deleteConversationRepository
      );
      await deleteConversationUseCase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const conversationSlice = createSlice({
  name: "Conversation",
  initialState,
  reducers: {
    setSelectedConversationSlice: (state, action) => {
      state.selectedConversationSlice = action.payload;
    },
    clearSelectedConversationSlice: (state) => {
      state.selectedConversationSlice = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createConversationSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createConversationSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.conversations.push(action.payload);
      })
      .addCase(createConversationSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get Conversation
      .addCase(getConversationSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getConversationSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.conversationsDTO = action.payload;
      })
      .addCase(getConversationSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateConversationSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateConversationSlice.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.conversations.findIndex(
          (am) => am.id === action.payload.id
        );
        if (index !== -1) {
          state.conversations[index] = action.payload;
        }
      })
      .addCase(updateConversationSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteConversationSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteConversationSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.conversations = state.conversations.filter(
          (am) => am.id !== Number(action.payload)
        );
      })
      .addCase(deleteConversationSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedConversationSlice, clearSelectedConversationSlice } =
  conversationSlice.actions;
export default conversationSlice.reducer;
