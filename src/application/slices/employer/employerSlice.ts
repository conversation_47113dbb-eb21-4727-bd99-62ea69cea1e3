import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Employer } from "@/domain/models";
import {
  CreateEmployerRepository,
  GetEmployerByUserIdRepository,
  GetEmployerByIdRepository,
  UpdateEmployerRepository,
  DeleteEmployerRepository,
  CreateMultipleEmployeRepositorire,
} from "@/infrastructure/repositories/employer";
import {
  CreateEmployerUsecase,
  GetEmployerByUserIdUsecase,
  UpdateEmployerUsecase,
  DeleteEmployerUsecase,
} from "@/domain/usecases/employer";
import { PostgrestError } from "@supabase/supabase-js";
import GetEmployersByDashIdUsecase from "@/domain/usecases/employer/GetEmployerByDashIdUsecase";
import GetEmployersByDashIdRepository from "@/infrastructure/repositories/employer/GetEmployeeByDashIdRepository";
import { CreateUserUsecase } from "@/domain/usecases/user";
import CreateUserRepository from "@/infrastructure/repositories/user/CreateUserRepository";
import { PasswordService } from "@/domain/services/PasswordService";
import { RootState } from "@/store";
import { UploadProfilePhotoUsecase } from "@/domain/usecases/professional/UploadProfilePhotoUsecase";
import { FilenameGenerator } from "@/domain/services/FilenameGenerator";
import { SupabaseUploader } from "@/domain/services/SupabaseUploader";
import AddPhotosUsecase from "@/domain/usecases/photos/AddPhotosUsecase";
import AddPhotoRepository from "@/infrastructure/repositories/photos/AddPhotosRepository";
import DeletePhotoRepository from "@/infrastructure/repositories/photos/DeletePhotoRepository";
import DeletePhotoUsecase from "@/domain/usecases/photos/DeletePhotoUsecase";
import GetPhotoByPathRepository from "@/infrastructure/repositories/photos/GetPhotoByPathRepository";
import GetPhotoByPathUsecase from "@/domain/usecases/photos/GetPhotoByPathUsecase";

// create
const createEmployerRepository = new CreateEmployerRepository();
const createUserRepository = new CreateUserRepository();
const passwordService = new PasswordService();
const createUserUsecase = new CreateUserUsecase(
  createUserRepository,
  passwordService
);
const filenameGenerator = new FilenameGenerator();
const supabaseUploader = new SupabaseUploader(filenameGenerator);
const addPhotoRepository = new AddPhotoRepository();
const addPhotosUsecase = new AddPhotosUsecase(
  addPhotoRepository,
  supabaseUploader
);
const uploadProfilePhotoUsecase = new UploadProfilePhotoUsecase(
  supabaseUploader,
  addPhotosUsecase
);
const deletePhotoRepository = new DeletePhotoRepository();
const deletePhotoUsecase = new DeletePhotoUsecase(
  deletePhotoRepository,
  supabaseUploader
);
const createEmployerUsecase = new CreateEmployerUsecase(
  createEmployerRepository,
  createUserUsecase,
  uploadProfilePhotoUsecase
);

// get
const getEmployerByUserIdRepository = new GetEmployerByUserIdRepository();
const getemployerByUserIdUsecase = new GetEmployerByUserIdUsecase(
  getEmployerByUserIdRepository
);
const getEmployersByDashIdRepository = new GetEmployersByDashIdRepository();
const getEmployersByDashIdUsecase = new GetEmployersByDashIdUsecase(
  getEmployersByDashIdRepository
);

const getPhotoByPathRepository = new GetPhotoByPathRepository();
const getPhotoByPathUsecase = new GetPhotoByPathUsecase(
  getPhotoByPathRepository
);

// update
const updateEmployerRepository = new UpdateEmployerRepository();
const updateEmployerUsecase = new UpdateEmployerUsecase(
  updateEmployerRepository,
  deletePhotoUsecase,
  uploadProfilePhotoUsecase,
  getPhotoByPathUsecase
);

// delete
const deleteEmployerRepository = new DeleteEmployerRepository();
const deleteEmployerUsecase = new DeleteEmployerUsecase(
  deleteEmployerRepository
);

interface EmplyerSliceState {
  employers: Employer[];
  selectedEmplyerSlice: Employer | null;
  loading: boolean;
  error: string | null;
  /* Indique la progression de l'upload multiple */
  counter: number;
}

const initialState: EmplyerSliceState = {
  employers: [],
  selectedEmplyerSlice: null,
  loading: false,
  error: null,
  counter: 0,
};

export const createEmployerThunk = createAsyncThunk<
  Employer, // En cas de succes
  { data: Omit<Employer, "id" | "id_utilisateur">; profilePhoto: File | null }, // parametre
  { rejectValue: string } // en cas d'erreur
>("employer/create", async ({ data, profilePhoto }, { rejectWithValue }) => {
  try {
    const result = await createEmployerUsecase.execute(data, profilePhoto);
    return result;
  } catch (error) {
    const formatedError = error as PostgrestError;

    /* NOTE: Dans le cas ou on a besoin de gerer une autre code d'erreur
     * avec des messages personnalisees, on peut l'ajouter ici
     */
    switch (formatedError.code) {
      case "23505":
        return rejectWithValue("Matricule deja existant.");
      default:
        return rejectWithValue(formatedError.message);
    }
  }
});

// import multiple

export const createMultipleEmployes = createAsyncThunk<
  Employer[], // ✅ Ici : retourne un tableau d'Employer
  {
    data: Omit<Employer, "id" | "id_utilisateur">[];
    profilePhoto: File | null;
  },
  { rejectValue: string; state: RootState }
>(
  "employe/createMultipleEmployes",
  async ({ data, profilePhoto }, { rejectWithValue, dispatch, getState }) => {
    const result: Employer[] = [];

    for (const employer of data) {
      const action = await dispatch(
        createEmployerThunk({ data: employer, profilePhoto: null })
      );

      if (createEmployerThunk.fulfilled.match(action)) {
        result.push(action.payload);
        dispatch(setCounter());
      } else if (createEmployerThunk.rejected.match(action)) {
        return rejectWithValue(action.payload as string);
      }
    }

    return result;
  }
);

export const getEmployerByIdThunk = createAsyncThunk(
  "employer/getById",
  async (id: number, { rejectWithValue }) => {
    try {
      const result = await getemployerByUserIdUsecase.execute(id);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getEmployersByDashIdThunk = createAsyncThunk(
  "employer/getEmployer",
  async (dashId: number, { rejectWithValue }) => {
    try {
      const result = await getEmployersByDashIdUsecase.execute(dashId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateEmployerThunk = createAsyncThunk<
  Employer, // En cas de succes
  {
    id: number;
    data: Partial<Employer>;
    profilePhoto: File | null;
    lastPath: string;
  }, // parametre
  { rejectValue: string } // en cas d'erreur
>(
  "employer/update",
  async ({ id, data, profilePhoto, lastPath }, { rejectWithValue }) => {
    try {
      const result = await updateEmployerUsecase.execute(
        id,
        data,
        profilePhoto,
        lastPath
      );
      return result;
    } catch (error) {
      const formatedError = error as PostgrestError;

      /* NOTE: Dans le cas ou on a besoin de gerer une autre code d'erreur
       * avec des messages personnalisees, on peut l'ajouter ici
       */
      switch (formatedError.code) {
        case "23505":
          return rejectWithValue("Matricule deja existant.");
        default:
          return rejectWithValue(formatedError.message);
      }
    }
  }
);

export const deleteEmployerThunk = createAsyncThunk(
  "employer/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      await deleteEmployerUsecase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

// // Import
// export const createMultipleEmployes = createAsyncThunk(
//   'employe/createMultipleEmployes',
//   async (employesData: Employer[], { rejectWithValue }) => {
//     try {
//       const employeRepository = new CreateMultipleEmployeRepositorire();
//       const employeUsecase = new CreateMultipleEmployerUsecase(employeRepository);
//       return await employeUsecase.createMultipleEmployes(employesData);

//     } catch (error) {
//       return rejectWithValue((error as Error).message);
//     }

//   }
// );

const employerSlice = createSlice({
  name: "employer",
  initialState,
  reducers: {
    setSelectedEmployer: (setSelectedEmployerSlice, action) => {
      setSelectedEmployerSlice.selectedEmplyerSlice = action.payload;
    },
    clearSelectedEmployer: (state) => {
      state.selectedEmplyerSlice = null;
    },
    setCounter: (state) => {
      state.counter = state.counter + 1;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createEmployerThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createEmployerThunk.fulfilled, (state, action) => {
        state.employers = [...state.employers, action.payload];
        state.error = null;
        state.loading = false;
      })
      .addCase(createEmployerThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      })
      // Get employer
      .addCase(getEmployersByDashIdThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getEmployersByDashIdThunk.fulfilled, (state, action) => {
        console.log(action.payload);
        state.employers = action.payload;
        state.error = null;
        state.loading = false;
      })
      .addCase(getEmployersByDashIdThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      })
      // // Create multiple employes
      // .addCase(createMultipleEmployes.fulfilled, (state, action: PayloadAction<Employer[]>) => {
      //   state.employers = [...action.payload, ...state.employers];
      // })

      // Get employer
      .addCase(getEmployerByIdThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getEmployerByIdThunk.fulfilled, (state, action) => {
        state.selectedEmplyerSlice = action.payload;
        state.error = null;
        state.loading = false;
      })
      .addCase(getEmployerByIdThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      })
      // Update
      .addCase(updateEmployerThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateEmployerThunk.fulfilled, (state, action) => {
        const index = state.employers.findIndex(
          (am) => am.id === action.payload.id
        );
        if (index !== -1) {
          state.employers[index] = action.payload;
        }
        state.error = null;
        state.loading = false;
      })
      .addCase(updateEmployerThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      })
      // Delete
      .addCase(deleteEmployerThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteEmployerThunk.fulfilled, (state, action) => {
        state.employers = state.employers.filter(
          (am) => am.id !== Number(action.payload)
        );
        state.error = null;
        state.loading = false;
      })
      .addCase(deleteEmployerThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      });
  },
});

export const { setSelectedEmployer, clearSelectedEmployer, setCounter } =
  employerSlice.actions;
export default employerSlice.reducer;
