// import supabase from "@/utils/supabase";

type Schedule = {
    name: string;
    specialization: string;
    address: string;
    city: string;
    videoAvailable: boolean;
    initialSchedule: (startDate: any) => any; // Remplacez `any` par le type correct
  };

export const ScheduleService = {
  // Get all social media
  getAllSchedule: async (): Promise<Schedule[]> => {
    // const { data, error } = await supabase.from("").select("*");

    // if (error) throw new Error(error.message);
    return [
        
    ];
  },
};
