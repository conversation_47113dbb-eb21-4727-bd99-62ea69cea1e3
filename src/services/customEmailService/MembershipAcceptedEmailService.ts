import {
  IMembershipAcceptedEmailService,
  MembershipAcceptedError,
  MembershipAcceptedRequest,
  MembershipAcceptedSuccess,
} from "@/domain/interfaces/services/customEmailService/IMembershipAcceptedEmailService";
import { ApiFetch } from "./ApiFetch";

/**
 * Service responsable de l'envoi des emails d'acceptation d'adhésion
 */
class MembershipAcceptedEmailService implements IMembershipAcceptedEmailService {
  constructor() {}

  /**
   * Envoie un email d'acceptation d'adhésion
   * @param params Les informations nécessaires pour l'email d'acceptation
   * @returns Un objet de succès ou d'erreur
   */
  async execute(
    params: MembershipAcceptedRequest
  ): Promise<MembershipAcceptedSuccess | MembershipAcceptedError> {
    try {
      const result = await ApiFetch.post<
        MembershipAcceptedRequest,
        MembershipAcceptedSuccess | MembershipAcceptedError
      >("membership-accepted", params);

      return result;
    } catch (error) {
      console.error("Erreur lors de l'envoi de l'email d'acceptation d'adhésion", error);

      return {
        error: "Erreur lors de l'envoi de l'email d'acceptation d'adhésion",
        details: error instanceof Error ? error.message : JSON.stringify(error),
      };
    }
  }
}

export default MembershipAcceptedEmailService;
