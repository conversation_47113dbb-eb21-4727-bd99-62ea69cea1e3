import {
  NotifyProfessionalRequest,
  NotifyProfessionalSuccess,
  NotifyProfessionalError,
} from "@/domain/DTOS/service/NotifyProfessionalDTO";

import { ApiFetch } from "./ApiFetch";
import { INotifyProfessionalEmailService } from "@/domain/interfaces/services/customEmailService/INotifyProfessionalEmailService";

/**
 * Service responsable de la notification du professionnel de santé par email
 */
class NotifyProfessionalEmailService
  implements INotifyProfessionalEmailService
{
  constructor() {}

  async execute(
    params: NotifyProfessionalRequest
  ): Promise<NotifyProfessionalSuccess | NotifyProfessionalError> {
    try {
      const result = await ApiFetch.post<
        NotifyProfessionalRequest,
        NotifyProfessionalSuccess | NotifyProfessionalError
      >("/notify-professional-appointment", params);

      return result;
    } catch (error) {
      console.error("Erreur lors de la notification du professionnel", error);

      return {
        error: "Erreur lors de la notification du professionnel",
        details: error instanceof Error ? error.message : JSON.stringify(error),
      };
    }
  }
}

export default NotifyProfessionalEmailService;
