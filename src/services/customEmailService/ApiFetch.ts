import { supabase } from "@/infrastructure/supabase/supabase";

export class ApiFetch {
  private static baseUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`;

  // Méthode GET
  static async get<TResponse>(
    endpoint: string,
    params?: Record<string, string>,
  ): Promise<TResponse> {
    const token = await this.getToken();

    const url = new URL(`${this.baseUrl}/${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    const res = await fetch(url.toString(), {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!res.ok) {
      throw new Error(`Erreur API GET: ${res.statusText}`);
    }

    return res.json();
  }

  // Méthode POST
  static async post<TRequest, TResponse>(
    endpoint: string,
    body: TRequest,
  ): Promise<TResponse> {
    const token = await this.getToken();

    const res = await fetch(`${this.baseUrl}/${endpoint}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(body),
    });

    if (!res.ok) {
      throw new Error(`Erreur API POST: ${res.statusText}`);
    }

    return res.json();
  }

  private static async getToken(): Promise<string> {
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();

    if (error || !session?.access_token) {
      throw new Error("Utilisateur non authentifié");
    }

    return session.access_token;
  }
}
