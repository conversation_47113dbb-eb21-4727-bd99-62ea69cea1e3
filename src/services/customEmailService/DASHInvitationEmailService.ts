// src/infrastructure/services/DASInvitationEmailService.ts

import {
  IDASHInvitationEmailService,
  DASHInvitationRequest,
  DASHInvitationSuccess,
  DASHInvitationError,
} from "@/domain/interfaces/services/customEmailService/IDASHInvitationEmailService";
import { ApiFetch } from "./ApiFetch";

/**
 * Service responsable de l'envoi des emails d'invitation pour les DASH
 */
class DASHInvitationEmailService implements IDASHInvitationEmailService {
  async execute(
    params: DASHInvitationRequest
  ): Promise<DASHInvitationSuccess | DASHInvitationError> {
    try {
      const result = await ApiFetch.post<
        DASHInvitationRequest,
        DASHInvitationSuccess | DASHInvitationError
      >("/Dash-Invitation-Link", params);

      return result;
    } catch (error) {
      console.error(
        "Erreur lors de l'envoi de l'email d'invitation DASH",
        error
      );

      return {
        error: "Erreur lors de l'envoi de l'email d'invitation DASH",
        details: error instanceof Error ? error.message : JSON.stringify(error),
      };
    }
  }
}

export default DASHInvitationEmailService;
