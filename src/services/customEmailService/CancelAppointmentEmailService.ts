import {
  CancelAppointmentError,
  CancelAppointmentRequest,
  CancelAppointmentSuccess,
} from "@/domain/DTOS/service/CancelAppointmentDTO";
import { ApiFetch } from "./ApiFetch";
import { ICancelAppointmentEmailService } from "@/domain/interfaces/services/customEmailService/ICancelAppointmentEmailService";

class CancelAppointmentEmailService implements ICancelAppointmentEmailService {
  constructor() {}

  async execute(
    params: CancelAppointmentRequest,
  ): Promise<CancelAppointmentSuccess | CancelAppointmentError> {
    try {
      const result = await ApiFetch.post<
        CancelAppointmentRequest,
        CancelAppointmentSuccess | CancelAppointmentError
      >("cancel-appointment", params);

      return result;
    } catch (error) {
      console.error("Erreur réseau ou interne", error);

      return {
        error: "Erreur réseau ou interne",
        details: error instanceof Error ? error.message : JSON.stringify(error),
      };
    }
  }
}

export default CancelAppointmentEmailService;
