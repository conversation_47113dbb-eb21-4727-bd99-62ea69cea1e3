import {
  ConfirmAppointmentError,
  ConfirmAppointmentRequest,
  ConfirmAppointmentSuccess,
} from "@/domain/DTOS/service/ConfirmAppointmentDTO";
import { ApiFetch } from "./ApiFetch";
import { IConfirmAppointmentEmailService } from "@/domain/interfaces/services/customEmailService/IConfirmAppointmentEmailService";

/**
 * Service responsable de l'envoi des emails de confirmation de rendez-vous
 */
class ConfirmAppointmentEmailService implements IConfirmAppointmentEmailService {
  constructor() {}

  /**
   * Envoie un email de confirmation de rendez-vous
   * @param params Les informations nécessaires pour l'email de confirmation
   * @returns Un objet de succès ou d'erreur
   */
  async execute(
    params: ConfirmAppointmentRequest,
  ): Promise<ConfirmAppointmentSuccess | ConfirmAppointmentError> {
    try {
      const result = await ApiFetch.post<
        ConfirmAppointmentRequest,
        ConfirmAppointmentSuccess | ConfirmAppointmentError
      >("/appointment-confirmed", params);

      return result;
    } catch (error) {
      console.error("Erreur lors de l'envoi de l'email de confirmation", error);

      return {
        error: "Erreur lors de l'envoi de l'email de confirmation",
        details: error instanceof Error ? error.message : JSON.stringify(error),
      };
    }
  }
}

export default ConfirmAppointmentEmailService;
