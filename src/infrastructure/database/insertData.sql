CREATE TYPE "utilisateurs_role_enum" AS ENUM (
'patient',
'professionnel',
'admin'
);

CREATE TYPE "patients_sexe_enum" AS ENUM (
'homme',
'femme',
'autre'
);

CREATE TYPE "patients_groupe_sanguin_enum" AS ENUM (
'A+',
'A-',
'B+',
'B-',
'O+',
'O-',
'AB+',
'AB-'
);

CREATE TYPE "proches_sexe_enum" AS ENUM (
'F',
'H'
);

CREATE TYPE "professionnels_categories_enum" AS ENUM (
'Un Cabinet Médical (CM)',
'Un établissement de santé privé (EHPR)',
'Un laboratoire d''analyses médicales (LAM)',
'Un centre d''imagerie médicale (CIM)',
'BMH'
);

CREATE TYPE "professionnels_titre_enum" AS ENUM (
'Docteur',
'Professeur'
);

CREATE TYPE "professionnels_types_consultation_enum" AS ENUM (
'en cabinet',
'à domicile',
'urgentiste'
);

CREATE TYPE "emplois_temps_jour_semaine_enum" AS ENUM (
'lundi',
'mardi',
'mercredi',
'jeudi',
'vendredi',
'samedi',
'dimanche'
);


CREATE TABLE "utilisateurs" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "role" utilisateurs_role_enum NOT NULL,
  "email" VARCHAR(255) UNIQUE NOT NULL,
  "mot_de_passe_hash" VARCHAR(255) NOT NULL,
  "bani" BOOLEAN,
  "cree_a" TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  "mis_a_jour_a" TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE "patients" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "utilisateur_id" INT NOT NULL,
  "unique_id" VARCHAR(255) UNIQUE NOT NULL,
  "nom" VARCHAR(255) NOT NULL,
  "prenom" VARCHAR(255),
  "sexe" patients_sexe_enum NOT NULL,
  "age" VARCHAR(255) NOT NULL,
  "decede" BOOLEAN DEFAULT false,
  "date_naissance" DATE NOT NULL,
  "adresse" Varchar(255),
  "district" VARCHAR(255) NOT NULL,
  "commune" VARCHAR(255) NOT NULL,
  "fokontany" VARCHAR(255) NOT NULL,
  "donneur_sang" BOOLEAN,
  "groupe_sanguin" VARCHAR(255),
  "nationalite" VARCHAR(255),
  "pays" VARCHAR(255),
  "situation_matrimonial" VARCHAR(255) NOT NULL,
  "nb_enfant" INT,
  "profession" VARCHAR(255)
);

CREATE TABLE "professionnels" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "utilisateur_id" INT NOT NULL,
  "titre" professionnels_titre_enum NOT NULL,
  "nom" VARCHAR(255) NOT NULL,
  "prenom" VARCHAR(255),
  "ordre_appartenance" VARCHAR(255) NOT NULL,
  "numero_ordre" VARCHAR(50) NOT NULL,
  "raison_sociale" VARCHAR(255),
  "nif" VARCHAR(255),
  "stat" VARCHAR(255),
  "presentation_generale" TEXT,
  "mots_cles" TEXT,
  "types_consultation" professionnels_types_consultation_enum NOT NULL,
  "modes_paiement_acceptes" TEXT,
  "langues_parlees" TEXT,
  "contact_email" VARCHAR(255),
  "reseaux_sociaux" TEXT
);

INSERT INTO mot_cles (mot) VALUES
('Accouchement'),
('Accouchement normal'),
('Allérgie'),
('Analyses médicales'),
('Collier du cycle'),
('Condom masculin'),
('Conseils en matière de planning familial'),
('Contraceptifs injectables (Depo)'),
('Contraception d''urgence'),
('COVID-19'),
('Dépistage'),
('Détartrage'),
('EVASAN'),
('Extraction dentaire'),
('Fraisage'),
('Hospitalisation'),
('Implants'),
('Maladies pédiatriques, diarrhées, infections'),
('Oeils'),
('Opération césarienne'),
('Paludisme'),
('Pilules contraceptives orales'),
('Planning familial'),
('Prescription méthodes de planning familial'),
('Préservatif féminin'),
('Prévention et traitement de l''infécondité'),
('Prise en charge des fistules'),
('Prothèse dentaire'),
('Santé maternelle'),
('Santé néonatale et infantile'),
('Santé Reproduction'),
('Service d''urgence'),
('Services d''eau assainissement et hygiène'),
('Services de chirurgie'),
('Services de laboratoire'),
('Services de radiologie et imagerie médicale'),
('Soins après avortement'),
('Soins essentiels du nouveau né'),
('Soins infirmiers'),
('Soins postnataux'),
('Soins prénataux'),
('Stérilisation féminine'),
('Stérilisation masculine'),
('Surveillance de la croissance'),
('Traitement d''une carie'),
('Tuberculose'),
('Vaccinations'),
('VIH/SIDA'),
('Visite à domicile');

INSERT INTO ordre_appartenance (nom) VALUES
('Ordre des masseurs kinésithérapeutes'),
('Ordre des sages femmes'),
('ORDRE NATIONAL DES CHIRURGIENS-DENTISTES'),
('ORDRE NATIONAL DES INFIRMIERS'),
('Ordre national des médecins'),
('ORDRE NATIONAL DES PÉDICURES-PODOLOGUES'),
('ORDRE NATIONAL DES PHARMACIENS'),
('Ordre national des psychologues'),
('Ordre national des odonto-stomatologistes'),
('Autres');