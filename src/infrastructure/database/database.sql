CREATE TYPE "utilisateurs_role_enum" AS ENUM (
  'patient',
  'professionnel',
  'admin'
);

CREATE TYPE "sexe_enum" AS ENUM (
  'homme',
  'femme'
);

CREATE TYPE "patients_groupe_sanguin_enum" AS ENUM (
  'A+',
  'A-',
  'B+',
  'B-',
  'O+',
  'O-',
  'AB+',
  'AB-'
);

CREATE TYPE "professionnels_categories_enum" AS ENUM (
  'Un Cabinet Médical (CM)',
  'Un établissement de santé privé (EHPR)',
  'Un laboratoire d''analyses médicales (LAM)',
  'Un centre d’imagerie médicale (CIM)',
  'BMH'
);

CREATE TYPE "professionnels_titre_enum" AS ENUM (
  'Docteur',
  'Professeur'
);

CREATE TYPE "professionnels_types_consultation_enum" AS ENUM (
  'en cabinet',
  'à domicile',
  'urgentiste'
);

CREATE TYPE "rendez_vous_statut_enum" AS ENUM (
  'A venir',
  'Terminer',
  'Manquer',
  'Annuler'
);

CREATE TYPE "emplois_temps_jour_semaine_enum" AS ENUM (
  'lundi',
  'mardi',
  'mercredi',
  'jeudi',
  'vendredi',
  'samedi',
  'dimanche'
);

CREATE TABLE "utilisateurs" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "role" utilisateurs_role_enum NOT NULL,
  "email" VARCHAR(255) UNIQUE NOT NULL,
  "mot_de_passe_hash" VARCHAR(255) NOT NULL,
  "bani" BOOLEAN,
  "cree_a" TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  "mis_a_jour_a" TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE "patients" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "utilisateur_id" INT NOT NULL,
  "unique_id" VARCHAR(255) UNIQUE NOT NULL,
  "nom" VARCHAR(255) NOT NULL,
  "prenom" VARCHAR(255),
  "sexe" sexe_enum NOT NULL,
  "decede" BOOLEAN DEFAULT false,
  "date_naissance" DATE NOT NULL,
  "adresse" Varchar(255),
  "district" VARCHAR(255) NOT NULL,
  "commune" VARCHAR(255) NOT NULL,
  "fokontany" VARCHAR(255) NOT NULL,
  "donneur_sang" BOOLEAN,
  "groupe_sanguin" VARCHAR(255),
  "nationalite" VARCHAR(255),
  "pays" VARCHAR(255),
  "situation_matrimonial" VARCHAR(255) NOT NULL,
  "nb_enfant" INT,
  "profession" VARCHAR(255)
);

CREATE TABLE "proches" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "id_patient" INT NOT NULL,
  "nom" VARCHAR(255) NOT NULL,
  "prenom" VARCHAR(255) NOT NULL,
  "sexe" sexe_enum NOT NULL,
  "date_naissance" DATE NOT NULL,
  "lien_parente" VARCHAR(255)
);

CREATE TABLE "professionnels" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "utilisateur_id" INT NOT NULL,
  "titre" professionnels_titre_enum NOT NULL,
  "nom" VARCHAR(255) NOT NULL,
  "prenom" VARCHAR(255),
  "sexe" sexe_enum,
  "id_ordre_appartenance" INT NOT NULL,
  "numero_ordre" VARCHAR(50) NOT NULL,
  "raison_sociale" VARCHAR(255),
  "nif" VARCHAR(255),
  "stat" VARCHAR(255),
  "presentation_generale" TEXT,
  "temps_moyen_consulation" INT,
  "types_consultation" professionnels_types_consultation_enum NOT NULL,
  "modes_paiement_acceptes" TEXT,
  "nouveau_patient_acceptes" BOOLEAN,
  "adresse" TEXT NOT NULL,
  "region" VARCHAR(255),
  "district" VARCHAR(255),
  "commune" VARCHAR(255),
  "fokontany" VARCHAR(255),
  "informations_acces" TEXT,
  "geolocalisation" POINT
);

CREATE TABLE "emplois_temps_professionnel" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "id_professionnel" INT NOT NULL,
  "date_disponibilite" date,
  "jour_semaine" emplois_temps_jour_semaine_enum NOT NULL,
  "heure_debut_matin" TIME NOT NULL,
  "heure_fin_matin" TIME NOT NULL,
  "heure_debut_aprem" TIME NOT NULL,
  "heure_fin_aprem" TIME NOT NULL,
  "prise" BOOLEAN DEFAULT false
);

CREATE TABLE "langues_parlees_professionnel" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom_langue" VARCHAR(255) NOT NULL,
  "id_professionnel" INT NOT NULL
);

CREATE TABLE "reseaux_sociaux_professionnel" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom_langue" VARCHAR(255) NOT NULL,
  "id_professionnel" INT NOT NULL
);

CREATE TABLE "specialites_professionnel" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom_specialite" VARCHAR(255) NOT NULL,
  "type_etablissement" professionnels_categories_enum,
  "id_professionnel" INT NOT NULL
);

CREATE TABLE "mot_cles_professionnel" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom" VARCHAR(255) NOT NULL,
  "id_professionnel" INT NOT NULL
);

CREATE TABLE "photo_professionnel" (
  "id" INT,
  "path" VARCHAR(255),
  "id_professionnel" INT NOT NULL
);

CREATE TABLE "etablissements_professionnel" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "id_professionnel" INT NOT NULL,
  "nom_etablissement" VARCHAR(255) NOT NULL,
  "nom_responsable" VARCHAR(255) NOT NULL,
  "prenom_responsable" VARCHAR(255) NOT NULL,
  "adresse" VARCHAR(255) NOT NULL
);

CREATE TABLE "liste_mot_cles" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom" VARCHAR(255) NOT NULL
);

CREATE TABLE "ordre_appartenance" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom" VARCHAR(255) NOT NULL
);

CREATE TABLE "liste_specialites" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom_specialite" INT NOT NULL,
  "id_type_etablissement" INT NOT NULL
);

CREATE TABLE "liste_type_etablissement" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom_etablissement" INT NOT NULL
);

CREATE TABLE "rendez_vous" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "patient_id" INT NOT NULL,
  "id_professionnel" INT NOT NULL,
  "date_rendez_vous" TIMESTAMP NOT NULL,
  "motif" TEXT,
  "statut" rendez_vous_statut_enum DEFAULT 'A venir',
  "est_absent" BOOLEAN DEFAULT false,
  "rappel_envoye" BOOLEAN DEFAULT false,
  "cree_a" TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  "mis_a_jour_a" TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE "historiques_medicaux" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "patient_id" INT NOT NULL,
  "id_professionnel" INT NOT NULL,
  "id_action_historique" INT,
  "id_consultation" INT,
  "confidentialite" BOOLEAN,
  "cree_a" TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE "action_historiques" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom_action" varchar(255) NOT NULL,
  "action_table" varchar(255) NOT NULL,
  "id_professionnel" INT NOT NULL
);

CREATE TABLE "AnnulerRendezVous" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "motifs" VARCHAR(255) NOT NULL,
  "id_rendez-vous" INT
);

CREATE TABLE "messages" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "expediteur_id" INT NOT NULL,
  "destinataire_id" INT NOT NULL,
  "contenu" TEXT NOT NULL,
  "lu" BOOLEAN NOT NULL,
  "envoye_a" TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE "administrateurs" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "utilisateur_id" INT NOT NULL,
  "nom" VARCHAR(255) NOT NULL
);

CREATE TABLE "journaux_activites" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "administrateur_id" INT NOT NULL,
  "action" TEXT NOT NULL,
  "horodatage" TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE "carnet_de_sante_numerique" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "id_allergie" INT,
  "id_medicament" INT,
  "id_affectation_medical" INT,
  "id_historique_mental_et_psychiatre" INT,
  "id_dispositif_medicaux" INT,
  "id_antecedant_chirurgicaux" INT,
  "id_antecedant_familliaux" INT,
  "id_antecedant_sociaux_fumeur" INT,
  "id_antecedant_sociaux_alcoolique" INT,
  "id_vaccination" INT,
  "id_laboratoire_et_diagnostics" INT,
  "id_patient" INT
);

CREATE TABLE "allergie" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "remarques" TEXT,
  "reaction" Text,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "medicament" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom" VARCHAR(255),
  "force" VARCHAR(200),
  "duree_jour" int,
  "frequence_dose" VARCHAR(255),
  "posologie" VARCHAR(255),
  "type_consommation" VARCHAR(255),
  "quantite_par_dosage" VARCHAR(255),
  "remarques" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "affectation_medical" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "maladie" TEXT,
  "date" TIMESTAMP,
  "remarques" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "historique_mental_et_psychiatre" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "maladie" TEXT,
  "date" TIMESTAMP,
  "remarques" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "dispositif_medicaux" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom" VARCHAR(255),
  "marque" VARCHAR(255),
  "modele" VARCHAR(255),
  "reference_appareil" VARCHAR(255),
  "date_aquiition" TIMESTAMP,
  "prochaine_mise_a_jour" TIMESTAMP,
  "remarques" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "antecedant_chirurgicaux" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom" VARCHAR(255),
  "date" TIMESTAMP,
  "description" TEXT,
  "remarques" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "antecedant_familliaux" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom_lien" VARCHAR(255),
  "decede" BOOLEAN,
  "affections_medicales" TEXT,
  "remarques" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "antecedant_sociaux_fumeur" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "fumeur_actif" BOOLEAN,
  "decede" BOOLEAN,
  "annee_a_fumer" INT,
  "quantite_par_jour" INT,
  "remarques" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "antecedant_sociaux_alcoolique" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "consommez_activement" BOOLEAN,
  "frequence" VARCHAR(255),
  "nb_boisson_consomer" INT,
  "remarques" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "vaccination" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom_vaccin" VARCHAR(255),
  "date_administration" TIMESTAMP,
  "prochaine_date_échéance" TIMESTAMP,
  "remarques" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "consultation_medical" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "id_professionnel" INT NOT NULL,
  "id_patient" INT NOT NULL,
  "raison_de_visite" VARCHAR(255) NOT NULL,
  "plainte_principale" VARCHAR(255) NOT NULL,
  "remarque" text NOT NULL,
  "examen_systemes" text,
  "diagnostique" text,
  "plan_de_soin" text,
  "confidentialite" BOOLEAN
);

CREATE TABLE "signe_vitaux" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "taille" FLOAT,
  "poid" FLOAT,
  "indice_masse_corporel" TEXT,
  "temperature" FLOAT,
  "circonference_tete" TEXT,
  "frequence_cardiaque" TEXT,
  "frequence_respiratoire" TEXT,
  "sa02" TEXT,
  "niveau_glucose" TEXT,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "urgence" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "contact_urgence_nom" VARCHAR(255),
  "contact_urgence_prenom" VARCHAR(255),
  "contact_urgence_telephone" VARCHAR(15),
  "id_patient" INT
);

CREATE TABLE "contact" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "contact_numero" VARCHAR(255),
  "contact_type" VARCHAR(15),
  "utilisateur_id" INT
);

CREATE TABLE "laboratoire_diagnostics" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "titre" VARCHAR(255),
  "type fichier" VARCHAR(255),
  "date" timestamp,
  "path" VARCHAR(255),
  "resultat" text,
  "remarque" text,
  "confidentialite" BOOLEAN,
  "id_patient" INT
);

CREATE TABLE "demande_adhesion" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "Nom" VARCHAR(255),
  "prenom" VARCHAR(255),
  "ville_Cabinet" VARCHAR(255),
  "district" VARCHAR(255),
  "commune" VARCHAR(255),
  "telephone" INT,
  "type_etablissemnt" VARCHAR(250),
  "email" VARCHAR(250)
);

CREATE TABLE parametre_disponibilite (
    id SERIAL PRIMARY KEY,
    id_professionnel INT NOT NULL,
    type VARCHAR(255) NOT NULL,
    sans_fin BOOLEAN DEFAULT TRUE NOT NULL,
    heure_debut TIMESTAMP NOT NULL,
    heure_fin TIMESTAMP NOT NULL,
    duree_pause INT NOT NULL,
    max_rendez_vous_par_jour INT NOT NULL,
    peut_inviter_autre BOOLEAN DEFAULT TRUE NOT NULL,
    CONSTRAINT fk_professionnel FOREIGN KEY (id_professionnel) REFERENCES professionnels(id) ON DELETE CASCADE
);

CREATE TABLE horaire_hebdomadaire (
    id SERIAL PRIMARY KEY,
    id_parametre_disponibilite INT NOT NULL,
    jour VARCHAR(255),
    CONSTRAINT fk_parametre_dispo_horaire FOREIGN KEY (id_parametre_disponibilite) REFERENCES parametre_disponibilite(id) ON DELETE CASCADE
);

CREATE TABLE horaire_date_specifique (
    id SERIAL PRIMARY KEY,
    id_parametre_disponibilite INT NOT NULL,
    date TIMESTAMP,
    CONSTRAINT fk_parametre_dispo_date FOREIGN KEY (id_parametre_disponibilite) REFERENCES parametre_disponibilite(id) ON DELETE CASCADE
);

CREATE TABLE pause (
    id SERIAL PRIMARY KEY,
    id_parametre_disponibilite INT NOT NULL,
    heure_debut VARCHAR(255),
    heure_fin VARCHAR(255),
    CONSTRAINT fk_parametre_dispo_pause FOREIGN KEY (id_parametre_disponibilite) REFERENCES parametre_disponibilite(id) ON DELETE CASCADE
);

CREATE TABLE creneau_horaire (
    id SERIAL PRIMARY KEY,
    id_horaire_specifique INT NOT NULL,
    id_horaire_hebdomadaire INT NOT NULL,
    heure_debut TIMESTAMP NOT NULL,
    heure_fin TIMESTAMP NOT NULL,
    CONSTRAINT fk_horaire_specifique FOREIGN KEY (id_horaire_specifique) REFERENCES horaire_date_specifique(id) ON DELETE CASCADE,
    CONSTRAINT fk_horaire_hebdo FOREIGN KEY (id_horaire_hebdomadaire) REFERENCES horaire_hebdomadaire(id) ON DELETE CASCADE
);

CREATE TABLE "evenement" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "id_professionnel" int,
  "titre" varchar(255),
  "description" text,
  "date_debut" date NOT NULL,
  "date_fin" date NOT NULL,
  "est_reportee" boolean NOT NULL DEFAULT false,
  "cree_a" timestamp DEFAULT (CURRENT_TIMESTAMP),
  "mis_a_jour_a" timestamp DEFAULT (CURRENT_TIMESTAMP)
);

ALTER TABLE "disponibilite" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id");

ALTER TABLE "pause" ADD FOREIGN KEY ("disponibilite_id") REFERENCES "disponibilite" ("id");

ALTER TABLE "evenement" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id");

ALTER TABLE "emplois_temps_professionnel" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id") ON DELETE CASCADE;

ALTER TABLE "langues_parlees_professionnel" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id");

ALTER TABLE "reseaux_sociaux_professionnel" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id");

ALTER TABLE "mot_cles_professionnel" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id");

ALTER TABLE "patients" ADD FOREIGN KEY ("utilisateur_id") REFERENCES "utilisateurs" ("id") ON DELETE CASCADE;

ALTER TABLE "proches" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "professionnels" ADD FOREIGN KEY ("utilisateur_id") REFERENCES "utilisateurs" ("id") ON DELETE CASCADE;

ALTER TABLE "etablissements_professionnel" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id") ON DELETE CASCADE;

ALTER TABLE "photo_professionnel" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id") ON DELETE CASCADE;

ALTER TABLE "rendez_vous" ADD FOREIGN KEY ("patient_id") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "rendez_vous" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id") ON DELETE CASCADE;

ALTER TABLE "historiques_medicaux" ADD FOREIGN KEY ("patient_id") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "historiques_medicaux" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id") ON DELETE SET NULL;

ALTER TABLE "specialites_professionnel" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id");

ALTER TABLE "messages" ADD FOREIGN KEY ("expediteur_id") REFERENCES "utilisateurs" ("id") ON DELETE CASCADE;

ALTER TABLE "messages" ADD FOREIGN KEY ("destinataire_id") REFERENCES "utilisateurs" ("id") ON DELETE CASCADE;

ALTER TABLE "administrateurs" ADD FOREIGN KEY ("utilisateur_id") REFERENCES "utilisateurs" ("id") ON DELETE CASCADE;

ALTER TABLE "journaux_activites" ADD FOREIGN KEY ("administrateur_id") REFERENCES "administrateurs" ("id") ON DELETE CASCADE;

ALTER TABLE "liste_specialites" ADD FOREIGN KEY ("id_type_etablissement") REFERENCES "liste_type_etablissement" ("id") ON DELETE CASCADE;

ALTER TABLE "AnnulerRendezVous" ADD FOREIGN KEY ("id_rendez-vous") REFERENCES "rendez_vous" ("id");

ALTER TABLE "laboratoire_diagnostics" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id");

ALTER TABLE "signe_vitaux" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id");

ALTER TABLE "consultation_medical" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id");

ALTER TABLE "consultation_medical" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id");

ALTER TABLE "urgence" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "allergie" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "medicament" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "affectation_medical" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "historique_mental_et_psychiatre" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "dispositif_medicaux" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "antecedant_chirurgicaux" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "antecedant_familliaux" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "vaccination" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "antecedant_sociaux_fumeur" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "antecedant_sociaux_alcoolique" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "contact" ADD FOREIGN KEY ("utilisateur_id") REFERENCES "utilisateurs" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_allergie") REFERENCES "allergie" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_medicament") REFERENCES "medicament" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_affectation_medical") REFERENCES "affectation_medical" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_vaccination") REFERENCES "vaccination" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_historique_mental_et_psychiatre") REFERENCES "historique_mental_et_psychiatre" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_dispositif_medicaux") REFERENCES "dispositif_medicaux" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_antecedant_chirurgicaux") REFERENCES "antecedant_chirurgicaux" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_antecedant_familliaux") REFERENCES "antecedant_familliaux" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_antecedant_sociaux_alcoolique") REFERENCES "antecedant_sociaux_alcoolique" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_antecedant_sociaux_fumeur") REFERENCES "antecedant_sociaux_fumeur" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_laboratoire_et_diagnostics") REFERENCES "laboratoire_diagnostics" ("id") ON DELETE CASCADE;

ALTER TABLE "carnet_de_sante_numerique" ADD FOREIGN KEY ("id_patient") REFERENCES "patients" ("id") ON DELETE CASCADE;

ALTER TABLE "historiques_medicaux" ADD FOREIGN KEY ("id_consultation") REFERENCES "consultation_medical" ("id");

ALTER TABLE "action_historiques" ADD FOREIGN KEY ("id_professionnel") REFERENCES "professionnels" ("id");

ALTER TABLE "professionnels" ADD FOREIGN KEY ("id_ordre_appartenance") REFERENCES "ordre_appartenance" ("id");

ALTER TABLE "historiques_medicaux" ADD FOREIGN KEY ("id_action_historique") REFERENCES "action_historiques" ("id");
