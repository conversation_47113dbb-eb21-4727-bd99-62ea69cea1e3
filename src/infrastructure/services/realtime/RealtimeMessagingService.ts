import { supabase } from "@/infrastructure/supabase/supabase";
import {
  RealtimeChannel,
  RealtimePostgresChangesPayload,
} from "@supabase/supabase-js";
import { message, conversation } from "@/domain/models";
import { MESSAGE_TABLE_NAME } from "@/infrastructure/repositories/message/Constant";
import { CONVERSATION_TABLE_NAME } from "@/infrastructure/repositories/conversation/Constant";

export type MessageChangePayload = RealtimePostgresChangesPayload<message>;
export type ConversationChangePayload =
  RealtimePostgresChangesPayload<conversation>;

export interface RealtimeCallbacks {
  onNewMessage?: (message: message) => void;
  onUpdateMessage?: (message: message) => void;
  onDeleteMessage?: (messageId: number) => void;
  onNewConversation?: (conversation: conversation) => void;
  onUpdateConversation?: (conversation: conversation) => void;
  onTyping?: (data: {
    userId: number;
    conversationId: number;
    isTyping: boolean;
  }) => void;
  onPresence?: (data: { userId: number; status: "online" | "offline" }) => void;
}

export class RealtimeMessagingService {
  private channels: Map<string, RealtimeChannel> = new Map();
  private typingTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private currentUserId: number | null = null;

  constructor(userId?: number) {
    if (userId) this.currentUserId = userId;
  }

  /** Subscribe to message changes for a specific conversation */
  public subscribeToConversation(
    conversationId: number,
    callbacks: RealtimeCallbacks
  ): void {
    const channelName = `conversation:${conversationId}`;
    this.unsubscribeFromConversation(conversationId);

    const channel = supabase
      .channel(channelName)
      // Nouveau message
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: MESSAGE_TABLE_NAME,
          filter: `id_conversation=eq.${conversationId}`,
        },
        (payload: MessageChangePayload) => {
          if (!callbacks.onNewMessage || !payload.new) return;
          const newMessage = payload.new as message;
          if (newMessage.id_expediteur !== this.currentUserId) {
            callbacks.onNewMessage(newMessage);
          }
        }
      )
      // Mise à jour message
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: MESSAGE_TABLE_NAME,
          filter: `id_conversation=eq.${conversationId}`,
        },
        (payload: MessageChangePayload) => {
          if (callbacks.onUpdateMessage && payload.new) {
            callbacks.onUpdateMessage(payload.new as message);
          }
        }
      )
      // Suppression message
      .on(
        "postgres_changes",
        {
          event: "DELETE",
          schema: "public",
          table: MESSAGE_TABLE_NAME,
          filter: `id_conversation=eq.${conversationId}`,
        },
        (payload: MessageChangePayload) => {
          if (callbacks.onDeleteMessage && payload.old) {
            const oldMessage = payload.old as { id: number };
            callbacks.onDeleteMessage(oldMessage.id);
          }
        }
      )
      // Mise à jour conversation
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: CONVERSATION_TABLE_NAME,
          filter: `id=eq.${conversationId}`,
        },
        (payload: ConversationChangePayload) => {
          if (callbacks.onUpdateConversation && payload.new) {
            callbacks.onUpdateConversation(payload.new as conversation);
          }
        }
      );

    // Typing / Presence
    if (callbacks.onTyping || callbacks.onPresence) {
      channel
        .on("presence", { event: "sync" }, () => {
          const state = channel.presenceState();
          this.handlePresenceSync(state, callbacks);
        })
        .on("broadcast", { event: "typing" }, (payload) => {
          if (callbacks.onTyping && payload.payload) {
            callbacks.onTyping(payload.payload);
          }
        });
    }

    channel.subscribe((status) => {
      if (status === "SUBSCRIBED") {
        console.log(`Subscribed to conversation ${conversationId}`);
        if (this.currentUserId) {
          channel.track({
            userId: this.currentUserId,
            online_at: new Date().toISOString(),
          });
        }
      }
    });

    this.channels.set(channelName, channel);
  }

  /** Subscribe to all conversations of a user */
  public subscribeToUserConversations(
    userId: number,
    callbacks: RealtimeCallbacks
  ): void {
    const channelName = `user:${userId}:conversations`;
    this.unsubscribeFromUserConversations(userId);

    const channel = supabase
      .channel(channelName)
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: CONVERSATION_TABLE_NAME,
          filter: `id_expediteur=eq.${userId}`,
        },
        (payload: ConversationChangePayload) => {
          if (callbacks.onNewConversation && payload.new) {
            callbacks.onNewConversation(payload.new as conversation);
          }
        }
      )
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: CONVERSATION_TABLE_NAME,
          filter: `id_recepteur=eq.${userId}`,
        },
        (payload: ConversationChangePayload) => {
          if (callbacks.onNewConversation && payload.new) {
            callbacks.onNewConversation(payload.new as conversation);
          }
        }
      )
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: CONVERSATION_TABLE_NAME,
          filter: `id_recepteur=eq.${userId}`,
        },
        (payload: ConversationChangePayload) => {
          if (callbacks.onUpdateConversation && payload.new) {
            callbacks.onUpdateConversation(payload.new as conversation);
          }
        }
      );

    // Presence globale
    if (callbacks.onPresence) {
      channel.on("presence", { event: "sync" }, () => {
        this.handlePresenceSync(channel.presenceState(), callbacks);
      });
    }

    channel.subscribe((status) => {
      if (status === "SUBSCRIBED") {
        console.log(`Subscribed to user ${userId} conversations`);
      }
    });

    this.channels.set(channelName, channel);
  }

  /** Send typing indicator */
  public sendTypingIndicator(
    conversationId: number,
    userId: number,
    isTyping: boolean
  ): void {
    const channel = this.channels.get(`conversation:${conversationId}`);
    if (!channel) return;

    channel.send({
      type: "broadcast",
      event: "typing",
      payload: { userId, conversationId, isTyping },
    });

    const timeoutKey = `${conversationId}:${userId}`;
    const existingTimeout = this.typingTimeouts.get(timeoutKey);
    if (existingTimeout) clearTimeout(existingTimeout);

    if (isTyping) {
      const timeout = setTimeout(() => {
        this.sendTypingIndicator(conversationId, userId, false);
        this.typingTimeouts.delete(timeoutKey);
      }, 3000);
      this.typingTimeouts.set(timeoutKey, timeout);
    }
  }

  /** Update user presence */
  public updatePresenceStatus(status: "online" | "offline"): void {
    if (!this.currentUserId) return;
    this.channels.forEach((channel) => {
      channel.track({
        userId: this.currentUserId,
        status,
        online_at: new Date().toISOString(),
      });
    });
  }

  /** Handle presence sync */
  private handlePresenceSync(
    state: Record<string, any>,
    callbacks: RealtimeCallbacks
  ) {
    if (!callbacks.onPresence) return;
    Object.keys(state).forEach((key) => {
      (state[key] as any[]).forEach((presence: any) => {
        if (presence && "userId" in presence) {
          callbacks.onPresence!({
            userId: presence.userId,
            status: presence.status === "offline" ? "offline" : "online",
          });
        }
      });
    });
  }

  /** Unsubscribe from conversation */
  public unsubscribeFromConversation(conversationId: number): void {
    const channelName = `conversation:${conversationId}`;
    const channel = this.channels.get(channelName);
    if (!channel) return;

    this.typingTimeouts.forEach((timeout, key) => {
      if (key.startsWith(`${conversationId}:`)) clearTimeout(timeout);
    });
    this.channels.delete(channelName);
    channel.unsubscribe();
    console.log(`Unsubscribed from conversation ${conversationId}`);
  }

  /** Unsubscribe from user conversations */
  public unsubscribeFromUserConversations(userId: number): void {
    const channelName = `user:${userId}:conversations`;
    const channel = this.channels.get(channelName);
    if (!channel) return;

    channel.unsubscribe();
    this.channels.delete(channelName);
    console.log(`Unsubscribed from user ${userId} conversations`);
  }

  /** Unsubscribe all channels */
  public unsubscribeAll(): void {
    this.typingTimeouts.forEach(clearTimeout);
    this.typingTimeouts.clear();
    this.channels.forEach((channel) => channel.unsubscribe());
    this.channels.clear();
    console.log("Unsubscribed from all channels");
  }

  /** Cleanup resources */
  public cleanup(): void {
    this.updatePresenceStatus("offline");
    this.unsubscribeAll();
  }
}
