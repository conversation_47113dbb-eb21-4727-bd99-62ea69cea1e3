import { IProfessionalEstablishmentRepository } from '@/domain/interfaces/repositories/IProfessionalEstablishmentRepository'
import { EtablissementProfessionnel } from '@/domain/models'
import { supabase } from '../supabase/supabase'
import { handleError } from '../supabase/supabaseFetchError'

class ProfessionalEstablishmentRepository implements IProfessionalEstablishmentRepository {
  private readonly TABLE_NAME = 'etablissements_professionnel'

  async getAllEstablishments(): Promise<EtablissementProfessionnel[]> {
    const { data, error } = await supabase.from(this.TABLE_NAME).select('*')

    handleError(error)

    return data
  }

  async getEstablishmentById(id: number): Promise<EtablissementProfessionnel> {
    const { data, error } = await supabase.from(this.TABLE_NAME).select('*').eq('id', id).single()

    handleError(error)

    return data
  }

  async getEstablishmentByProfessionalId(
    professionalId: number
  ): Promise<EtablissementProfessionnel> {
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*')
      .eq('id_professionnel', professionalId)
      .single()

    handleError(error)

    return data
  }

  async createEstablishment(
    establishment: Omit<EtablissementProfessionnel, 'id'>
  ): Promise<EtablissementProfessionnel> {
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .insert(establishment)
      .select()
      .single()

    handleError(error)

    return data
  }

  async updateEstablishment(
    id: number,
    data: Partial<EtablissementProfessionnel>
  ): Promise<EtablissementProfessionnel> {
    const { data: updatedData, error } = await supabase
      .from(this.TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return updatedData
  }

  async deleteEstablishment(id: number) {
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as EtablissementProfessionnel
  }
}
export const professionalEstablishmentRepository = new ProfessionalEstablishmentRepository()
