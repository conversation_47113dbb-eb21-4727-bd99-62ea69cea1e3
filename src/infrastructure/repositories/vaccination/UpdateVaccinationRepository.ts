import { Vaccination } from '@/domain/models'
import { IUpdateVaccinationRepository } from '@/domain/interfaces/repositories/vaccination'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { VACCINATION_TABLE_NAME } from './Constant'

export class UpdateVaccinationRepository implements IUpdateVaccinationRepository {
  async update(id: number, data: Partial<Vaccination>): Promise<Vaccination> {
    const { data: updatedData, error } = await supabase
      .from(VACCINATION_TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      handleError(error)
    }

    return updatedData
  }
}
