import { Vaccination } from '@/domain/models'
import { ICreateVaccinationRepository } from '@/domain/interfaces/repositories/vaccination'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { VACCINATION_TABLE_NAME } from './Constant'

export class CreateVaccinationRepository implements ICreateVaccinationRepository {
  async create(data: Omit<Vaccination, "id">[]): Promise<Vaccination[]> {
    const { data: createdData, error } = await supabase
      .from(VACCINATION_TABLE_NAME)
      .insert(data)
      .select()

    if (error) {
      handleError(error)
    }

    return createdData as Vaccination[]
  }
}
