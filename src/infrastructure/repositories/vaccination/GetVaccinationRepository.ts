import { Vaccination } from '@/domain/models'
import { IGetVaccinationRepository } from '@/domain/interfaces/repositories/vaccination'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { VACCINATION_TABLE_NAME } from './Constant'

export class GetVaccinationRepository implements IGetVaccinationRepository {
  async getById(id: number): Promise<Vaccination | null> {
    const { data, error } = await supabase
      .from(VACCINATION_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      handleError(error)
    }

    return data
  }

  async getAll(carnetId: number): Promise<Vaccination[]> {
    const { data, error } = await supabase
      .from(VACCINATION_TABLE_NAME)
      .select('*')
      .eq('id_carnet', carnetId)

    if (error) {
      handleError(error)
    }

    return data || []
  }
}
