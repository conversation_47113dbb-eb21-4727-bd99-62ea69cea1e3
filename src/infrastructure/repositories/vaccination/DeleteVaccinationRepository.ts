import { IDeleteVaccinationRepository } from '@/domain/interfaces/repositories/vaccination'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { VACCINATION_TABLE_NAME } from './Constant'

export class DeleteVaccinationRepository implements IDeleteVaccinationRepository {
  async delete(id: number): Promise<void> {
    const { error } = await supabase
      .from(VACCINATION_TABLE_NAME)
      .delete()
      .eq('id', id)

    if (error) {
      handleError(error)
    }
  }
}
