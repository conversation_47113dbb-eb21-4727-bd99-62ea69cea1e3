import { supabase } from "@/infrastructure/supabase/supabase";
import { ENTREES_STOCKS_TABLE_NAME } from "./constants";
import { IDeleteEntreesStocksRepository } from "@/domain/interfaces/repositories/entreesStocks/IDeleteEntreesStocksRepository";

class DeleteEntreesStocksRepository implements IDeleteEntreesStocksRepository {
  constructor() {}

  async execute(id: number): Promise<boolean> {
    const { error } = await supabase
      .from(ENTREES_STOCKS_TABLE_NAME)
      .delete()
      .eq("id", id);

    if (error) throw error;

    return true;
  }
}

export default DeleteEntreesStocksRepository;
