import { EntreesStocks } from "@/domain/models/EntreesStocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ENTREES_STOCKS_TABLE_NAME } from "./constants";
import { IGetEntreesStocksRepository } from "@/domain/interfaces/repositories/entreesStocks/IGetEntreesStocksRepository";

class GetEntreesStocksRepository implements IGetEntreesStocksRepository {
  constructor() {}

  async execute(professionalId: number): Promise<EntreesStocks[]> {
    const { data, error } = await supabase
      .from(ENTREES_STOCKS_TABLE_NAME)
      .select(
        `
        *,
        stocks!inner(utilisateur_id)
      `
      )
      .eq("stocks.utilisateur_id", professionalId)
      .order("date_entree", { ascending: false });

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as EntreesStocks[];
  }
}

export default GetEntreesStocksRepository;
