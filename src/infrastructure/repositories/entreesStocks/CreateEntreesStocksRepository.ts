import { EntreesStocks } from "@/domain/models/EntreesStocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ENTREES_STOCKS_TABLE_NAME } from "./constants";
import { ICreateEntreesStocksRepository } from "@/domain/interfaces/repositories/entreesStocks/ICreateEntreesStocksRepository";

class CreateEntreesStocksRepository implements ICreateEntreesStocksRepository {
  constructor() {}

  async execute(entreesStocks: Omit<EntreesStocks, "id">[]): Promise<EntreesStocks[]> {
    const { data, error } = await supabase
      .from(ENTREES_STOCKS_TABLE_NAME)
      .insert(entreesStocks)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as EntreesStocks[];
  }
}

export default CreateEntreesStocksRepository;
