import { EntreesStocks } from "@/domain/models/EntreesStocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ENTREES_STOCKS_TABLE_NAME } from "./constants";
import { IUpdateEntreesStocksRepository } from "@/domain/interfaces/repositories/entreesStocks/IUpdateEntreesStocksRepository";

class UpdateEntreesStocksRepository implements IUpdateEntreesStocksRepository {
  constructor() {}

  async execute(id: number, entreesStocks: Partial<Omit<EntreesStocks, "id">>): Promise<EntreesStocks | null> {
    const { data, error } = await supabase
      .from(ENTREES_STOCKS_TABLE_NAME)
      .update(entreesStocks)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // Aucun enregistrement trouvé
      throw error;
    }

    return data as EntreesStocks;
  }
}

export default UpdateEntreesStocksRepository;
