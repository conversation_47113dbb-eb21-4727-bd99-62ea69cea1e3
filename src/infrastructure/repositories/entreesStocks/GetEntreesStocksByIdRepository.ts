import { EntreesStocks } from "@/domain/models/EntreesStocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ENTREES_STOCKS_TABLE_NAME } from "./constants";
import { IGetEntreesStocksByIdRepository } from "@/domain/interfaces/repositories/entreesStocks/IGetEntreesStocksByIdRepository";

class GetEntreesStocksByIdRepository implements IGetEntreesStocksByIdRepository {
  constructor() {}

  async execute(id: number): Promise<EntreesStocks | null> {
    const { data, error } = await supabase
      .from(ENTREES_STOCKS_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // Aucun enregistrement trouvé
      throw error;
    }

    return data as EntreesStocks;
  }
}

export default GetEntreesStocksByIdRepository;
