import { EntreesStocks } from "@/domain/models/EntreesStocks.ts";
import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { ENTREES_STOCKS_TABLE_NAME } from "./constants.ts";
import { IGetEntreeStocksByStockIdRepository } from "@/domain/interfaces/repositories/entreesStocks/IGetEntreeStocksByStockIdRepository.ts";

class GetEntreeStockByStockIdRepository
  implements IGetEntreeStocksByStockIdRepository
{
  constructor() {}

  async execute(stockId: number) {
    const { data, error } = await supabase
      .from(ENTREES_STOCKS_TABLE_NAME)
      .select("*")
      .eq("stock_id", stockId)
      .gte("quantite", 0)
      .order("date_entree", { ascending: false });

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as EntreesStocks[];
  }
}

export default GetEntreeStockByStockIdRepository;
