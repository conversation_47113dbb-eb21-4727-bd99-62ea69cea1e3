import { supabase } from "@/infrastructure/supabase/supabase";
import { FOURNISSEURS_TABLE_NAME } from "./constants";
import { IDeleteFournisseurRepository } from "@/domain/interfaces/repositories/fournisseurs/IDeleteFournisseurRepository";

/**
 * Repository pour la suppression d'un fournisseur
 * Implémente l'interface IDeleteFournisseurRepository
 */
class DeleteFournisseurRepository implements IDeleteFournisseurRepository {
  /**
   * Supprime un fournisseur de la base de données
   * @param id - L'ID du fournisseur à supprimer
   * @throws Error si la suppression échoue ou si le fournisseur n'existe pas
   */
  async execute(id: number): Promise<void> {
    const { error } = await supabase
      .from(FOURNISSEURS_TABLE_NAME)
      .delete()
      .eq("id", id);

    if (error) {
      throw new Error(`Erreur lors de la suppression du fournisseur: ${error.message}`);
    }
  }
}

export default DeleteFournisseurRepository;
