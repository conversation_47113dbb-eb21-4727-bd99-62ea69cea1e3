import { Fournisseurs } from "@/domain/models/Fournisseurs";
import { supabase } from "@/infrastructure/supabase/supabase";
import { FOURNISSEURS_TABLE_NAME } from "./constants";
import { IUpdateFournisseurRepository } from "@/domain/interfaces/repositories/fournisseurs/IUpdateFournisseurRepository";

/**
 * Repository pour la mise à jour d'un fournisseur
 * Implémente l'interface IUpdateFournisseurRepository
 */
class UpdateFournisseurRepository implements IUpdateFournisseurRepository {
  /**
   * Met à jour un fournisseur existant dans la base de données
   * @param id - L'ID du fournisseur à mettre à jour
   * @param fournisseur - Les données partielles du fournisseur à modifier
   * @returns Le fournisseur mis à jour
   * @throws Error si la mise à jour échoue ou si le fournisseur n'existe pas
   */
  async execute(id: number, fournisseur: Partial<Omit<Fournisseurs, "id">>): Promise<Fournisseurs> {
    const { data, error } = await supabase
      .from(FOURNISSEURS_TABLE_NAME)
      .update(fournisseur)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw new Error(`Erreur lors de la mise à jour du fournisseur: ${error.message}`);
    }

    if (!data) {
      throw new Error(`Fournisseur avec l'ID ${id} non trouvé`);
    }

    return data;
  }
}

export default UpdateFournisseurRepository;
