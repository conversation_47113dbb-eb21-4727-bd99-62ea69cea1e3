import { Fournisseurs } from "@/domain/models/Fournisseurs";
import { supabase } from "@/infrastructure/supabase/supabase";
import { FOURNISSEURS_TABLE_NAME } from "./constants";
import { ICreateFournisseurRepository } from "@/domain/interfaces/repositories/fournisseurs/ICreateFournisseurRepository";

/**
 * Repository pour la création d'un fournisseur
 * Implémente l'interface ICreateFournisseurRepository
 */
class CreateFournisseurRepository implements ICreateFournisseurRepository {
  /**
   * Crée un nouveau fournisseur dans la base de données
   * @param fournisseur - Les données du fournisseur à créer (sans l'ID)
   * @returns Le fournisseur créé avec son ID
   * @throws Error si la création échoue
   */
  async execute(fournisseur: Omit<Fournisseurs, "id">): Promise<Fournisseurs> {
    const { data, error } = await supabase
      .from(FOURNISSEURS_TABLE_NAME)
      .insert(fournisseur)
      .select()
      .single();

    if (error) {
      throw new Error(
        `Erreur lors de la création du fournisseur: ${error.message}`
      );
    }

    if (!data) {
      throw new Error(
        "Aucune donnée retournée après la création du fournisseur"
      );
    }

    return data;
  }
}

export default CreateFournisseurRepository;
