import { Fournisseurs } from "@/domain/models/Fournisseurs";
import { supabase } from "@/infrastructure/supabase/supabase";
import { FOURNISSEURS_TABLE_NAME } from "./constants";
import { IGetFournisseurByIdRepository } from "@/domain/interfaces/repositories/fournisseurs/IGetFournisseurByIdRepository";

/**
 * Repository pour la récupération d'un fournisseur par son ID
 * Implémente l'interface IGetFournisseurByIdRepository
 */
class GetFournisseurByIdRepository implements IGetFournisseurByIdRepository {
  /**
   * Récupère un fournisseur par son ID
   * @param id - L'ID du fournisseur à récupérer
   * @returns Le fournisseur trouvé ou null si non trouvé
   * @throws Error si la requête échoue
   */
  async execute(id: number): Promise<Fournisseurs | null> {
    const { data, error } = await supabase
      .from(FOURNISSEURS_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      // Si l'erreur est "PGRST116" (pas de ligne trouvée), retourner null
      if (error.code === "PGRST116") {
        return null;
      }
      throw new Error(`Erreur lors de la récupération du fournisseur: ${error.message}`);
    }

    return data;
  }
}

export default GetFournisseurByIdRepository;
