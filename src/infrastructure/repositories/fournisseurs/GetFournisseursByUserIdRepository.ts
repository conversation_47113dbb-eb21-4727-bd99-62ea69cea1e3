import { Fournisseurs } from "@/domain/models/Fournisseurs";
import { supabase } from "@/infrastructure/supabase/supabase";
import { FOURNISSEURS_TABLE_NAME } from "./constants";
import { IGetFournisseursByUserIdRepository } from "@/domain/interfaces/repositories/fournisseurs/IGetFournisseursByUserIdRepository";

/**
 * Repository pour la récupération des fournisseurs par utilisateur_id
 * Implémente l'interface IGetFournisseursByUserIdRepository
 */
class GetFournisseursByUserIdRepository implements IGetFournisseursByUserIdRepository {
  /**
   * Récupère tous les fournisseurs d'un utilisateur spécifique
   * @param utilisateurId - L'ID de l'utilisateur
   * @returns La liste des fournisseurs de l'utilisateur
   * @throws Error si la requête échoue
   */
  async execute(utilisateurId: number): Promise<Fournisseurs[]> {
    const { data, error } = await supabase
      .from(FOURNISSEURS_TABLE_NAME)
      .select("*")
      .eq("utilisateur_id", utilisateurId)
      .order("nom", { ascending: true });

    if (error) {
      throw new Error(`Erreur lors de la récupération des fournisseurs: ${error.message}`);
    }

    return data || [];
  }
}

export default GetFournisseursByUserIdRepository;
