import { ExpertiseProfessionnel } from "@/domain/models/ExpertiseProfessionnel";
import { supabase } from "@/infrastructure/supabase/supabase";
import { EXPERTISE_PROFESSIONNEL_TABLE_NAME } from "./constants";
import { ICreateExpertiseProfessionnelRepository } from "@/domain/interfaces/repositories/expertiseProfessionnel/ICreateExpertiseProfessionnelRepository";

export class CreateExpertiseProfessionnelRepository implements ICreateExpertiseProfessionnelRepository {
  async execute(expertiseProfessionnel: Omit<ExpertiseProfessionnel, "id">): Promise<ExpertiseProfessionnel> {
    const { data, error } = await supabase
      .from(EXPERTISE_PROFESSIONNEL_TABLE_NAME)
      .insert(expertiseProfessionnel)
      .select();
    
    if (error) throw error;
    return data[0] as ExpertiseProfessionnel;
  }
}
