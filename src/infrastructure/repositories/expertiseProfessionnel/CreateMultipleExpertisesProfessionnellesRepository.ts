import { ExpertiseProfessionnel } from "@/domain/models/ExpertiseProfessionnel";
import { supabase } from "@/infrastructure/supabase/supabase";
import { EXPERTISE_PROFESSIONNEL_TABLE_NAME } from "./constants";
import { ICreateMultipleExpertisesProfessionnellesRepository } from "@/domain/interfaces/repositories/expertiseProfessionnel/ICreateMultipleExpertisesProfessionnellesRepository";

export class CreateMultipleExpertisesProfessionnellesRepository implements ICreateMultipleExpertisesProfessionnellesRepository {
  async execute(expertisesProfessionnelles: Omit<ExpertiseProfessionnel, "id">[]): Promise<ExpertiseProfessionnel[]> {
    const { data, error } = await supabase
      .from(EXPERTISE_PROFESSIONNEL_TABLE_NAME)
      .insert(expertisesProfessionnelles)
      .select();
    
    if (error) throw error;
    return data || [];
  }
}
