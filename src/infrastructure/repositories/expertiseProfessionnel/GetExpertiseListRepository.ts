import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { EXPERTISE_LIST_TABLE_NAME } from "./constants.ts";
import { ListeExpertises } from "@/domain/models/ListeExpertises.ts";
import { IGetExpertiseListRepository } from "@/domain/interfaces/repositories/expertiseProfessionnel/IGetExpertiseListRepository.ts";

class GetExpertiseListRepository implements IGetExpertiseListRepository {
  constructor() {}

  async execute() {
    const { data, error } = await supabase
      .from(EXPERTISE_LIST_TABLE_NAME)
      .select("id, nom");

    if (error) throw error;

    return data as ListeExpertises[];
  }
}

export default GetExpertiseListRepository;
