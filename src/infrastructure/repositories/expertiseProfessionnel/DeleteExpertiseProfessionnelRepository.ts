import { supabase } from "@/infrastructure/supabase/supabase";
import { EXPERTISE_PROFESSIONNEL_TABLE_NAME } from "./constants";
import { IDeleteExpertiseProfessionnelRepository } from "@/domain/interfaces/repositories/expertiseProfessionnel/IDeleteExpertiseProfessionnelRepository";

export class DeleteExpertiseProfessionnelRepository implements IDeleteExpertiseProfessionnelRepository {
  async execute(id: number): Promise<void> {
    const { error } = await supabase
      .from(EXPERTISE_PROFESSIONNEL_TABLE_NAME)
      .delete()
      .eq("id", id);

    if (error) throw error;
  }
}
