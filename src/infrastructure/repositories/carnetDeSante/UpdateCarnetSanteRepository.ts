import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { CARNET_SANTE_TABLE_NAME } from './Constant'
import { CarnetSante } from '@/domain/models'
import { IUpdateCarnetSanteRepository } from '@/domain/interfaces/repositories/carnetSante'

export class UpdateCarnetSanteRepository implements IUpdateCarnetSanteRepository {
  async execute(patientId: number, dataCarnetSante: Partial<CarnetSante>): Promise<CarnetSante> {
    const { data, error } = await supabase
      .from(CARNET_SANTE_TABLE_NAME)
      .update(dataCarnetSante)
      .eq('id_patient', patientId)

    handleError(error)

    return data as CarnetSante
  }
}
