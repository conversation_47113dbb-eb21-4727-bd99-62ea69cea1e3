import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { CARNET_SANTE_TABLE_NAME } from './Constant'
import { IDeleteCarnetSanteRepository } from '@/domain/interfaces/repositories/carnetSante/IDeleteCarnetSanteRepository'
import { CarnetSante } from '@/domain/models'

export class DeleteCarnetSanteRepository implements IDeleteCarnetSanteRepository {
  async execute(id: number): Promise<CarnetSante> {
    const { data, error } = await supabase
      .from(CARNET_SANTE_TABLE_NAME)
      .delete()
      .eq('id', id)

    handleError(error)

    return data as CarnetSante
  }
}
