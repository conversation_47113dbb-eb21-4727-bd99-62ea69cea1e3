import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { CARNET_SANTE_TABLE_NAME } from './Constant'
import { ICreateCarnetSanteRepository } from '@/domain/interfaces/repositories/carnetSante'
import { CarnetSante } from '@/domain/models'

export class CreateCarnetSanteRepository implements ICreateCarnetSanteRepository {
  async execute(
    CarnetSanteData: Omit<CarnetSante, 'id'>
  ): Promise<CarnetSante> {
    const { data, error } = await supabase
      .from(CARNET_SANTE_TABLE_NAME)
      .insert(CarnetSanteData)
      .select()
      .single()

    handleError(error)

    return data as CarnetSante
  }
}
