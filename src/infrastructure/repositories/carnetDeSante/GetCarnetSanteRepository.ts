import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { CARNET_SANTE_TABLE_NAME } from "./Constant";
import { IGetCarnetSanteRepository } from "@/domain/interfaces/repositories/carnetSante";
import { CarnetSanteDTO } from "@/domain/DTOS";

export class GetCarnetSanteRepository implements IGetCarnetSanteRepository {
  async execute(proprietaireId: number): Promise<CarnetSanteDTO> {
    const { data, error } = await supabase
      .from(CARNET_SANTE_TABLE_NAME)
      .select(
        `
        *,
        allergie(*),
        medicament(*),
        affectation_medical(*),
        dispositif_medicaux(*),
        antecedant_chirurgicaux(*),
        antecedant_familliaux(*),
        antecedant_sociaux_alcoolique(*),
        antecedant_sociaux_fumeur(*),
        vaccination(*)
      `
      )
      .eq("id_proprietaire", proprietaireId)
      .maybeSingle();

    handleError(error);

    return data as CarnetSanteDTO;
  }
}
