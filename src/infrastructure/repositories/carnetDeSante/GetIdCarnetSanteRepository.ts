import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { CARNET_SANTE_TABLE_NAME } from "./Constant";
import { IGetIdCarnetSanteRepository } from "@/domain/interfaces/repositories/carnetSante";

export class GetIdCarnetSanteRepository implements IGetIdCarnetSanteRepository {
  async execute(proprietaireId: number): Promise<number> {
    const { data, error } = await supabase
      .from(CARNET_SANTE_TABLE_NAME)
      .select("id")
      .eq("id_proprietaire", proprietaireId);

    handleError(error);

    if (!data) return null;

    return data[0].id as number;
  }
}
