import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { CARNET_SANTE_TABLE_NAME } from "./Constant";
import { IGetIdCarnetSanteByEmployedIDRepository } from "@/domain/interfaces/repositories/carnetSante";

export class GetIdCarnetSanteByEmployedIDRepository
  implements IGetIdCarnetSanteByEmployedIDRepository
{
  async execute(employerId: number): Promise<number> {
    const { data, error } = await supabase
      .from(CARNET_SANTE_TABLE_NAME)
      .select("id")
      .eq("id_employer", employerId)
      .single();

    handleError(error);

    return data.id as number;
  }
}
