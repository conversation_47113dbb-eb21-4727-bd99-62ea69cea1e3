import { ICreateProfessionalPublicationRepository } from "@/domain/interfaces/repositories/professionalPublication";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_PUBLICATION_TABLE_NAME } from "./constants";

export class CreateProfessionalPublicationRepository
  implements ICreateProfessionalPublicationRepository
{
  async execute(
    publication: Omit<PublicationProfessionnel, "id">[]
  ): Promise<PublicationProfessionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_PUBLICATION_TABLE_NAME)
      .insert(publication)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as PublicationProfessionnel[];
  }
}
