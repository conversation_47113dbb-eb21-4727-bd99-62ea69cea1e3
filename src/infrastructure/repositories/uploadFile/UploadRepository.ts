import { supabase } from "@/infrastructure/supabase/supabase";
import { STORAGE_BUCKET } from "./Constant";
import { IUploadRepository } from "@/domain/interfaces/repositories/uploadFile";

export class UploadRepository implements IUploadRepository {
  async execute(fileName: string, file: File): Promise<void> {
    // Upload du fichier
    const { data, error } = await supabase.storage
      .from(STORAGE_BUCKET)
      .upload(fileName, file, {
        cacheControl: "3600",
        upsert: false,
      });

    if (error) throw error;

    if (!data) {
      throw new Error("Erreur lors de l'upload de l'image");
    }
  }
}