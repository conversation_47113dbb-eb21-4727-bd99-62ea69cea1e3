import { supabase } from "@/infrastructure/supabase/supabase";
import { STORAGE_BUCKET } from "./Constant";
import { IDeleteImageRepository } from "@/domain/interfaces/repositories/uploadFile";

export class DeleteImageRepository implements IDeleteImageRepository {
  async execute(fileName: string): Promise<void> {
    const { error } = await supabase.storage
      .from(STORAGE_BUCKET)
      .remove([fileName]);

    if (error) throw error;
  }
}