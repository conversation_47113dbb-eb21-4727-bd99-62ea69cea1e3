import { supabase } from "@/infrastructure/supabase/supabase";
import { STORAGE_BUCKET } from "./Constant";
import { IGetPublicUrlRepository } from "@/domain/interfaces/repositories/uploadFile";

export class GetPublicUrlRepository implements IGetPublicUrlRepository {
  async execute(fileName: string): Promise<string> {
    // Récupérer l'URL publique du fichier
    const { data: publicUrl } = supabase.storage
      .from(STORAGE_BUCKET)
      .getPublicUrl(fileName);

    console.log("Upload successful. Public URL:", publicUrl.publicUrl);

    return publicUrl.publicUrl;
  }
}