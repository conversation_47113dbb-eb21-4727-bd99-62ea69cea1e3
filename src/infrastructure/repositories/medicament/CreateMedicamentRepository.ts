import { Medicament } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { MEDICAMENT_TABLE_NAME } from './Constant'
import { ICreateMedicamentRepository } from '@/domain/interfaces/repositories/medicament'

export class CreateMedicamentRepository implements ICreateMedicamentRepository {
  async execute(
    MedicamentData: Omit<Medicament, 'id'>[]
  ): Promise<Medicament[]> {
    const { data, error } = await supabase
      .from(MEDICAMENT_TABLE_NAME)
      .insert(MedicamentData)
      .select()

    handleError(error)

    return data as Medicament[]
  }
}
