import { supabase } from "@/infrastructure/supabase/supabase";
import { MEDICAMENT_LIST_TABLE_NAME } from "./Constant";
import { IGetMedicamentListCompleteRepository } from "@/domain/interfaces/repositories/medicament/IGetMedicamentListCompleteRepository";
import { ListeMedicaments } from "@/domain/models";

class GetMedicamentListcompleteRepository implements IGetMedicamentListCompleteRepository{
    constructor() {}

    async execute() {
        const {data, error} = await supabase.from(MEDICAMENT_LIST_TABLE_NAME).select("*");

        if (error) throw error;

        return data as ListeMedicaments[];
    }
}

export default GetMedicamentListcompleteRepository