import { Medicament } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { MEDICAMENT_TABLE_NAME } from './Constant'
import { IDeleteMedicamentRepository } from '@/domain/interfaces/repositories/medicament'

export class DeleteMedicamentRepository implements IDeleteMedicamentRepository {
  async execute(id: number): Promise<Medicament> {
    const { data, error } = await supabase
      .from(MEDICAMENT_TABLE_NAME)
      .delete()
      .eq('id', id)

    handleError(error)

    return data as Medicament
  }
}
