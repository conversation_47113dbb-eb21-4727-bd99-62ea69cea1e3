import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { MEDICAMENT_TABLE_NAME } from './Constant'
import { IUpdateMedicamentRepository } from '@/domain/interfaces/repositories/medicament'
import { Medicament } from '@/domain/models'

export class UpdateMedicamentRepository implements IUpdateMedicamentRepository {
  async execute(medicamentId: number, dataMedicament: Partial<Medicament>): Promise<Medicament> {
    const { data, error } = await supabase
      .from(MEDICAMENT_TABLE_NAME)
      .update(dataMedicament)
      .eq('id', medicamentId)
      .select()
      .single()

    handleError(error)

    return data as Medicament
  }
}
