import { supabase } from '@/infrastructure/supabase/supabase'
import { IGetAllMedicamentRepository } from "@/domain/interfaces/repositories/medicament";
import { MEDICAMENT_TABLE_NAME } from "./Constant";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { Medicament } from "@/domain/models";

export class GetAllMedicamentRepository implements IGetAllMedicamentRepository {
  async execute(carnetId: number): Promise<Medicament[]> {
    const { data, error } = await supabase
      .from(MEDICAMENT_TABLE_NAME)
      .select("*")
      .eq("id_carnet", carnetId);

    handleError(error)

    return data as Medicament[]
  }
}