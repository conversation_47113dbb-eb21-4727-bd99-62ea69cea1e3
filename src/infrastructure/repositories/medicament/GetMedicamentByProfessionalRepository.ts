import { supabase } from "@/infrastructure/supabase/supabase";
import { MEDICAMENT_TABLE_NAME } from "./Constant";
import {
    IGetMedicamentByProfessionalRepository,
    MedicamentProfessionalStats
} from "@/domain/interfaces/repositories/medicament";

class GetMedicamentByProfessionalRepository implements IGetMedicamentByProfessionalRepository {
    async execute(professionalId: number): Promise<MedicamentProfessionalStats[]> {
        const { data, error } = await supabase
            .from(MEDICAMENT_TABLE_NAME)
            .select(`
                nom,
                carnet_sante!inner(
                    id,
                    utilisateurs!inner(
                        id,
                        role,
                        patients!inner(
                            id,
                            professionnel_patient!inner(
                                id_professionnel,
                                is_delete
                            )
                        )
                    )
                )
            `)
            .eq('carnet_sante.utilisateurs.patients.professionnel_patient.id_professionnel', professionalId)
            .eq('carnet_sante.utilisateurs.patients.professionnel_patient.is_delete', false);

        if (error) throw error;

        // Group by nom and count repetitions manually
        const groupedData = (data || []).reduce((acc, item) => {
            const nom = item.nom;
            if (acc[nom]) {
                acc[nom]++;
            } else {
                acc[nom] = 1;
            }
            return acc;
        }, {} as Record<string, number>);

        // Transform to array format and sort by repetitions DESC
        return Object.entries(groupedData)
            .map(([nom, repetitions]) => ({
                nom,
                repetitions
            }))
            .sort((a, b) => b.repetitions - a.repetitions);
    }
}

export default GetMedicamentByProfessionalRepository;