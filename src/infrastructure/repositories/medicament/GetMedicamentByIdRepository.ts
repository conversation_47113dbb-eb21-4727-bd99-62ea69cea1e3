import { supabase } from '@/infrastructure/supabase/supabase'
import { IGetMedicamentByIdRepository } from "@/domain/interfaces/repositories/medicament";
import { MEDICAMENT_TABLE_NAME } from "./Constant";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { Medicament } from "@/domain/models";

export class GetMedicamentByIdRepository implements IGetMedicamentByIdRepository {
  async execute(id: number): Promise<Medicament> {
    const { data, error } = await supabase
      .from(MEDICAMENT_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    handleError(error)

    return data as Medicament
  }
}