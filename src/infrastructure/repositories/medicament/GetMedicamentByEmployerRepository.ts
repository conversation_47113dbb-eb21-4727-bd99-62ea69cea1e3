import { supabase } from "@/infrastructure/supabase/supabase";
import { MEDICAMENT_TABLE_NAME } from "./Constant";
import {
    IGetMedicamentByEmployerRepository,
    MedicamentEmployerStats
} from "@/domain/interfaces/repositories/medicament/IGetMedicamentByEmployerRepository";

class GetMedicamentByEmployerRepository implements IGetMedicamentByEmployerRepository {
    async execute(): Promise<MedicamentEmployerStats[]> {
        const { data, error } = await supabase
            .from(MEDICAMENT_TABLE_NAME)
            .select(`
        nom,
        carnet_sante!inner(
          utilisateurs!inner(role)
        )
      `)
            .eq('carnet_sante.utilisateurs.role', 'employer');

        if (error) throw error;

        // Group by nom and count repetitions manually
        const groupedData = (data || []).reduce((acc, item) => {
            const nom = item.nom;
            if (acc[nom]) {
                acc[nom]++;
            } else {
                acc[nom] = 1;
            }
            return acc;
        }, {} as Record<string, number>);

        // Transform to array format
        return Object.entries(groupedData).map(([nom, repetitions]) => ({
            nom,
            repetitions
        }));
    }
}

export default GetMedicamentByEmployerRepository;