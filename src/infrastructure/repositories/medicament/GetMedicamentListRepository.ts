import { Medicament } from "@/domain/models/Medicament.ts";
import { GET_LIMIT, MEDICAMENT_LIST_TABLE_NAME } from "./Constant.ts";
import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { IGetMedicamentListRepository } from "@/domain/interfaces/repositories/medicament/IGetMedicamentListRepository.ts";

class GetMedicamentListRepository implements IGetMedicamentListRepository {
  async execute(name: string): Promise<Medicament[]> {
    const { data, error } = await supabase
      .from(MEDICAMENT_LIST_TABLE_NAME)
      .select("*")
      .ilike("nom", `%${name}%`)
      .limit(GET_LIMIT);

    if (error) throw error;

    return data as Medicament[];
  }
}

export default GetMedicamentListRepository;
