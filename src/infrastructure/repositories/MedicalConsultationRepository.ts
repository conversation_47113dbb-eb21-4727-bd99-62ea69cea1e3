import { IMedicalConsultationRepository } from '@/domain/interfaces/repositories/IMedicalConsultationRepository'
import { consultation_medical } from '@/domain/models'
import { supabase } from '../supabase/supabase'
import { handleError } from '../supabase/supabaseFetchError'

export class MedicalConsultationRepository implements IMedicalConsultationRepository {
  private readonly CONSULTATION_TABLE_NAME = 'consultation_medical'

  async getConsultation(id: number): Promise<consultation_medical> {
    const { data, error } = await supabase
      .from(this.CONSULTATION_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single()

    handleError(error)

    return data
  }

  async getConsultationsByProfessionalId(professionalId: number): Promise<consultation_medical[]> {
    const { data, error } = await supabase
      .from(this.CONSULTATION_TABLE_NAME)
      .select('*')
      .eq('id_professionnel', professionalId)

    handleError(error)

    return data
  }

  async getConsultationsByPatientId(patientId: number): Promise<consultation_medical[]> {
    const { data, error } = await supabase
      .from(this.CONSULTATION_TABLE_NAME)
      .select('*')
      .eq('id_patient', patientId)

    handleError(error)

    return data
  }

  async createConsultation(
    consultationData: Omit<consultation_medical, 'id'>
  ): Promise<consultation_medical> {
    const { data, error } = await supabase
      .from(this.CONSULTATION_TABLE_NAME)
      .insert(consultationData)
      .select()
      .single()

    handleError(error)

    return data
  }

  async updateConsultation(
    id: number,
    updateData: Partial<consultation_medical>
  ): Promise<consultation_medical> {
    if (!id) {
      throw new Error("L'id est requis pour la mise à jour.")
    }

    const { data, error } = await supabase
      .from(this.CONSULTATION_TABLE_NAME)
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data
  }

  async deleteConsultation(id: number): Promise<consultation_medical> {
    if (!id) {
      throw new Error("L'id est requis pour la suppression.")
    }

    const { data, error } = await supabase
      .from(this.CONSULTATION_TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data
  }
}

export const medicalConsultationRepository = new MedicalConsultationRepository()
