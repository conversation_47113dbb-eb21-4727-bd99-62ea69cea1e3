// SearchProfessionalsRepository.ts
import { supabase } from "../supabase/supabase";
import { SearchProfessionalDTO } from "@/domain/DTOS/ProfessionalDTO";
import type {
  ISearchProfessionalsRepository,
  SearchProfessionalsRepositoryParams,
} from "@/domain/interfaces/repositories/ISearchProfessionalsRepository";

const PROFESSIONAL_TABLE_NAME = "professionnels";

class SearchProfessionalsRepository implements ISearchProfessionalsRepository {
  /**
   * Exécute la requête contre Supabase/PostgREST.
   * Ne contient pas de logique métier (ex: filtrage de rendez_vous par date, recherche dans relations).
   */
  async execute(
    params: SearchProfessionalsRepositoryParams
  ): Promise<SearchProfessionalDTO[]> {
    const { name, localization, page = 0, limit = 50 } = params;

    // Construit les conditions de recherche dynamiquement
    const orConditions: string[] = [];

    if (name && name.trim() !== "") {
      const escaped = name.replace(/%/g, "\\%").replace(/'/g, "''");

      // Recherche dans les champs du professionnel uniquement
      // Les recherches dans les tables liées seront faites côté UseCase
      orConditions.push(`nom.ilike.%${escaped}%`);
      orConditions.push(`prenom.ilike.%${escaped}%`);
      orConditions.push(`raison_sociale.ilike.%${escaped}%`);
      orConditions.push(`numero_ordre.ilike.%${escaped}%`);
      orConditions.push(`presentation_generale.ilike.%${escaped}%`);
    }

    if (localization && localization.trim() !== "") {
      const loc = localization.replace(/%/g, "\\%").replace(/'/g, "''");

      // Recherche dans les champs géographiques du professionnel uniquement
      orConditions.push(`adresse.ilike.%${loc}%`);
      orConditions.push(`region.ilike.%${loc}%`);
      orConditions.push(`district.ilike.%${loc}%`);
      orConditions.push(`commune.ilike.%${loc}%`);
      orConditions.push(`fokontany.ilike.%${loc}%`);
    }

    // Requête SELECT optimisée avec les relations nécessaires
    const selectQuery = `
      *,
      rendez_vous(
        id,
        patient_id,
        date_rendez_vous,
        motif,
        raison,
        categorie,
        statut,
        est_absent
      ),
      specialites_professionnel(
        id,
        nom_specialite,
        type_etablissement
      ),
      etablissements_professionnel(
        id,
        nom_etablissement,
        nom_responsable,
        prenom_responsable,
        equipe,
        est_supprimmee
      ),
      mot_cles_professionnel(
        id,
        symptome,
        id_professionnel
      ),
      parametre_disponibilite(
        id,
        id_professionnel,
        type,
        date_debut,
        date_fin,
        duree_pause,
        max_rdv_par_jours,
        peut_inviter_autre,
        temps_moyen_consulation,
        horaire_hebdomadaire(
          id,
          id_parametre_disponibilite,
          jour,
          creneau_horaire(
            id,
            heure_debut,
            heure_fin,
            id_horaire_hebdomadaire
          )
        ),
        horaire_date_specifique(
          id,
          id_parametre_disponibilite,
          date,
          est_specifique,
          creneau_horaire(
            id,
            heure_debut,
            heure_fin,
            id_horaire_specifique
          )
        )
      ),
      publication_professionnel(
        id,
        annee,
        description,
        titre,
        id_professionnel
      ),
      experience_professionnel(
        id,
        date_debut,
        date_fin,
        description,
        est_actuel,
        etablissement,
        poste,
        id_professionnel
      ),
      diplome_professionnel(
        id,
        annee,
        description,
        etablissement,
        titre,
        id_professionnel
      ),
      langues_parlees_professionnel(
        id,
        nom_langue,
        id_professionnel
      ),
      utilisateurs!utilisateur_id(
        id,
        email,
        evenement!id_professionnel(
          id,
          titre,
          description,
          date_debut,
          date_fin,
          est_toute_la_journee,
          est_reportee,
          repetition,
          cree_a,
          mis_a_jour_a
        ),
        contact(
          id,
          numero
        ),
        photos:photos!utilisateur_id(
          id,
          path,
          type
        )
      )
    `;

    let qb = supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select(selectQuery, { count: "estimated" });

    if (orConditions.length > 0) {
      // Utilise .or() pour les conditions de recherche
      qb = qb.or(orConditions.join(","));
    }

    // Tri par pertinence : professionnels acceptant de nouveaux patients en premier
    qb = qb.order("nouveau_patient_acceptes", { ascending: false });
    qb = qb.order("nom", { ascending: true });

    // Pagination via range (Supabase)
    const from = page * limit;
    const to = from + limit - 1;
    qb = qb.range(from, to);

    const { data, error } = await qb;

    if (error) {
      // le repository remonte l'erreur au caller (usecase)
      throw error;
    }

    // aucune logique métier : renvoyer tel quel
    return (data ?? []) as SearchProfessionalDTO[];
  }

  /**
   * Méthode de debug pour tester les requêtes individuellement
   * @param professionalId ID du professionnel à tester
   */
  async debugProfessionalData(professionalId: number) {
    console.log(
      "=== DEBUG: Récupération des données pour le professionnel",
      professionalId,
      "==="
    );

    // Récupérer d'abord l'utilisateur_id du professionnel
    const { data: profData } = await supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select("utilisateur_id")
      .eq("id", professionalId)
      .single();

    const utilisateur_id = profData?.utilisateur_id;
    console.log("Utilisateur ID:", utilisateur_id);

    // Test 1: Données de base
    const { data: baseData, error: baseError } = await supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select("*")
      .eq("id", professionalId);

    console.log("1. Données de base:", baseData, "Erreur:", baseError);

    // Test 2: Mots-clés
    const { data: motCles, error: motClesError } = await supabase
      .from("mot_cles_professionnel")
      .select("*")
      .eq("id_professionnel", professionalId);

    console.log("2. Mots-clés:", motCles, "Erreur:", motClesError);

    // Test 3: Événements (via utilisateur_id)
    const { data: evenements, error: evenementsError } = await supabase
      .from("evenement")
      .select("*")
      .eq("id_professionnel", utilisateur_id);

    console.log("3. Événements:", evenements, "Erreur:", evenementsError);

    // Test 4: Contacts (via utilisateur_id)
    const { data: contacts, error: contactsError } = await supabase
      .from("contact")
      .select("*")
      .eq("utilisateur_id", utilisateur_id);

    console.log("4. Contacts:", contacts, "Erreur:", contactsError);

    // Test 5: Paramètres de disponibilité
    const { data: disponibilite, error: disponibiliteError } = await supabase
      .from("parametre_disponibilite")
      .select("*")
      .eq("id_professionnel", professionalId);

    console.log(
      "5. Disponibilité:",
      disponibilite,
      "Erreur:",
      disponibiliteError
    );

    // Test 6: Requête avec jointures directes
    const { data: withDirectJoins, error: directJoinsError } = await supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select(
        `
        id,
        nom,
        prenom,
        utilisateur_id,
        mot_cles_professionnel(
          id,
          symptome
        ),
        parametre_disponibilite(
          id,
          type
        )
      `
      )
      .eq("id", professionalId);

    console.log(
      "6. Jointures directes:",
      withDirectJoins,
      "Erreur:",
      directJoinsError
    );

    // Test 7: Requête avec jointures via utilisateurs
    const { data: withUserJoins, error: userJoinsError } = await supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select(
        `
        id,
        nom,
        prenom,
        utilisateur_id,
        utilisateurs!utilisateur_id(
          id,
          evenement!id_professionnel(
            id,
            titre,
            description
          ),
          contact(
            id,
            numero
          )
        )
      `
      )
      .eq("id", professionalId);

    console.log(
      "7. Jointures via utilisateurs:",
      withUserJoins,
      "Erreur:",
      userJoinsError
    );

    return {
      baseData,
      motCles,
      evenements,
      contacts,
      disponibilite,
      withDirectJoins,
      withUserJoins,
    };
  }

  /**
   * Recherche les professionnels par proximité géographique
   * @param latitude Latitude du point de référence
   * @param longitude Longitude du point de référence
   * @param radiusKm Rayon de recherche en kilomètres
   * @param limit Nombre maximum de résultats
   * @returns Liste des professionnels dans le rayon spécifié
   */
  async searchByProximity(
    latitude: number,
    longitude: number,
    radiusKm: number = 10,
    limit: number = 50
  ): Promise<SearchProfessionalDTO[]> {
    // Utilise une fonction RPC pour la recherche géographique avec PostGIS
    const { data, error } = await supabase.rpc(
      "search_professionals_by_proximity",
      {
        search_lat: latitude,
        search_lng: longitude,
        radius_km: radiusKm,
        result_limit: limit,
      }
    );

    if (error) {
      throw error;
    }

    return (data ?? []) as SearchProfessionalDTO[];
  }

  /**
   * Recherche intelligente combinant texte et géolocalisation
   * @param params Paramètres de recherche incluant la géolocalisation
   * @returns Liste des professionnels triés par pertinence et proximité
   */
  async searchWithGeolocation(
    params: SearchProfessionalsRepositoryParams & {
      latitude?: number;
      longitude?: number;
      radiusKm?: number;
    }
  ): Promise<SearchProfessionalDTO[]> {
    const { latitude, longitude, radiusKm = 50, ...searchParams } = params;

    // Si on a des coordonnées, on privilégie la recherche géographique
    if (latitude && longitude) {
      return this.searchByProximity(
        latitude,
        longitude,
        radiusKm,
        searchParams.limit
      );
    }

    // Sinon, on utilise la recherche textuelle classique
    return this.execute(searchParams);
  }
}

export default SearchProfessionalsRepository;
