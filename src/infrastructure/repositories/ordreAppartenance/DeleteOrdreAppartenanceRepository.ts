import { ordre_appartenance } from "@/domain/models";
import { IDeleteOrdreAppartenanceRepository } from "@/domain/interfaces/repositories/ordreAppartenance/IDeleteOrdreAppartenanceRepository";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ORDRE_APPARTENANCE_LIST_TABLE_NAME } from "./constants";

class DeleteOrdreAppartenanceRepository
  implements IDeleteOrdreAppartenanceRepository {
  async execute(id: number): Promise<ordre_appartenance> {
    const { data, error } = await supabase
      .from(ORDRE_APPARTENANCE_LIST_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return data as ordre_appartenance;
  }
}

export default DeleteOrdreAppartenanceRepository;
