import { ordre_appartenance } from "@/domain/models";
import { IGetOrdreAppartenanceByIdRepository } from "@/domain/interfaces/repositories/ordreAppartenance/IGetOrdreAppartenanceByIdRepository";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ORDRE_APPARTENANCE_LIST_TABLE_NAME } from "./constants";

class GetOrdreAppartenanceByIdRepository
  implements IGetOrdreAppartenanceByIdRepository {
  async execute(id: number): Promise<ordre_appartenance> {
    const { data, error } = await supabase
      .from(ORDRE_APPARTENANCE_LIST_TABLE_NAME)
      .select("*")
      .eq("id", id);

    if (error) throw error;

    if (!data || data.length === 0) return null;

    return data[0] as ordre_appartenance;
  }
}

export default GetOrdreAppartenanceByIdRepository;
