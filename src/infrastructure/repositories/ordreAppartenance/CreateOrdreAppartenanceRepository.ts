import { ordre_appartenance_professionnel } from "@/domain/models";
import { ICreateOrdreAppartenanceRepository } from "@/domain/interfaces/repositories/ordreAppartenance/ICreateOrdreAppartenanceRepository";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ORDRE_APPARTENANCE_TABLE_NAME } from "./constants";

class CreateOrdreAppartenanceRepository
  implements ICreateOrdreAppartenanceRepository {
  async execute(
    ordreAppartenanceInformations: Omit<ordre_appartenance_professionnel, "id">[],
  ): Promise<ordre_appartenance_professionnel[]> {
    const { data, error } = await supabase
      .from(ORDRE_APPARTENANCE_TABLE_NAME)
      .insert(ordreAppartenanceInformations)
      .select()

    if (error) throw error;

    return data as ordre_appartenance_professionnel[];
  }
}

export default CreateOrdreAppartenanceRepository;
