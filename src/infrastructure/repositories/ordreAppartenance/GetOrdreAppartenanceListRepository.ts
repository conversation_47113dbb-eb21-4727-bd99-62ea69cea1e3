import { ordre_appartenance } from "@/domain/models";
import { IGetOrdreAppartenanceListRepository } from "@/domain/interfaces/repositories/ordreAppartenance/IGetOrdreAppartenanceListRepository";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ORDRE_APPARTENANCE_LIST_TABLE_NAME } from "./constants";

class GetOrdreAppartenanceListRepository
  implements IGetOrdreAppartenanceListRepository {
  async execute(): Promise<ordre_appartenance[]> {
    const { data, error } = await supabase
      .from(ORDRE_APPARTENANCE_LIST_TABLE_NAME)
      .select("*");

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as ordre_appartenance[];
  }
}

export default GetOrdreAppartenanceListRepository;
