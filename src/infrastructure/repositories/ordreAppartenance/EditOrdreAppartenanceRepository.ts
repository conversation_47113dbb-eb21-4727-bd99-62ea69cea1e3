import { ordre_appartenance } from "@/domain/models";
import { IEditOrdreAppartenanceRepository } from "@/domain/interfaces/repositories/ordreAppartenance/IEditOrdreAppartenanceRepository";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ORDRE_APPARTENANCE_LIST_TABLE_NAME } from "./constants";

class EditOrdreAppartenanceRepository
  implements IEditOrdreAppartenanceRepository {
  async execute(
    id: number,
    ordreAppartenanceData: Partial<ordre_appartenance>,
  ): Promise<ordre_appartenance> {
    const { data, error } = await supabase
      .from(ORDRE_APPARTENANCE_LIST_TABLE_NAME)
      .update(ordreAppartenanceData)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return data as ordre_appartenance;
  }
}

export default EditOrdreAppartenanceRepository;
