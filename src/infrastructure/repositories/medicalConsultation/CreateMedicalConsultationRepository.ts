import { consultation_medical } from '@/domain/models'
import { supabase } from '../../supabase/supabase'
import { handleError } from '../../supabase/supabaseFetchError'
import { ICreateMedicalConsultationRepository } from '@/domain/interfaces/repositories/medicalConsultation'
import { CONSULTATION_TABLE_NAME } from './Constant'

export class CreateMedicalConsultationRepository implements ICreateMedicalConsultationRepository {
  async execute(
    consultationData: Omit<consultation_medical, 'id'>
  ): Promise<consultation_medical> {
    const { data, error } = await supabase
      .from(CONSULTATION_TABLE_NAME)
      .insert(consultationData)
      .select()
      .single()

    handleError(error)

    return data as consultation_medical
  }
}
