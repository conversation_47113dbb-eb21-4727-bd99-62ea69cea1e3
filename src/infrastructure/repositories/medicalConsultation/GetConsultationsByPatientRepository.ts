import { supabase } from "@/infrastructure/supabase/supabase";
import { CONSULTATION_TABLE_NAME } from "./Constant";
import {
    IGetConsultationsByPatientRepository,
} from "@/domain/interfaces/repositories/medicalConsultation/IGetConsultationsByPatientRepository";
import { ConsultationDetails } from "@/domain/DTOS/MedicalConsultationDetailsDTO";

class GetConsultationsByPatientRepository implements IGetConsultationsByPatientRepository {
    async execute(patientId: number): Promise<ConsultationDetails[]> {
        const { data, error } = await supabase
            .from(CONSULTATION_TABLE_NAME)
            .select(`
                id,
                date_visite,
                raison_de_visite,
                plainte_principale,
                remarque,
                utilisateurs!inner(
                    professionnels!inner(id, nom, prenom)
                ),
                carnet_sante!inner(id_proprietaire)
            `)
            .eq('carnet_sante.id_proprietaire', patientId)
            .order('date_visite', { ascending: false });

        if (error) throw error;

        // Transform data to match expected format
        return (data || []).map(item => {
            // Accès correct aux données imbriquées
            const professionnel = (item.utilisateurs as any)?.professionnels?.[0];

            return {
                id: item.id,
                nomProfessionnel: professionnel?.nom ?? "Professionnel inconnu",
                prenomProfessionnel: professionnel?.prenom ?? "Professionnel inconnu",
                dateConsultation: item.date_visite,
                raisonDeVisite: item.raison_de_visite ?? "",
                plaintePrincipale: item.plainte_principale ?? "",
                remarques: item.remarque ?? "",
                professionnelId: professionnel?.id ?? 0
            };
        });
    }
}

export default GetConsultationsByPatientRepository;