import { consultation_medical } from '@/domain/models'
import { supabase } from '../../supabase/supabase'
import { handleError } from '../../supabase/supabaseFetchError'
import { IDeleteMedicalConsultationRepository } from '@/domain/interfaces/repositories/medicalConsultation'
import { CONSULTATION_TABLE_NAME } from './Constant'

export class DeleteMedicalConsultationRepository implements IDeleteMedicalConsultationRepository {
  async execute(id: number): Promise<consultation_medical> {
    if (!id) {
      throw new Error("L'id est requis pour la suppression.")
    }

    const { data, error } = await supabase
      .from(CONSULTATION_TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data
  }
}
