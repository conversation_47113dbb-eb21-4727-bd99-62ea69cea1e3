import { consultation_medical } from '@/domain/models'
import { supabase } from '../../supabase/supabase'
import { handleError } from '../../supabase/supabaseFetchError'
import { IGetMedicalConsultationsByPatientIdRepository } from '@/domain/interfaces/repositories/medicalConsultation'
import { CONSULTATION_TABLE_NAME } from './Constant'

export class GetMedicalConsultationsByPatientIdRepository implements IGetMedicalConsultationsByPatientIdRepository {
  async execute(patientId: number): Promise<consultation_medical[]> {
    const { data, error } = await supabase
      .from(CONSULTATION_TABLE_NAME)
      .select('*')
      .eq('id_carnet', patientId)

    handleError(error)

    return data
  }
}
