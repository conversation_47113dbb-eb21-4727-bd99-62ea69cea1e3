import { supabase } from "../../supabase/supabase";
import { handleError } from "../../supabase/supabaseFetchError";
import { IGetMedicalConsultationsByProfessionalIdRepository } from "@/domain/interfaces/repositories/medicalConsultation";
import { CONSULTATION_TABLE_NAME } from "./Constant";
import { MedicalConsultationDTO } from "@/domain/DTOS";
import { consultation_medical } from "@/domain/models";

export class GetMedicalConsultationsByProfessionalIdRepository
  implements IGetMedicalConsultationsByProfessionalIdRepository
{
  async execute(professionalId: number): Promise<consultation_medical[]> {
    const { data, error } = await supabase
      .from(CONSULTATION_TABLE_NAME)
      .select("*")
      .eq("id_professionnel", professionalId);

    handleError(error);

    return data;
  }
}
