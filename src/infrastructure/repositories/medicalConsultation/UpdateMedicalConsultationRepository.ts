import { consultation_medical } from '@/domain/models'
import { supabase } from '../../supabase/supabase'
import { handleError } from '../../supabase/supabaseFetchError'
import { CONSULTATION_TABLE_NAME } from './Constant'
import { IUpdateMedicalConsultationRepository } from '@/domain/interfaces/repositories/medicalConsultation'

export class UpdateMedicalConsultationRepository implements IUpdateMedicalConsultationRepository {
  async execute(
    id: number,
    updateData: Partial<consultation_medical>
  ): Promise<consultation_medical> {
    if (!id) {
      throw new Error("L'id est requis pour la mise à jour.")
    }

    const { data, error } = await supabase
      .from(CONSULTATION_TABLE_NAME)
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data
  }
}
