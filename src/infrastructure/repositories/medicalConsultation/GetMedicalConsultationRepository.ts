import { consultation_medical } from '@/domain/models'
import { supabase } from '../../supabase/supabase'
import { handleError } from '../../supabase/supabaseFetchError'
import { CONSULTATION_TABLE_NAME } from './Constant'
import { IGetMedicalConsultationRepository } from '@/domain/interfaces/repositories/medicalConsultation'

export class GetMedicalConsultationRepository implements IGetMedicalConsultationRepository {
  async execute(id: number): Promise<consultation_medical> {
    const { data, error } = await supabase
      .from(CONSULTATION_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single()

    handleError(error)

    return data
  }
}
