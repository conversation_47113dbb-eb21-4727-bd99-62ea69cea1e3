import { Employer } from "@/domain/models/Employer";
import { supabase } from "@/infrastructure/supabase/supabase";
import { EMPLOYER_TABLE_NAME } from "./constants";
import { IGetEmployerByIdRepository } from "@/domain/interfaces/repositories/employer";

export class GetEmployerByIdRepository implements IGetEmployerByIdRepository {
  async execute(id: number): Promise<Employer> {
    const { data, error } = await supabase
      .from(EMPLOYER_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();
    if (error) throw error;
    return data as Employer;
  }
}
