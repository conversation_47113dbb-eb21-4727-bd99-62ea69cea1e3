import { Employer } from "@/domain/models/Employer";
import { supabase } from "@/infrastructure/supabase/supabase";
import { EMPLOYER_TABLE_NAME } from "./constants";
import { IUpdateEmployerRepository } from "@/domain/interfaces/repositories/employer/IUpdateEmployerRepository";

export class UpdateEmployerRepository implements IUpdateEmployerRepository {
  async execute(
    id: number,
    employerData: Partial<Employer>,
  ): Promise<Employer> {
    const { data, error } = await supabase
      .from(EMPLOYER_TABLE_NAME)
      .update(employerData)
      .eq("id", id)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
}
