import { supabase } from "@/infrastructure/supabase/supabase";
import { EMPLOYER_TABLE_NAME } from "./constants";
import { Employer } from "@/domain/models";
import { IGetEmployerByDashIdRepository } from "@/domain/interfaces/repositories/employer";

class GetEmployersByDashIdRepository implements IGetEmployerByDashIdRepository {
  constructor() {}

  async execute(dashId: number): Promise<Employer[]> {
    const { data, error } = await supabase
      .from(EMPLOYER_TABLE_NAME)
      .select("*")
      .eq("id_dash", dashId);

    if (error) throw error;

    return data as Employer[];
  }
}

export default GetEmployersByDashIdRepository;
