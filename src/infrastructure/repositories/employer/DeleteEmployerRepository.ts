import { supabase } from "@/infrastructure/supabase/supabase";
import { EMPLOYER_TABLE_NAME } from "./constants";
import { IDeleteEmployerRepository } from "@/domain/interfaces/repositories/employer/IDeleteEmployerRepository";

export class DeleteEmployerRepository implements IDeleteEmployerRepository {
  async execute(id: number): Promise<void> {
    const { error } = await supabase
      .from(EMPLOYER_TABLE_NAME)
      .delete()
      .eq("id", id);
    if (error) throw error;
  }
}
