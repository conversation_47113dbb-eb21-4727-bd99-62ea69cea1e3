import { Employer } from "@/domain/models/Employer";
import { supabase } from "@/infrastructure/supabase/supabase";
import { EMPLOYER_TABLE_NAME } from "./constants";
import { ICreateEmployerRepository } from "@/domain/interfaces/repositories/employer/ICreateEmployerRepository";

export class CreateEmployerRepository implements ICreateEmployerRepository {
  async execute(employers: Omit<Employer, "id">): Promise<Employer> {
    const { data, error } = await supabase
      .from(EMPLOYER_TABLE_NAME)
      .insert(employers)
      .select();
    if (error) throw error;
    return data[0] as Employer;
  }
}
