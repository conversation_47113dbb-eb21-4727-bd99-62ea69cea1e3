import { Employer } from "@/domain/models/Employer";
import { supabase } from "@/infrastructure/supabase/supabase";
import { EMPLOYER_TABLE_NAME } from "./constants";
import { ICreateMultipleEmployerRepository } from "@/domain/interfaces/repositories/employer/ICreateMultipleEmployerRepository";

export class CreateMultipleEmployeRepositorire implements ICreateMultipleEmployerRepository {
    async execute(employesData: Omit<Employer[], "id">): Promise<Employer[]> {
        const { data, error } = await supabase
            .from(EMPLOYER_TABLE_NAME)
            .insert(employesData as any)
            .select();

        if (error) throw error;
        return (data as any) || [];
    }
}