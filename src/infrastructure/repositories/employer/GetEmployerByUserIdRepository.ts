import { Employer } from "@/domain/models/Employer";
import { supabase } from "@/infrastructure/supabase/supabase";
import { EMPLOYER_TABLE_NAME } from "./constants";
import { IGetEmployerByUserIdRepository } from "@/domain/interfaces/repositories/employer";

export class GetEmployerByUserIdRepository
  implements IGetEmployerByUserIdRepository
{
  async execute(id_utilisateur: number): Promise<Employer> {
    const { data, error } = await supabase
      .from(EMPLOYER_TABLE_NAME)
      .select("*")
      .eq("id_utilisateur", id_utilisateur);

    if (!data) {
      return null;
    }

    if (error) throw error;

    return data[0] as Employer;
  }
}
