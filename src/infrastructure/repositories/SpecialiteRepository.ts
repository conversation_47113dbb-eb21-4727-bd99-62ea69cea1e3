import { SpecialiteProfessionnel } from '@/domain/models'
import { supabase } from '../supabase/supabase'
import { handleError } from '../supabase/supabaseFetchError'

const tableName = 'specialites_professionnel'

export class SpecialiteRepository {
  async getSpecialiteById(id: number): Promise<SpecialiteProfessionnel> {
    const { data, error } = await supabase.from(tableName).select('*').eq('id', id)

    handleError(error)

    return data[0] as SpecialiteProfessionnel
  }

  async getSpecialiteByProfessionnalId(id: number): Promise<SpecialiteProfessionnel[]> {
    const { data, error } = await supabase.from(tableName).select('*').eq('id_professionnel', id)

    handleError(error)

    return data as SpecialiteProfessionnel[]
  }

  async getAllSpecialites(): Promise<SpecialiteProfessionnel[]> {
    const { data, error } = await supabase.from(tableName).select('*')

    handleError(error)

    return data as SpecialiteProfessionnel[]
  }

  async getSpecialitesByCategorie(categorieId: number): Promise<SpecialiteProfessionnel[]> {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('id_categorie', categorieId)

    handleError(error)

    return data as SpecialiteProfessionnel[]
  }

  async createSpecialite(
    specialiteInformations: Omit<SpecialiteProfessionnel, 'id'>
  ): Promise<SpecialiteProfessionnel> {
    const { data, error } = await supabase
      .from(tableName)
      .insert(specialiteInformations)
      .select()
      .single()

    handleError(error)

    return data as SpecialiteProfessionnel
  }

  async editSpecialite(
    id: number,
    specialiteData: Partial<SpecialiteProfessionnel>
  ): Promise<SpecialiteProfessionnel> {
    const { data, error } = await supabase
      .from(tableName)
      .update(specialiteData)
      .eq('id', id)
      .select()

    handleError(error)

    return data[0] as SpecialiteProfessionnel
  }

  async deleteSpecialite(id: number): Promise<SpecialiteProfessionnel> {
    const { data, error } = await supabase.from(tableName).delete().eq('id', id).select().single()

    handleError(error)

    return data as SpecialiteProfessionnel
  }
}

export const specialiteRepository = new SpecialiteRepository()
