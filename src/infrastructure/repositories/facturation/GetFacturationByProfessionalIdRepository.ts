import { supabase } from "../../supabase/supabase";
import { handleError } from "../../supabase/supabaseFetchError";
import { IGetFacturationsByProfessionalIdRepository } from "@/domain/interfaces/repositories/facturation";
import {
  FACTURATION_TABLE_NAME,
  PATIENT_TABLE_NAME,
  PROFESSIONAL_TABLE_NAME,
  USER_TABLE_NAME,
} from "./Constant";
import { FacturationDTO } from "@/domain/DTOS";

export class GetFacturationsByProfessionalIdRepository
  implements IGetFacturationsByProfessionalIdRepository
{
  async execute(professionalId: number): Promise<FacturationDTO[]> {
    const { data, error } = await supabase
      .from(FACTURATION_TABLE_NAME)
      .select(
        `
            *,
            patient:${USER_TABLE_NAME}!id_patient(
              id,
              ${PATIENT_TABLE_NAME}!utilisateur_id(
              nom,
              prenom
              )
            ),
            professionnel:${USER_TABLE_NAME}!id_professionnel(
              id,
              ${PROFESSIONAL_TABLE_NAME}!utilisateur_id(
                id,
                nom,
                prenom
              )
            )
          `
      )
      .eq("id_professionnel", professionalId);

    if (error) throw error;

    return data;
  }
}
