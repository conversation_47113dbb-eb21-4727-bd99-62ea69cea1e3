import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetFacturationByPatientIdRepository } from "@/domain/interfaces/repositories/facturation";
import {
  FACTURATION_TABLE_NAME,
  PATIENT_TABLE_NAME,
  PROFESSIONAL_TABLE_NAME,
  USER_TABLE_NAME,
} from "./Constant";

export class GetFacturationByPatientIdRepository
  implements IGetFacturationByPatientIdRepository
{
  async execute(patientId: number) {
    const { data, error } = await supabase
      .from(FACTURATION_TABLE_NAME)
      .select(
        `
        *,
        patient:${USER_TABLE_NAME}!id_patient(
          id,
          ${PATIENT_TABLE_NAME}!utilisateur_id(
          nom,
          prenom
          )
        ),
        professionnel:${USER_TABLE_NAME}!id_professionnel(
          id,
          ${PROFESSIONAL_TABLE_NAME}!utilisateur_id(
            id,
            nom,
            prenom
          )
        )
      `
      )
      .eq("id_patient", patientId);

    if (error) throw error;

    return data;
  }
}
