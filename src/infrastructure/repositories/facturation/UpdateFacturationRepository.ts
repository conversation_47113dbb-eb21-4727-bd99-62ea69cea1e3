import { Facturation } from "@/domain/models";
import { supabase } from "../../supabase/supabase";
import { handleError } from "../../supabase/supabaseFetchError";
import { FACTURATION_TABLE_NAME } from "./Constant";
import { IUpdateFacturationRepository } from "@/domain/interfaces/repositories/facturation";

export class UpdateFacturationRepository
  implements IUpdateFacturationRepository
{
  async execute(
    id: number,
    updateData: Partial<Facturation>
  ): Promise<Facturation> {
    const { data, error } = await supabase
      .from(FACTURATION_TABLE_NAME)
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data;
  }
}
