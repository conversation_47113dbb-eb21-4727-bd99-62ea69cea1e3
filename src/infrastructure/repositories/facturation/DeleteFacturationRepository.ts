import { Facturation } from "@/domain/models";
import { supabase } from "../../supabase/supabase";
import { handleError } from "../../supabase/supabaseFetchError";
import { IDeleteFacturationRepository } from "@/domain/interfaces/repositories/facturation";
import { FACTURATION_TABLE_NAME } from "./Constant";

export class DeleteFacturationRepository
  implements IDeleteFacturationRepository
{
  async execute(id: number): Promise<Facturation> {
    const { data, error } = await supabase
      .from(FACTURATION_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data;
  }
}
