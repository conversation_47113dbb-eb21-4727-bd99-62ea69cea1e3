import { Facturation } from "@/domain/models";
import { supabase } from "../../supabase/supabase";
import { handleError } from "../../supabase/supabaseFetchError";
import { FACTURATION_TABLE_NAME } from "./Constant";
import { IGetFacturationRepository } from "@/domain/interfaces/repositories/facturation";

export class GetFacturationRepository implements IGetFacturationRepository {
  async execute(id: number): Promise<Facturation> {
    const { data, error } = await supabase
      .from(FACTURATION_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    handleError(error);

    return data;
  }
}
