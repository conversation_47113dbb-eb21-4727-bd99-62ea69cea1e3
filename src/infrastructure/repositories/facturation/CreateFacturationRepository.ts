import { Facturation } from "@/domain/models";
import { supabase } from "../../supabase/supabase";
import { handleError } from "../../supabase/supabaseFetchError";
import { ICreateFacturationRepository } from "@/domain/interfaces/repositories/facturation";
import { FACTURATION_TABLE_NAME } from "./Constant";

export class CreateFacturationRepository
  implements ICreateFacturationRepository
{
  async execute(
    consultationData: Omit<Facturation, "id">
  ): Promise<Facturation> {
    const { data, error } = await supabase
      .from(FACTURATION_TABLE_NAME)
      .insert(consultationData)
      .select()
      .single();

    handleError(error);

    return data as Facturation;
  }
}
