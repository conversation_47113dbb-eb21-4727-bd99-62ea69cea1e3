import { supabase } from "@/infrastructure/supabase/supabase";
import { CONVERSATION_TABLE_NAME } from "./Constant";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { IGetConversationRepository } from "@/domain/interfaces/repositories/conversation";
import { conversation } from "@/domain/models";

export class GetConversationRepository implements IGetConversationRepository {
  async execute(userId: number): Promise<conversation[]> {
    const { data, error } = await supabase
      .from(CONVERSATION_TABLE_NAME)
      .select("*")
      .or(`id_expediteur.eq.${userId},id_recepteur.eq.${userId}`)
      .order("mis_a_jour_a", { ascending: false });

    handleError(error);

    return data as conversation[];
  }
}
