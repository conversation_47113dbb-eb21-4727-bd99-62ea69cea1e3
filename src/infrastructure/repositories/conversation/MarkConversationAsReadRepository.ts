import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { CONVERSATION_TABLE_NAME } from "./Constant";
import { IMarkConversationAsReadRepository } from "@/domain/interfaces/repositories/conversation";
import { conversation } from "@/domain/models";

export class MarkConversationAsReadRepository
  implements IMarkConversationAsReadRepository
{
  async execute(id: number): Promise<conversation> {
    const { data, error } = await supabase
      .from(CONVERSATION_TABLE_NAME)
      .update({
        nombre_non_lu: 0,
        mis_a_jour_a: new Date(),
      })
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as conversation;
  }
}
