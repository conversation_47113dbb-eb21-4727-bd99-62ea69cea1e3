import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { CONVERSATION_TABLE_NAME } from "./Constant";
import { IDeleteConversationRepository } from "@/domain/interfaces/repositories/conversation";
import { conversation } from "@/domain/models";

export class DeleteConversationRepository
  implements IDeleteConversationRepository
{
  async execute(id: number): Promise<conversation> {
    const { data, error } = await supabase
      .from(CONVERSATION_TABLE_NAME)
      .delete()
      .eq("id", id);

    handleError(error);

    return data as conversation;
  }
}
