import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { CONVERSATION_TABLE_NAME } from "./Constant";
import { IUpdateConversationRepository } from "@/domain/interfaces/repositories/conversation";
import { conversation } from "@/domain/models";

export class UpdateConversationRepository
  implements IUpdateConversationRepository
{
  async execute(
    id: number,
    conversationData: Partial<conversation>
  ): Promise<conversation> {
    const { data, error } = await supabase
      .from(CONVERSATION_TABLE_NAME)
      .update(conversationData)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as conversation;
  }
}
