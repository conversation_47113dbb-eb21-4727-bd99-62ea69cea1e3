import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { CONVERSATION_TABLE_NAME } from "./Constant";
import { ICreateConversationRepository } from "@/domain/interfaces/repositories/conversation";
import { conversation } from "@/domain/models";

export class CreateConversationRepository
  implements ICreateConversationRepository
{
  async execute(
    conversationData: Omit<conversation, "id">
  ): Promise<conversation> {
    const { data, error } = await supabase
      .from(CONVERSATION_TABLE_NAME)
      .insert(conversationData)
      .select()
      .single();

    handleError(error);

    return data as conversation;
  }
}
