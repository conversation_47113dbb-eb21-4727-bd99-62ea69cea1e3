import { URGENCE_TABLE_NAME } from "./Constant";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { Urgence } from "@/domain/models";
import { IGetUrgenceRepository } from "@/domain/interfaces/repositories/urgence";

export class GetUrgenceRepository implements IGetUrgenceRepository {
  async execute(patientId: number): Promise<Urgence[]> {
    const { data, error } = await supabase
      .from(URGENCE_TABLE_NAME)
      .select('*')
      .eq('id_patient', patientId)

    if (error) handleError(error);
    return data as Urgence[];
  }
}
