import { Urgence } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IDeleteUrgenceRepository } from '@/domain/interfaces/repositories/urgence'
import { URGENCE_TABLE_NAME } from './Constant'

export class DeleteUrgenceRepository implements IDeleteUrgenceRepository {
  async execute(id: number): Promise<Urgence[]> {
    const { data, error } = await supabase
      .from(URGENCE_TABLE_NAME)
      .delete()
      .eq('id_patient', id)
      .select()

    handleError(error)

    return data as Urgence[]
  }
}
