import { Urgence } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { IUpdateUrgenceRepository } from "@/domain/interfaces/repositories/urgence";
import { URGENCE_TABLE_NAME } from "./Constant";

export class UpdateUrgenceRepository implements IUpdateUrgenceRepository {
  async execute(id: number, settings: Partial<Urgence>) {
    const { data, error } = await supabase
      .from(URGENCE_TABLE_NAME)
      .update(settings)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as Urgence;
  }
}
