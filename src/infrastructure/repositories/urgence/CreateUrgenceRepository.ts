import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ICreateUrgenceRepository } from '@/domain/interfaces/repositories/urgence'
import { URGENCE_TABLE_NAME } from './Constant'
import { Urgence } from '@/domain/models'

export class CreateUrgenceRepository implements ICreateUrgenceRepository {
  async execute(newUrgence: Omit<Urgence, 'id'>[]): Promise<Urgence[]> {
    const { data, error } = await supabase
      .from(URGENCE_TABLE_NAME)
      .insert(newUrgence)
      .select()

    handleError(error)

    return data as Urgence[]
  }
}
