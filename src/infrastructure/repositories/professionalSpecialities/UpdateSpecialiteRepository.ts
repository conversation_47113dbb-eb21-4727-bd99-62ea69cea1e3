import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_SPECIALITIES_TABLE_NAME } from "./constants";
import { SpecialiteProfessionnel } from "@/domain/models";
import { IUpdateSpecialiteRepository } from "@/domain/interfaces/repositories/professionalSpecialities";

class UpdateSpecialiteRepository implements IUpdateSpecialiteRepository {
  constructor() {}

  async execute(id: number, specialiteData: Partial<SpecialiteProfessionnel>) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_SPECIALITIES_TABLE_NAME)
      .update(specialiteData)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return data as SpecialiteProfessionnel;
  }
}

export default UpdateSpecialiteRepository;
