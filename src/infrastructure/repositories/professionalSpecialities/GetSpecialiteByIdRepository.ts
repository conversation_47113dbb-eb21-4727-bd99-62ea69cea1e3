import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_SPECIALITIES_TABLE_NAME } from "./constants";
import { SpecialiteProfessionnel } from "@/domain/models";
import { IGetSpecialiteByIdRepository } from "@/domain/interfaces/repositories/professionalSpecialities";

class GetSpecialiteByIdRepository implements IGetSpecialiteByIdRepository {
  constructor() {}

  async execute(id: number) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_SPECIALITIES_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;

    return data as SpecialiteProfessionnel;
  }
}

export default GetSpecialiteByIdRepository;
