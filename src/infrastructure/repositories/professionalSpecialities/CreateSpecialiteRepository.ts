import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_SPECIALITIES_TABLE_NAME } from "./constants";
import { SpecialiteProfessionnel } from "@/domain/models";
import { ICreateSpecialiteRepository } from "@/domain/interfaces/repositories/professionalSpecialities";

class CreateSpecialiteRepository implements ICreateSpecialiteRepository {
  constructor() { }

  async execute(specialiteInformations: Omit<SpecialiteProfessionnel, "id">[]) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_SPECIALITIES_TABLE_NAME)
      .insert([specialiteInformations])
      .select()
      .single();

    if (error) throw error;

    return data as SpecialiteProfessionnel;
  }
}

export default CreateSpecialiteRepository;
