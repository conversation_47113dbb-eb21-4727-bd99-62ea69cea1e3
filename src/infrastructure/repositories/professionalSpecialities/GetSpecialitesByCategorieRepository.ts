import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_SPECIALITIES_TABLE_NAME } from "./constants";
import { SpecialiteProfessionnel } from "@/domain/models";
import { IGetSpecialitesByCategorieRepository } from "@/domain/interfaces/repositories/professionalSpecialities";

class GetSpecialitesByCategorieRepository implements IGetSpecialitesByCategorieRepository {
  constructor() {}

  async execute(categorieId: number) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_SPECIALITIES_TABLE_NAME)
      .select("*")
      .eq("id_type_etablissement", categorieId);

    if (error) throw error;

    return data as SpecialiteProfessionnel[];
  }
}

export default GetSpecialitesByCategorieRepository;
