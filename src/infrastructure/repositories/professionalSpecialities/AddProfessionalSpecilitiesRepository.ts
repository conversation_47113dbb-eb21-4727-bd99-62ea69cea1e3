import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_SPECIALITIES_TABLE_NAME } from "./constants";
import { SpecialiteProfessionnel } from "@/domain/models";
import { IAddProfessionalSpecilitiesRepository } from "@/domain/interfaces/repositories/professionalSpecialities";

class AddProfessionalSpecilitiesRepository
  implements IAddProfessionalSpecilitiesRepository {
  constructor() { }

  async execute(
    newSpecialities: Omit<SpecialiteProfessionnel, "id">[]
  ) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_SPECIALITIES_TABLE_NAME)
      .insert(newSpecialities)
      .select();

    if (error) throw error;

    return data as SpecialiteProfessionnel[];
  }
}

export default AddProfessionalSpecilitiesRepository;
