import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_SPECIALITIES_TABLE_NAME } from "./constants";
import { IGetProfessionalSpecialitiesRepository } from "@/domain/interfaces/repositories/professionalSpecialities";
import { SpecialiteProfessionnel } from "@/domain/models";

class GetProfessionalSpecialitiesRepository
  implements IGetProfessionalSpecialitiesRepository
{
  constructor() {}

  async execute(professionalId: number) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_SPECIALITIES_TABLE_NAME)
      .select("*")
      .eq("id_professionnel", professionalId);

    if (error) throw error;

    return data as SpecialiteProfessionnel[];
  }
}

export default GetProfessionalSpecialitiesRepository;
