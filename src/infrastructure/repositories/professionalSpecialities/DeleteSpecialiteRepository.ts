import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_SPECIALITIES_TABLE_NAME } from "./constants";
import { SpecialiteProfessionnel } from "@/domain/models";
import { IDeleteSpecialiteRepository } from "@/domain/interfaces/repositories/professionalSpecialities";

class DeleteSpecialiteRepository implements IDeleteSpecialiteRepository {
  constructor() {}

  async execute(id: number) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_SPECIALITIES_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return data as SpecialiteProfessionnel;
  }
}

export default DeleteSpecialiteRepository;
