import { supabase } from "@/infrastructure/supabase/supabase";
import { SPECIALITIES_LIST_TABLE_NAME } from "./constants";
import { ListeSpecialites } from "@/domain/models";
import { IGetSpecialitiesRepository } from "@/domain/interfaces/repositories/professionalSpecialities";

class GetSpecialitiesRepository implements IGetSpecialitiesRepository {
  constructor() {}

  async execute() {
    const { data, error } = await supabase
      .from(SPECIALITIES_LIST_TABLE_NAME)
      .select("*");

    if (error) throw error;

    return data as ListeSpecialites[];
  }
}

export default GetSpecialitiesRepository;
