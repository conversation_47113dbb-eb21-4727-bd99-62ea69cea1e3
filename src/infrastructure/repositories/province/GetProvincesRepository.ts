import { Province } from "@/domain/models"
import { supabase } from "@/infrastructure/supabase/supabase"
import { PROVINCE_TABLE_NAME } from "./constants"
import { IGetProvincesRepository } from "@/domain/interfaces/repositories/province/IGetProvincesRepository";

class GetProvincesRepository implements IGetProvincesRepository {
  async execute(): Promise<Province[]> {
    const { data, error } = await supabase.from(PROVINCE_TABLE_NAME).select('*')

    if (error) throw error

    return data as Province[]
  }
}

export default GetProvincesRepository