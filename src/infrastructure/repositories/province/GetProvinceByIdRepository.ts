import { Province } from "@/domain/models"
import { supabase } from "@/infrastructure/supabase/supabase"
import { PROVINCE_TABLE_NAME } from "./constants"
import { IGetProvinceByIdRepository } from "@/domain/interfaces/repositories/province"

class GetProvinceByIdRepository implements IGetProvinceByIdRepository {
  async execute(id: number): Promise<Province> {
    const { data, error } = await supabase.from(PROVINCE_TABLE_NAME).select('*').eq('id', id).single()

    if (error) throw error

    return data as Province
  }
}

export default GetProvinceByIdRepository