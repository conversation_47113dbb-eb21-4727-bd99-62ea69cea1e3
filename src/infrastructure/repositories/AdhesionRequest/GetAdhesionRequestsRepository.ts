import { demande_adhesion } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ADHESION_REQUEST_TABLE_NAME } from './Constant'
import { IGetAdhesionRequestsRepository } from '@/domain/interfaces/repositories/adhesionRequest'

export class GetAdhesionRequestsRepository implements IGetAdhesionRequestsRepository {
  async execute(): Promise<demande_adhesion[]> {
    const { data, error } = await supabase.from(ADHESION_REQUEST_TABLE_NAME).select('*')

    handleError(error)

    return data as demande_adhesion[]
  }
}