import { demande_adhesion } from "@/domain/models";
import { demande_adhesion_statut_enum } from "@/domain/models/enums";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ADHESION_REQUEST_TABLE_NAME } from "./Constant";
import { IMarkAdhesionRequestAsReadRepository } from "@/domain/interfaces/repositories/adhesionRequest";

export class MarkAdhesionRequestAsReadRepository
  implements IMarkAdhesionRequestAsReadRepository
{
  async execute(id: number): Promise<demande_adhesion | undefined> {
    if (!id) {
      throw "Id requis pour modifier l'etat de la demande";
    }

    const newValue: { status: string } = {
      status: demande_adhesion_statut_enum.LU,
    };

    const { data, error } = await supabase
      .from(ADHESION_REQUEST_TABLE_NAME)
      .update(newValue)
      .eq("id", id)
      .select();

    if (error) throw error;

    return data[0] as demande_adhesion;
  }
}
