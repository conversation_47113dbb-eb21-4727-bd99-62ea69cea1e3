import { demande_adhesion } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ADHESION_REQUEST_TABLE_NAME } from "./Constant";
import { ICreateAdhesionRequestRepository } from "@/domain/interfaces/repositories/adhesionRequest";

export class CreateAdhesionRequestRepository
  implements ICreateAdhesionRequestRepository
{
  async execute(
    adhesionRequestData: Omit<demande_adhesion, "id" | "status" | "cree_a">,
  ): Promise<demande_adhesion> {
    const { data, error } = await supabase
      .from(ADHESION_REQUEST_TABLE_NAME)
      .insert(adhesionRequestData)
      .select();

    if (error) throw error;

    return data[0] as demande_adhesion;
  }
}
