import { demande_adhesion } from "@/domain/models";
import { demande_adhesion_statut_enum } from "@/domain/models/enums";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ADHESION_REQUEST_TABLE_NAME } from "./Constant";
import { IApproveAdhesionRequestRepository } from "@/domain/interfaces/repositories/adhesionRequest";

export class ApproveAdhesionRequestRepository
  implements IApproveAdhesionRequestRepository
{
  async execute(id: number): Promise<demande_adhesion> {
    if (!id) {
      throw "Id requis pour approuver la demande d'adhésion";
    }

    const newValue: { status: string } = {
      status: demande_adhesion_statut_enum.APPROUVEE,
    };

    const { data, error } = await supabase
      .from(ADHESION_REQUEST_TABLE_NAME)
      .update(newValue)
      .eq("id", id)
      .select();

    if (error) throw error;

    return data[0] as demande_adhesion;
  }
}
