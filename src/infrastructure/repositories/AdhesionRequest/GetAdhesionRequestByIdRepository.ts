import { demande_adhesion } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ADHESION_REQUEST_TABLE_NAME } from './Constant'
import { IGetAdhesionRequestByIdRepository } from '@/domain/interfaces/repositories/adhesionRequest'

export class GetAdhesionRequestByIdRepository implements IGetAdhesionRequestByIdRepository {
  async execute(id: number): Promise<demande_adhesion> {
    const { data, error } = await supabase
      .from(ADHESION_REQUEST_TABLE_NAME)
      .select('*')
      .eq('id', id)

    handleError(error)

    return data[0] as demande_adhesion
  }
}
