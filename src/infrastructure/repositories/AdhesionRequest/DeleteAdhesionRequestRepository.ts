import { demande_adhesion } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ADHESION_REQUEST_TABLE_NAME } from './Constant'
import { IDeleteAdhesionRequestRepository } from '@/domain/interfaces/repositories/adhesionRequest'

export class DeleteAdhesionRequestRepository implements IDeleteAdhesionRequestRepository {
  async execute(id: number): Promise<demande_adhesion> {
    const { data, error } = await supabase
      .from(ADHESION_REQUEST_TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()

    handleError(error)

    return data[0] as demande_adhesion
  }
}
