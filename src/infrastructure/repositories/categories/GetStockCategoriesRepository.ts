import { supabase } from "@/infrastructure/supabase/supabase";
import { STOCK_CATEGORIES_TABLE_NAME } from "./constants";
import { Categories } from "@/domain/models/Categories";
import { IGetStockCategoriesRepository } from "@/domain/interfaces/repositories/categories/IGetStockCategoriesRepository";

class GetStockCategoriesRepository implements IGetStockCategoriesRepository {
  constructor() {}

  async execute() {
    const { data, error } = await supabase
      .from(STOCK_CATEGORIES_TABLE_NAME)
      .select("*");

    if (error) throw error;

    return data as Categories[];
  }
}

export default GetStockCategoriesRepository;
