import { supabase } from "@/infrastructure/supabase/supabase";
import { STOCK_CATEGORIES_TABLE_NAME } from "./constants";
import { Categories } from "@/domain/models/Categories";
import { IGetStockCategoryByIdRepository } from "@/domain/interfaces/repositories/categories/IGetStockCategoryByIdRepository";

class GetStockCategoryByIdRepository
  implements IGetStockCategoryByIdRepository
{
  constructor() {}

  async execute(categoryId: number) {
    const { data, error } = await supabase
      .from(STOCK_CATEGORIES_TABLE_NAME)
      .select("*")
      .eq("id", categoryId);

    if (error) throw error;

    return data[0] as Categories;
  }
}

export default GetStockCategoryByIdRepository;
