import { message } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { MESSAGE_TABLE_NAME } from "./Constant";
import { ICreateMessageRepository } from "@/domain/interfaces/repositories/message";

export class CreateMessageRepository implements ICreateMessageRepository {
  async execute(messageData: Omit<message, "id">): Promise<message> {
    const { data, error } = await supabase
      .from(MESSAGE_TABLE_NAME)
      .insert(messageData)
      .select()
      .single();

    handleError(error);

    return data as message;
  }
}
