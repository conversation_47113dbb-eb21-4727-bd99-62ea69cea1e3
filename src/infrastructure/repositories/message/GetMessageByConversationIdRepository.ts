import { supabase } from "@/infrastructure/supabase/supabase";
import { MESSAGE_TABLE_NAME } from "./Constant";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { message } from "@/domain/models";
import { IGetMessageByConversationIdRepository } from "@/domain/interfaces/repositories/message";

export class GetMessageByConversationIdRepository
  implements IGetMessageByConversationIdRepository
{
  async execute(conversationId: number): Promise<message[]> {
    const { data, error } = await supabase
      .from(MESSAGE_TABLE_NAME)
      .select("*")
      .eq("id_conversation", conversationId);

    handleError(error);

    return data as message[];
  }
}
