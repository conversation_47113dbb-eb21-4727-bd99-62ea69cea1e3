import { message } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { MESSAGE_TABLE_NAME } from "./Constant";
import { IDeleteMessageRepository } from "@/domain/interfaces/repositories/message";

export class DeleteMessageRepository implements IDeleteMessageRepository {
  async execute(id: number): Promise<message> {
    const { data, error } = await supabase
      .from(MESSAGE_TABLE_NAME)
      .delete()
      .eq("id", id);

    handleError(error);

    return data as message;
  }
}
