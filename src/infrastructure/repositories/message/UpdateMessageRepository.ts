import { message } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { MESSAGE_TABLE_NAME } from "./Constant";
import { IUpdateMessageRepository } from "@/domain/interfaces/repositories/message";

export class UpdateMessageRepository implements IUpdateMessageRepository {
  async execute(id: number, messageData: Partial<message>): Promise<message> {
    const { data, error } = await supabase
      .from(MESSAGE_TABLE_NAME)
      .update(messageData)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as message;
  }
}
