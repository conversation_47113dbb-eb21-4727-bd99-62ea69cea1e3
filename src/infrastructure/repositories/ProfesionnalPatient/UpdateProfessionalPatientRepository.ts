import { ProfessionnelPatient } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { PROFESSIONEL_PATIENT_TABLE_NAME } from "./Constant";
import { IUpdateProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";

export class UpdateProfessionalPatientRepository
  implements IUpdateProfessionalPatientRepository
{
  async execute(id: number, newData: Partial<ProfessionnelPatient>) {
    const { data, error } = await supabase
      .from(PROFESSIONEL_PATIENT_TABLE_NAME)
      .update(newData)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as ProfessionnelPatient;
  }
}
