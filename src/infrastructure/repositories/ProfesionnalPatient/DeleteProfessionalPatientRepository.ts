import { ProfessionnelPatient } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { PROFESSIONEL_PATIENT_TABLE_NAME } from "./Constant";
import { IDeleteProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";

export class DeleteProfessionalPatientRepository
  implements IDeleteProfessionalPatientRepository
{
  async execute(id: number): Promise<ProfessionnelPatient> {
    const { data, error } = await supabase
      .from(PROFESSIONEL_PATIENT_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as ProfessionnelPatient;
  }
}
