import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONEL_PATIENT_TABLE_NAME } from "./Constant";
import { IGetProfessionalPatientByPatientIdRepository } from "@/domain/interfaces/repositories/professionnelPatient";
import { ProfessionnelPatientDTO } from "@/domain/DTOS";

export class GetProfessionalPatientByPatientIdRepository
  implements IGetProfessionalPatientByPatientIdRepository
{
  async execute(patientId: number) {
    const { data, error } = await supabase
      .from(PROFESSIONEL_PATIENT_TABLE_NAME)
      .select(
        `
          *,
          patient:patients(
            *,
            urgence(*)
          )
        `
      )
      .eq("id_patient", patientId);

    if (error) throw error;

    if (!data || data.length === 0) return null;

    return data[0] as ProfessionnelPatientDTO;
  }
}
