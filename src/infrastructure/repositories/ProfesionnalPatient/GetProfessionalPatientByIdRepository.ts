import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONEL_PATIENT_TABLE_NAME } from "./Constant";
import { ProfessionnelPatientDTO } from "@/domain/DTOS";
import { IGetProfessionalPatientByIdRepository } from "@/domain/interfaces/repositories/professionnelPatient";

export class GetProfessionalPatientByIdRepository
  implements IGetProfessionalPatientByIdRepository
{
  async execute(id: number) {
    const { data, error } = await supabase
      .from(PROFESSIONEL_PATIENT_TABLE_NAME)
      .select(
        `
          *,
          patient:patients(
            *,
            urgence(*)
          )
        `
      )
      .eq("id_patient", id);

    if (error) throw error;

    if (!data || data.length === 0) return null;

    return data[0] as ProfessionnelPatientDTO;
  }
}
