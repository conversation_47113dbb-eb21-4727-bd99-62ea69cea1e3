import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { PROFESSIONEL_PATIENT_TABLE_NAME } from "./Constant";
import { ProfessionnelPatient } from "@/domain/models";
import { ICreateProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";

export class CreateProfessionalPatientRepository
  implements ICreateProfessionalPatientRepository
{
  async execute(
    newProfessionelPatient: Omit<ProfessionnelPatient, "id">,
  ): Promise<ProfessionnelPatient> {
    const { data, error } = await supabase
      .from(PROFESSIONEL_PATIENT_TABLE_NAME)
      .insert(newProfessionelPatient)
      .select();

    handleError(error);

    return data[0] as ProfessionnelPatient;
  }
}
