import { supabase } from "@/infrastructure/supabase/supabase";
import {
  PATIENT_TABLE_NAME,
  PROFESSIONAL_TABLE_NAME,
  PROFESSIONEL_PATIENT_TABLE_NAME,
  URGENCE_TABLE_NAME,
  USER_TABLE_NAME,
} from "./Constant";
import { IGetProfessionalPatientByUserIdRepository } from "@/domain/interfaces/repositories/professionnelPatient";

class GetProfessionalPatientByUserIdRepository
  implements IGetProfessionalPatientByUserIdRepository
{
  constructor() {}

  async execute(userId: number) {
    const { data, error } = await supabase
      .from(USER_TABLE_NAME)
      .select(
        `
          ${PATIENT_TABLE_NAME}(*,
            ${PROFESSIONEL_PATIENT_TABLE_NAME}(*),
            ${URGENCE_TABLE_NAME}(*)
          )
        `
      )
      .eq("id", userId);

    if (error) throw error;

    if (!data) return null;

    return data[0];
  }
}

export default GetProfessionalPatientByUserIdRepository;
