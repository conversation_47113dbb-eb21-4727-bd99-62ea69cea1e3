import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { PROFESSIONEL_PATIENT_TABLE_NAME } from "./Constant";
import { ProfessionnelPatientDTO } from "@/domain/DTOS";
import { IGetProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";

export class GetProfessionalPatientRepository
  implements IGetProfessionalPatientRepository
{
  async execute(id: number) {
    const { data, error } = await supabase
      .from(PROFESSIONEL_PATIENT_TABLE_NAME)
      .select(
        `
          *,
          patient:patients!id_patient(
            *,
            urgence(*)
          )
        `,
      )
      .eq("id_professionnel", id);

    handleError(error);

    return data as ProfessionnelPatientDTO[];
  }
}
