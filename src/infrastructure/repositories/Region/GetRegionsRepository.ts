import { IGetRegionsRepository } from "@/domain/interfaces/repositories/region";
import { Region } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { REGION_TABLE_NAME } from "./constants";

class GetRegionsRepository implements IGetRegionsRepository {
  async execute(): Promise<Region[]> {
    const { data, error } = await supabase.from(REGION_TABLE_NAME).select('*')

    if (error) throw error

    return data as Region[]
  }
}

export default GetRegionsRepository