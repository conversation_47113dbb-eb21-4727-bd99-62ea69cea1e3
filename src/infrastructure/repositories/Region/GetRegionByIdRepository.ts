import { IGetRegionByIdRepository } from "@/domain/interfaces/repositories/region";
import { Region } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { REGION_TABLE_NAME } from "./constants";

class GetRegionByIdRepository implements IGetRegionByIdRepository {
  async execute(id: number): Promise<Region> {
    const { data, error } = await supabase.from(REGION_TABLE_NAME).select('*').eq('id', id).single()

    if (error) throw error

    return data as Region
  }
}

export default GetRegionByIdRepository