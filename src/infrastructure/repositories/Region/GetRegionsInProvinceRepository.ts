import { IGetRegionsInProvinceRepository } from "@/domain/interfaces/repositories/region";
import { Region } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { REGION_TABLE_NAME } from "./constants";

class GetRegionsInProvinceRepository implements IGetRegionsInProvinceRepository {
  async execute(id_province: number): Promise<Region[]> {
    const { data, error } = await supabase.from(REGION_TABLE_NAME).select('*').eq('id_province', id_province)

    if (error) throw error

    return data as Region[]
  }
}

export default GetRegionsInProvinceRepository