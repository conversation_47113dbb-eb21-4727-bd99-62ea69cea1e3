import { RendezVous } from "@/domain/models";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ICreateAppointmentRepository } from "@/domain/interfaces/repositories/appointment";

class CreateAppointmentRepository implements ICreateAppointmentRepository {
  async execute(appointmentData: Omit<RendezVous, "id">): Promise<RendezVous> {
    const { data, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .insert(appointmentData)
      .select()
      .single();

    if (error) throw error;

    return data as RendezVous;
  }
}

export default CreateAppointmentRepository;
