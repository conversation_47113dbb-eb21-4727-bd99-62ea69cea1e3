import { RendezVous } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { IDeleteAppointmentRepository } from "@/domain/interfaces/repositories/appointment";

class DeleteAppointmentRepository implements IDeleteAppointmentRepository {
  async execute(id: number): Promise<RendezVous> {
    if (!id) throw new Error("L'id est requis pour la suppression");
    const { data, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select();

    if (error) throw error;

    return data[0] as RendezVous;
  }
}

export default DeleteAppointmentRepository;
