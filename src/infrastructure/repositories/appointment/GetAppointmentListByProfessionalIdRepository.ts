import { supabase } from "@/infrastructure/supabase/supabase";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { IGetAppointmentListByProfessionalIdRepository } from "@/domain/interfaces/repositories/appointment";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";

export class GetAppointmentListByProfessionalIdRepository
  implements IGetAppointmentListByProfessionalIdRepository {
  async execute(id: number) {
    const { data: appointments, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .select(`
        *,
        patient:patients!patient_id(*)
      `)
      .eq("id_professionnel", id)
      .order("date_rendez_vous", { ascending: true });

    handleError(error);

    return appointments as AppointmentProfessionalDTO[];
  }
}
