import { RendezVous } from "@/domain/models";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { supabase } from "@/infrastructure/supabase/supabase";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { ICompleteAppointmentRepository } from "@/domain/interfaces/repositories/appointment";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";

export class CompleteAppointmentRepository implements ICompleteAppointmentRepository {
  async execute(id: number): Promise<RendezVous> {
    const doneState = { statut: rendez_vous_statut_enum.TERMINER };
    const { data, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .update(doneState)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as RendezVous;
  }
}
