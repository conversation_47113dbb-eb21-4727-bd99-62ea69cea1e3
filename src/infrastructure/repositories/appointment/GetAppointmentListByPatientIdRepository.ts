import { supabase } from "@/infrastructure/supabase/supabase";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { IGetAppointmentListByPatientIdRepository } from "@/domain/interfaces/repositories/appointment";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";

export class GetAppointmentListByPatientIdRepository implements IGetAppointmentListByPatientIdRepository {
  async execute(id: number): Promise<AppointmentPatientDTO[]> {
    // requête complète avec jointure
    const { data: appointments, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .select(`
        *,
        professional:professionnels!id_professionnel(
          id,
          titre,
          nom,
          prenom,
          adresse,
          raison_sociale,
          fokontany,
          commune
        )
      `)
      .eq("patient_id", id)
      .order('date_rendez_vous', { ascending: true });

    handleError(error)

    return appointments as AppointmentPatientDTO[]
  }
}
