import { RendezVous } from "@/domain/models";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { supabase } from "@/infrastructure/supabase/supabase";
import { IUpdateAppointmentRepository } from "@/domain/interfaces/repositories/appointment";

class UpdateAppointmentRepository implements IUpdateAppointmentRepository {
  async execute(
    id: number,
    appointmentData: Partial<RendezVous>,
  ): Promise<RendezVous> {
    if (!id) throw new Error("L'id est requis pour la modification");
    const { data, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .update(appointmentData)
      .eq("id", id)
      .select();

    if (error) throw error;

    return data[0] as RendezVous;
  }
}

export default UpdateAppointmentRepository;
