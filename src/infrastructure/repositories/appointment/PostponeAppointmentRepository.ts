import { RendezVous } from "@/domain/models";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { supabase } from "@/infrastructure/supabase/supabase";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { IPostponeAppointmentRepository } from "@/domain/interfaces/repositories/appointment";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";

export class PostponeAppointmentRepository implements IPostponeAppointmentRepository {
  async execute(id: number, dateRendezVous: Date): Promise<RendezVous> {
    const doneState = { statut: rendez_vous_statut_enum.REPORTER, date_rendez_vous: dateRendezVous };
    const { data, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .update(doneState)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as Ren<PERSON>Vous;
  }
}
