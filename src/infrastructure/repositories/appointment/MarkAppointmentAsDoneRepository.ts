import { RendezVous } from "@/domain/models";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { supabase } from "@/infrastructure/supabase/supabase";
import { IMarkAppointmentAsDoneRepository } from "@/domain/interfaces/repositories/appointment";

class MarkAppointmentAsDoneRepository implements IMarkAppointmentAsDoneRepository {
  async execute(id: number): Promise<RendezVous> {
    const doneState = { status: rendez_vous_statut_enum.TERMINER };

    const { data, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .update(doneState)
      .eq("id", id)
      .single();

    if (error) throw error;

    return data as RendezVous;
  }
}

export default MarkAppointmentAsDoneRepository;
