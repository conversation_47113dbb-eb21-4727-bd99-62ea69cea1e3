import { supabase } from "@/infrastructure/supabase/supabase";
import { CANCEL_APPOINTMENT_TABLE_NAME } from "./constants";
import { ICancelAppointmentByProfessionalRepository } from "@/domain/interfaces/repositories/appointment";
import { AnnulerRendezVous } from "@/domain/models/AnnulerRendezVous";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";

export class CancelAppointmentByProfessionalRepository
  implements ICancelAppointmentByProfessionalRepository {
  async execute(
    appointmentData: Omit<AnnulerRendezVous, "id">,
  ): Promise<AnnulerRendezVous | null> {
    const { data, error } = await supabase
      .from(CANCEL_APPOINTMENT_TABLE_NAME)
      .insert(appointmentData)
      .select();

    handleError(error);

    if (!data || data.length === 0) return null;

    return data[0] as AnnulerRendezVous;
  }
}
