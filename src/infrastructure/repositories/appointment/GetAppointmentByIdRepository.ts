import { RendezVous } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { IGetAppointmentByIdRepository } from "@/domain/interfaces/repositories/appointment";

class GetAppointmentByIdRepository implements IGetAppointmentByIdRepository {
  async execute(id: number): Promise<RendezVous> {
    const { data, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;

    return data as RendezVous;
  }
}

export default GetAppointmentByIdRepository;
