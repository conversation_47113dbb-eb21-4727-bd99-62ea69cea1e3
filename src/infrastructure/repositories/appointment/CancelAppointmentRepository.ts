import { RendezVous } from "@/domain/models";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { supabase } from "@/infrastructure/supabase/supabase";
import { APPOINTMENT_TABLE_NAME } from "./constants";
import { ICancelAppointmentRepository } from "@/domain/interfaces/repositories/appointment";

class CancelAppointmentRepository implements ICancelAppointmentRepository {
  async execute(id: number): Promise<RendezVous> {
    const doneState = { statut: rendez_vous_statut_enum.ANNULER };
    const { data, error } = await supabase
      .from(APPOINTMENT_TABLE_NAME)
      .update(doneState)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    if (!data) throw new Error(`Appointment with id ${id} not found`);

    return data as RendezVous;
  }
}

export default CancelAppointmentRepository;
