import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { AvailabilitySettingsDTO } from "@/domain/DTOS";
import { AVAILABILITY_SETTINGS_TABLE_NAME } from "./Constant";
import { IGetSettingsByProfessionalIdRepository } from "@/domain/interfaces/repositories/availabilitySettings/IGetSettingsByProfessionalIdRepository";

export class GetSettingsByProfessionalIdRepository implements IGetSettingsByProfessionalIdRepository {
    async execute(professionalId: number) {
        const { data, error } = await supabase
            .from(AVAILABILITY_SETTINGS_TABLE_NAME)
            .select(
                `
                    *,
                    horaire_hebdomadaire (
                        id,
                        jour,
                        creneau_horaire (
                            id,
                            heure_debut,
                            heure_fin
                        )
                    ),
                    horaire_date_specifique (
                        id,
                        date,
                        est_specifique,
                        creneau_horaire (
                            id,
                            heure_debut,
                            heure_fin
                        )
                    ),
                    pause (*)
                `
            )
            .eq('id_professionnel', professionalId)
            .maybeSingle()

        handleError(error);

        if (!data) {
            return null;
        }

        return data as AvailabilitySettingsDTO;
    }
}
