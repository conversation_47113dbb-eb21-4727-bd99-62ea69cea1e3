import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { IDeleteSettingsByProfessionalIdRepository } from "@/domain/interfaces/repositories/availabilitySettings/IDeleteSettingsByProfessionalIdRepository";
import { AvailabilitySettingsDTO } from "@/domain/DTOS";
import { AVAILABILITY_SETTINGS_TABLE_NAME } from "./Constant";

export class DeleteSettingsByProfessionalIdRepository implements IDeleteSettingsByProfessionalIdRepository {
  async execute(professionalId: number) {
    const { data, error } = await supabase
      .from(AVAILABILITY_SETTINGS_TABLE_NAME)
      .delete()
      .eq("id_professionnel", professionalId);

    handleError(error);

    return data as AvailabilitySettingsDTO;
  }
}
