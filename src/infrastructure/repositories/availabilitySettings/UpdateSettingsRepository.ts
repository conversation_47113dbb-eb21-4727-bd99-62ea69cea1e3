import { parametre_disponibilite } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { AVAILABILITY_SETTINGS_TABLE_NAME } from "./Constant";
import { IUpdateSettingsRepository } from "@/domain/interfaces/repositories/availabilitySettings/IUpdateSettingsRepository";

export class UpdateSettingsRepository implements IUpdateSettingsRepository {
  async execute(id: number, settings: Partial<parametre_disponibilite>) {
    const { data, error } = await supabase
      .from(AVAILABILITY_SETTINGS_TABLE_NAME)
      .update(settings)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as parametre_disponibilite;
  }
}
