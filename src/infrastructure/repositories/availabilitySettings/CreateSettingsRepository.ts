import { parametre_disponibilite } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { AVAILABILITY_SETTINGS_TABLE_NAME } from "./Constant";
import { ICreateSettingsRepository } from "@/domain/interfaces/repositories/availabilitySettings/ICreateSettingsRepository";

export class CreateSettingsRepository implements ICreateSettingsRepository {
  async execute(settings: Omit<parametre_disponibilite, "id">) {
    const { data, error } = await supabase
      .from(AVAILABILITY_SETTINGS_TABLE_NAME)
      .insert(settings)
      .select()
      .single();

    handleError(error);

    return data as parametre_disponibilite;
  }
}
