import { Allergie } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ALLERGIE_TABLE_NAME } from './Constant'
import { IUpdateAllergieRepository } from '@/domain/interfaces/repositories/allergie'

export class UpdateAllergieRepository implements IUpdateAllergieRepository {
  async execute(id: number, allergieData: Partial<Allergie>): Promise<Allergie> {
    const { data, error } = await supabase
      .from(ALLERGIE_TABLE_NAME)
      .update(allergieData)
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as Allergie
  }
}
