import { Allergie } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ALLERGIE_TABLE_NAME } from './Constant'
import { ICreateAllergieRepository } from '@/domain/interfaces/repositories/allergie'

export class CreateAllergieRepository implements ICreateAllergieRepository {
  async execute(
    allergieData: Omit<Allergie, 'id'>[]
  ): Promise<Allergie[]> {
    const { data, error } = await supabase
      .from(ALLERGIE_TABLE_NAME)
      .insert(allergieData)
      .select()

    handleError(error)

    return data as Allergie[]
  }
}
