import { supabase } from '@/infrastructure/supabase/supabase'
import { IGetAllergieByIdRepository } from "@/domain/interfaces/repositories/allergie";
import { ALLERGIE_TABLE_NAME } from "./Constant";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { Allergie } from "@/domain/models";

export class GetAllergieByIdRepository implements IGetAllergieByIdRepository {
  async execute(id: number): Promise<Allergie> {
    const { data, error } = await supabase
      .from(ALLERGIE_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    handleError(error)

    return data as Allergie
  }
}