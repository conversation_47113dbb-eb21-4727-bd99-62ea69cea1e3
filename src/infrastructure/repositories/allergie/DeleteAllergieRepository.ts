import { Allergie } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ALLERGIE_TABLE_NAME } from './Constant'
import { IDeleteAllergieRepository } from '@/domain/interfaces/repositories/allergie'

export class DeleteAllergieRepository implements IDeleteAllergieRepository {
  async execute(id: number): Promise<Allergie> {
    const { data, error } = await supabase
      .from(ALLERGIE_TABLE_NAME)
      .delete()
      .eq('id', id)

    handleError(error)

    return data as Allergie
  }
}
