import { supabase } from '@/infrastructure/supabase/supabase'
import { IGetAllAllergieRepository } from "@/domain/interfaces/repositories/allergie";
import { ALLERGIE_TABLE_NAME } from "./Constant";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { Allergie } from "@/domain/models";

export class GetAllAllergieRepository implements IGetAllAllergieRepository {
  async execute(carnetId: number): Promise<Allergie[]> {
    const { data, error } = await supabase
      .from(ALLERGIE_TABLE_NAME)
      .select("*")
      .eq("id_carnet", carnetId);

    handleError(error)

    return data as Allergie[]
  }
}