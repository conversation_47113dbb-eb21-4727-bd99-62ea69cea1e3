import { horaire_date_specifique } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IAddSpecificDateScheduleRepository } from '@/domain/interfaces/repositories/specificDataSchedule'

const SPECIFIC_DATA_SCHEDULE_TABLE_NAME = 'horaire_date_specifique'

export class AddSpecificDateScheduleRepository implements IAddSpecificDateScheduleRepository {
  async execute(schedule: Omit<horaire_date_specifique, 'id'>[]) {
    const { data, error } = await supabase
      .from(SPECIFIC_DATA_SCHEDULE_TABLE_NAME)
      .insert(schedule)
      .select()

    handleError(error)

    return data as horaire_date_specifique[]
  }
}
