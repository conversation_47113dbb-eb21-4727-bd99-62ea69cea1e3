import { horaire_date_specifique } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IDeleteSpecificDateScheduleRepository } from '@/domain/interfaces/repositories/specificDataSchedule'

const SPECIFIC_DATA_SCHEDULE_TABLE_NAME = 'horaire_date_specifique'

export class DeleteSpecificDateScheduleRepository implements IDeleteSpecificDateScheduleRepository {
  async execute(id_parametre_disponibilite: number) {
    const { data, error } = await supabase
      .from(SPECIFIC_DATA_SCHEDULE_TABLE_NAME)
      .delete()
      .eq("id_parametre_disponibilite", id_parametre_disponibilite);

    handleError(error)

    return data as horaire_date_specifique[]
  }
}
