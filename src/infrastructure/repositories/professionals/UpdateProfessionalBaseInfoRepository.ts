import { IUpdateProfessionalBaseInfoRepository } from "@/domain/interfaces/repositories/professionals/IUpdateProfessionalBaseInfoRepository";
import { Professionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { PROFESSIONAL_TABLE_NAME } from "./constants";

/**
 * Repository pour la mise à jour des informations de base du professionnel
 *
 * @description Ce repository gère la mise à jour des informations personnelles
 * de base d'un professionnel (titre, nom, prénom) dans la table professionnels.
 * Il utilise Supabase pour les opérations de base de données.
 *
 * @architecture
 * - Implémente l'interface IUpdateProfessionalBaseInfoRepository
 * - Utilise Supabase pour les opérations de base de données
 * - Gère les erreurs de manière centralisée avec handleError
 * - Respecte les principes de Clean Architecture
 */
export class UpdateProfessionalBaseInfoRepository
  implements IUpdateProfessionalBaseInfoRepository
{
  /**
   * Met à jour les informations de base du professionnel dans la base de données
   *
   * @param professionalId - L'identifiant unique du professionnel
   * @param baseInfo - Les informations de base à mettre à jour
   * @returns Promise<Professionnel> - Les données mises à jour du professionnel
   *
   * @throws {Error} En cas d'erreur lors de la mise à jour
   */
  async execute(
    professionalId: number,
    baseInfo: Pick<Professionnel, "titre" | "nom" | "prenom">,
  ): Promise<Professionnel> {
    try {
      const { data, error } = await supabase
        .from(PROFESSIONAL_TABLE_NAME)
        .update({
          titre: baseInfo.titre,
          nom: baseInfo.nom,
          prenom: baseInfo.prenom,
          mis_a_jour_a: new Date().toISOString(),
        })
        .eq("id", professionalId)
        .select()
        .single();

      handleError(error);

      if (!data) {
        throw new Error(
          "Aucune donnée retournée après la mise à jour des informations de base",
        );
      }

      return data as Professionnel;
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(
        "Erreur inconnue lors de la mise à jour des informations de base du professionnel",
      );
    }
  }
}
