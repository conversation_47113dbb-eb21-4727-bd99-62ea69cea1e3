import { ProfessionalCompleteDTO } from "@/domain/DTOS";
import { IGetProfessionalsRepository } from "@/domain/interfaces/repositories/professionals/IGetProfessionalsRepository";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_TABLE_NAME } from "./constants";

class GetProfessionalsRepository implements IGetProfessionalsRepository {
  async execute() {
    const { data, error } = await supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select(`*, specialites_professionnel(*)`);

    if (error) throw error;

    return data as ProfessionalCompleteDTO[];
  }
}

export default GetProfessionalsRepository;

