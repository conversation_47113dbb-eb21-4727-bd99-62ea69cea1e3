import { IGetProfessionalProfileCompleteRepository } from "@/domain/interfaces/repositories/professionals/IGetProfessionalProfileCompleteRepository";
import { supabase } from "@/infrastructure/supabase/supabase";

// Constantes des noms de tables
import {
  USER_TABLE_NAME,
  CONTACT_TABLE_NAME,
  PHOTO_TABLE_NAME,
  PROFESSIONAL_TABLE_NAME,
  PROFESSIONAL_SPECIALITIES_TABLE_NAME,
  PROFESSIONAL_DIPLOMA_TABLE_NAME,
  PROFESSIONAL_EXPERIENCE_TABLE_NAME,
  PROFESSIONAL_LANGUAGE_TABLE_NAME,
  PROFESSIONAL_MOT_CLES_TABLE_NAME,
  PROFESSIONAL_INSURANCE_TABLE_NAME,
  INSURANCE_LIST_TABLE_NAME,
  ETABLISHMENT_PROFESSIONNEL_TABLE_NAME,
  PROFESSIONAL_PUBLICATION_TABLE_NAME,
} from "./constants";

/**
 * Repository optimisé pour récupérer toutes les données du profil professionnel
 *
 * @description Ce repository récupère toutes les informations nécessaires pour construire
 * un profil professionnel complet en une seule requête optimisée avec toutes les jointures
 * nécessaires. Cela améliore considérablement les performances par rapport aux multiples
 * requêtes séparées.
 *
 * @architecture
 * - Utilise une seule requête Supabase avec toutes les jointures nécessaires
 * - Respecte l'interface IGetProfessionalProfileCompleteRepository
 * - Gère les erreurs de manière centralisée
 * - Retourne un DTO structuré selon ProfessionalProfileDTO
 *
 * @performance
 * Cette approche réduit le nombre de requêtes de ~12 requêtes séparées à 1 seule requête,
 * améliorant significativement les performances et réduisant la latence réseau.
 */
export class GetProfessionalProfileCompleteRepository
  implements IGetProfessionalProfileCompleteRepository
{
  /**
   * Récupère toutes les données du profil professionnel en une seule requête optimisée
   *
   * @param professionalId - L'identifiant unique du professionnel
   * @returns Promise<ProfessionalProfileDTO | null> - Les données complètes du profil ou null si non trouvé
   *
   * @throws {Error} En cas d'erreur lors de la récupération des données
   */
  async execute(professionalId: number): Promise<any | null> {
    try {
      const { data, error } = await supabase
        .from(USER_TABLE_NAME)
        .select(
          `*,
          contact:${CONTACT_TABLE_NAME}(*),
          photos:${PHOTO_TABLE_NAME}(*),
          professionnel:${PROFESSIONAL_TABLE_NAME}(
            *,
            specialites_professionnel:${PROFESSIONAL_SPECIALITIES_TABLE_NAME}(*),
            diplome_professionnel:${PROFESSIONAL_DIPLOMA_TABLE_NAME}(*),
            experience_professionnel:${PROFESSIONAL_EXPERIENCE_TABLE_NAME}(*),
            publication_professionnel:${PROFESSIONAL_PUBLICATION_TABLE_NAME}(*),
            langues_parlees_professionnel:${PROFESSIONAL_LANGUAGE_TABLE_NAME}(*),
            mot_cles_professionnel:${PROFESSIONAL_MOT_CLES_TABLE_NAME}(*),
            assurances_professionnel:${PROFESSIONAL_INSURANCE_TABLE_NAME}(
              assurance:${INSURANCE_LIST_TABLE_NAME}(*)
            ),
            etablissements_professionnel:${ETABLISHMENT_PROFESSIONNEL_TABLE_NAME}(*)
          )`,
        )
        .eq("id", professionalId);

      if (error) {
        throw error;
      }

      if (!data) {
        return null;
      }

      return data[0];
    } catch (error) {
      console.error(
        "Erreur lors de la récupération du profil professionnel complet:",
        error,
      );
      throw error;
    }
  }
}
