import { IUpdateProfessionalPresentationRepository } from "@/domain/interfaces/repositories/professionals";
import { Professionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_TABLE_NAME } from "./constants";

/**
 * Repository pour la mise à jour de la présentation du professionnel
 */
export class UpdateProfessionalPresentationRepository implements IUpdateProfessionalPresentationRepository {
  /**
   * Met à jour la présentation générale du professionnel
   * @param professionalId - ID du professionnel
   * @param presentation - Nouvelle présentation générale
   * @returns Professionnel mis à jour ou null en cas d'erreur
   */
  async execute(
    professionalId: number,
    presentation: string
  ): Promise<Professionnel | null> {
    try {
      const { data, error } = await supabase
        .from(PROFESSIONAL_TABLE_NAME)
        .update({ presentation_generale: presentation })
        .eq("id", professionalId)
        .select()
        .single();

      if (error) {
        console.error("Erreur lors de la mise à jour de la présentation:", error);
        throw error;
      }

      return data as Professionnel;
    } catch (error) {
      console.error("Erreur dans UpdateProfessionalPresentationRepository:", error);
      return null;
    }
  }
}
