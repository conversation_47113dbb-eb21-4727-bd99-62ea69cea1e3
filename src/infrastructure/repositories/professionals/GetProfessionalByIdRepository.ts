import { IGetProfessionalByIdRepository } from "@/domain/interfaces/repositories/professionals/IGetProfessionalByIdRepository";
import { Professionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_TABLE_NAME } from "./constants";

export class GetProfessionalByIdRepository
  implements IGetProfessionalByIdRepository
{
  async execute(id: number): Promise<Professionnel | null> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select(`*`)
      .eq("id", id);

    if (error) throw error;

    if (!data || data.length === 0) return null;

    return data[0] as Professionnel;
  }
}
