import { IUpdateProfessionalServicesRepository } from "@/domain/interfaces/repositories/professionals";
import { Professionnel } from "@/domain/models";
import { professionnels_types_consultation_enum } from "@/domain/models/enums";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_TABLE_NAME } from "./constants";

/**
 * Repository pour la mise à jour des services du professionnel
 */
export class UpdateProfessionalServicesRepository implements IUpdateProfessionalServicesRepository {
  /**
   * Met à jour les informations de services du professionnel
   * @param professionalId - ID du professionnel
   * @param servicesData - Données des services à mettre à jour
   * @returns Professionnel mis à jour ou null en cas d'erreur
   */
  async execute(
    professionalId: number,
    servicesData: {
      types_consultation?: professionnels_types_consultation_enum;
      nouveau_patient_acceptes?: boolean;
    }
  ): Promise<Professionnel | null> {
    try {
      // Construire l'objet de mise à jour avec seulement les champs fournis
      const updateData: Partial<Professionnel> = {};
      
      if (servicesData.types_consultation !== undefined) {
        updateData.types_consultation = servicesData.types_consultation;
      }
      
      if (servicesData.nouveau_patient_acceptes !== undefined) {
        updateData.nouveau_patient_acceptes = servicesData.nouveau_patient_acceptes;
      }

      const { data, error } = await supabase
        .from(PROFESSIONAL_TABLE_NAME)
        .update(updateData)
        .eq("id", professionalId)
        .select()
        .single();

      if (error) {
        console.error("Erreur lors de la mise à jour des services:", error);
        throw error;
      }

      return data as Professionnel;
    } catch (error) {
      console.error("Erreur dans UpdateProfessionalServicesRepository:", error);
      return null;
    }
  }
}
