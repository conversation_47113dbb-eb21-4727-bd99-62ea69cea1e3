import { supabase } from "@/infrastructure/supabase/supabase";
import {
  PROFESSIONAL_TABLE_NAME,
  APPOINTMNT_TABLE_NAME,
  PROFESSIONAL_SPECIALITIES_TABLE_NAME,
  EVENT_TABLE_NAME,
  ETABLISHMENT_PROFESSIONNEL_TABLE_NAME,
  AVAILABILITY_SETTINGS_TABLE_NAME,
  TIME_SLOT_TABLE_NAME,
  HORAIRE_DATE_SPECIFIQUE_TABLE_NAME,
  HORAIRE_HEBDOMADAIRE_TABLE_NAME,
  CONTACT_TABLE_NAME,
  PROFESSIONAL_PUBLICATION_TABLE_NAME,
  PROFESSIONAL_EXPERIENCE_TABLE_NAME,
  PROFESSIONAL_DIPLOMA_TABLE_NAME,
  PROFESSIONAL_LANGUAGE_TABLE_NAME,
  PROFESSIONAL_MOT_CLES_TABLE_NAME,
  PHOTO_TABLE_NAME,
  USER_TABLE_NAME,
} from "./constants";
import { professionnels_titre_enum } from "@/domain/models/enums/professionnelsTitreEnum.ts";

class GetProfessionalProfileRepository {
  constructor() {}

  async execute(
    title: professionnels_titre_enum,
    firstName: string,
    lastName: string,
    address: string
  ) {
    const { data, error } = await supabase
      .from(USER_TABLE_NAME)
      .select(
        `
         email,
         ${CONTACT_TABLE_NAME}(*),
         ${PHOTO_TABLE_NAME}(*),
         ${PROFESSIONAL_TABLE_NAME}(
            *,
            ${PROFESSIONAL_SPECIALITIES_TABLE_NAME}(*),
            ${APPOINTMNT_TABLE_NAME}(*),
            ${EVENT_TABLE_NAME}(*),
            ${ETABLISHMENT_PROFESSIONNEL_TABLE_NAME}(*),
            ${PROFESSIONAL_PUBLICATION_TABLE_NAME}(*),
            ${PROFESSIONAL_EXPERIENCE_TABLE_NAME}(*),
            ${PROFESSIONAL_DIPLOMA_TABLE_NAME}(*),
            ${PROFESSIONAL_LANGUAGE_TABLE_NAME}(*),
            ${PROFESSIONAL_MOT_CLES_TABLE_NAME}(*),
            ${AVAILABILITY_SETTINGS_TABLE_NAME}(
              *,
              ${HORAIRE_HEBDOMADAIRE_TABLE_NAME}(
                *,
                ${TIME_SLOT_TABLE_NAME}(*)
              ),
              ${HORAIRE_DATE_SPECIFIQUE_TABLE_NAME}(
                *,
                ${TIME_SLOT_TABLE_NAME}(*)
              )
            )
          )
        `
      )
      .eq("professionnels.titre", title)
      .ilike("professionnels.nom", `%${firstName}%`)
      .ilike("professionnels.prenom", `%${lastName}%`)
      .ilike("professionnels.adresse", `%${address}%`)
      .eq("role", "professionnel");

    if (error) throw error;

    if (!data) return null;

    return data[0];
  }
}

export default GetProfessionalProfileRepository;
