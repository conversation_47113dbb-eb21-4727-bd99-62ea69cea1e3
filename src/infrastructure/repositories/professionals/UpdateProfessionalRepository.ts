import { Professionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_TABLE_NAME } from "./constants";
import { IUpdateProfessionalRepository } from "@/domain/interfaces/repositories/professionals";

class UpdateProfessionalRepository implements IUpdateProfessionalRepository {
  constructor() {}

  async execute(
    id: number,
    professional: Partial<Professionnel>,
  ): Promise<Professionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .update(professional)
      .eq("id", id)
      .select();

    if (error) throw error;

    return data[0] as Professionnel;
  }
}

export default UpdateProfessionalRepository;
