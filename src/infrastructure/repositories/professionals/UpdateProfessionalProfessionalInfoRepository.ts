import { IUpdateProfessionalProfessionalInfoRepository } from "@/domain/interfaces/repositories/professionals";
import { Professionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_TABLE_NAME } from "./constants";

/**
 * Repository pour la mise à jour des informations professionnelles
 */
export class UpdateProfessionalProfessionalInfoRepository implements IUpdateProfessionalProfessionalInfoRepository {
  /**
   * Met à jour les informations professionnelles du professionnel
   * @param professionalId - ID du professionnel
   * @param professionalData - Données professionnelles à mettre à jour
   * @returns Professionnel mis à jour ou null en cas d'erreur
   */
  async execute(
    professionalId: number,
    professionalData: {
      numero_ordre?: string;
      raison_sociale?: string;
      nif?: string;
      stat?: string;
    }
  ): Promise<Professionnel | null> {
    try {
      // Construire l'objet de mise à jour avec seulement les champs fournis
      const updateData: Partial<Professionnel> = {};
      
      if (professionalData.numero_ordre !== undefined) {
        updateData.numero_ordre = professionalData.numero_ordre;
      }
      
      if (professionalData.raison_sociale !== undefined) {
        updateData.raison_sociale = professionalData.raison_sociale;
      }
      
      if (professionalData.nif !== undefined) {
        updateData.nif = professionalData.nif;
      }
      
      if (professionalData.stat !== undefined) {
        updateData.stat = professionalData.stat;
      }

      const { data, error } = await supabase
        .from(PROFESSIONAL_TABLE_NAME)
        .update(updateData)
        .eq("id", professionalId)
        .select()
        .single();

      if (error) {
        console.error("Erreur lors de la mise à jour des informations professionnelles:", error);
        throw error;
      }

      return data as Professionnel;
    } catch (error) {
      console.error("Erreur dans UpdateProfessionalProfessionalInfoRepository:", error);
      return null;
    }
  }
}
