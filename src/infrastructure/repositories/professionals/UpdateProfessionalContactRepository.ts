import { 
  IUpdateProfessionalContactRepository, 
  IUpdateProfessionalPhoneRepository 
} from "@/domain/interfaces/repositories/professionals";
import { Professionnel, Contact } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_TABLE_NAME, CONTACT_TABLE_NAME } from "./constants";

/**
 * Repository pour la mise à jour des informations de contact du professionnel
 */
export class UpdateProfessionalContactRepository implements IUpdateProfessionalContactRepository {
  /**
   * Met à jour les informations de contact et d'adresse du professionnel
   * @param professionalId - ID du professionnel
   * @param contactData - Données de contact à mettre à jour
   * @returns Professionnel mis à jour ou null en cas d'erreur
   */
  async execute(
    professionalId: number,
    contactData: {
      adresse?: string;
      fokontany?: string;
      informations_acces?: string;
    }
  ): Promise<Professionnel | null> {
    try {
      // Construire l'objet de mise à jour avec seulement les champs fournis
      const updateData: Partial<Professionnel> = {};
      
      if (contactData.adresse !== undefined) {
        updateData.adresse = contactData.adresse;
      }
      
      if (contactData.fokontany !== undefined) {
        updateData.fokontany = contactData.fokontany;
      }
      
      if (contactData.informations_acces !== undefined) {
        updateData.informations_acces = contactData.informations_acces;
      }

      const { data, error } = await supabase
        .from(PROFESSIONAL_TABLE_NAME)
        .update(updateData)
        .eq("id", professionalId)
        .select()
        .single();

      if (error) {
        console.error("Erreur lors de la mise à jour des informations de contact:", error);
        throw error;
      }

      return data as Professionnel;
    } catch (error) {
      console.error("Erreur dans UpdateProfessionalContactRepository:", error);
      return null;
    }
  }
}

/**
 * Repository pour la mise à jour du numéro de téléphone du professionnel
 */
export class UpdateProfessionalPhoneRepository implements IUpdateProfessionalPhoneRepository {
  /**
   * Met à jour le numéro de téléphone du professionnel
   * @param userId - ID de l'utilisateur
   * @param phoneNumber - Nouveau numéro de téléphone
   * @returns Contact mis à jour ou null en cas d'erreur
   */
  async execute(
    userId: number,
    phoneNumber: string
  ): Promise<Contact | null> {
    try {
      const { data, error } = await supabase
        .from(CONTACT_TABLE_NAME)
        .update({ numero: phoneNumber })
        .eq("utilisateur_id", userId)
        .select()
        .single();

      if (error) {
        console.error("Erreur lors de la mise à jour du téléphone:", error);
        throw error;
      }

      return data as Contact;
    } catch (error) {
      console.error("Erreur dans UpdateProfessionalPhoneRepository:", error);
      return null;
    }
  }
}
