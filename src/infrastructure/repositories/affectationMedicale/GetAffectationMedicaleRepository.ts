import { AffectationMedical } from '@/domain/models'
import { IGetAffectationMedicaleRepository } from '@/domain/interfaces/repositories/affectationMedicale'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { AFFECTATION_MEDICAL_TABLE_NAME } from './Constant'

export class GetAffectationMedicaleRepository implements IGetAffectationMedicaleRepository {
  async getById(id: number): Promise<AffectationMedical | null> {
    const { data, error } = await supabase
      .from(AFFECTATION_MEDICAL_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      handleError(error)
    }

    return data
  }

  async getAll(carnetId: number): Promise<AffectationMedical[]> {
    const { data, error } = await supabase
      .from(AFFECTATION_MEDICAL_TABLE_NAME)
      .select('*')
      .eq('id_carnet', carnetId)

    if (error) {
      handleError(error)
    }

    return data || []
  }
}
