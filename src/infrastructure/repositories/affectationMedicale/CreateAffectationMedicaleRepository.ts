import { AffectationMedical } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { AFFECTATION_MEDICAL_TABLE_NAME } from './Constant'
import { ICreateAffectationMedicaleRepository } from '@/domain/interfaces/repositories/affectationMedicale'

export class CreateAffectationMedicaleRepository implements ICreateAffectationMedicaleRepository {
  async create(data: Omit<AffectationMedical, "id">[]): Promise<AffectationMedical[]> {
    const { data: createdData, error } = await supabase
      .from(AFFECTATION_MEDICAL_TABLE_NAME)
      .insert(data)
      .select()

    if (error) {
      handleError(error)
    }

    return createdData as AffectationMedical[]
  }
}
