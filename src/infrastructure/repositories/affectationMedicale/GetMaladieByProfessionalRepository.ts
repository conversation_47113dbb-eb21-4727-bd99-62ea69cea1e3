import { supabase } from "@/infrastructure/supabase/supabase";
import { AFFECTATION_MEDICAL_TABLE_NAME } from "./Constant";
import {
    IGetMaladieByProfessionalRepository,
    MaladieProfessionalStats
} from "@/domain/interfaces/repositories/affectationMedicale";

class GetMaladieByProfessionalRepository implements IGetMaladieByProfessionalRepository {
    async execute(professionalId: number): Promise<MaladieProfessionalStats[]> {
        const { data, error } = await supabase
            .from(AFFECTATION_MEDICAL_TABLE_NAME)
            .select(`
                maladie,
                carnet_sante!inner(
                    id,
                    utilisateurs!inner(
                        id,
                        role,
                        patients!inner(
                            id,
                            professionnel_patient!inner(
                                id_professionnel,
                                is_delete
                            )
                        )
                    )
                )
            `)
            .eq('carnet_sante.utilisateurs.patients.professionnel_patient.id_professionnel', professionalId)
            .eq('carnet_sante.utilisateurs.patients.professionnel_patient.is_delete', false);

        if (error) throw error;

        // Group by maladie and count repetitions manually
        const groupedData = (data || []).reduce((acc, item) => {
            const maladie = item.maladie;
            if (acc[maladie]) {
                acc[maladie]++;
            } else {
                acc[maladie] = 1;
            }
            return acc;
        }, {} as Record<string, number>);

        // Transform to array format and sort by repetitions DESC
        return Object.entries(groupedData)
            .map(([maladie, repetitions]) => ({
                maladie,
                repetitions
            }))
            .sort((a, b) => b.repetitions - a.repetitions);
    }
}

export default GetMaladieByProfessionalRepository;