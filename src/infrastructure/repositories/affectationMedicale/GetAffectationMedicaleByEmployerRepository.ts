import { supabase } from "@/infrastructure/supabase/supabase";
import { AFFECTATION_MEDICAL_TABLE_NAME } from "./Constant";
import {
    IGetAffectationMedicaleByEmployerRepository,
    AffectationMedicaleEmployerStats
} from "@/domain/interfaces/repositories/affectationMedicale/IGetAffectationMedicaleByEmployerRepository";

class GetAffectationMedicaleByEmployerRepository implements IGetAffectationMedicaleByEmployerRepository {
    async execute(): Promise<AffectationMedicaleEmployerStats[]> {
        const { data, error } = await supabase
            .from(AFFECTATION_MEDICAL_TABLE_NAME)
            .select(`
        maladie,
        carnet_sante!inner(
          utilisateurs!inner(role)
        )
      `)
            .eq('carnet_sante.utilisateurs.role', 'employer');

        if (error) throw error;

        // Group by maladie and count repetitions manually
        const groupedData = (data || []).reduce((acc, item) => {
            const maladie = item.maladie;
            if (acc[maladie]) {
                acc[maladie]++;
            } else {
                acc[maladie] = 1;
            }
            return acc;
        }, {} as Record<string, number>);

        // Transform to array format
        return Object.entries(groupedData).map(([maladie, repetitions]) => ({
            maladie,
            repetitions
        }));
    }
}

export default GetAffectationMedicaleByEmployerRepository;
