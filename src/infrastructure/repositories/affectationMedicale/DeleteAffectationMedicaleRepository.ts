import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { AFFECTATION_MEDICAL_TABLE_NAME } from './Constant'
import { IDeleteAffectationMedicaleRepository } from '@/domain/interfaces/repositories/affectationMedicale'

export class DeleteAffectationMedicaleRepository implements IDeleteAffectationMedicaleRepository {
  async delete(id: number): Promise<void> {
    const { error } = await supabase
      .from(AFFECTATION_MEDICAL_TABLE_NAME)
      .delete()
      .eq('id', id)

    if (error) {
      handleError(error)
    }
  }
}
