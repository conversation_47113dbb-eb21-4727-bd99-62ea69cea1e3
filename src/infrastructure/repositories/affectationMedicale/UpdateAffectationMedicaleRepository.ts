import { AffectationMedical } from '@/domain/models'
import { IUpdateAffectationMedicaleRepository } from '@/domain/interfaces/repositories/affectationMedicale'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { AFFECTATION_MEDICAL_TABLE_NAME } from './Constant'

export class UpdateAffectationMedicaleRepository implements IUpdateAffectationMedicaleRepository {
  async update(id: number, data: Partial<AffectationMedical>): Promise<AffectationMedical> {
    const { data: updatedData, error } = await supabase
      .from(AFFECTATION_MEDICAL_TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      handleError(error)
    }

    return updatedData
  }
}
