import { Utilisateur } from "@/domain/models/Utilisateurs.ts";
import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { USER_TABLE_NAME } from "./constants.ts";
import { IUpdateUserRepository } from "@/domain/interfaces/repositories/user/IUpdateUserRepository.ts";

class UpdateUserRepository implements IUpdateUserRepository {
  constructor() {}

  async execute(id: number, userData: Partial<Utilisateur>) {
    if (!id) throw new Error("L'id est requis pour la modification");
    const { data, error } = await supabase
      .from(USER_TABLE_NAME)
      .update(userData)
      .eq("id", id)
      .select();

    if (error) throw error;

    return data[0] as Utilisateur;
  }
}

export default UpdateUserRepository;
