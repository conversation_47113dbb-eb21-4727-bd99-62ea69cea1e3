import { IGetUsersCanMessagingRepository } from "@/domain/interfaces/repositories/user";
import { supabase } from "@/infrastructure/supabase/supabase";
import { USER_TABLE_NAME } from "./constants";
import { Utilisateur } from "@/domain/models";
import { utilisateurs_role_enum } from "@/domain/models/enums";

class GetUsersCanMessagingRepository
  implements IGetUsersCanMessagingRepository
{
  async execute() {
    const { data, error } = await supabase
      .from(USER_TABLE_NAME)
      .select("*")
      .in("role", [
        utilisateurs_role_enum.PATIENT,
        utilisateurs_role_enum.PROFESSIONNEL,
        utilisateurs_role_enum.ADMIN,
      ])
      .not("email", "is", null);

    if (error) throw error;

    return data as Utilisateur[];
  }
}

export default GetUsersCanMessagingRepository;
