import { AntecedantChirurgicaux } from '@/domain/models'
import { IGetAntecedantChirurgicauxRepository } from '@/domain/interfaces/repositories/antecedantChirurgicaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDANT_CHIRURGICAUX_TABLE_NAME } from './Constant'

export class GetAntecedantChirurgicauxRepository implements IGetAntecedantChirurgicauxRepository {
  async getById(id: number): Promise<AntecedantChirurgicaux | null> {
    const { data, error } = await supabase
      .from(ANTECEDANT_CHIRURGICAUX_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      handleError(error)
    }

    return data
  }

  async getAll(carnetId: number): Promise<AntecedantChirurgicaux[]> {
    const { data, error } = await supabase
      .from(ANTECEDANT_CHIRURGICAUX_TABLE_NAME)
      .select('*')
      .eq('id_carnet', carnetId)

    if (error) {
      handleError(error)
    }

    return data || []
  }
}
