import { AntecedantChirurgicaux } from '@/domain/models'
import { IUpdateAntecedantChirurgicauxRepository } from '@/domain/interfaces/repositories/antecedantChirurgicaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDANT_CHIRURGICAUX_TABLE_NAME } from './Constant'

export class UpdateAntecedantChirurgicauxRepository implements IUpdateAntecedantChirurgicauxRepository {
  async update(id: number, data: Partial<AntecedantChirurgicaux>): Promise<AntecedantChirurgicaux> {
    const { data: updatedData, error } = await supabase
      .from(ANTECEDANT_CHIRURGICAUX_TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      handleError(error)
    }

    return updatedData
  }
}
