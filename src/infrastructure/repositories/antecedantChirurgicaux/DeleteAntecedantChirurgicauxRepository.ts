import { IDeleteAntecedantChirurgicauxRepository } from '@/domain/interfaces/repositories/antecedantChirurgicaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDANT_CHIRURGICAUX_TABLE_NAME } from './Constant'

export class DeleteAntecedantChirurgicauxRepository implements IDeleteAntecedantChirurgicauxRepository {
  async delete(id: number): Promise<void> {
    const { error } = await supabase
      .from(ANTECEDANT_CHIRURGICAUX_TABLE_NAME)
      .delete()
      .eq('id', id)

    if (error) {
      handleError(error)
    }
  }
}
