import { AntecedantChirurgicaux } from '@/domain/models'
import { ICreateAntecedantChirurgicauxRepository } from '@/domain/interfaces/repositories/antecedantChirurgicaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDANT_CHIRURGICAUX_TABLE_NAME } from './Constant'

export class CreateAntecedantChirurgicauxRepository implements ICreateAntecedantChirurgicauxRepository {
  async create(data: Omit<AntecedantChirurgicaux, "id">[]): Promise<AntecedantChirurgicaux[]> {
    const { data: createdData, error } = await supabase
      .from(ANTECEDANT_CHIRURGICAUX_TABLE_NAME)
      .insert(data)
      .select()

    if (error) {
      handleError(error)
    }

    return createdData as AntecedantChirurgicaux[]
  }
}
