import { InvitationDash } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { INVITATION_DASH_TABLE_NAME } from "./constants";
import { IGetInvitationDashByTokenRepository } from "@/domain/interfaces/repositories/invitationDash/IGetInvitationDashByTokenRepository";

class GetInvitationDashByTokenRepository implements IGetInvitationDashByTokenRepository {
  constructor() {}

  async execute(token: string): Promise<InvitationDash | null> {
    const { data, error } = await supabase
      .from(INVITATION_DASH_TABLE_NAME)
      .select("*")
      .eq("token", token)
      .single();

    if (error) {
      // Si l'erreur est "PGRST116" (aucun résultat trouvé), on retourne null
      if (error.code === "PGRST116") {
        return null;
      }
      throw error;
    }

    return data as InvitationDash;
  }
}

export default GetInvitationDashByTokenRepository;
