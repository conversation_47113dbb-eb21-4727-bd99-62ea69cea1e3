import { InvitationDash } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { INVITATION_DASH_TABLE_NAME } from "./constants";
import { IGetInvitationsDashRepository } from "@/domain/interfaces/repositories/invitationDash/IGetInvitationsDashRepository";

class GetInvitationsDashRepository implements IGetInvitationsDashRepository {
  constructor() {}

  async execute(): Promise<InvitationDash[]> {
    const { data, error } = await supabase
      .from(INVITATION_DASH_TABLE_NAME)
      .select("*")
      .order("cree_le", { ascending: false });

    if (error) {
      throw error;
    }

    return data as InvitationDash[];
  }
}

export default GetInvitationsDashRepository;
