import { InvitationDash } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { INVITATION_DASH_TABLE_NAME } from "./constants";
import { ICreateInvitationDashRepository } from "@/domain/interfaces/repositories/invitationDash/ICreateInvitationDashRepository";

class CreateInvitationDashRepository
  implements ICreateInvitationDashRepository
{
  constructor() {}

  async execute(
    invitation: Omit<InvitationDash, "id">
  ): Promise<InvitationDash> {
    const { data, error } = await supabase
      .from(INVITATION_DASH_TABLE_NAME)
      .insert(invitation)
      .select();

    if (error) {
      throw error;
    }

    return data[0] as InvitationDash;
  }
}

export default CreateInvitationDashRepository;
