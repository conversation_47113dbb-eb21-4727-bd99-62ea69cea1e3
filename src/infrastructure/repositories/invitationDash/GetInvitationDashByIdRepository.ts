import { InvitationDash } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { INVITATION_DASH_TABLE_NAME } from "./constants";
import { IGetInvitationDashByIdRepository } from "@/domain/interfaces/repositories/invitationDash/IGetInvitationDashByIdRepository";

class GetInvitationDashByIdRepository
  implements IGetInvitationDashByIdRepository
{
  constructor() {}

  async execute(invitation_id: number): Promise<InvitationDash | null> {
    const { data, error } = await supabase
      .from(INVITATION_DASH_TABLE_NAME)
      .select("*")
      .eq("id", invitation_id)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // Aucun résultat trouvé
        return null;
      }
      throw error;
    }

    return data as InvitationDash;
  }
}

export default GetInvitationDashByIdRepository;
