import { InvitationDash } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { INVITATION_DASH_TABLE_NAME } from "./constants";
import { IMarkInvitationDashAsUsedRepository } from "@/domain/interfaces/repositories/invitationDash/IMarkInvitationDashAsUsedRepository";

class MarkInvitationDashAsUsedRepository
  implements IMarkInvitationDashAsUsedRepository
{
  constructor() {}

  async execute(invitation_id: number): Promise<InvitationDash> {
    const { data, error } = await supabase
      .from(INVITATION_DASH_TABLE_NAME)
      .update({ est_utilisee: true })
      .eq("id", invitation_id)
      .select();

    if (error) throw error;

    return data[0] as InvitationDash;
  }
}

export default MarkInvitationDashAsUsedRepository;
