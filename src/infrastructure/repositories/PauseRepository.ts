import { IPauseRepository } from '@/domain/interfaces/repositories/IPauseRepository'
import { pause } from '@/domain/models'
import { supabase } from '../supabase/supabase'
import { handleError } from '../supabase/supabaseFetchError'

const PAUSE_TABLE_NAME = 'pause'
export class PauseRepository implements IPauseRepository {
  async getPausesByAvailabilitySettingsId(availabilitySettingsId: number) {
    const { data, error } = await supabase
      .from(PAUSE_TABLE_NAME)
      .select(`*`)
      .eq('id_parametre_disponibilite', availabilitySettingsId)
    handleError(error)

    return data as pause[]
  }

  async getPauseById(id: number) {
    const { data, error } = await supabase.from(PAUSE_TABLE_NAME).select('*').eq('id', id).single()

    handleError(error)

    return data as pause
  }

  async createPause(pauses: Omit<pause, 'id'>[]) {
    const { data, error } = await supabase.from(PAUSE_TABLE_NAME).insert(pauses).select()

    handleError(error)

    return data as pause[]
  }

  async updatePause(id: number, pause: Omit<pause, 'id'>) {
    const { data, error } = await supabase
      .from(PAUSE_TABLE_NAME)
      .update(pause)
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as pause
  }

  async deletePause(id: number) {
    const { data, error } = await supabase
      .from(PAUSE_TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as pause
  }
}
