import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetDistrictsRepository } from "@/domain/interfaces/repositories/district/IGetDistrictsRepository";
import { District } from "@/domain/models/District";
import { DISTRICT_TABLE_NAME } from "./constants";

export class GetDistrictsRepository implements IGetDistrictsRepository {
  async execute(): Promise<District[]> {
    const { data, error } = await supabase.from(DISTRICT_TABLE_NAME).select('*')

    if (error) throw error

    return data as District[]
  }
}
