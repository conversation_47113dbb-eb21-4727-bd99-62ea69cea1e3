import { supabase } from "@/infrastructure/supabase/supabase";
import { DISTRICT_FILTER_LIMIT, DISTRICT_TABLE_NAME } from "./constants";
import { District } from "@/domain/models";
import { ISearchDistrictsByNameRepository } from "@/domain/interfaces/repositories/district";

class SearchDistrictsByNameRepository
  implements ISearchDistrictsByNameRepository {
  async execute(searchTerm: string) {
    const { data, error } = await supabase
      .from(DISTRICT_TABLE_NAME)
      .select("*")
      .or(`libelle.ilike.%${searchTerm}%`)
      .limit(DISTRICT_FILTER_LIMIT);

    if (error) throw error;

    return data as District[];
  }
}

export default SearchDistrictsByNameRepository;
