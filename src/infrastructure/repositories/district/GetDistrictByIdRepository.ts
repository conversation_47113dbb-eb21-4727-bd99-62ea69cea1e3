import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetDistrictByIdRepository } from "@/domain/interfaces/repositories/district/IGetDistrictByIdRepository";
import { District } from "@/domain/models/District";
import { DISTRICT_TABLE_NAME } from "./constants";

export class GetDistrictByIdRepository implements IGetDistrictByIdRepository {

  async execute(id: number): Promise<District> {
    const { data, error } = await supabase.from(DISTRICT_TABLE_NAME).select('*').eq('id', id).single()

    if (error) throw error

    return data as District
  }
}
