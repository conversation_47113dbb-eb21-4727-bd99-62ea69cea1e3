import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetDistrictsInRegionRepository } from "@/domain/interfaces/repositories/district/IGetDistrictsInRegionRepository";
import { District } from "@/domain/models/District";
import { DISTRICT_TABLE_NAME } from "./constants";

export class GetDistrictsInRegionRepository implements IGetDistrictsInRegionRepository {
  async execute(regionId: number): Promise<District[]> {
    const { data, error } = await supabase
      .from(DISTRICT_TABLE_NAME)
      .select('*')
      .eq('id_region', regionId);

    if (error) throw error;

    return data as District[];
  }
}
