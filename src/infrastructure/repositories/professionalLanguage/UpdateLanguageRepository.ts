import { IUpdateLanguageRepository } from "@/domain/interfaces/repositories/professionalLanguage";
import { LangueParleeProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_LANGUAGE_TABLE_NAME } from "./constants";

/**
 * Repository pour la mise à jour des langues du professionnel
 */
export class UpdateLanguageRepository implements IUpdateLanguageRepository {
  /**
   * Met à jour une langue parlée par le professionnel
   * @param languageId - ID de la langue à mettre à jour
   * @param languageData - Nouvelles données de la langue
   * @returns Langue mise à jour ou null en cas d'erreur
   */
  async execute(
    languageId: number,
    languageData: Partial<Omit<LangueParleeProfessionnel, "id" | "id_professionnel">>
  ): Promise<LangueParleeProfessionnel | null> {
    try {
      const { data, error } = await supabase
        .from(PROFESSIONAL_LANGUAGE_TABLE_NAME)
        .update(languageData)
        .eq("id", languageId)
        .select()
        .single();

      if (error) {
        console.error("Erreur lors de la mise à jour de la langue:", error);
        throw error;
      }

      return data as LangueParleeProfessionnel;
    } catch (error) {
      console.error("Erreur dans UpdateLanguageRepository:", error);
      return null;
    }
  }
}
