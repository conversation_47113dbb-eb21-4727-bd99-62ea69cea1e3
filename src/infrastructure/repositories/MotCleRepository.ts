import { ListeMotCle, MotClesProfessionnel } from '@/domain/models'
import { supabase } from '../supabase/supabase'
import { handleError } from '../supabase/supabaseFetchError'

const MOT_CLE_TABLE_NAME = 'mot_cles_professionnel'
const MOT_CLE_TABLE_NAME_LIST = 'mot_cles_professionnel'

class MotCleRepository {
  async getAllMotCles(): Promise<ListeMotCle[]> {
    const { data, error } = await supabase.from(MOT_CLE_TABLE_NAME_LIST).select('*')

    handleError(error)

    return data as ListeMotCle[]
  }

  async getMotClesById(id: number): Promise<ListeMotCle> {
    const { data, error } = await supabase
      .from(MOT_CLE_TABLE_NAME_LIST)
      .select('*')
      .eq('id', id)
      .single()

    handleError(error)

    return data as ListeMotCle
  }

  async getMotCleById(id: number) {
    const { data, error } = await supabase.from(MOT_CLE_TABLE_NAME).select('*').eq('id', id)

    handleError(error)

    return data[0] as MotClesProfessionnel
  }

  async getAllProfessionnalMotCles(professionnal_id: number): Promise<MotClesProfessionnel[]> {
    const { data, error } = await supabase
      .from(MOT_CLE_TABLE_NAME)
      .select('*')
      .eq('id_professionnel', professionnal_id)

    handleError(error)

    return data as MotClesProfessionnel[]
  }

  async addProfessionnalMotClee(
    newMotClee: Omit<MotClesProfessionnel, 'id'>
  ): Promise<MotClesProfessionnel> {
    const { data, error } = await supabase.from(MOT_CLE_TABLE_NAME).insert(newMotClee).select()

    handleError(error)

    return data[0] as MotClesProfessionnel
  }

  async deleteProfessionnalMotClee(id: number): Promise<MotClesProfessionnel> {
    const { data, error } = await supabase
      .from(MOT_CLE_TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as MotClesProfessionnel
  }
}

export const motCleRepository = new MotCleRepository()
