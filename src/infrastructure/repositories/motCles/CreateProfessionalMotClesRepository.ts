import { mot_cles } from "@/domain/models/MotCles";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_MOT_CLES_TABLE_NAME } from "./constants";
import { ICreateProfessionalMotClesRepository } from "@/domain/interfaces/repositories/motcles";
import { MotClesProfessionnel } from "@/domain/models";

class CreateProfessionalMotClesRepository
  implements ICreateProfessionalMotClesRepository {
  public async execute(motCles: Omit<mot_cles, "id">[]): Promise<MotClesProfessionnel[]> {

    const { data, error } = await supabase
      .from(PROFESSIONAL_MOT_CLES_TABLE_NAME)
      .insert(motCles)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as MotClesProfessionnel[];
  }
}

export default CreateProfessionalMotClesRepository;
