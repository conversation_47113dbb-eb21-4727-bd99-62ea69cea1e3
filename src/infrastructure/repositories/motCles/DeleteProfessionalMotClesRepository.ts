import { IDeleteProfessionalMotClesRepository } from "@/domain/interfaces/repositories/motcles";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_MOT_CLES_TABLE_NAME } from "./constants";

/**
 * Repository pour la suppression des mots-clés du professionnel
 */
export class DeleteProfessionalMotClesRepository implements IDeleteProfessionalMotClesRepository {
  /**
   * Supprime un mot-clé du professionnel
   * @param motCleId - ID du mot-clé à supprimer
   * @returns true si la suppression a réussi, false sinon
   */
  async execute(motCleId: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from(PROFESSIONAL_MOT_CLES_TABLE_NAME)
        .delete()
        .eq("id", motCleId);

      if (error) {
        console.error("Erreur lors de la suppression du mot-clé:", error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error("Erreur dans DeleteProfessionalMotClesRepository:", error);
      return false;
    }
  }
}
