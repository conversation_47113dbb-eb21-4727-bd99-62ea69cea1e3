import { IUpdateProfessionalMotClesRepository } from "@/domain/interfaces/repositories/motcles";
import { MotClesProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_MOT_CLES_TABLE_NAME } from "./constants";

/**
 * Repository pour la mise à jour des mots-clés du professionnel
 */
export class UpdateProfessionalMotClesRepository implements IUpdateProfessionalMotClesRepository {
  /**
   * Met à jour un mot-clé du professionnel
   * @param motCleId - ID du mot-clé à mettre à jour
   * @param motCleData - Nouvelles données du mot-clé
   * @returns Mot-clé mis à jour ou null en cas d'erreur
   */
  async execute(
    motCleId: number,
    motCleData: Partial<Omit<MotClesProfessionnel, "id" | "id_professionnel">>
  ): Promise<MotClesProfessionnel | null> {
    try {
      const { data, error } = await supabase
        .from(PROFESSIONAL_MOT_CLES_TABLE_NAME)
        .update(motCleData)
        .eq("id", motCleId)
        .select()
        .single();

      if (error) {
        console.error("Erreur lors de la mise à jour du mot-clé:", error);
        throw error;
      }

      return data as MotClesProfessionnel;
    } catch (error) {
      console.error("Erreur dans UpdateProfessionalMotClesRepository:", error);
      return null;
    }
  }
}
