import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_MOT_CLES_TABLE_NAME } from "./constants";
import { MotClesProfessionnel } from "@/domain/models";
import { IGetProfessionalMotClesByIdRepository } from "@/domain/interfaces/repositories/motcles";

class GetProfessionalByProfessionalIdRepository
  implements IGetProfessionalMotClesByIdRepository
{
  async execute(professionalId: number) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_MOT_CLES_TABLE_NAME)
      .select("*")
      .eq("id", professionalId);

    if (error) throw error;

    return data as MotClesProfessionnel[];
  }
}

export default GetProfessionalByProfessionalIdRepository;
