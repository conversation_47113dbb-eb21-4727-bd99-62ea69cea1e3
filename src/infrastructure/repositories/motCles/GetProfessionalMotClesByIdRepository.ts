import { supabase } from "@/infrastructure/supabase/supabase";
import { MOT_CLES_TABLE_NAME } from "./constants";
import { IGetProfessionalMotClesByIdRepository } from "@/domain/interfaces/repositories/motcles";

class GetProfessionalMotClesByIdRepository
  implements IGetProfessionalMotClesByIdRepository
{
  public async execute(id: number) {
    const { data, error } = await supabase
      .from(MOT_CLES_TABLE_NAME)
      .select("*")
      .eq("id", id);

    if (error) throw error;

    return data;
  }
}

export default GetProfessionalMotClesByIdRepository;
