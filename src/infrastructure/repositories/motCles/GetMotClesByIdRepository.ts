import { mot_cles } from "@/domain/models/MotCles";
import { supabase } from "@/infrastructure/supabase/supabase";
import { MOT_CLES_TABLE_NAME } from "./constants";
import { IGetMotClesByIdRepository } from "@/domain/interfaces/repositories/motcles";

class GetMotClesByIdRepository implements IGetMotClesByIdRepository {
  public async execute(id: number): Promise<mot_cles> {
    const { data, error } = await supabase
      .from(MOT_CLES_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;

    return data as mot_cles;
  }
}

export default GetMotClesByIdRepository;
