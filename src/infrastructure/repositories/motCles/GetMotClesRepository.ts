import { mot_cles } from "@/domain/models/MotCles";
import { supabase } from "@/infrastructure/supabase/supabase";
import { MOT_CLES_TABLE_NAME } from "./constants";
import { IGetMotClesRepository } from "@/domain/interfaces/repositories/motcles";

class GetMotCles implements IGetMotClesRepository {
  async execute() {
    const { data, error } = await supabase
      .from(MOT_CLES_TABLE_NAME)
      .select("*");

    if (error) throw error;

    return data as mot_cles[];
  }
}

export default GetMotCles;
