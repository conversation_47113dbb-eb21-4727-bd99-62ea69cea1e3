import { supabase } from '@/infrastructure/supabase/supabase'
import { DIAGNOSTIC_TABLE_NAME } from "./Constant";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { laboratoire_diagnostics } from "@/domain/models";
import { IGetAllDiagnosticRepository } from '@/domain/interfaces/repositories/diagnostic';

export class GetAllDiagnosticRepository implements IGetAllDiagnosticRepository {
  async execute(carnetId: number): Promise<laboratoire_diagnostics[]> {
    const { data, error } = await supabase
      .from(DIAGNOSTIC_TABLE_NAME)
      .select("*")
      .eq("id_carnet", carnetId);

    handleError(error)

    return data as laboratoire_diagnostics[]
  }
}