import { IGetDiagnostiByPatientRepository } from "@/domain/interfaces/repositories/diagnostic/IGetDiagnostiByPatientRepository";
import { DiagnosticDTO } from "@/domain/DTOS/DiagnosticDTO";
import { supabase } from "@/infrastructure/supabase/supabase";
import { DIAGNOSTIC_TABLE_NAME } from "./Constant";
import { array } from "zod";

class GetDiagnosticByPatientRepository implements IGetDiagnostiByPatientRepository {
    async execute(patientId: number): Promise<DiagnosticDTO[]> {
        const { data, error } = await supabase
            .from(DIAGNOSTIC_TABLE_NAME)
            .select(`
           id,
           id_carnet,
           titre,
           type_fichier,
           date,
           path,
           resultat,
           remarque,
           carnet_sante!inner(id_proprietaire)
       `)
            .eq('carnet_sante.id_proprietaire', patientId)
            .order('date', { ascending: false });

        if (error) throw error;
        return (data || []).map(item => ({
            id: item.id,
            id_carnet: item.id_carnet,
            titre: item.titre,
            type_fichier: item.type_fichier,
            date: item.date,
            path: item.path,
            resultat: item.resultat,
            remarque: item.remarque
        }));
    }
}

export default GetDiagnosticByPatientRepository;
