import { laboratoire_diagnostics } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { DIAGNOSTIC_TABLE_NAME } from './Constant'
import { IUpdateDiagnosticRepository } from '@/domain/interfaces/repositories/diagnostic'

export class UpdateDiagnosticRepository implements IUpdateDiagnosticRepository {
  async execute(id: number, diagnosticData: Partial<laboratoire_diagnostics>): Promise<laboratoire_diagnostics> {
    const { data, error } = await supabase
      .from(DIAGNOSTIC_TABLE_NAME)
      .update(diagnosticData)
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as laboratoire_diagnostics
  }
}
