import { supabase } from '@/infrastructure/supabase/supabase'
import { DIAGNOSTIC_TABLE_NAME } from "./Constant";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { laboratoire_diagnostics } from "@/domain/models";
import { IGetDiagnosticByIdRepository } from '@/domain/interfaces/repositories/diagnostic';

export class GetDiagnosticByIdRepository implements IGetDiagnosticByIdRepository {
  async execute(id: number): Promise<laboratoire_diagnostics> {
    const { data, error } = await supabase
      .from(DIAGNOSTIC_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    handleError(error)

    return data as laboratoire_diagnostics
  }
}