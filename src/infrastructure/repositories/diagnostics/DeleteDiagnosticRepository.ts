import { laboratoire_diagnostics } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { DIAGNOSTIC_TABLE_NAME } from './Constant'
import { IDeleteDiagnosticRepository } from '@/domain/interfaces/repositories/diagnostic'

export class DeleteDiagnosticRepository implements IDeleteDiagnosticRepository {
  async execute(id: number): Promise<laboratoire_diagnostics> {
    const { data, error } = await supabase
      .from(DIAGNOSTIC_TABLE_NAME)
      .delete()
      .eq('id', id)

    handleError(error)

    return data as laboratoire_diagnostics
  }
}
