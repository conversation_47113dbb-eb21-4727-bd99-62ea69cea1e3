import { laboratoire_diagnostics } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { DIAGNOSTIC_TABLE_NAME } from './Constant'
import { ICreateDiagnosticRepository } from '@/domain/interfaces/repositories/diagnostic/ICreateDiagnosticRepository'

export class CreateDiagnosticRepository implements ICreateDiagnosticRepository {
  async execute(
    diagnosticData: Omit<laboratoire_diagnostics, 'id'>
  ): Promise<laboratoire_diagnostics> {
    const { data, error } = await supabase
      .from(DIAGNOSTIC_TABLE_NAME)
      .insert(diagnosticData)
      .select()
      .single()

    handleError(error)

    return data as laboratoire_diagnostics
  }
}
