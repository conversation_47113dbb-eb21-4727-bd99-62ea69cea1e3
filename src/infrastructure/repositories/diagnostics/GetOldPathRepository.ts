import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetOldPathRepository } from "@/domain/interfaces/repositories/diagnostic";
import { DIAGNOSTIC_TABLE_NAME } from "./Constant";

export class GetOldPathRepository implements IGetOldPathRepository {
  async execute(id: number): Promise<string> {
    const { data, error } = await supabase
      .from(DIAGNOSTIC_TABLE_NAME)
      .select("path")
      .eq("id", id)
      .single();
    
    if (error) throw error
    return data.path as string
  }
}