import { SortiesStocks } from "@/domain/models/SortiesStocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { SORTIES_STOCKS_TABLE_NAME } from "./constants";
import { IGetSortiesStocksRepository } from "@/domain/interfaces/repositories/sortiesStocks/IGetSortiesStocksRepository";

class GetSortiesStocksRepository implements IGetSortiesStocksRepository {
  constructor() {}

  async execute(professionalId: number) {
    const { data, error } = await supabase
      .from(SORTIES_STOCKS_TABLE_NAME)
      .select(
        `
      id,
      lot_id,
      quantite,
      type_sortie,
      destinataire,
      date_sortie,
      lots(
        id,
        entrees_stock(
          stocks(
            *
          )
        )
      )
  `
      )
      .eq("lots.entrees_stock.stocks.utilisateur_id", professionalId)
      .order("date_sortie", { ascending: false });

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data;
  }
}

export default GetSortiesStocksRepository;
