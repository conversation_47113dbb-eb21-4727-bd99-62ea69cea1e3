import { SortiesStocks } from "@/domain/models/SortiesStocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { SORTIES_STOCKS_TABLE_NAME } from "./constants";
import { ICreateSortiesStocksRepository } from "@/domain/interfaces/repositories/sortiesStocks/ICreateSortiesStocksRepository";

class CreateSortiesStocksRepository implements ICreateSortiesStocksRepository {
  constructor() {}

  async execute(sortiesStocks: Omit<SortiesStocks, "id">[]): Promise<SortiesStocks[]> {
    const { data, error } = await supabase
      .from(SORTIES_STOCKS_TABLE_NAME)
      .insert(sortiesStocks)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as SortiesStocks[];
  }
}

export default CreateSortiesStocksRepository;
