import { supabase } from "@/infrastructure/supabase/supabase";
import { SORTIES_STOCKS_TABLE_NAME } from "./constants";
import { IDeleteSortiesStocksRepository } from "@/domain/interfaces/repositories/sortiesStocks/IDeleteSortiesStocksRepository";

class DeleteSortiesStocksRepository implements IDeleteSortiesStocksRepository {
  constructor() {}

  async execute(id: number): Promise<boolean> {
    const { error } = await supabase
      .from(SORTIES_STOCKS_TABLE_NAME)
      .delete()
      .eq("id", id);

    if (error) throw error;

    return true;
  }
}

export default DeleteSortiesStocksRepository;
