import { SortiesStocks } from "@/domain/models/SortiesStocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { SORTIES_STOCKS_TABLE_NAME } from "./constants";
import { IUpdateSortiesStocksRepository } from "@/domain/interfaces/repositories/sortiesStocks/IUpdateSortiesStocksRepository";

class UpdateSortiesStocksRepository implements IUpdateSortiesStocksRepository {
  constructor() {}

  async execute(id: number, sortiesStocks: Partial<Omit<SortiesStocks, "id">>): Promise<SortiesStocks | null> {
    const { data, error } = await supabase
      .from(SORTIES_STOCKS_TABLE_NAME)
      .update(sortiesStocks)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // Aucun enregistrement trouvé
      throw error;
    }

    return data as SortiesStocks;
  }
}

export default UpdateSortiesStocksRepository;
