import { SortiesStocks } from "@/domain/models/SortiesStocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { SORTIES_STOCKS_TABLE_NAME } from "./constants";
import { IGetSortiesStocksByIdRepository } from "@/domain/interfaces/repositories/sortiesStocks/IGetSortiesStocksByIdRepository";

class GetSortiesStocksByIdRepository implements IGetSortiesStocksByIdRepository {
  constructor() {}

  async execute(id: number): Promise<SortiesStocks | null> {
    const { data, error } = await supabase
      .from(SORTIES_STOCKS_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // Aucun enregistrement trouvé
      throw error;
    }

    return data as SortiesStocks;
  }
}

export default GetSortiesStocksByIdRepository;
