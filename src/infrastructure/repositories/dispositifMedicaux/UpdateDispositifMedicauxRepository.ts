import { DispositifMedicaux } from '@/domain/models'
import { IUpdateDispositifMedicauxRepository } from '@/domain/interfaces/repositories/dispositifMedicaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { DISPOSITIF_MEDICAUX_TABLE_NAME } from './Constant'

export class UpdateDispositifMedicauxRepository implements IUpdateDispositifMedicauxRepository {
  async update(id: number, data: Partial<DispositifMedicaux>): Promise<DispositifMedicaux> {
    const { data: updatedData, error } = await supabase
      .from(DISPOSITIF_MEDICAUX_TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      handleError(error)
    }

    return updatedData
  }
}
