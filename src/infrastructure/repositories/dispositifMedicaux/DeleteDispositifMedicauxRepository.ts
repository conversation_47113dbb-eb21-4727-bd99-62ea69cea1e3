import { IDeleteDispositifMedicauxRepository } from '@/domain/interfaces/repositories/dispositifMedicaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { DISPOSITIF_MEDICAUX_TABLE_NAME } from './Constant'

export class DeleteDispositifMedicauxRepository implements IDeleteDispositifMedicauxRepository {
  async delete(id: number): Promise<void> {
    const { error } = await supabase
      .from(DISPOSITIF_MEDICAUX_TABLE_NAME)
      .delete()
      .eq('id', id)

    if (error) {
      handleError(error)
    }
  }
}
