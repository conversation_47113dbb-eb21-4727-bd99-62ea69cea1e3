import { DispositifMedicaux } from '@/domain/models'
import { IGetDispositifMedicauxRepository } from '@/domain/interfaces/repositories/dispositifMedicaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { DISPOSITIF_MEDICAUX_TABLE_NAME } from './Constant'

export class GetDispositifMedicauxRepository implements IGetDispositifMedicauxRepository {
  async getById(id: number): Promise<DispositifMedicaux | null> {
    const { data, error } = await supabase
      .from(DISPOSITIF_MEDICAUX_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      handleError(error)
    }

    return data
  }

  async getAll(carnetId: number): Promise<DispositifMedicaux[]> {
    const { data, error } = await supabase
      .from(DISPOSITIF_MEDICAUX_TABLE_NAME)
      .select('*')
      .eq('id_carnet', carnetId)

    if (error) {
      handleError(error)
    }

    return data || []
  }
}
