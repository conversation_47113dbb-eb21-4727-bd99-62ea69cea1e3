import { DispositifMedicaux } from '@/domain/models'
import { ICreateDispositifMedicauxRepository } from '@/domain/interfaces/repositories/dispositifMedicaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { DISPOSITIF_MEDICAUX_TABLE_NAME } from './Constant'

export class CreateDispositifMedicauxRepository implements ICreateDispositifMedicauxRepository {
  async create(data: Omit<DispositifMedicaux, "id">[]): Promise<DispositifMedicaux[]> {
    const { data: createdData, error } = await supabase
      .from(DISPOSITIF_MEDICAUX_TABLE_NAME)
      .insert(data)
      .select()

    if (error) {
      handleError(error)
    }

    return createdData as DispositifMedicaux[]
  }
}
