import { signe_vitaux } from "@/domain/models/SigneVitaux";
import { SIGNE_VITAUX_TABLE_NAME } from "./contant";
import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetLastSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";

export class GetLastSigneVitauxRepository
  implements IGetLastSigneVitauxRepository
{
  async execute(id: number): Promise<signe_vitaux | null> {
    const { data, error } = await supabase
      .from(SIGNE_VITAUX_TABLE_NAME)
      .select("*")
      .eq("id_carnet", id)
      .order("id", { ascending: false })
      .limit(1);

    if (error) throw error;

    return data[0] as signe_vitaux;
  }
}
