import { signe_vitaux } from "@/domain/models/SigneVitaux";
import { SIGNE_VITAUX_TABLE_NAME } from "./contant";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { IGetByIdSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";

export class GetByIdSigneVitauxRepository implements IGetByIdSigneVitauxRepository {
  async execute(id: number): Promise<signe_vitaux | null> {
    const { data, error } = await supabase
      .from(SIGNE_VITAUX_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single();

    if (error) handleError(error);
    return data as signe_vitaux;
  }
}
