import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetVitalSignsByPatientRepository } from "@/domain/interfaces/repositories/signeVitaux/IGetVitalSignsByPatientRepository";
import { VitalSignsDetails } from "@/domain/DTOS/VitalSignsDetailsDTO";
import { SIGNE_VITAUX_TABLE_NAME } from "./contant";

class GetVitalSignsByPatientRepository implements IGetVitalSignsByPatientRepository {
    async execute(patientId: number): Promise<VitalSignsDetails[]> {
        const { data, error } = await supabase
            .from(SIGNE_VITAUX_TABLE_NAME)
            .select(`
                id,
                id_carnet,
                date_visite,
                taille,
                poid,
                indice_masse_corporel,
                temperature,
                circonference_tete,
                frequence_cardiaque,
                frequence_respiratoire,
                sa02,
                niveau_glucose,
                tension_arterielle,
                carnet_sante!inner(id_proprietaire)
            `)
            .eq('carnet_sante.id_proprietaire', patientId)
            .order('date_visite', { ascending: false });

        if (error) throw error;

        // Transform data to match expected format
        return (data || []).map(item => ({
            id: item.id,
            idCarnet: item.id_carnet,
            dateConsultation: item.date_visite,
            taille: item.taille ?? null,
            poids: item.poid ?? null,
            indiceMasseCorporel: item.indice_masse_corporel ?? null,
            temperature: item.temperature ?? null,
            circonferenceTete: item.circonference_tete ?? null,
            frequenceCardiaque: item.frequence_cardiaque ?? null,
            frequenceRespiratoire: item.frequence_respiratoire ?? null,
            sa02: item.sa02 ?? null,
            niveauGlucose: item.niveau_glucose ?? null,
            tensionArterielle: item.tension_arterielle ?? null
        }));
    }
}

export default GetVitalSignsByPatientRepository;