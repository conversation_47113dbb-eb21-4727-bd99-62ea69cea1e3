import { signe_vitaux } from "@/domain/models/SigneVitaux";
import { SIGNE_VITAUX_TABLE_NAME } from "./contant";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { IUpdateSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";

export class UpdateSigneVitauxRepository implements IUpdateSigneVitauxRepository {
  async execute(id: number, data: Partial<signe_vitaux>): Promise<signe_vitaux> {
    const { data: updatedData, error } = await supabase
      .from(SIGNE_VITAUX_TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single();

    if (error) handleError(error);
    return updatedData as signe_vitaux;
  }
}
