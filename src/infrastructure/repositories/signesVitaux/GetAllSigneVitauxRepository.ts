import { signe_vitaux } from "@/domain/models/SigneVitaux";
import { SIGNE_VITAUX_TABLE_NAME } from "./contant";
import { IGetAllSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";

export class GetAllSigneVitauxRepository implements IGetAllSigneVitauxRepository {
  async execute(carnetId: number): Promise<signe_vitaux[]> {
    const { data, error } = await supabase
      .from(SIGNE_VITAUX_TABLE_NAME)
      .select('*')
      .eq('id_carnet', carnetId)

    if (error) handleError(error);
    return data as signe_vitaux[];
  }
}
