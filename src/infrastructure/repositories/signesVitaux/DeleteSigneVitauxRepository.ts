import { SIGNE_VITAUX_TABLE_NAME } from "./contant";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { IDeleteSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";

export class DeleteSigneVitauxRepository implements IDeleteSigneVitauxRepository {
  async execute(id: number): Promise<void> {
    const { error } = await supabase
      .from(SIGNE_VITAUX_TABLE_NAME)
      .delete()
      .eq('id', id);

    if (error) handleError(error);
  }
}
