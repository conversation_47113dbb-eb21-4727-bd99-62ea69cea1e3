import { signe_vitaux } from "@/domain/models/SigneVitaux";
import { SIGNE_VITAUX_TABLE_NAME } from "./contant";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { ICreateSigneVitauxRepository } from "@/domain/interfaces/repositories/signeVitaux";

export class CreateSigneVitauxRepository implements ICreateSigneVitauxRepository {
  async execute(data: Omit<signe_vitaux, 'id'>): Promise<signe_vitaux> {
    const { data: createdData, error } = await supabase
      .from(SIGNE_VITAUX_TABLE_NAME)
      .insert(data)
      .select()
      .single();

    if (error) handleError(error);
    return createdData as signe_vitaux;
  }
}
