import { AntecedantFamilliaux } from '@/domain/models'
import { ICreateAntecedantFamiliauxRepository } from '@/domain/interfaces/repositories/antecedantFamiliaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDANT_FAMILIAUX_TABLE_NAME } from './Constant'

export class CreateAntecedantFamiliauxRepository implements ICreateAntecedantFamiliauxRepository {
  async create(data: Omit<AntecedantFamilliaux, "id">[]): Promise<AntecedantFamilliaux[]> {
    const { data: createdData, error } = await supabase
      .from(ANTECEDANT_FAMILIAUX_TABLE_NAME)
      .insert(data)
      .select()

    if (error) {
      handleError(error)
    }

    return createdData as AntecedantFamilliaux[]
  }
}
