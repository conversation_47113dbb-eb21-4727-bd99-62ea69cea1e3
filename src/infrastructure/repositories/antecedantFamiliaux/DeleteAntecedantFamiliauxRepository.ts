import { IDeleteAntecedantFamiliauxRepository } from '@/domain/interfaces/repositories/antecedantFamiliaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDANT_FAMILIAUX_TABLE_NAME } from './Constant'

export class DeleteAntecedantFamiliauxRepository implements IDeleteAntecedantFamiliauxRepository {
  async delete(id: number): Promise<void> {
    const { error } = await supabase
      .from(ANTECEDANT_FAMILIAUX_TABLE_NAME)
      .delete()
      .eq('id', id)

    if (error) {
      handleError(error)
    }
  }
}
