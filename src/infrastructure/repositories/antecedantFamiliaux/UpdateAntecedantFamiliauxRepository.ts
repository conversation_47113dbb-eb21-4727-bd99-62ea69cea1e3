import { AntecedantFamilliaux } from '@/domain/models'
import { IUpdateAntecedantFamiliauxRepository } from '@/domain/interfaces/repositories/antecedantFamiliaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDANT_FAMILIAUX_TABLE_NAME } from './Constant'

export class UpdateAntecedantFamiliauxRepository implements IUpdateAntecedantFamiliauxRepository {
  async update(id: number, data: Partial<AntecedantFamilliaux>): Promise<AntecedantFamilliaux> {
    const { data: updatedData, error } = await supabase
      .from(ANTECEDANT_FAMILIAUX_TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      handleError(error)
    }

    return updatedData
  }
}
