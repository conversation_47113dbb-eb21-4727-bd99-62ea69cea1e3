import { AntecedantFamilliaux } from '@/domain/models'
import { IGetAntecedantFamiliauxRepository } from '@/domain/interfaces/repositories/antecedantFamiliaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDANT_FAMILIAUX_TABLE_NAME } from './Constant'

export class GetAntecedantFamiliauxRepository implements IGetAntecedantFamiliauxRepository {
  async getById(id: number): Promise<AntecedantFamilliaux | null> {
    const { data, error } = await supabase
      .from(ANTECEDANT_FAMILIAUX_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      handleError(error)
    }

    return data
  }

  async getAll(carnetId: number): Promise<AntecedantFamilliaux[]> {
    const { data, error } = await supabase
      .from(ANTECEDANT_FAMILIAUX_TABLE_NAME)
      .select('*')
      .eq('id_carnet', carnetId)

    if (error) {
      handleError(error)
    }

    return data || []
  }
}
