import { Evenement } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IDeleteEventRepository } from '@/domain/interfaces/repositories/evenement/IDeleteEventRepository'
import { EVENEMENT_TABLE_NAME } from './Constants'

export class DeleteEventRepository implements IDeleteEventRepository {
  async execute(id: number): Promise<Evenement> {
    const { data, error } = await supabase
      .from(EVENEMENT_TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as Evenement
  }
}
