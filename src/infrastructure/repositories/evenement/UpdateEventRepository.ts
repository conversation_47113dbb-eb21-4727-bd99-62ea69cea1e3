import { Evenement } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IUpdateEventRepository } from '@/domain/interfaces/repositories/evenement/IUpdateEventRepository'
import { EVENEMENT_TABLE_NAME } from './Constants'

export class UpdateEventRepository implements IUpdateEventRepository {
  async execute(id: number, data: Partial<Evenement>): Promise<Evenement> {
    const { data: updatedData, error } = await supabase
      .from(EVENEMENT_TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return updatedData as Evenement
  }
}
