import { Evenement } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetEventsByProfessionalIdRepository } from "@/domain/interfaces/repositories/evenement/IGetEventsByProfessionalIdRepository";
import { EVENEMENT_TABLE_NAME } from "./Constants";

export class GetEventsByProfessionalIdRepository
  implements IGetEventsByProfessionalIdRepository
{
  async execute(professionalId: number): Promise<Evenement[]> {
    const { data, error } = await supabase
      .from(EVENEMENT_TABLE_NAME)
      .select("*")
      .eq("id_professionnel", professionalId);

    if (error) throw error;

    return data as Evenement[];
  }
}
