import { Evenement } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetDistinctEventsByProfessionalIdRepository } from "@/domain/interfaces/repositories/evenement";
import { DISTINCT_EVENEMENT_TABLE_NAME } from "./Constants";

export class GetDistinctEventsByProfessionalIdRepository
  implements IGetDistinctEventsByProfessionalIdRepository
{
  async execute(professionalId: number): Promise<Evenement[]> {
    const { data, error } = await supabase
      .from(DISTINCT_EVENEMENT_TABLE_NAME)
      .select("*")
      .eq("id_professionnel", professionalId);

    if (error) throw error;

    return data as Evenement[];
  }
}
