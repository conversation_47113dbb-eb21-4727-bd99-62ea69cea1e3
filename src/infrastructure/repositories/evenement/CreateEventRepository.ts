import { Evenement } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ICreateEventRepository } from '@/domain/interfaces/repositories/evenement/ICreateEventRepository'
import { EVENEMENT_TABLE_NAME } from './Constants'

export class CreateEventRepository implements ICreateEventRepository {
  async execute(eventData: Omit<Evenement[], 'id'>): Promise<Evenement[]> {
    const { data, error } = await supabase
      .from(EVENEMENT_TABLE_NAME)
      .insert(eventData)
      .select()

    handleError(error)

    return data as Evenement[]
  }
}
