import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetEventsByUserIdRepository } from "@/domain/interfaces/repositories/evenement/IGetEventsByUserIdRepository.ts";
import { Evenement } from "@/domain/models/Evenement.ts";
import { EVENEMENT_TABLE_NAME } from "./Constants.ts";

class GetEventsByUserIdRepository implements IGetEventsByUserIdRepository {
  async execute(userId: number): Promise<Evenement[]> {
    const { data, error } = await supabase
      .from(EVENEMENT_TABLE_NAME)
      .select("*")
      .eq("id_professionnel", userId);

    if (error) throw error;

    return data as Evenement[];
  }
}

export default GetEventsByUserIdRepository;
