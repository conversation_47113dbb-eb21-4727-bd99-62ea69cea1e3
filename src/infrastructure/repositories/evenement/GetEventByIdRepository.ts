import { Evenement } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { IGetEventByIdRepository } from '@/domain/interfaces/repositories/evenement/IGetEventByIdRepository'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { EVENEMENT_TABLE_NAME } from './Constants'

export class GetEventByIdRepository implements IGetEventByIdRepository {
  async execute(id: number): Promise<Evenement> {
    const { data, error } = await supabase.from(EVENEMENT_TABLE_NAME).select('*').eq('id', id).single()

    handleError(error)

    return data as Evenement
  }
}
