import { Evenement } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IDeleteEventByProfessionalIdRepository } from '@/domain/interfaces/repositories/evenement/IDeleteEventByProfessionalIdRepository'
import { EVENEMENT_TABLE_NAME } from './Constants'

export class DeleteEventByProfessionalIdRepository implements IDeleteEventByProfessionalIdRepository {
  async execute(id: number): Promise<Evenement[]> {
    const { data, error } = await supabase
      .from(EVENEMENT_TABLE_NAME)
      .delete()
      .eq('id_professionnel', id)
      .select()

    handleError(error)

    return data as Evenement[]
  }
}
