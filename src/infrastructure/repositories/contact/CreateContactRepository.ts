import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ICreateContactRepository } from '@/domain/interfaces/repositories/contact'
import { CONTACT_TABLE_NAME } from './Constant'
import { Contact } from '@/domain/models'

export class CreateContactRepository implements ICreateContactRepository {
  async execute(newContact: Omit<Contact, 'id'>[]): Promise<Contact[]> {
    const { data, error } = await supabase
      .from(CONTACT_TABLE_NAME)
      .insert(newContact)
      .select()

    handleError(error)

    return data as Contact[]
  }
}
