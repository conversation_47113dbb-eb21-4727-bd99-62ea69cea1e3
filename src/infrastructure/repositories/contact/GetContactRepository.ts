import { Contact } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { IGetContactRepository } from "@/domain/interfaces/repositories/contact";
import { CONTACT_TABLE_NAME } from "./Constant";

export class GetContactRepository implements IGetContactRepository {
  async execute(id: number) {
    const { data, error } = await supabase
      .from(CONTACT_TABLE_NAME)
      .select("*")
      .eq("utilisateur_id", id);

    handleError(error);

    return data as Contact[];
  }
}
