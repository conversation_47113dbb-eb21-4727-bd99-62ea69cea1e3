import { Contact, parametre_disponibilite } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { IUpdateContactRepository } from "@/domain/interfaces/repositories/contact";
import { CONTACT_TABLE_NAME } from "./Constant";

export class UpdateContactRepository implements IUpdateContactRepository {
  async execute(id: number, settings: Partial<Contact>) {
    const { data, error } = await supabase
      .from(CONTACT_TABLE_NAME)
      .update(settings)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as Contact;
  }
}
