import { Contact } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IDeleteContactRepository } from '@/domain/interfaces/repositories/contact'
import { CONTACT_TABLE_NAME } from './Constant'

export class DeleteContactRepository implements IDeleteContactRepository {
  async execute(id: number): Promise<Contact> {
    const { data, error } = await supabase
      .from(CONTACT_TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as Contact
  }
}
