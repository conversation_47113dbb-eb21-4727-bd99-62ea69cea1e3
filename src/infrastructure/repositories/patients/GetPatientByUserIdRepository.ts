import { Patient } from "@/domain/models"
import { supabase } from "@/infrastructure/supabase/supabase"
import { PATIENT_TABLE_NAME } from "./constants"
import { IGetPatientByUserIdRepository } from "@/domain/interfaces/repositories/patients"

class GetPatientByUserIdRepository implements IGetPatientByUserIdRepository {
  async execute(id: number): Promise<Patient> {
    const { data, error } = await supabase.from(PATIENT_TABLE_NAME).select('*').eq('utilisateur_id', id).single()

    if (error) throw error

    return data as Patient
  }
}

export default GetPatientByUserIdRepository