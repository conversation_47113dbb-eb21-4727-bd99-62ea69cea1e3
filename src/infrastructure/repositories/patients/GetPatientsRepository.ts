import { Patient } from "@/domain/models"
import { supabase } from "@/infrastructure/supabase/supabase"
import { PATIENT_TABLE_NAME } from "./constants"
import { IGetPatientsRepository } from "@/domain/interfaces/repositories/patients"

class GetPatientsRepository implements IGetPatientsRepository {
  async execute(): Promise<Patient[]> {
    const { data, error } = await supabase.from(PATIENT_TABLE_NAME).select('*')

    if (error) throw error

    return data as Patient[]
  }
}

export default GetPatientsRepository