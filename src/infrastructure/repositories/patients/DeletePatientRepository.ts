import { IDeletePatientRepository } from "@/domain/interfaces/repositories/patients"
import { Patient } from "@/domain/models"
import { supabase } from "@/infrastructure/supabase/supabase"

class DeletePatientRepository implements IDeletePatientRepository {
  async execute(id: number): Promise<Patient> {
    if (!id) throw new Error("L'id est requis pour la suppression")
    const { data, error } = await supabase.from('patients').delete().eq('id', id).select().single()

    if (error) throw error

    return data as Patient
  }
}

export default DeletePatientRepository