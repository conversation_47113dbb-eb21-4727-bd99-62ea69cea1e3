import { IDecedePatientRepository } from "@/domain/interfaces/repositories/patients"
import { Patient } from "@/domain/models"
import { supabase } from "@/infrastructure/supabase/supabase"
import { handleError } from "@/infrastructure/supabase/supabaseFetchError"
import { PATIENT_TABLE_NAME } from "./constants"

class DecedePatientRepository implements IDecedePatientRepository {
  async execute(id: number): Promise<Patient> {
    const doneState = { decede: true };

    const { data, error } = await supabase
      .from(PATIENT_TABLE_NAME)
      .update(doneState)
      .eq("id", id)
      .select()
      .single();

    if (error) handleError(error)

    return data as Patient
  }
}

export default DecedePatientRepository