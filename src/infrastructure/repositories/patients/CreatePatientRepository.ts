import { Patient } from "@/domain/models";
import { PATIENT_TABLE_NAME } from "./constants";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ICreatePatientRepository } from "@/domain/interfaces/repositories/patients";

class CreatePatientRepository implements ICreatePatientRepository {
  async execute(patientData: Omit<Patient, "id">): Promise<Patient> {
    const { data, error } = await supabase
      .from(PATIENT_TABLE_NAME)
      .insert(patientData)
      .select();

    if (error) throw error

    return data[0] as Patient
  }
}

export default CreatePatientRepository