import { PATIENT_TABLE_NAME } from "./constants";
import { supabase } from "@/infrastructure/supabase/supabase";
import { Patient } from "@/domain/models";

/**
 * Repository pour récupérer les informations de base d'un patient
 * sans le carnet de santé (pour éviter les problèmes de relations cassées)
 */
export class GetPatientBasicInfoRepository {
  async execute(id: number): Promise<Patient | null> {
    const { data, error } = await supabase
      .from(PATIENT_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .maybeSingle();

    if (error) throw error;

    if (!data) return null;

    return data as Patient;
  }
}
