import { PATIENT_TABLE_NAME } from "./constants";
import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetPatientByIdRepository } from "@/domain/interfaces/repositories/patients";
import { PatientDTO } from "@/domain/DTOS";

export class GetPatientByIdRepository implements IGetPatientByIdRepository {
  async execute(id: number): Promise<PatientDTO | null> {
    const { data, error } = await supabase
      .from(PATIENT_TABLE_NAME)
      .select(
        `
        *,
        urgence(*),
        carnet_sante:carnet_sante!id_patient(
          *,
          signe_vitaux(*),
          allergie(*),
          medicament(*),
          affectation_medical(*),
          dispositif_medicaux(*),
          antecedant_chirurgicaux(*),
          antecedant_familliaux(*),
          antecedant_sociaux_alcoolique(*),
          antecedant_sociaux_fumeur(*),
          vaccination(*),
          condition_gynecologique(*),
          laboratoire_diagnostics(*)
        )
      `,
      )
      .eq("id", id)
      .maybeSingle();

    if (error) throw error;

    if (!data) return null;

    return data as PatientDTO;
  }
}
