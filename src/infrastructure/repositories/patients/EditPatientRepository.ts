import { IEditPatientRepository } from "@/domain/interfaces/repositories/patients"
import { Patient } from "@/domain/models"
import { supabase } from "@/infrastructure/supabase/supabase"

class EditPatientRepository implements IEditPatientRepository {
  async execute(id: number, patientData: Partial<Patient>): Promise<Patient> {
    if (!id) throw new Error("L'id est requis pour la modification")
    const { data, error } = await supabase
      .from('patients')
      .update(patientData)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    return data as Patient
  }
}

export default EditPatientRepository