import { Patient } from "@/domain/models/Patient";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PATIENT_TABLE_NAME } from "./constants";
import { IGetPatientByPatientIdRepository } from "@/domain/interfaces/repositories/patients/IGetPatientByPatientIdRepository";

class GetPatientByPatientIdRepository
  implements IGetPatientByPatientIdRepository
{
  constructor() {}

  async execute(id: number): Promise<Patient> {
    const { data, error } = await supabase
      .from(PATIENT_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;

    return data as Patient;
  }
}

export default GetPatientByPatientIdRepository;
