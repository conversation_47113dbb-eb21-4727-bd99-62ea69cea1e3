import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_AVAILABILITY_TABLE_NAME } from "./constants";
import { AvailabilitySettingsDTO } from "@/domain/DTOS";
import { IGetProfessionalAvailabilityByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalAvailability";

class GetProfessionalAvailabilityByProfessionalIdRepository
  implements IGetProfessionalAvailabilityByProfessionalIdRepository
{
  constructor() {}

  async execute(
    professionalId: number,
  ): Promise<AvailabilitySettingsDTO | null> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_AVAILABILITY_TABLE_NAME)
      .select(
        `
        id,
        horaire_hebdomadaire(
          *,
          creneau_horaire(*)
        ),
        horaire_date_specifique(
          *,
          creneau_horaire(*)
        )
    `,
      )
      .eq("id_professionnel", professionalId);

    if (error) throw error;

    if (!data || data.length == 0) return null;

    return data[0] as AvailabilitySettingsDTO;
  }
}

export default GetProfessionalAvailabilityByProfessionalIdRepository;
