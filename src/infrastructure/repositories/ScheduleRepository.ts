import { IScheduleRepository } from '@/domain/interfaces/repositories/IScheduleRepository'
import { parametre_disponibilite } from '@/domain/models'
import { supabase } from '../supabase/supabase'
import { handleError } from '../supabase/supabaseFetchError'

const TABLE_NAME = 'parametre_disponibilite'

export class ScheduleRepository implements IScheduleRepository {
  async getAllSchedules(): Promise<parametre_disponibilite[]> {
    const { data, error } = await supabase.from(TABLE_NAME).select('*')

    handleError(error)

    return data as parametre_disponibilite[]
  }

  async getSchedulesByProffessionalId(id: number): Promise<parametre_disponibilite[]> {
    const { data, error } = await supabase.from(TABLE_NAME).select('*').eq('id_professionnel', id)

    handleError(error)

    return data as parametre_disponibilite[]
  }

  async getSchedulesById(id: number): Promise<parametre_disponibilite> {
    const { data, error } = await supabase
      .from(TABLE_NAME)
      .select('*')
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as parametre_disponibilite
  }

  async createSchedule(
    newSchedule: Omit<parametre_disponibilite, 'id'>
  ): Promise<parametre_disponibilite> {
    const { data, error } = await supabase.from(TABLE_NAME).insert(newSchedule).select().single()
    handleError(error)

    return data as parametre_disponibilite
  }

  async updateSchedule(
    id: number,
    newData: Partial<parametre_disponibilite>
  ): Promise<parametre_disponibilite> {
    const { data, error } = await supabase.from(TABLE_NAME).update(newData).eq('id', id).select()

    handleError(error)

    return data[0] as parametre_disponibilite
  }

  async deleteSchedule(id: number): Promise<parametre_disponibilite> {
    const { data, error } = await supabase.from(TABLE_NAME).delete().eq('id', id).select().single()

    handleError(error)

    return data as parametre_disponibilite
  }
}

export const scheduleRepository = new ScheduleRepository()
