import { IProfessionalRepository } from "@/domain/interfaces/repositories/IProfessionalRepository";
import { Professional_data, Professionnel } from "@/domain/models";
import {
  ProfessionalFilters,
  ProfessionalSearchParams,
} from "@/domain/models/Professionnel";
import { supabase } from "../supabase/supabase";
import { handleError } from "../supabase/supabaseFetchError";

const PROFESSIONNAL_TABLE_NAME = "professionnels";

export class ProfessionalRepository implements IProfessionalRepository {
  async getProfessionals(): Promise<Professionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONNAL_TABLE_NAME)
      .select("*");

    handleError(error);

    return data as Professionnel[];
  }

  async getProfessionalById(id: number): Promise<Professionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONNAL_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    handleError(error);

    return data as Professionnel;
  }

  async getProfessionalByUserId(id: number): Promise<Professionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONNAL_TABLE_NAME)
      .select("*")
      .eq("utilisateur_id", id)
      .single();

    handleError(error);

    return data as Professionnel;
  }

  async getFilteredProfessionalsWithAllRelations(
    params?: ProfessionalSearchParams
  ): Promise<Professional_data[]> {
    try {
      const { page = 1, limit = 10, ...filters } = params;
      const offset = (page - 1) * limit;

      // Construire la requête avec les filtres
      const query = buildFilteredQuery(filters)
        .select(
          `
          *,
          specialites_professionnel!inner (
            nom_specialite
          ),
          parametre_disponibilite (*),
          rendez_vous (*),
          historiques_medicaux (*),
          consultation_medical (*),
          etablissements_professionnel (*),
          ordre_appartenance (*),
          mot_cles_professionnel (*),
          langues_parlees_professionnel (*)
        `
        )
        .range(offset, offset + limit - 1);

      const { data, error } = await query;

      if (error) throw error;

      return data as Professional_data[];
    } catch (error) {
      handleError(error);
      return [];
    }
  }

  async getProfessionalWithAllRelationsById(
    id: number
  ): Promise<Professional_data> {
    try {
      const { data, error } = await supabase
        .from(PROFESSIONNAL_TABLE_NAME)
        .select(
          `
          *,
          specialites_professionnel!inner (
            nom_specialite
          ),
          parametre_disponibilite!inner (*),
          rendez_vous!inner (*),
          historiques_medicaux!inner (*),
          consultation_medical!inner (*),
          etablissements_professionnel!inner (*),
          ordre_appartenance!inner (*),
          mot_cles_professionnel!inner (*),
          langues_parlees_professionnel!inner (*)
        `
        )
        .eq("id", id)
        .single();

      if (error) throw error;

      return data as Professional_data;
    } catch (error) {
      handleError(error);
    }
  }

  async searchProfessionals(searchTerm: string): Promise<Professionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONNAL_TABLE_NAME)
      .select(
        `
        *,
        specialites (*)
        `
      )
      .or(`nom.ilike.%${searchTerm}%,prenom.ilike.%${searchTerm}%`);

    handleError(error);

    return data;
  }

  async filterBySpeciality(specialityId: number): Promise<Professionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONNAL_TABLE_NAME)
      .select(
        `
      *,
      specialites (*)
      `
      )
      .eq("specialites.id", specialityId);

    handleError(error);

    return data;
  }

  async createProfessional(
    professionnalData: Omit<Professionnel, "id">
  ): Promise<Professionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONNAL_TABLE_NAME)
      .insert(professionnalData)
      .select();

    handleError(error);

    return data[0] as Professionnel;
  }

  async updateProfessional(
    id: number,
    professionnalData: Partial<Professionnel>
  ): Promise<Professionnel> {
    if (!id) throw new Error("L'id est requis pour la modification");
    const { data, error } = await supabase
      .from(PROFESSIONNAL_TABLE_NAME)
      .update(professionnalData)
      .eq("id", id)
      .select();

    handleError(error);

    return data[0] as Professionnel;
  }

  async deleteProfessional(id: number): Promise<Professionnel> {
    if (!id) throw new Error("L'id est requis pour la suppression");
    const { data, error } = await supabase
      .from(PROFESSIONNAL_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select();

    handleError(error);

    return data[0] as Professionnel;
  }

  async filterByLocation(region: string): Promise<Professionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONNAL_TABLE_NAME)
      .select(
        `
        *,
        localisations_professionnels (*)
        `
      )
      .eq("localisations_professionnels.region", region);

    handleError(error);

    return data as Professionnel[];
  }
}

/**
 * Construit la requête de base pour la recherche de professionnels avec filtres
 */
const buildFilteredQuery = (filters: ProfessionalFilters) => {
  let query = supabase.from(PROFESSIONNAL_TABLE_NAME).select(`
      *,
      specialites_professionnel!inner (
        nom_specialite
      ),
      etablissements_professionnel (*),
      langues_parlees_professionnel (*)
    `);

  if (filters.nouveauPatientAcceptes !== undefined) {
    query = query.eq(
      "nouveau_patient_acceptes",
      filters.nouveauPatientAcceptes
    );
  }

  if (filters.sexe) {
    query = query.eq("sexe", filters.sexe);
  }

  if (filters.speciality) {
    query = query.eq(
      "specialites_professionnel.nom_specialite",
      filters.speciality
    );
  }

  if (filters.localisation) {
    query = query.or(
      `region.eq.${filters.localisation},district.eq.${filters.localisation},commune.eq.${filters.localisation}`
    );
  }

  if (filters.langueParlee && filters.langueParlee.length > 0) {
    query = query.in(
      "langues_parlees_professionnel.langue",
      filters.langueParlee
    );
  }

  return query;
};

export const professionnalRepository = new ProfessionalRepository();
