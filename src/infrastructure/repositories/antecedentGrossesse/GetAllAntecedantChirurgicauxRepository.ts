import { Antecedent<PERSON><PERSON>esse } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDENT_GROSSESSE_TABLE_NAME } from './Constant'
import { IGetAllAntecedentGrossesseRepository } from '@/domain/interfaces/repositories/antecedentGrossesse'

export class GetAllAntecedentGrossesseRepository implements IGetAllAntecedentGrossesseRepository {
  async execute(carnetId: number): Promise<AntecedentGrossesse[]> {
    const { data, error } = await supabase
      .from(ANTECEDENT_GROSSESSE_TABLE_NAME)
      .select('*')
      .eq('id_carnet', carnetId)

    if (error) handleError(error);
    return data as AntecedentGrossesse[]
  }
}
