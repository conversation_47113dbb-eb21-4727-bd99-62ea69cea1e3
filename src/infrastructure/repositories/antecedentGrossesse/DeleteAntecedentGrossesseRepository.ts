import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDENT_GROSSESSE_TABLE_NAME } from './Constant'
import { IDeleteAntecedentGrossesseRepository } from '@/domain/interfaces/repositories/antecedentGrossesse'
import { AntecedentGrossesse } from '@/domain/models'

export class DeleteAntecedentGrossesseRepository implements IDeleteAntecedentGrossesseRepository {
  async execute(id: number): Promise<AntecedentGrossesse> {
    const { data, error } = await supabase
      .from(ANTECEDENT_GROSSESSE_TABLE_NAME)
      .delete()
      .eq('id', id)

    if (error) handleError(error)
    return data as AntecedentGrossesse
  }
}
