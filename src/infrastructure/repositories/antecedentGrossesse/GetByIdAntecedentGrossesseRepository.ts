import { AntecedentGrossesse } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDENT_GROSSESSE_TABLE_NAME } from './Constant'
import { IGetByIdAntecedentGrossesseRepository } from '@/domain/interfaces/repositories/antecedentGrossesse'

export class GetByIdAntecedentGrossesseRepository implements IGetByIdAntecedentGrossesseRepository {
  async execute(id: number): Promise<AntecedentGrossesse | null> {
    const { data, error } = await supabase
      .from(ANTECEDENT_GROSSESSE_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single()

    if (error) handleError(error);
    return data as AntecedentGrossesse
  }
}
