import { Antecedent<PERSON><PERSON>esse } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDENT_GROSSESSE_TABLE_NAME } from './Constant'
import { IUpdateAntecedentGrossesseRepository } from '@/domain/interfaces/repositories/antecedentGrossesse'

export class UpdateAntecedentGrossesseRepository implements IUpdateAntecedentGrossesseRepository {
  async execute(id: number, data: Partial<AntecedentGrossesse>): Promise<AntecedentGrossesse> {
    const { data: updatedData, error } = await supabase
      .from(ANTECEDENT_GROSSESSE_TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    if (error) handleError(error)
    return updatedData as AntecedentGrossesse
  }
}
