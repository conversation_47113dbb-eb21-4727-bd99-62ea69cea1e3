import { Antecedent<PERSON>rossesse } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDENT_GROSSESSE_TABLE_NAME } from './Constant'
import { ICreateAntecedentGrossesseRepository } from '@/domain/interfaces/repositories/antecedentGrossesse'

export class CreateAntecedentGrossesseRepository implements ICreateAntecedentGrossesseRepository {
  async execute(data: Omit<AntecedentGrossesse, "id">[]): Promise<AntecedentGrossesse[]> {
    const { data: createdData, error } = await supabase
      .from(ANTECEDENT_GROSSESSE_TABLE_NAME)
      .insert(data)
      .select()

    if (error) handleError(error)
    return createdData as AntecedentGrossesse[]
  }
}
