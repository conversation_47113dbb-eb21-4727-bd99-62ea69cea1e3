import { Lots } from "@/domain/models/Lots.ts";
import { LOTS_TABLE_NAME } from "./constants.ts";
import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { IGetLotByIdRepository } from "@/domain/interfaces/repositories/lots/IGetLotByIdRepository.ts";

class GetLotByIdRepository implements IGetLotByIdRepository {
  constructor() {}

  async execute(lotId: number): Promise<Lots> {
    const { data, error } = await supabase
      .from(LOTS_TABLE_NAME)
      .select("*")
      .eq("id", lotId)
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // Aucun enregistrement trouvé
      throw error;
    }

    return data as Lots;
  }
}

export default GetLotByIdRepository;
