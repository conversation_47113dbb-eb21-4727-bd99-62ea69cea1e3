import { Lots } from "@/domain/models/Lots.ts";
import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { LOTS_TABLE_NAME } from "./constants.ts";
import { ICreateLotRepository } from "@/domain/interfaces/repositories/lots/ICreateLotRepository.ts";

class CreateLotRepository implements ICreateLotRepository {
  constructor() {}

  async execute(lots: Omit<Lots, "id">[]): Promise<Lots[]> {
    const { data, error } = await supabase
      .from(LOTS_TABLE_NAME)
      .insert(lots)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as Lots[];
  }
}

export default CreateLotRepository;
