import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { LOTS_TABLE_NAME } from "./constants.ts";
import { IDeleteLotRepository } from "@/domain/interfaces/repositories/lots/IDeleteLotRepository.ts";

class DeleteLotRepository implements IDeleteLotRepository {
  constructor() {}

  async execute(lotId: number): Promise<boolean> {
    const { error } = await supabase
      .from(LOTS_TABLE_NAME)
      .delete()
      .eq("id", lotId);

    if (error) throw error;

    return true;
  }
}

export default DeleteLotRepository;
