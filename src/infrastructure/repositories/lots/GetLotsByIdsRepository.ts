import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { LOTS_TABLE_NAME } from "./constants.ts";
import { Lots } from "@/domain/models/Lots.ts";
import { IGetLotsByIdsRepository } from "@/domain/interfaces/repositories/lots/IGetLotsByIdsRepository.ts";

class GetLotsByIdsRepository implements IGetLotsByIdsRepository {
  constructor() {}

  async execute(ids: number[]) {
    const { data, error } = await supabase
      .from(LOTS_TABLE_NAME)
      .select("*")
      .in("id", ids);

    if (error) throw error;

    return data as Lots[];
  }
}

export default GetLotsByIdsRepository;
