import { Lots } from "@/domain/models/Lots.ts";
import { LOTS_TABLE_NAME } from "./constants.ts";
import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { IUpdateLotRepository } from "@/domain/interfaces/repositories/lots/IUpdateLotRepository.ts";

class UpdateLotRepository implements IUpdateLotRepository {
  constructor() {}

  async execute(
    lotId: number,
    lotData: Partial<Omit<Lots, "id">>
  ): Promise<Lots | null> {
    const { data, error } = await supabase
      .from(LOTS_TABLE_NAME)
      .update(lotData)
      .eq("id", lotId)
      .select()
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // Aucun enregistrement trouvé
      throw error;
    }

    return data as Lots;
  }
}

export default UpdateLotRepository;
