import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { LOTS_TABLE_NAME } from "./constants.ts";
import { IGetLotsWithStockDataRepository } from "@/domain/interfaces/repositories/lots/IGetLotsWithStockDataRepository.ts";

class GetLotsWithStockDataRepository
  implements IGetLotsWithStockDataRepository
{
  constructor() {}

  async execute(userId: number): Promise<any[]> {
    const { data, error } = await supabase
      .from(LOTS_TABLE_NAME)
      .select(
        `
        id,
        entree_id,
        numero_lot,
        quantite,
        date_expiration,
        entrees_stock(
          stocks(
            id,
            utilisateur_id,
            nom,
            description,
            categorie_id,
            unite,
            seuil_alerte,
            cree_le
          )
        )
      `
      )
      .gt("quantite", 0) // Seulement les lots avec quantité disponible
      .eq("entrees_stock.stocks.utilisateur_id", userId);

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data;
  }
}

export default GetLotsWithStockDataRepository;
