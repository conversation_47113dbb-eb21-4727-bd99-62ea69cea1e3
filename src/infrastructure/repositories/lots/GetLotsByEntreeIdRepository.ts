import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { LOTS_TABLE_NAME } from "./constants.ts";
import { Lots } from "@/domain/models/Lots.ts";
import { IGetLotsByEntreeIdRepository } from "@/domain/interfaces/repositories/lots/IGetLotsByEntreeIdRepository.ts";

class GetLotsByEntreeIdRepository implements IGetLotsByEntreeIdRepository {
  constructor() {}

  async execute(entreeId: number): Promise<Lots[]> {
    const { data, error } = await supabase
      .from(LOTS_TABLE_NAME)
      .select("*")
      .eq("entree_id", entreeId)
      .gte("quantite", 0)
      .order("date_expiration", { ascending: true });

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data;
  }
}

export default GetLotsByEntreeIdRepository;
