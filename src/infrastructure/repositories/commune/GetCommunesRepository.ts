import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetCommunesRepository } from "@/domain/interfaces/repositories/commune/IGetCommunesRepository";
import { Commune } from "@/domain/models/Commune";
import { COMMUNE_TABLE_NAME } from "./constants";

export class GetCommunesRepository implements IGetCommunesRepository {
  async execute(): Promise<Commune[]> {
    const { data, error } = await supabase
      .from(COMMUNE_TABLE_NAME)
      .select('*');

    if (error) throw error;

    return data as Commune[];
  }
}
