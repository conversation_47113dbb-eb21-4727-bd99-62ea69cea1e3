import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetCommunesInDistrictRepository } from "@/domain/interfaces/repositories/commune/IGetCommunesInDistrictRepository";
import { Commune } from "@/domain/models/Commune";
import { COMMUNE_TABLE_NAME } from "./constants";

export class GetCommunesInDistrictRepository implements IGetCommunesInDistrictRepository {
  async execute(districtId: number): Promise<Commune[]> {
    const { data, error } = await supabase
      .from(COMMUNE_TABLE_NAME)
      .select('*')
      .eq('id_district', districtId);

    if (error) throw error;

    return data as Commune[];
  }
}
