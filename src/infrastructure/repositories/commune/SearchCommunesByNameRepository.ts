import { supabase } from "@/infrastructure/supabase/supabase";
import { COMMUNE_FILTER_LIMIT, COMMUNE_TABLE_NAME } from "./constants";
import { Commune } from "@/domain/models";
import { ISearchCommunesByNameRepository } from "@/domain/interfaces/repositories/commune";

class SearchCommunesByNameRepository
  implements ISearchCommunesByNameRepository {
  async execute(searchTerm: string): Promise<Commune[]> {
    const { data, error } = await supabase
      .from(COMMUNE_TABLE_NAME)
      .select("*")
      .or(`nom.ilike.%${searchTerm}%`)
      .limit(COMMUNE_FILTER_LIMIT);

    if (error) throw error;

    return data as Commune[];
  }
}

export default SearchCommunesByNameRepository;
