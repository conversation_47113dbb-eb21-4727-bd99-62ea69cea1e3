import { supabase } from "@/infrastructure/supabase/supabase";
import { IGetCommuneByIdRepository } from "@/domain/interfaces/repositories/commune/IGetCommuneByIdRepository";
import { Commune } from "@/domain/models/Commune";
import { COMMUNE_TABLE_NAME } from "./constants";

export class GetCommuneByIdRepository implements IGetCommuneByIdRepository {
  async execute(id: number): Promise<Commune> {
    const { data, error } = await supabase
      .from(COMMUNE_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;

    return data as Commune;
  }
}
