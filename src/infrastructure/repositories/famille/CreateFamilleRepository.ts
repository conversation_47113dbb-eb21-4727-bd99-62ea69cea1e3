import { Familles } from "@/domain/models/Famille";
import { supabase } from "@/infrastructure/supabase/supabase";
import { FAMILLE_TABLE_NAME } from "./constants";
import { ICreateFamilleRepository } from "@/domain/interfaces/repositories/famille/ICreateFamilleRepository";

export class CreateFamilleRepository implements ICreateFamilleRepository {
  async execute(familles: Omit<Familles, "id">[]): Promise<Familles[]> {
    const { data, error } = await supabase
      .from(FAMILLE_TABLE_NAME)
      .insert(familles)
      .select();
    if (error) throw error;
    return data || [];
  }
}
