import { supabase } from "@/infrastructure/supabase/supabase";
import { FAMILLE_TABLE_NAME } from "./constants";
import { IDeleteFamilleRepository } from "@/domain/interfaces/repositories/famille/IDeleteFamilleRepository";

export class DeleteFamilleRepository implements IDeleteFamilleRepository {
  async execute(id: number): Promise<void> {
    const { error } = await supabase
      .from(FAMILLE_TABLE_NAME)
      .delete()
      .eq("id", id);
    if (error) throw error;
  }
}
