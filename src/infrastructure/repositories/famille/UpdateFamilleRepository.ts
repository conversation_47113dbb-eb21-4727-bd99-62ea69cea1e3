import { Familles } from "@/domain/models/Famille";
import { supabase } from "@/infrastructure/supabase/supabase";
import { FAMILLE_TABLE_NAME } from "./constants";
import { IUpdateFamilleRepository } from "@/domain/interfaces/repositories/famille/IUpdateFamilleRepository";

export class UpdateFamilleRepository implements IUpdateFamilleRepository {
  async execute(famille: Familles): Promise<Familles> {
    const { data, error } = await supabase
      .from(FAMILLE_TABLE_NAME)
      .update(famille)
      .eq("id", famille.id)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
}
