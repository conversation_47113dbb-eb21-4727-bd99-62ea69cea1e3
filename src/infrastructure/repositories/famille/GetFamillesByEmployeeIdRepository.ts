import { Familles } from "@/domain/models/Famille";
import { supabase } from "@/infrastructure/supabase/supabase";
import { FAMILLE_TABLE_NAME } from "./constants";
import { IGetFamillesByEmployeeIdRepository } from "@/domain/interfaces/repositories/famille/IGetFamillesByEmployeeIdRepository";

export class GetFamillesByEmployeeIdRepository
  implements IGetFamillesByEmployeeIdRepository
{
  async execute(id_employee: number): Promise<Familles[]> {
    const { data, error } = await supabase
      .from(FAMILLE_TABLE_NAME)
      .select("*")
      .eq("id_employee", id_employee);
    if (error) throw error;
    return (data as Familles[]) || [];
  }
}
