import { IGetStocksDataRepository } from "@/domain/interfaces/repositories/stocks/IGetStocksDataRepository.ts";
import {
  PROFESSIONAL_STOCKS_CATEGORIES_TABLE_NAME,
  PROFESSIONAL_STOCKS_ENTREES_TABLE_NAME,
  PROFESSIONAL_STOCKS_FOURNISSEURS_TABLE_NAME,
  PROFESSIONAL_STOCKS_LOTS_TABLE_NAME,
  PROFESSIONAL_STOCKS_SORTIES_TABLE_NAME,
  PROFESSIONAL_STOCKS_TABLE_NAME,
} from "./constants";
import { supabase } from "@/infrastructure/supabase/supabase";

class GetStocksDataRepository implements IGetStocksDataRepository {
  constructor() {}

  async execute(userId: number) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_STOCKS_TABLE_NAME)
      .select(
        `
    *,
    categorie:${PROFESSIONAL_STOCKS_CATEGORIES_TABLE_NAME} (
      id,
      nom,
      description
    ),
    entrees:${PROFESSIONAL_STOCKS_ENTREES_TABLE_NAME} (
      id,
      quantite,
      prix_unitaire,
      date_entree,
      fournisseur:${PROFESSIONAL_STOCKS_FOURNISSEURS_TABLE_NAME} (
        id,
        nom,
        adresse,
        telephone,
        courriel
      ),
      lots:${PROFESSIONAL_STOCKS_LOTS_TABLE_NAME} (
        id,
        numero_lot,
        quantite,
        date_expiration,
        sorties:${PROFESSIONAL_STOCKS_SORTIES_TABLE_NAME} (
          id,
          quantite,
          type_sortie,
          destinataire,
          date_sortie
        )
      )
    )
  `
      )
      .eq("utilisateur_id", userId);

    if (error) throw error;

    return data;
  }
}

export default GetStocksDataRepository;
