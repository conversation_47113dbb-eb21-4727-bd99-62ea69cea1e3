import { Stocks } from "@/domain/models/Stocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_STOCKS_TABLE_NAME } from "./constants";
import { IUpdateStocksRepository } from "@/domain/interfaces/repositories/stocks/IUpdateStocksRepository";

class UpdateStocksRepository implements IUpdateStocksRepository {
  constructor() {}

  async execute(stockId: number, stockData: Partial<Omit<Stocks, "id">>): Promise<Stocks | null> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_STOCKS_TABLE_NAME)
      .update(stockData)
      .eq("id", stockId)
      .select()
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // Aucun enregistrement trouvé
      throw error;
    }

    return data as Stocks;
  }
}

export default UpdateStocksRepository;
