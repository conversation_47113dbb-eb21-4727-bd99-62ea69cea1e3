import { Stocks } from "@/domain/models/Stocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_STOCKS_TABLE_NAME } from "./constants";
import { IGetStocksRepository } from "@/domain/interfaces/repositories/stocks/IGetStocksRepository";

class GetStocksRepository implements IGetStocksRepository {
  constructor() {}

  async execute(userId: number): Promise<Stocks[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_STOCKS_TABLE_NAME)
      .select("*")
      .eq("utilisateur_id", userId)
      .order("cree_le", { ascending: false });

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as Stocks[];
  }
}

export default GetStocksRepository;
