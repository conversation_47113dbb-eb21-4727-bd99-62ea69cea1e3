import { Stocks } from "@/domain/models/Stocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_STOCKS_TABLE_NAME } from "./constants";
import { IGetStocksByProductNameRepository } from "@/domain/interfaces/repositories/stocks/IGetStocksByProductNameRepository";

class GetStocksByProductNameRepository
  implements IGetStocksByProductNameRepository
{
  constructor() {}

  async execute(
    professionalId: number,
    productName: string
  ): Promise<Stocks[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_STOCKS_TABLE_NAME)
      .select("*")
      .eq("utilisateur_id", professionalId)
      .eq("nom", productName)
      .gt("stock_actuel", 0) // Seulement les stocks avec quantité disponible
      .order("date_expiration", { ascending: true, nullsFirst: false }) // FIFO basé sur la date d'expiration
      .order("cree_le", { ascending: true }); // En cas d'égalité, prendre le plus ancien

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as Stocks[];
  }
}

export default GetStocksByProductNameRepository;
