import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_STOCKS_TABLE_NAME } from "./constants";
import { IDeleteStocksRepository } from "@/domain/interfaces/repositories/stocks/IDeleteStocksRepository";

class DeleteStocksRepository implements IDeleteStocksRepository {
  constructor() {}

  async execute(stockId: number): Promise<boolean> {
    const { error } = await supabase
      .from(PROFESSIONAL_STOCKS_TABLE_NAME)
      .delete()
      .eq("id", stockId);

    if (error) throw error;

    return true;
  }
}

export default DeleteStocksRepository;
