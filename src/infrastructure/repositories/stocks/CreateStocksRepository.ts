import { Stocks } from "@/domain/models/Stocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_STOCKS_TABLE_NAME } from "./constants";
import { ICreateStocksRepository } from "@/domain/interfaces/repositories/stocks/ICreateStocksRepository";

class CreateStocksRepository implements ICreateStocksRepository {
  constructor() {}

  async execute(stocks: Omit<Stocks, "id">[]): Promise<Stocks[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_STOCKS_TABLE_NAME)
      .insert(stocks)
      .select();

    if (error) {
      if (error.code === "23505") throw new Error("Ce médicament éxiste déjà dans le stock");
      throw error;
    }

    if (!data || data.length === 0) return [];

    return data as Stocks[];
  }
}

export default CreateStocksRepository;
