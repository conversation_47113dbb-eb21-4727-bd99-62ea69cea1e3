import { Stocks } from "@/domain/models/Stocks";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_STOCKS_TABLE_NAME } from "./constants";
import { IGetStockByIdRepository } from "@/domain/interfaces/repositories/stocks/IGetStockByIdRepository";

class GetStockByIdRepository implements IGetStockByIdRepository {
  constructor() {}

  async execute(stockId: number): Promise<Stocks | null> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_STOCKS_TABLE_NAME)
      .select("*")
      .eq("id", stockId)
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // Aucun enregistrement trouvé
      throw error;
    }

    return data as Stocks;
  }
}

export default GetStockByIdRepository;
