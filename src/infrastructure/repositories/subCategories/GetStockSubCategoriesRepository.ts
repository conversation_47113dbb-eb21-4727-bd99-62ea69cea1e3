import { supabase } from "@/infrastructure/supabase/supabase";
import { STOCK_SUB_CATEGORY_TABLE_NAME } from "./constants";
import { IGetStockSubCategoriesRepository } from "@/domain/interfaces/repositories/subCategories/IGetStockSubCategoriesRepository";
import { SousCategories } from "@/domain/models/SousCategories.ts";

class GetStockSubCategoriesRepository
  implements IGetStockSubCategoriesRepository
{
  constructor() {}

  async execute(): Promise<SousCategories[]> {
    const { data, error } = await supabase
      .from(STOCK_SUB_CATEGORY_TABLE_NAME)
      .select("*");

    if (error) throw error;

    return data as SousCategories[];
  }
}

export default GetStockSubCategoriesRepository;
