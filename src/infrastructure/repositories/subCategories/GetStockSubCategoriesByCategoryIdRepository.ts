import { SousCategories } from "@/domain/models/SousCategories";
import { supabase } from "@/infrastructure/supabase/supabase";
import { STOCK_SUB_CATEGORY_TABLE_NAME } from "./constants";
import { IGetStockSubCategoriesByCategoryIdRepository } from "@/domain/interfaces/repositories/subCategories/IGetStockSubCategoriesByCategoryIdRepository";

class GetStockSubCategoriesByCategoryIdRepository
  implements IGetStockSubCategoriesByCategoryIdRepository
{
  constructor() {}

  async execute(categoryId: number): Promise<SousCategories[]> {
    const { data, error } = await supabase
      .from(STOCK_SUB_CATEGORY_TABLE_NAME)
      .select("*")
      .eq("id_categorie", categoryId);

    if (error) throw error;

    return data;
  }
}

export default GetStockSubCategoriesByCategoryIdRepository;
