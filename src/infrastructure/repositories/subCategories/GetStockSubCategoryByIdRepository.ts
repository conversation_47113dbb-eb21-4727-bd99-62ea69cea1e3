import { supabase } from "@/infrastructure/supabase/supabase";
import { STOCK_SUB_CATEGORY_TABLE_NAME } from "./constants";
import { SousCategories } from "@/domain/models/SousCategories";
import { IGetStockSubCategoryByIdRepository } from "@/domain/interfaces/repositories/subCategories/IGetStockSubCategoryByIdRepository";

class GetStockSubCategoryByIdRepository
  implements IGetStockSubCategoryByIdRepository
{
  constructor() {}

  async execute(subCategoryId: number): Promise<SousCategories> {
    const { data, error } = await supabase
      .from(STOCK_SUB_CATEGORY_TABLE_NAME)
      .select("*")
      .eq("id", subCategoryId);

    if (error) throw error;

    return data[0] as SousCategories;
  }
}

export default GetStockSubCategoryByIdRepository;
