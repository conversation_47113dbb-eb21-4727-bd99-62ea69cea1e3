import { Proche } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { PROCHE_TABLE_NAME } from "./Constant";
import { IUpdateProcheRepository } from "@/domain/interfaces/repositories/prochePatient";

export class UpdateProcheRepository implements IUpdateProcheRepository {
  async execute(id: number, updateData: Partial<Proche>): Promise<Proche> {
    const { data, error } = await supabase
      .from(PROCHE_TABLE_NAME)
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as Proche;
  }
}
