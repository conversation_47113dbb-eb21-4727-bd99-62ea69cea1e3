import { Proche } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { PROCHE_TABLE_NAME } from './Constant'
import { ICreateProchePatientRepository } from '@/domain/interfaces/repositories/prochePatient'

export class CreateProchePatientRepository implements ICreateProchePatientRepository {
  async execute(
    procheData: Omit<Proche, 'id'>
  ): Promise<Proche> {
    const { data, error } = await supabase
      .from(PROCHE_TABLE_NAME)
      .insert(procheData)
      .select()

    handleError(error)

    return data[0] as Proche
  }
}
