import { supabase } from "@/infrastructure/supabase/supabase";
import { USER_TABLE_NAME } from "./Constant";
import { IGetProcheEmployerRepository } from "@/domain/interfaces/repositories/prochePatient";

export class GetProcheEmployerRepository
  implements IGetProcheEmployerRepository
{
  async execute(patientId: number[]) {
    const { data, error } = await supabase
      .from(USER_TABLE_NAME)
      .select(
        `
          employees:employees(*),
          proches:proches!proches_id_patient_fkey(*)
        `
      )
      .in("id", patientId);

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }
}
