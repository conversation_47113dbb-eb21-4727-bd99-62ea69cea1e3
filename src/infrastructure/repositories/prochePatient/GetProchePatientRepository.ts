import { supabase } from "@/infrastructure/supabase/supabase";
import {
  PROCHE_TABLE_NAME,
  USER_TABLE_NAME,
  PATIENT_TABLE_NAME,
} from "./Constant";
import { IGetProchePatientRepository } from "@/domain/interfaces/repositories/prochePatient";

export class GetProchePatientRepository implements IGetProchePatientRepository {
  async execute(patientId: number[]) {
    const { data, error } = await supabase
      .from(USER_TABLE_NAME)
      .select(
        `
          patients:patients(*),
          proches:proches!proches_id_patient_fkey(*)
        `
      )
      .in("id", patientId);

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }
}
