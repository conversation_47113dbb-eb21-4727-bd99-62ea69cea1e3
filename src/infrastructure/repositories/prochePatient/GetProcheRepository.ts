import { supabase } from "@/infrastructure/supabase/supabase";
import { PROCHE_TABLE_NAME } from "./Constant";
import { IGetProcheRepository } from "@/domain/interfaces/repositories/prochePatient";
import { Proche } from "@/domain/models";

export class GetProcheRepository implements IGetProcheRepository {
  async execute(patientId: number): Promise<Proche[]> {
    const { data, error } = await supabase
      .from(PROCHE_TABLE_NAME)
      .select("*")
      .eq("id_patient", patientId);

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }
}
