import { supabase } from "@/infrastructure/supabase/supabase";
import { USER_TABLE_NAME } from "./Constant";
import { IGetProchePatientByIdRepository } from "@/domain/interfaces/repositories/prochePatient";

export class GetProchePatientByUserIdRepository
  implements IGetProchePatientByIdRepository
{
  async execute(id: number) {
    const { data, error } = await supabase
      .from(USER_TABLE_NAME)
      .select(
        `
          proches!proches_utilisateur_id_fkey(*)
        `
      )
      .eq("id", id)
      .single();

    if (error) {
      throw new Error(error.message);
    }
    console.log(data);

    return data;
  }
}
