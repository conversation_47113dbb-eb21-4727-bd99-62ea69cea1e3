import { Proche } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { PROCHE_TABLE_NAME } from './Constant'
import { IDeleteProcheRepository } from '@/domain/interfaces/repositories/prochePatient'

export class DeleteProcheRepository implements IDeleteProcheRepository {
  async execute(id: number): Promise<Proche> {
    const { data, error } = await supabase
      .from(PROCHE_TABLE_NAME)
      .delete()
      .eq('id', id)

    handleError(error)

    return data as Proche
  }
}
