import { supabase } from "@/infrastructure/supabase/supabase";
import { USER_TABLE_NAME } from "./Constant";
import { IGetProcheByIdRepository } from "@/domain/interfaces/repositories/prochePatient";
import { Proche } from "@/domain/models";

export class GetProcheByIdRepository implements IGetProcheByIdRepository {
  async execute(id: number) {
    const { data, error } = await supabase
      .from(USER_TABLE_NAME)
      .select(
        `
          proches!proches_utilisateur_id_fkey(*)
        `
      )
      .eq("id", id)
      .single();

    if (error) {
      throw new Error(error.message);
    }
    return data;
  }
}
