import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { CONDITION_GYNECOLOGIQUE_TABLE_NAME } from './Constant'
import { IDeleteConditionGynecologiqueRepository } from '@/domain/interfaces/repositories/conditionGynecologique'
import { ConditionGynecologique } from '@/domain/models'

export class DeleteConditionGynecologiqueRepository implements IDeleteConditionGynecologiqueRepository {
  async execute(id: number): Promise<ConditionGynecologique> {
    const { data, error } = await supabase
      .from(CONDITION_GYNECOLOGIQUE_TABLE_NAME)
      .delete()
      .eq('id', id)

    if (error) handleError(error)
    return data as ConditionGynecologique
  }
}
