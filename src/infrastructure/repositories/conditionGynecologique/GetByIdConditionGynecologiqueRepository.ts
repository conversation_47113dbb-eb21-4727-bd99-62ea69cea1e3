import { ConditionGynecologique } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { CONDITION_GYNECOLOGIQUE_TABLE_NAME } from './Constant'
import { IGetByIdConditionGynecologiqueRepository } from '@/domain/interfaces/repositories/conditionGynecologique'

export class GetByIdConditionGynecologiqueRepository implements IGetByIdConditionGynecologiqueRepository {
  async execute(id: number): Promise<ConditionGynecologique | null> {
    const { data, error } = await supabase
      .from(CONDITION_GYNECOLOGIQUE_TABLE_NAME)
      .select('*')
      .eq('id', id)
      .single()

    if (error) handleError(error);
    return data as ConditionGynecologique
  }
}
