import { ConditionGynecologique } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { CONDITION_GYNECOLOGIQUE_TABLE_NAME } from './Constant'
import { ICreateConditionGynecologiqueRepository } from '@/domain/interfaces/repositories/conditionGynecologique'

export class CreateConditionGynecologiqueRepository implements ICreateConditionGynecologiqueRepository {
  async execute(data: Omit<ConditionGynecologique, "id">[]): Promise<ConditionGynecologique[]> {
    const { data: createdData, error } = await supabase
      .from(CONDITION_GYNECOLOGIQUE_TABLE_NAME)
      .insert(data)
      .select()

    if (error) handleError(error)
    return createdData as ConditionGynecologique[]
  }
}
