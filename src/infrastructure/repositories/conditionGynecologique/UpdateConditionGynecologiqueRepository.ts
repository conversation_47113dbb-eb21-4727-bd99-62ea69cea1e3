import { ConditionGynecologique } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { CONDITION_GYNECOLOGIQUE_TABLE_NAME } from './Constant'
import { IUpdateConditionGynecologiqueRepository } from '@/domain/interfaces/repositories/conditionGynecologique'

export class UpdateConditionGynecologiqueRepository implements IUpdateConditionGynecologiqueRepository {
  async execute(id: number, data: Partial<ConditionGynecologique>): Promise<ConditionGynecologique> {
    const { data: updatedData, error } = await supabase
      .from(CONDITION_GYNECOLOGIQUE_TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    if (error) handleError(error)
    return updatedData as ConditionGynecologique
  }
}
