import { ConditionGynecologique } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { CONDITION_GYNECOLOGIQUE_TABLE_NAME } from './Constant'
import { IGetAllConditionGynecologiqueRepository } from '@/domain/interfaces/repositories/conditionGynecologique'

export class GetAllConditionGynecologiqueRepository implements IGetAllConditionGynecologiqueRepository {
  async execute(carnetId: number): Promise<ConditionGynecologique[]> {
    const { data, error } = await supabase
      .from(CONDITION_GYNECOLOGIQUE_TABLE_NAME)
      .select('*')
      .eq('id_carnet', carnetId)

    if (error) handleError(error);
    return data as ConditionGynecologique[] || []
  }
}
