import { supabase } from "@/infrastructure/supabase/supabase";
import { DASH_TABLE_NAME } from "./constants";
import { Dash } from "@/domain/models/Dash";
import { IGetDashByUserIdUsecase } from "@/domain/interfaces/usecases/dash/IGetDashByUserIdUsecase";

class GetDashByUserIdRepository implements IGetDashByUserIdUsecase {
  constructor() {}

  async execute(id: number) {
    const { data, error } = await supabase
      .from(DASH_TABLE_NAME)
      .select("*")
      .eq("utilisateur_id", id);

    if (error) throw error;

    return data[0] as Dash;
  }
}

export default GetDashByUserIdRepository;
