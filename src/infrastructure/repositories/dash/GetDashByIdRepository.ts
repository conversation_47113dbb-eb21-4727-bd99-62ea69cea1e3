import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { DASH_TABLE_NAME } from "./constants.ts";
import { Dash } from "@/domain/models/Dash.ts";
import { IGetDashByIdRepository } from "@/domain/interfaces/repositories/dash/IGetDashByIdRepository.ts";

class GetDashByIdRepository implements IGetDashByIdRepository {
  constructor() {}

  async execute(id: number) {
    const { data, error } = await supabase
      .from(DASH_TABLE_NAME)
      .select("*")
      .eq("id", id);

    if (error) throw error;

    return data[0] as Dash;
  }
}

export default GetDashByIdRepository;
