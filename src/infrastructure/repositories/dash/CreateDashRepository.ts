import { Dash } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { DASH_TABLE_NAME } from "./constants";
import { ICreateDashRepository } from "@/domain/interfaces/repositories/dash/ICreateDashRepository";

class CreateDashRepository implements ICreateDashRepository {
  constructor() {}

  async execute(dash: Omit<Dash, "id">): Promise<Dash> {
    const { data, error } = await supabase
      .from(DASH_TABLE_NAME)
      .insert(dash)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data as Dash;
  }
}

export default CreateDashRepository;
