import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { HISTORIQUECARNETSANTE_TABLE_NAME } from './Constant'
import { HistoriqueCarnetSante } from '@/domain/models'
import { IUpdateHistoriqueCarnetSanteRepository } from '@/domain/interfaces/repositories/historiqueCarnetSante'

export class UpdateHistoriqueCarnetSanteRepository implements IUpdateHistoriqueCarnetSanteRepository {
  async execute(patientId: number, dataHistoriqueCarnetSante: Partial<HistoriqueCarnetSante>): Promise<HistoriqueCarnetSante> {
    const { data, error } = await supabase
      .from(HISTORIQUECARNETSANTE_TABLE_NAME)
      .update(dataHistoriqueCarnetSante)
      .eq('id_patient', patientId)

    handleError(error)

    return data as HistoriqueCarnetSante
  }
}
