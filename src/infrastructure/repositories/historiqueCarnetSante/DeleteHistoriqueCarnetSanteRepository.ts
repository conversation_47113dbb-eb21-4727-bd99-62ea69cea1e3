import { HistoriqueCarnetSante } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { HISTORIQUECARNETSANTE_TABLE_NAME } from './Constant'
import { IDeleteHistoriqueCarnetSanteRepository } from '@/domain/interfaces/repositories/historiqueCarnetSante'

export class DeleteHistoriqueCarnetSanteRepository implements IDeleteHistoriqueCarnetSanteRepository {
  async execute(id: number): Promise<HistoriqueCarnetSante> {
    const { data, error } = await supabase
      .from(HISTORIQUECARNETSANTE_TABLE_NAME)
      .delete()
      .eq('id', id)

    handleError(error)

    return data as HistoriqueCarnetSante
  }
}
