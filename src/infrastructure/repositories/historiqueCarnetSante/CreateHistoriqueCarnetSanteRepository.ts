import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { HISTORIQUECARNETSANTE_TABLE_NAME } from './Constant'
import { ICreateHistoriqueCarnetSanteRepository } from '@/domain/interfaces/repositories/historiqueCarnetSante'
import { HistoriqueCarnetSante } from '@/domain/models'

export class CreateHistoriqueCarnetSanteRepository implements ICreateHistoriqueCarnetSanteRepository {
  async execute(
    HistoriqueCarnetSanteData: Omit<HistoriqueCarnetSante, 'id'>[]
  ): Promise<HistoriqueCarnetSante[]> {
    const { data, error } = await supabase
      .from(HISTORIQUECARNETSANTE_TABLE_NAME)
      .insert(HistoriqueCarnetSanteData)
      .select()

    handleError(error)

    return data as HistoriqueCarnetSante[]
  }
}
