import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import {
  DASH_TABLE_NAME,
  HISTORIQUECARNETSANTE_TABLE_NAME,
  PROFESSIONAL_TABLE_NAME,
  USER_TABLE_NAME,
} from "./Constant";
import { IGetHistoriqueCarnetSanteRepository } from "@/domain/interfaces/repositories/historiqueCarnetSante";
import { HistoriqueCarnetSanteDTO } from "@/domain/DTOS/HistoriqueCarnetSanteDTO";
import { PROCHE_TABLE_NAME } from "../prochePatient/Constant";

export class GetHistoriqueCarnetSantePatientRepository
  implements IGetHistoriqueCarnetSanteRepository
{
  async execute(
    carnetId: number,
    tableConcernee: string
  ): Promise<HistoriqueCarnetSanteDTO[]> {
    const { data, error } = await supabase
      .from(HISTORIQUECARNETSANTE_TABLE_NAME)
      .select(
        `
      *,
      professionnel:${USER_TABLE_NAME}!id_professionnel(
        ${PROFESSIONAL_TABLE_NAME}(id, nom)
      )
    `
      )
      .eq("id_carnet", carnetId)
      .eq("table_concernee", tableConcernee);

    handleError(error);

    return data;
  }
}
