import { supabase } from "@/infrastructure/supabase/supabase";
import { PHOTO_TABLE_NAME } from "./constants";
import { Photo } from "@/domain/models/Photo";
import { PhotoTypeEnum } from "@/domain/models/enums";

/**
 * Interface pour le repository de récupération de photos par utilisateur et type
 */
export interface IGetPhotosByUserAndTypeRepository {
  execute(userId: number, type?: PhotoTypeEnum): Promise<Photo[]>;
}

/**
 * Repository pour la récupération des photos par utilisateur et type
 * 
 * @description
 * Ce repository permet de récupérer les photos d'un utilisateur
 * avec un filtrage optionnel par type (PROFILE, PRESENTATION, etc.)
 */
export class GetPhotosByUserAndTypeRepository implements IGetPhotosByUserAndTypeRepository {
  /**
   * Récupère les photos d'un utilisateur avec filtrage optionnel par type
   * 
   * @param userId - ID de l'utilisateur
   * @param type - Type de photo à filtrer (optionnel)
   * @returns Liste des photos correspondantes
   */
  async execute(userId: number, type?: PhotoTypeEnum): Promise<Photo[]> {
    try {
      let query = supabase
        .from(PHOTO_TABLE_NAME)
        .select("*")
        .eq("utilisateur_id", userId);

      // Ajouter le filtre par type si spécifié
      if (type) {
        query = query.eq("type", type);
      }

      const { data, error } = await query;

      if (error) {
        console.error("Erreur lors de la récupération des photos:", error);
        throw error;
      }

      if (!data || data.length === 0) {
        return [];
      }

      return data as Photo[];

    } catch (error) {
      console.error("Erreur lors de la récupération des photos par utilisateur et type:", error);
      throw error;
    }
  }
}
