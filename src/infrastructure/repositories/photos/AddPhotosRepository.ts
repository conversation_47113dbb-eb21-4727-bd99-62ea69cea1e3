import { supabase } from "@/infrastructure/supabase/supabase";
import { PHOTO_TABLE_NAME } from "./constants";
import { Photo } from "@/domain/models/Photo";
import { IAddPhotosRepository } from "@/domain/interfaces/repositories/photos";

class AddPhotoRepository implements IAddPhotosRepository {
  constructor() {}

  async execute(
    profilePhoto: Omit<Photo, "id"> | Omit<Photo, "id">[],
  ): Promise<Photo[]> {
    const { data, error } = await supabase
      .from(PHOTO_TABLE_NAME)
      .insert(profilePhoto)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as Photo[];
  }
}

export default AddPhotoRepository;
