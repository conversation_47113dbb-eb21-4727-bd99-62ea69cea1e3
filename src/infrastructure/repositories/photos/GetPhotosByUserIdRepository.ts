import { supabase } from "@/infrastructure/supabase/supabase";
import { PHOTO_TABLE_NAME } from "./constants";
import { Photo } from "@/domain/models/Photo";
import { IGetPhotosByUserIdUsecase } from "@/domain/interfaces/usecases/photos";

class GetPhotosByUserIdRepository implements IGetPhotosByUserIdUsecase {
  constructor() {}

  async execute(userId: number) {
    const { data, error } = await supabase
      .from(PHOTO_TABLE_NAME)
      .select("*")
      .eq("utilisateur_id", userId);

    if (error) throw error;

    if (!data || data.length == 0) return [];

    return data as Photo[];
  }
}

export default GetPhotosByUserIdRepository;
