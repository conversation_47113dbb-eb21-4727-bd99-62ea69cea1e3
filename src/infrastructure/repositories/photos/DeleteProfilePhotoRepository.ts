import { supabase } from "@/infrastructure/supabase/supabase";
import { PHOTO_TABLE_NAME } from "./constants";

/**
 * Interface pour le repository de suppression de photos de profil
 */
export interface IDeleteProfilePhotoRepository {
  execute(photoId: number): Promise<boolean>;
}

/**
 * Repository pour la suppression des photos de profil
 * 
 * @description
 * Ce repository gère la suppression complète d'une photo de profil :
 * - Suppression du fichier dans Supabase Storage (bucket 'profile')
 * - Suppression de l'enregistrement en base de données
 */
export class DeleteProfilePhotoRepository implements IDeleteProfilePhotoRepository {
  private readonly PROFILE_IMAGE_BUCKET = "profile";

  /**
   * Supprime une photo de profil
   * 
   * @param photoId - ID de la photo à supprimer
   * @returns true si la suppression a réussi, false sinon
   */
  async execute(photoId: number): Promise<boolean> {
    try {
      // Récupérer d'abord les informations de la photo pour obtenir le chemin du fichier
      const { data: photoData, error: fetchError } = await supabase
        .from(PHOTO_TABLE_NAME)
        .select("path")
        .eq("id", photoId)
        .single();

      if (fetchError) {
        console.error("Erreur lors de la récupération de la photo:", fetchError);
        throw fetchError;
      }

      // Extraire le nom du fichier depuis l'URL pour la suppression du storage
      if (photoData?.path) {
        try {
          const url = new URL(photoData.path);
          // Pour le bucket 'profile', le chemin est généralement : /storage/v1/object/public/profile/filename
          const pathParts = url.pathname.split('/');
          const fileName = pathParts[pathParts.length - 1]; // Récupère le nom du fichier

          if (fileName) {
            // Supprimer le fichier du storage
            const { error: storageError } = await supabase.storage
              .from(this.PROFILE_IMAGE_BUCKET)
              .remove([fileName]);

            if (storageError) {
              console.error("Erreur lors de la suppression du fichier:", storageError);
              // On continue même si la suppression du fichier échoue
              // car l'important est de supprimer l'enregistrement en base
            }
          }
        } catch (urlError) {
          console.error("Erreur lors du parsing de l'URL:", urlError);
          // On continue même si le parsing de l'URL échoue
        }
      }

      // Supprimer l'enregistrement de la base de données
      const { error } = await supabase
        .from(PHOTO_TABLE_NAME)
        .delete()
        .eq("id", photoId);

      if (error) {
        console.error("Erreur lors de la suppression de la photo:", error);
        throw error;
      }

      return true;

    } catch (error) {
      console.error("Erreur lors de la suppression de la photo de profil:", error);
      return false;
    }
  }
}
