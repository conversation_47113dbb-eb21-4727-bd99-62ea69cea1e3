import { supabase } from "@/infrastructure/supabase/supabase";
import { PHOTO_TABLE_NAME } from "./constants";
import { Photo } from "@/domain/models/Photo";

export interface IGetPhotoByPathRepository {
  execute(path: string): Promise<Photo>;
}

class GetPhotoByPathRepository implements IGetPhotoByPathRepository {
  constructor() {}

  async execute(path: string) {
    const { data, error } = await supabase
      .from(PHOTO_TABLE_NAME)
      .select("*")
      .eq("path", path);

    if (error) throw error;

    if (!data || data.length == 0) return null;

    return data[0] as Photo;
  }
}

export default GetPhotoByPathRepository;
