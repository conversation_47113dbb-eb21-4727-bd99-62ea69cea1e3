import { supabase } from "@/infrastructure/supabase/supabase";
import { PHOTO_TABLE_NAME } from "./constants";
import { IDeleteCabinetImageRepository } from "@/domain/usecases/professional/DeleteCabinetImageUsecase";

/**
 * Repository pour la suppression des images du cabinet
 * 
 * @description
 * Ce repository gère la suppression des images du cabinet en base de données.
 * Il récupère d'abord le chemin de la photo avant de la supprimer pour permettre
 * au usecase de supprimer aussi le fichier du storage.
 */
export class DeleteCabinetImageRepository implements IDeleteCabinetImageRepository {
  /**
   * Supprime une image du cabinet en base de données
   * 
   * @param photoId - ID de la photo à supprimer
   * @returns Résultat avec le chemin de la photo supprimée
   */
  async execute(photoId: number): Promise<{ success: boolean; photoPath?: string }> {
    try {
      // Récupérer d'abord les informations de la photo pour obtenir le chemin du fichier
      const { data: photoData, error: fetchError } = await supabase
        .from(PHOTO_TABLE_NAME)
        .select("path")
        .eq("id", photoId)
        .single();

      if (fetchError) {
        console.error("Erreur lors de la récupération de la photo:", fetchError);
        return { success: false };
      }

      // Supprimer l'enregistrement de la base de données
      const { error: deleteError } = await supabase
        .from(PHOTO_TABLE_NAME)
        .delete()
        .eq("id", photoId);

      if (deleteError) {
        console.error("Erreur lors de la suppression de la photo:", deleteError);
        return { success: false };
      }

      return {
        success: true,
        photoPath: photoData?.path
      };

    } catch (error) {
      console.error("Erreur lors de la suppression de l'image du cabinet:", error);
      return { success: false };
    }
  }
}
