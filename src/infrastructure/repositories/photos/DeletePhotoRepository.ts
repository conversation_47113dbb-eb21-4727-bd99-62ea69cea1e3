import { supabase } from "@/infrastructure/supabase/supabase";
import { PHOTO_TABLE_NAME } from "./constants";
import { Photo } from "@/domain/models/Photo";
import { IDeletePhotoRepository } from "@/domain/interfaces/repositories/photos/IDeletePhotoRepository.ts";

class DeletePhotoRepository implements IDeletePhotoRepository {
  async execute(id: number) {
    const { data, error } = await supabase
      .from(PHOTO_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select();

    if (error) throw error;

    return data[0] as Photo;
  }
}

export default DeletePhotoRepository;
