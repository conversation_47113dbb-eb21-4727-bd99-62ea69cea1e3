import { horaire_hebdomadaire } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IDeleteWeeklyScheduleRepository } from '@/domain/interfaces/repositories/weeklySchedule/IDeleteWeeklyScheduleRepository'

const WEEKLY_SCHEDULE_TABLE_NAME = 'horaire_hebdomadaire'

export class DeleteWeeklyScheduleRepository implements IDeleteWeeklyScheduleRepository {
  async execute(id_parametre_disponibilite: number) {
    const { data, error } = await supabase
      .from(WEEKLY_SCHEDULE_TABLE_NAME)
      .delete()
      .eq('id_parametre_disponibilite', id_parametre_disponibilite)

    handleError(error)

    return data as horaire_hebdomadaire[]
  }
}
