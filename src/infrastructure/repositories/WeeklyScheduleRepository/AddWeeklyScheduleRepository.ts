import { horaire_hebdomadaire } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IAddWeeklyScheduleRepository } from '@/domain/interfaces/repositories/weeklySchedule'

const WEEKLY_SCHEDULE_TABLE_NAME = 'horaire_hebdomadaire'

export class AddWeeklyScheduleRepository implements IAddWeeklyScheduleRepository {
  async execute(schedule: Omit<horaire_hebdomadaire, 'id'>[]) {
    const { data, error } = await supabase
      .from(WEEKLY_SCHEDULE_TABLE_NAME)
      .insert(schedule)
      .select()

    handleError(error)

    return data as horaire_hebdomadaire[]
  }
}
