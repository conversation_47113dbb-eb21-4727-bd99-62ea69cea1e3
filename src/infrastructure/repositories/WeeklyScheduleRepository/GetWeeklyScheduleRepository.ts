import { horaire_hebdomadaire } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IGetWeeklySchedulesRepository } from '@/domain/interfaces/repositories/weeklySchedule'

const WEEKLY_SCHEDULE_TABLE_NAME = 'horaire_hebdomadaire'

export class GetWeeklySchedulesRepository implements IGetWeeklySchedulesRepository {
  async execute(professionalId: number) {
    const { data, error } = await supabase
      .from(WEEKLY_SCHEDULE_TABLE_NAME)
      .select('*')
      .eq('id_professionnel', professionalId)
      .single();

    handleError(error)

    return data as horaire_hebdomadaire
  }
}
