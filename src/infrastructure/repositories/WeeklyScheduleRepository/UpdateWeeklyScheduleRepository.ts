import { horaire_hebdomadaire } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IUpdateWeeklyScheduleRepository } from '@/domain/interfaces/repositories/weeklySchedule'

const WEEKLY_SCHEDULE_TABLE_NAME = 'horaire_hebdomadaire'

export class UpdateWeeklyScheduleRepository implements IUpdateWeeklyScheduleRepository {
  async execute(id: number, schedule: Partial<horaire_hebdomadaire>) {
    const { data, error } = await supabase
      .from(WEEKLY_SCHEDULE_TABLE_NAME)
      .update(schedule)
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as horaire_hebdomadaire
  }
}
