import { supabase } from "@/infrastructure/supabase/supabase";
import { ETABLISHMENT_LIST_TABLEN_NAME } from "./constants";
import { ListeTypeEtablissement } from "@/domain/models";
import { IGetEtablishmentTypesRepository } from "@/domain/interfaces/repositories/typeEtablissement/IGetEtablishmentTypesRepository";

class GetEtablishmentTypesRepository implements IGetEtablishmentTypesRepository {
  constructor() {}

  async execute(): Promise<ListeTypeEtablissement[]> {
    const { data, error } = await supabase
      .from(ETABLISHMENT_LIST_TABLEN_NAME)
      .select("*");

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as ListeTypeEtablissement[];
  }
}

export default GetEtablishmentTypesRepository;
