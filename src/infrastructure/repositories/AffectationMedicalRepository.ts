import { IAffectationMedicalRepository } from '@/domain/interfaces/repositories/IAffectationMedicalRepository'
import { AffectationMedical } from '@/domain/models'
import { PostgrestError } from '@supabase/supabase-js'
import { supabase } from '../supabase/supabase'
import { SupabaseError } from '../supabase/supabaseError'

// Nom de la table
const AffectationMedical_TABLE = 'AffectationMedical'

const handleError = (error: PostgrestError) => {
  if (error) {
    throw new SupabaseError(error.code, error.message, error.details)
  }
}

export class AffectationMedicalRepository implements IAffectationMedicalRepository {
  async getAffectation(id: number): Promise<AffectationMedical> {
    const { data, error } = await supabase.from(AffectationMedical_TABLE).select('*').eq('id', id)

    handleError(error)

    return data[0] as AffectationMedical
  }

  async getAffectationsByPatient(id_patient: number): Promise<AffectationMedical[]> {
    const { data, error } = await supabase
      .from(AffectationMedical_TABLE)
      .select('*')
      .eq('id_patient', id_patient)

    handleError(error)

    return data as AffectationMedical[]
  }

  async createAffectation(
    affectationData: Omit<AffectationMedical, 'id'>
  ): Promise<AffectationMedical> {
    const { data, error } = await supabase
      .from(AffectationMedical_TABLE)
      .insert(affectationData)
      .select()

    handleError(error)

    return data[0] as AffectationMedical
  }

  async updateAffectation(
    id: number,
    updatedData: Partial<Omit<AffectationMedical, 'id'>>
  ): Promise<AffectationMedical> {
    if (!id) throw new Error("L'id est requis pour la modification.")

    const { data, error } = await supabase
      .from(AffectationMedical_TABLE)
      .update(updatedData)
      .eq('id', id)
      .select()

    handleError(error)

    return data[0] as AffectationMedical
  }

  async deleteAffectation(id: number): Promise<AffectationMedical> {
    if (!id) throw new Error("L'id est requis pour la suppression.")

    const { data, error } = await supabase
      .from(AffectationMedical_TABLE)
      .delete()
      .eq('id', id)
      .select()

    handleError(error)

    return data[0] as AffectationMedical
  }
}

export const affectationMedicalRepository = new AffectationMedicalRepository()
