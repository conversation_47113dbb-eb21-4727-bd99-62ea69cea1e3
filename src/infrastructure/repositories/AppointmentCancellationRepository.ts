import { IAppointmentCancellationRepository } from '@/domain/interfaces/repositories/IAppointmentCancellationRepository'
import { AnnulerRendezVous } from '@/domain/models/AnnulerRendezVous'
import { supabase } from '../supabase/supabase'
import { handleError } from '../supabase/supabaseFetchError'

export class AppointmentCancellationRepository implements IAppointmentCancellationRepository {
  private readonly TABLE_NAME = 'AnnulerRendezVous'

  async getAnnulationById(id: number): Promise<AnnulerRendezVous> {
    const { data, error } = await supabase.from(this.TABLE_NAME).select('*').eq('id', id).single()

    handleError(error)

    return data as AnnulerRendezVous
  }

  async getAnnulationsByRendezVous(id_rendez_vous: number): Promise<AnnulerRendezVous[]> {
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*')
      .eq('id_rendez-vous', id_rendez_vous)

    handleError(error)

    return data as AnnulerRendezVous[]
  }

  async createAnnulation(
    annulationData: Omit<AnnulerRendezVous, 'id'>
  ): Promise<AnnulerRendezVous> {
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .insert(annulationData)
      .select()
      .single()

    handleError(error)

    return data as AnnulerRendezVous
  }

  async deleteAnnulation(id: number): Promise<AnnulerRendezVous> {
    if (!id) {
      throw new Error("L'id est requis pour la suppression.")
    }

    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as AnnulerRendezVous
  }
}

export const appointmentCancelRepository = new AppointmentCancellationRepository()
