import { ICreateProfessionalInvitationRepository } from "@/domain/interfaces/repositories/professionalInvitation";
import { Invitation } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_INVITATION_TABLE_NAME } from "./constants";

class CreateProfessionalInvitationRepository
  implements ICreateProfessionalInvitationRepository
{
  constructor() {}

  async execute(invitation: Omit<Invitation, "id">): Promise<Invitation> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_INVITATION_TABLE_NAME)
      .insert(invitation)
      .select();

    if (error) {
      throw error;
    }

    return data[0] as Invitation;
  }
}

export default CreateProfessionalInvitationRepository;
