import { IMarkInvitationAsUsedRepository } from "@/domain/interfaces/repositories/professionalInvitation";
import { Invitation } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_INVITATION_TABLE_NAME } from "./constants";

class MarkInvitationAsUsedRepository implements IMarkInvitationAsUsedRepository {
  constructor() {}

  async execute(invitation_id: number): Promise<Invitation> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_INVITATION_TABLE_NAME)
      .update({ est_utilisee: true })
      .eq("id", invitation_id)
      .select();

    if (error) throw error;

    return data[0] as Invitation;
  }
}

export default MarkInvitationAsUsedRepository;
