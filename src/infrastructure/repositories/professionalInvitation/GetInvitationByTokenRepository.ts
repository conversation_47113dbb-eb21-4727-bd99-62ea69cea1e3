import { IGetInvitationByTokenRepository } from "@/domain/interfaces/repositories/professionalInvitation";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_INVITATION_TABLE_NAME } from "./constants";
import { ProfessionalInvitationDTO } from "@/domain/DTOS";

class GetInvitationByTokenRepository
  implements IGetInvitationByTokenRepository
{
  constructor() {}

  async execute(token: string) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_INVITATION_TABLE_NAME)
      .select(
        `
        *,
        demande_adhesion (*)
      `,
      )
      .eq("token", token);

    if (error) throw error;

    return data[0] as ProfessionalInvitationDTO;
  }
}

export default GetInvitationByTokenRepository;
