import { supabase } from "@/infrastructure/supabase/supabase";
import { ETABLISHMENT_PROFESSIONNEL_TABLE_NAME } from "./constants";
import { EtablissementProfessionnel } from "@/domain/models";
import { IGetProfessionalEtablishmentsByProfessionalIdRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel";

class GetProfessionalEtablishmentByProfessionalIdRepository
  implements IGetProfessionalEtablishmentsByProfessionalIdRepository
{
  constructor() {}

  async execute(professionalId: number) {
    const { data, error } = await supabase
      .from(ETABLISHMENT_PROFESSIONNEL_TABLE_NAME)
      .select(`*`)
      .eq("id_professionnel", professionalId);

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as EtablissementProfessionnel[];
  }
}

export default GetProfessionalEtablishmentByProfessionalIdRepository;
