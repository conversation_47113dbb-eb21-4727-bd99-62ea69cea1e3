import { IDeleteProfessionalEtablishmentRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel";
import { supabase } from "../../supabase/supabase";
import { handleError } from "../../supabase/supabaseFetchError";
import { ETABLISHMENT_PROFESSIONNEL_TABLE_NAME } from "./constants";

class DeleteProfessionalEtablishmentRepository
  implements IDeleteProfessionalEtablishmentRepository
{
  async execute(id: number): Promise<void> {
    const { error } = await supabase
      .from(ETABLISHMENT_PROFESSIONNEL_TABLE_NAME)
      .delete()
      .eq("id", id);
    handleError(error);
  }
}

export default DeleteProfessionalEtablishmentRepository;
