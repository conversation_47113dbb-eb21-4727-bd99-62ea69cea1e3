import { ICreateProfessionalEtablishmentRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel/ICreateProfessionalEtablishmentRepository";
import { EtablissementProfessionnel } from "@/domain/models";
import { supabase } from "../../supabase/supabase";
import { handleError } from "../../supabase/supabaseFetchError";
import { ETABLISHMENT_PROFESSIONNEL_TABLE_NAME } from "./constants";

/**
 * Repository pour la création d'établissements professionnels
 *
 * @description Implémente la persistance des établissements professionnels dans Supabase
 */
class CreateProfessionalEtablishmentRepository
  implements ICreateProfessionalEtablishmentRepository
{
  /**
   * Crée un nouvel établissement professionnel dans la base de données
   *
   * @param etablissement - Les données de l'établissement à créer (sans l'ID)
   * @returns Promise<EtablissementProfessionnel> - L'établissement créé avec son ID
   * @throws Error si la création échoue
   */
  async execute(
    etablissement: Omit<EtablissementProfessionnel, "id">,
  ): Promise<EtablissementProfessionnel> {
    try {
      // Insertion dans la base de données avec retour des données créées
      const { data, error } = await supabase
        .from(ETABLISHMENT_PROFESSIONNEL_TABLE_NAME)
        .insert(etablissement)
        .select()
        .single();

      // Gestion des erreurs Supabase
      handleError(error);

      // Vérification que les données ont été retournées
      if (!data) {
        throw new Error(
          "Aucune donnée retournée après la création de l'établissement",
        );
      }

      return data as EtablissementProfessionnel;
    } catch (error) {
      console.error(
        "Erreur dans CreateEtablissementProfessionnelRepository:",
        error,
      );

      // Re-lancer l'erreur pour qu'elle soit gérée par le use case
      if (error instanceof Error) {
        throw error;
      }

      throw new Error(
        "Erreur inconnue lors de la création de l'établissement professionnel",
      );
    }
  }
}

export default CreateProfessionalEtablishmentRepository;
