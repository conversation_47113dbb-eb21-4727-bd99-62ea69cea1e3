import { IGetProfessionalEtablishmentsRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel/IGetProfessionalEtablishmentsRepository";
import { supabase } from "../../supabase/supabase";
import { EtablissementProfessionnel } from "@/domain/models";
import { ETABLISHMENT_PROFESSIONNEL_TABLE_NAME } from "./constants";

class GetProfessionalEtablishmentsRepository
  implements IGetProfessionalEtablishmentsRepository
{
  async execute(): Promise<EtablissementProfessionnel[]> {
    const { data, error } = await supabase
      .from(ETABLISHMENT_PROFESSIONNEL_TABLE_NAME)
      .select("*");

    if (error) throw error;

    return data as EtablissementProfessionnel[];
  }
}

export default GetProfessionalEtablishmentsRepository;
