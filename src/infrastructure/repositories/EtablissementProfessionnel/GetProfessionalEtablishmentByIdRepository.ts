import { IGetProfessionalEtablishmentByIdRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel";
import { supabase } from "../../supabase/supabase";
import { ETABLISHMENT_PROFESSIONNEL_TABLE_NAME } from "./constants";
import { EtablissementProfessionnel } from "@/domain/models";

class GetProfessionalEtablishmentByIdRepository
  implements IGetProfessionalEtablishmentByIdRepository
{
  async execute(id: number): Promise<EtablissementProfessionnel | null> {
    const { data, error } = await supabase
      .from(ETABLISHMENT_PROFESSIONNEL_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();
    if (error) throw error;
    return (data as EtablissementProfessionnel) || null;
  }
}

export default GetProfessionalEtablishmentByIdRepository;
