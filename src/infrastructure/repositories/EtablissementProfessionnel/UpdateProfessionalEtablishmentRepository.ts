import { EtablissementProfessionnel } from "@/domain/models";
import { supabase } from "../../supabase/supabase";
import { handleError } from "../../supabase/supabaseFetchError";
import { ETABLISHMENT_PROFESSIONNEL_TABLE_NAME } from "./constants";
import { IUpdateEtablissementProfessionnelRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel/IUpdateEtablissementProfessionnelRepository";

class UpdateEtablissementProfessionnelRepository
    implements IUpdateEtablissementProfessionnelRepository {
    async execute(
        id: number,
        updates: Partial<Omit<EtablissementProfessionnel, "id">>,
    ): Promise<EtablissementProfessionnel> {
        try {
            const { data, error } = await supabase
                .from(ETABLISHMENT_PROFESSIONNEL_TABLE_NAME)
                .update(updates)
                .eq("id", id)
                .select()
                .single();
            handleError(error);
            if (!data) {
                throw new Error(
                    "Aucune donnée retournée après la mise à jour de l'établissement",
                );
            }
            return data as EtablissementProfessionnel;
        } catch (error) {
            if (error instanceof Error) throw error;
            throw new Error(
                "Erreur inconnue lors de la mise à jour de l'établissement professionnel",
            );
        }
    }
}

export default UpdateEtablissementProfessionnelRepository;
