import { antecedant_sociaux_fumeur } from "@/domain/models";
import { IUpdateAntecedentSociauxFummeurRepository } from "@/domain/interfaces/repositories/antecedentSociaux";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME } from "./Constant";

export class UpdateAntecedentSociauxFummeurRepository
  implements IUpdateAntecedentSociauxFummeurRepository
{
  async execute(
    id: number,
    data: Partial<antecedant_sociaux_fumeur>
  ): Promise<antecedant_sociaux_fumeur> {
    const { data: updatedData, error } = await supabase
      .from(ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME)
      .update(data)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return updatedData;
  }
}
