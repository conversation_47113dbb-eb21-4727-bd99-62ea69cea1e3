import { antecedant_sociaux_fumeur } from "@/domain/models";
import { IGetAntecedentSociauxFummeurRepository } from "@/domain/interfaces/repositories/antecedentSociaux";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME } from "./Constant";

export class GetAntecedentSociauxFummeurRepository
  implements IGetAntecedentSociauxFummeurRepository
{
  async execute(carnetId: number): Promise<antecedant_sociaux_fumeur> {
    const { data, error } = await supabase
      .from(ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME)
      .select("*")
      .eq("id_carnet", carnetId);

    if (error) {
      throw error;
    }

    return data[0] as antecedant_sociaux_fumeur;
  }
}
