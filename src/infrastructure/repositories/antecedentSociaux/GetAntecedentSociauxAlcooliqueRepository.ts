import { antecedant_sociaux_alcoolique } from "@/domain/models";
import { IGetAntecedentSociauxAlcooliqueRepository } from "@/domain/interfaces/repositories/antecedentSociaux";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ANTECEDENT_SOCIAUX_ALCOOLIQUE_TABLE_NAME } from "./Constant";

export class GetAntecedentSociauxAlcooliqueRepository
  implements IGetAntecedentSociauxAlcooliqueRepository
{
  async execute(carnetId: number): Promise<antecedant_sociaux_alcoolique> {
    const { data, error } = await supabase
      .from(ANTECEDENT_SOCIAUX_ALCOOLIQUE_TABLE_NAME)
      .select("*")
      .eq("id_carnet", carnetId);

    if (error) throw error;

    return data[0] as antecedant_sociaux_alcoolique;
  }
}
