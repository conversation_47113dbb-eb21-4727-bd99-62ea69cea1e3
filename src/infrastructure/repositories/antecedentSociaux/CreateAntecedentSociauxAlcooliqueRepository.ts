import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { ANTECEDENT_SOCIAUX_ALCOOLIQUE_TABLE_NAME } from "./Constant";
import { ICreateAntecedentSociauxAlcooliqueRepository } from "@/domain/interfaces/repositories/antecedentSociaux";
import { antecedant_sociaux_alcoolique } from "@/domain/models";

export class CreateAntecedentSociauxAlcooliqueRepository
  implements ICreateAntecedentSociauxAlcooliqueRepository
{
  async execute(
    data: Omit<antecedant_sociaux_alcoolique, "id">
  ): Promise<antecedant_sociaux_alcoolique> {
    const { data: createdData, error } = await supabase
      .from(ANTECEDENT_SOCIAUX_ALCOOLIQUE_TABLE_NAME)
      .insert(data)
      .select()
      .single();

    if (error) handleError(error);
    return createdData as antecedant_sociaux_alcoolique;
  }
}
