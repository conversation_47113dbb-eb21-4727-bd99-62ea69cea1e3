import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME } from "./Constant";
import { ICreateAntecedentSociauxFummeurRepository } from "@/domain/interfaces/repositories/antecedentSociaux";
import { antecedant_sociaux_fumeur } from "@/domain/models";

export class CreateAntecedentSociauxFummeurRepository
  implements ICreateAntecedentSociauxFummeurRepository
{
  async execute(
    data: Omit<antecedant_sociaux_fumeur, "id">
  ): Promise<antecedant_sociaux_fumeur> {
    const { data: createdData, error } = await supabase
      .from(ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME)
      .insert(data)
      .select()
      .single();

    if (error) handleError(error);
    return createdData as antecedant_sociaux_fumeur;
  }
}
