import { IDeleteAntecedentSociauxFummeurRepository } from "@/domain/interfaces/repositories/antecedentSociaux";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME } from "./Constant";

export class DeleteAntecedentSociauxFummeurRepository
  implements IDeleteAntecedentSociauxFummeurRepository
{
  async execute(id: number): Promise<void> {
    const { error } = await supabase
      .from(ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME)
      .delete()
      .eq("id", id);

    if (error) {
      throw error;
    }
  }
}
