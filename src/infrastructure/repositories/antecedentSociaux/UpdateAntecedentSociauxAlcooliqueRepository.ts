import { antecedant_sociaux_alcoolique } from "@/domain/models";
import { IUpdateAntecedentSociauxAlcooliqueRepository } from "@/domain/interfaces/repositories/antecedentSociaux";
import { supabase } from "@/infrastructure/supabase/supabase";
import { ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME } from "./Constant";

export class UpdateAntecedentSociauxAlcooliqueRepository
  implements IUpdateAntecedentSociauxAlcooliqueRepository
{
  async execute(
    id: number,
    data: Partial<antecedant_sociaux_alcoolique>
  ): Promise<antecedant_sociaux_alcoolique> {
    const { data: updatedData, error } = await supabase
      .from(ANTECEDENT_SOCIAUX_FUMMEUR_TABLE_NAME)
      .update(data)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return updatedData;
  }
}
