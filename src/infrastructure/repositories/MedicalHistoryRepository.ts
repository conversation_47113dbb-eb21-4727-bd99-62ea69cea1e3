import { IMedicalHistoryRepository } from '@/domain/interfaces/repositories/IMedicalHistoryRepository'
import { historiques_medicaux } from '@/domain/models'
import { supabase } from '../supabase/supabase'
import { handleError } from '../supabase/supabaseFetchError'

class MedicalHistoryRepository implements IMedicalHistoryRepository {
  private readonly TABLE_NAME = 'historiques_medicaux'

  async getAllMedicalHistories(): Promise<historiques_medicaux[]> {
    const { data, error } = await supabase.from(this.TABLE_NAME).select('*')

    handleError(error)

    return data
  }

  async getMedicalHistoriesByProfessionalId(
    professionalId: number
  ): Promise<historiques_medicaux[]> {
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*')
      .eq('id_professionnel', professionalId)

    handleError(error)

    return data
  }

  async getMedicalHistoryByPatientId(patientId: number): Promise<historiques_medicaux> {
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*')
      .eq('patient_id', patientId)
      .single()

    handleError(error)

    return data
  }

  async getMedicalHistoryByActionId(actionId: number): Promise<historiques_medicaux> {
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*')
      .eq('id_action_historique', actionId)
      .single()

    handleError(error)

    return data
  }

  async getMedicalHistoryByConsultationId(consultationId: number): Promise<historiques_medicaux> {
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*')
      .eq('id_consultation', consultationId)
      .single()

    handleError(error)

    return data
  }

  async getMedicalHistoryById(id: number): Promise<historiques_medicaux> {
    const { data, error } = await supabase.from(this.TABLE_NAME).select('*').eq('id', id).single()

    handleError(error)

    return data
  }

  async createMedicalHistory(
    history: Omit<historiques_medicaux, 'id'>
  ): Promise<historiques_medicaux> {
    const { data, error } = await supabase.from(this.TABLE_NAME).insert(history).select().single()

    handleError(error)

    return data
  }

  async updateMedicalHistory(
    id: number,
    data: Partial<historiques_medicaux>
  ): Promise<historiques_medicaux> {
    const { data: updatedData, error } = await supabase
      .from(this.TABLE_NAME)
      .update(data)
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return updatedData
  }

  async deleteMedicalHistory(id: number): Promise<void> {
    const { error } = await supabase.from(this.TABLE_NAME).delete().eq('id', id)

    handleError(error)
  }
}

export const medicalHistoryRepository = new MedicalHistoryRepository()
