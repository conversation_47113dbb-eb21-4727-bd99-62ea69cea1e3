import { creneau_horaire } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ICreateTimeSlotRepository } from '@/domain/interfaces/repositories/timelot'
import { TIME_SLOT_TABLE_NAME } from './Constant'

export class CreateTimeSlotRepository implements ICreateTimeSlotRepository {
  async execute(newTimeSlot: Omit<creneau_horaire, 'id'>[]): Promise<creneau_horaire[]> {
    const { data, error } = await supabase
      .from(TIME_SLOT_TABLE_NAME)
      .insert(newTimeSlot)
      .select()

    handleError(error)

    return data as creneau_horaire[]
  }
}
