import { creneau_horaire } from '@/domain/models'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { IDeleteTimeSlotRepository } from '@/domain/interfaces/repositories/timelot/IDeleteTimeSlotRepository'
import { TIME_SLOT_TABLE_NAME } from './Constant'

export class DeleteTimeSlotRepository implements IDeleteTimeSlotRepository {
  async execute(id: number): Promise<creneau_horaire> {
    const { data, error } = await supabase
      .from(TIME_SLOT_TABLE_NAME)
      .delete()
      .eq('id', id)
      .select()
      .single()

    handleError(error)

    return data as creneau_horaire
  }
}
