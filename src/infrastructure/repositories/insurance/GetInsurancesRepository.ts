import { supabase } from "@/infrastructure/supabase/supabase";
import { INSURANCE_LIST_TABLE_NAME } from "./constants";
import { ListeAssurances } from "@/domain/models";
import { IGetInsurancesRepository } from "@/domain/interfaces/repositories/insurance";

class GetInsurancesRepository implements IGetInsurancesRepository {
  constructor() {}

  async execute() {
    const { data, error } = await supabase
      .from(INSURANCE_LIST_TABLE_NAME)
      .select("*");

    if (error) throw error;
    if (!data || data.length === 0) return [];

    return data as ListeAssurances[];
  }
}

export default GetInsurancesRepository;
