import { supabase } from "@/infrastructure/supabase/supabase";
import {
  INSURANCE_LIST_TABLE_NAME,
  PROFESSIONAL_INSURANCE_TABLE_NAME,
} from "./constants";
import { ListeAssurances } from "@/domain/models";
import { IGetProfessionalInsurancesRepository } from "@/domain/interfaces/repositories/insurance";

class GetProfessionalInsurancesRepository
  implements IGetProfessionalInsurancesRepository
{
  constructor() {}

  async execute(professional_id: number) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_INSURANCE_TABLE_NAME)
      .select(
        `
        id_assurance,
        ${INSURANCE_LIST_TABLE_NAME} (
          id,
          nom,
          date_creation
        )
      `
      )
      .eq("id_professionnel", professional_id);

    if (error) throw error;
    if (!data || data.length === 0) return [];

    // Transformer les données pour obtenir uniquement les informations des assurances
    const insurances = data.map((item) => item[INSURANCE_LIST_TABLE_NAME]);

    return insurances as unknown as ListeAssurances[];
  }
}

export default GetProfessionalInsurancesRepository;
