import { ListeAssurances } from "@/domain/models/AssuranceProfessionnel.ts";
import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { INSURANCE_LIST_TABLE_NAME } from "./constants.ts";
import { IGetInsuranceByIdRepository } from "@/domain/interfaces/repositories/insurance/IGetInsuranceByIdRepository.ts";

class GetInsuranceByIdRepository implements IGetInsuranceByIdRepository {
  constructor() {}

  async execute(id: number) {
    const { data, error } = await supabase
      .from(INSURANCE_LIST_TABLE_NAME)
      .select("*")
      .eq("id", id);

    if (error) throw error;

    return data[0] as ListeAssurances;
  }
}

export default GetInsuranceByIdRepository;
