import { ISupabaseEdgeFunctionService } from "@/domain/interfaces/supabase";

export class SupabaseEdgeFunctionService
  implements ISupabaseEdgeFunctionService {
  private supabaseUrl: string;
  private supabaseAnonKey: string;

  constructor() {
    this.supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    this.supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (!this.supabaseUrl || !this.supabaseAnonKey) {
      throw new Error(
        "Supabase credentials are missing in environment variables.",
      );
    }
  }

  /**
   * Exécute une Edge Function Supabase
   * @param functionName Nom de la fonction Edge
   * @param data Données à envoyer (pour POST)
   * @param method Méthode HTTP (GET ou POST)
   * @returns Réponse JSON ou erreur
   */
  async callEdgeFunction<T>(
    functionName: string,
    data?: Record<string, unknown>,
    method: "POST" | "GET" = "POST",
  ): Promise<T> {
    const url = `${this.supabaseUrl}/functions/v1/${functionName}`;

    const options: RequestInit = {
      method,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${this.supabaseAnonKey}`,
      },
      body: method === "POST" ? JSON.stringify(data) : undefined,
    };

    try {
      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`Supabase Edge Function error: ${response.statusText}`);
      }

      return (await response.json()) as T;
    } catch (error) {
      console.error(`Failed to call Edge Function '${functionName}':`, error);
      throw error;
    }
  }
}
