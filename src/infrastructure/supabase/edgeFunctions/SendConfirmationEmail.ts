import { ISendConfirmationEmail } from "@/domain/interfaces/supabase";
import { SupabaseEdgeFunctionService } from "@/infrastructure/supabase/edgeFunctions/SupabaseEdgeFunctionService";

export class SendConfirmationEmail implements ISendConfirmationEmail {
  constructor(
    private readonly supabaseEdgeFunctionService: SupabaseEdgeFunctionService,
  ) {
    this.supabaseEdgeFunctionService = new SupabaseEdgeFunctionService();
  }

  async execute(email: string, validation_link: string): Promise<string> {
    try {
      const response = await this.supabaseEdgeFunctionService.callEdgeFunction(
        "confirm-account",
        { to: email, validation_link: validation_link },
      );

      return JSON.stringify(response);
    } catch (error) {
      console.error("Error in SendConfirmationEmail:", error);
      throw error;
    }
  }
}
