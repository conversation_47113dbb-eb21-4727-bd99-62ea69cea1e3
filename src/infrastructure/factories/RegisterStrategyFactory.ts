import { IRegisterStrategy } from "@/domain/interfaces/strategies/IRegisterStrategy";
import { RegisterPatientStrategy } from "@/application/strategies/RegisterPatientStrategy";
import { RegisterProfessionalStrategy } from "@/application/strategies/RegisterProfessionalStrategy";
import { RegisterAdminStrategy } from "@/application/strategies/RegisterAdminStrategy";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { IProfessionalRepository } from "@/domain/interfaces/repositories";
import { ICreateAdminRepository } from "@/domain/interfaces/repositories/admins";
import { ICreatePatientRepository } from "@/domain/interfaces/repositories/patients";
import { IMatriculeGenerator } from "@/domain/interfaces/services/IMatriculeGenerator";

export class RegisterStrategyFactory {
  constructor(
    private readonly createPatientRepository: ICreatePatientRepository,
    private readonly professionalRepository: IProfessionalRepository,
    private readonly adminRepository: ICreateAdminRepository,
    private readonly matriculeGenerator: IMatriculeGenerator
  ) { }

  createStrategy(role: utilisateurs_role_enum): IRegisterStrategy {

    const strategies = {
      [utilisateurs_role_enum.PATIENT]: () =>
        new RegisterPatientStrategy(this.createPatientRepository, this.matriculeGenerator),
      [utilisateurs_role_enum.PROFESSIONNEL]: () =>
        new RegisterProfessionalStrategy(this.professionalRepository),
      [utilisateurs_role_enum.ADMIN]: () =>
        new RegisterAdminStrategy(this.adminRepository),
    };

    const strategyFactory = strategies[role];
    if (!strategyFactory) {
      throw new Error(`Aucune stratégie trouvée pour le rôle : ${role}`);
    }

    return strategyFactory();
  }
}
