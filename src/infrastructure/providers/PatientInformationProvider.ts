import { IGetPatientByUserIdRepository } from "@/domain/interfaces/repositories/patients";
import { IUserInformationProvider } from "@/domain/interfaces/services/IUserInformationProvider";
import { Patient } from "@/domain/models";

export class PatientInformationProvider implements IUserInformationProvider {
  constructor(private readonly getPatientByUserIdRepository: IGetPatientByUserIdRepository) { }

  async getUserInformation(id: number): Promise<Patient | null> {
    return await this.getPatientByUserIdRepository.execute(id);
  }
}
