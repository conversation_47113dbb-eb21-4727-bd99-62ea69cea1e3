import { IUserInformationProvider } from "@/domain/interfaces/services/IUserInformationProvider";
import { IGetDashByUserIdUsecase } from "@/domain/interfaces/usecases/dash/IGetDashByUserIdUsecase.ts";
import { Dash } from "@/domain/models";

export class DashI<PERSON><PERSON>P<PERSON>ider implements IUserInformationProvider {
  constructor(
    private readonly getDashByUserIdUsecase: IGetDashByUserIdUsecase
  ) {}

  async getUserInformation(id: number): Promise<Dash | null> {
    return await this.getDashByUserIdUsecase.execute(id);
  }
}
