import { IProfessionalRepository } from "@/domain/interfaces/repositories";
import { IUserInformationProvider } from "@/domain/interfaces/services/IUserInformationProvider";
import { Professionnel } from "@/domain/models";

export class ProfessionalInformationProvider
  implements IUserInformationProvider {
  constructor(
    private readonly professionalRepository: IProfessionalRepository
  ) { }

  async getUserInformation(id: number): Promise<Professionnel | null> {
    return await this.professionalRepository.getProfessionalByUserId(id);
  }
}
